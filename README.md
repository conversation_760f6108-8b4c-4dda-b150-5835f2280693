# Carbonix - B2B Carbon Credit Trading Platform

Carbonix is a B2B enterprise SaaS platform for carbon trading where enterprises can self-onboard, list their projects and carbon credits, and trade them with fellow enterprises. The platform supports the entire lifecycle of carbon credits, from verification to tokenization, trading, and retirement, with a focus on security, compliance, and user experience.

## Features

- **Self-Onboarding**: Enterprises can register and onboard themselves with a streamlined KYC/AML process
- **Project Management**: Create and manage multiple carbon credit projects with detailed information
- **Carbon Credit Management**: List, verify, and manage carbon credits with comprehensive tracking
- **Trading Platform**: Buy and sell carbon credits with other enterprises through an intuitive marketplace
- **Blockchain Integration**: Tokenize, transfer, and retire carbon credits on multiple blockchain networks
- **Transaction History**: View complete history of all blockchain transactions with detailed analytics
- **Gas Estimation**: Estimate transaction costs before submitting blockchain operations with optimization suggestions
- **Batch Operations**: Perform actions on multiple carbon credits at once for improved efficiency
- **Matching Engine**: Automated matching of buy and sell orders with configurable parameters
- **Wallet Management**: Secure blockchain wallet integration with Alchemy's Account Abstraction
- **Fee Management**: Configurable listing and transaction fees with revenue sharing options
- **Platform Administration**: User management, organization management, subscription management
- **Analytics and Reporting**: Comprehensive analytics and reports with exportable data
- **Communication System**: Notifications and messaging with customizable preferences
- **Compliance Dashboard**: KYC/AML verification, document management, and regulatory reporting
- **Role-Based Access Control**: Granular permissions with team-level integration

## Tech Stack

- **Frontend**: Next.js 15, Auth.js, Tailwind CSS 4, TanStack (Query/Table), shadcn/ui, Framer Motion
- **Backend**: Node.js with Next.js API routes, Edge Runtime support
- **Database**: PostgreSQL 16 with Prisma ORM 6
- **Blockchain**: Alchemy SDK for wallet and transaction management, Alchemy Account Abstraction for smart accounts, ERC-1155 for carbon credits
- **Authentication**: NextAuth.js 5 with multi-factor authentication
- **Containerization**: Docker and Docker Compose with multi-stage builds
- **Multi-Chain Support**: Ethereum, Polygon, Arbitrum, Optimism, Base
- **Testing**: Jest, React Testing Library
- **CI/CD**: GitHub Actions for automated testing and deployment
- **Monitoring**: Prometheus and Grafana for performance monitoring

## Getting Started

### Prerequisites

- Node.js 20+
- pnpm (recommended) or npm
- Docker and Docker Compose
- Git

### Local Development

#### Option 1: Using Docker for Everything (Recommended)

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/carbon-exchange.git
   cd carbon-exchange
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your configuration.

3. Start the development environment with Docker:
   ```bash
   pnpm docker:dev:build
   ```

   This will start the PostgreSQL database and the Next.js development server with hot reloading.

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

#### Option 2: Docker for Database, Local for Next.js

1. Clone the repository and install dependencies:
   ```bash
   git clone https://github.com/yourusername/carbon-exchange.git
   cd carbon-exchange
   pnpm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   ```

3. Start the development environment with dockerized PostgreSQL:
   ```bash
   pnpm dev:docker
   ```

   This script will:
   - Start the PostgreSQL container
   - Run database migrations
   - Generate the Prisma client
   - Create the uploads directory
   - Start the Next.js development server

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

5. Alternatively, you can run each step manually:
   ```bash
   # Start only the database
   pnpm docker:db

   # Run migrations with the dockerized database
   pnpm prisma:migrate:docker

   # Generate Prisma client
   pnpm prisma:generate

   # Start the development server
   pnpm dev
   ```

### Database Management

- Access Prisma Studio with local PostgreSQL: `pnpm prisma:studio`
- Access Prisma Studio with dockerized PostgreSQL: `pnpm prisma:studio:docker`
- Create a new migration with local PostgreSQL: `pnpm prisma:migrate`
- Create a new migration with dockerized PostgreSQL: `pnpm prisma:migrate:docker`
- Apply migrations in production: `pnpm prisma:deploy`
- Start only the PostgreSQL container: `pnpm docker:db`
- Stop the PostgreSQL container: `pnpm docker:db:stop`

### Production Deployment

#### Using Docker Compose (Recommended)

1. Set up environment variables for production:
   ```bash
   cp .env.example .env.production
   ```
   Edit the `.env.production` file with your production configuration.

   **Required Environment Variables for Production:**
   - `DATABASE_URL`: PostgreSQL connection string
   - `DATABASE_HOST`: PostgreSQL host
   - `DATABASE_PORT`: PostgreSQL port
   - `DATABASE_USER`: PostgreSQL user
   - `DATABASE_PASSWORD`: PostgreSQL password
   - `NEXTAUTH_URL`: URL of the application (e.g., https://example.com)
   - `NEXTAUTH_SECRET`: Secret for NextAuth session encryption
   - `EMAIL_SERVER`: SMTP server for sending emails
   - `EMAIL_FROM`: Email address to send emails from
   - `ALCHEMY_API_KEY`: Alchemy API key for blockchain integration
   - `ALCHEMY_NETWORK`: Alchemy network (e.g., eth-mainnet)
   - `ALCHEMY_GAS_MANAGER_POLICY_ID`: Alchemy Gas Manager policy ID for sponsored transactions
   - `WALLET_ENCRYPTION_KEY`: Key for encrypting wallet data

   **Production Blockchain Networks:**
   - `ETHEREUM_NETWORK`: mainnet
   - `POLYGON_NETWORK`: polygon
   - `OPTIMISM_NETWORK`: optimism
   - `ARBITRUM_NETWORK`: arbitrum
   - `BASE_NETWORK`: base

2. Build and start the production containers:
   ```bash
   pnpm docker:prod:build
   ```

3. Initialize SSL certificates (first time only):
   ```bash
   pnpm ssl:init yourdomain.com
   ```

4. The application will automatically check for required environment variables before starting. If any required variables are missing, the application will not start.

#### Manual Deployment

1. Build the application:
   ```bash
   pnpm build
   ```

2. Start the application:
   ```bash
   pnpm start
   ```

## Project Structure

```
carbon-exchange/
├── docs/                    # Documentation
│   ├── developers/          # Developer documentation
│   ├── users/               # User documentation
│   └── consolidation-summary.md # Summary of module consolidation
├── prisma/                  # Database schema and migrations
├── public/                  # Static assets
├── scripts/                 # Utility scripts
│   ├── migrate-imports.sh   # Script to help migrate imports
│   ├── remove-old-modules.sh # Script to remove old modules
│   └── ...                  # Other utility scripts
├── src/                     # Source code
│   ├── app/                 # Next.js App Router
│   │   ├── (auth)/          # Authentication routes
│   │   ├── admin/           # Admin dashboard
│   │   ├── api/             # API routes
│   │   │   ├── auth/        # Authentication endpoints
│   │   │   ├── carbon-credits/ # Carbon credit management
│   │   │   ├── marketplace/ # Marketplace operations
│   │   │   ├── organizations/ # Organization management
│   │   │   ├── wallet/      # Wallet operations
│   │   │   └── ...          # Other API endpoints
│   │   ├── dashboard/       # User dashboard
│   │   ├── marketplace/     # Carbon credit marketplace
│   │   ├── settings/        # User and organization settings
│   │   └── wallet/          # Wallet management
│   ├── components/          # Reusable components
│   │   ├── carbon-credits/  # Carbon credit components
│   │   ├── marketplace/     # Marketplace components
│   │   ├── providers/       # Context providers
│   │   ├── ui/              # UI components
│   │   │   ├── animated/    # Animated UI components
│   │   │   └── ...          # Other UI components
│   │   ├── wallet/          # Wallet components
│   │   └── forms/           # Form components
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility functions and services
│   │   ├── analytics/       # Analytics modules
│   │   ├── audit/           # Audit logging
│   │   ├── auth/            # Authentication utilities
│   │   ├── blockchain/      # Blockchain integration
│   │   │   ├── alchemy/     # Alchemy SDK integration
│   │   │   ├── config/      # Blockchain configuration
│   │   │   ├── contracts/   # Smart contract interactions
│   │   │   ├── core/        # Core blockchain functionality
│   │   │   └── gas/         # Gas estimation and optimization
│   │   ├── carbon-credits/  # Carbon credit management
│   │   ├── compatibility/   # Backward compatibility layer
│   │   ├── compliance/      # Compliance and regulatory features
│   │   ├── marketplace/     # Marketplace functionality
│   │   ├── notifications/   # Notification system
│   │   ├── orders/          # Order management
│   │   ├── payments/        # Payment processing
│   │   ├── rbac/            # Role-based access control
│   │   ├── storage/         # File storage system
│   │   └── validation/      # Form validation
│   ├── middleware/          # Next.js middleware
│   └── templates/           # Email and document templates
├── .env                     # Environment variables
├── docker-compose.yml       # Docker Compose configuration
├── Dockerfile               # Docker configuration
└── package.json             # Project dependencies and scripts
```

## API Routes

- `/api/auth/*` - Authentication endpoints
- `/api/register` - User registration
- `/api/organizations` - Organization management
- `/api/carbon-credits` - Carbon credit management
- `/api/carbon-credits/:id/tokenize` - Tokenize carbon credits
- `/api/carbon-credits/:id/transfer` - Transfer tokenized carbon credits
- `/api/carbon-credits/:id/retire` - Retire tokenized carbon credits
- `/api/carbon-credits/:id/transactions` - Get transaction history
- `/api/orders` - Order management
- `/api/orders/match` - Match buy and sell orders
- `/api/wallet` - Wallet operations
- `/api/gas-estimation/*` - Gas estimation for blockchain operations
- `/api/notifications` - Notification management

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Documentation

For detailed documentation, please refer to the [docs](./docs) directory:

- [Developer Documentation](./docs/developers/README.md)
- [User Documentation](./docs/users/README.md)
- [Blockchain Integration](./docs/developers/blockchain-integration.md)
- [Account Abstraction](./docs/developers/account-abstraction.md)
- [Transaction History & Gas Estimation](./docs/developers/transaction-history-gas-estimation.md)
- [Blockchain Operations Guide](./docs/users/blockchain-operations.md)

## Code Organization

### Module Structure

The codebase follows a domain-driven design approach with modules organized by functionality:

- **Analytics**: Platform, organization, user, and market analytics
- **Blockchain**: Blockchain client, network configuration, gas estimation and optimization
- **Carbon Credits**: Carbon credit management, verification, and tokenization
- **Marketplace**: Marketplace listings, discovery, and search
- **Orders**: Order management, matching engine, and market data
- **Notifications**: Notification management, preferences, and announcements
- **Audit**: Audit logging and reporting
- **Payments**: Payment processing, subscription management, and billing
- **Compliance**: KYC/AML verification, document management, and regulatory reporting
- **RBAC**: Role-based access control with granular permissions
- **Storage**: File storage system with local and cloud provider support

### Recent Consolidation

The project has undergone a code consolidation process to improve organization and maintainability:

1. **Module Consolidation**: Restructured the `lib` directory into domain-specific modules
2. **Directory Structure**: Consolidated all source code into the `carbon-exchange/src` directory
3. **Compatibility Layer**: Added backward compatibility for smooth transition
4. **File Organization**: Moved files from root `src` directory to the correct `carbon-exchange/src` directory

The consolidation ensures all code is properly organized in the correct location, making the codebase more maintainable and easier to navigate.

For more details, see the [Consolidation Summary](./docs/consolidation-summary.md).

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Prisma](https://www.prisma.io/)
- [Tailwind CSS](https://tailwindcss.com/)
- [NextAuth.js](https://next-auth.js.org/)
- [Alchemy SDK](https://www.alchemy.com/)
- [Alchemy Account Kit](https://docs.alchemy.com/reference/account-kit-overview)
- [shadcn/ui](https://ui.shadcn.com/)
- [Ethers.js](https://docs.ethers.org/)
