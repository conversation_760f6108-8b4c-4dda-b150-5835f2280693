#!/bin/bash

# This script will initialize Let's Encrypt SSL certificates for your domain
# It now supports environment variables for more flexibility

# Use domain from env or argument
DOMAIN=${DOMAIN_NAME:-$1}

if [ -z "$DOMAIN" ]; then
  echo "Error: Please provide a domain name as an argument or set DOMAIN_NAME environment variable."
  echo "Usage: $0 yourdomain.com"
  echo "Or: DOMAIN_NAME=yourdomain.com $0"
  exit 1
fi

domains=($DOMAIN www.$DOMAIN)
rsa_key_size=4096
data_path="./nginx/certbot"
email=${EMAIL_FROM:-""} # Use EMAIL_FROM from env if available

# Create required directories
mkdir -p "$data_path/conf/live/$DOMAIN"
mkdir -p "$data_path/www"

# Create dummy certificates
echo "Creating dummy certificates for $DOMAIN..."
openssl req -x509 -nodes -newkey rsa:$rsa_key_size -days 1 \
  -keyout "$data_path/conf/live/$DOMAIN/privkey.pem" \
  -out "$data_path/conf/live/$DOMAIN/fullchain.pem" \
  -subj "/CN=localhost"

# Update Nginx configuration with the actual domain name
echo "Creating Nginx configuration with your domain..."
export DOMAIN_NAME=$DOMAIN
envsubst '${DOMAIN_NAME}' < ./nginx/conf/app.conf > ./nginx/conf/app_updated.conf
mv ./nginx/conf/app_updated.conf ./nginx/conf/app.conf

# Build and start Nginx
echo "Starting Nginx..."
docker-compose -f docker-compose.prod.yml up -d nginx

# Wait for Nginx to start
echo "Waiting for Nginx to start..."
sleep 5

# Delete dummy certificates
echo "Deleting dummy certificates..."
rm -rf "$data_path/conf/live"

# Request Let's Encrypt certificates
echo "Requesting Let's Encrypt certificates for $DOMAIN and www.$DOMAIN..."
docker-compose -f docker-compose.prod.yml run --rm certbot certonly \
  --webroot -w /var/www/certbot \
  --email $email \
  --agree-tos --no-eff-email \
  -d $DOMAIN -d www.$DOMAIN

# Check if certificates were successfully issued
if [ ! -d "$data_path/conf/live/$DOMAIN" ]; then
  echo "Failed to obtain Let's Encrypt certificates. Using self-signed certificates for now."
  
  # Create self-signed certificates as fallback
  echo "Creating self-signed certificates..."
  openssl req -x509 -nodes -newkey rsa:$rsa_key_size -days 365 \
    -keyout "$data_path/conf/live/$DOMAIN/privkey.pem" \
    -out "$data_path/conf/live/$DOMAIN/fullchain.pem" \
    -subj "/CN=$DOMAIN"
  
  mkdir -p "$data_path/conf/live/$DOMAIN"
  echo "Self-signed certificates created. Replace with proper certificates when possible."
fi

# Restart Nginx to apply the new certificates
echo "Restarting Nginx..."
docker-compose -f docker-compose.prod.yml restart nginx

echo "SSL certificates initialization complete!"
echo "Next step: Run 'docker-compose -f docker-compose.prod.yml up -d' to start all services."
