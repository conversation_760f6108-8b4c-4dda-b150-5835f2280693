#!/bin/bash

# Script to help migrate imports from old module paths to new ones
# Usage: ./scripts/migrate-imports.sh

# Find files with old imports
echo "Finding files with old imports..."

# Analytics service
analytics_files=$(grep -r "from '@/lib/analytics-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$analytics_files" ]; then
  echo "Files importing from '@/lib/analytics-service':"
  echo "$analytics_files"
  echo ""
fi

# Blockchain client
blockchain_files=$(grep -r "from '@/lib/blockchain-client" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$blockchain_files" ]; then
  echo "Files importing from '@/lib/blockchain-client':"
  echo "$blockchain_files"
  echo ""
fi

# Gas estimation
gas_estimation_files=$(grep -r "from '@/lib/gas-estimation" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$gas_estimation_files" ]; then
  echo "Files importing from '@/lib/gas-estimation':"
  echo "$gas_estimation_files"
  echo ""
fi

# Gas optimizer
gas_optimizer_files=$(grep -r "from '@/lib/gas-optimizer" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$gas_optimizer_files" ]; then
  echo "Files importing from '@/lib/gas-optimizer':"
  echo "$gas_optimizer_files"
  echo ""
fi

# Carbon credit service
carbon_credit_files=$(grep -r "from '@/lib/carbon-credit-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$carbon_credit_files" ]; then
  echo "Files importing from '@/lib/carbon-credit-service':"
  echo "$carbon_credit_files"
  echo ""
fi

# Notification service
notification_files=$(grep -r "from '@/lib/notification-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$notification_files" ]; then
  echo "Files importing from '@/lib/notification-service':"
  echo "$notification_files"
  echo ""
fi

# Order service
order_files=$(grep -r "from '@/lib/order-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$order_files" ]; then
  echo "Files importing from '@/lib/order-service':"
  echo "$order_files"
  echo ""
fi

# Marketplace service
marketplace_files=$(grep -r "from '@/lib/marketplace-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$marketplace_files" ]; then
  echo "Files importing from '@/lib/marketplace-service':"
  echo "$marketplace_files"
  echo ""
fi

# Audit service
audit_files=$(grep -r "from '@/lib/audit-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$audit_files" ]; then
  echo "Files importing from '@/lib/audit-service':"
  echo "$audit_files"
  echo ""
fi

# Payment service
payment_files=$(grep -r "from '@/lib/payment-service" --include="*.ts" --include="*.tsx" carbon-exchange/src | cut -d: -f1 | sort | uniq)
if [ -n "$payment_files" ]; then
  echo "Files importing from '@/lib/payment-service':"
  echo "$payment_files"
  echo ""
fi

# Count total files
total_files=$(echo "$analytics_files $blockchain_files $gas_estimation_files $gas_optimizer_files $carbon_credit_files $notification_files $order_files $marketplace_files $audit_files $payment_files" | wc -w)

if [ "$total_files" -eq 0 ]; then
  echo "No files found with old imports. Migration complete!"
else
  echo "Found $total_files files with old imports."
  echo ""
  echo "To migrate these files, update the imports as follows:"
  echo ""
  echo "// Old"
  echo "import analyticsService from '@/lib/analytics-service';"
  echo "// New"
  echo "import { analyticsService } from '@/lib/analytics';"
  echo ""
  echo "// Old"
  echo "import { BlockchainClient } from '@/lib/blockchain-client';"
  echo "// New"
  echo "import { BlockchainClient } from '@/lib/blockchain';"
  echo ""
  echo "// Old"
  echo "import { gasEstimationService } from '@/lib/gas-estimation';"
  echo "// New"
  echo "import { blockchainService } from '@/lib/blockchain';"
  echo "const gasService = blockchainService.getGasService();"
  echo ""
  echo "// Old"
  echo "import { gasOptimizerService } from '@/lib/gas-optimizer';"
  echo "// New"
  echo "import { blockchainService } from '@/lib/blockchain';"
  echo "const gasService = blockchainService.getGasService();"
  echo ""
  echo "// Old"
  echo "import carbonCreditService from '@/lib/carbon-credit-service';"
  echo "// New"
  echo "import { carbonCreditManager } from '@/lib/carbon-credits';"
  echo ""
  echo "// Old"
  echo "import notificationService from '@/lib/notification-service';"
  echo "// New"
  echo "import { notificationService } from '@/lib/notifications';"
  echo ""
  echo "// Old"
  echo "import orderService from '@/lib/order-service';"
  echo "// New"
  echo "import { orderManager } from '@/lib/orders';"
  echo ""
  echo "// Old"
  echo "import marketplaceService from '@/lib/marketplace-service';"
  echo "// New"
  echo "import { marketplaceManager } from '@/lib/marketplace';"
  echo ""
  echo "// Old"
  echo "import auditService from '@/lib/audit-service';"
  echo "// New"
  echo "import { auditManager } from '@/lib/audit';"
  echo ""
  echo "// Old"
  echo "import paymentService from '@/lib/payment-service';"
  echo "// New"
  echo "import { paymentManager } from '@/lib/payments';"
fi
