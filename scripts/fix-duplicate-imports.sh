#!/bin/bash

# Script to fix duplicate imports in tenant isolation files
# Usage: ./scripts/fix-duplicate-imports.sh

echo "Fixing duplicate imports..."

# Fix files with duplicate getTenantContext and withTenantIsolation
files=$(grep -r "getTenantContext.*getTenantContext" --include="*.ts" --include="*.tsx" src | cut -d: -f1 | sort | uniq)

for file in $files; do
  echo "Processing $file"
  
  # Check if the file has withResourceIsolation
  if grep -q "withResourceIsolation" "$file"; then
    sed -i '' 's/import { withResourceIsolation, getTenantContext, withTenantIsolation, getTenantContext, withTenantIsolation } from "@\/lib\/tenant-isolation";/import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@\/lib\/tenant-isolation";/g' "$file"
  
  # Check if the file has withOrganizationIsolation
  elif grep -q "withOrganizationIsolation" "$file"; then
    sed -i '' 's/import { withOrganizationIsolation, getTenantContext, withTenantIsolation, getTenantContext, withTenantIsolation } from "@\/lib\/tenant-isolation";/import { withOrganizationIsolation, getTenantContext, withTenantIsolation } from "@\/lib\/tenant-isolation";/g' "$file"
  
  # Check if the file has withTenantQuery
  elif grep -q "withTenantQuery" "$file"; then
    sed -i '' 's/import { withTenantIsolation, getTenantContext, withTenantIsolation as withTenantQuery, getTenantContext, withTenantIsolation as withTenantQuery } from "@\/lib\/tenant-isolation";/import { withTenantIsolation, getTenantContext, withTenantQuery } from "@\/lib\/tenant-isolation";/g' "$file"
  
  # Check if the file has withOrderTenantIsolation
  elif grep -q "withOrderTenantIsolation" "$file"; then
    sed -i '' 's/import { withTenantIsolation, getTenantContext, withOrderTenantIsolation, getTenantContext, withOrderTenantIsolation } from "@\/lib\/tenant-isolation";/import { withTenantIsolation, getTenantContext, withOrderTenantIsolation } from "@\/lib\/tenant-isolation";/g' "$file"
  
  # Default case for simple duplicates
  else
    sed -i '' 's/import { withTenantIsolation, getTenantContext, getTenantContext } from "@\/lib\/tenant-isolation";/import { withTenantIsolation, getTenantContext } from "@\/lib\/tenant-isolation";/g' "$file"
  fi
done

echo "Duplicate imports fixed successfully!"
