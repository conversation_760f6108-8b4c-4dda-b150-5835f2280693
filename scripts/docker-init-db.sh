#!/bin/bash

# Colors for console output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Database Initialization for Docker Deployment ===${NC}"

# Wait for database to be ready
echo -e "${YELLOW}Waiting for database to be ready...${NC}"
until nc -z db 5432; do
  echo -e "${YELLOW}Database not ready yet, waiting...${NC}"
  sleep 2
done
echo -e "${GREEN}Database is ready!${NC}"

# Run migrations
echo -e "\n${BLUE}Running database migrations...${NC}"
pnpm exec prisma migrate deploy

# Generate Prisma client
echo -e "\n${BLUE}Generating Prisma client...${NC}"
pnpm exec prisma generate

# Run deployment seed
echo -e "\n${BLUE}Running deployment seed...${NC}"
echo -e "${YELLOW}This will create the admin user and essential data...${NC}"

# Run the seed script directly
node scripts/deployment-seed.js

# Exit with success
echo -e "\n${GREEN}Database initialization completed successfully!${NC}"
echo -e "${GREEN}You can now access the application with the following credentials:${NC}"
echo -e "${BLUE}Admin User:${NC}"
echo -e "  Email:    <EMAIL>"
echo -e "  Password: Admin123!"
echo -e "\n${BLUE}Organization Admin:${NC}"
echo -e "  Email:    <EMAIL>"
echo -e "  Password: Password123!"

exit 0
