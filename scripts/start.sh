#!/bin/sh

# Check environment variables
echo "Checking environment variables..."
node /app/scripts/check-env.js

# If the environment check fails, exit
if [ $? -ne 0 ]; then
  echo "Environment check failed. Exiting."
  exit 1
fi

# Wait for the database to be ready
echo "Waiting for database to be ready..."
/bin/sh -c 'until pg_isready -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USER; do sleep 1; done'

# Run database migrations
echo "Running database migrations..."
pnpm exec prisma migrate deploy

# Start the application
echo "Starting the application..."
# Using the path where Next.js places the standalone server when using output: 'standalone'
exec node /app/server.js
