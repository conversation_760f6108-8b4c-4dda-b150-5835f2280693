#!/bin/bash

# This script runs Prisma migrations against the dockerized PostgreSQL database

echo "Running Prisma migrations against dockerized PostgreSQL..."

# Make sure the database is running
if ! docker compose ps | grep -q "db.*running"; then
  echo "PostgreSQL container is not running. Starting it..."
  ./scripts/start-db.sh
fi

# Run the migrations
echo "Running migrations..."
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/carbon_exchange" pnpm exec prisma migrate dev

echo "Migrations completed successfully!"
