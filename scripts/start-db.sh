#!/bin/bash

# This script starts the PostgreSQL container for local development

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if we need to use sudo for Docker commands
# These variables might be exported from the parent script
if [ -z "$DOCKER" ]; then
  if sudo docker info &> /dev/null; then
    echo -e "${YELLOW}Docker requires sudo privileges. Using sudo for Docker commands.${NC}"
    DOCKER="sudo docker"
    DOCKER_COMPOSE="sudo docker compose"
  elif docker info &> /dev/null; then
    DOCKER="docker"
    DOCKER_COMPOSE="docker compose"
  else
    echo -e "${RED}Cannot access Docker daemon. Please check Docker installation or permissions.${NC}"
    echo -e "${YELLOW}Try running this script with sudo or add your user to the docker group:${NC}"
    echo -e "  sudo usermod -aG docker $USER && newgrp docker"
    exit 1
  fi
fi

echo -e "${BLUE}Starting PostgreSQL container for development...${NC}"
$DOCKER_COMPOSE up -d db

echo -e "${BLUE}Waiting for PostgreSQL to be ready...${NC}"
until $DOCKER_COMPOSE exec db pg_isready -U postgres; do
  echo -e "${YELLOW}PostgreSQL is not ready yet... waiting${NC}"
  sleep 2
done

echo -e "${GREEN}PostgreSQL is ready!${NC}"
echo -e "${BLUE}Connection details:${NC}"
echo -e "  Host: localhost"
echo -e "  Port: 5433 (mapped from container port 5432)"
echo -e "  User: postgres"
echo -e "  Password: postgres"
echo -e "  Database: carbon_exchange"
echo ""
echo -e "${BLUE}To connect using psql:${NC}"
echo -e "  $DOCKER_COMPOSE exec db psql -U postgres -d carbon_exchange"
echo ""
echo -e "${BLUE}To stop the database:${NC}"
echo -e "  $DOCKER_COMPOSE down db"
