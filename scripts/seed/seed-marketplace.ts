import { db as prisma } from '../../src/lib/db';

// Function to seed marketplace data
export async function seedMarketplace(organizations: any[], users: any[][], carbonCredits: any[]) {
  console.log('Starting to seed marketplace data...');

  // Debug information
  console.log(`Received organizations: ${organizations ? organizations.length : 0}`);
  console.log(`Received users: ${users ? users.flat().length : 0}`);
  console.log(`Received carbon credits: ${carbonCredits ? carbonCredits.length : 0}`);

  const listings = [];
  const orders = [];

  try {

  // Create marketplace listings for carbon credits
  for (const credit of carbonCredits) {
    // Only create listings for credits that are in LISTED status
    if (credit.status !== 'LISTED') continue;

    // Find the organization and a user from that organization
    const organization = organizations.find(org => org.id === credit.organizationId);
    if (!organization) continue;

    const orgUsers = users.flat().filter(user => user.organizationId === organization.id);
    if (orgUsers.length === 0) continue;

    const user = orgUsers[Math.floor(Math.random() * orgUsers.length)];

    // Check if listing already exists
    const existingListing = await prisma.marketplaceListing.findFirst({
      where: {
        carbonCreditId: credit.id
      }
    });

    if (existingListing) {
      console.log(`Marketplace listing already exists for ${credit.name}. Skipping creation.`);
      listings.push(existingListing);
      continue;
    }

    // Determine pricing strategy
    const pricingStrategies = [
      "FIXED",
      "AUCTION",
      "DYNAMIC",
      "TIERED"
    ];
    const pricingStrategy = pricingStrategies[Math.floor(Math.random() * pricingStrategies.length)];

    // Create listing data based on pricing strategy
    let listingData = {
      title: `${credit.name} - ${credit.vintage} Vintage`,
      description: `${credit.description} from ${credit.location}, ${credit.country}. Standard: ${credit.standard}, Methodology: ${credit.methodology}`,
      status: "ACTIVE",
      quantity: credit.availableQuantity,
      availableQuantity: credit.availableQuantity,
      minPurchaseQuantity: credit.minPurchaseQuantity,
      price: credit.price,
      pricingStrategy,
      visibility: Math.random() > 0.8 ? "PRIVATE" : "PUBLIC",
      featured: Math.random() > 0.8,
      tags: [credit.standard, credit.methodology.split(' ')[0], credit.country],
      metadata: {
        vintage: credit.vintage,
        standard: credit.standard,
        methodology: credit.methodology,
        location: credit.location,
        country: credit.country,
        projectId: credit.projectId,
        serialNumber: credit.serialNumber
      },
      user: {
        connect: {
          id: user.id
        }
      },
      organization: {
        connect: {
          id: organization.id
        }
      },
      carbonCredit: {
        connect: {
          id: credit.id
        }
      }
    };

    // Add pricing strategy specific data
    if (pricingStrategy === "AUCTION") {
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 14) + 7); // 7-21 days from now

      listingData = {
        ...listingData,
        auctionEndTime: endDate,
        auctionReservePrice: credit.price * 0.9,
        auctionMinIncrement: credit.price * 0.05
      };
    } else if (pricingStrategy === "DYNAMIC") {
      listingData = {
        ...listingData,
        dynamicPricingRules: {
          basePrice: credit.price,
          volumeDiscounts: [
            { minQuantity: credit.minPurchaseQuantity * 5, discount: 0.05 },
            { minQuantity: credit.minPurchaseQuantity * 10, discount: 0.1 },
            { minQuantity: credit.minPurchaseQuantity * 20, discount: 0.15 }
          ],
          timeBasedAdjustments: [
            { daysRemaining: 30, adjustment: 0.05 },
            { daysRemaining: 15, adjustment: -0.05 },
            { daysRemaining: 7, adjustment: -0.1 }
          ]
        }
      };
    } else if (pricingStrategy === "TIERED") {
      listingData = {
        ...listingData,
        tieredPricingRules: {
          tiers: [
            { minQuantity: credit.minPurchaseQuantity, price: credit.price },
            { minQuantity: credit.minPurchaseQuantity * 5, price: credit.price * 0.95 },
            { minQuantity: credit.minPurchaseQuantity * 10, price: credit.price * 0.9 },
            { minQuantity: credit.minPurchaseQuantity * 20, price: credit.price * 0.85 }
          ]
        }
      };
    }

    // Create the listing
    const newListing = await prisma.marketplaceListing.create({
      data: listingData
    });

    console.log(`Created marketplace listing: ${newListing.title} (${newListing.id})`);
    listings.push(newListing);

    // Create watchlist entries (30% chance per user not from the selling organization)
    const otherUsers = users.flat().filter(u => u.organizationId !== organization.id);

    for (const otherUser of otherUsers) {
      if (Math.random() > 0.7) continue; // 30% chance to create watchlist

      // Check if user already has a watchlist
      let watchlist = await prisma.marketplaceWatchlist.findFirst({
        where: { userId: otherUser.id }
      });

      // Create watchlist if it doesn't exist
      if (!watchlist) {
        watchlist = await prisma.marketplaceWatchlist.create({
          data: {
            name: 'My Watchlist',
            user: {
              connect: {
                id: otherUser.id
              }
            }
          }
        });
        console.log(`Created watchlist for ${otherUser.name}`);
      }

      // Add item to watchlist
      await prisma.watchlistItem.create({
        data: {
          priceAlertEnabled: Math.random() > 0.5,
          priceAlertThreshold: Math.random() > 0.5 ? credit.price * (Math.random() > 0.5 ? 0.9 : 1.1) : null,
          priceAlertDirection: Math.random() > 0.5 ?
                              (Math.random() > 0.5 ? "ABOVE" : "BELOW") :
                              "BOTH",
          notes: Math.random() > 0.7 ? 'Interested in this credit' : null,
          watchlist: {
            connect: {
              id: watchlist.id
            }
          },
          listing: {
            connect: {
              id: newListing.id
            }
          }
        }
      });

      console.log(`Added ${newListing.title} to ${otherUser.name}'s watchlist`);
    }

    // Create orders for the listing (50% chance)
    if (Math.random() > 0.5) {
      // Determine how many orders to create (1-3)
      const orderCount = Math.floor(Math.random() * 3) + 1;

      for (let i = 0; i < orderCount; i++) {
        // Find a buyer from a different organization
        const buyerOrgs = organizations.filter(org => org.id !== organization.id);
        if (buyerOrgs.length === 0) continue;

        const buyerOrg = buyerOrgs[Math.floor(Math.random() * buyerOrgs.length)];
        const buyerUsers = users.flat().filter(u => u.organizationId === buyerOrg.id);
        if (buyerUsers.length === 0) continue;

        const buyer = buyerUsers[Math.floor(Math.random() * buyerUsers.length)];

        // Determine order quantity (between minPurchaseQuantity and availableQuantity)
        const minQuantity = credit.minPurchaseQuantity || 1;
        const maxQuantity = Math.min(credit.availableQuantity, credit.availableQuantity / orderCount);
        const quantity = Math.floor(Math.random() * (maxQuantity - minQuantity)) + minQuantity;

        // Determine order status
        const orderStatuses = ["PENDING", "MATCHED", "COMPLETED", "CANCELLED"];
        const weights = [0.3, 0.2, 0.4, 0.1]; // 30% pending, 20% matched, 40% completed, 10% cancelled

        let statusIndex = 0;
        const roll = Math.random();
        let cumulativeWeight = 0;

        for (let j = 0; j < weights.length; j++) {
          cumulativeWeight += weights[j];
          if (roll < cumulativeWeight) {
            statusIndex = j;
            break;
          }
        }

        const status = orderStatuses[statusIndex];

        // Create the order
        const newOrder = await prisma.order.create({
          data: {
            type: "BUY",
            quantity,
            price: credit.price,
            status,
            buyer: {
              connect: {
                id: buyer.id
              }
            },
            seller: {
              connect: {
                id: user.id
              }
            },
            carbonCredit: {
              connect: {
                id: credit.id
              }
            },
            MarketplaceListing: {
              connect: {
                id: newListing.id
              }
            }
          }
        });

        console.log(`Created order: ${newOrder.id} for ${quantity} credits of ${credit.name}`);
        orders.push(newOrder);

        // If order is completed, create a transaction
        if (status === "COMPLETED") {
          // Find a wallet for the buyer's organization
          const buyerWallet = await prisma.wallet.findFirst({
            where: {
              organizationId: buyerOrg.id
            }
          });

          if (!buyerWallet) {
            console.log(`No wallet found for ${buyerOrg.name}. Skipping transaction creation.`);
          } else {
            await prisma.transaction.create({
              data: {
                amount: quantity,
                fee: quantity * credit.price * 0.02, // 2% fee
                type: 'PURCHASE',
                status: 'COMPLETED',
                category: 'TRADING',
                notes: `Purchase of ${quantity} credits of ${credit.name}`,
                date: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // 0-30 days ago
                user: {
                  connect: {
                    id: buyer.id
                  }
                },
                organization: {
                  connect: {
                    id: buyerOrg.id
                  }
                },
                order: {
                  connect: {
                    id: newOrder.id
                  }
                },
                wallet: {
                  connect: {
                    id: buyerWallet.id
                  }
                }
            }
          });

          console.log(`Created transaction for order ${newOrder.id}`);
          }

          // Update credit available quantity
          await prisma.carbonCredit.update({
            where: { id: credit.id },
            data: { availableQuantity: credit.availableQuantity - quantity }
          });

          // Update listing available quantity
          await prisma.marketplaceListing.update({
            where: { id: newListing.id },
            data: { availableQuantity: newListing.availableQuantity - quantity }
          });
        }
      }
    }
  }

    return { listings, orders };
  } catch (error) {
    console.error('Error in seedMarketplace:', error);
    return { listings, orders };
  }
}

// For direct execution of this script
if (require.main === module) {
  // This will fail without organizations, users, and carbon credits, so this script should be called from the master script
  console.error('This script should be called from the master seed script.');
  process.exit(1);
}
