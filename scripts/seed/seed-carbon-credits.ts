import { db as prisma } from '../../src/lib/db';

// Function to generate a random serial number
function generateSerialNumber(prefix: string, projectId: string): string {
  const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `${prefix}-${projectId}-${random}`;
}

// Function to seed carbon credits
export async function seedCarbonCredits(organizations: any[], projects: any[], users: any[][]) {
  console.log('Starting to seed carbon credits...');

  // Debug information
  console.log(`Received organizations: ${organizations ? organizations.length : 0}`);
  console.log(`Received projects: ${projects ? projects.length : 0}`);
  console.log(`Received users: ${users ? users.flat().length : 0}`);

  const carbonCredits = [];

  try {

  // Create carbon credits for each project
  for (const project of projects) {
    // Find the organization that owns this project
    const organization = organizations.find(org => org.id === project.organizationId);
    if (!organization) continue;

    // Find users from this organization
    const orgUsers = users.flat().filter(user => user.organizationId === organization.id);
    if (orgUsers.length === 0) continue;

    // Determine how many carbon credits to create for this project (1-3)
    const creditCount = Math.floor(Math.random() * 3) + 1;

    for (let i = 0; i < creditCount; i++) {
      // Assign to a random user from the organization
      const user = orgUsers[Math.floor(Math.random() * orgUsers.length)];

      // Generate credit data based on project type
      const vintage = new Date().getFullYear() - Math.floor(Math.random() * 3);
      const quantity = Math.floor(Math.random() * 10000) + 1000;
      const availableQuantity = Math.floor(quantity * (0.7 + Math.random() * 0.3)); // 70-100% available
      const retiredQuantity = Math.floor((quantity - availableQuantity) * Math.random()); // Some may be retired
      const price = (Math.floor(Math.random() * 30) + 5 + Math.random()) * 82.5; // ₹412.5-₹2887.5 with decimal (converted from $5-$35)

      // Determine status (mostly LISTED, but some with other statuses)
      let status: 'PENDING' | 'VERIFIED' | 'LISTED' | 'SOLD' | 'RETIRED' | 'TOKENIZED' = 'LISTED';
      const statusRoll = Math.random();
      if (statusRoll < 0.1) status = 'PENDING';
      else if (statusRoll < 0.2) status = 'VERIFIED';
      else if (statusRoll < 0.3) status = 'SOLD';
      else if (statusRoll < 0.4) status = 'RETIRED';
      else if (statusRoll < 0.5) status = 'TOKENIZED';

      // Generate a name based on project
      const suffixes = ['Phase 1', 'Phase 2', 'Block A', 'Block B', 'Year ' + vintage, 'Batch ' + (i + 1)];
      const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
      const name = `${project.name} - ${suffix}`;

      // Check if credit already exists
      const existingCredit = await prisma.carbonCredit.findFirst({
        where: {
          name,
          projectId: project.id
        }
      });

      if (existingCredit) {
        console.log(`Carbon credit ${name} already exists for project ${project.name}. Skipping creation.`);
        carbonCredits.push(existingCredit);
        continue;
      }

      // Create carbon credit
      const standardPrefix = project.standard === 'Verra' ? 'VCS' :
                            project.standard === 'Gold Standard' ? 'GS' :
                            project.standard === 'American Carbon Registry' ? 'ACR' :
                            project.standard === 'Climate Action Reserve' ? 'CAR' : 'CCR';

      const newCredit = await prisma.carbonCredit.create({
        data: {
          name,
          description: `Carbon credits generated from ${project.name} during ${suffix}`,
          quantity,
          availableQuantity,
          retiredQuantity,
          price: parseFloat(price.toFixed(2)),
          minPurchaseQuantity: Math.max(1, Math.floor(quantity * 0.01)), // 1% of total quantity
          vintage,
          standard: project.standard,
          methodology: project.methodology,
          location: project.location,
          country: project.country,
          externalProjectId: project.externalProjectId,
          serialNumber: generateSerialNumber(standardPrefix, project.externalProjectId),
          status,
          verificationStatus: 'VERIFIED',
          listingDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // 0-30 days ago
          certificationDate: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000), // 0-60 days ago
          // Remove additionalCertifications and cobenefits as they don't exist in the schema
          organization: {
            connect: {
              id: organization.id
            }
          },
          project: {
            connect: {
              id: project.id
            }
          },
          user: {
            connect: {
              id: user.id
            }
          }
        }
      });

      console.log(`Created carbon credit: ${newCredit.name} (${newCredit.id}) for project ${project.name}`);
      carbonCredits.push(newCredit);

      // Create price history
      const pricePoints = Math.floor(Math.random() * 5) + 2; // 2-6 price points
      let currentPrice = newCredit.price * 0.8; // Start at 80% of current price

      for (let j = 0; j < pricePoints; j++) {
        const priceDate = new Date(newCredit.listingDate);
        priceDate.setDate(priceDate.getDate() + j * Math.floor(Math.random() * 5 + 1)); // 1-5 days between price points

        if (priceDate > new Date()) continue; // Don't create future price points

        // Price tends to increase over time
        currentPrice = currentPrice * (1 + (Math.random() * 0.1 - 0.02)); // -2% to +8% change

        await prisma.carbonCreditPrice.create({
          data: {
            price: parseFloat(currentPrice.toFixed(2)),
            timestamp: priceDate, // Changed from 'date' to 'timestamp'
            carbonCredit: {
              connect: {
                id: newCredit.id
              }
            }
          }
        });
      }

      // Create verification history
      await prisma.carbonCreditVerification.create({
        data: {
          status: 'VERIFIED',
          verifier: 'Verification Authority', // Changed from 'verifiedBy' to 'verifier'
          verifierEmail: '<EMAIL>', // Added required field
          notes: 'Initial verification',
          carbonCredit: {
            connect: {
              id: newCredit.id
            }
          }
        }
      });

      // Create retirement records for retired credits
      if (status === 'RETIRED' && retiredQuantity > 0) {
        await prisma.retirement.create({
          data: {
            amount: retiredQuantity, // Changed from 'quantity' to 'amount'
            createdAt: new Date(Date.now() - Math.floor(Math.random() * 15) * 24 * 60 * 60 * 1000), // Using createdAt instead of timestamp/date
            reason: 'Corporate carbon neutrality commitment',
            beneficiary: organization.name,
            // Removed certificateUrl as it's not in the schema
            carbonCredit: {
              connect: {
                id: newCredit.id
              }
            },
            user: {
              connect: {
                id: user.id
              }
            },
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });

        console.log(`Created retirement record for ${retiredQuantity} credits of ${newCredit.name}`);
      }

      // Create tokenization records for tokenized credits
      if (status === 'TOKENIZED') {
        await prisma.tokenization.create({
          data: {
            amount: availableQuantity, // Changed from 'quantity' to 'amount'
            tokenId: `t${standardPrefix}${vintage}-${Math.floor(Math.random() * 1000)}`, // Added required tokenId field
            tokenName: `t${standardPrefix}${vintage}`,
            tokenSymbol: `t${standardPrefix}${vintage.toString().substring(2)}`,
            tokenStandard: 'ERC20',
            network: 'ethereum', // Added required network field
            contractAddress: `0x${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`, // Changed from tokenAddress
            status: 'COMPLETED',
            transactionHash: `0x${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
            createdAt: new Date(Date.now() - Math.floor(Math.random() * 20) * 24 * 60 * 60 * 1000), // Changed from tokenizationDate
            carbonCredit: {
              connect: {
                id: newCredit.id
              }
            },
            user: {
              connect: {
                id: user.id
              }
            },
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });

        console.log(`Created tokenization record for ${availableQuantity} credits of ${newCredit.name}`);
      }

      // Create compliance documents
      const docTypes = ['VERIFICATION_REPORT', 'ISSUANCE_CERTIFICATE', 'METHODOLOGY_DOCUMENTATION', 'PROJECT_DOCUMENTATION'];
      const docCount = Math.floor(Math.random() * 3) + 1; // 1-3 documents

      for (let j = 0; j < docCount; j++) {
        const docType = docTypes[Math.floor(Math.random() * docTypes.length)];

        await prisma.complianceDocument.create({
          data: {
            type: "VERIFICATION_REPORT", // Using a specific valid type instead of dynamic value
            name: `${docType.replace('_', ' ')} - ${newCredit.name}`,
            url: `https://example.com/documents/${docType.toLowerCase()}-${newCredit.id}.pdf`,
            status: 'APPROVED',
            carbonCredit: {
              connect: {
                id: newCredit.id
              }
            },
            user: {
              connect: {
                id: user.id
              }
            },
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });
      }
    }
  }

    return carbonCredits;
  } catch (error) {
    console.error('Error in seedCarbonCredits:', error);
    return carbonCredits;
  }
}

// For direct execution of this script
if (require.main === module) {
  // This will fail without organizations, projects, and users, so this script should be called from the master script
  console.error('This script should be called from the master seed script.');
  process.exit(1);
}
