import { db as prisma } from '../../src/lib/db';

// Function to seed notifications and audit logs
export async function seedNotificationsAndAudit(organizations: any[], users: any[][], projects: any[], carbonCredits: any[], wallets: any[], transactions: any[]) {
  console.log('Starting to seed notifications and audit logs...');

  // Debug information
  console.log(`Received organizations: ${organizations ? organizations.length : 0}`);
  console.log(`Received users: ${users ? users.flat().length : 0}`);
  console.log(`Received projects: ${projects ? projects.length : 0}`);
  console.log(`Received carbon credits: ${carbonCredits ? carbonCredits.length : 0}`);
  console.log(`Received wallets: ${wallets ? wallets.length : 0}`);
  console.log(`Received transactions: ${transactions ? transactions.length : 0}`);

  try {

  // Create notification preferences for users
  for (const userGroup of users) {
    for (const user of userGroup) {
      // Check if notification preference already exists
      const existingPref = await prisma.notificationPreference.findUnique({
        where: { userId: user.id }
      });

      if (existingPref) {
        console.log(`Notification preference already exists for ${user.name}. Skipping creation.`);
        continue;
      }

      // Create notification preference
      await prisma.notificationPreference.create({
        data: {
          email: true,
          inApp: true,
          push: Math.random() > 0.5,

          // Store notification type preferences as JSON
          types: {
            system: {
              email: true,
              inApp: true,
              push: Math.random() > 0.5,
              sms: Math.random() > 0.7
            },
            transaction: {
              email: true,
              inApp: true,
              push: Math.random() > 0.5,
              sms: Math.random() > 0.7
            },
            credit: {
              email: true,
              inApp: true,
              push: Math.random() > 0.5,
              sms: Math.random() > 0.7
            },
            billing: {
              email: true,
              inApp: true,
              push: Math.random() > 0.5,
              sms: Math.random() > 0.7
            },
            document: {
              email: true,
              inApp: true,
              push: Math.random() > 0.5,
              sms: Math.random() > 0.7
            },
            security: {
              email: true,
              inApp: true,
              push: Math.random() > 0.6,
              sms: Math.random() > 0.6
            },
            marketing: {
              email: Math.random() > 0.3,
              inApp: Math.random() > 0.3,
              push: Math.random() > 0.7,
              sms: Math.random() > 0.8
            }
          },

          user: {
            connect: {
              id: user.id
            }
          }
        }
      });

      console.log(`Created notification preference for ${user.name}`);
    }
  }

  // Create notifications for users
  for (const userGroup of users) {
    for (const user of userGroup) {
      // Skip users without an organization
      if (!user.organizationId) continue;

      // Find the organization
      const organization = organizations.find(org => org.id === user.organizationId);
      if (!organization) continue;

      // Create 5-15 notifications per user
      const notificationCount = Math.floor(Math.random() * 11) + 5;

      for (let i = 0; i < notificationCount; i++) {
        // Determine notification type
        const notificationTypes = [
          "SYSTEM",
          "TRANSACTION",
          "CREDIT",
          "BILLING",
          "VERIFICATION",
          "SECURITY",
          "WALLET"
        ];
        const notificationType = notificationTypes[Math.floor(Math.random() * notificationTypes.length)];

        // Determine notification priority
        const notificationPriorities = [
          "LOW",
          "NORMAL",
          "HIGH",
          "URGENT"
        ];
        const weights = [0.3, 0.5, 0.15, 0.05]; // 30% low, 50% normal, 15% high, 5% urgent

        let priorityIndex = 0;
        const roll = Math.random();
        let cumulativeWeight = 0;

        for (let j = 0; j < weights.length; j++) {
          cumulativeWeight += weights[j];
          if (roll < cumulativeWeight) {
            priorityIndex = j;
            break;
          }
        }

        const priority = notificationPriorities[priorityIndex];

        // Determine notification read status (mostly unread for recent, read for older)
        const notificationDate = new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000); // 0-30 days ago
        const isRecent = (Date.now() - notificationDate.getTime()) < 7 * 24 * 60 * 60 * 1000; // Less than 7 days old
        const read = !(isRecent && Math.random() > 0.3); // true for READ, false for UNREAD

        // Generate notification content based on type
        let title, message, actionUrl, actionLabel, icon;

        switch (notificationType) {
          case "SYSTEM":
            title = 'System Notification';
            message = [
              'Your account has been successfully verified.',
              'Platform maintenance scheduled for this weekend.',
              'New feature released: Advanced reporting tools.',
              'Please update your profile information.',
              'Welcome to Carbon Exchange Platform!'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/dashboard';
            actionLabel = 'View Dashboard';
            icon = 'bell';
            break;

          case "TRANSACTION":
            title = 'Transaction Update';
            message = [
              'Your transaction has been completed successfully.',
              'New transaction requires your approval.',
              'Transaction failed. Please check details.',
              'You have received a new transaction.',
              'Transaction status updated.'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/transactions';
            actionLabel = 'View Transactions';
            icon = 'credit-card';
            break;

          case "CREDIT":
            title = 'Carbon Credit Update';
            message = [
              'New carbon credits listed in the marketplace.',
              'Your carbon credit listing has received interest.',
              'Carbon credit verification completed.',
              'Price update for your watched carbon credits.',
              'New carbon credit project available.'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/marketplace';
            actionLabel = 'View Marketplace';
            icon = 'leaf';
            break;

          case "BILLING":
            title = 'Billing Notification';
            message = [
              'Your subscription has been renewed.',
              'Invoice available for your recent transaction.',
              'Payment received. Thank you!',
              'Subscription plan upgrade available.',
              'Upcoming payment reminder.'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/billing';
            actionLabel = 'View Billing';
            icon = 'rupee-sign';
            break;

          case "VERIFICATION":
            title = 'Verification Notification';
            message = [
              'New document uploaded to your account.',
              'Document verification completed.',
              'Please review and sign the pending document.',
              'Document expiring soon. Please update.',
              'New compliance document required.'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/documents';
            actionLabel = 'View Documents';
            icon = 'file-text';
            break;

          case "SECURITY":
            title = 'Security Alert';
            message = [
              'New login detected from an unknown device.',
              'Password changed successfully.',
              'Security settings updated.',
              'Two-factor authentication enabled.',
              'Important security update required.'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/settings/security';
            actionLabel = 'Security Settings';
            icon = 'shield';
            break;

          case "WALLET":
            title = 'Wallet Update';
            message = [
              'Wallet balance updated.',
              'New transaction in your wallet.',
              'Wallet security settings updated.',
              'Wallet connection status changed.',
              'New wallet feature available.'
            ][Math.floor(Math.random() * 5)];
            actionUrl = '/wallets';
            actionLabel = 'View Wallets';
            icon = 'wallet';
            break;
        }

        // Create notification
        await prisma.notification.create({
          data: {
            type: notificationType,
            priority,
            read,
            title,
            message,
            actionUrl,
            actionLabel,
            icon,
            // Removed readAt field as it doesn't exist in the schema
            expiresAt: Math.random() > 0.7 ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : null,
            metadata: {},
            createdAt: notificationDate,
            // Removed updatedAt field as it doesn't exist in the schema
            user: {
              connect: {
                id: user.id
              }
            },
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });
      }

      console.log(`Created ${notificationCount} notifications for ${user.name}`);
    }
  }

  // Create announcements
  const announcementCount = Math.floor(Math.random() * 5) + 3; // 3-7 announcements

  for (let i = 0; i < announcementCount; i++) {
    // Find a random admin user
    const adminUsers = users.flat().filter(user => user.role === 'ADMIN');
    if (adminUsers.length === 0) continue;

    const adminUser = adminUsers[Math.floor(Math.random() * adminUsers.length)];

    // Determine announcement type and priority
    const types = ['INFO', 'WARNING', 'CRITICAL', 'SUCCESS'];
    const type = types[Math.floor(Math.random() * types.length)];

    const priorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT'];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];

    // Determine target audience
    const targetAudiences = ['ALL', 'AUTHENTICATED', 'ORGANIZATION'];
    const targetAudience = targetAudiences[Math.floor(Math.random() * targetAudiences.length)];

    // Create start and end dates
    const startDate = new Date(Date.now() - Math.floor(Math.random() * 15) * 24 * 60 * 60 * 1000); // 0-15 days ago
    const endDate = new Date(Date.now() + Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000); // 0-30 days in future

    // Generate announcement content based on type
    let title, message, actionUrl, actionLabel;

    switch (type) {
      case 'INFO':
        title = 'Platform Update';
        message = 'We have released new features to enhance your carbon trading experience. Check out the updated marketplace and reporting tools.';
        actionUrl = '/updates';
        actionLabel = 'Learn More';
        break;

      case 'WARNING':
        title = 'Scheduled Maintenance';
        message = 'The platform will undergo scheduled maintenance on ' + new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString() + ' from 2:00 AM to 4:00 AM UTC. Some services may be temporarily unavailable.';
        actionUrl = '/maintenance';
        actionLabel = 'View Schedule';
        break;

      case 'CRITICAL':
        title = 'Important Security Update';
        message = 'We have implemented critical security enhancements. Please update your password and review your security settings.';
        actionUrl = '/settings/security';
        actionLabel = 'Update Now';
        break;

      case 'SUCCESS':
        title = 'Record Trading Volume Achieved';
        message = 'Our platform has reached a milestone of 1 million carbon credits traded! Thank you for being part of this journey.';
        actionUrl = '/stats';
        actionLabel = 'View Statistics';
        break;
    }

    // Create announcement
    await prisma.announcement.create({
      data: {
        title,
        message,
        type,
        startDate,
        endDate,
        actionUrl,
        actionLabel,
        dismissible: Math.random() > 0.3,
        priority,
        targetAudience,
        targetOrganizationIds: targetAudience === 'ORGANIZATION' ? organizations.slice(0, 3).map(org => org.id) : [],
        published: true,
        createdById: adminUser.id,
        createdAt: startDate,
        updatedAt: startDate
      }
    });

    console.log(`Created announcement: ${title}`);
  }

  // Create audit logs
  // For organizations
  for (const organization of organizations) {
    // Create organization creation audit log
    await prisma.auditLog.create({
      data: {
        type: "ORGANIZATION_CREATED",
        description: `Organization "${organization.name}" created`,
        metadata: {
          organizationId: organization.id,
          organizationName: organization.name,
          status: organization.status
        },
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        createdAt: new Date(organization.createdAt.getTime() - 1000), // Just before organization creation
        organization: {
          connect: {
            id: organization.id
          }
        }
      }
    });

    // Create organization update audit log (70% chance)
    if (Math.random() > 0.3) {
      await prisma.auditLog.create({
        data: {
          type: "ORGANIZATION_UPDATED",
          description: `Organization "${organization.name}" updated`,
          metadata: {
            organizationId: organization.id,
            organizationName: organization.name,
            updatedFields: ['status', 'verificationStatus']
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: new Date(organization.updatedAt.getTime() - 1000), // Just before organization update
          organization: {
            connect: {
              id: organization.id
            }
          }
        }
      });
    }
  }

  // For users
  for (const userGroup of users) {
    for (const user of userGroup) {
      // Create user creation audit log
      await prisma.auditLog.create({
        data: {
          type: "USER_CREATED",
          description: `User "${user.name}" created`,
          metadata: {
            userId: user.id,
            userEmail: user.email,
            userRole: user.role
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: new Date(user.createdAt.getTime() - 1000), // Just before user creation
          user: {
            connect: {
              id: user.id
            }
          },
          organization: user.organizationId ? {
            connect: {
              id: user.organizationId
            }
          } : undefined
        }
      });

      // Create user login audit logs (3-7 logs)
      const loginCount = Math.floor(Math.random() * 5) + 3;

      for (let i = 0; i < loginCount; i++) {
        const loginDate = new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000); // 0-30 days ago

        await prisma.auditLog.create({
          data: {
            type: "LOGIN_SUCCESS",
            description: `User "${user.name}" logged in`,
            metadata: {
              userId: user.id,
              userEmail: user.email,
              browser: 'Chrome',
              os: Math.random() > 0.5 ? 'Windows' : 'MacOS'
            },
            ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            createdAt: loginDate,
            user: {
              connect: {
                id: user.id
              }
            },
            organization: user.organizationId ? {
              connect: {
                id: user.organizationId
              }
            } : undefined
          }
        });
      }
    }
  }

  // For carbon credits
  for (const credit of carbonCredits) {
    // Create carbon credit creation audit log
    await prisma.auditLog.create({
      data: {
        type: "CARBON_CREDIT_CREATED",
        description: `Carbon credit "${credit.name}" created`,
        metadata: {
          creditId: credit.id,
          creditName: credit.name,
          quantity: credit.quantity,
          vintage: credit.vintage,
          standard: credit.standard
        },
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        createdAt: new Date(credit.createdAt.getTime() - 1000), // Just before credit creation
        user: {
          connect: {
            id: credit.userId
          }
        },
        organization: {
          connect: {
            id: credit.organizationId
          }
        }
      }
    });

    // Create carbon credit listing audit log (if listed)
    if (credit.status === 'LISTED') {
      await prisma.auditLog.create({
        data: {
          type: "CARBON_CREDIT_LISTED",
          description: `Carbon credit "${credit.name}" listed on marketplace`,
          metadata: {
            creditId: credit.id,
            creditName: credit.name,
            quantity: credit.availableQuantity,
            price: credit.price
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: credit.listingDate || new Date(credit.createdAt.getTime() + 1000), // Just after credit creation
          user: {
            connect: {
              id: credit.userId
            }
          },
          organization: {
            connect: {
              id: credit.organizationId
            }
          }
        }
      });
    }

    // Create carbon credit retirement audit log (if retired)
    if (credit.status === 'RETIRED') {
      await prisma.auditLog.create({
        data: {
          type: "CARBON_CREDIT_RETIRED",
          description: `Carbon credit "${credit.name}" retired`,
          metadata: {
            creditId: credit.id,
            creditName: credit.name,
            quantity: credit.retiredQuantity,
            retirementReason: 'Corporate carbon neutrality commitment'
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: credit.retirementDate || new Date(credit.updatedAt.getTime() - 1000),
          user: {
            connect: {
              id: credit.userId
            }
          },
          organization: {
            connect: {
              id: credit.organizationId
            }
          }
        }
      });
    }

    // Create carbon credit tokenization audit log (if tokenized)
    if (credit.status === 'TOKENIZED') {
      await prisma.auditLog.create({
        data: {
          type: "CARBON_CREDIT_TOKENIZED",
          description: `Carbon credit "${credit.name}" tokenized`,
          metadata: {
            creditId: credit.id,
            creditName: credit.name,
            quantity: credit.availableQuantity,
            tokenStandard: 'ERC20'
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: new Date(credit.updatedAt.getTime() - 1000),
          user: {
            connect: {
              id: credit.userId
            }
          },
          organization: {
            connect: {
              id: credit.organizationId
            }
          }
        }
      });
    }
  }

  // For wallets
  for (const wallet of wallets) {
    // Create wallet creation audit log
    await prisma.auditLog.create({
      data: {
        type: "WALLET_CREATED",
        description: `Wallet "${wallet.name}" created`,
        metadata: {
          walletId: wallet.id,
          walletName: wallet.name,
          walletAddress: wallet.address,
          network: wallet.network,
          chainId: wallet.chainId
        },
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        createdAt: new Date(wallet.createdAt.getTime() - 1000), // Just before wallet creation
        user: wallet.userId ? {
          connect: {
            id: wallet.userId
          }
        } : undefined,
        organization: wallet.organizationId ? {
          connect: {
            id: wallet.organizationId
          }
        } : undefined
      }
    });

    // Create wallet connection audit log (50% chance)
    if (Math.random() > 0.5) {
      await prisma.auditLog.create({
        data: {
          type: "WALLET_CONNECTED",
          description: `Wallet "${wallet.name}" connected`,
          metadata: {
            walletId: wallet.id,
            walletName: wallet.name,
            walletAddress: wallet.address,
            network: wallet.network
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: new Date(wallet.createdAt.getTime() + 60000), // 1 minute after wallet creation
          user: wallet.userId ? {
            connect: {
              id: wallet.userId
            }
          } : undefined,
          organization: wallet.organizationId ? {
            connect: {
              id: wallet.organizationId
            }
          } : undefined
        }
      });
    }
  }

  // For transactions
  for (const transaction of transactions) {
    // Create transaction audit log
    await prisma.auditLog.create({
      data: {
        type: "TRANSACTION_CREATED",
        description: `${transaction.type} transaction created`,
        metadata: {
          transactionId: transaction.id,
          type: transaction.type,
          amount: transaction.amount,
          status: transaction.status,
          network: transaction.network
        },
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        createdAt: transaction.date || transaction.createdAt,
        user: transaction.userId ? {
          connect: {
            id: transaction.userId
          }
        } : undefined,
        organization: transaction.organizationId ? {
          connect: {
            id: transaction.organizationId
          }
        } : undefined
      }
    });

    // Create transaction update audit log (if completed and was pending)
    if (transaction.status === 'COMPLETED' && Math.random() > 0.7) {
      await prisma.auditLog.create({
        data: {
          type: "TRANSACTION_UPDATED",
          description: `${transaction.type} transaction completed`,
          metadata: {
            transactionId: transaction.id,
            type: transaction.type,
            amount: transaction.amount,
            oldStatus: 'PENDING',
            newStatus: 'COMPLETED',
            transactionHash: transaction.transactionHash
          },
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          createdAt: new Date((transaction.date || transaction.createdAt).getTime() + 300000), // 5 minutes after transaction creation
          user: transaction.userId ? {
            connect: {
              id: transaction.userId
            }
          } : undefined,
          organization: transaction.organizationId ? {
            connect: {
              id: transaction.organizationId
            }
          } : undefined
        }
      });
    }
  }

    return true;
  } catch (error) {
    console.error('Error in seedNotificationsAndAudit:', error);
    return false;
  }
}

// For direct execution of this script
if (require.main === module) {
  // This will fail without other entities, so this script should be called from the master script
  console.error('This script should be called from the master seed script.');
  process.exit(1);
}
