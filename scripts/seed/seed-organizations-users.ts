import { db as prisma } from '../../src/lib/db';
import bcrypt from 'bcryptjs';

// Organization names and details
const organizationData = [
  {
    name: 'GreenTech Solutions',
    description: 'Innovative solutions for a sustainable future',
    website: 'https://greentech.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'United States',
    industry: 'Renewable Energy',
    size: 'MEDIUM',
    foundedYear: 2010,
    primaryContact: '<PERSON>',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+1234567890',
    address: '123 Green Street',
    city: 'San Francisco',
    state: 'CA',
    postalCode: '94105',
    legalName: 'GreenTech Solutions Inc.',
    registrationNumber: 'US12345678',
    taxId: '98-7654321',
  },
  {
    name: 'Carbon Trading Inc',
    description: 'Leading the way in carbon credit trading',
    website: 'https://carbontrading.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'United Kingdom',
    industry: 'Financial Services',
    size: 'LARGE',
    foundedYear: 2008,
    primaryContact: '<PERSON>',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+***********',
    address: '456 Carbon Avenue',
    city: 'London',
    state: '',
    postalCode: 'EC1A 1BB',
    legalName: 'Carbon Trading International Ltd',
    registrationNumber: 'UK87654321',
    taxId: 'GB123456789',
  },
  {
    name: 'EcoSustain Partners',
    description: 'Partnering for a sustainable planet',
    website: 'https://ecosustain.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'Germany',
    industry: 'Environmental Services',
    size: 'SMALL',
    foundedYear: 2015,
    primaryContact: 'Hans Mueller',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+49123456789',
    address: '789 Eco Street',
    city: 'Berlin',
    state: '',
    postalCode: '10115',
    legalName: 'EcoSustain GmbH',
    registrationNumber: 'DE12345678',
    taxId: 'DE987654321',
  },
  {
    name: 'Forest Conservation Alliance',
    description: 'Protecting forests worldwide',
    website: 'https://forestalliance.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'Brazil',
    industry: 'Non-profit',
    size: 'MEDIUM',
    foundedYear: 2012,
    primaryContact: 'Maria Silva',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+5511987654321',
    address: '101 Forest Road',
    city: 'São Paulo',
    state: 'SP',
    postalCode: '01310-200',
    legalName: 'Forest Conservation Alliance NGO',
    registrationNumber: 'BR12345678',
    taxId: 'BR987654321',
  },
  {
    name: 'CleanEnergy Ventures',
    description: 'Investing in clean energy solutions',
    website: 'https://cleanenergy.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'Australia',
    industry: 'Energy',
    size: 'LARGE',
    foundedYear: 2009,
    primaryContact: 'James Wilson',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+***********',
    address: '202 Energy Lane',
    city: 'Sydney',
    state: 'NSW',
    postalCode: '2000',
    legalName: 'CleanEnergy Ventures Pty Ltd',
    registrationNumber: 'AU12345678',
    taxId: 'AU987654321',
  },
  {
    name: 'Sustainable Agriculture Cooperative',
    description: 'Promoting sustainable farming practices',
    website: 'https://sustainableag.example.com',
    status: "PENDING",
    verificationStatus: "IN_REVIEW",
    country: 'India',
    industry: 'Agriculture',
    size: 'SMALL',
    foundedYear: 2018,
    primaryContact: 'Raj Patel',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+***********',
    address: '303 Farm Road',
    city: 'Mumbai',
    state: 'Maharashtra',
    postalCode: '400001',
    legalName: 'Sustainable Agriculture Cooperative Society',
    registrationNumber: 'IN12345678',
    taxId: 'IN987654321',
  },
  {
    name: 'Ocean Conservation Institute',
    description: 'Dedicated to preserving marine ecosystems',
    website: 'https://oceanconservation.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'Canada',
    industry: 'Environmental Services',
    size: 'MEDIUM',
    foundedYear: 2011,
    primaryContact: 'Sarah Thompson',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+1416987654321',
    address: '404 Ocean Drive',
    city: 'Vancouver',
    state: 'BC',
    postalCode: 'V6B 1G1',
    legalName: 'Ocean Conservation Institute of Canada',
    registrationNumber: 'CA12345678',
    taxId: 'CA987654321',
  },
  {
    name: 'Carbon Exchange Platform',
    description: 'The platform administrator organization',
    website: 'https://carbonexchange.example.com',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    country: 'United States',
    industry: 'Technology',
    size: 'MEDIUM',
    foundedYear: 2020,
    primaryContact: 'Admin User',
    primaryContactEmail: '<EMAIL>',
    primaryContactPhone: '+18001234567',
    address: '505 Platform Street',
    city: 'New York',
    state: 'NY',
    postalCode: '10001',
    legalName: 'Carbon Exchange Platform Inc.',
    registrationNumber: 'US87654321',
    taxId: 'US123456789',
  }
];

// User data for each organization
const userData = [
  // GreenTech Solutions
  [
    {
      email: '<EMAIL>',
      name: 'John Smith',
      role: "ORGANIZATION_ADMIN",
      jobTitle: 'CEO',
      departmentName: 'Executive',
      phoneNumber: '+1234567890',
      bio: 'Founder and CEO with 15+ years in renewable energy',
    },
    {
      email: '<EMAIL>',
      name: 'Sarah Jones',
      role: "USER",
      jobTitle: 'CFO',
      departmentName: 'Finance',
      phoneNumber: '+1234567891',
      bio: 'Financial expert with background in sustainable investments',
    },
    {
      email: '<EMAIL>',
      name: 'Michael Brown',
      role: "USER",
      jobTitle: 'Project Manager',
      departmentName: 'Operations',
      phoneNumber: '+1234567892',
      bio: 'Manages carbon offset projects and verifications',
    },
    {
      email: '<EMAIL>',
      name: 'Lisa Wong',
      role: "USER",
      jobTitle: 'Sustainability Analyst',
      departmentName: 'Research',
      phoneNumber: '+**********',
      bio: 'Specializes in carbon footprint analysis and reduction strategies',
    }
  ],
  // Carbon Trading Inc
  [
    {
      email: '<EMAIL>',
      name: 'Emma Johnson',
      role: "ORGANIZATION_ADMIN",
      jobTitle: 'Managing Director',
      departmentName: 'Executive',
      phoneNumber: '+***********',
      bio: 'Expert in carbon markets with investment banking background',
    },
    {
      email: '<EMAIL>',
      name: 'David Williams',
      role: "USER",
      jobTitle: 'Trading Director',
      departmentName: 'Trading',
      phoneNumber: '+***********',
      bio: 'Oversees all trading activities and market strategies',
    },
    {
      email: '<EMAIL>',
      name: 'Olivia Taylor',
      role: "USER",
      jobTitle: 'Compliance Officer',
      departmentName: 'Legal',
      phoneNumber: '+***********',
      bio: 'Ensures regulatory compliance in carbon trading operations',
    }
  ],
  // Additional users for other organizations...
];

// Team data for organizations
const teamData = [
  // GreenTech Solutions
  [
    {
      name: 'Executive Team',
      description: 'Senior leadership team',
      members: [0, 1] // Indices of users in the organization
    },
    {
      name: 'Project Management',
      description: 'Team responsible for carbon project management',
      members: [2, 3]
    },
    {
      name: 'Finance',
      description: 'Financial operations team',
      members: [1]
    }
  ],
  // Carbon Trading Inc
  [
    {
      name: 'Management',
      description: 'Company management team',
      members: [0]
    },
    {
      name: 'Trading Desk',
      description: 'Carbon credit trading operations',
      members: [1]
    },
    {
      name: 'Compliance',
      description: 'Regulatory compliance team',
      members: [2]
    }
  ],
  // Additional teams for other organizations...
];

// Custom roles for organizations
const customRoleData = [
  // GreenTech Solutions
  [
    {
      name: 'Project Approver',
      description: 'Can approve new carbon projects',
      permissions: ['create:project', 'read:project', 'update:project', 'approve:project']
    },
    {
      name: 'Credit Manager',
      description: 'Manages carbon credit listings and sales',
      permissions: ['create:carbon_credit', 'read:carbon_credit', 'update:carbon_credit', 'list:carbon_credit']
    }
  ],
  // Carbon Trading Inc
  [
    {
      name: 'Senior Trader',
      description: 'Can execute high-value trades',
      permissions: ['create:order', 'read:order', 'update:order', 'execute:trade', 'approve:trade']
    },
    {
      name: 'Compliance Reviewer',
      description: 'Reviews compliance documentation',
      permissions: ['read:compliance', 'update:compliance', 'approve:compliance']
    }
  ],
  // Additional custom roles for other organizations...
];

// Function to seed organizations and users
export async function seedOrganizationsAndUsers() {
  console.log('Starting to seed organizations and users...');

  const hashedPassword = await bcrypt.hash('Password123!', 10);
  const organizations = [];
  const users = [];

  // Create organizations
  for (const orgData of organizationData) {
    // Check if organization already exists
    const existingOrg = await prisma.organization.findFirst({
      where: { name: orgData.name }
    });

    if (existingOrg) {
      console.log(`Organization ${orgData.name} already exists. Skipping creation.`);
      organizations.push(existingOrg);
    } else {
      const newOrg = await prisma.organization.create({
        data: orgData
      });
      console.log(`Created organization: ${newOrg.name} (${newOrg.id})`);
      organizations.push(newOrg);
    }
  }

  // Create users for each organization
  for (let i = 0; i < Math.min(organizations.length, userData.length); i++) {
    const orgUsers = [];

    for (const user of userData[i]) {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: user.email }
      });

      if (existingUser) {
        console.log(`User ${user.email} already exists. Skipping creation.`);
        orgUsers.push(existingUser);
      } else {
        const newUser = await prisma.user.create({
          data: {
            ...user,
            password: hashedPassword,
            emailVerified: new Date(),
            organization: {
              connect: {
                id: organizations[i].id
              }
            }
          }
        });
        console.log(`Created user: ${newUser.email} (${newUser.id})`);
        orgUsers.push(newUser);
      }
    }

    users.push(orgUsers);
  }

  // Create admin user if it doesn't exist
  let adminUser = await prisma.user.findFirst({
    where: { role: "ADMIN" }
  });

  if (!adminUser) {
    const platformOrg = organizations.find(org => org.name === 'Carbon Exchange Platform');

    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: await bcrypt.hash('Admin123!', 10),
        role: "ADMIN",
        emailVerified: new Date(),
        organization: {
          connect: {
            id: platformOrg.id
          }
        }
      }
    });
    console.log(`Created admin user: ${adminUser.email} (${adminUser.id})`);
  } else {
    console.log(`Admin user already exists: ${adminUser.email} (${adminUser.id})`);
  }

  // Create teams for each organization
  for (let i = 0; i < Math.min(organizations.length, teamData.length); i++) {
    for (const team of teamData[i]) {
      // Check if team already exists
      const existingTeam = await prisma.team.findFirst({
        where: {
          name: team.name,
          organizationId: organizations[i].id
        }
      });

      if (existingTeam) {
        console.log(`Team ${team.name} already exists for ${organizations[i].name}. Skipping creation.`);
        continue;
      }

      const newTeam = await prisma.team.create({
        data: {
          name: team.name,
          description: team.description,
          organization: {
            connect: {
              id: organizations[i].id
            }
          }
        }
      });

      console.log(`Created team: ${newTeam.name} (${newTeam.id})`);

      // Add members to the team
      for (const memberIndex of team.members) {
        if (memberIndex < users[i].length) {
          await prisma.teamMember.create({
            data: {
              role: memberIndex === 0 ? "ADMIN" : "MEMBER",
              team: {
                connect: {
                  id: newTeam.id
                }
              },
              user: {
                connect: {
                  id: users[i][memberIndex].id
                }
              }
            }
          });
          console.log(`Added ${users[i][memberIndex].name} to team ${newTeam.name}`);
        }
      }

      // Create resource scopes for the team
      await prisma.resourceScope.create({
        data: {
          team: {
            connect: {
              id: newTeam.id
            }
          },
          resourceType: 'carbon_credit',
          accessLevel: "READ"
        }
      });

      await prisma.resourceScope.create({
        data: {
          team: {
            connect: {
              id: newTeam.id
            }
          },
          resourceType: 'project',
          accessLevel: "READ"
        }
      });
    }
  }

  // Create custom roles and assign to users
  for (let i = 0; i < Math.min(organizations.length, customRoleData.length); i++) {
    for (let j = 0; j < customRoleData[i].length; j++) {
      const roleData = customRoleData[i][j];

      // Check if role already exists
      const existingRole = await prisma.customRole.findFirst({
        where: {
          name: roleData.name,
          organizationId: organizations[i].id
        }
      });

      if (existingRole) {
        console.log(`Role ${roleData.name} already exists for ${organizations[i].name}. Skipping creation.`);
        continue;
      }

      // First create the custom role
      const newRole = await prisma.customRole.create({
        data: {
          name: roleData.name,
          description: roleData.description,
          organization: {
            connect: {
              id: organizations[i].id
            }
          }
        }
      });

      // Then create permissions for each permission string
      for (const permName of roleData.permissions) {
        // Check if permission exists
        let permission = await prisma.permission.findUnique({
          where: { name: permName }
        });

        // Create permission if it doesn't exist
        if (!permission) {
          permission = await prisma.permission.create({
            data: {
              name: permName,
              displayName: permName.split(':').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' '),
              description: `Permission to ${permName.replace(':', ' ')}`,
              category: permName.split(':')[1] || 'general'
            }
          });
          console.log(`Created permission: ${permission.name}`);
        }

        // Create role permission mapping
        await prisma.rolePermission.create({
          data: {
            role: {
              connect: {
                id: newRole.id
              }
            },
            permission: {
              connect: {
                id: permission.id
              }
            }
          }
        });
        console.log(`Added permission ${permission.name} to role ${newRole.name}`);
      }

      console.log(`Created custom role: ${newRole.name} (${newRole.id})`);

      // Assign role to a user (if available)
      if (j < users[i].length) {
        await prisma.userCustomRole.create({
          data: {
            user: {
              connect: {
                id: users[i][j].id
              }
            },
            role: {
              connect: {
                id: newRole.id
              }
            }
          }
        });
        console.log(`Assigned role ${newRole.name} to user ${users[i][j].name}`);
      }
    }
  }

  return { organizations, users, adminUser };
}

// For direct execution of this script
if (require.main === module) {
  seedOrganizationsAndUsers()
    .catch(e => {
      console.error('Error seeding organizations and users:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
