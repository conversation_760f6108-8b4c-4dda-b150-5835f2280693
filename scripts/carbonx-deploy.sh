#!/bin/bash

# Carbonx Unified Deployment Script
# This script provides a single command to deploy the application to any environment
# (development, pre-production, or production) with visual feedback and error logging.

# Exit on error
set -e

# Color codes for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Unicode symbols for better visual feedback
CHECK_MARK="✓"
CROSS_MARK="✗"
WARNING="⚠"
ARROW="→"
GEAR="⚙"
ROCKET="🚀"
DATABASE="🗄️"
GLOBE="🌐"
LOCK="🔒"
CLOCK="🕒"

# Log file
LOG_DIR="logs"
mkdir -p $LOG_DIR
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/deploy_${TIMESTAMP}.log"

# Function to log messages to file
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# Function to print section headers
print_header() {
    echo -e "\n${BOLD}${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' {1..50})${NC}"
    log_message "SECTION: $1"
}

# Function to print step information
print_step() {
    echo -e "\n${BOLD}${CYAN}Step $1:${NC} ${CYAN}$2${NC}"
    log_message "STEP $1: $2"
}

# Function to print information
print_info() {
    echo -e "${BLUE}${INFO_SYMBOL:-ℹ} $1${NC}"
    log_message "INFO: $1"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}${CHECK_MARK} $1${NC}"
    log_message "SUCCESS: $1"
}

# Function to print warning messages
print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
    log_message "WARNING: $1"
}

# Function to print error messages
print_error() {
    echo -e "${RED}${CROSS_MARK} $1${NC}"
    log_message "ERROR: $1"
}

# Function to print command output
print_output() {
    echo -e "${MAGENTA}$1${NC}"
    log_message "OUTPUT: $1"
}

# Function to execute commands with visual feedback
execute_command() {
    local command="$1"
    local description="$2"
    local show_output="${3:-false}"

    echo -e "${BLUE}${GEAR} $description...${NC}"
    log_message "EXECUTING: $command"

    if [ "$show_output" = true ]; then
        echo -e "${CYAN}$(printf '~%.0s' {1..50})${NC}"
        eval "$command" 2>&1 | tee -a $LOG_FILE
        local exit_code=${PIPESTATUS[0]}
        echo -e "${CYAN}$(printf '~%.0s' {1..50})${NC}"
    else
        eval "$command" >> $LOG_FILE 2>&1
        local exit_code=$?
    fi

    if [ $exit_code -eq 0 ]; then
        print_success "$description completed successfully"
    else
        print_error "$description failed (exit code: $exit_code)"
        print_info "Check the log file for details: $LOG_FILE"
        return $exit_code
    fi
}

# Function to check if Docker is installed and running
check_docker() {
    print_info "Checking Docker installation..."

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        print_info "Please install Docker first:"
        print_output "  https://docs.docker.com/get-docker/"
        return 1
    fi

    # Check if we need to use sudo for Docker commands
    if sudo docker info &> /dev/null; then
        print_warning "Docker requires sudo privileges. Using sudo for Docker commands."
        DOCKER="sudo docker"
        DOCKER_COMPOSE="sudo docker compose"
    elif docker info &> /dev/null; then
        DOCKER="docker"
        DOCKER_COMPOSE="docker compose"
    else
        print_error "Docker daemon is not running or you don't have permission to use Docker"
        print_info "Start Docker daemon or add your user to the docker group:"
        print_output "  sudo systemctl start docker"
        print_output "  sudo usermod -aG docker $USER && newgrp docker"
        return 1
    fi

    # Export these variables so child scripts can use them
    export DOCKER
    export DOCKER_COMPOSE

    print_success "Docker is installed and running"
    return 0
}

# Function to check if Docker Compose is installed
check_docker_compose() {
    print_info "Checking Docker Compose installation..."

    # If DOCKER_COMPOSE is already set by check_docker, use that
    if [ -n "$DOCKER_COMPOSE" ]; then
        # Just verify it works
        if $DOCKER_COMPOSE version &> /dev/null; then
            print_success "Docker Compose is installed and working"
            return 0
        fi
    fi

    # Check for docker compose command (Docker Compose V2)
    if $DOCKER compose version &> /dev/null; then
        DOCKER_COMPOSE="$DOCKER compose"
        print_success "Docker Compose V2 is installed"
        return 0
    fi

    # Check for docker-compose command (Docker Compose V1)
    if command -v docker-compose &> /dev/null; then
        if [ -n "$DOCKER" ] && [[ "$DOCKER" == *"sudo"* ]]; then
            DOCKER_COMPOSE="sudo docker-compose"
        else
            DOCKER_COMPOSE="docker-compose"
        fi
        print_success "Docker Compose V1 is installed"
        return 0
    fi

    print_error "Docker Compose is not installed"
    print_info "Please install Docker Compose:"
    print_output "  https://docs.docker.com/compose/install/"
    return 1
}

# Function to check if pnpm is installed and set the command
check_pnpm() {
    print_info "Checking pnpm installation..."

    # Check if we're running as root/sudo
    if [ "$(id -u)" -eq 0 ]; then
        print_info "Running with sudo/root privileges. Trying to find pnpm in the system..."

        # Get the real user who invoked sudo
        REAL_USER=$(logname 2>/dev/null || echo "${SUDO_USER:-$USER}")
        REAL_HOME=$(eval echo ~$REAL_USER)

        # Try to find pnpm in common locations for the real user
        if [ -f "$REAL_HOME/.local/share/pnpm/pnpm" ]; then
            PNPM_CMD="$REAL_HOME/.local/share/pnpm/pnpm"
            print_success "pnpm found at $PNPM_CMD"
            return 0
        fi

        # Try to run pnpm as the real user to get its path
        PNPM_PATH=$(sudo -u $REAL_USER which pnpm 2>/dev/null)
        if [ -n "$PNPM_PATH" ] && [ -f "$PNPM_PATH" ]; then
            PNPM_CMD="$PNPM_PATH"
            print_success "pnpm found at $PNPM_CMD"
            return 0
        fi

        # Try running with sudo -E to preserve environment
        if sudo -E -u $REAL_USER pnpm --version &>/dev/null; then
            PNPM_CMD="sudo -E -u $REAL_USER pnpm"
            print_success "Using pnpm with sudo -E -u $REAL_USER"
            return 0
        fi
    else
        # Normal user execution - check if pnpm is in PATH
        if command -v pnpm &> /dev/null; then
            PNPM_CMD="pnpm"
            print_success "pnpm is installed and in PATH"
            return 0
        fi

        # Try to find pnpm in common locations
        if [ -f "$HOME/.local/share/pnpm/pnpm" ]; then
            PNPM_CMD="$HOME/.local/share/pnpm/pnpm"
            print_success "pnpm found at $PNPM_CMD"
            return 0
        elif [ -f "/usr/local/bin/pnpm" ]; then
            PNPM_CMD="/usr/local/bin/pnpm"
            print_success "pnpm found at $PNPM_CMD"
            return 0
        fi
    fi

    print_error "pnpm not found. Please install pnpm or add it to your PATH."
    print_info "You can install pnpm with: npm install -g pnpm"
    print_info "Alternatively, run this script without sudo: ./scripts/carbonx-deploy.sh"
    return 1
}

# Function to check and create .env file
check_env_file() {
    local env_type="$1"

    print_info "Checking .env file..."

    if [ ! -f .env ]; then
        print_warning "No .env file found. Creating from example..."

        if [ "$env_type" = "production" ] && [ -f .env.production.example ]; then
            cp .env.production.example .env
        else
            cp .env.example .env
        fi

        print_warning "Please edit the .env file with your $env_type configuration."
        print_info "Press Enter to continue after editing the .env file, or Ctrl+C to abort."
        read -p ""
    fi

    print_success ".env file is ready"
}

# Function to deploy to development environment
deploy_development() {
    print_header "${ROCKET} DEPLOYING TO DEVELOPMENT ENVIRONMENT"

    # Check and create .env file
    check_env_file "development"

    # Step 1: Start the database
    print_step "1" "Starting PostgreSQL container"
    execute_command "./scripts/start-db.sh" "Starting PostgreSQL container" true

    # Step 2: Set up the database
    print_step "2" "Setting up the database"

    # Generate Prisma client
    print_info "Generating Prisma client..."
    execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client" true

    # Run migrations
    print_info "Running database migrations..."

    # Try to run the fix-db-constraints script first to ensure constraints are fixed
    print_info "Fixing database constraints before migration..."
    if [ -f "./scripts/fix-db-constraints.sh" ]; then
        execute_command "./scripts/fix-db-constraints.sh" "Fixing database constraints" true
    fi

    # Reset migration state if needed
    print_info "Checking if migration reset is needed..."
    if [ -f "./scripts/reset-migration.sh" ]; then
        execute_command "./scripts/reset-migration.sh" "Resetting migration state" true
    fi

    # Try direct database migration with --skip-generate and --name
    print_info "Running migrations with --skip-generate..."
    if ! execute_command "$PNPM_CMD exec prisma migrate dev --skip-generate --name safe_migration" "Running database migrations" true; then
        print_warning "Migration failed. Trying direct schema push..."

        # If migrations fail, try direct schema push as a last resort
        if [ -f "./scripts/direct-schema-push.sh" ]; then
            print_info "Using direct schema push to bypass migrations..."
            if ! execute_command "./scripts/direct-schema-push.sh" "Pushing schema directly" true; then
                print_error "Direct schema push failed. Please check your database and schema."
                print_info "You may need to manually fix the database schema."
                print_info "Try running: ./scripts/reset-database.sh"
                return 1
            fi
        else
            print_error "Migration failed and direct-schema-push.sh not found."
            print_info "You may need to manually fix the database schema."
            print_info "Try running: ./scripts/run-sql-fix.sh"
            return 1
        fi
    fi

    # Then generate the client separately
    print_info "Generating Prisma client after migration..."
    execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client" true

    # Step 3: Run deployment seed
    print_step "3" "Running deployment seed"
    print_info "This will create the admin user and essential data..."
    execute_command "node scripts/deployment-seed.js" "Running deployment seed" true

    # Step 4: Start the application
    print_step "4" "Starting the application"

    # Ask user for deployment method
    print_info "Do you want to run the application using Docker or directly with Next.js?"
    echo -e "  ${BLUE}1)${NC} Docker (recommended for full environment)"
    echo -e "  ${BLUE}2)${NC} Next.js (faster for local development)"
    read -p "Enter your choice (1-2): " DEV_CHOICE

    case $DEV_CHOICE in
        1)
            # Start the db-init service first to initialize the database
            execute_command "$DOCKER_COMPOSE up -d db-init" "Initializing database" true
            execute_command "$DOCKER_COMPOSE logs -f db-init" "Showing database initialization logs" true

            # Then start the app-dev service
            execute_command "$DOCKER_COMPOSE up app-dev" "Starting Docker development environment" true
            ;;
        2)
            execute_command "$PNPM_CMD dev" "Starting Next.js development server" true
            ;;
        *)
            print_warning "Invalid choice. Using Next.js development server."
            execute_command "$PNPM_CMD dev" "Starting Next.js development server" true
            ;;
    esac
}

# Function to deploy to pre-production environment
deploy_preprod() {
    print_header "${ROCKET} DEPLOYING TO PRE-PRODUCTION ENVIRONMENT"

    # Check and create .env file
    check_env_file "pre-production"

    # Step 1: Build Docker images
    print_step "1" "Building Docker images"
    execute_command "$DOCKER_COMPOSE -f docker-compose.preprod.yml build" "Building Docker images" true

    # Step 2: Initialize SSL certificates if needed
    print_step "2" "Checking SSL certificates"

    # Get domain from .env file
    DOMAIN_NAME=$(grep NEXTAUTH_URL .env | cut -d'=' -f2 | sed 's/https:\/\///' | sed 's/\/.*//')

    if [ -z "$DOMAIN_NAME" ]; then
        print_warning "No domain found in NEXTAUTH_URL. Using default domain."
        DOMAIN_NAME="preprod.example.com"

        # Update NEXTAUTH_URL in .env
        sed -i.bak "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$DOMAIN_NAME|g" .env
        rm -f .env.bak
    fi

    print_info "Using domain: $DOMAIN_NAME"

    # Step 3: Start the pre-production environment
    print_step "3" "Starting pre-production environment"
    execute_command "$DOCKER_COMPOSE -f docker-compose.preprod.yml up -d" "Starting pre-production environment" true

    # Step 4: Initialize the database
    print_step "4" "Initializing database"
    print_info "This will run migrations and seed essential data for the application to function."
    print_info "Admin credentials will be displayed when complete."
    execute_command "$DOCKER_COMPOSE -f docker-compose.preprod.yml logs -f db-init" "Initializing database" true

    # Wait for services to start
    print_info "Waiting for services to start..."
    sleep 10

    # Step 5: Check service status
    print_step "5" "Checking service status"
    SERVICES_RUNNING=$($DOCKER_COMPOSE -f docker-compose.preprod.yml ps --services --filter "status=running" | wc -l)
    SERVICES_TOTAL=$($DOCKER_COMPOSE -f docker-compose.preprod.yml ps --services | wc -l)

    if [ "$SERVICES_RUNNING" -eq "$SERVICES_TOTAL" ]; then
        print_success "All services are running successfully!"
    else
        print_warning "Some services might not be running."
        $DOCKER_COMPOSE -f docker-compose.preprod.yml ps
    fi

    print_header "${CHECK_MARK} DEPLOYMENT COMPLETE"
    print_success "Carbonx has been successfully deployed to pre-production!"
    print_info "You can access your application at: https://$DOMAIN_NAME"
}

# Function to deploy to production environment
deploy_production() {
    print_header "${ROCKET} DEPLOYING TO PRODUCTION ENVIRONMENT"

    # Check and create .env file
    check_env_file "production"

    # Step 1: Build Docker images
    print_step "1" "Building Docker images"
    execute_command "$DOCKER_COMPOSE -f docker-compose.prod.yml build" "Building Docker images" true

    # Step 2: Initialize SSL certificates if needed
    print_step "2" "Checking SSL certificates"

    # Get domain from .env file
    DOMAIN_NAME=$(grep NEXTAUTH_URL .env | cut -d'=' -f2 | sed 's/https:\/\///' | sed 's/\/.*//')

    if [ -z "$DOMAIN_NAME" ]; then
        print_warning "No domain found in NEXTAUTH_URL. Please enter your domain name:"
        read -p "Domain (e.g., example.com): " DOMAIN_NAME

        if [ -z "$DOMAIN_NAME" ]; then
            print_error "No domain provided. Exiting."
            exit 1
        fi

        # Update NEXTAUTH_URL in .env
        sed -i.bak "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$DOMAIN_NAME|g" .env
        rm -f .env.bak
    fi

    print_info "Using domain: $DOMAIN_NAME"
    export DOMAIN_NAME

    # Ask about SSL certificates
    print_info "Do you want to initialize SSL certificates? (Requires domain pointing to this server)"
    read -p "Initialize SSL certificates? (y/n): " SSL_INIT

    if [[ "$SSL_INIT" =~ ^[Yy]$ ]]; then
        execute_command "./scripts/init-letsencrypt.sh" "Initializing SSL certificates" true
    else
        print_warning "Skipping SSL certificate initialization."
        print_info "You will need to set up SSL certificates manually or run the script later."
    fi

    # Step 3: Start the production environment
    print_step "3" "Starting production environment"
    execute_command "$DOCKER_COMPOSE -f docker-compose.prod.yml up -d" "Starting production environment" true

    # Wait for services to start
    print_info "Waiting for services to start..."
    sleep 10

    # Step 4: Check service status
    print_step "4" "Checking service status"
    SERVICES_RUNNING=$($DOCKER_COMPOSE -f docker-compose.prod.yml ps --services --filter "status=running" | wc -l)
    SERVICES_TOTAL=$($DOCKER_COMPOSE -f docker-compose.prod.yml ps --services | wc -l)

    if [ "$SERVICES_RUNNING" -eq "$SERVICES_TOTAL" ]; then
        print_success "All services are running successfully!"
    else
        print_warning "Some services might not be running."
        $DOCKER_COMPOSE -f docker-compose.prod.yml ps
    fi

    # Step 5: Schedule database backup
    print_step "5" "Scheduling database backups"
    print_info "Setting up daily database backups at 2 AM..."
    (crontab -l 2>/dev/null; echo "0 2 * * * cd $(pwd) && $DOCKER_COMPOSE -f docker-compose.prod.yml run --rm backup") | crontab -

    print_header "${CHECK_MARK} DEPLOYMENT COMPLETE"
    print_success "Carbonx has been successfully deployed to production!"
    print_info "You can access your application at: https://$DOMAIN_NAME"
}

# Main function
main() {
    # Start logging
    log_message "Starting deployment script"

    # Print welcome message
    print_header "CARBONX DEPLOYMENT SCRIPT"
    print_info "This script will deploy the Carbonx application to your chosen environment."
    print_info "Log file: $LOG_FILE"

    # Check if running with sudo and warn
    if [ "$(id -u)" -eq 0 ]; then
        print_warning "You are running this script with sudo/root privileges."
        print_info "For better compatibility, it's recommended to run without sudo:"
        print_output "  ./scripts/carbonx-deploy.sh"
        print_info "Only Docker commands will be executed with sudo as needed."
        print_info "Press Enter to continue anyway, or Ctrl+C to abort."
        read -p ""
    fi

    # Check Docker, Docker Compose, and pnpm
    check_docker || exit 1
    check_docker_compose || exit 1
    check_pnpm || exit 1

    # Get environment choice from command line argument or ask user
    ENV_CHOICE="$1"

    # If no argument was provided, ask for user input
    if [ -z "$ENV_CHOICE" ]; then
        print_info "Which environment would you like to deploy to?"
        echo -e "  ${BLUE}1)${NC} Development (local development with Docker)"
        echo -e "  ${BLUE}2)${NC} Pre-Production (test environment with test blockchain networks)"
        echo -e "  ${BLUE}3)${NC} Production (live environment with real blockchain networks)"
        read -p "Enter your choice (1-3): " ENV_CHOICE
    fi

    case $ENV_CHOICE in
        1)
            deploy_development
            ;;
        2)
            deploy_preprod
            ;;
        3)
            deploy_production
            ;;
        *)
            print_error "Invalid choice. Exiting."
            exit 1
            ;;
    esac

    # End logging
    log_message "Deployment script completed successfully"
}

# Run the main function
main "$@"
