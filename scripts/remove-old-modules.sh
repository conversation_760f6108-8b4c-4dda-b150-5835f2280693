#!/bin/bash

# Script to remove old module files after migration is complete
# Usage: ./scripts/remove-old-modules.sh

# Check if there are any files still using old imports
echo "Checking for files still using old imports..."

old_imports=$(grep -r "from '@/lib/analytics-service\|from '@/lib/blockchain-client\|from '@/lib/gas-estimation\|from '@/lib/gas-optimizer\|from '@/lib/notification-service\|from '@/lib/carbon-credit-service\|from '@/lib/order-service\|from '@/lib/marketplace-service\|from '@/lib/audit-service\|from '@/lib/payment-service" --include="*.ts" --include="*.tsx" carbon-exchange/src)

if [ -n "$old_imports" ]; then
  echo "WARNING: Found files still using old imports. Please migrate these files first:"
  echo "$old_imports"
  echo ""
  echo "Run ./scripts/migrate-imports.sh for more information."
  exit 1
fi

echo "No files found using old imports. Safe to remove old modules."
echo ""

# List old module files
echo "The following files will be removed:"
echo ""
echo "carbon-exchange/src/lib/analytics-service.ts"
echo "carbon-exchange/src/lib/analytics.ts"
echo "carbon-exchange/src/lib/blockchain-client.ts"
echo "carbon-exchange/src/lib/blockchain-config.ts"
echo "carbon-exchange/src/lib/blockchain.ts"
echo "carbon-exchange/src/lib/gas-estimation.ts"
echo "carbon-exchange/src/lib/gas-optimizer.ts"
echo "carbon-exchange/src/lib/notification-service.ts"
echo "carbon-exchange/src/lib/notifications.ts"
echo "carbon-exchange/src/lib/carbon-credit-service.ts"
echo "carbon-exchange/src/lib/carbon-credit-verification.ts"
echo "carbon-exchange/src/lib/order-service.ts"
echo "carbon-exchange/src/lib/marketplace-service.ts"
echo "carbon-exchange/src/lib/audit-service.ts"
echo "carbon-exchange/src/lib/payment-service.ts"
echo ""

# Prompt for confirmation
read -p "Are you sure you want to remove these files? (y/n) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
  # Remove old module files
  rm -f carbon-exchange/src/lib/analytics-service.ts
  rm -f carbon-exchange/src/lib/analytics.ts
  rm -f carbon-exchange/src/lib/blockchain-client.ts
  rm -f carbon-exchange/src/lib/blockchain-config.ts
  rm -f carbon-exchange/src/lib/blockchain.ts
  rm -f carbon-exchange/src/lib/gas-estimation.ts
  rm -f carbon-exchange/src/lib/gas-optimizer.ts
  rm -f carbon-exchange/src/lib/notification-service.ts
  rm -f carbon-exchange/src/lib/notifications.ts
  rm -f carbon-exchange/src/lib/carbon-credit-service.ts
  rm -f carbon-exchange/src/lib/carbon-credit-verification.ts
  rm -f carbon-exchange/src/lib/order-service.ts
  rm -f carbon-exchange/src/lib/marketplace-service.ts
  rm -f carbon-exchange/src/lib/audit-service.ts
  rm -f carbon-exchange/src/lib/payment-service.ts
  
  echo "Old module files removed successfully."
else
  echo "Operation cancelled."
fi
