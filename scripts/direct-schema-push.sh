#!/bin/bash

# This script directly pushes the schema to the database without migrations
# Use this when migrations are failing

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if pnpm is installed
if command -v pnpm &> /dev/null; then
    PNPM_CMD="pnpm"
elif [ -f "$HOME/.local/share/pnpm/pnpm" ]; then
    PNPM_CMD="$HOME/.local/share/pnpm/pnpm"
else
    echo -e "${RED}pnpm not found. Please install pnpm or add it to your PATH.${NC}"
    exit 1
fi

echo -e "${BLUE}Directly pushing schema to database...${NC}"
echo -e "${YELLOW}This will bypass migrations and directly update the database schema.${NC}"
echo -e "${YELLOW}This should only be used when migrations are failing.${NC}"

# First, generate the Prisma client
echo -e "${BLUE}Generating Prisma client...${NC}"
$PNPM_CMD exec prisma generate

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to generate Prisma client.${NC}"
    exit 1
fi

# Push the schema directly to the database
echo -e "${BLUE}Pushing schema to database...${NC}"
$PNPM_CMD exec prisma db push --force-reset

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to push schema to database.${NC}"
    exit 1
fi

echo -e "${GREEN}Schema successfully pushed to database!${NC}"
echo -e "${BLUE}Now try running the deployment script again:${NC}"
echo -e "  ./scripts/carbonx-deploy.sh"
