#!/bin/bash

# Colors for console output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Determine the command to use for pnpm
if command -v pnpm &> /dev/null; then
    PNPM_CMD="pnpm"
else
    PNPM_CMD="npx pnpm"
fi

# Determine the command to use for docker-compose
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo -e "${RED}Neither docker-compose nor docker compose is available. Please install Docker and Docker Compose.${NC}"
    exit 1
fi

# Create a log directory if it doesn't exist
mkdir -p logs

# Create a timestamp for the log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/prisma_migrate_${TIMESTAMP}.log"

# Function to log messages
log() {
    local message="$1"
    local color="$2"
    echo -e "${color}${message}${NC}"
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $message" >> "$LOG_FILE"
}

# Function to execute a command and log the result
execute_command() {
    local command="$1"
    local description="$2"
    local allow_failure="${3:-false}"
    
    log "EXECUTING: $command" "$BLUE"
    echo "$ $command" >> "$LOG_FILE"
    
    if eval "$command" >> "$LOG_FILE" 2>&1; then
        log "SUCCESS: $description completed successfully" "$GREEN"
        return 0
    else
        local exit_code=$?
        if [ "$allow_failure" = "true" ]; then
            log "WARNING: $description failed (exit code: $exit_code)" "$YELLOW"
            log "Check the log file for details: $LOG_FILE" "$BLUE"
            return $exit_code
        else
            log "ERROR: $description failed (exit code: $exit_code)" "$RED"
            log "Check the log file for details: $LOG_FILE" "$BLUE"
            return $exit_code
        fi
    fi
}

# Function to display help
show_help() {
    echo -e "${BLUE}Prisma Migration Script${NC}"
    echo "This script helps manage Prisma migrations in different scenarios."
    echo
    echo "Usage: $0 [OPTION]"
    echo
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -d, --dev                  Run a development migration (prisma migrate dev)"
    echo "  -p, --deploy               Run a deployment migration (prisma migrate deploy)"
    echo "  -r, --reset                Reset the database and run a clean migration"
    echo "  -f, --fix                  Fix migration issues by resetting and creating a clean migration"
    echo "  -s, --schema-push          Push the schema directly to the database (bypasses migrations)"
    echo "  -c, --create-only NAME     Create a migration without applying it"
    echo "  -g, --generate             Generate Prisma client"
    echo "  --seed                     Run the deployment seed after migration"
    echo
    echo "Examples:"
    echo "  $0 -d                      Run a development migration"
    echo "  $0 -p                      Run a deployment migration"
    echo "  $0 -r                      Reset the database and run a clean migration"
    echo "  $0 -f                      Fix migration issues"
    echo "  $0 -s                      Push the schema directly to the database"
    echo "  $0 -c initial_migration    Create a migration named 'initial_migration'"
    echo "  $0 -d --seed               Run a development migration and seed the database"
    echo
}

# Function to check if the database is running
check_database() {
    log "Checking if the database is running..." "$BLUE"
    if ! $DOCKER_COMPOSE ps | grep -q "db.*running"; then
        log "PostgreSQL container is not running. Starting it..." "$YELLOW"
        execute_command "$DOCKER_COMPOSE up -d db" "Starting database container"
        
        # Wait for PostgreSQL to be ready
        log "Waiting for PostgreSQL to be ready..." "$BLUE"
        until $DOCKER_COMPOSE exec db pg_isready -U postgres; do
            echo -e "${YELLOW}PostgreSQL is not ready yet... waiting${NC}"
            sleep 2
        done
    else
        log "PostgreSQL container is already running." "$GREEN"
    fi
}

# Function to reset the database
reset_database() {
    log "Resetting the database..." "$BLUE"
    log "This will drop all tables in the database." "$YELLOW"

    SQL_SCRIPT=$(cat <<EOF
-- Drop all tables in the public schema
DO \$\$
DECLARE
    r RECORD;
BEGIN
    -- Disable triggers temporarily
    EXECUTE 'SET session_replication_role = replica';
    
    -- Drop all tables in the public schema
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS public.' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
    
    -- Re-enable triggers
    EXECUTE 'SET session_replication_role = DEFAULT';
    
    -- Drop the _prisma_migrations table if it exists
    DROP TABLE IF EXISTS _prisma_migrations;
END \$\$;
EOF
    )

    execute_command "$DOCKER_COMPOSE exec -T db psql -U postgres -d carbon_exchange -c \"$SQL_SCRIPT\"" "Resetting database"
}

# Function to run the deployment seed
run_seed() {
    log "Running deployment seed..." "$BLUE"
    execute_command "node scripts/deployment-seed.js" "Running deployment seed"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    show_help
    exit 0
fi

# Default values
DEV_MIGRATION=false
DEPLOY_MIGRATION=false
RESET_DATABASE=false
FIX_MIGRATION=false
SCHEMA_PUSH=false
CREATE_ONLY=false
GENERATE_CLIENT=false
RUN_SEED=false
MIGRATION_NAME=""

# Parse arguments
while [ $# -gt 0 ]; do
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dev)
            DEV_MIGRATION=true
            shift
            ;;
        -p|--deploy)
            DEPLOY_MIGRATION=true
            shift
            ;;
        -r|--reset)
            RESET_DATABASE=true
            shift
            ;;
        -f|--fix)
            FIX_MIGRATION=true
            shift
            ;;
        -s|--schema-push)
            SCHEMA_PUSH=true
            shift
            ;;
        -c|--create-only)
            CREATE_ONLY=true
            if [ $# -gt 1 ] && [[ ! "$2" =~ ^- ]]; then
                MIGRATION_NAME="$2"
                shift 2
            else
                log "Error: --create-only requires a migration name" "$RED"
                exit 1
            fi
            ;;
        -g|--generate)
            GENERATE_CLIENT=true
            shift
            ;;
        --seed)
            RUN_SEED=true
            shift
            ;;
        *)
            log "Unknown option: $1" "$RED"
            show_help
            exit 1
            ;;
    esac
done

# Main script starts here
log "=== Prisma Migration Script ===" "$BLUE"

# Check if the database is running
check_database

# Execute the requested actions
if [ "$FIX_MIGRATION" = "true" ]; then
    # Fix migration issues
    reset_database
    execute_command "$PNPM_CMD exec prisma migrate dev --name initial_migration --create-only" "Creating migration"
    execute_command "$PNPM_CMD exec prisma migrate deploy" "Applying migration"
    execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client"
    if [ "$RUN_SEED" = "true" ]; then
        run_seed
    fi
elif [ "$RESET_DATABASE" = "true" ]; then
    # Reset the database and run a clean migration
    reset_database
    execute_command "$PNPM_CMD exec prisma migrate deploy" "Applying migration"
    if [ "$GENERATE_CLIENT" = "true" ]; then
        execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client"
    fi
    if [ "$RUN_SEED" = "true" ]; then
        run_seed
    fi
elif [ "$SCHEMA_PUSH" = "true" ]; then
    # Push the schema directly to the database
    log "Pushing schema directly to the database..." "$BLUE"
    log "This will bypass migrations and directly update the database schema." "$YELLOW"
    execute_command "$PNPM_CMD exec prisma db push" "Pushing schema to database"
    if [ "$GENERATE_CLIENT" = "true" ]; then
        execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client"
    fi
    if [ "$RUN_SEED" = "true" ]; then
        run_seed
    fi
elif [ "$CREATE_ONLY" = "true" ]; then
    # Create a migration without applying it
    log "Creating migration '$MIGRATION_NAME' without applying it..." "$BLUE"
    execute_command "$PNPM_CMD exec prisma migrate dev --name $MIGRATION_NAME --create-only" "Creating migration"
elif [ "$DEV_MIGRATION" = "true" ]; then
    # Run a development migration
    log "Running development migration..." "$BLUE"
    execute_command "$PNPM_CMD exec prisma migrate dev" "Running development migration"
    if [ "$RUN_SEED" = "true" ]; then
        run_seed
    fi
elif [ "$DEPLOY_MIGRATION" = "true" ]; then
    # Run a deployment migration
    log "Running deployment migration..." "$BLUE"
    execute_command "$PNPM_CMD exec prisma migrate deploy" "Running deployment migration"
    if [ "$GENERATE_CLIENT" = "true" ]; then
        execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client"
    fi
    if [ "$RUN_SEED" = "true" ]; then
        run_seed
    fi
elif [ "$GENERATE_CLIENT" = "true" ]; then
    # Generate Prisma client
    execute_command "$PNPM_CMD exec prisma generate" "Generating Prisma client"
fi

# Final message
log "Script execution completed!" "$GREEN"
log "Check the log file for details: $LOG_FILE" "$BLUE"

exit 0
