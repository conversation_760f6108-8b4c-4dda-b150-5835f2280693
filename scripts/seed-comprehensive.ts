import { db as prisma } from '../src/lib/db';
import { seedOrganizationsAndUsers } from './seed/seed-organizations-users';
import { seedProjects } from './seed/seed-projects';
import { seedCarbonCredits } from './seed/seed-carbon-credits';
import { seedWalletsAndTransactions } from './seed/seed-wallets-transactions';
import { seedMarketplace } from './seed/seed-marketplace';
import { seedCompliance } from './seed/seed-compliance';
import { seedNotificationsAndAudit } from './seed/seed-notifications-audit';

/**
 * Comprehensive seed script that populates all tables with sufficient data
 * This script creates:
 * - Multiple organizations (5-10) with different statuses and verification levels
 * - Multiple users per organization with different roles
 * - Multiple projects (10-15) with different types and statuses
 * - Multiple carbon credits (20-30) with different statuses, standards, and vintages
 * - Multiple wallets for users and organizations
 * - Multiple orders and transactions with different statuses
 * - Multiple tokenizations with different statuses
 * - Compliance documents and verification records
 * - Historical data for charts and analytics
 * - Teams and team members
 * - Permissions and roles
 * - Notifications and audit logs
 */
async function main() {
  console.log('Starting comprehensive seed script...');
  console.log('This will populate all tables with sufficient data for development and testing.');
  console.log('-------------------------------------------------------------------');

  try {
    // Step 1: Seed organizations and users
    console.log('\n=== Step 1: Seeding Organizations and Users ===');
    const { organizations, users, adminUser } = await seedOrganizationsAndUsers();
    console.log(`Created ${organizations.length} organizations and ${users.flat().length + 1} users (including admin)`);

    // Step 2: Seed projects
    console.log('\n=== Step 2: Seeding Projects ===');
    try {
      const projects = await seedProjects(organizations);
      console.log(`Created ${projects.length} projects`);

      // Step 3: Seed carbon credits
      console.log('\n=== Step 3: Seeding Carbon Credits ===');
      try {
        const carbonCredits = await seedCarbonCredits(organizations, projects, users);
        console.log(`Created ${carbonCredits.length} carbon credits`);

        // Step 4: Seed wallets and transactions
        console.log('\n=== Step 4: Seeding Wallets and Transactions ===');
        try {
          const { wallets, transactions } = await seedWalletsAndTransactions(organizations, users, projects, carbonCredits);
          console.log(`Created ${wallets.length} wallets and ${transactions.length} transactions`);

          // Step 5: Seed marketplace
          console.log('\n=== Step 5: Seeding Marketplace ===');
          try {
            const { listings, orders } = await seedMarketplace(organizations, users, carbonCredits);
            console.log(`Created ${listings.length} marketplace listings and ${orders.length} orders`);

            // Step 6: Seed compliance
            console.log('\n=== Step 6: Seeding Compliance Data ===');
            try {
              await seedCompliance(organizations, users);
              console.log('Created compliance data for organizations and users');

              // Step 7: Seed notifications and audit logs
              console.log('\n=== Step 7: Seeding Notifications and Audit Logs ===');
              try {
                await seedNotificationsAndAudit(organizations, users, projects, carbonCredits, wallets, transactions);
                console.log('Created notifications and audit logs');
              } catch (error) {
                console.error('Error during seeding notifications and audit logs:', error);
              }
            } catch (error) {
              console.error('Error during seeding compliance data:', error);
            }
          } catch (error) {
            console.error('Error during seeding marketplace:', error);
          }
        } catch (error) {
          console.error('Error during seeding wallets and transactions:', error);
        }
      } catch (error) {
        console.error('Error during seeding carbon credits:', error);
      }
    } catch (error) {
      console.error('Error during seeding projects:', error);
    }

    console.log('\n-------------------------------------------------------------------');
    console.log('Comprehensive seed completed successfully!');
    console.log('The database now contains:');

    // Count records in main tables
    const counts = await getTableCounts();
    for (const [table, count] of Object.entries(counts)) {
      console.log(`- ${count} ${table}`);
    }

    console.log('\nYou can now use the application with a fully populated database.');

  } catch (error) {
    console.error('Error during seeding:', error);
    process.exit(1);
  }
}

/**
 * Get counts of records in main tables
 */
async function getTableCounts() {
  return {
    'Organizations': await prisma.organization.count(),
    'Users': await prisma.user.count(),
    'Projects': await prisma.project.count(),
    'Carbon Credits': await prisma.carbonCredit.count(),
    'Wallets': await prisma.wallet.count(),
    'Transactions': await prisma.transaction.count(),
    'Marketplace Listings': await prisma.marketplaceListing.count(),
    'Orders': await prisma.order.count(),
    'Teams': await prisma.team.count(),
    'Team Members': await prisma.teamMember.count(),
    'Compliance Documents': await prisma.complianceDocument.count(),
    'KYC Verifications': await prisma.kycVerification.count(),
    'AML Checks': await prisma.amlCheck.count(),
    'Notifications': await prisma.notification.count(),
    'Audit Logs': await prisma.auditLog.count()
  };
}

// Execute the main function
main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    // Close Prisma client connection
    await prisma.$disconnect();
  });
