#!/bin/bash

# This script helps set up the environment for Docker deployment

# Colors for console output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Docker Environment Setup ===${NC}"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install Docker first.${NC}"
    echo "Visit https://docs.docker.com/get-docker/ for installation instructions."
    exit 1
fi

# Check if Docker Compose is installed (either as standalone or as a Docker plugin)
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}Docker Compose is not installed. Please install Docker Compose first.${NC}"
    echo "Visit https://docs.docker.com/compose/install/ for installation instructions."
    exit 1
fi

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
else
    DOCKER_COMPOSE="docker compose"
fi

# Check if we need to use sudo for Docker commands
if ! docker ps &> /dev/null; then
    # Try with sudo
    if sudo docker ps &> /dev/null; then
        echo -e "${YELLOW}Docker requires sudo privileges. Using sudo for Docker commands.${NC}"
        DOCKER="sudo docker"
        DOCKER_COMPOSE="sudo $DOCKER_COMPOSE"
    else
        echo -e "${RED}Cannot access Docker daemon even with sudo. Please check Docker installation.${NC}"
        exit 1
    fi
else
    DOCKER="docker"
fi

echo -e "${GREEN}Docker and Docker Compose are installed.${NC}"

# Ask which environment to set up
echo -e "\n${BLUE}Which environment would you like to set up?${NC}"
echo "1) Development"
echo "2) Pre-Production"
echo "3) Production"
read -p "Enter your choice (1-3): " ENV_CHOICE

case $ENV_CHOICE in
    1)
        ENV_TYPE="development"
        ENV_FILE=".env"
        COMPOSE_FILE="docker-compose.yml"
        ;;
    2)
        ENV_TYPE="pre-production"
        ENV_FILE=".env"
        COMPOSE_FILE="docker-compose.preprod.yml"
        ;;
    3)
        ENV_TYPE="production"
        ENV_FILE=".env"
        COMPOSE_FILE="docker-compose.prod.yml"
        ;;
    *)
        echo -e "${RED}Invalid choice. Exiting.${NC}"
        exit 1
        ;;
esac

echo -e "\n${BLUE}Setting up ${ENV_TYPE} environment...${NC}"

# Check if .env file exists
if [ ! -f $ENV_FILE ]; then
    echo -e "${YELLOW}No ${ENV_FILE} file found. Creating from example...${NC}"
    if [ "$ENV_TYPE" = "production" ]; then
        cp .env.production.example $ENV_FILE
    else
        cp .env.example $ENV_FILE
    fi
    echo -e "${YELLOW}Please edit ${ENV_FILE} file with your ${ENV_TYPE} configuration.${NC}"
    echo -e "${YELLOW}Press Enter to continue when done...${NC}"
    read
fi

# Set up environment-specific variables
if [ "$ENV_TYPE" = "development" ]; then
    # Development environment uses test networks
    sed -i.bak 's/ALCHEMY_NETWORK=.*/ALCHEMY_NETWORK=eth-sepolia/g' $ENV_FILE
    sed -i.bak 's/ETHEREUM_NETWORK=.*/ETHEREUM_NETWORK=sepolia/g' $ENV_FILE
    sed -i.bak 's/POLYGON_NETWORK=.*/POLYGON_NETWORK=mumbai/g' $ENV_FILE
    sed -i.bak 's/OPTIMISM_NETWORK=.*/OPTIMISM_NETWORK=optimism-sepolia/g' $ENV_FILE
    sed -i.bak 's/ARBITRUM_NETWORK=.*/ARBITRUM_NETWORK=arbitrum-sepolia/g' $ENV_FILE
    sed -i.bak 's/BASE_NETWORK=.*/BASE_NETWORK=base-sepolia/g' $ENV_FILE
    sed -i.bak 's/NODE_ENV=.*/NODE_ENV=development/g' $ENV_FILE
elif [ "$ENV_TYPE" = "pre-production" ]; then
    # Pre-production environment uses test networks
    sed -i.bak 's/ALCHEMY_NETWORK=.*/ALCHEMY_NETWORK=eth-sepolia/g' $ENV_FILE
    sed -i.bak 's/ETHEREUM_NETWORK=.*/ETHEREUM_NETWORK=sepolia/g' $ENV_FILE
    sed -i.bak 's/POLYGON_NETWORK=.*/POLYGON_NETWORK=mumbai/g' $ENV_FILE
    sed -i.bak 's/OPTIMISM_NETWORK=.*/OPTIMISM_NETWORK=optimism-sepolia/g' $ENV_FILE
    sed -i.bak 's/ARBITRUM_NETWORK=.*/ARBITRUM_NETWORK=arbitrum-sepolia/g' $ENV_FILE
    sed -i.bak 's/BASE_NETWORK=.*/BASE_NETWORK=base-sepolia/g' $ENV_FILE
    sed -i.bak 's/NODE_ENV=.*/NODE_ENV=production/g' $ENV_FILE
else
    # Production environment uses mainnet networks
    sed -i.bak 's/ALCHEMY_NETWORK=.*/ALCHEMY_NETWORK=eth-mainnet/g' $ENV_FILE
    sed -i.bak 's/ETHEREUM_NETWORK=.*/ETHEREUM_NETWORK=mainnet/g' $ENV_FILE
    sed -i.bak 's/POLYGON_NETWORK=.*/POLYGON_NETWORK=polygon/g' $ENV_FILE
    sed -i.bak 's/OPTIMISM_NETWORK=.*/OPTIMISM_NETWORK=optimism/g' $ENV_FILE
    sed -i.bak 's/ARBITRUM_NETWORK=.*/ARBITRUM_NETWORK=arbitrum/g' $ENV_FILE
    sed -i.bak 's/BASE_NETWORK=.*/BASE_NETWORK=base/g' $ENV_FILE
    sed -i.bak 's/NODE_ENV=.*/NODE_ENV=production/g' $ENV_FILE
fi

# Clean up backup files
rm -f $ENV_FILE.bak

echo -e "${GREEN}Environment variables set up for ${ENV_TYPE}.${NC}"

# Build and start the Docker containers
echo -e "\n${BLUE}Building and starting Docker containers for ${ENV_TYPE}...${NC}"
if [ "$ENV_TYPE" = "development" ]; then
    $DOCKER_COMPOSE -f $COMPOSE_FILE build app-dev db-init
    $DOCKER_COMPOSE -f $COMPOSE_FILE up -d app-dev

    # Initialize the database
    echo -e "\n${BLUE}Initializing database for development...${NC}"
    echo -e "${YELLOW}This will run migrations and seed essential data for the application to function.${NC}"
    $DOCKER_COMPOSE -f $COMPOSE_FILE up db-init

    echo -e "${GREEN}Database initialization complete!${NC}"
    echo -e "${YELLOW}Admin credentials:${NC}"
    echo -e "  Email:    <EMAIL>"
    echo -e "  Password: Admin123!"
elif [ "$ENV_TYPE" = "pre-production" ]; then
    $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.override.yml build
    $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.override.yml up -d

    # Initialize the database
    echo -e "\n${BLUE}Initializing database for pre-production...${NC}"
    echo -e "${YELLOW}This will run migrations and seed essential data for the application to function.${NC}"
    $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.override.yml logs -f db-init
else
    $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.override.yml build
    $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.override.yml up -d

    # Initialize the database
    echo -e "\n${BLUE}Initializing database for production...${NC}"
    echo -e "${YELLOW}This will run migrations and seed essential data for the application to function.${NC}"
    $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.override.yml logs -f db-init
fi

echo -e "\n${GREEN}Docker environment for ${ENV_TYPE} is now set up and running!${NC}"
echo -e "You can access the application at: http://localhost:3000"
echo -e "\n${BLUE}Useful commands:${NC}"
echo -e "  View logs:            $DOCKER_COMPOSE -f ${COMPOSE_FILE} logs -f"
echo -e "  Restart all services: $DOCKER_COMPOSE -f ${COMPOSE_FILE} restart"
echo -e "  Stop all services:    $DOCKER_COMPOSE -f ${COMPOSE_FILE} down"
