#!/usr/bin/env node

/**
 * This script ensures that the uploads directory exists.
 * It creates the directory if it doesn't exist.
 */

const fs = require('fs');
const path = require('path');

// Define the path to the uploads directory
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');

// Check if the directory exists
if (fs.existsSync(uploadsDir)) {
  console.log(`Uploads directory already exists: ${uploadsDir}`);
} else {
  // Create the directory
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log(`Created uploads directory: ${uploadsDir}`);
}
