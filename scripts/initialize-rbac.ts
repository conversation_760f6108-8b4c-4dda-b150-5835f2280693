/**
 * Initialize RBAC System
 * 
 * This script initializes the RBAC system by creating permissions and roles.
 */

import { PrismaClient } from '@prisma/client';
import { initializeRbacSystem } from '../src/lib/rbac/rbac-service';

const prisma = new PrismaClient();

async function main() {
  console.log('Initializing RBAC system...');
  
  try {
    await initializeRbacSystem();
    console.log('RBAC system initialized successfully');
  } catch (error) {
    console.error('Error initializing RBAC system:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
