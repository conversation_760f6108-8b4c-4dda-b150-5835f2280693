#!/bin/bash

# This script runs Prisma Studio against the dockerized PostgreSQL database

echo "Running Prisma Studio against dockerized PostgreSQL..."

# Make sure the database is running
if ! docker compose ps | grep -q "db.*running"; then
  echo "PostgreSQL container is not running. Starting it..."
  ./scripts/start-db.sh
fi

# Run Prisma Studio
echo "Starting Prisma Studio..."
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/carbon_exchange" pnpm exec prisma studio
