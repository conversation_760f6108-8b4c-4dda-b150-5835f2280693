import { db as prisma } from '../src/lib/db';
import { initializeRbacSystem } from '../src/lib/rbac/rbac-service';
import bcrypt from 'bcryptjs';
import { UserRole, OrganizationStatus, VerificationStatus, ProjectType, CarbonCreditStatus } from '@prisma/client';

/**
 * Deployment seed script
 *
 * This script creates the minimum required data for the application to function properly:
 * 1. Initializes the RBAC system (permissions and roles)
 * 2. Creates the platform organization and admin user
 * 3. Creates a sample organization with an admin user
 * 4. Creates sample projects
 * 5. Creates sample carbon credits
 */
async function main() {
  console.log('Starting deployment seed...');

  // Step 1: Initialize RBAC system
  console.log('Initializing RBAC system...');
  await initializeRbacSystem();

  // Step 2: Create platform organization and admin user
  console.log('Creating platform organization and admin user...');

  // Check if admin user already exists
  let adminUser = await prisma.user.findFirst({
    where: { role: "ADMIN" }
  });

  let platformOrg;
  if (!adminUser) {
    // Create platform organization
    platformOrg = await prisma.organization.findFirst({
      where: { name: 'Carbon Exchange Platform' }
    });

    if (!platformOrg) {
      platformOrg = await prisma.organization.create({
        data: {
          name: 'Carbon Exchange Platform',
          description: 'The platform administrator organization',
          website: 'https://carbonexchange.example.com',
          status: OrganizationStatus.ACTIVE,
          verificationStatus: VerificationStatus.VERIFIED,
          country: 'United States',
          industry: 'Technology',
          size: 'MEDIUM',
          foundedYear: 2020,
          primaryContact: 'Admin User',
          primaryContactEmail: '<EMAIL>',
          primaryContactPhone: '+18001234567',
          legalName: 'Carbon Exchange Platform Inc.',
          registrationNumber: 'US87654321',
          taxId: 'US123456789',
        }
      });
      console.log(`Created platform organization: ${platformOrg.name}`);
    } else {
      console.log(`Platform organization already exists: ${platformOrg.name}`);
    }

    // Create admin user
    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: await bcrypt.hash('Admin123!', 10),
        role: UserRole.ADMIN,
        emailVerified: new Date(),
        organization: {
          connect: {
            id: platformOrg.id
          }
        }
      }
    });
    console.log(`Created admin user: ${adminUser.email} (password: Admin123!)`);
  } else {
    console.log(`Admin user already exists: ${adminUser.email}`);
    platformOrg = await prisma.organization.findUnique({
      where: { id: adminUser.organizationId || '' }
    });
  }

  // Step 3: Create a sample organization with admin user
  console.log('Creating sample organization and user...');

  // Check if sample organization exists
  let sampleOrg = await prisma.organization.findFirst({
    where: { name: 'GreenTech Solutions' }
  });

  let orgAdmin;
  if (!sampleOrg) {
    sampleOrg = await prisma.organization.create({
      data: {
        name: 'GreenTech Solutions',
        description: 'Innovative solutions for a sustainable future',
        website: 'https://greentech.example.com',
        status: OrganizationStatus.ACTIVE,
        verificationStatus: VerificationStatus.VERIFIED,
        country: 'United States',
        industry: 'Renewable Energy',
        size: 'MEDIUM',
        foundedYear: 2010,
        primaryContact: 'John Smith',
        primaryContactEmail: '<EMAIL>',
        primaryContactPhone: '+1234567890',
        legalName: 'GreenTech Solutions Inc.',
        registrationNumber: 'US12345678',
        taxId: '98-7654321',
        address: '123 Green Street',
        city: 'San Francisco',
        state: 'CA',
        postalCode: '94105',
      }
    });
    console.log(`Created sample organization: ${sampleOrg.name}`);

    // Create organization admin
    orgAdmin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'John Smith',
        password: await bcrypt.hash('Password123!', 10),
        role: UserRole.ORGANIZATION_ADMIN,
        emailVerified: new Date(),
        jobTitle: 'CEO',
        departmentName: 'Executive',
        phoneNumber: '+1234567890',
        bio: 'Founder and CEO with 15+ years in renewable energy',
        organization: {
          connect: {
            id: sampleOrg.id
          }
        }
      }
    });
    console.log(`Created organization admin: ${orgAdmin.email} (password: Password123!)`);
  } else {
    console.log(`Sample organization already exists: ${sampleOrg.name}`);
    orgAdmin = await prisma.user.findFirst({
      where: {
        organizationId: sampleOrg.id,
        role: UserRole.ORGANIZATION_ADMIN
      }
    });
  }

  // Step 4: Create sample projects
  console.log('Creating sample projects...');

  // Check if sample project exists
  const projectExists = await prisma.project.findFirst({
    where: { name: 'Solar Power Plant - Arizona' }
  });

  let solarProject;
  if (!projectExists && sampleOrg) {
    solarProject = await prisma.project.create({
      data: {
        name: 'Solar Power Plant - Arizona',
        description: 'Large-scale solar power plant in Arizona desert generating clean energy',
        type: ProjectType.RENEWABLE_ENERGY,
        status: "ACTIVE",
        verificationStatus: VerificationStatus.VERIFIED,
        startDate: new Date('2021-03-15'),
        endDate: new Date('2041-03-15'),
        location: 'Arizona Desert',
        country: 'United States',
        coordinates: '33.4484° N, 112.0740° W',
        area: 2500.5,
        externalProjectId: 'SPP-AZ-001',
        registryId: 'VCS-RE-12345',
        standard: 'Verra',
        methodology: 'VM0042',
        methodologyVersion: '1.0',
        estimatedReductions: 75000,
        actualReductions: 68000,
        verifier: 'EcoVerify Inc.',
        validator: 'ClimateCheck Validation',
        sdgs: ['SDG-7', 'SDG-13'],
        tags: ['solar', 'renewable', 'desert'],
        organization: {
          connect: {
            id: sampleOrg.id
          }
        }
      }
    });
    console.log(`Created sample project: ${solarProject.name}`);
  } else if (projectExists) {
    console.log('Sample project already exists');
    solarProject = projectExists;
  } else {
    console.log('Organization not found, cannot create project');
  }

  // Create another project
  const forestProjectExists = await prisma.project.findFirst({
    where: { name: 'Amazon Rainforest Conservation' }
  });

  let forestProject;
  if (!forestProjectExists && sampleOrg) {
    forestProject = await prisma.project.create({
      data: {
        name: 'Amazon Rainforest Conservation',
        description: 'Conservation project to protect the Amazon rainforest from deforestation',
        type: ProjectType.FORESTRY,
        status: "ACTIVE",
        verificationStatus: VerificationStatus.VERIFIED,
        startDate: new Date('2020-01-01'),
        endDate: new Date('2050-01-01'),
        location: 'Amazon Basin',
        country: 'Brazil',
        coordinates: '-3.4653° S, -62.2159° W',
        area: 50000,
        externalProjectId: 'FOR-BR-001',
        registryId: 'VCS-FOR-54321',
        standard: 'Verra',
        methodology: 'VM0015',
        methodologyVersion: '2.1',
        estimatedReductions: 120000,
        actualReductions: 105000,
        verifier: 'Rainforest Alliance',
        validator: 'Climate Standards Board',
        sdgs: ['SDG-13', 'SDG-15'],
        tags: ['forestry', 'conservation', 'biodiversity'],
        organization: {
          connect: {
            id: sampleOrg.id
          }
        }
      }
    });
    console.log(`Created sample project: ${forestProject.name}`);
  } else if (forestProjectExists) {
    console.log('Forest project already exists');
    forestProject = forestProjectExists;
  }

  // Step 5: Create sample carbon credits
  console.log('Creating sample carbon credits...');

  // Only create carbon credits if we have projects and users
  if (solarProject && orgAdmin) {
    const solarCreditExists = await prisma.carbonCredit.findFirst({
      where: {
        name: 'Solar Power Credits - 2023',
        projectId: solarProject.id
      }
    });

    if (!solarCreditExists) {
      const solarCredit = await prisma.carbonCredit.create({
        data: {
          name: 'Solar Power Credits - 2023',
          description: 'Carbon credits generated from the Arizona Solar Power Plant in 2023',
          quantity: 10000,
          availableQuantity: 10000,
          price: 1275.50, // Converted from USD to INR
          minPurchaseQuantity: 10,
          vintage: 2023,
          standard: 'Verra',
          methodology: 'Renewable Energy',
          location: 'Arizona, USA',
          country: 'United States',
          serialNumber: 'VCS-SPP-AZ-2023-001',
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          listingDate: new Date(),
          user: {
            connect: {
              id: orgAdmin.id
            }
          },
          organization: {
            connect: {
              id: sampleOrg.id
            }
          },
          project: {
            connect: {
              id: solarProject.id
            }
          }
        }
      });
      console.log(`Created carbon credit: ${solarCredit.name}`);
    } else {
      console.log('Solar carbon credit already exists');
    }
  }

  if (forestProject && orgAdmin) {
    const forestCreditExists = await prisma.carbonCredit.findFirst({
      where: {
        name: 'Amazon Forest Conservation Credits',
        projectId: forestProject.id
      }
    });

    if (!forestCreditExists) {
      const forestCredit = await prisma.carbonCredit.create({
        data: {
          name: 'Amazon Forest Conservation Credits',
          description: 'Carbon credits from protecting the Amazon rainforest from deforestation',
          quantity: 20000,
          availableQuantity: 20000,
          price: 1546.88, // Converted from USD to INR
          minPurchaseQuantity: 5,
          vintage: 2022,
          standard: 'Verra',
          methodology: 'Forestry and Land Use',
          location: 'Amazon Basin',
          country: 'Brazil',
          serialNumber: 'VCS-FOR-BR-2022-001',
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          listingDate: new Date(),
          user: {
            connect: {
              id: orgAdmin.id
            }
          },
          organization: {
            connect: {
              id: sampleOrg.id
            }
          },
          project: {
            connect: {
              id: forestProject.id
            }
          }
        }
      });
      console.log(`Created carbon credit: ${forestCredit.name}`);
    } else {
      console.log('Forest carbon credit already exists');
    }
  }

  console.log('Deployment seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
