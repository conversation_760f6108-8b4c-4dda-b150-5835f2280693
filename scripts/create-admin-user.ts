import { UserRole } from '@prisma/client';
import { db as prisma } from '../src/lib/db';
import bcrypt from 'bcryptjs';

async function main() {
  console.log('Checking for admin user...');

  // Check if admin user already exists
  let adminUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (adminUser) {
    console.log(`Admin user already exists: ${adminUser.email} (${adminUser.id})`);
    console.log('Resetting password to "Admin123!"');

    // Reset password
    const hashedPassword = await bcrypt.hash('Admin123!', 10);
    adminUser = await prisma.user.update({
      where: { id: adminUser.id },
      data: { password: hashedPassword }
    });

    console.log('Password reset successfully!');
  } else {
    console.log('Creating admin user...');

    // Create admin user
    const hashedPassword = await bcrypt.hash('Admin123!', 10);

    // Check if we have a platform organization
    let platformOrg = await prisma.organization.findFirst({
      where: {
        name: 'Carbon Exchange Platform'
      }
    });

    // Create platform organization if it doesn't exist
    if (!platformOrg) {
      platformOrg = await prisma.organization.create({
        data: {
          name: 'Carbon Exchange Platform',
          description: 'Platform administration organization',
          status: 'ACTIVE',
          verificationStatus: 'VERIFIED'
        }
      });
      console.log(`Created platform organization: ${platformOrg.name} (${platformOrg.id})`);
    }

    // Create admin user with organization
    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: hashedPassword,
        role: UserRole.ADMIN,
        emailVerified: new Date(),
        organization: {
          connect: {
            id: platformOrg.id
          }
        }
      }
    });

    console.log(`Created admin user: ${adminUser.email} (${adminUser.id})`);
  }

  console.log('Admin user password is: Admin123!');
}

main()
  .catch((e) => {
    console.error('Error creating/updating admin user:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
