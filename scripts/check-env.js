#!/usr/bin/env node

/**
 * This script checks that all required environment variables are set
 * before starting the production deployment.
 */

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Required environment variables for production
const requiredEnvVars = [
  // Database
  { name: 'DATABASE_URL', description: 'PostgreSQL connection string' },
  { name: 'DATABASE_HOST', description: 'PostgreSQL host' },
  { name: 'DATABASE_PORT', description: 'PostgreSQL port' },
  { name: 'DATABASE_USER', description: 'PostgreSQL user' },
  { name: 'DATABASE_PASSWORD', description: 'PostgreSQL password' },

  // NextAuth
  { name: 'NEXTAUTH_URL', description: 'URL of the application (e.g., https://example.com)' },
  { name: 'NEXTAUTH_SECRET', description: 'Secret for NextAuth session encryption' },

  // Email
  { name: 'EMAIL_SERVER', description: 'SMTP server for sending emails' },
  { name: 'EMAIL_FROM', description: 'Email address to send emails from' },

  // Alchemy
  { name: 'ALCHEMY_API_KEY', description: 'Alchemy API key for blockchain integration' },
  { name: 'ALCHEMY_NETWORK', description: 'Alchemy network (e.g., eth-mainnet)' },
  { name: 'ALCHEMY_GAS_MANAGER_POLICY_ID', description: 'Alchemy Gas Manager policy ID for sponsored transactions' },

  // Wallet
  { name: 'WALLET_ENCRYPTION_KEY', description: 'Key for encrypting wallet data' },
];

// Optional environment variables with warnings
const optionalEnvVars = [
  // Blockchain networks
  { name: 'ETHEREUM_NETWORK', description: 'Ethereum network (e.g., mainnet)' },
  { name: 'POLYGON_NETWORK', description: 'Polygon network (e.g., polygon)' },
  { name: 'OPTIMISM_NETWORK', description: 'Optimism network (e.g., optimism)' },
  { name: 'ARBITRUM_NETWORK', description: 'Arbitrum network (e.g., arbitrum)' },
  { name: 'BASE_NETWORK', description: 'Base network (e.g., base)' },

  // Analytics
  { name: 'ANALYTICS_ID', description: 'Analytics ID for tracking' },

  // Storage
  { name: 'STORAGE_PROVIDER', description: 'Storage provider (e.g., local, s3)' },
  { name: 'S3_BUCKET', description: 'S3 bucket name for file storage' },
  { name: 'S3_REGION', description: 'S3 region for file storage' },
  { name: 'S3_ACCESS_KEY', description: 'S3 access key for file storage' },
  { name: 'S3_SECRET_KEY', description: 'S3 secret key for file storage' },
];

// Check if we're in production mode
const isProduction = process.env.NODE_ENV === 'production';

// Function to check environment variables
function checkEnvVars() {
  console.log(`${colors.blue}Checking environment variables...${colors.reset}`);

  let missingVars = [];
  let warningVars = [];

  // Check required environment variables
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar.name]) {
      missingVars.push(envVar);
    }
  }

  // Check optional environment variables
  for (const envVar of optionalEnvVars) {
    if (!process.env[envVar.name]) {
      warningVars.push(envVar);
    }
  }

  // Print results
  if (missingVars.length === 0) {
    console.log(`${colors.green}✓ All required environment variables are set.${colors.reset}`);
  } else {
    console.log(`${colors.red}✗ Missing ${missingVars.length} required environment variables:${colors.reset}`);
    for (const envVar of missingVars) {
      console.log(`  ${colors.red}✗ ${envVar.name}${colors.reset} - ${envVar.description}`);
    }
  }

  if (warningVars.length > 0) {
    console.log(`${colors.yellow}⚠ Missing ${warningVars.length} optional environment variables:${colors.reset}`);
    for (const envVar of warningVars) {
      console.log(`  ${colors.yellow}⚠ ${envVar.name}${colors.reset} - ${envVar.description}`);
    }
  }

  // Check for test networks in production
  if (isProduction) {
    const testNetworks = [
      { name: 'ALCHEMY_NETWORK', value: 'eth-sepolia', production: 'eth-mainnet' },
      { name: 'ETHEREUM_NETWORK', value: 'sepolia', production: 'mainnet' },
      { name: 'POLYGON_NETWORK', value: 'mumbai', production: 'polygon' },
      { name: 'OPTIMISM_NETWORK', value: 'optimism-sepolia', production: 'optimism' },
      { name: 'ARBITRUM_NETWORK', value: 'arbitrum-sepolia', production: 'arbitrum' },
      { name: 'BASE_NETWORK', value: 'base-sepolia', production: 'base' },
    ];

    let testNetworkWarnings = [];

    for (const network of testNetworks) {
      if (process.env[network.name] === network.value) {
        testNetworkWarnings.push(network);
      }
    }

    if (testNetworkWarnings.length > 0) {
      console.log(`${colors.magenta}⚠ WARNING: Using test networks in production:${colors.reset}`);
      for (const network of testNetworkWarnings) {
        console.log(`  ${colors.magenta}⚠ ${network.name}=${network.value}${colors.reset} - Should be ${network.production} for production`);
      }
    }
  }

  // Return true if all required variables are set
  return missingVars.length === 0;
}

// Main function
function main() {
  const allVarsSet = checkEnvVars();

  if (!allVarsSet && isProduction) {
    console.log(`${colors.red}✗ Missing required environment variables. Exiting.${colors.reset}`);
    process.exit(1);
  }

  if (allVarsSet) {
    console.log(`${colors.green}✓ Environment check passed.${colors.reset}`);
  }
}

// Run the main function
main();
