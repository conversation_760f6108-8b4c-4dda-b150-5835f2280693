import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from "next-auth/jwt";
import { rateLimit } from "@/lib/rate-limit";
import { addSecurityHeaders } from "@/lib/security-headers";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

// Define public paths that don't require authentication
const publicPaths = [
  '/',
  '/login',
  '/register',
  '/reset-password',
  '/verify-email',
  '/api/auth',
  '/api/register', // Allow public registration
  '/api/webhooks',
  '/api/test-env',
  '/api/me', // Allow /api/me to handle its own authentication
  '/api/health', // Health check endpoint
];

// Define paths that are protected (require authentication)
const protectedPaths = [
  "/dashboard",
  "/marketplace",
  "/wallet",
  "/settings",
  "/admin",
  "/api/carbon-credits",
  "/api/organizations",
  "/api/wallet",
  "/api/admin",
];

// Define paths that are auth paths (login, register, etc.)
const authPaths = [
  "/login",
  "/register",
  "/onboarding",
  "/reset-password",
  "/verify-email"
];

// Define paths that require specific roles
const adminOnlyPaths = [
  "/admin",
  "/api/admin"
];

const orgAdminPaths = [
  "/dashboard/organization/settings",
  "/api/organizations/[id]/members"
];

// Check if the path is public
function isPublicPath(path: string) {
  return publicPaths.some(publicPath =>
    path === publicPath ||
    path.startsWith(`${publicPath}/`) ||
    path.startsWith('/_next/') ||
    path.startsWith('/api/auth/') ||
    path.startsWith('/api/register') ||
    path.startsWith('/api/webhooks/') ||
    path === '/favicon.ico' ||
    path.startsWith('/public/')
  );
}

// Middleware function
export async function middleware(req: NextRequest) {
  // Store IP address and user agent in cookies for audit logging
  const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
  const userAgent = req.headers.get('user-agent') || 'unknown';

  // Set cookies for IP and user agent (httpOnly for security)
  const response = NextResponse.next();
  response.cookies.set('ip', ip, { httpOnly: true, secure: process.env.NODE_ENV === 'production' });
  response.cookies.set('user-agent', userAgent, { httpOnly: true, secure: process.env.NODE_ENV === 'production' });

  // Get the pathname of the request
  const path = req.nextUrl.pathname;

  // Allow public paths without authentication
  if (isPublicPath(path)) {
    // Apply rate limiting to API auth routes in production
    if (path.startsWith('/api/auth') && process.env.NODE_ENV !== 'development') {
      const rateLimitResult = await rateLimit(req, { limit: 50, windowMs: 60 * 1000 }); // 50 requests per minute
      if (rateLimitResult) {
        logger.warn(`Rate limit exceeded for ${path} from ${ip}`);
        return rateLimitResult;
      }
    }
    return addSecurityHeaders(req, response);
  }

  try {
    // Check for JWT token
    const token = await getToken({ req });
    const isAuthenticated = !!token;

    // Check if the path is protected
    const isProtectedPath = protectedPaths.some((protectedPath) =>
      path.startsWith(protectedPath)
    );

    // Check if the path is an auth path
    const isAuthPath = authPaths.some((authPath) => path.startsWith(authPath));

    // Check if the path requires admin role
    const isAdminPath = adminOnlyPaths.some((adminPath) => path.startsWith(adminPath));

    // Check if the path requires organization admin role
    const isOrgAdminPath = orgAdminPaths.some((orgAdminPath) => path.startsWith(orgAdminPath));

    // Apply rate limiting to API routes in production
    if (path.startsWith('/api') && process.env.NODE_ENV !== 'development') {
      const rateLimitResult = await rateLimit(req, { limit: 100, windowMs: 60 * 1000 }); // 100 requests per minute
      if (rateLimitResult) {
        logger.warn(`Rate limit exceeded for ${path} from ${ip}`);
        return rateLimitResult;
      }
    }

    // If the path is protected and the user is not authenticated,
    // redirect to the login page
    if (isProtectedPath && !isAuthenticated) {
      logger.info(`Unauthenticated access attempt to ${path} from ${ip}`);
      const redirectUrl = new URL("/login", req.url);
      redirectUrl.searchParams.set("callbackUrl", path);
      return NextResponse.redirect(redirectUrl);
    }

    // If the user is authenticated and trying to access an auth page,
    // redirect to the dashboard
    if (isAuthPath && isAuthenticated) {
      return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    // Role-based access control for authenticated users
    if (isAuthenticated) {
      // If the path is admin-only and the user is not an admin,
      // redirect to the dashboard
      if (isAdminPath && token?.role !== UserRole.ADMIN) {
        logger.warn(`Unauthorized access attempt to admin path ${path} by user ${token?.email} (${token?.id}) from ${ip}`);
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      // If the path requires organization admin and the user is not an org admin,
      // redirect to the dashboard
      if (isOrgAdminPath && token?.role !== UserRole.ORGANIZATION_ADMIN && token?.role !== UserRole.ADMIN) {
        logger.warn(`Unauthorized access attempt to org admin path ${path} by user ${token?.email} (${token?.id}) from ${ip}`);
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      // Organization-specific access control for API routes
      if (path.match(/\/api\/organizations\/([^\/]+)/) && token?.role !== UserRole.ADMIN) {
        const orgIdMatch = path.match(/\/api\/organizations\/([^\/]+)/);
        if (orgIdMatch && orgIdMatch[1] !== token?.organizationId) {
          logger.warn(`Cross-organization access attempt to ${path} by user ${token?.email} (${token?.id}) from ${ip}`);
          return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
            status: 403,
            headers: { "Content-Type": "application/json" },
          });
        }
      }

      // Add CSRF protection for mutation endpoints (except for public API endpoints)
      const isPublicApiPath = path.startsWith('/api/register') ||
                             path.startsWith('/api/auth') ||
                             path.startsWith('/api/webhooks');

      // Also accept requests with the bypass token for registration
      const hasValidBypassToken = req.headers.get("x-csrf-token") === "bypass-for-registration" &&
                                 path.startsWith('/api/register');

      if ((req.method === "POST" || req.method === "PUT" || req.method === "DELETE") &&
          !req.headers.get("x-csrf-token") &&
          !isPublicApiPath &&
          !hasValidBypassToken) {
        logger.warn(`Missing CSRF token for ${req.method} request to ${path} from ${ip}`);
        return new NextResponse(JSON.stringify({ error: "Invalid CSRF token" }), {
          status: 403,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    // Add security headers to all responses
    return addSecurityHeaders(req, response);
  } catch (error) {
    logger.error(`Middleware error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    // In case of error, allow the request to proceed but log the error
    return NextResponse.next();
  }
}

// Configure the middleware
export const config = {
  matcher: [
    // API routes
    "/api/:path*",

    // Protected routes
    "/dashboard/:path*",
    "/marketplace/:path*",
    "/wallet/:path*",
    "/settings/:path*",
    "/admin/:path*",

    // Auth routes
    "/login",
    "/register",
    "/onboarding/:path*",
    "/reset-password",
    "/verify-email",

    // Exclude static files and API routes that don't need auth
    "/((?!_next/static|_next/image|favicon.ico).*)"
  ],
};
