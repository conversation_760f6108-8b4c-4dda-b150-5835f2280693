#!/usr/bin/env node

// Manually load environment variables from .env file
const fs = require('fs');
const path = require('path');

try {
  const envFile = fs.readFileSync(path.join(__dirname, '.env'), 'utf8');
  const envVars = envFile.split('\n').filter(line => line.includes('='));
  envVars.forEach(line => {
    const [key, ...valueParts] = line.split('=');
    const value = valueParts.join('=').replace(/^"/, '').replace(/"$/, '');
    process.env[key] = value;
  });
} catch (error) {
  console.log('⚠️  Could not load .env file:', error.message);
}

const { PrismaClient } = require('@prisma/client');

async function testDatabaseConnection() {
  const prisma = new PrismaClient();
  
  console.log('🔍 Testing database connection...');
  console.log('📍 Database URL:', process.env.DATABASE_URL);
  
  try {
    // Test basic connection
    console.log('\n1️⃣ Testing basic connection...');
    await prisma.$connect();
    console.log('✅ Successfully connected to database');
    
    // Test query execution
    console.log('\n2️⃣ Testing query execution...');
    const result = await prisma.$queryRaw`SELECT version() as version, now() as current_time`;
    console.log('✅ Query executed successfully');
    console.log('📊 Database version:', result[0].version);
    console.log('🕐 Current time:', result[0].current_time);
    
    // Test if tables exist
    console.log('\n3️⃣ Checking database schema...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    if (tables.length > 0) {
      console.log('✅ Found', tables.length, 'tables in database:');
      tables.slice(0, 10).forEach(table => {
        console.log('  📋', table.table_name);
      });
      if (tables.length > 10) {
        console.log('  ... and', tables.length - 10, 'more tables');
      }
    } else {
      console.log('⚠️  No tables found - database might need migration');
    }
    
    // Test if User table exists (from your schema)
    console.log('\n4️⃣ Testing application tables...');
    try {
      const userCount = await prisma.user.count();
      console.log('✅ User table accessible, found', userCount, 'users');
    } catch (error) {
      console.log('⚠️  User table not accessible:', error.message);
      console.log('💡 You may need to run: npx prisma migrate deploy');
    }
    
    console.log('\n🎉 Database connection test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Database connection failed:');
    console.error('Error:', error.message);
    
    if (error.message.includes('ENOTFOUND')) {
      console.error('💡 Suggestion: Check if the database host is reachable');
    } else if (error.message.includes('authentication failed')) {
      console.error('💡 Suggestion: Check your username and password');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('💡 Suggestion: Check if the database name is correct');
    } else if (error.message.includes('timeout')) {
      console.error('💡 Suggestion: Check if the database port is open and accessible');
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabaseConnection().catch(console.error);
