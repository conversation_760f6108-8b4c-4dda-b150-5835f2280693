import NextAuth, { DefaultSession } from "next-auth"
import { UserRole } from "@/lib/authorization"

declare module "next-auth" {
  /**
   * Returned by `getServerSession`, `auth`, etc.
   */
  interface Session extends DefaultSession {
    user: {
      id: string
      role: UserRole
      organizationId: string | null
    } & DefaultSession["user"]
  }

  interface User {
    id: string
    role: UserRole
    organizationId: string | null
    emailVerified?: Date | null
    twoFactorEnabled?: boolean
    lastLoginAt?: Date | null
  }

  /**
   * For backwards compatibility with older code that directly imports from next-auth
   */
  export function getServerSession(): Promise<Session | null>
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    role: UserRole
    organizationId: string | null
  }
}