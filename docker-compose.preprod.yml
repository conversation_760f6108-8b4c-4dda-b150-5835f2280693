version: '3.8'

services:
  # Pre-production application service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-root}@db:5432/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-root}
      - NEXTAUTH_URL=${NEXTAUTH_URL:-https://preprod.example.com}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-2825c02c6e162bb89551c9fae64e2395cd08d7806bf28e535ededc833beee3f1}
      - EMAIL_SERVER=${EMAIL_SERVER:-smtp.example.com}
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}
      - SMTP_HOST=${SMTP_HOST:-smtp.example.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-<EMAIL>}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-your-smtp-password}
      - SMTP_FROM=${SMTP_FROM:-<EMAIL>}
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY:-your-alchemy-api-key}
      # Use test networks for pre-production
      - ALCHEMY_NETWORK=${ALCHEMY_NETWORK:-eth-sepolia}
      - ETHEREUM_NETWORK=${ETHEREUM_NETWORK:-sepolia}
      - POLYGON_NETWORK=${POLYGON_NETWORK:-mumbai}
      - OPTIMISM_NETWORK=${OPTIMISM_NETWORK:-optimism-sepolia}
      - ARBITRUM_NETWORK=${ARBITRUM_NETWORK:-arbitrum-sepolia}
      - BASE_NETWORK=${BASE_NETWORK:-base-sepolia}
      - NODE_ENV=production
      - WALLET_ENCRYPTION_KEY=${WALLET_ENCRYPTION_KEY:-preprod-wallet-encryption-key}
    volumes:
      - uploads_data:/app/public/uploads
    command: /app/scripts/start.sh
    networks:
      - app_network

  # Database initialization service
  db-init:
    build:
      context: .
      dockerfile: Dockerfile
    restart: "no"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-root}@db:5432/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD:-root}
      - NEXTAUTH_URL=${NEXTAUTH_URL:-https://preprod.example.com}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NODE_ENV=production
    command: /app/scripts/docker-init-db.sh
    networks:
      - app_network

  # Database service
  db:
    image: postgres:16
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-root}
      - POSTGRES_DB=carbon_exchange
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app_network

  # Nginx service for SSL termination and serving static files
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf:/etc/nginx/conf.d
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    depends_on:
      - app
    networks:
      - app_network

  # Certbot service for SSL certificates
  certbot:
    image: certbot/certbot
    restart: unless-stopped
    volumes:
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    networks:
      - app_network

volumes:
  postgres_data:
  uploads_data:

networks:
  app_network:
    driver: bridge
