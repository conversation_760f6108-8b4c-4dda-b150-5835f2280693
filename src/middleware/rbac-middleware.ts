/**
 * RBAC Middleware
 *
 * This middleware initializes the RBAC system and provides permission checking.
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { initializeRbacSystem, hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';

// Initialize RBAC system
let rbacInitialized = false;

/**
 * Initialize the RBAC system if not already initialized
 */
async function ensureRbacInitialized() {
  if (!rbacInitialized) {
    try {
      await initializeRbacSystem();
      rbacInitialized = true;
      logger.info('RBAC system initialized');
    } catch (error) {
      logger.error('Failed to initialize RBAC system:', error);
    }
  }
}

/**
 * RBAC middleware
 */
export async function rbacMiddleware(
  req: NextRequest,
  handler: (req: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  // Initialize RBAC system if needed
  await ensureRbacInitialized();

  // Get the session
  const session = await auth();

  // If no session, proceed to the handler (auth middleware will handle this)
  if (!session?.user) {
    return handler(req);
  }

  // Get the user
  const user = await db.user.findUnique({
    where: { email: session.user.email as string },
    select: { id: true, organizationId: true },
  });

  if (!user) {
    return NextResponse.json(
      { error: 'User not found' },
      { status: 401 }
    );
  }

  // Add user and organization to the request
  req.headers.set('X-User-ID', user.id);
  if (user.organizationId) {
    req.headers.set('X-Organization-ID', user.organizationId);
  }

  // Proceed to the handler
  return handler(req);
}

/**
 * Check if the user has a permission
 */
export async function checkPermissionMiddleware(
  req: NextRequest,
  permissionName: string,
  options: {
    resourceType?: string;
    resourceIdParam?: string;
    teamIdParam?: string;
    logUsage?: boolean;
  } = {}
): Promise<NextResponse | null> {
  // Get user ID from the request
  const userId = req.headers.get('X-User-ID');
  const organizationId = req.headers.get('X-Organization-ID');

  if (!userId) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  // Build permission context
  const context: PermissionContext = {
    userId,
    organizationId: organizationId || undefined,
  };

  // Add resource type and ID if specified
  if (options.resourceType) {
    context.resourceType = options.resourceType;

    // Get resource ID from URL parameter if specified
    if (options.resourceIdParam) {
      const url = new URL(req.url);
      const resourceId = url.pathname.split('/').pop();
      if (resourceId) {
        context.resourceId = resourceId;
      }
    }
  }

  // Add team ID from URL parameter if specified
  if (options.teamIdParam) {
    const url = new URL(req.url);
    const teamId = url.pathname.split('/').find(segment =>
      segment.startsWith('team-')
    );
    if (teamId) {
      context.teamId = teamId.replace('team-', '');
    }
  }

  // Check permission
  const granted = await hasPermission(permissionName, context, {
    logUsage: options.logUsage,
    action: `${req.method} ${req.nextUrl.pathname}`,
    ipAddress: req.headers.get('x-forwarded-for') || req.ip,
    userAgent: req.headers.get('user-agent'),
  });

  if (!granted) {
    return NextResponse.json(
      { error: 'Permission denied', permission: permissionName },
      { status: 403 }
    );
  }

  return null;
}

/**
 * Require a permission for a route
 */
export function requirePermission(
  permissionName: string,
  options: {
    resourceType?: string;
    resourceIdParam?: string;
    teamIdParam?: string;
    logUsage?: boolean;
  } = {}
) {
  return async (req: NextRequest, handler: (req: NextRequest) => Promise<NextResponse>) => {
    // Apply RBAC middleware
    const rbacResponse = await rbacMiddleware(req, async (req) => {
      // Check permission
      const permissionResponse = await checkPermissionMiddleware(
        req,
        permissionName,
        options
      );

      if (permissionResponse) {
        return permissionResponse;
      }

      // Permission granted, proceed to handler
      return handler(req);
    });

    return rbacResponse;
  };
}
