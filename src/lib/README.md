# Carbonix Library

This directory contains the core library modules for the Carbonix platform.

## Module Structure

### Analytics
- **Path**: `@/lib/analytics`
- **Description**: Analytics services for platform, organization, user, and market data.
- **Key Components**:
  - `PlatformAnalytics`: Platform-wide analytics
  - `OrganizationAnalytics`: Organization-specific analytics
  - `UserAnalytics`: User activity analytics
  - `MarketAnalytics`: Market trends and carbon credit analytics

### Blockchain
- **Path**: `@/lib/blockchain`
- **Description**: Blockchain integration services for interacting with various networks.
- **Key Components**:
  - `BlockchainService`: Main service for blockchain interactions
  - `GasService`: Gas estimation and optimization
  - `CarbonCreditContract`: Carbon credit smart contract interface
  - `WalletManager`: Wallet management utilities

### Carbon Credits
- **Path**: `@/lib/carbon-credits`
- **Description**: Carbon credit management services.
- **Key Components**:
  - `CarbonCreditManager`: Main service for carbon credit operations
  - `CarbonCreditVerification`: Verification workflows
  - `CarbonCreditTokenization`: Tokenization workflows

### Notifications
- **Path**: `@/lib/notifications`
- **Description**: Notification services for various channels.
- **Key Components**:
  - `NotificationService`: Main service for sending notifications
  - `InAppNotificationChannel`: In-app notifications
  - `EmailNotificationChannel`: Email notifications
  - `PushNotificationChannel`: Push notifications
  - `SmsNotificationChannel`: SMS notifications
  - `AnnouncementService`: Platform announcements

### Database
- **Path**: `@/lib/db`
- **Description**: Database client and utilities.

### Email
- **Path**: `@/lib/email`
- **Description**: Email sending and template services.

### Authentication
- **Path**: `@/lib/auth`
- **Description**: Authentication services.

### Audit
- **Path**: `@/lib/audit`
- **Description**: Audit logging and reporting services.
- **Key Components**:
  - `AuditManager`: Main service for audit operations
  - `AuditService`: Core audit logging functionality

### Payments
- **Path**: `@/lib/payments`
- **Description**: Payment processing, subscription management, and billing services.
- **Key Components**:
  - `PaymentManager`: Main service for payment operations
  - `StripeProvider`: Stripe payment provider implementation
  - `PaymentService`: Core payment processing functionality

### Orders
- **Path**: `@/lib/orders`
- **Description**: Order management and matching engine for carbon credit trading.
- **Key Components**:
  - `OrderManager`: Main service for order operations
  - `OrderService`: Core order processing functionality
  - `OrderBookService`: Order book and market data functionality

### Marketplace
- **Path**: `@/lib/marketplace`
- **Description**: Marketplace for carbon credit listings and discovery.
- **Key Components**:
  - `MarketplaceManager`: Main service for marketplace operations
  - `MarketplaceService`: Core marketplace functionality

## Usage Examples

### Analytics

```typescript
import { analyticsService } from '@/lib/analytics';

// Get platform statistics
const platformStats = await analyticsService.getPlatformStats();

// Get organization analytics
const orgAnalytics = await analyticsService.getOrganizationAnalytics('org-id', 'month');
```

### Blockchain

```typescript
import { blockchainService } from '@/lib/blockchain';

// Get the carbon credit contract
const carbonCreditContract = blockchainService.getCarbonCreditContract();

// Estimate gas for a transaction
const gasEstimation = await blockchainService.getGasService().estimateTokenizationGas(
  tokenId,
  amount,
  ownerAddress,
  metadata
);
```

### Carbon Credits

```typescript
import { carbonCreditManager } from '@/lib/carbon-credits';

// Create a new carbon credit
const carbonCredit = await carbonCreditManager.createCarbonCredit({
  projectId: 'project-123',
  vintage: 2023,
  standard: 'VCS',
  methodology: 'VM0015',
  description: 'Forest conservation project',
  quantity: 1000,
  price: 15.5,
  organizationId: 'org-id',
  userId: 'user-id',
});

// Verify a carbon credit
const verificationResult = await carbonCreditManager.verifyCarbonCredit(
  'carbon-credit-id',
  'verifier-id',
  {
    projectDocumentUrl: 'https://example.com/project-doc.pdf',
    methodologyDocumentUrl: 'https://example.com/methodology-doc.pdf',
    verificationReportUrl: 'https://example.com/verification-report.pdf',
  }
);
```

### Notifications

```typescript
import { notificationService } from '@/lib/notifications';

// Send a notification to a user
await notificationService.createNotification({
  userId: 'user-id',
  title: 'Carbon Credit Verified',
  message: 'Your carbon credit has been verified and is ready for tokenization.',
  type: 'CARBON_CREDIT',
  priority: 'HIGH',
  actionUrl: '/dashboard/carbon-credits/123',
  actionLabel: 'View Carbon Credit',
  organizationId: 'org-id',
});

// Get user notifications
const notifications = await notificationService.getNotifications('user-id', {
  limit: 10,
  offset: 0,
});
```

### Audit

```typescript
import { auditManager } from '@/lib/audit';

// Create an audit log
await auditManager.createAuditLog({
  type: 'CARBON_CREDIT_CREATED',
  description: 'Carbon credit created',
  userId: 'user-id',
  organizationId: 'org-id',
  metadata: {
    carbonCreditId: 'carbon-credit-id',
    quantity: 1000,
  },
});

// Get audit logs for an organization
const auditLogs = await auditManager.getOrganizationAuditLogs('org-id', {
  limit: 10,
  offset: 0,
});
```

### Payments

```typescript
import { paymentManager } from '@/lib/payments';

// Create a payment method
const paymentMethod = await paymentManager.createPaymentMethod({
  type: 'CREDIT_CARD',
  userId: 'user-id',
  organizationId: 'org-id',
  isDefault: true,
  lastFour: '4242',
  expiryMonth: 12,
  expiryYear: 2025,
  cardBrand: 'visa',
  cardholderName: 'John Doe',
});

// Create a subscription
const subscription = await paymentManager.createSubscription({
  plan: 'BUSINESS',
  userId: 'user-id',
  organizationId: 'org-id',
});

// Get billing records
const billingRecords = await paymentManager.getBillingRecords({
  organizationId: 'org-id',
  limit: 10,
  offset: 0,
});
```

### Orders

```typescript
import { orderManager } from '@/lib/orders';

// Create a buy order
const buyOrder = await orderManager.createOrder({
  type: 'LIMIT',
  side: 'BUY',
  quantity: 100,
  price: 15.5,
  carbonCreditId: 'carbon-credit-id',
  userId: 'user-id',
});

// Get order book
const orderBook = await orderManager.getOrderBook('carbon-credit-id');

// Get market summary
const marketSummary = await orderManager.getMarketSummary('carbon-credit-id');
```

### Marketplace

```typescript
import { marketplaceManager } from '@/lib/marketplace';

// Get marketplace listings
const listings = await marketplaceManager.getListings({
  standard: 'VCS',
  minPrice: 10,
  maxPrice: 20,
  limit: 10,
  offset: 0,
});

// Get marketplace statistics
const statistics = await marketplaceManager.getStatistics();

// Search marketplace with facets
const searchResults = await marketplaceManager.searchListings({
  search: 'forest conservation',
  limit: 10,
  offset: 0,
});
```

## Compatibility Layer

For backward compatibility, we provide a compatibility layer that re-exports the new modules with the old import paths. However, it's recommended to use the new import paths for new code.

The compatibility layer is located in `@/lib/compatibility` and will be removed in a future version.
