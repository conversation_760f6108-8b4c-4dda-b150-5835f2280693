import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format currency based on network and amount
 * @param amount The amount to format
 * @param network The blockchain network (ethereum, polygon, etc.)
 * @param options Additional formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  network?: string,
  options?: {
    maximumFractionDigits?: number;
    minimumFractionDigits?: number;
    currency?: string;
    showSymbol?: boolean;
  }
): string {
  // Default options
  const defaults = {
    maximumFractionDigits: 6,
    minimumFractionDigits: 2,
    showSymbol: true,
  };

  const config = { ...defaults, ...options };

  // Determine currency symbol based on network
  let symbol = options?.currency || "ETH";
  if (network) {
    const networkLower = network.toLowerCase();
    if (networkLower === "polygon" || networkLower === "matic") {
      symbol = "MATIC";
    } else if (networkLower === "arbitrum") {
      symbol = "ARB";
    } else if (networkLower === "optimism") {
      symbol = "OP";
    }
    // Base and Ethereum both use ETH, so no need to change the default
  }

  // Format the number
  const formattedNumber = new Intl.NumberFormat("en-US", {
    minimumFractionDigits: config.minimumFractionDigits,
    maximumFractionDigits: config.maximumFractionDigits,
  }).format(amount);

  // Return with or without symbol
  return config.showSymbol ? `${formattedNumber} ${symbol}` : formattedNumber;
}

/**
 * Truncate an Ethereum address for display
 * @param address The Ethereum address to truncate
 * @param startLength Number of characters to show at the start
 * @param endLength Number of characters to show at the end
 * @returns Truncated address string
 */
export function truncateAddress(address: string, startLength: number = 6, endLength: number = 4): string {
  if (!address) return '';
  if (address.length <= startLength + endLength) return address;
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

/**
 * Show an animated toast notification
 * @param message The message to display in the toast
 * @param type The type of toast (success, error, info, warning)
 * @param duration How long the toast should be displayed (in ms)
 */
export function showAnimatedToast(
  message: string,
  type: 'success' | 'error' | 'info' | 'warning' = 'info',
  duration: number = 5000
) {
  // Import toast dynamically to avoid issues with SSR
  const { toast } = require("sonner");

  switch (type) {
    case 'success':
      toast.success(message, { duration });
      break;
    case 'error':
      toast.error(message, { duration });
      break;
    case 'warning':
      toast.warning(message, { duration });
      break;
    case 'info':
    default:
      toast.info(message, { duration });
      break;
  }
}
