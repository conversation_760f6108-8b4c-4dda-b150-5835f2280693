import * as React from 'react';
import {
  Body,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface WelcomeEmailProps {
  name: string;
  organizationName?: string;
  dashboardUrl: string;
}

export const WelcomeEmail = ({
  name,
  organizationName,
  dashboardUrl,
}: WelcomeEmailProps) => {
  const previewText = `Welcome to Carbonix, ${name}!`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${process.env.NEXT_PUBLIC_APP_URL}/logo.png`}
            width="120"
            height="40"
            alt="Carbonix"
            style={logo}
          />
          <Heading style={heading}>Welcome to Carbonix!</Heading>
          <Text style={paragraph}>Hello {name},</Text>
          <Text style={paragraph}>
            Thank you for joining Carbonix{organizationName ? ` as part of ${organizationName}` : ''}. We're excited to have you on board!
          </Text>
          <Text style={paragraph}>
            Carbonix helps you accelerate sustainability goals with secure, transparent carbon credit trading. Here's what you can do:
          </Text>
          <ul style={list}>
            <li style={listItem}>Buy and sell verified carbon credits</li>
            <li style={listItem}>Track your carbon offset portfolio</li>
            <li style={listItem}>Generate detailed reports and analytics</li>
            <li style={listItem}>Connect with other organizations in the carbon market</li>
          </ul>
          <Section style={buttonContainer}>
            <Button style={button} href={dashboardUrl}>
              Go to Dashboard
            </Button>
          </Section>
          <Text style={paragraph}>
            If you have any questions or need assistance, please don't hesitate to contact our support team at{' '}
            <Link href="mailto:<EMAIL>" style={link}>
              <EMAIL>
            </Link>
            .
          </Text>
          <Hr style={hr} />
          <Text style={footer}>
            © {new Date().getFullYear()} Carbonix. All rights reserved.
            <br />
            123 Green Street, Eco City, EC 12345
          </Text>
          <Text style={footer}>
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/settings/notifications`} style={link}>
              Manage notification preferences
            </Link>
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  maxWidth: '600px',
  borderRadius: '4px',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
};

const logo = {
  margin: '0 auto 20px',
  display: 'block',
};

const heading = {
  fontSize: '24px',
  fontWeight: 'bold',
  marginTop: '30px',
  color: '#333',
  textAlign: 'center' as const,
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '26px',
  color: '#555',
  marginBottom: '16px',
};

const list = {
  marginBottom: '24px',
};

const listItem = {
  fontSize: '16px',
  lineHeight: '26px',
  color: '#555',
  marginBottom: '8px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '30px 0',
};

const button = {
  backgroundColor: '#10b981',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
};

const link = {
  color: '#10b981',
  textDecoration: 'underline',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '30px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '14px',
  lineHeight: '22px',
  textAlign: 'center' as const,
  marginBottom: '10px',
};

export default WelcomeEmail;
