import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface InvitationEmailProps {
  name: string;
  inviterName: string;
  organizationName: string;
  invitationLink: string;
  role: string;
  message?: string;
}

export const InvitationEmail = ({
  name,
  inviterName,
  organizationName,
  invitationLink,
  role,
  message,
}: InvitationEmailProps) => {
  const previewText = `${inviterName} has invited you to join ${organizationName} on Carbonix`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${process.env.NEXT_PUBLIC_APP_URL}/logo.png`}
            width="120"
            height="40"
            alt="Carbonix"
            style={logo}
          />
          <Heading style={heading}>You've been invited to join {organizationName}</Heading>
          <Text style={paragraph}>Hello {name},</Text>
          <Text style={paragraph}>
            <strong>{inviterName}</strong> has invited you to join <strong>{organizationName}</strong> on Carbonix as a <strong>{role}</strong>.
          </Text>

          {message && (
            <Section style={messageContainer}>
              <Text style={messageText}>"{message}"</Text>
            </Section>
          )}

          <Text style={paragraph}>
            Carbonix is a platform for trading carbon credits and managing your organization's carbon offset initiatives.
          </Text>
          <Section style={buttonContainer}>
            <Button style={button} href={invitationLink}>
              Accept Invitation
            </Button>
          </Section>
          <Text style={paragraph}>
            This invitation link will expire in 7 days. If you don't have an account yet, you'll be able to create one when you accept the invitation.
          </Text>
          <Text style={paragraph}>
            If you have any questions, please contact {inviterName} or our support team at{' '}
            <Link href="mailto:<EMAIL>" style={link}>
              <EMAIL>
            </Link>
            .
          </Text>
          <Hr style={hr} />
          <Text style={footer}>
            © {new Date().getFullYear()} Carbonix. All rights reserved.
            <br />
            123 Green Street, Eco City, EC 12345
          </Text>
          <Text style={footer}>
            <Link href={`${process.env.NEXT_PUBLIC_APP_URL}/settings/notifications`} style={link}>
              Manage notification preferences
            </Link>
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  maxWidth: '600px',
  borderRadius: '4px',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
};

const logo = {
  margin: '0 auto 20px',
  display: 'block',
};

const heading = {
  fontSize: '24px',
  fontWeight: 'bold',
  marginTop: '30px',
  color: '#333',
  textAlign: 'center' as const,
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '26px',
  color: '#555',
  marginBottom: '16px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '30px 0',
};

const button = {
  backgroundColor: '#10b981',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
};

const link = {
  color: '#10b981',
  textDecoration: 'underline',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '30px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '14px',
  lineHeight: '22px',
  textAlign: 'center' as const,
  marginBottom: '10px',
};

const messageContainer = {
  backgroundColor: '#f9f9f9',
  borderLeft: '4px solid #10b981',
  padding: '12px 15px',
  margin: '20px 0',
  borderRadius: '4px',
};

const messageText = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#555',
  fontStyle: 'italic',
  margin: '0',
};

export default InvitationEmail;
