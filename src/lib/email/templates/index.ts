import { render } from '@react-email/render';
import { logger } from '@/lib/logger';
import { WelcomeEmail } from './welcome-email';
import { InvitationEmail } from './invitation-email';
import { VerificationEmail } from './verification-email';
import { PasswordResetEmail } from './password-reset-email';
import { TransactionConfirmationEmail } from './transaction-confirmation-email';
import { CarbonCreditVerificationEmail } from './carbon-credit-verification-email';
import { OrganizationVerificationEmail } from './organization-verification-email';
import { BillingInvoiceEmail } from './billing-invoice-email';
import { AnnouncementEmail } from './announcement-email';

/**
 * Email template types
 */
export enum EmailTemplateType {
  WELCOME = 'WELCOME',
  INVITATION = 'INVITATION',
  VERIFICATION = 'VERIFICATION',
  PASSWORD_RESET = 'PASSWORD_RESET',
  TRANSACTION_CONFIRMATION = 'TRANSACTION_CONFIRMATION',
  CARBON_CREDIT_VERIFICATION = 'CARBON_CREDIT_VERIFICATION',
  ORGANIZATION_VERIFICATION = 'ORGANIZATION_VERIFICATION',
  BILLING_INVOICE = 'BILLING_INVOICE',
  ANNOUNCEMENT = 'ANNOUNCEMENT',
}

/**
 * Get email template based on type
 * @param type Email template type
 * @param data Template data
 * @returns HTML string
 */
export function getEmailTemplate(type: EmailTemplateType, data: any): string {
  try {
    switch (type) {
      case EmailTemplateType.WELCOME:
        return render(WelcomeEmail(data));
      
      case EmailTemplateType.INVITATION:
        return render(InvitationEmail(data));
      
      case EmailTemplateType.VERIFICATION:
        return render(VerificationEmail(data));
      
      case EmailTemplateType.PASSWORD_RESET:
        return render(PasswordResetEmail(data));
      
      case EmailTemplateType.TRANSACTION_CONFIRMATION:
        return render(TransactionConfirmationEmail(data));
      
      case EmailTemplateType.CARBON_CREDIT_VERIFICATION:
        return render(CarbonCreditVerificationEmail(data));
      
      case EmailTemplateType.ORGANIZATION_VERIFICATION:
        return render(OrganizationVerificationEmail(data));
      
      case EmailTemplateType.BILLING_INVOICE:
        return render(BillingInvoiceEmail(data));
      
      case EmailTemplateType.ANNOUNCEMENT:
        return render(AnnouncementEmail(data));
      
      default:
        throw new Error(`Unknown email template type: ${type}`);
    }
  } catch (error) {
    logger.error(`Error rendering email template ${type}:`, error);
    throw error;
  }
}

/**
 * Get email subject based on template type
 * @param type Email template type
 * @param data Template data
 * @returns Email subject
 */
export function getEmailSubject(type: EmailTemplateType, data: any): string {
  switch (type) {
    case EmailTemplateType.WELCOME:
      return `Welcome to Carbonix, ${data.name}!`;
    
    case EmailTemplateType.INVITATION:
      return `${data.inviterName} invited you to join ${data.organizationName} on Carbonix`;
    
    case EmailTemplateType.VERIFICATION:
      return 'Verify your email address for Carbonix';
    
    case EmailTemplateType.PASSWORD_RESET:
      return 'Reset your Carbonix password';
    
    case EmailTemplateType.TRANSACTION_CONFIRMATION:
      return `Transaction Confirmation: ${data.transactionType} - Carbonix`;
    
    case EmailTemplateType.CARBON_CREDIT_VERIFICATION:
      return `Carbon Credit Verification ${data.status}: ${data.carbonCreditName}`;
    
    case EmailTemplateType.ORGANIZATION_VERIFICATION:
      return `Organization Verification ${data.status}: ${data.organizationName}`;
    
    case EmailTemplateType.BILLING_INVOICE:
      return `Invoice #${data.invoiceNumber} - Carbonix`;
    
    case EmailTemplateType.ANNOUNCEMENT:
      return `${data.title} - Carbonix Announcement`;
    
    default:
      return 'Carbonix Notification';
  }
}
