import { render } from '@react-email/render';
import nodemailer from 'nodemailer';
import { logger } from '@/lib/logger';
import { InvitationEmail } from './templates/invitation-email';

interface SendInvitationEmailParams {
  email: string;
  name: string;
  inviterName: string;
  organizationName: string;
  invitationLink: string;
  role: string;
  message?: string;
}

/**
 * Send invitation email to a user
 * @param params Email parameters
 */
export async function sendInvitationEmail(params: SendInvitationEmailParams) {
  const {
    email,
    name,
    inviterName,
    organizationName,
    invitationLink,
    role,
    message,
  } = params;

  try {
    // Render email template
    const html = render(
      InvitationEmail({
        name,
        inviterName,
        organizationName,
        invitationLink,
        role,
        message,
      })
    );

    // Create email subject
    const subject = `${inviterName} invited you to join ${organizationName} on Carbonix`;

    // Get email transporter
    const transporter = getEmailTransporter();

    // Send email
    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject,
      html,
    });

    logger.info(`Invitation email sent to ${email} for organization ${organizationName}`);
  } catch (error) {
    logger.error(`Error sending invitation email to ${email}:`, error);
    throw error;
  }
}

/**
 * Get email transporter
 * @returns Nodemailer transporter
 */
function getEmailTransporter() {
  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment) {
    // In development, log emails instead of sending them
    return {
      sendMail: async (options: any) => {
        logger.info('Email would be sent in production:');
        logger.info(`To: ${options.to}`);
        logger.info(`Subject: ${options.subject}`);
        logger.info(`HTML: ${options.html.substring(0, 100)}...`);
        return { messageId: 'dev-mode-' + Date.now() };
      },
    };
  }

  // In production, use real email service
  return nodemailer.createTransport({
    host: process.env.EMAIL_SERVER_HOST,
    port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
    secure: process.env.EMAIL_SERVER_SECURE === 'true',
  });
}
