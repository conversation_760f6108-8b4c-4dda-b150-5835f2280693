import nodemailer from 'nodemailer';
import { logger } from '../logger';

class EmailService {
  private transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
  }

  async sendEmail({ to, subject, html }) {
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to,
      subject,
      html,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      logger.info(`Email sent to ${to}`);
    } catch (error) {
      logger.error(`Error sending email to ${to}:`, error);
    }
  }

  /**
   * Send verification email
   * @param email User's email
   * @param name User's name
   * @param token Verification token
   * @param isNewRegistration Whether this is a new registration (affects redirection)
   */
  async sendVerificationEmail(email: string, name: string, token: string, isNewRegistration: boolean = false) {
    try {
      const verificationLink = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${token}&onboard=${isNewRegistration ? 'true' : 'false'}`;

      await this.sendEmail({
        to: email,
        subject: 'Verify your email address',
        html: `
          <h1>Verify your email address</h1>
          <p>Hello ${name},</p>
          <p>Thank you for registering with CarbonX. Please verify your email address by clicking the link below:</p>
          <p><a href="${verificationLink}">Verify Email Address</a></p>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not create an account, please ignore this email.</p>
          <p>Best regards,<br>The CarbonX Team</p>
        `,
      });
      logger.info(`Verification email sent to ${email}`);
    } catch (error) {
      logger.error(`Error sending verification email to ${email}:`, error);
    }
  }
}

export default new EmailService();
