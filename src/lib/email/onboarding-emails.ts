
import { logger } from '@/lib/logger';
import emailService from './email-service';

/**
 * Service for sending onboarding-related emails
 */
export const onboardingEmailService = {
  /**
   * Send welcome email to a new organization
   * @param email User's email
   * @param name User's name
   * @param organizationName Organization name
   */
  async sendWelcomeEmail(email: string, name: string, organizationName: string) {
    try {
      await emailService.sendEmail({
        to: email,
        subject: `Welcome to CarbonX, ${name}!`,
        html: `
          <h1>Welcome to CarbonX!</h1>
          <p>Hello ${name},</p>
          <p>Thank you for creating your organization <strong>${organizationName}</strong> on our platform.</p>
          <p>You're now ready to start your carbon trading journey. Here's what you can do next:</p>
          <ul>
            <li>Complete your organization profile</li>
            <li>Invite team members to join your organization</li>
            <li>Set up your wallet to start trading</li>
            <li>Submit verification documents</li>
          </ul>
          <p>If you have any questions, please don't hesitate to contact our support team.</p>
          <p>Best regards,<br>The CarbonX Team</p>
        `,
      });
      logger.info(`Welcome email sent to ${email}`);
    } catch (error) {
      logger.error(`Error sending welcome email to ${email}:`, error);
    }
  },

  /**
   * Send organization verification submitted email
   * @param email User's email
   * @param name User's name
   * @param organizationName Organization name
   */
  async sendVerificationSubmittedEmail(email: string, name: string, organizationName: string) {
    try {
      await emailService.sendEmail({
        to: email,
        subject: `Verification Documents Received - ${organizationName}`,
        html: `
          <h1>Verification Documents Received</h1>
          <p>Hello ${name},</p>
          <p>We have received the verification documents for <strong>${organizationName}</strong>.</p>
          <p>Our team will review your documents and update your organization's verification status within 1-3 business days.</p>
          <p>You will receive a notification once the verification process is complete.</p>
          <p>In the meantime, you can continue setting up your organization and exploring our platform.</p>
          <p>Best regards,<br>The CarbonX Team</p>
        `,
      });
      logger.info(`Verification submitted email sent to ${email}`);
    } catch (error) {
      logger.error(`Error sending verification submitted email to ${email}:`, error);
    }
  },

  /**
   * Send wallet creation confirmation email
   * @param email User's email
   * @param name User's name
   * @param organizationName Organization name
   * @param walletAddress Wallet address
   * @param network Blockchain network
   * @param isTestnet Whether the wallet is on a testnet
   */
  async sendWalletCreatedEmail(
    email: string,
    name: string,
    organizationName: string,
    walletAddress: string,
    network: string,
    isTestnet: boolean
  ) {
    try {
      await emailService.sendEmail({
        to: email,
        subject: `Wallet Created for ${organizationName}`,
        html: `
          <h1>Wallet Created Successfully</h1>
          <p>Hello ${name},</p>
          <p>Your organization wallet for <strong>${organizationName}</strong> has been created successfully.</p>
          <p><strong>Wallet Address:</strong> ${walletAddress}</p>
          <p><strong>Network:</strong> ${network} ${isTestnet ? '(Testnet)' : '(Mainnet)'}</p>
          <p>You can now use this wallet to trade carbon credits on our platform.</p>
          <p>Remember to keep your wallet credentials secure and never share them with anyone.</p>
          <p>Best regards,<br>The CarbonX Team</p>
        `,
      });
      logger.info(`Wallet created email sent to ${email}`);
    } catch (error) {
      logger.error(`Error sending wallet created email to ${email}:`, error);
    }
  },

  /**
   * Send subscription confirmation email
   * @param email User's email
   * @param name User's name
   * @param organizationName Organization name
   * @param plan Subscription plan
   */
  async sendSubscriptionConfirmationEmail(
    email: string,
    name: string,
    organizationName: string,
    plan: string
  ) {
    try {
      await emailService.sendEmail({
        to: email,
        subject: `Subscription Confirmed - ${organizationName}`,
        html: `
          <h1>Subscription Confirmed</h1>
          <p>Hello ${name},</p>
          <p>Your <strong>${plan}</strong> subscription for <strong>${organizationName}</strong> has been confirmed.</p>
          <p>You now have access to all the features included in the ${plan} plan.</p>
          <p>Thank you for choosing our platform for your carbon trading needs.</p>
          <p>Best regards,<br>The CarbonX Team</p>
        `,
      });
      logger.info(`Subscription confirmation email sent to ${email}`);
    } catch (error) {
      logger.error(`Error sending subscription confirmation email to ${email}:`, error);
    }
  },
};
