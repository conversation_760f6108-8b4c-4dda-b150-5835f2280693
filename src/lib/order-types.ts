/**
 * Order status enum
 */
export enum OrderStatus {
  PENDING = "PENDING",
  MATCHED = "MATCHED",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
  FAILED = "FAILED",
}

/**
 * Order type enum
 */
export enum OrderType {
  BUY = "BUY",
  SELL = "SELL",
}

/**
 * Transaction status enum
 */
export enum TransactionStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
}

/**
 * Transaction type enum
 */
export enum TransactionType {
  PURCHASE = "PURCHASE",
  SALE = "SALE",
  TRANSFER = "TRANSFER",
  RETIREMENT = "RETIREMENT",
  TOKENIZATION = "TOKENIZATION",
  PLATFORM_FEE = "PLATFORM_FEE",
}

/**
 * Tokenization status enum
 */
export enum TokenizationStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

/**
 * Order match interface
 */
export interface OrderMatch {
  buyOrderId: string;
  sellOrderId: string;
  buyerId: string;
  sellerId: string;
  buyerOrgId: string;
  sellerOrgId: string;
  carbonCreditId: string;
  carbonCreditName: string;
  quantity: number;
  price: number;
  totalValue: number;
  buyerFee: number;
  sellerFee: number;
  timestamp: Date;
}

/**
 * Order book entry interface
 */
export interface OrderBookEntry {
  price: number;
  quantity: number;
  orders: number;
}

/**
 * Order book interface
 */
export interface OrderBook {
  buyOrders: OrderBookEntry[];
  sellOrders: OrderBookEntry[];
  lastPrice: number | null;
  spread: number | null;
  timestamp: Date;
}

/**
 * Calculate the fee for an order
 * @param price Price per ton
 * @param quantity Quantity in tons
 * @param feeRate Fee rate (default: 0.01 = 1%)
 * @returns Fee amount
 */
export function calculateOrderFee(
  price: number,
  quantity: number,
  feeRate: number = 0.01
): number {
  return price * quantity * feeRate;
}

/**
 * Format order status for display
 * @param status Order status
 * @returns Formatted status
 */
export function formatOrderStatus(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.PENDING:
      return "Pending";
    case OrderStatus.MATCHED:
      return "Matched";
    case OrderStatus.COMPLETED:
      return "Completed";
    case OrderStatus.CANCELLED:
      return "Cancelled";
    case OrderStatus.EXPIRED:
      return "Expired";
    case OrderStatus.FAILED:
      return "Failed";
    default:
      return status;
  }
}

/**
 * Format transaction status for display
 * @param status Transaction status
 * @returns Formatted status
 */
export function formatTransactionStatus(status: TransactionStatus): string {
  switch (status) {
    case TransactionStatus.PENDING:
      return "Pending";
    case TransactionStatus.PROCESSING:
      return "Processing";
    case TransactionStatus.COMPLETED:
      return "Completed";
    case TransactionStatus.FAILED:
      return "Failed";
    case TransactionStatus.CANCELLED:
      return "Cancelled";
    default:
      return status;
  }
}
