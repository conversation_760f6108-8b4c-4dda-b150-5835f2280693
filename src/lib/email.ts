import nodemailer from "nodemailer";
import { logger } from "@/lib/logger";

/**
 * Email options
 */
export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  replyTo?: string;
  attachments?: any[];
}

/**
 * Email service for sending emails
 */
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    // Create reusable transporter object using SMTP transport
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp.example.com",
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: process.env.SMTP_SECURE === "true",
      auth: {
        user: process.env.SMTP_USER || "<EMAIL>",
        pass: process.env.SMTP_PASSWORD || "password",
      },
    });
  }

  /**
   * Send an email
   * @param options Email options
   * @returns Nodemailer info object
   */
  async sendEmail(options: EmailOptions) {
    try {
      const { to, subject, text, html, from, replyTo, attachments } = options;

      // Set default from address
      const fromAddress = from || process.env.EMAIL_FROM || "Carbonix <<EMAIL>>";

      // In development, just log the email
      if (process.env.NODE_ENV === "development") {
        logger.info(`[DEV] Email to: ${to}, Subject: ${subject}`);
        logger.info(`[DEV] Email content: ${text || html}`);
        return { messageId: "dev-mode" };
      }

      // Send mail with defined transport object
      const info = await this.transporter.sendMail({
        from: fromAddress,
        to,
        subject,
        text,
        html,
        replyTo,
        attachments,
      });

      logger.info(`Email sent to ${to}: ${subject} (${info.messageId})`);

      return info;
    } catch (error) {
      logger.error(`Error sending email to ${options.to}:`, error);
      throw new Error("Failed to send email");
    }
  }

  /**
   * Send a password reset email
   * @param to Recipient email
   * @param name Recipient name
   * @param token Reset token
   * @returns Nodemailer info object
   */
  async sendPasswordResetEmail(to: string, name: string = "User", token: string) {
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL}/reset-password?token=${token}`;
    const subject = "Reset your password";
    const html = `
      <h1>Password Reset</h1>
      <p>Hello ${name},</p>
      <p>We received a request to reset your password. Click the button below to create a new password:</p>
      <p>
        <a href="${resetUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          Reset Password
        </a>
      </p>
      <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
      <p>${resetUrl}</p>
      <p>This link will expire in 1 hour.</p>
      <p>If you didn't request a password reset, you can safely ignore this email.</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }

  /**
   * Send a verification email
   * @param to Recipient email
   * @param name Recipient name
   * @param token Verification token
   * @returns Nodemailer info object
   */
  async sendVerificationEmail(to: string, name: string = "User", token: string) {
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL}/verify-email?token=${token}`;
    const subject = "Verify your email address";
    const html = `
      <h1>Email Verification</h1>
      <p>Hello ${name},</p>
      <p>Please verify your email address by clicking the button below:</p>
      <p>
        <a href="${verificationUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          Verify Email
        </a>
      </p>
      <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
      <p>${verificationUrl}</p>
      <p>This link will expire in 24 hours.</p>
      <p>If you didn't create an account, you can safely ignore this email.</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }

  /**
   * Send a notification email
   * @param to Recipient email
   * @param name Recipient name
   * @param subject Email subject
   * @param message Email message
   * @returns Nodemailer info object
   */
  async sendNotificationEmail(to: string, name: string = "User", subject: string, message: string) {
    const html = `
      <h1>${subject}</h1>
      <p>Hello ${name},</p>
      <p>${message}</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }

  /**
   * Send an invitation email
   * @param to Recipient email
   * @param organizationName Organization name
   * @param inviterName Inviter name
   * @param token Invitation token
   * @returns Nodemailer info object
   */
  async sendInvitationEmail(
    to: string,
    organizationName: string,
    inviterName: string,
    token: string
  ) {
    const invitationUrl = `${process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL}/invitation?token=${token}`;
    const subject = `Invitation to join ${organizationName} on Carbonix`;
    const html = `
      <h1>You've been invited to join ${organizationName}</h1>
      <p>Hello,</p>
      <p>${inviterName} has invited you to join ${organizationName} on Carbonix. Accelerate your sustainability goals with our secure, transparent carbon credit trading.</p>
      <p>Click the button below to accept the invitation:</p>
      <p>
        <a href="${invitationUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          Accept Invitation
        </a>
      </p>
      <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
      <p>${invitationUrl}</p>
      <p>This invitation will expire in 7 days.</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }

  /**
   * Send a welcome email
   * @param to Recipient email
   * @param name Recipient name
   * @returns Nodemailer info object
   */
  async sendWelcomeEmail(to: string, name: string = "User") {
    const subject = "Welcome to Carbonix";
    const html = `
      <h1>Welcome to Carbonix!</h1>
      <p>Hello ${name},</p>
      <p>Thank you for joining Carbonix. Accelerate your sustainability goals with our secure, transparent carbon credit trading.</p>
      <p>With our platform, you can:</p>
      <ul>
        <li>List your carbon credits</li>
        <li>Trade with other enterprises</li>
        <li>Track your carbon footprint</li>
        <li>Generate detailed reports</li>
      </ul>
      <p>If you have any questions, please don't hesitate to contact our support team.</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }

  /**
   * Send a transaction confirmation email
   * @param to Recipient email
   * @param name Recipient name
   * @param transactionDetails Transaction details
   * @returns Nodemailer info object
   */
  async sendTransactionConfirmationEmail(
    to: string,
    name: string,
    transactionDetails: any
  ) {
    const subject = "Transaction Confirmation";
    const html = `
      <h1>Transaction Confirmation</h1>
      <p>Hello ${name},</p>
      <p>Your transaction has been confirmed:</p>
      <table style="border-collapse: collapse; width: 100%;">
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Transaction ID</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${transactionDetails.id}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Type</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${transactionDetails.type}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Amount</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${transactionDetails.amount}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Fee</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${transactionDetails.fee}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Status</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${transactionDetails.status}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Date</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${new Date(
            transactionDetails.createdAt
          ).toLocaleString()}</td>
        </tr>
      </table>
      <p>You can view more details in your dashboard.</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }

  /**
   * Send a carbon credit listing confirmation email
   * @param to Recipient email
   * @param name Recipient name
   * @param creditDetails Carbon credit details
   * @returns Nodemailer info object
   */
  async sendCarbonCreditListingEmail(
    to: string,
    name: string,
    creditDetails: any
  ) {
    const subject = "Carbon Credit Listed";
    const html = `
      <h1>Carbon Credit Listed</h1>
      <p>Hello ${name},</p>
      <p>Your carbon credit has been successfully listed on the marketplace:</p>
      <table style="border-collapse: collapse; width: 100%;">
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Credit ID</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${creditDetails.id}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Name</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${creditDetails.name}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Quantity</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${creditDetails.quantity}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Price</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${creditDetails.price}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Standard</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${creditDetails.standard}</td>
        </tr>
        <tr>
          <td style="border: 1px solid #dddddd; padding: 8px;"><strong>Methodology</strong></td>
          <td style="border: 1px solid #dddddd; padding: 8px;">${creditDetails.methodology}</td>
        </tr>
      </table>
      <p>You can view more details in your dashboard.</p>
      <p>Best regards,<br>The Carbonix Team</p>
    `;

    return this.sendEmail({ to, subject, html });
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Export individual methods for direct imports
export const sendInvitationEmail = emailService.sendInvitationEmail.bind(emailService);
