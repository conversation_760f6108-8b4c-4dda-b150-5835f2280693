import { AuditLogType } from '@prisma/client';
import { auditManager } from '@/lib/audit';
import { logger } from '@/lib/logger';
import { 
  ComplianceStatus, 
  KycLevel, 
  ComplianceRiskLevel, 
  ComplianceCheckType,
  ComplianceCheckResult,
  ComplianceDocumentType
} from '@/lib/compliance/types';

/**
 * Compliance Audit Service
 * Enhanced audit logging for compliance-related activities
 */
export class ComplianceAuditService {
  /**
   * Log KYC verification request
   * @param params Parameters
   */
  static async logKycVerificationRequest(params: {
    userId: string;
    organizationId: string;
    level: KycLevel;
    documentIds: string[];
    documentTypes: ComplianceDocumentType[];
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { userId, organizationId, level, documentIds, documentTypes, ipAddress, userAgent } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.KYC_VERIFICATION_REQUESTED,
        description: `KYC verification requested for level ${level}`,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          level,
          documentCount: documentIds.length,
          documentIds,
          documentTypes,
          timestamp: new Date().toISOString(),
          requestType: 'Initial Request',
        },
      });
      
      logger.info(`Compliance audit: KYC verification requested for user ${userId}, organization ${organizationId}, level ${level}`);
    } catch (error) {
      logger.error('Error logging KYC verification request:', error);
    }
  }

  /**
   * Log KYC verification status update
   * @param params Parameters
   */
  static async logKycVerificationStatusUpdate(params: {
    userId: string;
    organizationId: string;
    kycVerificationId: string;
    previousStatus: ComplianceStatus;
    newStatus: ComplianceStatus;
    level: KycLevel;
    reviewerId?: string;
    notes?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { 
        userId, 
        organizationId, 
        kycVerificationId, 
        previousStatus, 
        newStatus, 
        level, 
        reviewerId, 
        notes,
        ipAddress,
        userAgent
      } = params;
      
      const auditType = newStatus === ComplianceStatus.APPROVED 
        ? AuditLogType.KYC_VERIFICATION_APPROVED 
        : newStatus === ComplianceStatus.REJECTED 
          ? AuditLogType.KYC_VERIFICATION_REJECTED 
          : AuditLogType.KYC_VERIFICATION_REQUESTED;
      
      await auditManager.createAuditLog({
        type: auditType,
        description: `KYC verification status changed from ${previousStatus} to ${newStatus}`,
        userId: reviewerId || userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          kycVerificationId,
          userId,
          level,
          previousStatus,
          newStatus,
          reviewerId,
          notes,
          timestamp: new Date().toISOString(),
        },
      });
      
      logger.info(`Compliance audit: KYC verification status updated for user ${userId}, organization ${organizationId}, from ${previousStatus} to ${newStatus}`);
    } catch (error) {
      logger.error('Error logging KYC verification status update:', error);
    }
  }

  /**
   * Log KYC document status update
   * @param params Parameters
   */
  static async logKycDocumentStatusUpdate(params: {
    userId: string;
    organizationId: string;
    documentId: string;
    documentType: ComplianceDocumentType;
    previousStatus: ComplianceStatus;
    newStatus: ComplianceStatus;
    reviewerId?: string;
    notes?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { 
        userId, 
        organizationId, 
        documentId, 
        documentType, 
        previousStatus, 
        newStatus, 
        reviewerId, 
        notes,
        ipAddress,
        userAgent
      } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.DOCUMENT_VERIFIED,
        description: `KYC document (${documentType}) status changed from ${previousStatus} to ${newStatus}`,
        userId: reviewerId || userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          documentId,
          documentType,
          userId,
          previousStatus,
          newStatus,
          reviewerId,
          notes,
          timestamp: new Date().toISOString(),
        },
      });
      
      logger.info(`Compliance audit: KYC document status updated for user ${userId}, document ${documentId}, from ${previousStatus} to ${newStatus}`);
    } catch (error) {
      logger.error('Error logging KYC document status update:', error);
    }
  }

  /**
   * Log AML check
   * @param params Parameters
   */
  static async logAmlCheck(params: {
    userId?: string;
    organizationId?: string;
    walletAddress?: string;
    transactionHash?: string;
    amount?: number;
    checkId: string;
    riskLevel: ComplianceRiskLevel;
    riskScore: number;
    result: ComplianceCheckResult;
    details: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { 
        userId, 
        organizationId, 
        walletAddress, 
        transactionHash, 
        amount, 
        checkId, 
        riskLevel, 
        riskScore, 
        result, 
        details,
        ipAddress,
        userAgent
      } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.AML_CHECK_PERFORMED,
        description: `AML check performed with risk level ${riskLevel}, result ${result}`,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          checkId,
          riskLevel,
          riskScore,
          result,
          walletAddress,
          transactionHash,
          amount,
          details,
          timestamp: new Date().toISOString(),
        },
      });
      
      logger.info(`Compliance audit: AML check performed for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''}${walletAddress ? `wallet ${walletAddress}` : ''}${transactionHash ? `transaction ${transactionHash}` : ''}, risk level ${riskLevel}, result ${result}`);
    } catch (error) {
      logger.error('Error logging AML check:', error);
    }
  }

  /**
   * Log AML status update
   * @param params Parameters
   */
  static async logAmlStatusUpdate(params: {
    userId?: string;
    organizationId?: string;
    amlCheckId: string;
    previousStatus: ComplianceStatus;
    newStatus: ComplianceStatus;
    previousRiskLevel?: ComplianceRiskLevel;
    newRiskLevel: ComplianceRiskLevel;
    reviewerId?: string;
    notes?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { 
        userId, 
        organizationId, 
        amlCheckId, 
        previousStatus, 
        newStatus, 
        previousRiskLevel, 
        newRiskLevel, 
        reviewerId, 
        notes,
        ipAddress,
        userAgent
      } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.AML_CHECK_PERFORMED,
        description: `AML status changed from ${previousStatus} to ${newStatus}, risk level from ${previousRiskLevel || 'unknown'} to ${newRiskLevel}`,
        userId: reviewerId || userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          amlCheckId,
          userId,
          organizationId,
          previousStatus,
          newStatus,
          previousRiskLevel,
          newRiskLevel,
          reviewerId,
          notes,
          timestamp: new Date().toISOString(),
          isStatusUpdate: true,
        },
      });
      
      logger.info(`Compliance audit: AML status updated for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''}, from ${previousStatus} to ${newStatus}`);
    } catch (error) {
      logger.error('Error logging AML status update:', error);
    }
  }

  /**
   * Log compliance check
   * @param params Parameters
   */
  static async logComplianceCheck(params: {
    userId?: string;
    organizationId?: string;
    checkId: string;
    checkType: ComplianceCheckType;
    result: ComplianceCheckResult;
    riskLevel: ComplianceRiskLevel;
    details?: Record<string, any>;
    walletAddress?: string;
    transactionHash?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { 
        userId, 
        organizationId, 
        checkId, 
        checkType, 
        result, 
        riskLevel, 
        details, 
        walletAddress, 
        transactionHash,
        ipAddress,
        userAgent
      } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.AML_CHECK_PERFORMED,
        description: `Compliance check (${checkType}) performed with result ${result}, risk level ${riskLevel}`,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          checkId,
          checkType,
          result,
          riskLevel,
          walletAddress,
          transactionHash,
          details,
          timestamp: new Date().toISOString(),
        },
      });
      
      logger.info(`Compliance audit: Compliance check (${checkType}) performed for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''}, result ${result}, risk level ${riskLevel}`);
    } catch (error) {
      logger.error('Error logging compliance check:', error);
    }
  }

  /**
   * Log tax report generation
   * @param params Parameters
   */
  static async logTaxReportGeneration(params: {
    userId?: string;
    organizationId?: string;
    reportId: string;
    year: number;
    quarter?: number;
    format: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { userId, organizationId, reportId, year, quarter, format, ipAddress, userAgent } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.TAX_REPORT_GENERATED,
        description: `Tax report generated for ${year}${quarter ? `, Q${quarter}` : ''} in ${format.toUpperCase()} format`,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          reportId,
          year,
          quarter,
          format,
          timestamp: new Date().toISOString(),
        },
      });
      
      logger.info(`Compliance audit: Tax report generated for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''}, year ${year}${quarter ? `, quarter ${quarter}` : ''}, format ${format}`);
    } catch (error) {
      logger.error('Error logging tax report generation:', error);
    }
  }

  /**
   * Log tax report download
   * @param params Parameters
   */
  static async logTaxReportDownload(params: {
    userId: string;
    organizationId?: string;
    reportId: string;
    year: number;
    quarter?: number;
    format: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const { userId, organizationId, reportId, year, quarter, format, ipAddress, userAgent } = params;
      
      await auditManager.createAuditLog({
        type: AuditLogType.TAX_REPORT_GENERATED,
        description: `Tax report downloaded for ${year}${quarter ? `, Q${quarter}` : ''} in ${format.toUpperCase()} format`,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        metadata: {
          reportId,
          year,
          quarter,
          format,
          timestamp: new Date().toISOString(),
          isDownload: true,
        },
      });
      
      logger.info(`Compliance audit: Tax report downloaded by user ${userId}, report ${reportId}`);
    } catch (error) {
      logger.error('Error logging tax report download:', error);
    }
  }

  /**
   * Get compliance audit logs
   * @param params Parameters
   * @returns Audit logs
   */
  static async getComplianceAuditLogs(params: {
    userId?: string;
    organizationId?: string;
    startDate?: Date;
    endDate?: Date;
    types?: AuditLogType[];
    limit?: number;
    offset?: number;
  }) {
    try {
      const { userId, organizationId, startDate, endDate, types, limit = 50, offset = 0 } = params;
      
      // Define compliance-related audit log types
      const complianceTypes = types || [
        AuditLogType.KYC_VERIFICATION_REQUESTED,
        AuditLogType.KYC_VERIFICATION_APPROVED,
        AuditLogType.KYC_VERIFICATION_REJECTED,
        AuditLogType.AML_CHECK_PERFORMED,
        AuditLogType.DOCUMENT_VERIFIED,
        AuditLogType.TAX_REPORT_GENERATED,
      ];
      
      return auditManager.getAuditLogs({
        userId,
        organizationId,
        startDate,
        endDate,
        types: complianceTypes,
        limit,
        offset,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      });
    } catch (error) {
      logger.error('Error getting compliance audit logs:', error);
      throw error;
    }
  }
}
