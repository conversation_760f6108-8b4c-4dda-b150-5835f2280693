import { AuditLogType } from '@prisma/client';

/**
 * Audit log interface
 */
export interface AuditLog {
  id: string;
  type: AuditLogType;
  description: string;
  userId?: string;
  organizationId?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

/**
 * Audit log creation data
 */
export interface AuditLogCreationData {
  type: AuditLogType;
  description: string;
  userId?: string;
  organizationId?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Audit log filter interface
 */
export interface AuditLogFilter {
  userId?: string;
  organizationId?: string;
  type?: AuditLogType;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Audit log pagination interface
 */
export interface AuditLogPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Audit log response interface
 */
export interface AuditLogResponse {
  auditLogs: AuditLog[];
  pagination: AuditLogPagination;
}

/**
 * Audit summary interface
 */
export interface AuditSummary {
  totalLogs: number;
  logsByType: Record<AuditLogType, number>;
  recentActivity: {
    date: string;
    count: number;
  }[];
  topUsers: {
    userId: string;
    userName?: string;
    count: number;
  }[];
  topOrganizations: {
    organizationId: string;
    organizationName?: string;
    count: number;
  }[];
}
