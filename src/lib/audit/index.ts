import { AuditLogType } from '@prisma/client';
import {
  AuditLog,
  AuditLogCreationData,
  AuditLogFilter,
  AuditLogResponse,
  AuditSummary
} from './types';
import { AuditService } from './service';
import { getServerSession } from '@/lib/nextauth-compat';
import { headers } from 'next/headers';

/**
 * Audit manager
 */
export class AuditManager {
  /**
   * Create an audit log
   * @param data Audit log creation data
   * @returns Created audit log
   */
  static async createAuditLog(data: AuditLogCreationData): Promise<AuditLog> {
    return AuditService.createAuditLog(data);
  }

  /**
   * Create an audit log with request context
   * @param data Audit log creation data without IP address and user agent
   * @returns Created audit log
   */
  static async createAuditLogWithContext(data: Omit<AuditLogCreationData, 'ipAddress' | 'userAgent'>): Promise<AuditLog> {
    // Get IP address and user agent from request headers
    const headersList = headers();
    const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'unknown';
    const userAgent = headersList.get('user-agent') || 'unknown';

    // Get user from session if not provided
    let userId = data.userId;
    if (!userId) {
      try {
        const session = await getServerSession();
        if (session?.user?.id) {
          userId = session.user.id;
        }
      } catch (error) {
        // Ignore session errors
      }
    }

    return AuditService.createAuditLog({
      ...data,
      userId,
      ipAddress,
      userAgent,
    });
  }

  /**
   * Get audit log by ID
   * @param id Audit log ID
   * @returns Audit log
   */
  static async getAuditLogById(id: string): Promise<AuditLog | null> {
    return AuditService.getAuditLogById(id);
  }

  /**
   * Get audit logs
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getAuditLogs(filter: AuditLogFilter = {}): Promise<AuditLogResponse> {
    return AuditService.getAuditLogs(filter);
  }

  /**
   * Get audit logs for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getUserAuditLogs(userId: string, filter: Omit<AuditLogFilter, 'userId'> = {}): Promise<AuditLogResponse> {
    return AuditService.getUserAuditLogs(userId, filter);
  }

  /**
   * Get audit logs for an organization
   * @param organizationId Organization ID
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getOrganizationAuditLogs(organizationId: string, filter: Omit<AuditLogFilter, 'organizationId'> = {}): Promise<AuditLogResponse> {
    return AuditService.getOrganizationAuditLogs(organizationId, filter);
  }

  /**
   * Get audit summary
   * @param filter Filter options
   * @returns Audit summary
   */
  static async getAuditSummary(filter: Omit<AuditLogFilter, 'limit' | 'offset' | 'sortBy' | 'sortOrder'> = {}): Promise<AuditSummary> {
    return AuditService.getAuditSummary(filter);
  }

  /**
   * Export audit logs to CSV
   * @param filter Filter options
   * @returns CSV content
   */
  static async exportAuditLogs(filter: AuditLogFilter = {}): Promise<string> {
    return AuditService.exportAuditLogs(filter);
  }
}

// Create a singleton instance
const auditManager = new AuditManager();

// Export the singleton instance
export { auditManager };

// Export types and enums
export {
  AuditLogType,
  AuditLog,
  AuditLogCreationData,
  AuditLogFilter,
  AuditLogResponse,
  AuditSummary
};
