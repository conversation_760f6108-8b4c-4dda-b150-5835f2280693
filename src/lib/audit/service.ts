import { AuditLogType, Prisma } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { 
  AuditLog, 
  AuditLogCreationData, 
  AuditLogFilter, 
  AuditLogResponse,
  AuditSummary
} from './types';

/**
 * Audit service for logging and retrieving audit logs
 */
export class AuditService {
  /**
   * Create an audit log
   * @param data Audit log creation data
   * @returns Created audit log
   */
  static async createAuditLog(data: AuditLogCreationData): Promise<AuditLog> {
    try {
      logger.info(`Creating audit log: ${data.type} - ${data.description}`);
      
      const auditLog = await db.auditLog.create({
        data: {
          type: data.type,
          description: data.description,
          ...(data.userId && {
            user: {
              connect: { id: data.userId }
            }
          }),
          ...(data.organizationId && {
            organization: {
              connect: { id: data.organizationId }
            }
          }),
          metadata: data.metadata as Prisma.JsonObject,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
        },
      });
      
      return auditLog;
    } catch (error) {
      logger.error(`Error creating audit log: ${data.type} - ${data.description}`, error);
      throw new Error(`Failed to create audit log: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get audit log by ID
   * @param id Audit log ID
   * @returns Audit log
   */
  static async getAuditLogById(id: string): Promise<AuditLog | null> {
    try {
      return await db.auditLog.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error getting audit log ${id}:`, error);
      throw new Error(`Failed to get audit log: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get audit logs
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getAuditLogs(filter: AuditLogFilter = {}): Promise<AuditLogResponse> {
    try {
      const {
        userId,
        organizationId,
        type,
        startDate,
        endDate,
        search,
        limit = 10,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = filter;
      
      // Build query
      const where: Prisma.AuditLogWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(type && { type }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
        ...(search && {
          OR: [
            { description: { contains: search, mode: "insensitive" } },
            { type: { contains: search, mode: "insensitive" } },
          ],
        }),
      };
      
      // Get audit logs
      const auditLogs = await db.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.auditLog.count({ where });
      
      return {
        auditLogs,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting audit logs:", error);
      throw new Error(`Failed to get audit logs: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get audit logs for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getUserAuditLogs(userId: string, filter: Omit<AuditLogFilter, 'userId'> = {}): Promise<AuditLogResponse> {
    return this.getAuditLogs({ ...filter, userId });
  }

  /**
   * Get audit logs for an organization
   * @param organizationId Organization ID
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getOrganizationAuditLogs(organizationId: string, filter: Omit<AuditLogFilter, 'organizationId'> = {}): Promise<AuditLogResponse> {
    return this.getAuditLogs({ ...filter, organizationId });
  }

  /**
   * Get audit summary
   * @param filter Filter options
   * @returns Audit summary
   */
  static async getAuditSummary(filter: Omit<AuditLogFilter, 'limit' | 'offset' | 'sortBy' | 'sortOrder'> = {}): Promise<AuditSummary> {
    try {
      const {
        userId,
        organizationId,
        type,
        startDate,
        endDate,
        search,
      } = filter;
      
      // Build query
      const where: Prisma.AuditLogWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(type && { type }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
        ...(search && {
          OR: [
            { description: { contains: search, mode: "insensitive" } },
            { type: { contains: search, mode: "insensitive" } },
          ],
        }),
      };
      
      // Get total count
      const totalLogs = await db.auditLog.count({ where });
      
      // Get logs by type
      const logsByTypeResult = await db.auditLog.groupBy({
        by: ['type'],
        where,
        _count: {
          id: true,
        },
      });
      
      const logsByType = logsByTypeResult.reduce((acc, curr) => {
        acc[curr.type as AuditLogType] = curr._count.id;
        return acc;
      }, {} as Record<AuditLogType, number>);
      
      // Get recent activity
      const recentActivity = await db.$queryRaw<{ date: string; count: number }[]>`
        SELECT 
          DATE_TRUNC('day', "createdAt") as date,
          COUNT(*) as count
        FROM "AuditLog"
        WHERE ${where ? Prisma.sql`${Prisma.join(Object.entries(where).map(([key, value]) => Prisma.sql`${Prisma.raw(key)} = ${value}`), ' AND ')}` : Prisma.sql`1=1`}
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date DESC
        LIMIT 30
      `;
      
      // Get top users
      const topUsers = await db.auditLog.groupBy({
        by: ['userId'],
        where: {
          ...where,
          userId: { not: null },
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 10,
      });
      
      // Get user names
      const userIds = topUsers.map((user) => user.userId).filter((id): id is string => id !== null);
      const users = await db.user.findMany({
        where: {
          id: { in: userIds },
        },
        select: {
          id: true,
          name: true,
        },
      });
      
      const userMap = users.reduce((acc, user) => {
        acc[user.id] = user.name;
        return acc;
      }, {} as Record<string, string | undefined>);
      
      // Get top organizations
      const topOrganizations = await db.auditLog.groupBy({
        by: ['organizationId'],
        where: {
          ...where,
          organizationId: { not: null },
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 10,
      });
      
      // Get organization names
      const organizationIds = topOrganizations.map((org) => org.organizationId).filter((id): id is string => id !== null);
      const organizations = await db.organization.findMany({
        where: {
          id: { in: organizationIds },
        },
        select: {
          id: true,
          name: true,
        },
      });
      
      const organizationMap = organizations.reduce((acc, org) => {
        acc[org.id] = org.name;
        return acc;
      }, {} as Record<string, string | undefined>);
      
      return {
        totalLogs,
        logsByType,
        recentActivity,
        topUsers: topUsers.map((user) => ({
          userId: user.userId!,
          userName: userMap[user.userId!],
          count: user._count.id,
        })),
        topOrganizations: topOrganizations.map((org) => ({
          organizationId: org.organizationId!,
          organizationName: organizationMap[org.organizationId!],
          count: org._count.id,
        })),
      };
    } catch (error) {
      logger.error("Error getting audit summary:", error);
      throw new Error(`Failed to get audit summary: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Export audit logs to CSV
   * @param filter Filter options
   * @returns CSV content
   */
  static async exportAuditLogs(filter: AuditLogFilter = {}): Promise<string> {
    try {
      // Get audit logs
      const { auditLogs } = await this.getAuditLogs({
        ...filter,
        limit: 1000, // Limit to 1000 logs for export
      });
      
      // Create CSV header
      const header = [
        "ID",
        "Type",
        "Description",
        "User ID",
        "User Name",
        "User Email",
        "Organization ID",
        "Organization Name",
        "IP Address",
        "User Agent",
        "Created At",
      ].join(",");
      
      // Create CSV rows
      const rows = auditLogs.map((log) => {
        const user = log.user as any;
        const organization = log.organization as any;
        
        return [
          log.id,
          log.type,
          `"${log.description.replace(/"/g, '""')}"`, // Escape quotes
          log.userId || "",
          user?.name ? `"${user.name.replace(/"/g, '""')}"` : "",
          user?.email || "",
          log.organizationId || "",
          organization?.name ? `"${organization.name.replace(/"/g, '""')}"` : "",
          log.ipAddress || "",
          log.userAgent ? `"${log.userAgent.replace(/"/g, '""')}"` : "",
          log.createdAt.toISOString(),
        ].join(",");
      });
      
      // Combine header and rows
      return [header, ...rows].join("\n");
    } catch (error) {
      logger.error("Error exporting audit logs:", error);
      throw new Error(`Failed to export audit logs: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
