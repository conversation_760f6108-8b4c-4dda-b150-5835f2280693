/**
 * Logger utility for consistent logging across the application
 * In a production environment, this would be replaced with a more robust logging solution
 * like <PERSON>, Pi<PERSON>, or a cloud-based logging service.
 */

// Log levels
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

// Current log level (can be set via environment variable)
const currentLogLevel = process.env.LOG_LEVEL
  ? parseInt(process.env.LOG_LEVEL)
  : process.env.NODE_ENV === "production"
  ? LogLevel.INFO
  : LogLevel.DEBUG;

// Format the log message with timestamp and metadata
function formatLogMessage(level: string, message: string, meta?: any): string {
  const timestamp = new Date().toISOString();
  const metaString = meta ? ` ${JSON.stringify(meta)}` : "";
  return `[${timestamp}] [${level}] ${message}${metaString}`;
}

// Log to the appropriate output
function log(level: LogLevel, levelName: string, message: string, meta?: any) {
  if (level <= currentLogLevel) {
    const formattedMessage = formatLogMessage(levelName, message, meta);
    
    switch (level) {
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage);
        break;
      case LogLevel.DEBUG:
        console.debug(formattedMessage);
        break;
    }
    
    // In a production environment, you would also log to a file or external service
    // For example:
    // if (process.env.NODE_ENV === "production") {
    //   logToExternalService(level, levelName, message, meta);
    // }
  }
}

// Logger interface
export const logger = {
  error: (message: string, meta?: any) => log(LogLevel.ERROR, "ERROR", message, meta),
  warn: (message: string, meta?: any) => log(LogLevel.WARN, "WARN", message, meta),
  info: (message: string, meta?: any) => log(LogLevel.INFO, "INFO", message, meta),
  debug: (message: string, meta?: any) => log(LogLevel.DEBUG, "DEBUG", message, meta),
  
  // Create a child logger with context
  child: (context: Record<string, any>) => ({
    error: (message: string, meta?: any) => log(LogLevel.ERROR, "ERROR", message, { ...context, ...meta }),
    warn: (message: string, meta?: any) => log(LogLevel.WARN, "WARN", message, { ...context, ...meta }),
    info: (message: string, meta?: any) => log(LogLevel.INFO, "INFO", message, { ...context, ...meta }),
    debug: (message: string, meta?: any) => log(LogLevel.DEBUG, "DEBUG", message, { ...context, ...meta }),
  }),
};
