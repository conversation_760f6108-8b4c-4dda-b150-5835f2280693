import { Alchemy, Utils, AssetTransfersCategory } from "alchemy-sdk";
import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { createAlchemyInstance, defaultAlchemy } from "@/lib/blockchain/alchemy-config";
import {
  createLightAccount,
  createLightAccountFromEncryptedKey,
  sendUserOperation,
  sendBatchUserOperation,
  estimateUserOperationGas,
  getSmartAccountAddress
} from "@/lib/blockchain/account-abstraction";

// Export the default Alchemy instance
export const alchemy = defaultAlchemy;

// Create a provider
const provider = new ethers.JsonRpcProvider(
  `https://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
);

// Carbon Credit Token ABI (ERC-1155 standard with additional metadata)
const CARBON_CREDIT_ABI = [
  // ERC-1155 standard functions
  "function balanceOf(address account, uint256 id) view returns (uint256)",
  "function balanceOfBatch(address[] calldata accounts, uint256[] calldata ids) view returns (uint256[] memory)",
  "function setApprovalForAll(address operator, bool approved)",
  "function isApprovedForAll(address account, address operator) view returns (bool)",
  "function safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes calldata data)",
  "function safeBatchTransferFrom(address from, address to, uint256[] calldata ids, uint256[] calldata amounts, bytes calldata data)",

  // Carbon credit specific functions
  "function mint(address to, uint256 id, uint256 amount, bytes calldata data)",
  "function mintBatch(address to, uint256[] calldata ids, uint256[] calldata amounts, bytes calldata data)",
  "function retire(uint256 id, uint256 amount)",
  "function retireBatch(uint256[] calldata ids, uint256[] calldata amounts)",
  "function getCarbonCreditData(uint256 id) view returns (string memory projectId, string memory vintage, string memory standard, string memory methodology, uint256 totalSupply, uint256 retiredSupply)",
  "function setURI(uint256 id, string calldata newuri)",
  "function uri(uint256 id) view returns (string memory)",

  // Events
  "event TransferSingle(address indexed operator, address indexed from, address indexed to, uint256 id, uint256 value)",
  "event TransferBatch(address indexed operator, address indexed from, address indexed to, uint256[] ids, uint256[] values)",
  "event ApprovalForAll(address indexed account, address indexed operator, bool approved)",
  "event URI(string value, uint256 indexed id)",
  "event CarbonCreditMinted(uint256 indexed id, address indexed to, uint256 amount, string projectId, string vintage, string standard, string methodology)",
  "event CarbonCreditRetired(uint256 indexed id, address indexed account, uint256 amount)",
];

// Map network to viem chain
function getViemChain(network: SupportedNetwork, useTestnet: boolean): Chain {
  if (useTestnet) {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia;
      case SupportedNetwork.POLYGON:
        return polygonMumbai;
      case SupportedNetwork.OPTIMISM:
        return optimismSepolia;
      case SupportedNetwork.ARBITRUM:
        return arbitrumSepolia;
      case SupportedNetwork.BASE:
        return baseSepolia;
      default:
        return sepolia;
    }
  } else {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia; // Replace with mainnet in production
      case SupportedNetwork.POLYGON:
        return polygon;
      case SupportedNetwork.OPTIMISM:
        return optimism;
      case SupportedNetwork.ARBITRUM:
        return arbitrum;
      case SupportedNetwork.BASE:
        return base;
      default:
        return sepolia; // Replace with mainnet in production
    }
  }
}

/**
 * Create a new wallet
 * @param createSmartAccount Whether to create a smart account
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Object containing wallet address, smart account address (if applicable), and encrypted private key
 */
export async function createWallet(
  createSmartAccount: boolean = false,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
) {
  try {
    // Generate a new random wallet
    const wallet = ethers.Wallet.createRandom();
    const address = wallet.address;

    // Encrypt the private key with a password (in production, this would be user-provided)
    const encryptedKey = await wallet.encrypt(process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret");

    logger.info(`Created new wallet with address: ${address}`);

    // If smart account is requested, create one
    if (createSmartAccount) {
      try {
        // Create a Light Account client
        const lightAccountClient = await createLightAccount(
          wallet.privateKey,
          network,
          useTestnet
        );

        // Get the smart account address
        const smartAccountAddress = await getSmartAccountAddress(lightAccountClient);

        logger.info(`Created smart account with address: ${smartAccountAddress}`);

        return {
          address,
          encryptedKey,
          smartAccountAddress,
          isSmartAccount: true,
        };
      } catch (smartAccountError) {
        logger.error("Error creating smart account:", smartAccountError);
        // Return regular wallet if smart account creation fails
        return {
          address,
          encryptedKey,
          isSmartAccount: false,
          smartAccountError: smartAccountError instanceof Error ? smartAccountError.message : "Unknown error",
        };
      }
    }

    return {
      address,
      encryptedKey,
      isSmartAccount: false,
    };
  } catch (error) {
    logger.error("Error creating wallet:", error);
    throw new Error("Failed to create wallet");
  }
}

/**
 * Get wallet balance
 * @param address Wallet address
 * @returns Wallet balance in ETH
 */
export async function getWalletBalance(address: string) {
  try {
    // Get the balance in wei
    const balanceWei = await alchemy.core.getBalance(address);

    // Convert to ETH
    const balanceEth = Utils.formatEther(balanceWei);

    logger.info(`Retrieved balance for wallet ${address}: ${balanceEth} ETH`);

    return {
      wei: balanceWei.toString(),
      eth: balanceEth,
    };
  } catch (error) {
    logger.error(`Error getting wallet balance for ${address}:`, error);
    throw new Error("Failed to get wallet balance");
  }
}

/**
 * Send transaction
 * @param encryptedKey Encrypted private key of the sender
 * @param to Recipient address
 * @param amount Amount to send in ETH
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @param isSmartAccount Whether to use a smart account
 * @param useGasManager Whether to use Alchemy's Gas Manager for sponsored transactions
 * @returns Transaction hash and details
 */
export async function sendTransaction(
  encryptedKey: string,
  to: string,
  amount: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true,
  isSmartAccount: boolean = false,
  useGasManager: boolean = false
) {
  try {
    // Get network configuration
    const networkConfig = getNetworkConfig(network, useTestnet);

    // Create Alchemy instance for the specified network
    const alchemyInstance = createAlchemyInstance(network, useTestnet);

    // Convert amount from ETH to wei
    const amountWei = ethers.parseEther(amount);

    // If using a smart account, use Account Abstraction SDK
    if (isSmartAccount) {
      try {
        logger.info(`Sending transaction using smart account to ${to}`);

        // Create Light Account client from encrypted key
        const lightAccountClient = await createLightAccountFromEncryptedKey(
          encryptedKey,
          network,
          useTestnet,
          useGasManager
        );

        // Get the smart account address
        const smartAccountAddress = await getSmartAccountAddress(lightAccountClient);

        // Send user operation
        const result = await sendUserOperation(
          lightAccountClient,
          to,
          "0x", // Empty data for ETH transfer
          BigInt(amountWei.toString())
        );

        logger.info(`Smart account transaction sent: ${result.txHash}`);

        return {
          hash: result.txHash,
          userOpHash: result.hash,
          from: smartAccountAddress,
          to,
          amount,
          network,
          chainId: networkConfig.chainId,
          isSmartAccount: true,
          receipt: result.receipt,
        };
      } catch (smartAccountError) {
        logger.error("Error sending transaction with smart account:", smartAccountError);
        throw new Error(`Smart account transaction failed: ${smartAccountError instanceof Error ? smartAccountError.message : "Unknown error"}`);
      }
    } else {
      // Decrypt the private key
      const wallet = await ethers.Wallet.fromEncryptedJson(
        encryptedKey,
        process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
      );

      // Create a provider for the specified network
      const networkProvider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);

      // Connect the wallet to the provider
      const connectedWallet = wallet.connect(networkProvider);

      // Create and send the transaction
      const tx = await connectedWallet.sendTransaction({
        to,
        value: amountWei,
      });

      logger.info(`Transaction sent: ${tx.hash}`);

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      logger.info(`Transaction confirmed in block ${receipt?.blockNumber}`);

      return {
        hash: tx.hash,
        from: wallet.address,
        to,
        amount,
        network,
        chainId: networkConfig.chainId,
        blockNumber: receipt?.blockNumber,
        gasUsed: receipt?.gasUsed.toString(),
        isSmartAccount: false,
      };
    }
  } catch (error) {
    logger.error("Error sending transaction:", error);
    throw new Error(`Failed to send transaction: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get transaction history for a wallet
 * @param address Wallet address
 * @param limit Number of transactions to retrieve
 * @returns Array of transactions
 */
export async function getTransactionHistory(address: string, limit = 10) {
  try {
    // Get transactions for the address
    const transactions = await alchemy.core.getAssetTransfers({
      fromAddress: address,
      category: [AssetTransfersCategory.EXTERNAL, AssetTransfersCategory.INTERNAL, AssetTransfersCategory.ERC20, AssetTransfersCategory.ERC721, AssetTransfersCategory.ERC1155] as any[],
      maxCount: limit,
    });

    logger.info(`Retrieved ${transactions.transfers.length} transactions for ${address}`);

    return transactions.transfers;
  } catch (error) {
    logger.error(`Error getting transaction history for ${address}:`, error);
    throw new Error("Failed to get transaction history");
  }
}

/**
 * Verify a transaction
 * @param txHash Transaction hash
 * @returns Transaction details
 */
export async function verifyTransaction(txHash: string) {
  try {
    // Get transaction details
    const tx = await alchemy.core.getTransaction(txHash);

    if (!tx) {
      throw new Error("Transaction not found");
    }

    // Get transaction receipt
    const receipt = await alchemy.core.getTransactionReceipt(txHash);

    logger.info(`Verified transaction ${txHash}`);

    return {
      transaction: tx,
      receipt,
      confirmed: receipt?.confirmations ? receipt.confirmations > 0 : false,
      success: receipt?.status === 1,
    };
  } catch (error) {
    logger.error(`Error verifying transaction ${txHash}:`, error);
    throw new Error("Failed to verify transaction");
  }
}

/**
 * Create a smart contract account provider
 * @param privateKey Private key of the owner
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @param useGasManager Whether to use Alchemy's Gas Manager for sponsored transactions
 * @returns Smart contract account provider
 */
export async function createSmartAccountProvider(
  privateKey: string,
  network: SupportedNetwork,
  useTestnet: boolean,
  useGasManager: boolean = false
) {
  try {
    logger.info(`Creating smart account provider for network: ${network}, testnet: ${useTestnet}`);

    // Create Light Account client
    const lightAccountClient = await createLightAccount(
      privateKey,
      network,
      useTestnet,
      useGasManager
    );

    return lightAccountClient;
  } catch (error) {
    logger.error("Error creating smart account provider:", error);
    throw new Error("Failed to create smart account provider");
  }
}

/**
 * Get the contract address for a network
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Contract address
 */
export function getCarbonCreditContractAddress(network: SupportedNetwork, useTestnet: boolean): string {
  const contractAddresses: Record<SupportedNetwork, { mainnet: string; testnet: string }> = {
    [SupportedNetwork.ETHEREUM]: {
      mainnet: process.env.CARBON_CREDIT_CONTRACT_ETHEREUM_MAINNET || "",
      testnet: process.env.CARBON_CREDIT_CONTRACT_ETHEREUM_TESTNET || "******************************************", // Example address
    },
    [SupportedNetwork.POLYGON]: {
      mainnet: process.env.CARBON_CREDIT_CONTRACT_POLYGON_MAINNET || "",
      testnet: process.env.CARBON_CREDIT_CONTRACT_POLYGON_TESTNET || "******************************************", // Example address
    },
    [SupportedNetwork.ARBITRUM]: {
      mainnet: process.env.CARBON_CREDIT_CONTRACT_ARBITRUM_MAINNET || "",
      testnet: process.env.CARBON_CREDIT_CONTRACT_ARBITRUM_TESTNET || "******************************************", // Example address
    },
    [SupportedNetwork.OPTIMISM]: {
      mainnet: process.env.CARBON_CREDIT_CONTRACT_OPTIMISM_MAINNET || "",
      testnet: process.env.CARBON_CREDIT_CONTRACT_OPTIMISM_TESTNET || "******************************************", // Example address
    },
    [SupportedNetwork.BASE]: {
      mainnet: process.env.CARBON_CREDIT_CONTRACT_BASE_MAINNET || "",
      testnet: process.env.CARBON_CREDIT_CONTRACT_BASE_TESTNET || "******************************************", // Example address
    },
  };

  const addresses = contractAddresses[network];
  return useTestnet ? addresses.testnet : addresses.mainnet;
}

/**
 * Tokenize a carbon credit on the blockchain
 * @param carbonCreditId Database ID of the carbon credit
 * @param tokenId Token ID to use (can be derived from the carbon credit ID)
 * @param amount Amount to tokenize
 * @param metadata Metadata for the carbon credit
 * @param ownerWalletAddress Wallet address of the owner
 * @param encryptedPrivateKey Encrypted private key of the owner
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Transaction details
 */
export async function tokenizeCarbonCredit(
  carbonCreditId: string,
  tokenId: number,
  amount: number,
  metadata: {
    projectId: string;
    vintage: number;
    standard: string;
    methodology: string;
    uri: string;
  },
  ownerWalletAddress: string,
  encryptedPrivateKey: string,
  network: SupportedNetwork = SupportedNetwork.POLYGON,
  useTestnet: boolean = true
) {
  try {
    logger.info(`Tokenizing carbon credit ${carbonCreditId} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);

    // Decrypt the private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedPrivateKey,
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );

    // Create a smart account provider with gas manager enabled
    const provider = await createSmartAccountProvider(wallet.privateKey, network, useTestnet, true);

    // Get the contract address
    const contractAddress = getCarbonCreditContractAddress(network, useTestnet);

    // Create the contract interface
    const contractInterface = new ethers.Interface(CARBON_CREDIT_ABI);

    // Encode the function data for minting
    const data = contractInterface.encodeFunctionData("mint", [
      ownerWalletAddress,
      tokenId,
      ethers.parseUnits(amount.toString(), 0), // Amount as a whole number
      ethers.toUtf8Bytes(JSON.stringify({
        projectId: metadata.projectId,
        vintage: metadata.vintage,
        standard: metadata.standard,
        methodology: metadata.methodology,
      })),
    ]);

    // Send the transaction
    const { hash } = await provider.sendTransaction({
      to: contractAddress,
      data,
      value: "0",
    });

    // Wait for transaction receipt
    let receipt = null;
    let retries = 0;
    const maxRetries = 10;

    while (!receipt && retries < maxRetries) {
      try {
        receipt = await provider.waitForTransactionReceipt({ hash });
      } catch (error) {
        retries++;
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
      }
    }

    // Set the token URI if the mint was successful
    if (receipt && receipt.status === "success") {
      // Encode the function data for setting the URI
      const uriData = contractInterface.encodeFunctionData("setURI", [
        tokenId,
        metadata.uri,
      ]);

      // Send the transaction to set the URI
      const { hash: uriHash } = await provider.sendTransaction({
        to: contractAddress,
        data: uriData,
        value: "0",
      });

      // Wait for the URI transaction to be mined
      let uriReceipt = null;
      retries = 0;

      while (!uriReceipt && retries < maxRetries) {
        try {
          uriReceipt = await provider.waitForTransactionReceipt({ hash: uriHash });
        } catch (error) {
          retries++;
          await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
        }
      }
    }

    // Get the smart wallet address
    const smartWalletAddress = await provider.getAddress();

    // Return the transaction details
    return {
      transactionHash: hash,
      tokenId,
      amount,
      contractAddress,
      ownerAddress: ownerWalletAddress,
      smartWalletAddress,
      network,
      chainId: getNetworkConfig(network, useTestnet).chainId,
      blockNumber: receipt?.blockNumber,
      status: receipt?.status === "success" ? "success" : "failed",
      metadata,
    };
  } catch (error) {
    logger.error("Error tokenizing carbon credit:", error);
    throw new Error(`Failed to tokenize carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Transfer tokenized carbon credits
 * @param fromAddress Sender address
 * @param toAddress Recipient address
 * @param tokenId Token ID
 * @param amount Amount to transfer
 * @param encryptedPrivateKey Encrypted private key of the sender
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Transaction details
 */
export async function transferCarbonCredit(
  fromAddress: string,
  toAddress: string,
  tokenId: number,
  amount: number,
  encryptedPrivateKey: string,
  network: SupportedNetwork = SupportedNetwork.POLYGON,
  useTestnet: boolean = true
) {
  try {
    logger.info(`Transferring ${amount} of token ${tokenId} from ${fromAddress} to ${toAddress} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);

    // Decrypt the private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedPrivateKey,
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );

    // Create a smart account provider with gas manager enabled
    const provider = await createSmartAccountProvider(wallet.privateKey, network, useTestnet, true);

    // Get the contract address
    const contractAddress = getCarbonCreditContractAddress(network, useTestnet);

    // Create the contract interface
    const contractInterface = new ethers.Interface(CARBON_CREDIT_ABI);

    // Encode the function data for transferring
    const data = contractInterface.encodeFunctionData("safeTransferFrom", [
      fromAddress,
      toAddress,
      tokenId,
      ethers.parseUnits(amount.toString(), 0), // Amount as a whole number
      "0x", // No additional data
    ]);

    // Send the transaction
    const { hash } = await provider.sendTransaction({
      to: contractAddress,
      data,
      value: "0",
    });

    // Wait for transaction receipt
    let receipt = null;
    let retries = 0;
    const maxRetries = 10;

    while (!receipt && retries < maxRetries) {
      try {
        receipt = await provider.waitForTransactionReceipt({ hash });
      } catch (error) {
        retries++;
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
      }
    }

    // Return the transaction details
    return {
      transactionHash: hash,
      tokenId,
      amount,
      contractAddress,
      fromAddress,
      toAddress,
      network,
      chainId: getNetworkConfig(network, useTestnet).chainId,
      blockNumber: receipt?.blockNumber,
      status: receipt?.status === "success" ? "success" : "failed",
    };
  } catch (error) {
    logger.error("Error transferring carbon credit:", error);
    throw new Error(`Failed to transfer carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Retire tokenized carbon credits
 * @param ownerAddress Owner address
 * @param tokenId Token ID
 * @param amount Amount to retire
 * @param encryptedPrivateKey Encrypted private key of the owner
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Transaction details
 */
export async function retireCarbonCredit(
  ownerAddress: string,
  tokenId: number,
  amount: number,
  encryptedPrivateKey: string,
  network: SupportedNetwork = SupportedNetwork.POLYGON,
  useTestnet: boolean = true
) {
  try {
    logger.info(`Retiring ${amount} of token ${tokenId} for ${ownerAddress} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);

    // Decrypt the private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedPrivateKey,
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );

    // Create a smart account provider with gas manager enabled
    const provider = await createSmartAccountProvider(wallet.privateKey, network, useTestnet, true);

    // Get the contract address
    const contractAddress = getCarbonCreditContractAddress(network, useTestnet);

    // Create the contract interface
    const contractInterface = new ethers.Interface(CARBON_CREDIT_ABI);

    // Encode the function data for retiring
    const data = contractInterface.encodeFunctionData("retire", [
      tokenId,
      ethers.parseUnits(amount.toString(), 0), // Amount as a whole number
    ]);

    // Send the transaction
    const { hash } = await provider.sendTransaction({
      to: contractAddress,
      data,
      value: "0",
    });

    // Wait for transaction receipt
    let receipt = null;
    let retries = 0;
    const maxRetries = 10;

    while (!receipt && retries < maxRetries) {
      try {
        receipt = await provider.waitForTransactionReceipt({ hash });
      } catch (error) {
        retries++;
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
      }
    }

    // Return the transaction details
    return {
      transactionHash: hash,
      tokenId,
      amount,
      contractAddress,
      ownerAddress,
      network,
      chainId: getNetworkConfig(network, useTestnet).chainId,
      blockNumber: receipt?.blockNumber,
      status: receipt?.status === "success" ? "success" : "failed",
    };
  } catch (error) {
    logger.error("Error retiring carbon credit:", error);
    throw new Error(`Failed to retire carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get carbon credit token balance
 * @param walletAddress Wallet address
 * @param tokenId Token ID
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Token balance
 */
export async function getCarbonCreditBalance(
  walletAddress: string,
  tokenId: number,
  network: SupportedNetwork = SupportedNetwork.POLYGON,
  useTestnet: boolean = true
) {
  try {
    logger.info(`Getting balance of token ${tokenId} for ${walletAddress} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);

    // Get the contract address
    const contractAddress = getCarbonCreditContractAddress(network, useTestnet);

    // Create a provider
    const provider = new ethers.JsonRpcProvider(getNetworkConfig(network, useTestnet).rpcUrl);

    // Create the contract instance
    const contract = new ethers.Contract(contractAddress, CARBON_CREDIT_ABI, provider);

    // Get the balance
    const balance = await contract.balanceOf(walletAddress, tokenId);

    // Get the carbon credit data
    const data = await contract.getCarbonCreditData(tokenId);

    // Return the balance and data
    return {
      balance: Number(balance),
      tokenId,
      projectId: data.projectId,
      vintage: data.vintage,
      standard: data.standard,
      methodology: data.methodology,
      totalSupply: Number(data.totalSupply),
      retiredSupply: Number(data.retiredSupply),
    };
  } catch (error) {
    logger.error("Error getting carbon credit balance:", error);
    throw new Error(`Failed to get carbon credit balance: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Estimate transaction cost
export async function estimateTransactionCost(
  txData: {
    to: string;
    data: string;
    value?: string;
  },
  network: SupportedNetwork = SupportedNetwork.POLYGON,
  useTestnet: boolean = true
) {
  try {
    logger.info(`Estimating transaction cost for ${JSON.stringify(txData)}`);

    // Create a provider
    const provider = new ethers.JsonRpcProvider(getNetworkConfig(network, useTestnet).rpcUrl);

    // Get gas price
    const gasPrice = await provider.getFeeData();

    // Estimate gas limit
    const gasLimit = await provider.estimateGas({
      to: txData.to,
      data: txData.data,
      value: txData.value || "0",
    });

    // Calculate cost in wei
    const maxFeePerGas = gasPrice.maxFeePerGas || gasPrice.gasPrice;
    const costInWei = (maxFeePerGas || BigInt(0)) * gasLimit;

    // Convert to ether
    const costInEther = Number(ethers.formatEther(costInWei));

    return {
      gasPrice: maxFeePerGas?.toString() || "0",
      gasLimit: gasLimit.toString(),
      costInWei: costInWei.toString(),
      costInEther
    };
  } catch (error) {
    logger.error("Error estimating transaction cost:", error);
    throw new Error(`Failed to estimate transaction cost: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Export blockchain service singleton for compatibility
export const blockchainService = {
  verifyTransaction,
  getCarbonCreditBalance,
  getTransactionHistory,
  getWalletBalance,
  createWallet,
  tokenizeCarbonCredit,
  transferCarbonCredit,
  retireCarbonCredit
};
