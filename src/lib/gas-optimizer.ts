import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { BlockchainClient, getBlockchainClient } from "@/lib/blockchain-client";
import { SupportedNetwork } from "@/lib/blockchain-config";

/**
 * Gas optimization strategy
 */
export enum GasStrategy {
  SLOW = "slow",
  AVERAGE = "average",
  FAST = "fast",
  CUSTOM = "custom",
}

/**
 * Gas optimization options
 */
export interface GasOptions {
  strategy?: GasStrategy;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  gasPrice?: string;
  gasLimit?: number;
  nonce?: number;
}

/**
 * Gas optimizer for optimizing transaction gas parameters
 */
export class GasOptimizer {
  private client: BlockchainClient;

  /**
   * Create a new gas optimizer
   * @param network Network to use
   */
  constructor(network: SupportedNetwork) {
    this.client = getBlockchainClient(network);
  }

  /**
   * Get optimized gas parameters
   * @param options Gas options
   * @returns Optimized gas parameters
   */
  async getOptimizedGasParams(options: GasOptions = {}) {
    try {
      // Get current gas prices
      const gasPrices = await this.client.getGasPrices();
      
      // If custom strategy with explicit parameters, use those
      if (options.strategy === GasStrategy.CUSTOM) {
        if (gasPrices.eip1559) {
          return {
            maxFeePerGas: options.maxFeePerGas,
            maxPriorityFeePerGas: options.maxPriorityFeePerGas,
            gasLimit: options.gasLimit,
            nonce: options.nonce,
          };
        } else {
          return {
            gasPrice: options.gasPrice,
            gasLimit: options.gasLimit,
            nonce: options.nonce,
          };
        }
      }
      
      // Otherwise, use the selected strategy
      const strategy = options.strategy || GasStrategy.AVERAGE;
      
      if (gasPrices.eip1559) {
        return {
          maxFeePerGas: gasPrices[strategy].maxFeePerGas,
          maxPriorityFeePerGas: gasPrices[strategy].maxPriorityFeePerGas,
          gasLimit: options.gasLimit,
          nonce: options.nonce,
        };
      } else {
        return {
          gasPrice: gasPrices[strategy].gasPrice,
          gasLimit: options.gasLimit,
          nonce: options.nonce,
        };
      }
    } catch (error) {
      logger.error("Error getting optimized gas parameters:", error);
      throw new Error("Failed to get optimized gas parameters");
    }
  }

  /**
   * Estimate gas for a transaction
   * @param from Sender address
   * @param to Recipient address
   * @param value Amount to send
   * @param data Transaction data
   * @returns Estimated gas with buffer
   */
  async estimateGas(from: string, to: string, value: string, data: string = "0x") {
    return this.client.estimateGas(from, to, value, data);
  }

  /**
   * Calculate gas cost in native currency
   * @param gasLimit Gas limit
   * @param gasPrice Gas price in Gwei
   * @returns Gas cost in native currency
   */
  calculateGasCost(gasLimit: number, gasPrice: string) {
    const gasPriceWei = ethers.parseUnits(gasPrice, "gwei");
    const gasCostWei = BigInt(gasLimit) * gasPriceWei;
    return ethers.formatEther(gasCostWei);
  }

  /**
   * Calculate EIP-1559 gas cost in native currency
   * @param gasLimit Gas limit
   * @param maxFeePerGas Max fee per gas in Gwei
   * @returns Maximum gas cost in native currency
   */
  calculateEIP1559GasCost(gasLimit: number, maxFeePerGas: string) {
    const maxFeePerGasWei = ethers.parseUnits(maxFeePerGas, "gwei");
    const maxGasCostWei = BigInt(gasLimit) * maxFeePerGasWei;
    return ethers.formatEther(maxGasCostWei);
  }

  /**
   * Get gas price history
   * @param blocks Number of blocks to look back
   * @returns Gas price history
   */
  async getGasPriceHistory(blocks: number = 10) {
    try {
      const provider = this.client.getProvider();
      const currentBlock = await provider.getBlockNumber();
      
      const history = [];
      
      for (let i = 0; i < blocks; i++) {
        const blockNumber = currentBlock - i;
        const block = await provider.getBlock(blockNumber);
        
        if (block && block.baseFeePerGas) {
          history.push({
            blockNumber,
            timestamp: new Date(Number(block.timestamp) * 1000).toISOString(),
            baseFeePerGas: ethers.formatUnits(block.baseFeePerGas, "gwei"),
          });
        }
      }
      
      return history;
    } catch (error) {
      logger.error("Error getting gas price history:", error);
      throw new Error("Failed to get gas price history");
    }
  }

  /**
   * Recommend gas strategy based on transaction urgency and current network conditions
   * @param urgency Transaction urgency (0-100)
   * @returns Recommended gas strategy
   */
  async recommendGasStrategy(urgency: number) {
    try {
      // Get current gas prices
      const gasPrices = await this.client.getGasPrices();
      
      // Get gas price history
      const history = await this.getGasPriceHistory(5);
      
      // Calculate average base fee from history
      let avgBaseFee = 0;
      if (history.length > 0) {
        avgBaseFee = history.reduce((sum, block) => sum + parseFloat(block.baseFeePerGas), 0) / history.length;
      }
      
      // Check if current base fee is higher than average (congested network)
      const currentBaseFee = gasPrices.eip1559 ? parseFloat(gasPrices.baseFeePerGas) : 0;
      const isNetworkCongested = currentBaseFee > avgBaseFee * 1.2; // 20% higher than average
      
      // Adjust urgency based on network congestion
      const adjustedUrgency = isNetworkCongested ? Math.min(urgency + 20, 100) : urgency;
      
      // Determine strategy based on adjusted urgency
      if (adjustedUrgency < 30) {
        return {
          strategy: GasStrategy.SLOW,
          reason: isNetworkCongested 
            ? "Network is congested, but transaction is not urgent" 
            : "Network is not congested and transaction is not urgent",
        };
      } else if (adjustedUrgency < 70) {
        return {
          strategy: GasStrategy.AVERAGE,
          reason: isNetworkCongested 
            ? "Network is congested, using average gas price for moderate urgency" 
            : "Using average gas price for moderate urgency",
        };
      } else {
        return {
          strategy: GasStrategy.FAST,
          reason: isNetworkCongested 
            ? "Network is congested and transaction is urgent, using fast gas price" 
            : "Transaction is urgent, using fast gas price",
        };
      }
    } catch (error) {
      logger.error("Error recommending gas strategy:", error);
      // Default to average if there's an error
      return {
        strategy: GasStrategy.AVERAGE,
        reason: "Error occurred, defaulting to average gas price",
      };
    }
  }
}
