import NextAuth from "next-auth";
import type { DefaultSession, NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { AuditLogType } from "@prisma/client";
import { verifyPassword } from "@/lib/auth/password";

// Import the PrismaAdapter when ready to use it
// import { PrismaAdapter } from "@auth/prisma-adapter";

// For JWT type extensions
import { JWT } from "next-auth/jwt";

/**
 * Extend the built-in session types
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role: UserRole;
      organizationId: string | null;
    } & DefaultSession["user"];
  }

  interface User {
    id?: string;
    role: UserRole;
    organizationId: string | null;
    emailVerified?: Date | null;
    twoFactorEnabled?: boolean;
    lastLoginAt?: Date | null;
  }
}

/**
 * Extend the JWT type
 */
declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: UserRole;
    organizationId: string | null;
  }
}

/**
 * Auth.js configuration
 * @see https://authjs.dev/getting-started/installation
 */
export const authConfig: NextAuthConfig = {
  // Uncomment to use the Prisma adapter
  // adapter: PrismaAdapter(db),
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login",
    signOut: "/",
    error: "/error",
    verifyRequest: "/verify-email",
    newUser: "/onboarding", // Redirect new users directly to onboarding
  },
  debug: process.env.NODE_ENV === "development",
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        // Check if credentials are provided
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await db.user.findUnique({
          where: {
            email: credentials.email as string,
          },
        });

        if (!user || !user.password) {
          return null;
        }

        // Use our custom password verification function
        const isPasswordValid = await verifyPassword(credentials.password as string, user.password || '');

        if (!isPasswordValid) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organizationId: user.organizationId,
        };
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }: { user: any; account: any }) {
      // Allow OAuth without email verification
      if (account?.provider !== "credentials") {
        return true;
      }

      if (user) {
        // Update last login timestamp
        try {
          await db.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() },
          });

          // Log successful login
          try {
            await db.auditLog.create({
              data: {
                type: AuditLogType.LOGIN_SUCCESS,
                description: `User ${user.email} logged in successfully`,
                userId: user.id,
                organizationId: user.organizationId || undefined,
                ipAddress: "unknown", // In production, get from request
                userAgent: "unknown", // In production, get from request
              },
            });
          } catch (logError) {
            logger.error("Error logging login", logError);
          }
        } catch (error) {
          logger.error("Error updating last login time", error);
          // Don't block sign in if this fails
        }
      }

      return true;
    },
    async session({ session, token }: { session: any; token: JWT }) {
      if (token) {
        session.user.id = token.id;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.role = token.role;
        session.user.organizationId = token.organizationId;
      }
      return session;
    },
    async jwt({ token, user }: { token: JWT; user: any | undefined }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.organizationId = user.organizationId;
      }

      return token;
    },
  },
} satisfies NextAuthConfig;

/**
 * Auth.js v5 handler
 * This exports the handlers needed for Auth.js v5
 * @see https://authjs.dev/getting-started/installation?framework=Next.js
 */
export const { handlers, auth, signIn, signOut } = NextAuth(authConfig);

// For backwards compatibility
export const authOptions = authConfig;

/**
 * Get the current user from the session
 * This is a helper function that can be used in server components
 */
export async function getCurrentUser() {
  const session = await auth();

  if (!session?.user?.id) {
    return null;
  }

  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      organizationId: true,
      organization: {
        select: {
          id: true,
          name: true,
          status: true,
          verificationStatus: true,
        },
      },
      emailVerified: true,
      lastLoginAt: true,
      twoFactorEnabled: true,
    },
  });

  return user;
}

/**
 * Check if the current user has the required role
 * @param role The role to check for
 * @returns True if the user has the required role, false otherwise
 */
export async function hasRole(role: string | string[]) {
  const session = await auth();

  if (!session?.user?.role) {
    return false;
  }

  if (Array.isArray(role)) {
    return role.includes(session.user.role);
  }

  return session.user.role === role;
}

/**
 * Check if the current user belongs to the specified organization
 * @param organizationId The organization ID to check for
 * @returns True if the user belongs to the organization, false otherwise
 */
export async function belongsToOrganization(organizationId: string) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return false;
  }

  return session.user.organizationId === organizationId;
}

/**
 * Check if the current user is an admin of the specified organization
 * @param organizationId The organization ID to check for
 * @returns True if the user is an admin of the organization, false otherwise
 */
export async function isOrganizationAdmin(organizationId: string) {
  const session = await auth();

  if (!session?.user?.organizationId || !session?.user?.role) {
    return false;
  }

  return session.user.organizationId === organizationId && session.user.role === 'ORGANIZATION_ADMIN';
}

/**
 * Check if the current user is a platform admin
 * @returns True if the user is a platform admin, false otherwise
 */
export async function isPlatformAdmin() {
  const session = await auth();

  if (!session?.user?.role) {
    return false;
  }

  return session.user.role === 'ADMIN';
}
