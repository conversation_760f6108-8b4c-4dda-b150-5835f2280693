import { 
  OrganizationStatus, 
  VerificationStatus, 
  UserRole, 
  DocumentType, 
  DocumentStatus,
  OrganizationSize,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { NotificationService } from '@/lib/notification-service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Organization creation data interface
 */
export interface OrganizationCreationData {
  name: string;
  description?: string;
  website?: string;
  logo?: string;
  legalName?: string;
  registrationNumber?: string;
  taxId?: string;
  country?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  phoneNumber?: string;
  industry?: string;
  size?: OrganizationSize;
  foundedYear?: number;
  primaryContact?: string;
  primaryContactEmail?: string;
  primaryContactPhone?: string;
  userId: string; // ID of the user creating the organization
}

/**
 * Organization verification data interface
 */
export interface OrganizationVerificationData {
  organizationId: string;
  documents: {
    name: string;
    type: DocumentType;
    url: string;
    notes?: string;
  }[];
}

/**
 * Organization service for managing organizations
 */
export class OrganizationService {
  /**
   * Create a new organization
   * @param data Organization creation data
   * @returns Created organization
   */
  static async createOrganization(data: OrganizationCreationData) {
    try {
      // Create the organization
      const organization = await db.organization.create({
        data: {
          name: data.name,
          description: data.description,
          website: data.website,
          logo: data.logo,
          status: OrganizationStatus.PENDING,
          verificationStatus: VerificationStatus.PENDING,
          legalName: data.legalName,
          registrationNumber: data.registrationNumber,
          taxId: data.taxId,
          country: data.country,
          address: data.address,
          city: data.city,
          state: data.state,
          postalCode: data.postalCode,
          phoneNumber: data.phoneNumber,
          industry: data.industry,
          size: data.size,
          foundedYear: data.foundedYear,
          primaryContact: data.primaryContact,
          primaryContactEmail: data.primaryContactEmail,
          primaryContactPhone: data.primaryContactPhone,
          // Create a free subscription by default
          subscription: {
            create: {
              plan: 'FREE',
              startDate: new Date(),
            },
          },
          // Add the creating user as an organization admin
          users: {
            connect: { id: data.userId },
          },
        },
      });

      // Update the user's role to ORGANIZATION_ADMIN
      await db.user.update({
        where: { id: data.userId },
        data: { role: UserRole.ORGANIZATION_ADMIN },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'ORGANIZATION_CREATED',
          description: `Organization ${organization.name} created`,
          user: { connect: { id: data.userId } },
          organization: { connect: { id: organization.id } },
          metadata: {
            organizationId: organization.id,
            organizationName: organization.name,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Organization Created',
        message: `Your organization ${organization.name} has been created and is pending verification.`,
        type: 'SYSTEM',
        userId: data.userId,
        organizationId: organization.id,
        actionUrl: `/dashboard/organization/verification`,
        actionLabel: 'Complete Verification',
      });

      logger.info(`Organization ${organization.id} created by user ${data.userId}`);

      return organization;
    } catch (error) {
      logger.error('Error creating organization:', error);
      throw new Error('Failed to create organization');
    }
  }

  /**
   * Submit organization verification documents
   * @param data Organization verification data
   * @returns Updated organization
   */
  static async submitVerification(data: OrganizationVerificationData) {
    try {
      // Update organization verification status
      const organization = await db.organization.update({
        where: { id: data.organizationId },
        data: {
          verificationStatus: VerificationStatus.IN_REVIEW,
        },
      });

      // Add documents
      for (const doc of data.documents) {
        await db.document.create({
          data: {
            name: doc.name,
            type: doc.type,
            url: doc.url,
            notes: doc.notes,
            status: DocumentStatus.PENDING,
            organization: { connect: { id: data.organizationId } },
          },
        });
      }

      // Get organization admin
      const admin = await db.user.findFirst({
        where: {
          organizationId: data.organizationId,
          role: UserRole.ORGANIZATION_ADMIN,
        },
      });

      if (admin) {
        // Create an audit log
        await db.auditLog.create({
          data: {
            type: 'DOCUMENT_UPLOADED',
            description: `Verification documents submitted for ${organization.name}`,
            user: { connect: { id: admin.id } },
            organization: { connect: { id: organization.id } },
            metadata: {
              organizationId: organization.id,
              organizationName: organization.name,
              documentCount: data.documents.length,
            } as Prisma.JsonObject,
          },
        });

        // Send notification to the admin
        await NotificationService.createNotification({
          title: 'Verification Submitted',
          message: `Your organization verification documents have been submitted and are under review.`,
          type: 'VERIFICATION',
          userId: admin.id,
          organizationId: organization.id,
          actionUrl: `/dashboard/organization/verification`,
          actionLabel: 'View Status',
        });
      }

      // Send notification to platform admins
      const platformAdmins = await db.user.findMany({
        where: { role: UserRole.ADMIN },
      });

      for (const platformAdmin of platformAdmins) {
        await NotificationService.createNotification({
          title: 'New Organization Verification',
          message: `${organization.name} has submitted verification documents for review.`,
          type: 'VERIFICATION',
          priority: 'HIGH',
          userId: platformAdmin.id,
          organizationId: organization.id,
          actionUrl: `/admin/organizations/${organization.id}/verification`,
          actionLabel: 'Review Documents',
        });
      }

      logger.info(`Verification documents submitted for organization ${organization.id}`);

      return organization;
    } catch (error) {
      logger.error('Error submitting verification:', error);
      throw new Error('Failed to submit verification');
    }
  }

  /**
   * Approve organization verification
   * @param organizationId Organization ID
   * @param adminId Admin user ID
   * @param notes Optional notes
   * @returns Updated organization
   */
  static async approveVerification(organizationId: string, adminId: string, notes?: string) {
    try {
      // Update organization status
      const organization = await db.organization.update({
        where: { id: organizationId },
        data: {
          status: OrganizationStatus.ACTIVE,
          verificationStatus: VerificationStatus.VERIFIED,
        },
        include: {
          users: true,
        },
      });

      // Update document statuses
      await db.document.updateMany({
        where: {
          organizationId,
          status: DocumentStatus.PENDING,
        },
        data: {
          status: DocumentStatus.APPROVED,
          notes: notes,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'DOCUMENT_VERIFIED',
          description: `Organization ${organization.name} verification approved`,
          user: { connect: { id: adminId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            organizationId,
            organizationName: organization.name,
            notes,
          } as Prisma.JsonObject,
        },
      });

      // Send notifications to organization users
      for (const user of organization.users) {
        await NotificationService.createNotification({
          title: 'Organization Verified',
          message: `Your organization ${organization.name} has been verified and is now active.`,
          type: 'VERIFICATION',
          priority: 'HIGH',
          userId: user.id,
          organizationId,
          actionUrl: `/dashboard`,
          actionLabel: 'Go to Dashboard',
        });
      }

      logger.info(`Organization ${organizationId} verification approved by admin ${adminId}`);

      return organization;
    } catch (error) {
      logger.error('Error approving verification:', error);
      throw new Error('Failed to approve verification');
    }
  }

  /**
   * Reject organization verification
   * @param organizationId Organization ID
   * @param adminId Admin user ID
   * @param reason Rejection reason
   * @returns Updated organization
   */
  static async rejectVerification(organizationId: string, adminId: string, reason: string) {
    try {
      // Update organization status
      const organization = await db.organization.update({
        where: { id: organizationId },
        data: {
          verificationStatus: VerificationStatus.REJECTED,
        },
        include: {
          users: true,
        },
      });

      // Update document statuses
      await db.document.updateMany({
        where: {
          organizationId,
          status: DocumentStatus.PENDING,
        },
        data: {
          status: DocumentStatus.REJECTED,
          notes: reason,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'DOCUMENT_VERIFIED',
          description: `Organization ${organization.name} verification rejected`,
          user: { connect: { id: adminId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            organizationId,
            organizationName: organization.name,
            reason,
          } as Prisma.JsonObject,
        },
      });

      // Send notifications to organization users
      for (const user of organization.users) {
        await NotificationService.createNotification({
          title: 'Verification Rejected',
          message: `Your organization verification was rejected. Reason: ${reason}`,
          type: 'VERIFICATION',
          priority: 'HIGH',
          userId: user.id,
          organizationId,
          actionUrl: `/dashboard/organization/verification`,
          actionLabel: 'Update Verification',
        });
      }

      logger.info(`Organization ${organizationId} verification rejected by admin ${adminId}`);

      return organization;
    } catch (error) {
      logger.error('Error rejecting verification:', error);
      throw new Error('Failed to reject verification');
    }
  }

  /**
   * Invite a user to an organization
   * @param organizationId Organization ID
   * @param inviterId User ID of the inviter
   * @param email Email of the invitee
   * @param role Role to assign to the invitee
   * @param teamId Optional team ID to add the user to
   * @returns Created invitation
   */
  static async inviteUser(
    organizationId: string,
    inviterId: string,
    email: string,
    role: UserRole = UserRole.USER,
    teamId?: string
  ) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the inviter has permission to invite users
      const inviter = await db.user.findUnique({
        where: { id: inviterId },
      });

      if (!inviter || (inviter.role !== UserRole.ADMIN && inviter.role !== UserRole.ORGANIZATION_ADMIN)) {
        throw new Error('You do not have permission to invite users');
      }

      // Check if the user is already a member of the organization
      const existingUser = await db.user.findFirst({
        where: {
          email,
          organizationId,
        },
      });

      if (existingUser) {
        throw new Error('User is already a member of this organization');
      }

      // Check if there's an existing invitation
      const existingInvitation = await db.invitation.findFirst({
        where: {
          email,
          organizationId,
          status: 'PENDING',
        },
      });

      if (existingInvitation) {
        throw new Error('An invitation has already been sent to this email');
      }

      // Create a token and expiration date (24 hours from now)
      const token = uuidv4();
      const expires = new Date();
      expires.setHours(expires.getHours() + 24);

      // Create the invitation
      const invitation = await db.invitation.create({
        data: {
          email,
          token,
          role,
          teamId,
          expires,
          organization: { connect: { id: organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'INVITATION_SENT',
          description: `Invitation sent to ${email}`,
          user: { connect: { id: inviterId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            invitationId: invitation.id,
            email,
            role,
            teamId,
          } as Prisma.JsonObject,
        },
      });

      // In a real implementation, send an email to the invitee
      // For now, we'll just log it
      logger.info(`Invitation sent to ${email} for organization ${organizationId}`);

      return invitation;
    } catch (error) {
      logger.error('Error inviting user:', error);
      throw error;
    }
  }

  /**
   * Create a team in an organization
   * @param organizationId Organization ID
   * @param userId User ID creating the team
   * @param name Team name
   * @param description Team description
   * @param permissions Team permissions
   * @returns Created team
   */
  static async createTeam(
    organizationId: string,
    userId: string,
    name: string,
    description?: string,
    permissions?: any
  ) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the user has permission to create teams
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.ORGANIZATION_ADMIN)) {
        throw new Error('You do not have permission to create teams');
      }

      // Create the team
      const team = await db.team.create({
        data: {
          name,
          description,
          permissions: permissions as Prisma.JsonObject,
          organization: { connect: { id: organizationId } },
          // Add the creating user as a team admin
          members: {
            create: {
              role: 'ADMIN',
              user: { connect: { id: userId } },
            },
          },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'TEAM_CREATED',
          description: `Team ${name} created`,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            teamId: team.id,
            teamName: name,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Team Created',
        message: `You have created a new team: ${name}`,
        type: 'TEAM',
        userId,
        organizationId,
        actionUrl: `/dashboard/team/${team.id}`,
        actionLabel: 'View Team',
      });

      logger.info(`Team ${team.id} created in organization ${organizationId} by user ${userId}`);

      return team;
    } catch (error) {
      logger.error('Error creating team:', error);
      throw error;
    }
  }

  /**
   * Add a user to a team
   * @param teamId Team ID
   * @param userId User ID to add
   * @param role Role to assign to the user
   * @param adminId Admin user ID making the change
   * @returns Created team member
   */
  static async addTeamMember(teamId: string, userId: string, role: string, adminId: string) {
    try {
      // Check if the team exists
      const team = await db.team.findUnique({
        where: { id: teamId },
        include: {
          organization: true,
        },
      });

      if (!team) {
        throw new Error('Team not found');
      }

      // Check if the admin has permission to add team members
      const admin = await db.user.findUnique({
        where: { id: adminId },
      });

      if (!admin) {
        throw new Error('Admin user not found');
      }

      // Check if the admin is an organization admin or a team admin
      const isOrgAdmin = admin.role === UserRole.ADMIN || admin.role === UserRole.ORGANIZATION_ADMIN;
      const isTeamAdmin = await db.teamMember.findFirst({
        where: {
          teamId,
          userId: adminId,
          role: 'ADMIN',
        },
      });

      if (!isOrgAdmin && !isTeamAdmin) {
        throw new Error('You do not have permission to add team members');
      }

      // Check if the user is a member of the organization
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || user.organizationId !== team.organizationId) {
        throw new Error('User is not a member of this organization');
      }

      // Check if the user is already a member of the team
      const existingMember = await db.teamMember.findFirst({
        where: {
          teamId,
          userId,
        },
      });

      if (existingMember) {
        throw new Error('User is already a member of this team');
      }

      // Add the user to the team
      const teamMember = await db.teamMember.create({
        data: {
          role: role as any,
          team: { connect: { id: teamId } },
          user: { connect: { id: userId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'TEAM_UPDATED',
          description: `User ${user.name || user.email} added to team ${team.name}`,
          user: { connect: { id: adminId } },
          organization: { connect: { id: team.organizationId } },
          metadata: {
            teamId,
            teamName: team.name,
            userId,
            userName: user.name || user.email,
            role,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Added to Team',
        message: `You have been added to the team: ${team.name}`,
        type: 'TEAM',
        userId,
        organizationId: team.organizationId,
        actionUrl: `/dashboard/team/${team.id}`,
        actionLabel: 'View Team',
      });

      logger.info(`User ${userId} added to team ${teamId} by admin ${adminId}`);

      return teamMember;
    } catch (error) {
      logger.error('Error adding team member:', error);
      throw error;
    }
  }

  /**
   * Update organization settings
   * @param organizationId Organization ID
   * @param userId User ID making the change
   * @param data Organization data to update
   * @returns Updated organization
   */
  static async updateOrganization(
    organizationId: string,
    userId: string,
    data: Partial<OrganizationCreationData>
  ) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the user has permission to update the organization
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.ORGANIZATION_ADMIN)) {
        throw new Error('You do not have permission to update this organization');
      }

      // Update the organization
      const updatedOrganization = await db.organization.update({
        where: { id: organizationId },
        data: {
          name: data.name,
          description: data.description,
          website: data.website,
          logo: data.logo,
          legalName: data.legalName,
          registrationNumber: data.registrationNumber,
          taxId: data.taxId,
          country: data.country,
          address: data.address,
          city: data.city,
          state: data.state,
          postalCode: data.postalCode,
          phoneNumber: data.phoneNumber,
          industry: data.industry,
          size: data.size,
          foundedYear: data.foundedYear,
          primaryContact: data.primaryContact,
          primaryContactEmail: data.primaryContactEmail,
          primaryContactPhone: data.primaryContactPhone,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'ORGANIZATION_UPDATED',
          description: `Organization ${organization.name} updated`,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            organizationId,
            organizationName: organization.name,
            changes: data,
          } as Prisma.JsonObject,
        },
      });

      logger.info(`Organization ${organizationId} updated by user ${userId}`);

      return updatedOrganization;
    } catch (error) {
      logger.error('Error updating organization:', error);
      throw error;
    }
  }

  /**
   * Update organization fee settings
   * @param organizationId Organization ID
   * @param adminId Admin user ID making the change
   * @param transactionFeeRate Transaction fee rate
   * @param listingFeeRate Listing fee rate
   * @returns Updated organization
   */
  static async updateFeeSettings(
    organizationId: string,
    adminId: string,
    transactionFeeRate?: number,
    listingFeeRate?: number
  ) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the user is a platform admin
      const admin = await db.user.findUnique({
        where: { id: adminId },
      });

      if (!admin || admin.role !== UserRole.ADMIN) {
        throw new Error('You do not have permission to update fee settings');
      }

      // Update the organization
      const updatedOrganization = await db.organization.update({
        where: { id: organizationId },
        data: {
          transactionFeeRate: transactionFeeRate !== undefined ? transactionFeeRate : undefined,
          listingFeeRate: listingFeeRate !== undefined ? listingFeeRate : undefined,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'ORGANIZATION_UPDATED',
          description: `Fee settings updated for ${organization.name}`,
          user: { connect: { id: adminId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            organizationId,
            organizationName: organization.name,
            transactionFeeRate,
            listingFeeRate,
          } as Prisma.JsonObject,
        },
      });

      // Get organization admin
      const orgAdmin = await db.user.findFirst({
        where: {
          organizationId,
          role: UserRole.ORGANIZATION_ADMIN,
        },
      });

      if (orgAdmin) {
        // Send notification to the organization admin
        await NotificationService.createNotification({
          title: 'Fee Settings Updated',
          message: `Your organization's fee settings have been updated.`,
          type: 'SYSTEM',
          userId: orgAdmin.id,
          organizationId,
          actionUrl: `/dashboard/settings/fees`,
          actionLabel: 'View Fee Settings',
        });
      }

      logger.info(`Fee settings updated for organization ${organizationId} by admin ${adminId}`);

      return updatedOrganization;
    } catch (error) {
      logger.error('Error updating fee settings:', error);
      throw error;
    }
  }
}
