/**
 * Animation constants and utilities for the application
 * This file contains reusable animation variants for Framer Motion
 */

// Animation durations in seconds
export const DURATIONS = {
  fast: 0.15,
  medium: 0.3,
  slow: 0.5,
  extraSlow: 0.8,
};

// Animation easings
export const EASINGS = {
  // Standard easings
  easeOut: [0.16, 1, 0.3, 1],
  easeIn: [0.67, 0, 0.83, 0],
  easeInOut: [0.65, 0, 0.35, 1],

  // Spring-like easings
  spring: [0.34, 1.56, 0.64, 1],
  gentleSpring: [0.25, 1.2, 0.5, 1],

  // Custom easings
  bounce: [0.175, 0.885, 0.32, 1.275],
  gentle: [0.4, 0, 0.2, 1],
};

// Reusable animation variants for Framer Motion
export const VARIANTS = {
  // Fade animations
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: DURATIONS.medium, ease: EASINGS.easeOut } },
    exit: { opacity: 0, transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn } },
  },

  // Scale animations
  scaleIn: {
    initial: { opacity: 0, scale: 0.95 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  // Slide animations
  slideInFromBottom: {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      y: 20,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  slideInFromTop: {
    initial: { opacity: 0, y: -20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  slideInFromLeft: {
    initial: { opacity: 0, x: -20 },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      x: -20,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  slideInFromRight: {
    initial: { opacity: 0, x: 20 },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      x: 20,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  // Hover animations
  hoverScale: {
    initial: { scale: 1 },
    whileHover: {
      scale: 1.03,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.spring
      }
    },
  },

  hoverLift: {
    initial: { y: 0, boxShadow: '0 1px 3px rgba(0,0,0,0.1)' },
    whileHover: {
      y: -4,
      boxShadow: '0 8px 20px rgba(0,0,0,0.1)',
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
  },

  // Button animations
  buttonTap: {
    initial: { scale: 1 },
    whileTap: {
      scale: 0.98,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeOut
      }
    },
  },

  // List item animations (for staggered children)
  listItem: {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      y: 20,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  // Container for staggered children
  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
    exit: {
      transition: {
        staggerChildren: 0.03,
        staggerDirection: -1,
      },
    },
  },

  // Page transition animation
  pageTransition: {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.easeOut
      }
    },
    exit: {
      opacity: 0,
      y: 20,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
  },

  // Notification bell animation
  notificationBell: {
    initial: { rotate: 0 },
    animate: {
      rotate: [0, 15, -15, 10, -10, 5, -5, 0],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatDelay: 5,
      }
    },
    whileHover: {
      scale: 1.1,
      transition: { duration: DURATIONS.fast }
    },
    whileTap: {
      scale: 0.9,
      transition: { duration: DURATIONS.fast }
    },
  },

  // Pulse animation for badges and status indicators
  pulse: {
    animate: {
      scale: [1, 1.1, 1],
      opacity: [1, 0.8, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: "loop",
      },
    },
  },

  // Loading pulse animation
  loadingPulse: {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        repeatType: "loop",
      },
    },
  },

  // Success animation for checkmark
  successCheckmark: {
    initial: { pathLength: 0, opacity: 0 },
    animate: {
      pathLength: 1,
      opacity: 1,
      transition: {
        duration: DURATIONS.slow,
        ease: EASINGS.easeInOut
      }
    },
  },

  // Error animation for X mark
  errorXmark: {
    initial: { scale: 0, rotate: -90 },
    animate: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.easeOut
      }
    },
  },

  // Grid item animation
  gridItem: {
    initial: { opacity: 0, y: 10 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: DURATIONS.medium,
        ease: EASINGS.spring
      }
    },
    exit: {
      opacity: 0,
      y: 10,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeIn
      }
    },
    whileHover: {
      scale: 1.02,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.spring
      }
    },
    whileTap: {
      scale: 0.98,
      transition: {
        duration: DURATIONS.fast,
        ease: EASINGS.easeOut
      }
    },
  },
};

// Helper function to create staggered animation variants
export function createStaggerVariants(
  staggerDelay = 0.05,
  initialDelay = 0.1,
  exitStaggerDelay = 0.03
) {
  return {
    animate: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
    exit: {
      transition: {
        staggerChildren: exitStaggerDelay,
        staggerDirection: -1,
      },
    },
  };
}

// Helper function to combine animation variants
export function combineVariants(...variants: any[]) {
  return variants.reduce((acc, variant) => {
    return {
      ...acc,
      ...variant,
      initial: { ...acc.initial, ...variant.initial },
      animate: { ...acc.animate, ...variant.animate },
      exit: { ...acc.exit, ...variant.exit },
      whileHover: { ...acc.whileHover, ...variant.whileHover },
      whileTap: { ...acc.whileTap, ...variant.whileTap },
    };
  }, {});
}

// Staggered list variants for use in components
export const staggeredListVariants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1,
    },
  },
  exit: {
    transition: {
      staggerChildren: 0.03,
      staggerDirection: -1,
    },
  },
};

// Item variants for staggered lists
export const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: DURATIONS.medium,
      ease: EASINGS.spring,
    },
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: {
      duration: DURATIONS.fast,
      ease: EASINGS.easeIn,
    },
  },
};
