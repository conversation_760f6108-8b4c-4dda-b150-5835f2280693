import { NotificationType } from '@prisma/client';

/**
 * Email template data interface
 */
export interface EmailTemplateData {
  [key: string]: any;
}

/**
 * Email template interface
 */
export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

/**
 * Email templates service
 */
export class EmailTemplates {
  /**
   * Get email template for a notification
   * @param type Notification type
   * @param data Template data
   * @returns Email template
   */
  static getNotificationTemplate(type: NotificationType, data: EmailTemplateData): EmailTemplate {
    switch (type) {
      case 'SYSTEM':
        return this.systemNotificationTemplate(data);
      case 'ORDER':
        return this.orderNotificationTemplate(data);
      case 'TRANSACTION':
        return this.transactionNotificationTemplate(data);
      case 'CREDIT':
        return this.creditNotificationTemplate(data);
      case 'VERIFICATION':
        return this.verificationNotificationTemplate(data);
      case 'SUBSCRIPTION':
        return this.subscriptionNotificationTemplate(data);
      case 'PAYMENT':
        return this.paymentNotificationTemplate(data);
      case 'TEAM':
        return this.teamNotificationTemplate(data);
      case 'SECURITY':
        return this.securityNotificationTemplate(data);
      case 'MARKETPLACE':
        return this.marketplaceNotificationTemplate(data);
      case 'WALLET':
        return this.walletNotificationTemplate(data);
      default:
        return this.defaultTemplate(data);
    }
  }

  /**
   * System notification template
   * @param data Template data
   * @returns Email template
   */
  static systemNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, actionUrl, actionLabel } = data;

    return {
      subject: title || 'System Notification',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">${actionLabel || 'View Details'}</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\n${actionUrl ? `${actionLabel || 'View Details'}: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Order notification template
   * @param data Template data
   * @returns Email template
   */
  static orderNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, orderType, orderStatus, carbonCreditName, quantity, price, actionUrl } = data;

    return {
      subject: title || `Order ${orderStatus}: ${carbonCreditName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Order Type:</strong> ${orderType}</p>
            <p><strong>Status:</strong> ${orderStatus}</p>
            <p><strong>Carbon Credit:</strong> ${carbonCreditName}</p>
            <p><strong>Quantity:</strong> ${quantity}</p>
            <p><strong>Price:</strong> $${price}</p>
            <p><strong>Total:</strong> $${(quantity * price).toFixed(2)}</p>
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Order Details</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nOrder Type: ${orderType}\nStatus: ${orderStatus}\nCarbon Credit: ${carbonCreditName}\nQuantity: ${quantity}\nPrice: $${price}\nTotal: $${(quantity * price).toFixed(2)}\n\n${actionUrl ? `View Order Details: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Transaction notification template
   * @param data Template data
   * @returns Email template
   */
  static transactionNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, transactionType, amount, status, hash, actionUrl } = data;

    return {
      subject: title || `Transaction ${status}: ${transactionType}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Transaction Type:</strong> ${transactionType}</p>
            <p><strong>Amount:</strong> $${amount}</p>
            <p><strong>Status:</strong> ${status}</p>
            ${hash ? `<p><strong>Transaction Hash:</strong> ${hash}</p>` : ''}
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Transaction Details</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nTransaction Type: ${transactionType}\nAmount: $${amount}\nStatus: ${status}\n${hash ? `Transaction Hash: ${hash}\n` : ''}\n${actionUrl ? `View Transaction Details: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Carbon credit notification template
   * @param data Template data
   * @returns Email template
   */
  static creditNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, creditName, status, quantity, price, actionUrl } = data;

    return {
      subject: title || `Carbon Credit ${status}: ${creditName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Carbon Credit:</strong> ${creditName}</p>
            <p><strong>Status:</strong> ${status}</p>
            <p><strong>Quantity:</strong> ${quantity}</p>
            <p><strong>Price:</strong> $${price}</p>
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Carbon Credit</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nCarbon Credit: ${creditName}\nStatus: ${status}\nQuantity: ${quantity}\nPrice: $${price}\n\n${actionUrl ? `View Carbon Credit: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Verification notification template
   * @param data Template data
   * @returns Email template
   */
  static verificationNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, entityType, entityName, status, notes, actionUrl } = data;

    return {
      subject: title || `Verification ${status}: ${entityName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Entity Type:</strong> ${entityType}</p>
            <p><strong>Entity Name:</strong> ${entityName}</p>
            <p><strong>Status:</strong> ${status}</p>
            ${notes ? `<p><strong>Notes:</strong> ${notes}</p>` : ''}
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Details</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nEntity Type: ${entityType}\nEntity Name: ${entityName}\nStatus: ${status}\n${notes ? `Notes: ${notes}\n` : ''}\n${actionUrl ? `View Details: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Subscription notification template
   * @param data Template data
   * @returns Email template
   */
  static subscriptionNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, plan, status, startDate, endDate, amount, actionUrl } = data;

    return {
      subject: title || `Subscription ${status}: ${plan} Plan`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Plan:</strong> ${plan}</p>
            <p><strong>Status:</strong> ${status}</p>
            <p><strong>Start Date:</strong> ${new Date(startDate).toLocaleDateString()}</p>
            ${endDate ? `<p><strong>End Date:</strong> ${new Date(endDate).toLocaleDateString()}</p>` : ''}
            <p><strong>Amount:</strong> $${amount}</p>
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">Manage Subscription</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nPlan: ${plan}\nStatus: ${status}\nStart Date: ${new Date(startDate).toLocaleDateString()}\n${endDate ? `End Date: ${new Date(endDate).toLocaleDateString()}\n` : ''}Amount: $${amount}\n\n${actionUrl ? `Manage Subscription: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Payment notification template
   * @param data Template data
   * @returns Email template
   */
  static paymentNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, amount, status, paymentMethod, invoiceNumber, description, actionUrl } = data;

    return {
      subject: title || `Payment ${status}: $${amount}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Amount:</strong> $${amount}</p>
            <p><strong>Status:</strong> ${status}</p>
            <p><strong>Payment Method:</strong> ${paymentMethod}</p>
            ${invoiceNumber ? `<p><strong>Invoice Number:</strong> ${invoiceNumber}</p>` : ''}
            <p><strong>Description:</strong> ${description}</p>
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Payment Details</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nAmount: $${amount}\nStatus: ${status}\nPayment Method: ${paymentMethod}\n${invoiceNumber ? `Invoice Number: ${invoiceNumber}\n` : ''}Description: ${description}\n\n${actionUrl ? `View Payment Details: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Team notification template
   * @param data Template data
   * @returns Email template
   */
  static teamNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, teamName, action, user, role, actionUrl } = data;

    return {
      subject: title || `Team ${action}: ${teamName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Team:</strong> ${teamName}</p>
            <p><strong>Action:</strong> ${action}</p>
            ${user ? `<p><strong>User:</strong> ${user}</p>` : ''}
            ${role ? `<p><strong>Role:</strong> ${role}</p>` : ''}
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Team</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nTeam: ${teamName}\nAction: ${action}\n${user ? `User: ${user}\n` : ''}${role ? `Role: ${role}\n` : ''}\n${actionUrl ? `View Team: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Security notification template
   * @param data Template data
   * @returns Email template
   */
  static securityNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, eventType, ipAddress, location, device, timestamp, actionUrl } = data;

    return {
      subject: title || `Security Alert: ${eventType}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Event Type:</strong> ${eventType}</p>
            <p><strong>Time:</strong> ${new Date(timestamp).toLocaleString()}</p>
            ${ipAddress ? `<p><strong>IP Address:</strong> ${ipAddress}</p>` : ''}
            ${location ? `<p><strong>Location:</strong> ${location}</p>` : ''}
            ${device ? `<p><strong>Device:</strong> ${device}</p>` : ''}
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">Review Account Activity</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nEvent Type: ${eventType}\nTime: ${new Date(timestamp).toLocaleString()}\n${ipAddress ? `IP Address: ${ipAddress}\n` : ''}${location ? `Location: ${location}\n` : ''}${device ? `Device: ${device}\n` : ''}\n${actionUrl ? `Review Account Activity: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Marketplace notification template
   * @param data Template data
   * @returns Email template
   */
  static marketplaceNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, eventType, creditName, quantity, price, organization, actionUrl } = data;

    return {
      subject: title || `Marketplace Update: ${eventType}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Event Type:</strong> ${eventType}</p>
            <p><strong>Carbon Credit:</strong> ${creditName}</p>
            <p><strong>Quantity:</strong> ${quantity}</p>
            <p><strong>Price:</strong> $${price}</p>
            ${organization ? `<p><strong>Organization:</strong> ${organization}</p>` : ''}
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Marketplace</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nEvent Type: ${eventType}\nCarbon Credit: ${creditName}\nQuantity: ${quantity}\nPrice: $${price}\n${organization ? `Organization: ${organization}\n` : ''}\n${actionUrl ? `View Marketplace: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Wallet notification template
   * @param data Template data
   * @returns Email template
   */
  static walletNotificationTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, walletAddress, eventType, amount, token, network, actionUrl } = data;

    return {
      subject: title || `Wallet Update: ${eventType}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <p><strong>Wallet Address:</strong> ${walletAddress}</p>
            <p><strong>Event Type:</strong> ${eventType}</p>
            ${amount ? `<p><strong>Amount:</strong> ${amount} ${token || 'ETH'}</p>` : ''}
            ${network ? `<p><strong>Network:</strong> ${network}</p>` : ''}
          </div>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">View Wallet</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\nWallet Address: ${walletAddress}\nEvent Type: ${eventType}\n${amount ? `Amount: ${amount} ${token || 'ETH'}\n` : ''}${network ? `Network: ${network}\n` : ''}\n${actionUrl ? `View Wallet: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Default template
   * @param data Template data
   * @returns Email template
   */
  static defaultTemplate(data: EmailTemplateData): EmailTemplate {
    const { title, message, actionUrl, actionLabel } = data;

    return {
      subject: title || 'Notification',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          ${actionUrl ? `<a href="${actionUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">${actionLabel || 'View Details'}</a>` : ''}
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `${title}\n\n${message}\n\n${actionUrl ? `${actionLabel || 'View Details'}: ${actionUrl}` : ''}\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Welcome email template
   * @param data Template data
   * @returns Email template
   */
  static welcomeTemplate(data: EmailTemplateData): EmailTemplate {
    const { name, verificationUrl } = data;

    return {
      subject: 'Welcome to Carbonix',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Welcome to Carbonix!</h2>
          <p style="color: #666; font-size: 16px;">Hello ${name},</p>
          <p style="color: #666; font-size: 16px;">Thank you for joining Carbonix. Accelerate your sustainability goals with our secure, transparent carbon credit trading.</p>
          <p style="color: #666; font-size: 16px;">To get started, please verify your email address by clicking the button below:</p>
          <a href="${verificationUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">Verify Email</a>
          <p style="color: #666; font-size: 16px; margin-top: 30px;">If you have any questions, please don't hesitate to contact our support team.</p>
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `Welcome to Carbonix!\n\nHello ${name},\n\nThank you for joining Carbonix. Accelerate your sustainability goals with our secure, transparent carbon credit trading.\n\nTo get started, please verify your email address by clicking the link below:\n\n${verificationUrl}\n\nIf you have any questions, please don't hesitate to contact our support team.\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Password reset template
   * @param data Template data
   * @returns Email template
   */
  static passwordResetTemplate(data: EmailTemplateData): EmailTemplate {
    const { name, resetUrl } = data;

    return {
      subject: 'Reset Your Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Reset Your Password</h2>
          <p style="color: #666; font-size: 16px;">Hello ${name},</p>
          <p style="color: #666; font-size: 16px;">We received a request to reset your password. If you didn't make this request, you can ignore this email.</p>
          <p style="color: #666; font-size: 16px;">To reset your password, click the button below:</p>
          <a href="${resetUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">Reset Password</a>
          <p style="color: #666; font-size: 16px; margin-top: 30px;">This link will expire in 1 hour.</p>
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `Reset Your Password\n\nHello ${name},\n\nWe received a request to reset your password. If you didn't make this request, you can ignore this email.\n\nTo reset your password, click the link below:\n\n${resetUrl}\n\nThis link will expire in 1 hour.\n\nThis is an automated message from the Carbonix platform.`,
    };
  }

  /**
   * Invitation template
   * @param data Template data
   * @returns Email template
   */
  static invitationTemplate(data: EmailTemplateData): EmailTemplate {
    const { name, organizationName, inviterName, role, acceptUrl } = data;

    return {
      subject: `Invitation to Join ${organizationName} on Carbonix`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">You've Been Invited!</h2>
          <p style="color: #666; font-size: 16px;">Hello ${name},</p>
          <p style="color: #666; font-size: 16px;">${inviterName} has invited you to join ${organizationName} on Carbonix as a ${role}.</p>
          <p style="color: #666; font-size: 16px;">To accept this invitation, click the button below:</p>
          <a href="${acceptUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 20px;">Accept Invitation</a>
          <p style="color: #666; font-size: 16px; margin-top: 30px;">If you don't have an account, you'll be able to create one after accepting the invitation.</p>
          <p style="color: #999; font-size: 14px; margin-top: 30px;">This is an automated message from the Carbonix platform.</p>
        </div>
      `,
      text: `You've Been Invited!\n\nHello ${name},\n\n${inviterName} has invited you to join ${organizationName} on Carbonix as a ${role}.\n\nTo accept this invitation, click the link below:\n\n${acceptUrl}\n\nIf you don't have an account, you'll be able to create one after accepting the invitation.\n\nThis is an automated message from the Carbonix platform.`,
    };
  }
}
