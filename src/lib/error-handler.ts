import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { AuditLogType } from "@prisma/client";
import { db } from "@/lib/db";
// Import Prisma error classes
import { PrismaClientKnownRequestError } from "@/lib/prisma-errors";
import { getToken } from "next-auth/jwt";

/**
 * Error types
 */
export enum ErrorType {
  VALIDATION = "VALIDATION_ERROR",
  AUTHENTICATION = "AUTHENTICATION_ERROR",
  AUTHORIZATION = "AUTHORIZATION_ERROR",
  FORBIDDEN = "FORBIDDEN_ERROR",
  NOT_FOUND = "NOT_FOUND_ERROR",
  CONFLICT = "CONFLICT_ERROR",
  INTERNAL = "INTERNAL_ERROR",
  EXTERNAL = "EXTERNAL_SERVICE_ERROR",
  RATE_LIMIT = "RATE_LIMIT_ERROR",
  DATABASE = "DATABASE_ERROR",
  BLOCKCHAIN = "BLOCKCHAIN_ERROR",
  BUSINESS_LOGIC = "BUSINESS_LOGIC_ERROR",
}

/**
 * Custom API error class
 */
export class ApiError extends Error {
  type: ErrorType;
  statusCode: number;
  details?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.INTERNAL,
    statusCode: number = 500,
    details?: any
  ) {
    super(message);
    this.name = "ApiError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * Map error types to HTTP status codes
 */
const errorTypeToStatusCode: Record<ErrorType, number> = {
  [ErrorType.VALIDATION]: 400,
  [ErrorType.AUTHENTICATION]: 401,
  [ErrorType.AUTHORIZATION]: 403,
  [ErrorType.FORBIDDEN]: 403,
  [ErrorType.NOT_FOUND]: 404,
  [ErrorType.CONFLICT]: 409,
  [ErrorType.INTERNAL]: 500,
  [ErrorType.EXTERNAL]: 502,
  [ErrorType.RATE_LIMIT]: 429,
  [ErrorType.DATABASE]: 500,
  [ErrorType.BLOCKCHAIN]: 502,
  [ErrorType.BUSINESS_LOGIC]: 422,
};

/**
 * Map error types to user-friendly messages
 */
const errorTypeToMessage: Record<ErrorType, string> = {
  [ErrorType.VALIDATION]: "The provided data is invalid.",
  [ErrorType.AUTHENTICATION]: "Authentication is required to access this resource.",
  [ErrorType.AUTHORIZATION]: "You don't have permission to access this resource.",
  [ErrorType.FORBIDDEN]: "Access to this resource is forbidden.",
  [ErrorType.NOT_FOUND]: "The requested resource was not found.",
  [ErrorType.CONFLICT]: "The request conflicts with the current state of the resource.",
  [ErrorType.INTERNAL]: "An internal server error occurred. Please try again later.",
  [ErrorType.EXTERNAL]: "An error occurred while communicating with an external service.",
  [ErrorType.RATE_LIMIT]: "Too many requests. Please try again later.",
  [ErrorType.DATABASE]: "A database error occurred. Please try again later.",
  [ErrorType.BLOCKCHAIN]: "A blockchain error occurred. Please try again later.",
  [ErrorType.BUSINESS_LOGIC]: "The request could not be processed due to business logic constraints.",
};

/**
 * Format error response
 */
function formatErrorResponse(error: any, includeDetails: boolean = false) {
  // API errors (our custom error class)
  if (error instanceof ApiError) {
    return {
      error: error.message,
      type: error.type,
      code: error.statusCode,
      ...(error.details && includeDetails && { details: error.details }),
    };
  }

  // Validation errors (Zod)
  if (error instanceof z.ZodError) {
    return {
      error: "Validation error",
      type: ErrorType.VALIDATION,
      code: 400,
      ...(includeDetails && {
        details: error.errors.map((e) => ({
          path: e.path.join("."),
          message: e.message,
        })),
      }),
    };
  }

  // Database errors (Prisma)
  if (error instanceof PrismaClientKnownRequestError) {
    // Handle specific Prisma error codes
    if (error.code === "P2002") {
      // Unique constraint violation
      return {
        error: "The resource already exists.",
        type: ErrorType.CONFLICT,
        code: 409,
        ...(includeDetails && { details: { fields: error.meta?.target } }),
      };
    }

    if (error.code === "P2025") {
      // Record not found
      return {
        error: "The requested resource was not found.",
        type: ErrorType.NOT_FOUND,
        code: 404,
      };
    }

    // Generic database error
    return {
      error: "A database error occurred.",
      type: ErrorType.DATABASE,
      code: 500,
      ...(includeDetails && process.env.NODE_ENV !== "production" && { details: error.message }),
    };
  }

  // Standard JS errors
  if (error instanceof Error) {
    // Default to internal error
    return {
      error: process.env.NODE_ENV === "production" ? "An unexpected error occurred" : error.message,
      type: ErrorType.INTERNAL,
      code: 500,
      ...(includeDetails && process.env.NODE_ENV !== "production" && { stack: error.stack }),
    };
  }

  // Default error response for unknown error types
  return {
    error: "An unexpected error occurred",
    type: ErrorType.INTERNAL,
    code: 500,
  };
}

/**
 * Global error handler middleware
 */
export async function errorHandler(
  req: NextRequest,
  res: NextResponse,
  error: any
): Promise<NextResponse> {
  // Get the request details
  const path = req.nextUrl.pathname;
  const method = req.method;
  const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = req.headers.get('user-agent') || 'unknown';

  // Try to get the user ID from the token
  let userId: string | undefined;
  try {
    const token = await getToken({ req });
    userId = token?.id as string | undefined;
  } catch (tokenError) {
    // Ignore token errors
  }

  // Determine if this is a production environment
  const isProduction = process.env.NODE_ENV === "production";

  // Format the error response (include details in non-production environments)
  const errorResponse = formatErrorResponse(error, !isProduction);

  // Determine the status code
  let statusCode = 500;
  if (error instanceof ApiError) {
    statusCode = error.statusCode;
  } else if (error instanceof z.ZodError) {
    statusCode = 400;
  } else if (error instanceof PrismaClientKnownRequestError) {
    if (error.code === "P2002") statusCode = 409;
    if (error.code === "P2025") statusCode = 404;
  }

  // Log the error with appropriate level based on status code
  const logData = {
    path,
    method,
    ip,
    userId,
    statusCode,
    errorType: errorResponse.type,
    errorMessage: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
  };

  if (statusCode >= 500) {
    logger.error(`Server error (${statusCode}):`, logData);
  } else if (statusCode >= 400) {
    logger.warn(`Client error (${statusCode}):`, logData);
  }

  // Record the error in the audit log if we have a user ID
  try {
    if (userId) {
      await db.auditLog.create({
        data: {
          type: AuditLogType.API_ERROR,
          description: `API Error: ${errorResponse.error}`,
          userId,
          metadata: {
            path,
            method,
            statusCode,
            errorType: errorResponse.type,
          },
          ipAddress: ip,
          userAgent,
        },
      });
    }
  } catch (auditError) {
    // Don't let audit logging errors affect the response
    logger.error("Failed to log error to audit log", auditError);
  }

  // Return the error response
  return NextResponse.json(errorResponse, { status: statusCode });
}

/**
 * Wrap an API handler with error handling
 */
export function withErrorHandling(handler: Function) {
  return async (req: NextRequest, ...args: any[]) => {
    try {
      return await handler(req, ...args);
    } catch (error) {
      return errorHandler(req, NextResponse.next(), error);
    }
  };
}

/**
 * Create a validation error from a Zod error
 * @param error The Zod error
 * @returns An ApiError with validation details
 */
export function createValidationError(error: z.ZodError) {
  return new ApiError(
    "Validation error",
    ErrorType.VALIDATION,
    400,
    error.errors.map((e) => ({
      path: e.path.join("."),
      message: e.message,
    }))
  );
}

/**
 * Validate a request body against a Zod schema
 * @param schema The Zod schema
 * @param data The data to validate
 * @returns The validated data
 * @throws ApiError if validation fails
 */
export function validateRequest<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw createValidationError(error);
    }
    throw error;
  }
}

/**
 * Create a not found error
 * @param resourceType The type of resource that was not found
 * @param id The ID of the resource
 * @returns An ApiError for the not found resource
 */
export function createNotFoundError(resourceType: string, id?: string) {
  const message = id
    ? `${resourceType} with ID ${id} not found`
    : `${resourceType} not found`;

  return new ApiError(message, ErrorType.NOT_FOUND, 404);
}

/**
 * Create an unauthorized error
 * @param message Custom error message
 * @returns An ApiError for unauthorized access
 */
export function createUnauthorizedError(message?: string) {
  return new ApiError(
    message || "You are not authorized to access this resource",
    ErrorType.AUTHORIZATION,
    403
  );
}

/**
 * Create an unauthenticated error
 * @param message Custom error message
 * @returns An ApiError for unauthenticated access
 */
export function createUnauthenticatedError(message?: string) {
  return new ApiError(
    message || "Authentication required",
    ErrorType.AUTHENTICATION,
    401
  );
}
