import { Network } from "alchemy-sdk";

/**
 * Supported blockchain networks
 */
export enum SupportedNetwork {
  ETHEREUM = "ethereum",
  POLYGON = "polygon",
  ARBITRUM = "arbitrum",
  OPTIMISM = "optimism",
  BASE = "base",
}

/**
 * Network configuration for each supported network
 */
export interface NetworkConfig {
  name: string;
  chainId: number;
  alchemyNetwork: Network;
  rpcUrl: string;
  blockExplorer: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  isTestnet: boolean;
}

/**
 * Configuration for all supported networks
 */
export const NETWORK_CONFIG: Record<SupportedNetwork, NetworkConfig> = {
  [SupportedNetwork.ETHEREUM]: {
    name: "Ethereum",
    chainId: 1,
    alchemyNetwork: Network.ETH_MAINNET,
    rpcUrl: `https://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://etherscan.io",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: false,
  },
  [SupportedNetwork.POLYGON]: {
    name: "Polygon",
    chainId: 137,
    alchemyNetwork: Network.MATIC_MAINNET,
    rpcUrl: `https://polygon-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://polygonscan.com",
    nativeCurrency: {
      name: "MATIC",
      symbol: "MATIC",
      decimals: 18,
    },
    isTestnet: false,
  },
  [SupportedNetwork.ARBITRUM]: {
    name: "Arbitrum",
    chainId: 42161,
    alchemyNetwork: Network.ARB_MAINNET,
    rpcUrl: `https://arb-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://arbiscan.io",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: false,
  },
  [SupportedNetwork.OPTIMISM]: {
    name: "Optimism",
    chainId: 10,
    alchemyNetwork: Network.OPT_MAINNET,
    rpcUrl: `https://opt-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://optimistic.etherscan.io",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: false,
  },
  [SupportedNetwork.BASE]: {
    name: "Base",
    chainId: 8453,
    alchemyNetwork: Network.BASE_MAINNET,
    rpcUrl: `https://base-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://basescan.org",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: false,
  },
};

/**
 * Testnet configurations
 */
export const TESTNET_CONFIG: Record<SupportedNetwork, NetworkConfig> = {
  [SupportedNetwork.ETHEREUM]: {
    name: "Sepolia",
    chainId: 11155111,
    alchemyNetwork: Network.ETH_SEPOLIA,
    rpcUrl: `https://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://sepolia.etherscan.io",
    nativeCurrency: {
      name: "Sepolia Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: true,
  },
  [SupportedNetwork.POLYGON]: {
    name: "Mumbai",
    chainId: 80001,
    alchemyNetwork: Network.MATIC_MUMBAI,
    rpcUrl: `https://polygon-mumbai.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://mumbai.polygonscan.com",
    nativeCurrency: {
      name: "MATIC",
      symbol: "MATIC",
      decimals: 18,
    },
    isTestnet: true,
  },
  [SupportedNetwork.ARBITRUM]: {
    name: "Arbitrum Sepolia",
    chainId: 421614,
    alchemyNetwork: Network.ARB_SEPOLIA,
    rpcUrl: `https://arb-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://sepolia.arbiscan.io",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: true,
  },
  [SupportedNetwork.OPTIMISM]: {
    name: "Optimism Sepolia",
    chainId: 11155420,
    alchemyNetwork: Network.OPT_SEPOLIA,
    rpcUrl: `https://opt-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://sepolia-optimism.etherscan.io",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: true,
  },
  [SupportedNetwork.BASE]: {
    name: "Base Sepolia",
    chainId: 84532,
    alchemyNetwork: Network.BASE_SEPOLIA,
    rpcUrl: `https://base-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    blockExplorer: "https://sepolia.basescan.org",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18,
    },
    isTestnet: true,
  },
};

/**
 * Get network configuration based on network name and environment
 * @param network Network name
 * @param useTestnet Whether to use testnet
 * @returns Network configuration
 */
export function getNetworkConfig(network: SupportedNetwork, useTestnet = true): NetworkConfig {
  return useTestnet ? TESTNET_CONFIG[network] : NETWORK_CONFIG[network];
}

/**
 * Get block explorer URL for a transaction
 * @param txHash Transaction hash
 * @param network Network name
 * @param useTestnet Whether to use testnet
 * @returns Block explorer URL
 */
export function getExplorerUrl(txHash: string, network: SupportedNetwork, useTestnet = true): string {
  const config = getNetworkConfig(network, useTestnet);
  return `${config.blockExplorer}/tx/${txHash}`;
}

/**
 * Get block explorer URL for an address
 * @param address Wallet address
 * @param network Network name
 * @param useTestnet Whether to use testnet
 * @returns Block explorer URL
 */
export function getAddressExplorerUrl(address: string, network: SupportedNetwork, useTestnet = true): string {
  const config = getNetworkConfig(network, useTestnet);
  return `${config.blockExplorer}/address/${address}`;
}

/**
 * Default network to use
 */
export const DEFAULT_NETWORK = SupportedNetwork.ETHEREUM;

/**
 * Whether to use testnet by default
 */
export const USE_TESTNET = process.env.USE_TESTNET === "true" || process.env.NODE_ENV !== "production";

/**
 * Map network types to Alchemy SDK network configurations
 * @param networkType The blockchain network type
 * @param testMode Whether to use testnet or mainnet
 * @returns The appropriate Alchemy Network configuration
 */
export function getAlchemyNetworkByType(networkType: string, testMode: boolean): Network {
  const networkMap: Record<string, { mainnet: Network; testnet: Network }> = {
    ETHEREUM: {
      mainnet: Network.ETH_MAINNET,
      testnet: Network.ETH_SEPOLIA,
    },
    POLYGON: {
      mainnet: Network.MATIC_MAINNET,
      testnet: Network.MATIC_MUMBAI,
    },
    ARBITRUM: {
      mainnet: Network.ARB_MAINNET,
      testnet: Network.ARB_GOERLI,
    },
    OPTIMISM: {
      mainnet: Network.OPT_MAINNET,
      testnet: Network.OPT_GOERLI,
    },
    BASE: {
      mainnet: Network.BASE_MAINNET,
      testnet: Network.BASE_GOERLI,
    },
  };

  const network = networkMap[networkType] || networkMap.ETHEREUM;
  return testMode ? network.testnet : network.mainnet;
}

/**
 * Get the blockchain explorer URL for a given network
 * @param networkType The blockchain network type
 * @param testMode Whether to use testnet or mainnet
 * @returns The block explorer URL base
 */
export function getBlockExplorerUrl(networkType: string, testMode: boolean): string {
  const explorerMap: Record<string, { mainnet: string; testnet: string }> = {
    ETHEREUM: {
      mainnet: "https://etherscan.io",
      testnet: "https://sepolia.etherscan.io",
    },
    POLYGON: {
      mainnet: "https://polygonscan.com",
      testnet: "https://mumbai.polygonscan.com",
    },
    ARBITRUM: {
      mainnet: "https://arbiscan.io",
      testnet: "https://goerli.arbiscan.io",
    },
    OPTIMISM: {
      mainnet: "https://optimistic.etherscan.io",
      testnet: "https://goerli-optimism.etherscan.io",
    },
    BASE: {
      mainnet: "https://basescan.org",
      testnet: "https://goerli.basescan.org",
    },
  };

  const explorer = explorerMap[networkType] || explorerMap.ETHEREUM;
  return testMode ? explorer.testnet : explorer.mainnet;
}

/**
 * Get the blockchain RPC URL for a given network
 * Note: In a real application, you would likely use environment variables for these URLs
 * @param networkType The blockchain network type
 * @param testMode Whether to use testnet or mainnet
 * @returns The RPC URL for the network
 */
export function getRpcUrl(networkType: string, testMode: boolean): string {
  // In production, these would be actual RPC URLs with API keys
  const placeholder = "https://example-rpc-url.com";

  return `${placeholder}/${networkType.toLowerCase()}/${testMode ? 'testnet' : 'mainnet'}`;
}

/**
 * Get the configuration for creating an Alchemy client
 * @param apiKey The Alchemy API key
 * @param networkType The blockchain network type
 * @param testMode Whether to use testnet or mainnet
 * @returns Configuration object for Alchemy SDK
 */
export function getAlchemyConfig(apiKey: string, networkType: string, testMode: boolean) {
  return {
    apiKey,
    network: getAlchemyNetworkByType(networkType, testMode),
  };
}

/**
 * Get bridge providers for a network pair
 * @param sourceNetwork Source network
 * @param destinationNetwork Destination network
 * @returns Array of available bridge providers
 */
export function getBridgeProviders(sourceNetwork: SupportedNetwork, destinationNetwork: SupportedNetwork): string[] {
  // This is a simplified implementation
  // In a real-world scenario, this would be more complex and consider specific network pairs

  const allProviders = ["Connext", "Hop", "Across", "Synapse", "Stargate"];

  // Example logic for specific network pairs
  if (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.POLYGON) {
    return ["Connext", "Hop", "Across"];
  }

  if (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.ARBITRUM) {
    return ["Connext", "Hop", "Across"];
  }

  if (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.OPTIMISM) {
    return ["Connext", "Hop", "Across"];
  }

  if (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.BASE) {
    return ["Connext", "Across"];
  }

  // Default to all providers for other combinations
  return allProviders;
}

/**
 * Estimate bridge fee for a network pair
 * @param sourceNetwork Source network
 * @param destinationNetwork Destination network
 * @param amount Amount to bridge
 * @param bridgeProvider Bridge provider
 * @returns Estimated fee as a percentage
 */
export function estimateBridgeFee(
  sourceNetwork: SupportedNetwork,
  destinationNetwork: SupportedNetwork,
  amount: number,
  bridgeProvider: string
): number {
  // This is a simplified implementation
  // In a real-world scenario, this would call APIs or use more complex logic

  // Base fee percentage
  let feePercentage = 0.3; // 0.3%

  // Adjust based on provider
  if (bridgeProvider === "Hop") {
    feePercentage = 0.25;
  } else if (bridgeProvider === "Across") {
    feePercentage = 0.35;
  } else if (bridgeProvider === "Connext") {
    feePercentage = 0.2;
  }

  // Adjust based on network pair
  if (
    (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.POLYGON) ||
    (sourceNetwork === SupportedNetwork.POLYGON && destinationNetwork === SupportedNetwork.ETHEREUM)
  ) {
    feePercentage += 0.05;
  }

  // Adjust based on amount (lower percentage for higher amounts)
  if (amount > 10000) {
    feePercentage *= 0.8; // 20% discount
  } else if (amount > 1000) {
    feePercentage *= 0.9; // 10% discount
  }

  return feePercentage;
}

/**
 * Get estimated bridge time in minutes
 * @param sourceNetwork Source network
 * @param destinationNetwork Destination network
 * @param bridgeProvider Bridge provider
 * @returns Estimated time in minutes
 */
export function estimateBridgeTime(
  sourceNetwork: SupportedNetwork,
  destinationNetwork: SupportedNetwork,
  bridgeProvider: string
): number {
  // This is a simplified implementation
  // In a real-world scenario, this would be more complex

  // Base time in minutes
  let timeMinutes = 15;

  // Adjust based on provider
  if (bridgeProvider === "Hop") {
    timeMinutes = 10;
  } else if (bridgeProvider === "Across") {
    timeMinutes = 20;
  } else if (bridgeProvider === "Connext") {
    timeMinutes = 15;
  }

  // Adjust based on network pair
  if (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.OPTIMISM) {
    timeMinutes = 5; // Optimism is fast from Ethereum
  }

  if (sourceNetwork === SupportedNetwork.ETHEREUM && destinationNetwork === SupportedNetwork.ARBITRUM) {
    timeMinutes = 8; // Arbitrum is relatively fast from Ethereum
  }

  if (
    (sourceNetwork === SupportedNetwork.POLYGON && destinationNetwork === SupportedNetwork.ETHEREUM) ||
    (sourceNetwork === SupportedNetwork.ARBITRUM && destinationNetwork === SupportedNetwork.ETHEREUM) ||
    (sourceNetwork === SupportedNetwork.OPTIMISM && destinationNetwork === SupportedNetwork.ETHEREUM) ||
    (sourceNetwork === SupportedNetwork.BASE && destinationNetwork === SupportedNetwork.ETHEREUM)
  ) {
    timeMinutes *= 1.5; // Bridging to Ethereum typically takes longer
  }

  return Math.round(timeMinutes);
}
