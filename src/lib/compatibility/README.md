# Compatibility Layer

This directory contains compatibility modules that re-export the new module structure with the old import paths. This is provided to ease the transition to the new module structure.

## Deprecated Modules

The following modules are deprecated and should not be used in new code:

- `analytics-service.ts` → Use `@/lib/analytics` instead
- `blockchain-client.ts` → Use `@/lib/blockchain` instead
- `gas-estimation.ts` → Use `@/lib/blockchain/gas` instead
- `gas-optimizer.ts` → Use `@/lib/blockchain/gas` instead
- `carbon-credit-service.ts` → Use `@/lib/carbon-credits` instead
- `notification-service.ts` → Use `@/lib/notifications` instead
- `order-service.ts` → Use `@/lib/orders` instead
- `marketplace-service.ts` → Use `@/lib/marketplace` instead
- `audit-service.ts` → Use `@/lib/audit` instead
- `payment-service.ts` → Use `@/lib/payments` instead

## Migration Guide

To migrate from the old import paths to the new ones, follow these steps:

1. Find all imports from the old modules:

```bash
grep -r "from '@/lib/analytics-service" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/blockchain-client" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/gas-estimation" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/gas-optimizer" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/carbon-credit-service" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/notification-service" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/order-service" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/marketplace-service" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/audit-service" --include="*.ts" --include="*.tsx"
grep -r "from '@/lib/payment-service" --include="*.ts" --include="*.tsx"
```

2. Replace the imports with the new ones:

```typescript
// Old
import analyticsService from '@/lib/analytics-service';
// New
import { analyticsService } from '@/lib/analytics';

// Old
import { BlockchainClient } from '@/lib/blockchain-client';
// New
import { BlockchainClient } from '@/lib/blockchain';

// Old
import { gasEstimationService } from '@/lib/gas-estimation';
// New
import { blockchainService } from '@/lib/blockchain';
const gasService = blockchainService.getGasService();

// Old
import { gasOptimizerService } from '@/lib/gas-optimizer';
// New
import { blockchainService } from '@/lib/blockchain';
const gasService = blockchainService.getGasService();

// Old
import carbonCreditService from '@/lib/carbon-credit-service';
// New
import { carbonCreditManager } from '@/lib/carbon-credits';

// Old
import notificationService from '@/lib/notification-service';
// New
import { notificationService } from '@/lib/notifications';

// Old
import orderService from '@/lib/order-service';
// New
import { orderManager } from '@/lib/orders';

// Old
import marketplaceService from '@/lib/marketplace-service';
// New
import { marketplaceManager } from '@/lib/marketplace';

// Old
import auditService from '@/lib/audit-service';
// New
import { auditManager } from '@/lib/audit';

// Old
import paymentService from '@/lib/payment-service';
// New
import { paymentManager } from '@/lib/payments';
```

## Timeline

This compatibility layer will be removed in a future version. It is recommended to migrate to the new import paths as soon as possible.
