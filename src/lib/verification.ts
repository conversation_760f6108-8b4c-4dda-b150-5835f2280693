import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { tokenizeCarbonCredit, getCarbonCreditBalance } from "@/lib/blockchain";

/**
 * Verification status
 */
export enum VerificationStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  REVOKED = "REVOKED",
}

/**
 * Verification type
 */
export enum VerificationType {
  INITIAL = "INITIAL",
  ANNUAL = "ANNUAL",
  METHODOLOGY = "METHODOLOGY",
  OWNERSHIP = "OWNERSHIP",
  RETIREMENT = "RETIREMENT",
  TOKENIZATION = "TOKENIZATION",
  BATCH = "BATCH",
}

/**
 * Verification request
 */
export interface VerificationRequest {
  carbonCreditId: string;
  type: VerificationType;
  documents: string[];
  notes?: string;
  userId: string;
  organizationId: string;
  metadata?: Record<string, any>;
}

/**
 * Batch verification request interface
 */
export interface BatchVerificationRequest {
  carbonCreditIds: string[];
  type: VerificationType;
  documents: string[];
  notes?: string;
  userId: string;
  organizationId: string;
  metadata?: Record<string, any>;
}

/**
 * Tokenization request interface
 */
export interface TokenizationRequest {
  carbonCreditId: string;
  network: SupportedNetwork;
  useTestnet: boolean;
  userId: string;
  organizationId: string;
  metadata?: Record<string, any>;
}

/**
 * Verification service for carbon credit verification and certification
 */
export class VerificationService {
  /**
   * Request verification for a carbon credit
   * @param request Verification request
   * @returns Created verification
   */
  async requestVerification(request: VerificationRequest) {
    try {
      logger.info(`Requesting ${request.type} verification for carbon credit ${request.carbonCreditId}`);

      // Check if carbon credit exists and belongs to the organization
      const carbonCredit = await db.carbonCredit.findFirst({
        where: {
          id: request.carbonCreditId,
          organizationId: request.organizationId,
        },
      });

      if (!carbonCredit) {
        throw new Error("Carbon credit not found or does not belong to the organization");
      }

      // Create verification request
      const verification = await db.carbonCreditVerification.create({
        data: {
          status: VerificationStatus.PENDING,
          verifier: null,
          verifierEmail: null,
          notes: request.notes,
          metadata: request.metadata ? request.metadata : {},
          carbonCredit: {
            connect: { id: request.carbonCreditId },
          },
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: "VERIFICATION_REQUESTED" as any, // TODO: Update to use proper enum value
          description: `Verification requested for carbon credit ${carbonCredit.name}`,
          user: {
            connect: { id: request.userId },
          },
          organization: {
            connect: { id: request.organizationId },
          },
          metadata: {
            carbonCreditId: request.carbonCreditId,
            verificationType: request.type,
            verificationId: verification.id,
          },
        },
      });

      // Update carbon credit status if it's an initial verification
      if (request.type === VerificationType.INITIAL && carbonCredit.status === "PENDING") {
        await db.carbonCredit.update({
          where: { id: request.carbonCreditId },
          data: {
            verificationStatus: "PENDING",
          },
        });
      }

      // Log audit event
      await auditService.log(
        "VERIFICATION_REQUESTED",
        `Verification requested: ${request.type} for carbon credit ${carbonCredit.name}`,
        {
          verificationId: verification.id,
          carbonCreditId: request.carbonCreditId,
          type: request.type,
        },
        request.userId,
        request.organizationId
      );

      // Notify platform admins
      const admins = await db.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      });

      for (const admin of admins) {
        await notificationService.createNotification(
          admin.id,
          "New Verification Request",
          `A new ${request.type} verification has been requested for carbon credit ${carbonCredit.name} by ${request.organizationId}`,
          "CREDIT",
          [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
          {
            verificationId: verification.id,
            carbonCreditId: request.carbonCreditId,
            type: request.type,
          }
        );
      }

      return verification;
    } catch (error) {
      logger.error("Error requesting verification:", error);
      throw new Error(`Failed to request verification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Request batch verification for multiple carbon credits
   * @param request Batch verification request
   * @returns Array of created verifications
   */
  async requestBatchVerification(request: BatchVerificationRequest) {
    try {
      logger.info(`Requesting batch ${request.type} verification for ${request.carbonCreditIds.length} carbon credits`);

      // Check if carbon credits exist and belong to the organization
      const carbonCredits = await db.carbonCredit.findMany({
        where: {
          id: { in: request.carbonCreditIds },
          organizationId: request.organizationId,
        },
      });

      if (carbonCredits.length !== request.carbonCreditIds.length) {
        throw new Error("One or more carbon credits not found or do not belong to the organization");
      }

      // Create verification requests for each carbon credit
      const verifications = [];

      for (const carbonCredit of carbonCredits) {
        // Create verification request
        const verification = await db.carbonCreditVerification.create({
          data: {
            status: VerificationStatus.PENDING,
            verifier: null,
            verifierEmail: null,
            notes: request.notes,
            metadata: request.metadata ? request.metadata : {},
            carbonCredit: {
              connect: { id: carbonCredit.id },
            },
          },
        });

        verifications.push(verification);

        // Update carbon credit status if it's an initial verification
        if (request.type === VerificationType.INITIAL && carbonCredit.status === "PENDING") {
          await db.carbonCredit.update({
            where: { id: carbonCredit.id },
            data: {
              verificationStatus: "PENDING",
            },
          });
        }
      }

      // Create audit log for batch verification
      await db.auditLog.create({
        data: {
          type: "BATCH_VERIFICATION_REQUESTED" as any, // TODO: Update to use proper enum value
          description: `Batch verification requested for ${carbonCredits.length} carbon credits`,
          user: {
            connect: { id: request.userId },
          },
          organization: {
            connect: { id: request.organizationId },
          },
          metadata: {
            carbonCreditIds: request.carbonCreditIds,
            verificationType: request.type,
            verificationIds: verifications.map(v => v.id),
          },
        },
      });

      // Log audit event
      await auditService.log(
        "BATCH_VERIFICATION_REQUESTED",
        `Batch verification requested: ${request.type} for ${carbonCredits.length} carbon credits`,
        request.userId,
        request.organizationId
      );

      // Notify platform admins
      const platformAdmins = await db.user.findMany({
        where: {
          role: "ADMIN",
        },
      });

      for (const admin of platformAdmins) {
        await notificationService.createNotification(
          admin.id,
          "Batch Verification Request",
          `A batch verification request has been submitted for ${carbonCredits.length} carbon credits.`,
          "VERIFICATION",
          [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
          {
            priority: "HIGH",
            actionUrl: `/admin/verifications/batch/${verifications[0].id}`,
          }
        );
      }

      return verifications;
    } catch (error) {
      logger.error("Error requesting batch verification:", error);
      throw new Error(`Failed to request batch verification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Update verification status
   * @param verificationId Verification ID
   * @param status New status
   * @param notes Notes
   * @param adminId Admin user ID
   * @returns Updated verification
   */
  async updateVerificationStatus(
    verificationId: string,
    status: VerificationStatus,
    notes: string,
    adminId: string
  ) {
    try {
      logger.info(`Updating verification ${verificationId} status to ${status}`);

      // Get verification details
      const verification = await db.carbonCreditVerification.findUnique({
        where: { id: verificationId },
        include: {
          carbonCredit: true,
          requestedBy: true,
          organization: true,
        },
      });

      if (!verification) {
        throw new Error("Verification not found");
      }

      // Update verification status
      const updatedVerification = await db.carbonCreditVerification.update({
        where: { id: verificationId },
        data: {
          status,
          adminNotes: notes,
          verifiedBy: {
            connect: { id: adminId },
          },
          verifiedAt: new Date(),
        },
      });

      // Update carbon credit status if it's an initial verification
      if (verification.type === VerificationType.INITIAL) {
        const newCreditStatus = status === VerificationStatus.APPROVED
          ? "VERIFIED"
          : "PENDING";

        await db.carbonCredit.update({
          where: { id: verification.carbonCreditId },
          data: {
            status: newCreditStatus,
            verificationStatus: status as any, // TODO: Fix type mismatch
          },
        });
      }

      // Log audit event
      await auditService.log(
        "VERIFICATION_UPDATED" as any, // TODO: Add to AuditLogType enum
        `Verification ${status}: ${verification.type} for carbon credit ${verification.carbonCredit.name}`,
        {
          verificationId,
          carbonCreditId: verification.carbonCreditId,
          type: verification.type,
          status,
        },
        adminId
      );

      // Notify the requester
      await notificationService.createNotification(
        verification.requestedById,
        `Verification ${status === VerificationStatus.APPROVED ? "Approved" : "Rejected"}`,
        `Your ${verification.type} verification request for carbon credit ${verification.carbonCredit.name} has been ${status === VerificationStatus.APPROVED ? "approved" : "rejected"}.${notes ? ` Notes: ${notes}` : ""}`,
        "CREDIT",
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          verificationId,
          carbonCreditId: verification.carbonCreditId,
          type: verification.type,
          status,
        }
      );

      return updatedVerification;
    } catch (error) {
      logger.error("Error updating verification status:", error);
      throw new Error(`Failed to update verification status: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get verifications for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @returns Verifications
   */
  async getVerifications(carbonCreditId: string) {
    try {
      const verifications = await db.carbonCreditVerification.findMany({
        where: { carbonCreditId },
        include: {
          requestedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifiedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return verifications;
    } catch (error) {
      logger.error("Error getting verifications:", error);
      throw new Error(`Failed to get verifications: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get pending verifications for admin review
   * @param limit Maximum number of verifications to return
   * @param offset Offset for pagination
   * @returns Pending verifications
   */
  async getPendingVerifications(limit: number = 20, offset: number = 0) {
    try {
      const verifications = await db.carbonCreditVerification.findMany({
        where: { status: VerificationStatus.PENDING },
        include: {
          carbonCredit: true,
          requestedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "asc",
        },
        take: limit,
        skip: offset,
      });

      const totalCount = await db.carbonCreditVerification.count({
        where: { status: VerificationStatus.PENDING },
      });

      return {
        verifications,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount,
        },
      };
    } catch (error) {
      logger.error("Error getting pending verifications:", error);
      throw new Error(`Failed to get pending verifications: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get verification details
   * @param verificationId Verification ID
   * @returns Verification details
   */
  async getVerificationDetails(verificationId: string) {
    try {
      const verification = await db.carbonCreditVerification.findUnique({
        where: { id: verificationId },
        include: {
          carbonCredit: true,
          requestedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifiedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!verification) {
        throw new Error("Verification not found");
      }

      return verification;
    } catch (error) {
      logger.error("Error getting verification details:", error);
      throw new Error(`Failed to get verification details: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Request tokenization for a carbon credit
   * @param request Tokenization request
   * @returns Tokenization result
   */
  async requestTokenization(request: TokenizationRequest) {
    try {
      logger.info(`Requesting tokenization for carbon credit ${request.carbonCreditId} on ${request.network} ${request.useTestnet ? 'testnet' : 'mainnet'}`);

      // Check if carbon credit exists and belongs to the organization
      const carbonCredit = await db.carbonCredit.findFirst({
        where: {
          id: request.carbonCreditId,
          organizationId: request.organizationId,
          status: "VERIFIED", // Only verified carbon credits can be tokenized
        },
        include: {
          organization: {
            include: {
              wallets: true,
            },
          },
        },
      });

      if (!carbonCredit) {
        throw new Error("Carbon credit not found, does not belong to the organization, or is not verified");
      }

      // Check if the organization has a wallet
      if (!carbonCredit.organization?.wallets?.[0]) {
        throw new Error("Organization does not have a wallet");
      }

      // Generate a token ID based on the carbon credit ID
      // This is a simple hash function to generate a unique token ID
      const tokenId = parseInt(request.carbonCreditId.substring(0, 8), 16) % 1000000000;

      // Create metadata for the token
      const metadata = {
        projectId: carbonCredit.projectId || carbonCredit.id,
        vintage: carbonCredit.vintage,
        standard: carbonCredit.standard,
        methodology: carbonCredit.methodology,
        uri: `https://carbon-exchange.com/api/carbon-credits/${carbonCredit.id}/metadata`,
      };

      // Tokenize the carbon credit
      const tokenizationResult = await tokenizeCarbonCredit(
        carbonCredit.id,
        tokenId,
        carbonCredit.quantity,
        metadata,
        carbonCredit.organization.wallets[0].address,
        carbonCredit.organization.wallets[0].encryptedKey,
        request.network,
        request.useTestnet
      );

      // Create a tokenization record using our new Tokenization model
      const tokenization = await (db as any).tokenization.create({
        data: {
          tokenId: tokenId.toString(),
          amount: carbonCredit.quantity,
          network: request.network,
          chainId: tokenizationResult.chainId,
          contractAddress: tokenizationResult.contractAddress,
          transactionHash: tokenizationResult.transactionHash,
          status: tokenizationResult.status === "success" ? "COMPLETED" : "FAILED",
          metadata: request.metadata || {},
          carbonCredit: {
            connect: { id: request.carbonCreditId },
          },
          organization: {
            connect: { id: request.organizationId },
          },
          user: {
            connect: { id: request.userId },
          },
        },
      });

      // Update carbon credit status if tokenization was successful
      if (tokenizationResult.status === "success") {
        await (db.carbonCredit.update as any)({
          where: { id: request.carbonCreditId },
          data: {
            status: "TOKENIZED", // Now using our new enum value
            tokenId: tokenId.toString(),
            tokenAddress: tokenizationResult.contractAddress,
            tokenNetwork: request.network,
            tokenChainId: tokenizationResult.chainId,
          },
        });
      }

      // Create audit log
      await db.auditLog.create({
        data: {
          type: "CARBON_CREDIT_TOKENIZED",
          description: `Carbon credit ${carbonCredit.name} tokenized on ${request.network} ${request.useTestnet ? 'testnet' : 'mainnet'}`,
          user: {
            connect: { id: request.userId },
          },
          organization: {
            connect: { id: request.organizationId },
          },
          metadata: {
            carbonCreditId: request.carbonCreditId,
            tokenId: tokenId,
            network: request.network,
            chainId: tokenizationResult.chainId,
            transactionHash: tokenizationResult.transactionHash,
            status: tokenizationResult.status,
          },
        },
      });

      // Log audit event
      await auditService.log(
        "TOKENIZATION_REQUESTED",
        `Tokenization requested for carbon credit ${carbonCredit.name} on ${request.network} ${request.useTestnet ? 'testnet' : 'mainnet'}`,
        request.userId,
        request.organizationId
      );

      // Notify the organization admin
      const organizationAdmins = await db.user.findMany({
        where: {
          organizationId: request.organizationId,
          role: "ORGANIZATION_ADMIN",
        },
      });

      for (const admin of organizationAdmins) {
        await notificationService.createNotification(
          admin.id,
          "Carbon Credit Tokenized",
          `Carbon credit ${carbonCredit.name} has been tokenized on ${request.network} ${request.useTestnet ? 'testnet' : 'mainnet'}.`,
          "CREDIT" as any, // TODO: Add TOKENIZATION to NotificationType enum
          [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
          {
            carbonCreditId: request.carbonCreditId,
            tokenId: tokenId,
            network: request.network,
            chainId: tokenizationResult.chainId,
            transactionHash: tokenizationResult.transactionHash,
            status: tokenizationResult.status,
            blockExplorerUrl: `${tokenizationResult.network === SupportedNetwork.ETHEREUM ? 'https://etherscan.io' :
              tokenizationResult.network === SupportedNetwork.POLYGON ? 'https://polygonscan.com' :
              tokenizationResult.network === SupportedNetwork.ARBITRUM ? 'https://arbiscan.io' :
              tokenizationResult.network === SupportedNetwork.OPTIMISM ? 'https://optimistic.etherscan.io' :
              'https://basescan.org'}/tx/${tokenizationResult.transactionHash}`,
          }
        );
      }

      return {
        tokenization,
        tokenizationResult,
      };
    } catch (error) {
      logger.error("Error requesting tokenization:", error);
      throw new Error(`Failed to request tokenization: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Add document to verification
   * @param verificationId Verification ID
   * @param documentUrl Document URL
   * @param userId User ID
   * @returns Updated verification
   */
  async addDocument(verificationId: string, documentUrl: string, userId: string) {
    try {
      // Get verification details
      const verification = await db.carbonCreditVerification.findUnique({
        where: { id: verificationId },
        include: {
          carbonCredit: true,
        },
      });

      if (!verification) {
        throw new Error("Verification not found");
      }

      // Check if user is the requester or an admin
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new Error("User not found");
      }

      if (verification.requestedById !== userId && user.role !== "ADMIN") {
        throw new Error("User is not authorized to add documents to this verification");
      }

      // Add document to verification
      const updatedVerification = await db.carbonCreditVerification.update({
        where: { id: verificationId },
        data: {
          documents: {
            push: documentUrl,
          },
        },
      });

      // Log audit event
      await auditService.log(
        "DOCUMENT_UPLOADED" as any, // TODO: Add VERIFICATION_DOCUMENT_ADDED to AuditLogType enum
        `Document added to verification for carbon credit ${verification.carbonCredit.name}`,
        {
          verificationId,
          carbonCreditId: verification.carbonCreditId,
          documentUrl,
        },
        userId
      );

      return updatedVerification;
    } catch (error) {
      logger.error("Error adding document to verification:", error);
      throw new Error(`Failed to add document to verification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}

// Export singleton instance
export const verificationService = new VerificationService();
