import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

/**
 * Add security headers to the response
 */
export function addSecurityHeaders(req: NextRequest, res: NextResponse): NextResponse {
  // Define security headers
  const securityHeaders = {
    // Prevent XSS attacks
    "X-XSS-Protection": "1; mode=block",
    
    // Prevent MIME type sniffing
    "X-Content-Type-Options": "nosniff",
    
    // Prevent clickjacking
    "X-Frame-Options": "DENY",
    
    // Strict Transport Security
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
    
    // Referrer Policy
    "Referrer-Policy": "strict-origin-when-cross-origin",
    
    // Permissions Policy
    "Permissions-Policy": "camera=(), microphone=(), geolocation=(), interest-cohort=()",
    
    // Content Security Policy - Fix: Remove newlines and extra spaces
    "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' http://localhost:3000; frame-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; block-all-mixed-content; upgrade-insecure-requests;",
  };

  // Add security headers to the response
  Object.entries(securityHeaders).forEach(([key, value]) => {
    res.headers.set(key, value);
  });
  
  return res;
}
