import { db } from '@/lib/db';
import { logger } from '@/lib/logger';

export enum OnboardingStep {
  ORGANIZATION_DETAILS = 'organization_details',
  TEAM_INVITATIONS = 'team_invitations',
  SUBSCRIPTION = 'subscription',
  WALLET_SETUP = 'wallet_setup',
  VERIFICATION = 'verification',
  COMPLETE = 'complete'
}

export interface OnboardingState {
  currentStep: OnboardingStep;
  organizationId?: string;
  skippedSteps: OnboardingStep[];
  completedSteps: OnboardingStep[];
  lastUpdated: Date;
}

export const onboardingService = {
  /**
   * Get the current onboarding state for a user
   * @param userId User ID
   * @returns The current onboarding state or null if not found
   */
  async getState(userId: string): Promise<OnboardingState | null> {
    try {
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          onboardingState: true
        }
      });

      if (!user?.onboardingState) {
        return null;
      }

      return {
        currentStep: user.onboardingState.currentStep as OnboardingStep,
        organizationId: user.onboardingState.organizationId || undefined,
        skippedSteps: user.onboardingState.skippedSteps as OnboardingStep[] || [],
        completedSteps: user.onboardingState.completedSteps as OnboardingStep[] || [],
        lastUpdated: user.onboardingState.updatedAt
      };
    } catch (error) {
      logger.error('Error getting onboarding state:', error);
      return null;
    }
  },

  /**
   * Save the onboarding state for a user
   * @param userId User ID
   * @param state Onboarding state to save
   */
  async saveState(userId: string, state: Partial<OnboardingState>): Promise<void> {
    try {
      // Check if the user has an onboarding state
      const existingState = await db.onboardingState.findUnique({
        where: { userId }
      });

      if (existingState) {
        // Update existing state
        await db.onboardingState.update({
          where: { userId },
          data: {
            currentStep: state.currentStep || existingState.currentStep,
            organizationId: state.organizationId || existingState.organizationId,
            skippedSteps: state.skippedSteps || existingState.skippedSteps,
            completedSteps: state.completedSteps || existingState.completedSteps
          }
        });
      } else {
        // Create new state
        await db.onboardingState.create({
          data: {
            userId,
            currentStep: state.currentStep || OnboardingStep.ORGANIZATION_DETAILS,
            organizationId: state.organizationId,
            skippedSteps: state.skippedSteps || [],
            completedSteps: state.completedSteps || []
          }
        });
      }
    } catch (error) {
      logger.error('Error saving onboarding state:', error);
    }
  },

  /**
   * Mark a step as complete
   * @param userId User ID
   * @param step Step to mark as complete
   * @param nextStep Next step to move to
   */
  async completeStep(userId: string, step: OnboardingStep, nextStep: OnboardingStep): Promise<void> {
    try {
      const state = await this.getState(userId);
      const completedSteps = [...(state?.completedSteps || [])];

      // Add step to completed steps if not already there
      if (!completedSteps.includes(step)) {
        completedSteps.push(step);
      }

      await this.saveState(userId, {
        currentStep: nextStep,
        completedSteps
      });
    } catch (error) {
      logger.error('Error completing onboarding step:', error);
    }
  },

  /**
   * Skip a step
   * @param userId User ID
   * @param step Step to skip
   * @param nextStep Next step to move to
   */
  async skipStep(userId: string, step: OnboardingStep, nextStep: OnboardingStep): Promise<void> {
    try {
      const state = await this.getState(userId);
      const skippedSteps = [...(state?.skippedSteps || [])];

      // Add step to skipped steps if not already there
      if (!skippedSteps.includes(step)) {
        skippedSteps.push(step);
      }

      await this.saveState(userId, {
        currentStep: nextStep,
        skippedSteps
      });
    } catch (error) {
      logger.error('Error skipping onboarding step:', error);
    }
  },

  /**
   * Get next pending step
   * @param userId User ID
   * @returns The next pending step or COMPLETE if all steps are completed
   */
  async getNextPendingStep(userId: string): Promise<OnboardingStep> {
    try {
      const state = await this.getState(userId);

      if (!state) {
        return OnboardingStep.ORGANIZATION_DETAILS;
      }

      const allSteps = [
        OnboardingStep.ORGANIZATION_DETAILS,
        OnboardingStep.TEAM_INVITATIONS,
        OnboardingStep.SUBSCRIPTION,
        OnboardingStep.WALLET_SETUP,
        OnboardingStep.VERIFICATION
      ];

      // Find the first step that is not completed and not skipped
      const pendingStep = allSteps.find(step =>
        !state.completedSteps.includes(step) && !state.skippedSteps.includes(step)
      );

      return pendingStep || OnboardingStep.COMPLETE;
    } catch (error) {
      logger.error('Error getting next pending onboarding step:', error);
      return OnboardingStep.ORGANIZATION_DETAILS;
    }
  },

  /**
   * Check if onboarding is complete
   * @param userId User ID
   * @returns True if onboarding is complete, false otherwise
   */
  async isComplete(userId: string): Promise<boolean> {
    try {
      const nextStep = await this.getNextPendingStep(userId);
      return nextStep === OnboardingStep.COMPLETE;
    } catch (error) {
      logger.error('Error checking if onboarding is complete:', error);
      return false;
    }
  }
};
