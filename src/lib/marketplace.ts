import { db } from "@/lib/db";
import { CarbonCreditStatus, OrderStatus, TransactionStatus } from "@prisma/client";

/**
 * Get marketplace statistics
 * @returns Marketplace statistics
 */
export async function getMarketplaceStats() {
  // Get total listed carbon credits
  const totalListed = await db.carbonCredit.count({
    where: {
      status: CarbonCreditStatus.LISTED,
    },
  });

  // Get total volume of carbon credits
  const totalVolume = await db.carbonCredit.aggregate({
    where: {
      status: CarbonCreditStatus.LISTED,
    },
    _sum: {
      availableQuantity: true,
    },
  });

  // Get average price of carbon credits
  const averagePrice = await db.carbonCredit.aggregate({
    where: {
      status: CarbonCreditStatus.LISTED,
    },
    _avg: {
      price: true,
    },
  });

  // Get total number of transactions
  const totalTransactions = await db.transaction.count({
    where: {
      status: TransactionStatus.COMPLETED,
    },
  });

  // Get total volume of transactions
  const transactionVolume = await db.transaction.aggregate({
    where: {
      status: TransactionStatus.COMPLETED,
    },
    _sum: {
      quantity: true,
      amount: true,
    },
  });

  // Get total number of active buy orders
  const activeBuyOrders = await db.order.count({
    where: {
      type: "BUY",
      status: OrderStatus.PENDING,
    },
  });

  // Get total number of active sell orders
  const activeSellOrders = await db.order.count({
    where: {
      type: "SELL",
      status: OrderStatus.PENDING,
    },
  });

  // Get total number of organizations with listed credits
  const organizationsWithCredits = await db.organization.count({
    where: {
      carbonCredits: {
        some: {
          status: CarbonCreditStatus.LISTED,
        },
      },
    },
  });

  // Get recent transactions
  const recentTransactions = await db.transaction.findMany({
    where: {
      status: TransactionStatus.COMPLETED,
    },
    include: {
      carbonCredit: {
        select: {
          name: true,
          standard: true,
          vintage: true,
        },
      },
      buyer: {
        select: {
          organization: {
            select: {
              name: true,
            },
          },
        },
      },
      seller: {
        select: {
          organization: {
            select: {
              name: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 5,
  });

  // Get price history (average price per month for the last 6 months)
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  const priceHistory = await db.transaction.groupBy({
    by: [
      {
        month: {
          datepart: "month",
          date: "createdAt",
        },
      },
      {
        year: {
          datepart: "year",
          date: "createdAt",
        },
      },
    ],
    where: {
      createdAt: {
        gte: sixMonthsAgo,
      },
      status: TransactionStatus.COMPLETED,
    },
    _avg: {
      price: true,
    },
    orderBy: [
      {
        year: "asc",
      },
      {
        month: "asc",
      },
    ],
  });

  return {
    totalListed,
    totalVolume: totalVolume._sum.availableQuantity || 0,
    averagePrice: averagePrice._avg.price || 0,
    totalTransactions,
    transactionVolume: {
      quantity: transactionVolume._sum.quantity || 0,
      amount: transactionVolume._sum.amount || 0,
    },
    activeBuyOrders,
    activeSellOrders,
    organizationsWithCredits,
    recentTransactions,
    priceHistory,
  };
}

/**
 * Get carbon credit details with related data
 * @param id Carbon credit ID
 * @returns Carbon credit details
 */
export async function getCarbonCreditDetails(id: string) {
  const carbonCredit = await db.carbonCredit.findUnique({
    where: { id },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          logo: true,
          website: true,
          verificationStatus: true,
          country: true,
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      buyOrders: {
        where: {
          status: OrderStatus.PENDING,
        },
        orderBy: {
          price: "desc",
        },
        take: 5,
      },
      sellOrders: {
        where: {
          status: OrderStatus.PENDING,
        },
        orderBy: {
          price: "asc",
        },
        take: 5,
      },
      transactions: {
        where: {
          status: TransactionStatus.COMPLETED,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 10,
        include: {
          buyer: {
            select: {
              organization: {
                select: {
                  name: true,
                },
              },
            },
          },
          seller: {
            select: {
              organization: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      },
      tokenizations: {
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
      },
    },
  });

  if (!carbonCredit) {
    return null;
  }

  // Get similar carbon credits
  const similarCredits = await db.carbonCredit.findMany({
    where: {
      id: { not: id },
      status: CarbonCreditStatus.LISTED,
      OR: [
        { standard: carbonCredit.standard },
        { methodology: carbonCredit.methodology },
        { vintage: carbonCredit.vintage },
      ],
    },
    include: {
      organization: {
        select: {
          name: true,
          logo: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 3,
  });

  // Get price history for this carbon credit
  const priceHistory = await db.carbonCreditPrice.findMany({
    where: {
      carbonCreditId: id,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  return {
    carbonCredit,
    similarCredits,
    priceHistory,
  };
}

/**
 * Create a buy order for a carbon credit
 * @param userId User ID
 * @param organizationId Organization ID
 * @param carbonCreditId Carbon credit ID
 * @param quantity Quantity to buy
 * @param price Price per ton
 * @returns Created order
 */
export async function createBuyOrder(
  userId: string,
  organizationId: string,
  carbonCreditId: string,
  quantity: number,
  price: number
) {
  // Check if the carbon credit exists and is listed
  const carbonCredit = await db.carbonCredit.findFirst({
    where: {
      id: carbonCreditId,
      status: CarbonCreditStatus.LISTED,
    },
  });

  if (!carbonCredit) {
    throw new Error("Carbon credit not found or not available for purchase");
  }

  // Check if the quantity is available
  if (carbonCredit.availableQuantity < quantity) {
    throw new Error(`Only ${carbonCredit.availableQuantity} tons available for purchase`);
  }

  // Check if the user is not buying their own carbon credit
  if (carbonCredit.organizationId === organizationId) {
    throw new Error("You cannot buy your own carbon credits");
  }

  // Create the buy order
  const order = await db.order.create({
    data: {
      type: "BUY",
      quantity,
      price,
      matchedQuantity: 0,
      status: OrderStatus.PENDING,
      buyer: {
        connect: { id: userId },
      },
      seller: {
        connect: { id: carbonCredit.userId },
      },
      carbonCredit: {
        connect: { id: carbonCreditId },
      },
      organization: {
        connect: { id: organizationId },
      },
    },
  });

  // Create an audit log
  await db.auditLog.create({
    data: {
      type: "BUY_ORDER_CREATED",
      description: `Buy order created for ${quantity} tons of ${carbonCredit.name} at $${price} per ton`,
      userId,
      organizationId,
      metadata: {
        orderId: order.id,
        carbonCreditId,
        quantity,
        price,
        totalValue: quantity * price,
      },
    },
  });

  // Create a notification for the carbon credit owner
  await db.notification.create({
    data: {
      title: "New Buy Order",
      message: `A new buy order has been placed for ${quantity} tons of your carbon credit "${carbonCredit.name}" at $${price} per ton.`,
      type: "ORDER",
      priority: "HIGH",
      user: {
        connect: { id: carbonCredit.userId },
      },
      organization: {
        connect: { id: carbonCredit.organizationId },
      },
      actionUrl: `/dashboard/orders/${order.id}`,
      actionLabel: "View Order",
      metadata: {
        orderId: order.id,
        carbonCreditId,
        quantity,
        price,
        totalValue: quantity * price,
      },
    },
  });

  return order;
}

/**
 * Get marketplace activity
 * @param limit Maximum number of activities to return
 * @returns Recent marketplace activity
 */
export async function getMarketplaceActivity(limit: number = 10) {
  // Get recent transactions
  const transactions = await db.transaction.findMany({
    where: {
      status: TransactionStatus.COMPLETED,
    },
    include: {
      carbonCredit: {
        select: {
          id: true,
          name: true,
          standard: true,
          vintage: true,
        },
      },
      buyer: {
        select: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      seller: {
        select: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: limit,
  });

  // Get recent listings
  const listings = await db.carbonCredit.findMany({
    where: {
      status: CarbonCreditStatus.LISTED,
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
        },
      },
    },
    orderBy: {
      listingDate: "desc",
    },
    take: limit,
  });

  // Combine and sort by date
  const activities = [
    ...transactions.map(tx => ({
      type: "TRANSACTION" as const,
      id: tx.id,
      date: tx.createdAt,
      carbonCreditId: tx.carbonCreditId,
      carbonCreditName: tx.carbonCredit.name,
      quantity: tx.quantity,
      price: tx.price,
      amount: tx.amount,
      buyerName: tx.buyer.organization?.name || "Unknown",
      buyerId: tx.buyer.organization?.id || "",
      sellerName: tx.seller.organization?.name || "Unknown",
      sellerId: tx.seller.organization?.id || "",
    })),
    ...listings.map(listing => ({
      type: "LISTING" as const,
      id: listing.id,
      date: listing.listingDate || listing.createdAt,
      carbonCreditId: listing.id,
      carbonCreditName: listing.name,
      quantity: listing.availableQuantity,
      price: listing.price,
      amount: listing.availableQuantity * listing.price,
      organizationName: listing.organization.name,
      organizationId: listing.organization.id,
    })),
  ].sort((a, b) => b.date.getTime() - a.date.getTime()).slice(0, limit);

  return activities;
}
