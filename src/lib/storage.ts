/**
 * Storage System (Legacy Compatibility Layer)
 *
 * This file provides backward compatibility with the old storage API.
 * It re-exports the functions from the new storage system.
 *
 * @deprecated Use the new storage system from '@/lib/storage/index.ts' instead.
 */

import { logger } from "@/lib/logger";
import {
  uploadFile as newUploadFile,
  deleteFile as newDeleteFile,
  getFileUrl as newGetFileUrl,
  fileExists,
  getFileMetadata,
  generateUniqueFileName,
  storageProvider
} from "./storage/index";

import type {
  StorageProvider,
  UploadResult,
  DeleteResult,
  FileMetadata,
  StorageConfig
} from "./storage/types";

// Log a warning about using the deprecated API
logger.warn(
  "Using deprecated storage API. Please update to use the new storage system from '@/lib/storage/index.ts'."
);

/**
 * Upload a file to storage
 * @param fileBuffer File buffer
 * @param fileName File name
 * @param contentType Content type
 * @returns Upload result
 */
export async function uploadFile(
  fileBuffer: Buffer,
  fileName: string,
  contentType: string,
  metadata?: Record<string, any>
): Promise<{ success: boolean; url: string; error?: string }> {
  return newUploadFile(fileBuffer, fileName, contentType, metadata);
}

/**
 * Delete a file from storage
 * @param fileName File name
 * @returns Delete result
 */
export async function deleteFile(fileName: string): Promise<{ success: boolean; error?: string }> {
  return newDeleteFile(fileName);
}

/**
 * Get a URL for a file
 * @param fileName File name
 * @returns File URL
 */
export function getFileUrl(fileName: string): string {
  return newGetFileUrl(fileName);
}

// Re-export other functions and types from the new storage system
export {
  fileExists,
  getFileMetadata,
  generateUniqueFileName,
  storageProvider
};

// Re-export types
export type {
  StorageProvider,
  UploadResult,
  DeleteResult,
  FileMetadata,
  StorageConfig
};
