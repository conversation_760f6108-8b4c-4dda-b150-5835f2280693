import { compare, hash } from "bcryptjs";
import { logger } from "@/lib/logger";

/**
 * Verify a password against a hash
 * Uses bcryptjs which is compatible with both Node.js and Edge runtime environments
 *
 * @param plainPassword The plain text password to verify
 * @param hashedPassword The hashed password to compare against
 * @returns True if the password matches, false otherwise
 */
export async function verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
  try {
    return await compare(plainPassword, hashedPassword);
  } catch (error) {
    logger.error("Error verifying password", error);
    return false;
  }
}

/**
 * Hash a password
 * Uses bcryptjs which is compatible with both Node.js and Edge runtime environments
 *
 * @param password The plain text password to hash
 * @returns The hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    // Use a cost factor of 10 for good balance between security and performance
    return await hash(password, 10);
  } catch (error) {
    logger.error("Error hashing password", error);
    throw new Error("Failed to hash password");
  }
}
