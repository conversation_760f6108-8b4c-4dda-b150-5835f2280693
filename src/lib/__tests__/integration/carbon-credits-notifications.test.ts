import { carbonCreditManager } from '@/lib/carbon-credits';
import { notificationService } from '@/lib/notifications';
import { db } from '@/lib/db';

// Mock the database
jest.mock('@/lib/db', () => ({
  db: {
    carbonCredit: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    organization: {
      findUnique: jest.fn(),
    },
    auditLog: {
      create: jest.fn(),
    },
  },
}));

// Mock the notification service
jest.mock('@/lib/notifications', () => ({
  notificationService: {
    createNotification: jest.fn(),
    createNotificationsForUsers: jest.fn(),
    createOrganizationNotification: jest.fn(),
  },
}));

// Mock the logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

describe('Carbon Credits and Notifications Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createCarbonCredit', () => {
    it('should create a carbon credit and send notifications', async () => {
      // Mock database responses
      const mockCarbonCredit = {
        id: 'credit-123',
        projectId: 'project-123',
        vintage: 2023,
        standard: 'VCS',
        methodology: 'VM0015',
        description: 'Forest conservation project',
        quantity: 1000,
        availableQuantity: 1000,
        price: 15.5,
        status: 'PENDING',
        verificationStatus: 'PENDING',
        tokenizationStatus: 'NOT_TOKENIZED',
        organizationId: 'org-123',
        userId: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockUser = {
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        organizationId: 'org-123',
      };

      const mockOrganization = {
        id: 'org-123',
        name: 'Acme Corp',
      };

      const mockAdmins = [
        { id: 'admin-1' },
        { id: 'admin-2' },
      ];

      const mockPlatformAdmins = [
        { id: 'platform-admin-1' },
        { id: 'platform-admin-2' },
      ];

      (db.carbonCredit.create as jest.Mock).mockResolvedValue(mockCarbonCredit);
      (db.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (db.organization.findUnique as jest.Mock).mockResolvedValue(mockOrganization);
      (db.user.findMany as jest.Mock).mockImplementation((args) => {
        if (args.where.organizationId === 'org-123' && args.where.role === 'ORGANIZATION_ADMIN') {
          return Promise.resolve(mockAdmins);
        } else if (args.where.role === 'ADMIN') {
          return Promise.resolve(mockPlatformAdmins);
        }
        return Promise.resolve([]);
      });

      // Call the function
      const result = await carbonCreditManager.createCarbonCredit({
        projectId: 'project-123',
        vintage: 2023,
        standard: 'VCS',
        methodology: 'VM0015',
        description: 'Forest conservation project',
        quantity: 1000,
        price: 15.5,
        organizationId: 'org-123',
        userId: 'user-123',
      });

      // Verify the result
      expect(result).toEqual(mockCarbonCredit);

      // Verify database calls
      expect(db.carbonCredit.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          projectId: 'project-123',
          vintage: 2023,
          standard: 'VCS',
          methodology: 'VM0015',
          description: 'Forest conservation project',
          quantity: 1000,
          availableQuantity: 1000,
          price: 15.5,
          status: 'PENDING',
          verificationStatus: 'PENDING',
          tokenizationStatus: 'NOT_TOKENIZED',
        }),
      });

      expect(db.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          type: 'CARBON_CREDIT_CREATED',
          description: expect.stringContaining('Carbon credit created for project project-123'),
          userId: 'user-123',
          organizationId: 'org-123',
        }),
      });

      // Verify notification calls
      expect(notificationService.createNotificationsForUsers).toHaveBeenCalledWith(
        ['admin-1', 'admin-2'],
        expect.objectContaining({
          title: 'New Carbon Credit Created',
          message: expect.stringContaining('A new carbon credit for project project-123 has been created'),
          type: 'CARBON_CREDIT',
          priority: 'NORMAL',
          actionUrl: expect.stringContaining('/dashboard/carbon-credits/'),
          actionLabel: 'View Carbon Credit',
          organizationId: 'org-123',
        })
      );

      expect(notificationService.createNotificationsForUsers).toHaveBeenCalledWith(
        ['platform-admin-1', 'platform-admin-2'],
        expect.objectContaining({
          title: 'New Carbon Credit Pending Verification',
          message: expect.stringContaining('A new carbon credit for project project-123 has been created'),
          type: 'CARBON_CREDIT',
          priority: 'NORMAL',
          actionUrl: '/admin/carbon-credits/verification',
          actionLabel: 'Verify Carbon Credit',
        })
      );
    });

    it('should handle errors', async () => {
      // Mock database error
      (db.carbonCredit.create as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the function and expect it to throw
      await expect(carbonCreditManager.createCarbonCredit({
        projectId: 'project-123',
        vintage: 2023,
        standard: 'VCS',
        methodology: 'VM0015',
        description: 'Forest conservation project',
        quantity: 1000,
        price: 15.5,
        organizationId: 'org-123',
        userId: 'user-123',
      })).rejects.toThrow('Failed to create carbon credit');
    });
  });
});
