import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { AuditStatus } from "@prisma/client";

/**
 * Audit a transaction
 * @param transactionId Transaction ID
 * @param status Audit status
 * @param userId User ID performing the audit
 * @param options Additional audit options
 * @returns Audit record
 */
export async function auditTransaction(
  transactionId: string,
  status: AuditStatus,
  userId: string,
  options?: {
    notes?: string;
    flagged?: boolean;
    flagReason?: string;
    reconciled?: boolean;
    documentUrls?: string[];
  }
) {
  try {
    logger.info(`Auditing transaction ${transactionId} with status ${status}`);

    // Check if the transaction exists
    const transaction = await db.transaction.findUnique({
      where: {
        id: transactionId,
      },
      include: {
        wallet: true,
      },
    });

    if (!transaction) {
      throw new Error("Transaction not found");
    }

    // Check if an audit already exists for this transaction
    const existingAudit = await db.transactionAudit.findFirst({
      where: {
        transactionId,
      },
    });

    let audit;
    if (existingAudit) {
      // Update existing audit
      audit = await db.transactionAudit.update({
        where: {
          id: existingAudit.id,
        },
        data: {
          status,
          notes: options?.notes,
          flagged: options?.flagged ?? existingAudit.flagged,
          flagReason: options?.flagged ? options?.flagReason : existingAudit.flagReason,
          flaggedBy: options?.flagged ? userId : existingAudit.flaggedBy,
          flaggedAt: options?.flagged ? new Date() : existingAudit.flaggedAt,
          reconciled: options?.reconciled ?? existingAudit.reconciled,
          reconciledBy: options?.reconciled ? userId : existingAudit.reconciledBy,
          reconciledAt: options?.reconciled ? new Date() : existingAudit.reconciledAt,
          verifiedBy: status === AuditStatus.VERIFIED ? userId : existingAudit.verifiedBy,
          verifiedAt: status === AuditStatus.VERIFIED ? new Date() : existingAudit.verifiedAt,
          documentUrls: options?.documentUrls ?? existingAudit.documentUrls,
        },
      });
    } else {
      // Create new audit
      audit = await db.transactionAudit.create({
        data: {
          transaction: {
            connect: {
              id: transactionId,
            },
          },
          status,
          notes: options?.notes,
          flagged: options?.flagged ?? false,
          flagReason: options?.flagReason,
          flaggedBy: options?.flagged ? userId : null,
          flaggedAt: options?.flagged ? new Date() : null,
          reconciled: options?.reconciled ?? false,
          reconciledBy: options?.reconciled ? userId : null,
          reconciledAt: options?.reconciled ? new Date() : null,
          verifiedBy: status === AuditStatus.VERIFIED ? userId : null,
          verifiedAt: status === AuditStatus.VERIFIED ? new Date() : null,
          documentUrls: options?.documentUrls ?? [],
        },
      });
    }

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "TRANSACTION_AUDITED",
        description: `Transaction ${transactionId} audited with status ${status}`,
        userId,
        organizationId: transaction.wallet.organizationId,
        metadata: {
          transactionId,
          auditId: audit.id,
          status,
          flagged: options?.flagged,
          reconciled: options?.reconciled,
        },
      },
    });

    // Create notification for transaction owner if different from auditor
    if (transaction.wallet.userId !== userId) {
      await db.notification.create({
        data: {
          title: "Transaction Audited",
          message: `Your transaction has been audited with status: ${status}`,
          type: "TRANSACTION",
          priority: options?.flagged ? "HIGH" : "NORMAL",
          user: {
            connect: {
              id: transaction.wallet.userId,
            },
          },
          actionUrl: `/wallet/transactions/${transactionId}`,
          actionLabel: "View Transaction",
        },
      });
    }

    return audit;
  } catch (error) {
    logger.error(`Error auditing transaction:`, error);
    throw new Error("Failed to audit transaction");
  }
}

/**
 * Get transaction audits for a user or organization
 * @param options Query options
 * @returns Transaction audits
 */
export async function getTransactionAudits(options: {
  userId?: string;
  organizationId?: string;
  transactionId?: string;
  status?: AuditStatus;
  flagged?: boolean;
  reconciled?: boolean;
  limit?: number;
  offset?: number;
}) {
  try {
    const {
      userId,
      organizationId,
      transactionId,
      status,
      flagged,
      reconciled,
      limit = 20,
      offset = 0,
    } = options;

    // Build filter
    const filter: any = {};

    if (transactionId) {
      filter.transactionId = transactionId;
    } else {
      // If no specific transaction ID, filter by user or organization
      if (userId || organizationId) {
        filter.transaction = {
          wallet: {
            ...(userId ? { userId } : {}),
            ...(organizationId ? { organizationId } : {}),
          },
        };
      }
    }

    if (status) {
      filter.status = status;
    }

    if (flagged !== undefined) {
      filter.flagged = flagged;
    }

    if (reconciled !== undefined) {
      filter.reconciled = reconciled;
    }

    // Get transaction audits
    const audits = await db.transactionAudit.findMany({
      where: filter,
      include: {
        transaction: {
          include: {
            wallet: {
              select: {
                id: true,
                address: true,
                network: true,
                userId: true,
                organizationId: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get total count
    const totalCount = await db.transactionAudit.count({
      where: filter,
    });

    return {
      audits,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + audits.length < totalCount,
      },
    };
  } catch (error) {
    logger.error(`Error getting transaction audits:`, error);
    throw new Error("Failed to get transaction audits");
  }
}

/**
 * Generate audit report for an organization
 * @param organizationId Organization ID
 * @param startDate Start date
 * @param endDate End date
 * @returns Audit report data
 */
export async function generateAuditReport(organizationId: string, startDate: Date, endDate: Date) {
  try {
    logger.info(`Generating audit report for organization ${organizationId}`);

    // Get all audits for the organization in the date range
    const audits = await db.transactionAudit.findMany({
      where: {
        transaction: {
          wallet: {
            organizationId,
          },
        },
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        transaction: {
          include: {
            wallet: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Group audits by status
    const auditsByStatus = audits.reduce((acc: any, audit) => {
      if (!acc[audit.status]) {
        acc[audit.status] = [];
      }
      acc[audit.status].push(audit);
      return acc;
    }, {});

    // Calculate summary statistics
    const totalAudits = audits.length;
    const verifiedCount = (auditsByStatus[AuditStatus.VERIFIED] || []).length;
    const pendingCount = (auditsByStatus[AuditStatus.PENDING] || []).length;
    const flaggedCount = (auditsByStatus[AuditStatus.FLAGGED] || []).length;
    const reconciledCount = (auditsByStatus[AuditStatus.RECONCILED] || []).length;
    const needsReviewCount = (auditsByStatus[AuditStatus.NEEDS_REVIEW] || []).length;

    // Calculate flagged percentage
    const flaggedPercentage = totalAudits > 0 ? (flaggedCount / totalAudits) * 100 : 0;

    // Calculate verification rate
    const verificationRate = totalAudits > 0 ? (verifiedCount / totalAudits) * 100 : 0;

    // Calculate average time to verification
    const verifiedAudits = auditsByStatus[AuditStatus.VERIFIED] || [];
    let avgTimeToVerification = 0;
    if (verifiedAudits.length > 0) {
      const totalTime = verifiedAudits.reduce((sum: number, audit: any) => {
        const createdAt = new Date(audit.createdAt).getTime();
        const verifiedAt = new Date(audit.verifiedAt).getTime();
        return sum + (verifiedAt - createdAt);
      }, 0);
      avgTimeToVerification = totalTime / verifiedAudits.length / (1000 * 60 * 60); // Convert to hours
    }

    return {
      audits,
      auditsByStatus,
      summary: {
        totalAudits,
        verifiedCount,
        pendingCount,
        flaggedCount,
        reconciledCount,
        needsReviewCount,
        flaggedPercentage,
        verificationRate,
        avgTimeToVerification,
      },
      period: {
        startDate,
        endDate,
      },
    };
  } catch (error) {
    logger.error(`Error generating audit report:`, error);
    throw new Error("Failed to generate audit report");
  }
}
