import { 
  CarbonCreditStatus, 
  OrderStatus 
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { MarketTrends, CarbonCreditAnalytics, TimePeriod } from './types';
import { getDateRange } from './utils';

/**
 * Market analytics service
 */
export class MarketAnalytics {
  /**
   * Get carbon credit market trends
   * @param period Period in days (default: 30)
   * @returns Market trends
   */
  static async getMarketTrends(period: number = 30): Promise<MarketTrends> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      // Get completed orders in the period
      const orders = await db.order.findMany({
        where: {
          status: "COMPLETED",
          updatedAt: {
            gte: startDate,
          },
        },
        include: {
          carbonCredit: {
            select: {
              standard: true,
              methodology: true,
              vintage: true,
            },
          },
        },
        orderBy: {
          updatedAt: "asc",
        },
      });

      // Group orders by day
      const ordersByDay = orders.reduce((acc, order) => {
        const date = order.updatedAt.toISOString().split("T")[0];
        if (!acc[date]) {
          acc[date] = [];
        }
        acc[date].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate daily volume and average price
      const dailyStats = Object.entries(ordersByDay).map(([date, dayOrders]) => {
        const volume = dayOrders.reduce((sum, order) => sum + order.quantity, 0);
        const value = dayOrders.reduce(
          (sum, order) => sum + order.price * order.quantity,
          0
        );
        const averagePrice = volume > 0 ? value / volume : 0;

        return {
          date,
          volume,
          value,
          averagePrice,
          orderCount: dayOrders.length,
        };
      });

      // Group by standard
      const standardGroups = orders.reduce((acc, order) => {
        const standard = order.carbonCredit.standard;
        if (!acc[standard]) {
          acc[standard] = [];
        }
        acc[standard].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      const standardStats = Object.entries(standardGroups).map(
        ([standard, standardOrders]) => {
          const volume = standardOrders.reduce((sum, order) => sum + order.quantity, 0);
          const value = standardOrders.reduce(
            (sum, order) => sum + order.price * order.quantity,
            0
          );
          const averagePrice = volume > 0 ? value / volume : 0;

          return {
            standard,
            volume,
            value,
            averagePrice,
            orderCount: standardOrders.length,
          };
        }
      );

      // Group by methodology
      const methodologyGroups = orders.reduce((acc, order) => {
        const methodology = order.carbonCredit.methodology;
        if (!acc[methodology]) {
          acc[methodology] = [];
        }
        acc[methodology].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      const methodologyStats = Object.entries(methodologyGroups).map(
        ([methodology, methodologyOrders]) => {
          const volume = methodologyOrders.reduce(
            (sum, order) => sum + order.quantity,
            0
          );
          const value = methodologyOrders.reduce(
            (sum, order) => sum + order.price * order.quantity,
            0
          );
          const averagePrice = volume > 0 ? value / volume : 0;

          return {
            methodology,
            volume,
            value,
            averagePrice,
            orderCount: methodologyOrders.length,
          };
        }
      );

      // Group by vintage
      const vintageGroups = orders.reduce((acc, order) => {
        const vintage = order.carbonCredit.vintage.toString();
        if (!acc[vintage]) {
          acc[vintage] = [];
        }
        acc[vintage].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      const vintageStats = Object.entries(vintageGroups).map(
        ([vintage, vintageOrders]) => {
          const volume = vintageOrders.reduce((sum, order) => sum + order.quantity, 0);
          const value = vintageOrders.reduce(
            (sum, order) => sum + order.price * order.quantity,
            0
          );
          const averagePrice = volume > 0 ? value / volume : 0;

          return {
            vintage: parseInt(vintage),
            volume,
            value,
            averagePrice,
            orderCount: vintageOrders.length,
          };
        }
      );

      return {
        period,
        totalVolume: orders.reduce((sum, order) => sum + order.quantity, 0),
        totalValue: orders.reduce(
          (sum, order) => sum + order.price * order.quantity,
          0
        ),
        orderCount: orders.length,
        dailyStats,
        standardStats,
        methodologyStats,
        vintageStats,
      };
    } catch (error) {
      logger.error(`Error getting market trends for period ${period}:`, error);
      throw new Error("Failed to get market trends");
    }
  }

  /**
   * Get carbon credit analytics
   * @param period Time period
   * @returns Carbon credit analytics
   */
  static async getCarbonCreditAnalytics(period: TimePeriod = 'month'): Promise<CarbonCreditAnalytics> {
    try {
      const { startDate, endDate } = getDateRange(period);

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count();

      // Get new carbon credits in the period
      const newCarbonCredits = await db.carbonCredit.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      });

      // Get carbon credits by standard
      const carbonCreditsByStandard = await db.carbonCredit.groupBy({
        by: ['standard'],
        _count: {
          id: true,
        },
      });

      // Get carbon credits by methodology
      const carbonCreditsByMethodology = await db.carbonCredit.groupBy({
        by: ['methodology'],
        _count: {
          id: true,
        },
      });

      // Get carbon credits by vintage
      const carbonCreditsByVintage = await db.carbonCredit.groupBy({
        by: ['vintage'],
        _count: {
          id: true,
        },
        orderBy: {
          vintage: 'desc',
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits available quantity
      const carbonCreditAvailableQuantity = await db.carbonCredit.aggregate({
        _sum: {
          availableQuantity: true,
        },
      });

      // Get total carbon credits traded quantity
      const carbonCreditTradedQuantity = await db.order.aggregate({
        where: {
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get average carbon credit price
      const averageCarbonCreditPrice = await db.carbonCredit.aggregate({
        where: {
          status: CarbonCreditStatus.LISTED,
        },
        _avg: {
          price: true,
        },
      });

      // Get price range
      const priceRange = await db.$queryRaw<{ min: number; max: number }[]>`
        SELECT 
          MIN(price) as min,
          MAX(price) as max
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
      `;

      // Get price by standard
      const priceByStandard = await db.$queryRaw<{ standard: string; avg: number }[]>`
        SELECT 
          standard,
          AVG(price) as avg
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
        GROUP BY standard
        ORDER BY avg DESC
      `;

      // Get price by methodology
      const priceByMethodology = await db.$queryRaw<{ methodology: string; avg: number }[]>`
        SELECT 
          methodology,
          AVG(price) as avg
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
        GROUP BY methodology
        ORDER BY avg DESC
      `;

      // Get price by vintage
      const priceByVintage = await db.$queryRaw<{ vintage: number; avg: number }[]>`
        SELECT 
          vintage,
          AVG(price) as avg
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
        GROUP BY vintage
        ORDER BY vintage DESC
      `;

      // Get daily new listings
      const dailyNewListings = await db.$queryRaw<{ date: string; count: number }[]>`
        SELECT 
          DATE_TRUNC('day', "listingDate") as date,
          COUNT(*) as count
        FROM "CarbonCredit"
        WHERE 
          "listingDate" IS NOT NULL AND
          "listingDate" >= ${startDate} AND 
          "listingDate" <= ${endDate}
        GROUP BY DATE_TRUNC('day', "listingDate")
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        carbonCredits: {
          total: totalCarbonCredits,
          new: newCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          byStandard: carbonCreditsByStandard.reduce((acc, curr) => {
            acc[curr.standard] = curr._count.id;
            return acc;
          }, {} as Record<string, number>),
          byMethodology: carbonCreditsByMethodology.reduce((acc, curr) => {
            acc[curr.methodology] = curr._count.id;
            return acc;
          }, {} as Record<string, number>),
          byVintage: carbonCreditsByVintage.reduce((acc, curr) => {
            acc[curr.vintage] = curr._count.id;
            return acc;
          }, {} as Record<number, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          availableQuantity: carbonCreditAvailableQuantity._sum.availableQuantity || 0,
          tradedQuantity: carbonCreditTradedQuantity._sum.quantity || 0,
        },
        pricing: {
          average: averageCarbonCreditPrice._avg.price || 0,
          range: priceRange[0] || { min: 0, max: 0 },
          byStandard: priceByStandard,
          byMethodology: priceByMethodology,
          byVintage: priceByVintage,
        },
        listings: {
          daily: dailyNewListings,
        },
      };
    } catch (error) {
      logger.error('Error getting carbon credit analytics:', error);
      throw error;
    }
  }
}
