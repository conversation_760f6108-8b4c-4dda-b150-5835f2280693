import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { AssetType, ValuationMethod } from "@prisma/client";

/**
 * Create an asset valuation
 * @param assetType Asset type
 * @param assetId Asset ID
 * @param valuationMethod Valuation method
 * @param valueAmount Value amount
 * @param approvedBy User ID who approved the valuation
 * @param options Additional valuation options
 * @returns Asset valuation record
 */
export async function createAssetValuation(
  assetType: AssetType,
  assetId: string,
  valuationMethod: ValuationMethod,
  valueAmount: number,
  approvedBy: string,
  options?: {
    valueCurrency?: string;
    previousValue?: number;
    valuationNotes?: string;
    dataSource?: string;
    confidence?: number;
    transactionId?: string;
    carbonCreditId?: string;
  }
) {
  try {
    logger.info(`Creating ${valuationMethod} valuation for ${assetType} ${assetId}`);

    // Calculate change percentage if previous value is provided
    let changePercentage = null;
    if (options?.previousValue !== undefined && options.previousValue !== 0) {
      changePercentage = ((valueAmount - options.previousValue) / Math.abs(options.previousValue)) * 100;
    }

    // Create asset valuation
    const valuation = await db.assetValuation.create({
      data: {
        assetType,
        assetId,
        valuationMethod,
        valueAmount,
        valueCurrency: options?.valueCurrency || "USD",
        previousValue: options?.previousValue,
        changePercentage,
        valuationNotes: options?.valuationNotes,
        dataSource: options?.dataSource,
        confidence: options?.confidence,
        approvedBy,
        approvedAt: new Date(),
        ...(options?.transactionId
          ? {
              transaction: {
                connect: {
                  id: options.transactionId,
                },
              },
            }
          : {}),
        ...(options?.carbonCreditId
          ? {
              carbonCredit: {
                connect: {
                  id: options.carbonCreditId,
                },
              },
            }
          : {}),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "ASSET_VALUATION_CREATED",
        description: `Created ${assetType} valuation for asset ${assetId}`,
        userId: approvedBy,
        metadata: {
          valuationId: valuation.id,
          assetType,
          assetId,
          valueAmount,
          valuationMethod,
        },
      },
    });

    return valuation;
  } catch (error) {
    logger.error(`Error creating asset valuation:`, error);
    throw new Error("Failed to create asset valuation");
  }
}

/**
 * Get asset valuations
 * @param options Query options
 * @returns Asset valuations
 */
export async function getAssetValuations(options: {
  assetType?: AssetType;
  assetId?: string;
  valuationMethod?: ValuationMethod;
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  organizationId?: string;
  limit?: number;
  offset?: number;
}) {
  try {
    const {
      assetType,
      assetId,
      valuationMethod,
      startDate,
      endDate,
      userId,
      organizationId,
      limit = 20,
      offset = 0,
    } = options;

    // Build filter
    const filter: any = {};

    if (assetType) {
      filter.assetType = assetType;
    }

    if (assetId) {
      filter.assetId = assetId;
    }

    if (valuationMethod) {
      filter.valuationMethod = valuationMethod;
    }

    if (startDate || endDate) {
      filter.valuationDate = {};

      if (startDate) {
        filter.valuationDate.gte = startDate;
      }

      if (endDate) {
        filter.valuationDate.lte = endDate;
      }
    }

    // Apply tenant isolation based on asset type
    if (assetType === AssetType.CARBON_CREDIT) {
      // For carbon credits, check if they belong to the user or organization
      if (userId || organizationId) {
        filter.OR = [
          {
            carbonCredit: {
              ...(userId ? { userId } : {}),
              ...(organizationId ? { organizationId } : {}),
            },
          },
          {
            carbonCredit: {
              status: "LISTED", // Public carbon credits
            },
          },
        ];
      }
    } else if (assetType === AssetType.WALLET || assetType === AssetType.TOKEN) {
      // For wallets and tokens, check if they belong to the user or organization
      if (userId || organizationId) {
        filter.OR = [
          {
            transaction: {
              wallet: {
                ...(userId ? { userId } : {}),
                ...(organizationId ? { organizationId } : {}),
              },
            },
          },
        ];
      }
    }

    // Get asset valuations
    const valuations = await db.assetValuation.findMany({
      where: filter,
      include: {
        transaction: {
          select: {
            id: true,
            amount: true,
            type: true,
            status: true,
            wallet: {
              select: {
                id: true,
                address: true,
                network: true,
              },
            },
          },
        },
        carbonCredit: {
          select: {
            id: true,
            name: true,
            standard: true,
            vintage: true,
            quantity: true,
            availableQuantity: true,
            price: true,
          },
        },
      },
      orderBy: {
        valuationDate: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get total count
    const totalCount = await db.assetValuation.count({
      where: filter,
    });

    // Get asset details based on asset type
    let assetDetails = null;
    if (assetId && assetType) {
      if (assetType === AssetType.CARBON_CREDIT) {
        assetDetails = await db.carbonCredit.findUnique({
          where: { id: assetId },
          select: {
            id: true,
            name: true,
            standard: true,
            vintage: true,
            quantity: true,
            availableQuantity: true,
            price: true,
          },
        });
      } else if (assetType === AssetType.WALLET) {
        assetDetails = await db.wallet.findUnique({
          where: { id: assetId },
          select: {
            id: true,
            address: true,
            network: true,
            balance: true,
          },
        });
      } else if (assetType === AssetType.TOKEN) {
        assetDetails = await db.token.findUnique({
          where: { id: assetId },
          select: {
            id: true,
            address: true,
            symbol: true,
            name: true,
            balance: true,
          },
        });
      }
    }

    return {
      valuations,
      assetDetails,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + valuations.length < totalCount,
      },
    };
  } catch (error) {
    logger.error(`Error getting asset valuations:`, error);
    throw new Error("Failed to get asset valuations");
  }
}

/**
 * Get valuation history for an asset
 * @param assetType Asset type
 * @param assetId Asset ID
 * @param limit Maximum number of valuations to return
 * @returns Valuation history
 */
export async function getValuationHistory(assetType: AssetType, assetId: string, limit: number = 10) {
  try {
    logger.info(`Getting valuation history for ${assetType} ${assetId}`);

    // Get valuations for the asset
    const valuations = await db.assetValuation.findMany({
      where: {
        assetType,
        assetId,
      },
      orderBy: {
        valuationDate: "desc",
      },
      take: limit,
    });

    // Calculate statistics
    const currentValue = valuations.length > 0 ? valuations[0].valueAmount : 0;
    const oldestValue = valuations.length > 0 ? valuations[valuations.length - 1].valueAmount : 0;
    const changeValue = currentValue - oldestValue;
    const changePercentage = oldestValue !== 0 ? (changeValue / oldestValue) * 100 : 0;

    // Calculate average value
    const avgValue = valuations.length > 0
      ? valuations.reduce((sum, val) => sum + val.valueAmount, 0) / valuations.length
      : 0;

    // Calculate min and max values
    const minValue = valuations.length > 0
      ? Math.min(...valuations.map(val => val.valueAmount))
      : 0;
    const maxValue = valuations.length > 0
      ? Math.max(...valuations.map(val => val.valueAmount))
      : 0;

    return {
      valuations,
      statistics: {
        currentValue,
        oldestValue,
        changeValue,
        changePercentage,
        avgValue,
        minValue,
        maxValue,
      },
    };
  } catch (error) {
    logger.error(`Error getting valuation history:`, error);
    throw new Error("Failed to get valuation history");
  }
}

/**
 * Generate portfolio valuation report
 * @param userId User ID
 * @param organizationId Organization ID
 * @returns Portfolio valuation report
 */
export async function generatePortfolioValuation(userId?: string, organizationId?: string) {
  try {
    if (!userId && !organizationId) {
      throw new Error("Either userId or organizationId must be provided");
    }

    logger.info(`Generating portfolio valuation for ${userId ? `user ${userId}` : `organization ${organizationId}`}`);

    // Get carbon credits
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        ...(userId ? { userId } : {}),
        ...(organizationId ? { organizationId } : {}),
      },
    });

    // Get wallets
    const wallets = await db.wallet.findMany({
      where: {
        ...(userId ? { userId } : {}),
        ...(organizationId ? { organizationId } : {}),
      },
      include: {
        tokens: true,
      },
    });

    // Calculate carbon credit value
    const carbonCreditValue = carbonCredits.reduce((sum, credit) => sum + credit.price * credit.quantity, 0);

    // Calculate wallet value
    const walletValue = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);

    // Calculate token value
    const tokenValue = wallets.reduce(
      (sum, wallet) => sum + wallet.tokens.reduce((tokenSum, token) => tokenSum + token.balance, 0),
      0
    );

    // Calculate total portfolio value
    const totalValue = carbonCreditValue + walletValue + tokenValue;

    // Calculate asset allocation percentages
    const carbonCreditPercentage = totalValue > 0 ? (carbonCreditValue / totalValue) * 100 : 0;
    const walletPercentage = totalValue > 0 ? (walletValue / totalValue) * 100 : 0;
    const tokenPercentage = totalValue > 0 ? (tokenValue / totalValue) * 100 : 0;

    return {
      totalValue,
      assets: {
        carbonCredits: {
          value: carbonCreditValue,
          percentage: carbonCreditPercentage,
          count: carbonCredits.length,
          items: carbonCredits.map(credit => ({
            id: credit.id,
            name: credit.name,
            standard: credit.standard,
            vintage: credit.vintage,
            quantity: credit.quantity,
            price: credit.price,
            value: credit.price * credit.quantity,
          })),
        },
        wallets: {
          value: walletValue,
          percentage: walletPercentage,
          count: wallets.length,
          items: wallets.map(wallet => ({
            id: wallet.id,
            address: wallet.address,
            network: wallet.network,
            balance: wallet.balance,
          })),
        },
        tokens: {
          value: tokenValue,
          percentage: tokenPercentage,
          count: wallets.reduce((sum, wallet) => sum + wallet.tokens.length, 0),
          items: wallets.flatMap(wallet =>
            wallet.tokens.map(token => ({
              id: token.id,
              walletId: wallet.id,
              address: token.address,
              name: token.name,
              symbol: token.symbol,
              balance: token.balance,
            }))
          ),
        },
      },
    };
  } catch (error) {
    logger.error(`Error generating portfolio valuation:`, error);
    throw new Error("Failed to generate portfolio valuation");
  }
}
