import { 
  CarbonCreditStatus, 
  OrderStatus, 
  TransactionStatus,
  UserRole,
  BillingStatus
} from '@prisma/client';

/**
 * Time period for analytics
 */
export type TimePeriod = 'day' | 'week' | 'month' | 'year' | 'all';

/**
 * Platform statistics interface
 */
export interface PlatformStats {
  users: {
    total: number;
    organizations: {
      total: number;
      active: number;
    };
  };
  carbonCredits: {
    total: number;
    listed: number;
    sold: number;
    retired: number;
  };
  transactions: {
    total: number;
    completed: number;
    volume: number;
    fees: number;
  };
  orders: {
    total: number;
    completed: number;
    pending: number;
  };
  subscriptions: Record<string, number>;
}

/**
 * Platform overview interface
 */
export interface PlatformOverview {
  period: TimePeriod;
  startDate: Date;
  endDate: Date;
  organizations: {
    total: number;
  };
  users: {
    total: number;
  };
  carbonCredits: {
    total: number;
    byStatus: Record<CarbonCreditStatus, number>;
    quantity: number;
    tradedQuantity: number;
  };
  orders: {
    total: number;
    byStatus: Record<OrderStatus, number>;
  };
  transactions: {
    total: number;
    volume: number;
    fees: number;
    dailyVolume: { date: string; volume: number }[];
  };
}

/**
 * Organization statistics interface
 */
export interface OrganizationStats {
  organization: {
    id: string;
    name: string;
    status: string;
    subscription?: string;
    transactionFeeRate: number;
    listingFeeRate: number;
  };
  users: {
    total: number;
  };
  carbonCredits: {
    total: number;
    listed: number;
    sold: number;
    retired: number;
    volume: number;
  };
  transactions: {
    buyVolume: number;
    sellVolume: number;
    totalVolume: number;
    fees: number;
  };
}

/**
 * Organization analytics interface
 */
export interface OrganizationAnalytics {
  period: TimePeriod;
  startDate: Date;
  endDate: Date;
  organization: {
    id: string;
    name: string;
    status: string;
    verificationStatus: string;
    transactionFeeRate: number;
    listingFeeRate: number;
  };
  users: {
    total: number;
    new: number;
  };
  carbonCredits: {
    total: number;
    new: number;
    byStatus: Record<CarbonCreditStatus, number>;
    quantity: number;
    availableQuantity: number;
  };
  orders: {
    buy: {
      total: number;
      new: number;
      byStatus: Record<OrderStatus, number>;
      volume: number;
    };
    sell: {
      total: number;
      new: number;
      byStatus: Record<OrderStatus, number>;
      volume: number;
    };
  };
  fees: {
    transactionFees: number;
    listingFees: number;
    total: number;
  };
  transactions: {
    dailyVolume: { date: string; buyVolume: number; sellVolume: number }[];
  };
}

/**
 * Market trends interface
 */
export interface MarketTrends {
  period: number;
  totalVolume: number;
  totalValue: number;
  orderCount: number;
  dailyStats: {
    date: string;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
  standardStats: {
    standard: string;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
  methodologyStats: {
    methodology: string;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
  vintageStats: {
    vintage: number;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
}

/**
 * Carbon credit analytics interface
 */
export interface CarbonCreditAnalytics {
  period: TimePeriod;
  startDate: Date;
  endDate: Date;
  carbonCredits: {
    total: number;
    new: number;
    byStatus: Record<CarbonCreditStatus, number>;
    byStandard: Record<string, number>;
    byMethodology: Record<string, number>;
    byVintage: Record<number, number>;
    quantity: number;
    availableQuantity: number;
    tradedQuantity: number;
  };
  pricing: {
    average: number;
    range: { min: number; max: number };
    byStandard: { standard: string; avg: number }[];
    byMethodology: { methodology: string; avg: number }[];
    byVintage: { vintage: number; avg: number }[];
  };
  listings: {
    daily: { date: string; count: number }[];
  };
}

/**
 * User activity interface
 */
export interface UserActivity {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    organization: {
      id: string;
      name: string;
    } | null;
  };
  carbonCredits: {
    count: number;
    totalQuantity: number;
    byStatus: {
      pending: number;
      verified: number;
      listed: number;
      sold: number;
      retired: number;
    };
  };
  orders: {
    buy: {
      count: number;
      volume: number;
      byStatus: {
        pending: number;
        matched: number;
        completed: number;
        cancelled: number;
      };
    };
    sell: {
      count: number;
      volume: number;
      byStatus: {
        pending: number;
        matched: number;
        completed: number;
        cancelled: number;
      };
    };
    fees: number;
  };
  wallets: {
    count: number;
    tokens: number;
    nfts: number;
  };
}

/**
 * User analytics interface
 */
export interface UserAnalytics {
  period: TimePeriod;
  startDate: Date;
  endDate: Date;
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
    organization: {
      id: string;
      name: string;
    } | null;
  };
  carbonCredits: {
    total: number;
    new: number;
    byStatus: Record<CarbonCreditStatus, number>;
    quantity: number;
    availableQuantity: number;
  };
  orders: {
    buy: {
      total: number;
      new: number;
      byStatus: Record<OrderStatus, number>;
      volume: number;
    };
    sell: {
      total: number;
      new: number;
      byStatus: Record<OrderStatus, number>;
      volume: number;
    };
  };
  activity: {
    daily: { date: string; carbonCredits: number; buyOrders: number; sellOrders: number }[];
  };
}

/**
 * Audit filters interface
 */
export interface AuditFilters {
  organizationId?: string;
  userId?: string;
  startDate?: Date;
  endDate?: Date;
  type?: string;
  limit?: number;
  offset?: number;
}

/**
 * Audit trail interface
 */
export interface AuditTrail {
  records: any[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
