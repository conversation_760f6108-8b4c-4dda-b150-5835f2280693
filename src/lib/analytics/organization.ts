import { 
  CarbonCreditStatus, 
  OrderStatus, 
  BillingStatus
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { OrganizationStats, OrganizationAnalytics, TimePeriod } from './types';
import { getDateRange } from './utils';

/**
 * Organization analytics service
 */
export class OrganizationAnalytics {
  /**
   * Get organization statistics
   * @param organizationId Organization ID
   * @returns Organization statistics
   */
  static async getOrganizationStats(organizationId: string): Promise<OrganizationStats> {
    try {
      // Get organization details
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: {
          subscription: true,
        },
      });

      if (!organization) {
        throw new Error("Organization not found");
      }

      // Get user statistics
      const totalUsers = await db.user.count({
        where: { organizationId },
      });

      // Get carbon credit statistics
      const totalCarbonCredits = await db.carbonCredit.count({
        where: { organizationId },
      });
      const listedCarbonCredits = await db.carbonCredit.count({
        where: { organizationId, status: "LISTED" },
      });
      const soldCarbonCredits = await db.carbonCredit.count({
        where: { organizationId, status: "SOLD" },
      });
      const retiredCarbonCredits = await db.carbonCredit.count({
        where: { organizationId, status: "RETIRED" },
      });

      // Get carbon credit volume
      const carbonCreditVolume = await db.carbonCredit.aggregate({
        _sum: { quantity: true },
        where: { organizationId },
      });

      // Get transaction statistics
      const userIds = await db.user.findMany({
        where: { organizationId },
        select: { id: true },
      });
      const userIdList = userIds.map((user) => user.id);

      // Get buy orders
      const buyOrders = await db.order.findMany({
        where: {
          buyerId: { in: userIdList },
          status: "COMPLETED",
        },
        include: {
          transactions: true,
        },
      });

      // Get sell orders
      const sellOrders = await db.order.findMany({
        where: {
          sellerId: { in: userIdList },
          status: "COMPLETED",
        },
        include: {
          transactions: true,
        },
      });

      // Calculate transaction volume and fees
      const buyVolume = buyOrders.reduce(
        (sum, order) => sum + order.price * order.quantity,
        0
      );
      const sellVolume = sellOrders.reduce(
        (sum, order) => sum + order.price * order.quantity,
        0
      );
      const totalFees = [...buyOrders, ...sellOrders].reduce(
        (sum, order) =>
          sum +
          order.transactions.reduce(
            (orderSum, tx) => (tx.type === "FEE" ? orderSum + tx.amount : orderSum),
            0
          ),
        0
      );

      return {
        organization: {
          id: organization.id,
          name: organization.name,
          status: organization.status,
          subscription: organization.subscription?.plan || "NONE",
          transactionFeeRate: organization.transactionFeeRate,
          listingFeeRate: organization.listingFeeRate,
        },
        users: {
          total: totalUsers,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          listed: listedCarbonCredits,
          sold: soldCarbonCredits,
          retired: retiredCarbonCredits,
          volume: carbonCreditVolume._sum.quantity || 0,
        },
        transactions: {
          buyVolume,
          sellVolume,
          totalVolume: buyVolume + sellVolume,
          fees: totalFees,
        },
      };
    } catch (error) {
      logger.error(`Error getting organization stats for ${organizationId}:`, error);
      throw new Error("Failed to get organization statistics");
    }
  }

  /**
   * Get organization analytics
   * @param organizationId Organization ID
   * @param period Time period
   * @returns Organization analytics
   */
  static async getOrganizationAnalytics(organizationId: string, period: TimePeriod = 'month'): Promise<OrganizationAnalytics> {
    try {
      const { startDate, endDate } = getDateRange(period);

      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Get total users in the organization
      const totalUsers = await db.user.count({
        where: {
          organizationId,
        },
      });

      // Get new users in the period
      const newUsers = await db.user.count({
        where: {
          organizationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count({
        where: {
          organizationId,
        },
      });

      // Get new carbon credits in the period
      const newCarbonCredits = await db.carbonCredit.count({
        where: {
          organizationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        where: {
          organizationId,
        },
        _count: {
          id: true,
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        where: {
          organizationId,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits available quantity
      const carbonCreditAvailableQuantity = await db.carbonCredit.aggregate({
        where: {
          organizationId,
        },
        _sum: {
          availableQuantity: true,
        },
      });

      // Get total buy orders
      const totalBuyOrders = await db.order.count({
        where: {
          buyer: {
            organizationId,
          },
        },
      });

      // Get new buy orders in the period
      const newBuyOrders = await db.order.count({
        where: {
          buyer: {
            organizationId,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total sell orders
      const totalSellOrders = await db.order.count({
        where: {
          seller: {
            organizationId,
          },
        },
      });

      // Get new sell orders in the period
      const newSellOrders = await db.order.count({
        where: {
          seller: {
            organizationId,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get buy orders by status
      const buyOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          buyer: {
            organizationId,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get sell orders by status
      const sellOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          seller: {
            organizationId,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get total buy volume
      const buyVolume = await db.order.aggregate({
        where: {
          buyer: {
            organizationId,
          },
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total sell volume
      const sellVolume = await db.order.aggregate({
        where: {
          seller: {
            organizationId,
          },
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total transaction fees paid
      const transactionFeesPaid = await db.billingRecord.aggregate({
        where: {
          organizationId,
          type: 'TRANSACTION_FEE',
          status: BillingStatus.PAID,
        },
        _sum: {
          amount: true,
        },
      });

      // Get total listing fees paid
      const listingFeesPaid = await db.billingRecord.aggregate({
        where: {
          organizationId,
          type: 'LISTING_FEE',
          status: BillingStatus.PAID,
        },
        _sum: {
          amount: true,
        },
      });

      // Get daily transaction volume for the period
      const dailyTransactionVolume = await db.$queryRaw<{ date: string; buyVolume: number; sellVolume: number }[]>`
        SELECT 
          DATE_TRUNC('day', o."createdAt") as date,
          SUM(CASE WHEN bu."organizationId" = ${organizationId} THEN o.quantity * o.price ELSE 0 END) as "buyVolume",
          SUM(CASE WHEN se."organizationId" = ${organizationId} THEN o.quantity * o.price ELSE 0 END) as "sellVolume"
        FROM "Order" o
        JOIN "User" bu ON o."buyerId" = bu.id
        JOIN "User" se ON o."sellerId" = se.id
        WHERE 
          o."createdAt" >= ${startDate} AND 
          o."createdAt" <= ${endDate} AND
          o.status = 'COMPLETED' AND
          (bu."organizationId" = ${organizationId} OR se."organizationId" = ${organizationId})
        GROUP BY DATE_TRUNC('day', o."createdAt")
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        organization: {
          id: organization.id,
          name: organization.name,
          status: organization.status,
          verificationStatus: organization.verificationStatus,
          transactionFeeRate: organization.transactionFeeRate,
          listingFeeRate: organization.listingFeeRate,
        },
        users: {
          total: totalUsers,
          new: newUsers,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          new: newCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          availableQuantity: carbonCreditAvailableQuantity._sum.availableQuantity || 0,
        },
        orders: {
          buy: {
            total: totalBuyOrders,
            new: newBuyOrders,
            byStatus: buyOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: buyVolume._sum.quantity || 0,
          },
          sell: {
            total: totalSellOrders,
            new: newSellOrders,
            byStatus: sellOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: sellVolume._sum.quantity || 0,
          },
        },
        fees: {
          transactionFees: transactionFeesPaid._sum.amount || 0,
          listingFees: listingFeesPaid._sum.amount || 0,
          total: (transactionFeesPaid._sum.amount || 0) + (listingFeesPaid._sum.amount || 0),
        },
        transactions: {
          dailyVolume: dailyTransactionVolume,
        },
      };
    } catch (error) {
      logger.error('Error getting organization analytics:', error);
      throw error;
    }
  }
}
