import { 
  CarbonCreditStatus, 
  OrderStatus, 
  TransactionStatus,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { PlatformStats, PlatformOverview, TimePeriod } from './types';
import { getDateRange } from './utils';

/**
 * Platform analytics service
 */
export class PlatformAnalytics {
  /**
   * Get platform overview statistics
   * @returns Platform statistics
   */
  static async getPlatformStats(): Promise<PlatformStats> {
    try {
      // Get user statistics
      const totalUsers = await db.user.count();
      const totalOrganizations = await db.organization.count();
      const activeOrganizations = await db.organization.count({
        where: { status: "ACTIVE" },
      });

      // Get carbon credit statistics
      const totalCarbonCredits = await db.carbonCredit.count();
      const listedCarbonCredits = await db.carbonCredit.count({
        where: { status: "LISTED" },
      });
      const soldCarbonCredits = await db.carbonCredit.count({
        where: { status: "SOLD" },
      });
      const retiredCarbonCredits = await db.carbonCredit.count({
        where: { status: "RETIRED" },
      });

      // Get transaction statistics
      const totalTransactions = await db.transaction.count();
      const completedTransactions = await db.transaction.count({
        where: { status: "COMPLETED" },
      });
      const totalTransactionVolume = await db.transaction.aggregate({
        _sum: { amount: true },
        where: { status: "COMPLETED" },
      });
      const totalFees = await db.transaction.aggregate({
        _sum: { fee: true },
        where: { status: "COMPLETED" },
      });

      // Get order statistics
      const totalOrders = await db.order.count();
      const completedOrders = await db.order.count({
        where: { status: "COMPLETED" },
      });
      const pendingOrders = await db.order.count({
        where: { status: "PENDING" },
      });

      // Get subscription statistics
      const subscriptionsByPlan = await db.subscription.groupBy({
        by: ["plan"],
        _count: true,
      });

      return {
        users: {
          total: totalUsers,
          organizations: {
            total: totalOrganizations,
            active: activeOrganizations,
          },
        },
        carbonCredits: {
          total: totalCarbonCredits,
          listed: listedCarbonCredits,
          sold: soldCarbonCredits,
          retired: retiredCarbonCredits,
        },
        transactions: {
          total: totalTransactions,
          completed: completedTransactions,
          volume: totalTransactionVolume._sum.amount || 0,
          fees: totalFees._sum.fee || 0,
        },
        orders: {
          total: totalOrders,
          completed: completedOrders,
          pending: pendingOrders,
        },
        subscriptions: subscriptionsByPlan.reduce((acc, item) => {
          acc[item.plan.toLowerCase()] = item._count;
          return acc;
        }, {} as Record<string, number>),
      };
    } catch (error) {
      logger.error("Error getting platform stats:", error);
      throw new Error("Failed to get platform statistics");
    }
  }

  /**
   * Get platform overview analytics
   * @param period Time period
   * @returns Platform overview analytics
   */
  static async getPlatformOverview(period: TimePeriod = 'month'): Promise<PlatformOverview> {
    try {
      const { startDate, endDate } = getDateRange(period);

      // Get total organizations
      const totalOrganizations = await db.organization.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total users
      const totalUsers = await db.user.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get total orders
      const totalOrders = await db.order.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total orders by status
      const ordersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get total transactions
      const totalTransactions = await db.transaction.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total transaction volume
      const transactionVolume = await db.transaction.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: TransactionStatus.COMPLETED,
        },
        _sum: {
          amount: true,
        },
      });

      // Get total transaction fees
      const transactionFees = await db.transaction.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: TransactionStatus.COMPLETED,
        },
        _sum: {
          fee: true,
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits traded quantity
      const carbonCreditTradedQuantity = await db.order.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get daily transaction volume for the period
      const dailyTransactionVolume = await db.$queryRaw<{ date: string; volume: number }[]>`
        SELECT 
          DATE_TRUNC('day', "createdAt") as date,
          SUM(amount) as volume
        FROM "Transaction"
        WHERE 
          "createdAt" >= ${startDate} AND 
          "createdAt" <= ${endDate} AND
          status = 'COMPLETED'
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        organizations: {
          total: totalOrganizations,
        },
        users: {
          total: totalUsers,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          tradedQuantity: carbonCreditTradedQuantity._sum.quantity || 0,
        },
        orders: {
          total: totalOrders,
          byStatus: ordersByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<OrderStatus, number>),
        },
        transactions: {
          total: totalTransactions,
          volume: transactionVolume._sum.amount || 0,
          fees: transactionFees._sum.fee || 0,
          dailyVolume: dailyTransactionVolume,
        },
      };
    } catch (error) {
      logger.error('Error getting platform overview:', error);
      throw error;
    }
  }
}
