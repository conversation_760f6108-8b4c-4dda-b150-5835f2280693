import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { FinancialMetricType, MetricStatus } from "@prisma/client";

/**
 * Generate financial metrics for an organization
 * @param organizationId Organization ID
 * @param period Period to generate metrics for (e.g., "Q1 2023")
 * @param startDate Start date of the period
 * @param endDate End date of the period
 * @returns Generated metrics
 */
export async function generateFinancialMetrics(
  organizationId: string,
  period: string,
  startDate: Date,
  endDate: Date
) {
  try {
    logger.info(`Generating financial metrics for organization ${organizationId} for period ${period}`);

    // Get all wallets for the organization
    const wallets = await db.wallet.findMany({
      where: {
        organizationId,
      },
      include: {
        transactions: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
        },
        tokens: true,
      },
    });

    // Get carbon credits for the organization
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        organizationId,
      },
    });

    // Calculate transaction volume
    const transactions = wallets.flatMap(wallet => wallet.transactions);
    const transactionVolume = transactions.reduce((sum, tx) => sum + tx.amount, 0);

    // Calculate average transaction size
    const avgTransactionSize = transactions.length > 0 ? transactionVolume / transactions.length : 0;

    // Calculate wallet balance
    const walletBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);

    // Calculate carbon asset value
    const carbonAssetValue = carbonCredits.reduce((sum, credit) => sum + credit.price * credit.quantity, 0);

    // Calculate trading volume
    const tradingVolume = transactions
      .filter(tx => tx.type === "BUY" || tx.type === "SELL")
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Calculate retirement volume
    const retirementVolume = transactions
      .filter(tx => tx.type === "RETIRE")
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Calculate tokenization volume
    const tokenizationVolume = transactions
      .filter(tx => tx.type === "MINT")
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Calculate fee revenue
    const feeRevenue = transactions.reduce((sum, tx) => sum + tx.fee, 0);

    // Get previous period metrics for comparison
    const previousPeriodStartDate = new Date(startDate);
    previousPeriodStartDate.setMonth(previousPeriodStartDate.getMonth() - 3); // Assuming quarterly periods
    const previousPeriodEndDate = new Date(endDate);
    previousPeriodEndDate.setMonth(previousPeriodEndDate.getMonth() - 3);

    const previousPeriodMetrics = await db.financialMetric.findMany({
      where: {
        organizationId,
        startDate: {
          gte: previousPeriodStartDate,
        },
        endDate: {
          lte: previousPeriodEndDate,
        },
      },
    });

    // Create metrics
    const metrics = [
      {
        metricType: FinancialMetricType.TRANSACTION_VOLUME,
        name: "Transaction Volume",
        value: transactionVolume,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.TRANSACTION_VOLUME),
      },
      {
        metricType: FinancialMetricType.AVERAGE_TRANSACTION_SIZE,
        name: "Average Transaction Size",
        value: avgTransactionSize,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.AVERAGE_TRANSACTION_SIZE),
      },
      {
        metricType: FinancialMetricType.WALLET_BALANCE,
        name: "Wallet Balance",
        value: walletBalance,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.WALLET_BALANCE),
      },
      {
        metricType: FinancialMetricType.CARBON_ASSET_VALUE,
        name: "Carbon Asset Value",
        value: carbonAssetValue,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.CARBON_ASSET_VALUE),
      },
      {
        metricType: FinancialMetricType.TRADING_VOLUME,
        name: "Trading Volume",
        value: tradingVolume,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.TRADING_VOLUME),
      },
      {
        metricType: FinancialMetricType.RETIREMENT_VOLUME,
        name: "Retirement Volume",
        value: retirementVolume,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.RETIREMENT_VOLUME),
      },
      {
        metricType: FinancialMetricType.TOKENIZATION_VOLUME,
        name: "Tokenization Volume",
        value: tokenizationVolume,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.TOKENIZATION_VOLUME),
      },
      {
        metricType: FinancialMetricType.FEE_REVENUE,
        name: "Fee Revenue",
        value: feeRevenue,
        previousValue: getPreviousMetricValue(previousPeriodMetrics, FinancialMetricType.FEE_REVENUE),
      },
    ];

    // Save metrics to database
    const savedMetrics = await Promise.all(
      metrics.map(async metric => {
        // Calculate change percentage
        let changePercent = null;
        if (metric.previousValue !== undefined && metric.previousValue !== 0) {
          changePercent = ((metric.value - metric.previousValue) / Math.abs(metric.previousValue)) * 100;
        }

        // Determine status based on change
        let status = null;
        if (changePercent !== null) {
          if (changePercent >= 10) {
            status = MetricStatus.ABOVE_TARGET;
          } else if (changePercent >= 0) {
            status = MetricStatus.ON_TARGET;
          } else if (changePercent >= -10) {
            status = MetricStatus.BELOW_TARGET;
          } else {
            status = MetricStatus.CRITICAL;
          }
        }

        return db.financialMetric.create({
          data: {
            metricType: metric.metricType,
            name: metric.name,
            value: metric.value,
            previousValue: metric.previousValue,
            changePercent,
            status,
            currency: "USD",
            period,
            startDate,
            endDate,
            organization: {
              connect: {
                id: organizationId,
              },
            },
          },
        });
      })
    );

    return savedMetrics;
  } catch (error) {
    logger.error(`Error generating financial metrics:`, error);
    throw new Error("Failed to generate financial metrics");
  }
}

/**
 * Generate a financial report for an organization
 * @param organizationId Organization ID
 * @param reportType Report type
 * @param name Report name
 * @param period Period to generate report for (e.g., "Q1 2023")
 * @param startDate Start date of the period
 * @param endDate End date of the period
 * @param generatedBy User ID who generated the report
 * @returns Generated report
 */
export async function generateFinancialReport(
  organizationId: string,
  reportType: string,
  name: string,
  period: string,
  startDate: Date,
  endDate: Date,
  generatedBy: string
) {
  try {
    logger.info(`Generating ${reportType} report for organization ${organizationId} for period ${period}`);

    // Get metrics for the period
    const metrics = await db.financialMetric.findMany({
      where: {
        organizationId,
        startDate: {
          gte: startDate,
        },
        endDate: {
          lte: endDate,
        },
      },
    });

    // Get transactions for the period
    const transactions = await db.transaction.findMany({
      where: {
        wallet: {
          organizationId,
        },
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        wallet: true,
      },
    });

    // Generate report data based on report type
    let reportData: any = {};

    if (reportType === "TRANSACTION_SUMMARY") {
      // Group transactions by type
      const transactionsByType = transactions.reduce((acc: any, tx) => {
        if (!acc[tx.type]) {
          acc[tx.type] = [];
        }
        acc[tx.type].push(tx);
        return acc;
      }, {});

      // Calculate summary statistics
      const totalVolume = transactions.reduce((sum, tx) => sum + tx.amount, 0);
      const totalFees = transactions.reduce((sum, tx) => sum + tx.fee, 0);
      const avgTransactionSize = transactions.length > 0 ? totalVolume / transactions.length : 0;

      reportData = {
        transactionsByType,
        summary: {
          totalTransactions: transactions.length,
          totalVolume,
          totalFees,
          avgTransactionSize,
        },
      };
    } else if (reportType === "REVENUE_REPORT") {
      // Calculate revenue from fees
      const feeRevenue = transactions.reduce((sum, tx) => sum + tx.fee, 0);

      // Group revenue by network
      const revenueByNetwork = transactions.reduce((acc: any, tx) => {
        const network = tx.wallet.network;
        if (!acc[network]) {
          acc[network] = 0;
        }
        acc[network] += tx.fee;
        return acc;
      }, {});

      reportData = {
        feeRevenue,
        revenueByNetwork,
        transactions: transactions.map(tx => ({
          id: tx.id,
          amount: tx.amount,
          fee: tx.fee,
          type: tx.type,
          createdAt: tx.createdAt,
          network: tx.wallet.network,
        })),
      };
    } else if (reportType === "ASSET_VALUATION") {
      // Get carbon credits
      const carbonCredits = await db.carbonCredit.findMany({
        where: {
          organizationId,
        },
      });

      // Get wallet balances
      const wallets = await db.wallet.findMany({
        where: {
          organizationId,
        },
        include: {
          tokens: true,
        },
      });

      // Calculate asset values
      const carbonAssetValue = carbonCredits.reduce((sum, credit) => sum + credit.price * credit.quantity, 0);
      const walletBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      const tokenValue = wallets.reduce(
        (sum, wallet) => sum + wallet.tokens.reduce((tokenSum, token) => tokenSum + token.balance, 0),
        0
      );

      reportData = {
        assets: {
          carbonCredits: {
            count: carbonCredits.length,
            value: carbonAssetValue,
            items: carbonCredits.map(credit => ({
              id: credit.id,
              name: credit.name,
              standard: credit.standard,
              vintage: credit.vintage,
              quantity: credit.quantity,
              price: credit.price,
              value: credit.price * credit.quantity,
            })),
          },
          wallets: {
            count: wallets.length,
            value: walletBalance,
            items: wallets.map(wallet => ({
              id: wallet.id,
              address: wallet.address,
              network: wallet.network,
              balance: wallet.balance,
            })),
          },
          tokens: {
            value: tokenValue,
            items: wallets.flatMap(wallet =>
              wallet.tokens.map(token => ({
                id: token.id,
                walletId: wallet.id,
                address: token.address,
                name: token.name,
                symbol: token.symbol,
                balance: token.balance,
              }))
            ),
          },
        },
        summary: {
          totalAssetValue: carbonAssetValue + walletBalance + tokenValue,
          carbonAssetValue,
          walletBalance,
          tokenValue,
        },
      };
    } else if (reportType === "PROFIT_LOSS") {
      // Calculate revenue (fees)
      const revenue = transactions.reduce((sum, tx) => sum + tx.fee, 0);

      // Calculate expenses (gas costs)
      const expenses = transactions.reduce((sum, tx) => {
        const gasUsed = tx.gasUsed || 0;
        const gasPrice = tx.gasPrice || 0;
        return sum + (gasUsed * gasPrice) / 1e9; // Convert from wei to ETH
      }, 0);

      // Calculate profit/loss
      const profitLoss = revenue - expenses;

      reportData = {
        revenue,
        expenses,
        profitLoss,
        transactions: transactions.map(tx => ({
          id: tx.id,
          amount: tx.amount,
          fee: tx.fee,
          gasUsed: tx.gasUsed,
          gasPrice: tx.gasPrice,
          gasCost: tx.gasUsed && tx.gasPrice ? (tx.gasUsed * tx.gasPrice) / 1e9 : 0,
          type: tx.type,
          createdAt: tx.createdAt,
        })),
      };
    } else {
      // Custom report
      reportData = {
        metrics,
        transactions,
      };
    }

    // Create report in database
    const report = await db.financialReport.create({
      data: {
        reportType,
        name,
        period,
        startDate,
        endDate,
        data: reportData,
        status: "COMPLETED",
        generatedBy,
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });

    return report;
  } catch (error) {
    logger.error(`Error generating financial report:`, error);
    throw new Error("Failed to generate financial report");
  }
}

/**
 * Generate a period comparison for an organization
 * @param organizationId Organization ID
 * @param comparisonType Comparison type
 * @param name Comparison name
 * @param period1Start Start date of period 1
 * @param period1End End date of period 1
 * @param period2Start Start date of period 2
 * @param period2End End date of period 2
 * @param createdBy User ID who created the comparison
 * @returns Generated comparison
 */
export async function generatePeriodComparison(
  organizationId: string,
  comparisonType: string,
  name: string,
  period1Start: Date,
  period1End: Date,
  period2Start: Date,
  period2End: Date,
  createdBy: string
) {
  try {
    logger.info(`Generating ${comparisonType} comparison for organization ${organizationId}`);

    // Get metrics for period 1
    const period1Metrics = await db.financialMetric.findMany({
      where: {
        organizationId,
        startDate: {
          gte: period1Start,
        },
        endDate: {
          lte: period1End,
        },
      },
    });

    // Get metrics for period 2
    const period2Metrics = await db.financialMetric.findMany({
      where: {
        organizationId,
        startDate: {
          gte: period2Start,
        },
        endDate: {
          lte: period2End,
        },
      },
    });

    // Group metrics by type
    const period1MetricsByType = period1Metrics.reduce((acc: any, metric) => {
      acc[metric.metricType] = metric;
      return acc;
    }, {});

    const period2MetricsByType = period2Metrics.reduce((acc: any, metric) => {
      acc[metric.metricType] = metric;
      return acc;
    }, {});

    // Calculate comparisons
    const metricComparisons: any = {};
    const allMetricTypes = [
      ...new Set([
        ...period1Metrics.map(m => m.metricType),
        ...period2Metrics.map(m => m.metricType),
      ]),
    ];

    for (const metricType of allMetricTypes) {
      const period1Metric = period1MetricsByType[metricType];
      const period2Metric = period2MetricsByType[metricType];

      if (period1Metric && period2Metric) {
        const changeValue = period2Metric.value - period1Metric.value;
        const changePercent =
          period1Metric.value !== 0 ? (changeValue / Math.abs(period1Metric.value)) * 100 : null;

        metricComparisons[metricType] = {
          name: period2Metric.name,
          period1Value: period1Metric.value,
          period2Value: period2Metric.value,
          changeValue,
          changePercent,
        };
      } else if (period1Metric) {
        metricComparisons[metricType] = {
          name: period1Metric.name,
          period1Value: period1Metric.value,
          period2Value: 0,
          changeValue: -period1Metric.value,
          changePercent: -100,
        };
      } else if (period2Metric) {
        metricComparisons[metricType] = {
          name: period2Metric.name,
          period1Value: 0,
          period2Value: period2Metric.value,
          changeValue: period2Metric.value,
          changePercent: null,
        };
      }
    }

    // Calculate overall change percentage
    const changes = Object.values(metricComparisons)
      .filter((m: any) => m.changePercent !== null)
      .map((m: any) => m.changePercent);

    const overallChangePercent = changes.length > 0 ? changes.reduce((sum: number, val: number) => sum + val, 0) / changes.length : null;

    // Create comparison in database
    const comparison = await db.periodComparison.create({
      data: {
        comparisonType,
        name,
        period1Start,
        period1End,
        period2Start,
        period2End,
        metrics: metricComparisons,
        changePercent: overallChangePercent,
        createdBy,
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });

    return comparison;
  } catch (error) {
    logger.error(`Error generating period comparison:`, error);
    throw new Error("Failed to generate period comparison");
  }
}

/**
 * Helper function to get previous metric value
 * @param previousMetrics Previous metrics
 * @param metricType Metric type to find
 * @returns Previous value or undefined
 */
function getPreviousMetricValue(previousMetrics: any[], metricType: FinancialMetricType): number | undefined {
  const metric = previousMetrics.find(m => m.metricType === metricType);
  return metric ? metric.value : undefined;
}
