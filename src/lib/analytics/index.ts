import { PlatformAnalytics } from './platform';
import { OrganizationAnalytics } from './organization';
import { UserAnalytics } from './user';
import { MarketAnalytics } from './market';
import { 
  TimePeriod, 
  PlatformStats, 
  PlatformOverview,
  OrganizationStats,
  OrganizationAnalytics as OrgAnalytics,
  UserActivity,
  UserAnalytics as UserAnalyticsType,
  MarketTrends,
  CarbonCreditAnalytics,
  AuditFilters,
  AuditTrail
} from './types';
import { getDateRange } from './utils';

/**
 * Unified analytics service
 */
export class AnalyticsService {
  /**
   * Get platform statistics
   * @returns Platform statistics
   */
  async getPlatformStats(): Promise<PlatformStats> {
    return PlatformAnalytics.getPlatformStats();
  }

  /**
   * Get platform overview
   * @param period Time period
   * @returns Platform overview
   */
  async getPlatformOverview(period: TimePeriod = 'month'): Promise<PlatformOverview> {
    return PlatformAnalytics.getPlatformOverview(period);
  }

  /**
   * Get organization statistics
   * @param organizationId Organization ID
   * @returns Organization statistics
   */
  async getOrganizationStats(organizationId: string): Promise<OrganizationStats> {
    return OrganizationAnalytics.getOrganizationStats(organizationId);
  }

  /**
   * Get organization analytics
   * @param organizationId Organization ID
   * @param period Time period
   * @returns Organization analytics
   */
  async getOrganizationAnalytics(organizationId: string, period: TimePeriod = 'month'): Promise<OrgAnalytics> {
    return OrganizationAnalytics.getOrganizationAnalytics(organizationId, period);
  }

  /**
   * Get user activity
   * @param userId User ID
   * @returns User activity
   */
  async getUserActivity(userId: string): Promise<UserActivity> {
    return UserAnalytics.getUserActivity(userId);
  }

  /**
   * Get user analytics
   * @param userId User ID
   * @param period Time period
   * @returns User analytics
   */
  async getUserAnalytics(userId: string, period: TimePeriod = 'month'): Promise<UserAnalyticsType> {
    return UserAnalytics.getUserAnalytics(userId, period);
  }

  /**
   * Get market trends
   * @param period Period in days
   * @returns Market trends
   */
  async getMarketTrends(period: number = 30): Promise<MarketTrends> {
    return MarketAnalytics.getMarketTrends(period);
  }

  /**
   * Get carbon credit analytics
   * @param period Time period
   * @returns Carbon credit analytics
   */
  async getCarbonCreditAnalytics(period: TimePeriod = 'month'): Promise<CarbonCreditAnalytics> {
    return MarketAnalytics.getCarbonCreditAnalytics(period);
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();

// Export types
export {
  TimePeriod,
  PlatformStats,
  PlatformOverview,
  OrganizationStats,
  OrgAnalytics,
  UserActivity,
  UserAnalyticsType,
  MarketTrends,
  CarbonCreditAnalytics,
  AuditFilters,
  AuditTrail
};
