import { PlatformAnalytics } from '../platform';
import { db } from '@/lib/db';

// Mock the database
jest.mock('@/lib/db', () => ({
  db: {
    user: {
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    organization: {
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    carbonCredit: {
      count: jest.fn(),
      aggregate: jest.fn(),
      groupBy: jest.fn(),
    },
    transaction: {
      count: jest.fn(),
      aggregate: jest.fn(),
      groupBy: jest.fn(),
    },
    order: {
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    $queryRaw: jest.fn(),
  },
}));

// Mock the logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

describe('PlatformAnalytics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPlatformStats', () => {
    it('should return platform statistics', async () => {
      // Mock database responses
      (db.user.count as jest.Mock).mockResolvedValue(100);
      (db.organization.count as jest.Mock).mockResolvedValue(20);
      (db.carbonCredit.count as jest.Mock).mockResolvedValue(500);
      (db.transaction.count as jest.Mock).mockResolvedValue(1000);
      (db.carbonCredit.aggregate as jest.Mock).mockResolvedValue({
        _sum: { quantity: 10000 },
        _avg: { price: 15.5 },
      });
      (db.transaction.aggregate as jest.Mock).mockResolvedValue({
        _sum: { amount: 50000 },
      });

      // Call the function
      const stats = await PlatformAnalytics.getPlatformStats();

      // Verify the result
      expect(stats).toEqual({
        userCount: 100,
        organizationCount: 20,
        carbonCreditCount: 500,
        transactionCount: 1000,
        totalCarbonCredits: 10000,
        averageCarbonCreditPrice: 15.5,
        totalTransactionVolume: 50000,
      });

      // Verify database calls
      expect(db.user.count).toHaveBeenCalled();
      expect(db.organization.count).toHaveBeenCalled();
      expect(db.carbonCredit.count).toHaveBeenCalled();
      expect(db.transaction.count).toHaveBeenCalled();
      expect(db.carbonCredit.aggregate).toHaveBeenCalled();
      expect(db.transaction.aggregate).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock database error
      (db.user.count as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the function and expect it to throw
      await expect(PlatformAnalytics.getPlatformStats()).rejects.toThrow('Failed to get platform statistics');
    });
  });

  describe('getUserGrowth', () => {
    it('should return user growth data', async () => {
      // Mock database response
      (db.$queryRaw as jest.Mock).mockResolvedValue([
        { date: '2023-01-01', count: 10 },
        { date: '2023-01-02', count: 15 },
        { date: '2023-01-03', count: 20 },
      ]);

      // Call the function
      const growth = await PlatformAnalytics.getUserGrowth('day', 30);

      // Verify the result
      expect(growth).toEqual([
        { date: '2023-01-01', count: 10 },
        { date: '2023-01-02', count: 15 },
        { date: '2023-01-03', count: 20 },
      ]);

      // Verify database call
      expect(db.$queryRaw).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock database error
      (db.$queryRaw as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the function and expect it to throw
      await expect(PlatformAnalytics.getUserGrowth('day', 30)).rejects.toThrow('Failed to get user growth');
    });
  });

  describe('getOrganizationGrowth', () => {
    it('should return organization growth data', async () => {
      // Mock database response
      (db.$queryRaw as jest.Mock).mockResolvedValue([
        { date: '2023-01-01', count: 5 },
        { date: '2023-01-02', count: 8 },
        { date: '2023-01-03', count: 12 },
      ]);

      // Call the function
      const growth = await PlatformAnalytics.getOrganizationGrowth('day', 30);

      // Verify the result
      expect(growth).toEqual([
        { date: '2023-01-01', count: 5 },
        { date: '2023-01-02', count: 8 },
        { date: '2023-01-03', count: 12 },
      ]);

      // Verify database call
      expect(db.$queryRaw).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock database error
      (db.$queryRaw as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the function and expect it to throw
      await expect(PlatformAnalytics.getOrganizationGrowth('day', 30)).rejects.toThrow('Failed to get organization growth');
    });
  });

  describe('getTransactionVolume', () => {
    it('should return transaction volume data', async () => {
      // Mock database response
      (db.$queryRaw as jest.Mock).mockResolvedValue([
        { date: '2023-01-01', volume: 1000 },
        { date: '2023-01-02', volume: 1500 },
        { date: '2023-01-03', volume: 2000 },
      ]);

      // Call the function
      const volume = await PlatformAnalytics.getTransactionVolume('day', 30);

      // Verify the result
      expect(volume).toEqual([
        { date: '2023-01-01', volume: 1000 },
        { date: '2023-01-02', volume: 1500 },
        { date: '2023-01-03', volume: 2000 },
      ]);

      // Verify database call
      expect(db.$queryRaw).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock database error
      (db.$queryRaw as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the function and expect it to throw
      await expect(PlatformAnalytics.getTransactionVolume('day', 30)).rejects.toThrow('Failed to get transaction volume');
    });
  });

  describe('getCarbonCreditDistribution', () => {
    it('should return carbon credit distribution data', async () => {
      // Mock database response
      (db.carbonCredit.groupBy as jest.Mock).mockResolvedValue([
        { standard: 'VCS', _sum: { quantity: 5000 } },
        { standard: 'Gold Standard', _sum: { quantity: 3000 } },
        { standard: 'ACR', _sum: { quantity: 2000 } },
      ]);

      // Call the function
      const distribution = await PlatformAnalytics.getCarbonCreditDistribution('standard');

      // Verify the result
      expect(distribution).toEqual([
        { category: 'VCS', value: 5000 },
        { category: 'Gold Standard', value: 3000 },
        { category: 'ACR', value: 2000 },
      ]);

      // Verify database call
      expect(db.carbonCredit.groupBy).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock database error
      (db.carbonCredit.groupBy as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the function and expect it to throw
      await expect(PlatformAnalytics.getCarbonCreditDistribution('standard')).rejects.toThrow('Failed to get carbon credit distribution');
    });
  });
});
