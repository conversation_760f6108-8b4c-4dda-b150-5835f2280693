import { 
  CarbonCreditStatus, 
  OrderStatus 
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { UserActivity, UserAnalytics, TimePeriod } from './types';
import { getDateRange } from './utils';

/**
 * User analytics service
 */
export class UserAnalytics {
  /**
   * Get user activity report
   * @param userId User ID
   * @returns User activity report
   */
  static async getUserActivity(userId: string): Promise<UserActivity> {
    try {
      // Get user details
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Get carbon credits
      const carbonCredits = await db.carbonCredit.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      });

      // Get buy orders
      const buyOrders = await db.order.findMany({
        where: { buyerId: userId },
        include: {
          carbonCredit: true,
          transactions: true,
        },
        orderBy: { createdAt: "desc" },
      });

      // Get sell orders
      const sellOrders = await db.order.findMany({
        where: { sellerId: userId },
        include: {
          carbonCredit: true,
          transactions: true,
        },
        orderBy: { createdAt: "desc" },
      });

      // Get wallets
      const wallets = await db.wallet.findMany({
        where: { userId },
        include: {
          tokens: true,
          nfts: true,
        },
      });

      // Calculate statistics
      const totalCarbonCredits = carbonCredits.reduce(
        (sum, credit) => sum + credit.quantity,
        0
      );
      const totalBuyVolume = buyOrders
        .filter((order) => order.status === "COMPLETED")
        .reduce((sum, order) => sum + order.price * order.quantity, 0);
      const totalSellVolume = sellOrders
        .filter((order) => order.status === "COMPLETED")
        .reduce((sum, order) => sum + order.price * order.quantity, 0);
      const totalFees = [...buyOrders, ...sellOrders]
        .filter((order) => order.status === "COMPLETED")
        .reduce(
          (sum, order) =>
            sum +
            order.transactions.reduce(
              (orderSum, tx) => (tx.type === "FEE" ? orderSum + tx.amount : orderSum),
              0
            ),
          0
        );

      return {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          organization: user.organization
            ? {
                id: user.organization.id,
                name: user.organization.name,
              }
            : null,
        },
        carbonCredits: {
          count: carbonCredits.length,
          totalQuantity: totalCarbonCredits,
          byStatus: {
            pending: carbonCredits.filter((c) => c.status === "PENDING").length,
            verified: carbonCredits.filter((c) => c.status === "VERIFIED").length,
            listed: carbonCredits.filter((c) => c.status === "LISTED").length,
            sold: carbonCredits.filter((c) => c.status === "SOLD").length,
            retired: carbonCredits.filter((c) => c.status === "RETIRED").length,
          },
        },
        orders: {
          buy: {
            count: buyOrders.length,
            volume: totalBuyVolume,
            byStatus: {
              pending: buyOrders.filter((o) => o.status === "PENDING").length,
              matched: buyOrders.filter((o) => o.status === "MATCHED").length,
              completed: buyOrders.filter((o) => o.status === "COMPLETED").length,
              cancelled: buyOrders.filter((o) => o.status === "CANCELLED").length,
            },
          },
          sell: {
            count: sellOrders.length,
            volume: totalSellVolume,
            byStatus: {
              pending: sellOrders.filter((o) => o.status === "PENDING").length,
              matched: sellOrders.filter((o) => o.status === "MATCHED").length,
              completed: sellOrders.filter((o) => o.status === "COMPLETED").length,
              cancelled: sellOrders.filter((o) => o.status === "CANCELLED").length,
            },
          },
          fees: totalFees,
        },
        wallets: {
          count: wallets.length,
          tokens: wallets.reduce((sum, wallet) => sum + wallet.tokens.length, 0),
          nfts: wallets.reduce((sum, wallet) => sum + wallet.nfts.length, 0),
        },
      };
    } catch (error) {
      logger.error(`Error getting user activity for ${userId}:`, error);
      throw new Error("Failed to get user activity");
    }
  }

  /**
   * Get user analytics
   * @param userId User ID
   * @param period Time period
   * @returns User analytics
   */
  static async getUserAnalytics(userId: string, period: TimePeriod = 'month'): Promise<UserAnalytics> {
    try {
      const { startDate, endDate } = getDateRange(period);

      // Check if the user exists
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count({
        where: {
          userId,
        },
      });

      // Get new carbon credits in the period
      const newCarbonCredits = await db.carbonCredit.count({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        where: {
          userId,
        },
        _count: {
          id: true,
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        where: {
          userId,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits available quantity
      const carbonCreditAvailableQuantity = await db.carbonCredit.aggregate({
        where: {
          userId,
        },
        _sum: {
          availableQuantity: true,
        },
      });

      // Get total buy orders
      const totalBuyOrders = await db.order.count({
        where: {
          buyerId: userId,
        },
      });

      // Get new buy orders in the period
      const newBuyOrders = await db.order.count({
        where: {
          buyerId: userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total sell orders
      const totalSellOrders = await db.order.count({
        where: {
          sellerId: userId,
        },
      });

      // Get new sell orders in the period
      const newSellOrders = await db.order.count({
        where: {
          sellerId: userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get buy orders by status
      const buyOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          buyerId: userId,
        },
        _count: {
          id: true,
        },
      });

      // Get sell orders by status
      const sellOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          sellerId: userId,
        },
        _count: {
          id: true,
        },
      });

      // Get total buy volume
      const buyVolume = await db.order.aggregate({
        where: {
          buyerId: userId,
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total sell volume
      const sellVolume = await db.order.aggregate({
        where: {
          sellerId: userId,
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get daily activity
      const dailyActivity = await db.$queryRaw<{ date: string; carbonCredits: number; buyOrders: number; sellOrders: number }[]>`
        SELECT 
          DATE_TRUNC('day', date) as date,
          SUM(CASE WHEN type = 'carbonCredit' THEN count ELSE 0 END) as "carbonCredits",
          SUM(CASE WHEN type = 'buyOrder' THEN count ELSE 0 END) as "buyOrders",
          SUM(CASE WHEN type = 'sellOrder' THEN count ELSE 0 END) as "sellOrders"
        FROM (
          SELECT 
            "createdAt" as date,
            'carbonCredit' as type,
            COUNT(*) as count
          FROM "CarbonCredit"
          WHERE 
            "userId" = ${userId} AND
            "createdAt" >= ${startDate} AND 
            "createdAt" <= ${endDate}
          GROUP BY "createdAt"
          
          UNION ALL
          
          SELECT 
            "createdAt" as date,
            'buyOrder' as type,
            COUNT(*) as count
          FROM "Order"
          WHERE 
            "buyerId" = ${userId} AND
            "createdAt" >= ${startDate} AND 
            "createdAt" <= ${endDate}
          GROUP BY "createdAt"
          
          UNION ALL
          
          SELECT 
            "createdAt" as date,
            'sellOrder' as type,
            COUNT(*) as count
          FROM "Order"
          WHERE 
            "sellerId" = ${userId} AND
            "createdAt" >= ${startDate} AND 
            "createdAt" <= ${endDate}
          GROUP BY "createdAt"
        ) as activity
        GROUP BY DATE_TRUNC('day', date)
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organization: user.organization ? {
            id: user.organization.id,
            name: user.organization.name,
          } : null,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          new: newCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          availableQuantity: carbonCreditAvailableQuantity._sum.availableQuantity || 0,
        },
        orders: {
          buy: {
            total: totalBuyOrders,
            new: newBuyOrders,
            byStatus: buyOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: buyVolume._sum.quantity || 0,
          },
          sell: {
            total: totalSellOrders,
            new: newSellOrders,
            byStatus: sellOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: sellVolume._sum.quantity || 0,
          },
        },
        activity: {
          daily: dailyActivity,
        },
      };
    } catch (error) {
      logger.error('Error getting user analytics:', error);
      throw error;
    }
  }
}
