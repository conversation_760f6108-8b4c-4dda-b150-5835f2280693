import { NotificationType, NotificationPriority, Prisma } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { sendEmail } from '@/lib/email';
import { EmailTemplateType, getEmailTemplate, getEmailSubject } from '@/lib/email/templates';

/**
 * Notification channel types
 */
export enum NotificationChannel {
  IN_APP = "IN_APP",
  EMAIL = "EMAIL",
  SMS = "SMS",
  PUSH = "PUSH",
}

/**
 * Notification data interface
 */
export interface NotificationData {
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  userId: string;
  organizationId?: string;
  actionUrl?: string;
  actionLabel?: string;
  icon?: string;
  expiresAt?: Date;
  metadata?: any;
  channels?: NotificationChannel[];
}

/**
 * Email notification data interface
 */
interface EmailNotificationData {
  to: string;
  subject: string;
  templateType: EmailTemplateType;
  templateData: Record<string, any>;
}

/**
 * Notification service for managing notifications
 */
export class NotificationService {
  /**
   * Create a new notification
   * @param data Notification data
   * @returns Created notification
   */
  static async createNotification(data: NotificationData) {
    try {
      // Get user with notification preferences
      const user = await db.user.findUnique({
        where: { id: data.userId },
        include: {
          notificationPreferences: true,
        },
      });

      if (!user) {
        throw new Error(`User not found: ${data.userId}`);
      }

      // Determine notification channels
      const channels = data.channels || [NotificationChannel.IN_APP];
      const enabledChannels = this.getEnabledChannels(
        data.type,
        channels,
        user.notificationPreferences
      );

      // Create in-app notification if enabled
      let notification = null;
      if (enabledChannels.includes(NotificationChannel.IN_APP)) {
        notification = await db.notification.create({
          data: {
            title: data.title,
            message: data.message,
            type: data.type,
            priority: data.priority || NotificationPriority.NORMAL,
            user: {
              connect: { id: data.userId }
            },
            ...(data.organizationId && {
              organization: {
                connect: { id: data.organizationId }
              }
            }),
            actionUrl: data.actionUrl,
            actionLabel: data.actionLabel,
            icon: data.icon,
            expiresAt: data.expiresAt,
            metadata: data.metadata as Prisma.JsonObject,
          },
        });

        logger.info(`Created in-app notification: ${notification.id} for user ${data.userId}`);
      }

      // Send email notification if enabled
      if (enabledChannels.includes(NotificationChannel.EMAIL) && user.email) {
        await this.sendEmailNotification({
          type: data.type,
          userId: data.userId,
          userEmail: user.email,
          userName: user.name || "User",
          title: data.title,
          message: data.message,
          metadata: data.metadata,
          actionUrl: data.actionUrl,
          actionLabel: data.actionLabel,
        });

        logger.info(`Sent email notification to user ${data.userId} at ${user.email}`);
      }

      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  }

  /**
   * Send email notification
   * @param params Email notification parameters
   * @returns Success status
   */
  static async sendEmailNotification(params: {
    type: NotificationType | string;
    userId: string;
    userEmail: string;
    userName: string;
    title: string;
    message: string;
    metadata?: Record<string, any>;
    actionUrl?: string;
    actionLabel?: string;
  }) {
    try {
      const { type, userEmail, userName, title, message, metadata, actionUrl, actionLabel } = params;

      // Map notification type to email template type
      const templateType = this.mapNotificationTypeToEmailTemplate(type);

      // Prepare template data
      const templateData = {
        name: userName,
        title,
        message,
        actionUrl,
        actionLabel,
        ...metadata,
      };

      // Get email subject
      const subject = getEmailSubject(templateType, templateData);

      // Send email
      await sendEmail({
        to: userEmail,
        subject,
        html: getEmailTemplate(templateType, templateData),
      });

      logger.info(`Sent email notification to ${userEmail}: ${title}`);
      return true;
    } catch (error) {
      logger.error(`Error sending email notification:`, error);
      throw new Error('Failed to send email notification');
    }
  }

  /**
   * Send push notification
   * @param notificationId Notification ID
   * @returns Updated notification
   */
  static async sendPushNotification(notificationId: string) {
    try {
      // Get notification details
      const notification = await db.notification.findUnique({
        where: { id: notificationId },
        include: { user: true },
      });

      if (!notification) {
        throw new Error('Notification not found');
      }

      // In a real implementation, this would send a push notification using a service like Firebase Cloud Messaging, OneSignal, etc.
      // For now, we'll just log it and update the notification record
      logger.info(`Sending push notification to user ${notification.userId}: ${notification.title}`);

      // Update notification record
      const updatedNotification = await db.notification.update({
        where: { id: notificationId },
        data: {
          pushSent: true,
          pushSentAt: new Date(),
        },
      });

      return updatedNotification;
    } catch (error) {
      logger.error(`Error sending push notification ${notificationId}:`, error);
      throw new Error('Failed to send push notification');
    }
  }

  /**
   * Mark notification as read
   * @param notificationId Notification ID
   * @returns Updated notification
   */
  static async markAsRead(notificationId: string) {
    try {
      const notification = await db.notification.update({
        where: { id: notificationId },
        data: { read: true },
      });

      logger.info(`Marked notification ${notificationId} as read`);

      return notification;
    } catch (error) {
      logger.error(`Error marking notification ${notificationId} as read:`, error);
      throw new Error('Failed to mark notification as read');
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId User ID
   * @returns Number of notifications marked as read
   */
  static async markAllAsRead(userId: string) {
    try {
      const result = await db.notification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: { read: true },
      });

      logger.info(`Marked ${result.count} notifications as read for user ${userId}`);

      return result.count;
    } catch (error) {
      logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      throw new Error('Failed to mark all notifications as read');
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param options Query options
   * @returns Notifications
   */
  static async getNotifications(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      includeRead?: boolean;
      type?: NotificationType;
      priority?: NotificationPriority;
    } = {}
  ) {
    try {
      const { limit = 10, offset = 0, includeRead = false, type, priority } = options;

      // Build where clause
      const where: Prisma.NotificationWhereInput = {
        userId,
        ...(includeRead ? {} : { read: false }),
        ...(type ? { type } : {}),
        ...(priority ? { priority } : {}),
      };

      // Get notifications
      const notifications = await db.notification.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      });

      // Get total count
      const totalCount = await db.notification.count({ where });

      return {
        notifications,
        totalCount,
        hasMore: offset + limit < totalCount,
      };
    } catch (error) {
      logger.error(`Error getting notifications for user ${userId}:`, error);
      throw new Error('Failed to get notifications');
    }
  }

  /**
   * Get notification preferences for a user
   * @param userId User ID
   * @returns Notification preferences
   */
  static async getPreferences(userId: string) {
    try {
      // Get user preferences or create default if not exists
      let preferences = await db.notificationPreference.findUnique({
        where: { userId },
      });

      if (!preferences) {
        preferences = await db.notificationPreference.create({
          data: {
            user: { connect: { id: userId } },
            email: true,
            push: true,
            inApp: true,
          },
        });
      }

      return preferences;
    } catch (error) {
      logger.error(`Error getting notification preferences for user ${userId}:`, error);
      throw new Error('Failed to get notification preferences');
    }
  }

  /**
   * Update notification preferences for a user
   * @param userId User ID
   * @param data Preference data
   * @returns Updated preferences
   */
  static async updatePreferences(
    userId: string,
    data: {
      email?: boolean;
      push?: boolean;
      inApp?: boolean;
      types?: Record<NotificationType, boolean>;
    }
  ) {
    try {
      // Get current preferences
      const currentPreferences = await this.getPreferences(userId);

      // Update preferences
      const updatedPreferences = await db.notificationPreference.update({
        where: { id: currentPreferences.id },
        data: {
          ...(data.email !== undefined ? { email: data.email } : {}),
          ...(data.push !== undefined ? { push: data.push } : {}),
          ...(data.inApp !== undefined ? { inApp: data.inApp } : {}),
          ...(data.types ? { types: data.types as Prisma.JsonObject } : {}),
        },
      });

      logger.info(`Updated notification preferences for user ${userId}`);

      return updatedPreferences;
    } catch (error) {
      logger.error(`Error updating notification preferences for user ${userId}:`, error);
      throw new Error('Failed to update notification preferences');
    }
  }

  /**
   * Delete a notification
   * @param notificationId Notification ID
   * @returns Deleted notification
   */
  static async deleteNotification(notificationId: string) {
    try {
      const notification = await db.notification.delete({
        where: { id: notificationId },
      });

      logger.info(`Deleted notification ${notificationId}`);

      return notification;
    } catch (error) {
      logger.error(`Error deleting notification ${notificationId}:`, error);
      throw new Error('Failed to delete notification');
    }
  }

  /**
   * Create notifications for multiple users
   * @param userIds User IDs
   * @param data Notification data without userId
   * @returns Created notifications
   */
  static async createNotificationsForUsers(userIds: string[], data: Omit<NotificationData, 'userId'>) {
    try {
      const notifications = [];

      for (const userId of userIds) {
        const notification = await this.createNotification({
          ...data,
          userId,
        });

        if (notification) {
          notifications.push(notification);
        }
      }

      return notifications;
    } catch (error) {
      logger.error('Error creating notifications for multiple users:', error);
      throw new Error('Failed to create notifications for multiple users');
    }
  }

  /**
   * Create organization-wide notification
   * @param organizationId Organization ID
   * @param data Notification data without userId
   * @param excludeUserIds User IDs to exclude
   * @returns Created notifications
   */
  static async createOrganizationNotification(
    organizationId: string,
    data: Omit<NotificationData, 'userId'>,
    excludeUserIds: string[] = []
  ) {
    try {
      // Get all users in the organization
      const users = await db.user.findMany({
        where: {
          organizationId,
          id: {
            notIn: excludeUserIds,
          },
        },
        select: {
          id: true,
        },
      });

      const userIds = users.map((user) => user.id);

      // Create notifications for all users
      return this.createNotificationsForUsers(userIds, {
        ...data,
        organizationId,
      });
    } catch (error) {
      logger.error(`Error creating organization notification for ${organizationId}:`, error);
      throw new Error('Failed to create organization notification');
    }
  }

  /**
   * Delete expired notifications
   * @returns Number of deleted notifications
   */
  static async deleteExpiredNotifications() {
    try {
      const result = await db.notification.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      logger.info(`Deleted ${result.count} expired notifications`);

      return result.count;
    } catch (error) {
      logger.error('Error deleting expired notifications:', error);
      throw new Error('Failed to delete expired notifications');
    }
  }

  /**
   * Map notification type to email template type
   * @param type Notification type
   * @returns Email template type
   */
  private static mapNotificationTypeToEmailTemplate(type: NotificationType | string): EmailTemplateType {
    switch (type) {
      case NotificationType.SYSTEM:
        return EmailTemplateType.ANNOUNCEMENT;
      case "TRANSACTION":
        return EmailTemplateType.TRANSACTION_CONFIRMATION;
      case "CREDIT":
        return EmailTemplateType.CARBON_CREDIT_VERIFICATION;
      case "BILLING":
        return EmailTemplateType.BILLING_INVOICE;
      case "VERIFICATION":
        return EmailTemplateType.VERIFICATION;
      case "SECURITY":
        return EmailTemplateType.PASSWORD_RESET;
      default:
        return EmailTemplateType.ANNOUNCEMENT;
    }
  }

  /**
   * Get enabled notification channels based on user preferences
   * @param type Notification type
   * @param channels Requested channels
   * @param preferences User notification preferences
   * @returns Enabled channels
   */
  private static getEnabledChannels(
    type: NotificationType | string,
    channels: NotificationChannel[],
    preferences: any
  ): NotificationChannel[] {
    if (!preferences) {
      // Default to in-app only if no preferences
      return channels.includes(NotificationChannel.IN_APP)
        ? [NotificationChannel.IN_APP]
        : [];
    }

    const enabledChannels: NotificationChannel[] = [];

    // Check if in-app notifications are enabled
    if (
      channels.includes(NotificationChannel.IN_APP) &&
      preferences.inAppNotifications &&
      this.isChannelEnabledForType(type, "InApp", preferences)
    ) {
      enabledChannels.push(NotificationChannel.IN_APP);
    }

    // Check if email notifications are enabled
    if (
      channels.includes(NotificationChannel.EMAIL) &&
      preferences.emailNotifications &&
      this.isChannelEnabledForType(type, "Email", preferences)
    ) {
      enabledChannels.push(NotificationChannel.EMAIL);
    }

    return enabledChannels;
  }

  /**
   * Check if a specific channel is enabled for a notification type
   * @param type Notification type
   * @param channelSuffix Channel suffix (Email, InApp)
   * @param preferences User notification preferences
   * @returns Whether the channel is enabled
   */
  private static isChannelEnabledForType(
    type: NotificationType | string,
    channelSuffix: string,
    preferences: any
  ): boolean {
    const preferenceKey = this.getPreferenceKey(type, channelSuffix);
    return preferences[preferenceKey] !== false; // Default to true if preference doesn't exist
  }

  /**
   * Get preference key for a notification type and channel
   * @param type Notification type
   * @param channelSuffix Channel suffix (Email, InApp)
   * @returns Preference key
   */
  private static getPreferenceKey(type: NotificationType | string, channelSuffix: string): string {
    let typePrefix = "";

    switch (type) {
      case "SYSTEM":
        typePrefix = "system";
        break;
      case "TRANSACTION":
        typePrefix = "transaction";
        break;
      case "CREDIT":
        typePrefix = "credit";
        break;
      case "BILLING":
        typePrefix = "billing";
        break;
      case "DOCUMENT":
        typePrefix = "document";
        break;
      case "SECURITY":
        typePrefix = "security";
        break;
      case "MARKETING":
        typePrefix = "marketing";
        break;
      case "VERIFICATION":
        typePrefix = "system"; // Use system preferences for verification
        break;
      default:
        typePrefix = "system"; // Default to system preferences
    }

    return `${typePrefix}${channelSuffix}Enabled`;
  }
}
