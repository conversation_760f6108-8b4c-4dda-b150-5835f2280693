import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { BlockchainClient } from "@/lib/blockchain-client";

/**
 * ERC-1155 ABI for Carbon Credit Token
 */
const CarbonCreditTokenABI = [
  // ERC-1155 standard functions
  "function balanceOf(address account, uint256 id) view returns (uint256)",
  "function balanceOfBatch(address[] accounts, uint256[] ids) view returns (uint256[])",
  "function safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes data)",
  "function safeBatchTransferFrom(address from, address to, uint256[] ids, uint256[] amounts, bytes data)",
  "function setApprovalForAll(address operator, bool approved)",
  "function isApprovedForAll(address account, address operator) view returns (bool)",
  
  // Carbon credit specific functions
  "function mint(address to, uint256 id, uint256 amount, bytes data)",
  "function mintBatch(address to, uint256[] ids, uint256[] amounts, bytes data)",
  "function retire(uint256 id, uint256 amount, string reason, string beneficiary)",
  "function retireBatch(uint256[] ids, uint256[] amounts, string reason, string beneficiary)",
  "function getCarbonCreditData(uint256 id) view returns (string name, string standard, string methodology, uint256 vintage, string location, string projectId, string serialNumber, uint256 totalSupply, uint256 retiredSupply)",
  "function getRetirementRecords(uint256 id) view returns (address[] retirees, uint256[] amounts, string[] reasons, string[] beneficiaries, uint256[] timestamps)",
  
  // Events
  "event TransferSingle(address indexed operator, address indexed from, address indexed to, uint256 id, uint256 value)",
  "event TransferBatch(address indexed operator, address indexed from, address indexed to, uint256[] ids, uint256[] values)",
  "event ApprovalForAll(address indexed account, address indexed operator, bool approved)",
  "event URI(string value, uint256 indexed id)",
  "event CarbonCreditMinted(uint256 indexed id, address indexed to, uint256 amount, string name, string standard, string methodology, uint256 vintage)",
  "event CarbonCreditRetired(uint256 indexed id, address indexed retiree, uint256 amount, string reason, string beneficiary)",
];

/**
 * Carbon Credit Token Contract Interface
 */
export class CarbonCreditToken {
  private contract: ethers.Contract;
  private blockchainClient: BlockchainClient;
  private network: SupportedNetwork;
  private useTestnet: boolean;
  
  /**
   * Create a new Carbon Credit Token contract instance
   * @param contractAddress Contract address
   * @param network Blockchain network
   * @param useTestnet Whether to use testnet
   */
  constructor(
    private contractAddress: string,
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true
  ) {
    this.network = network;
    this.useTestnet = useTestnet;
    this.blockchainClient = new BlockchainClient(network, useTestnet);
    this.contract = new ethers.Contract(
      contractAddress,
      CarbonCreditTokenABI,
      this.blockchainClient.getProvider()
    );
    
    logger.info(`Initialized Carbon Credit Token contract at ${contractAddress} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);
  }
  
  /**
   * Get the balance of a token for an account
   * @param account Account address
   * @param tokenId Token ID
   * @returns Token balance
   */
  async balanceOf(account: string, tokenId: number): Promise<number> {
    try {
      const balance = await this.contract.balanceOf(account, tokenId);
      return Number(balance);
    } catch (error) {
      logger.error(`Error getting balance for token ${tokenId} of account ${account}:`, error);
      throw new Error("Failed to get token balance");
    }
  }
  
  /**
   * Get the balances of multiple tokens for multiple accounts
   * @param accounts Account addresses
   * @param tokenIds Token IDs
   * @returns Token balances
   */
  async balanceOfBatch(accounts: string[], tokenIds: number[]): Promise<number[]> {
    try {
      const balances = await this.contract.balanceOfBatch(accounts, tokenIds);
      return balances.map((balance: ethers.BigNumberish) => Number(balance));
    } catch (error) {
      logger.error(`Error getting batch balances:`, error);
      throw new Error("Failed to get batch token balances");
    }
  }
  
  /**
   * Transfer tokens from one account to another
   * @param encryptedKey Encrypted private key of the sender
   * @param from Sender address
   * @param to Recipient address
   * @param tokenId Token ID
   * @param amount Amount to transfer
   * @param data Additional data
   * @returns Transaction details
   */
  async safeTransferFrom(
    encryptedKey: string,
    from: string,
    to: string,
    tokenId: number,
    amount: number,
    data: string = "0x"
  ) {
    try {
      // Create a wallet from the encrypted key
      const wallet = await this.blockchainClient.getWalletFromEncryptedKey(encryptedKey);
      
      // Connect the wallet to the contract
      const connectedContract = this.contract.connect(wallet);
      
      // Estimate gas for the transaction
      const gasEstimate = await connectedContract.safeTransferFrom.estimateGas(
        from,
        to,
        tokenId,
        amount,
        data
      );
      
      // Add a buffer to the gas estimate
      const gasLimit = Math.ceil(Number(gasEstimate) * 1.2);
      
      // Get gas price
      const { maxFeePerGas, maxPriorityFeePerGas } = await this.blockchainClient.getGasPrice();
      
      // Send the transaction
      const tx = await connectedContract.safeTransferFrom(
        from,
        to,
        tokenId,
        amount,
        data,
        {
          gasLimit,
          maxFeePerGas,
          maxPriorityFeePerGas,
        }
      );
      
      // Wait for the transaction to be mined
      const receipt = await tx.wait();
      
      logger.info(`Transferred ${amount} of token ${tokenId} from ${from} to ${to} in transaction ${tx.hash}`);
      
      return {
        transactionHash: tx.hash,
        from,
        to,
        tokenId,
        amount,
        blockNumber: receipt.blockNumber,
        status: receipt.status === 1 ? "success" : "failed",
        gasUsed: receipt.gasUsed.toString(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        network: this.network,
        chainId: getNetworkConfig(this.network, this.useTestnet).chainId,
      };
    } catch (error) {
      logger.error(`Error transferring token ${tokenId} from ${from} to ${to}:`, error);
      throw new Error("Failed to transfer token");
    }
  }
  
  /**
   * Mint new carbon credit tokens
   * @param encryptedKey Encrypted private key of the minter
   * @param to Recipient address
   * @param tokenId Token ID
   * @param amount Amount to mint
   * @param data Additional data
   * @returns Transaction details
   */
  async mint(
    encryptedKey: string,
    to: string,
    tokenId: number,
    amount: number,
    data: string = "0x"
  ) {
    try {
      // Create a wallet from the encrypted key
      const wallet = await this.blockchainClient.getWalletFromEncryptedKey(encryptedKey);
      
      // Connect the wallet to the contract
      const connectedContract = this.contract.connect(wallet);
      
      // Estimate gas for the transaction
      const gasEstimate = await connectedContract.mint.estimateGas(
        to,
        tokenId,
        amount,
        data
      );
      
      // Add a buffer to the gas estimate
      const gasLimit = Math.ceil(Number(gasEstimate) * 1.2);
      
      // Get gas price
      const { maxFeePerGas, maxPriorityFeePerGas } = await this.blockchainClient.getGasPrice();
      
      // Send the transaction
      const tx = await connectedContract.mint(
        to,
        tokenId,
        amount,
        data,
        {
          gasLimit,
          maxFeePerGas,
          maxPriorityFeePerGas,
        }
      );
      
      // Wait for the transaction to be mined
      const receipt = await tx.wait();
      
      logger.info(`Minted ${amount} of token ${tokenId} to ${to} in transaction ${tx.hash}`);
      
      return {
        transactionHash: tx.hash,
        to,
        tokenId,
        amount,
        blockNumber: receipt.blockNumber,
        status: receipt.status === 1 ? "success" : "failed",
        gasUsed: receipt.gasUsed.toString(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        network: this.network,
        chainId: getNetworkConfig(this.network, this.useTestnet).chainId,
      };
    } catch (error) {
      logger.error(`Error minting ${amount} of token ${tokenId} to ${to}:`, error);
      throw new Error("Failed to mint token");
    }
  }
  
  /**
   * Retire carbon credit tokens
   * @param encryptedKey Encrypted private key of the retiree
   * @param tokenId Token ID
   * @param amount Amount to retire
   * @param reason Reason for retirement
   * @param beneficiary Beneficiary of the retirement
   * @returns Transaction details
   */
  async retire(
    encryptedKey: string,
    tokenId: number,
    amount: number,
    reason: string,
    beneficiary: string
  ) {
    try {
      // Create a wallet from the encrypted key
      const wallet = await this.blockchainClient.getWalletFromEncryptedKey(encryptedKey);
      
      // Connect the wallet to the contract
      const connectedContract = this.contract.connect(wallet);
      
      // Estimate gas for the transaction
      const gasEstimate = await connectedContract.retire.estimateGas(
        tokenId,
        amount,
        reason,
        beneficiary
      );
      
      // Add a buffer to the gas estimate
      const gasLimit = Math.ceil(Number(gasEstimate) * 1.2);
      
      // Get gas price
      const { maxFeePerGas, maxPriorityFeePerGas } = await this.blockchainClient.getGasPrice();
      
      // Send the transaction
      const tx = await connectedContract.retire(
        tokenId,
        amount,
        reason,
        beneficiary,
        {
          gasLimit,
          maxFeePerGas,
          maxPriorityFeePerGas,
        }
      );
      
      // Wait for the transaction to be mined
      const receipt = await tx.wait();
      
      logger.info(`Retired ${amount} of token ${tokenId} in transaction ${tx.hash}`);
      
      return {
        transactionHash: tx.hash,
        tokenId,
        amount,
        reason,
        beneficiary,
        blockNumber: receipt.blockNumber,
        status: receipt.status === 1 ? "success" : "failed",
        gasUsed: receipt.gasUsed.toString(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        network: this.network,
        chainId: getNetworkConfig(this.network, this.useTestnet).chainId,
      };
    } catch (error) {
      logger.error(`Error retiring ${amount} of token ${tokenId}:`, error);
      throw new Error("Failed to retire token");
    }
  }
  
  /**
   * Get carbon credit data
   * @param tokenId Token ID
   * @returns Carbon credit data
   */
  async getCarbonCreditData(tokenId: number) {
    try {
      const data = await this.contract.getCarbonCreditData(tokenId);
      
      return {
        name: data.name,
        standard: data.standard,
        methodology: data.methodology,
        vintage: Number(data.vintage),
        location: data.location,
        projectId: data.projectId,
        serialNumber: data.serialNumber,
        totalSupply: Number(data.totalSupply),
        retiredSupply: Number(data.retiredSupply),
      };
    } catch (error) {
      logger.error(`Error getting data for token ${tokenId}:`, error);
      throw new Error("Failed to get carbon credit data");
    }
  }
  
  /**
   * Get retirement records for a carbon credit
   * @param tokenId Token ID
   * @returns Retirement records
   */
  async getRetirementRecords(tokenId: number) {
    try {
      const records = await this.contract.getRetirementRecords(tokenId);
      
      const result = [];
      for (let i = 0; i < records.retirees.length; i++) {
        result.push({
          retiree: records.retirees[i],
          amount: Number(records.amounts[i]),
          reason: records.reasons[i],
          beneficiary: records.beneficiaries[i],
          timestamp: new Date(Number(records.timestamps[i]) * 1000),
        });
      }
      
      return result;
    } catch (error) {
      logger.error(`Error getting retirement records for token ${tokenId}:`, error);
      throw new Error("Failed to get retirement records");
    }
  }
  
  /**
   * Estimate gas for a token transfer
   * @param from Sender address
   * @param to Recipient address
   * @param tokenId Token ID
   * @param amount Amount to transfer
   * @returns Gas estimation
   */
  async estimateTransferGas(
    from: string,
    to: string,
    tokenId: number,
    amount: number
  ) {
    try {
      // Estimate gas for the transaction
      const gasEstimate = await this.contract.safeTransferFrom.estimateGas(
        from,
        to,
        tokenId,
        amount,
        "0x"
      );
      
      // Get gas price
      const { maxFeePerGas, maxPriorityFeePerGas } = await this.blockchainClient.getGasPrice();
      
      // Calculate gas cost
      const gasCost = Number(gasEstimate) * Number(maxFeePerGas);
      
      // Convert to ETH/MATIC
      const gasCostInEth = ethers.formatEther(gasCost.toString());
      
      return {
        gasEstimate: gasEstimate.toString(),
        maxFeePerGas: maxFeePerGas.toString(),
        maxPriorityFeePerGas: maxPriorityFeePerGas.toString(),
        gasCost: gasCostInEth,
        network: this.network,
        chainId: getNetworkConfig(this.network, this.useTestnet).chainId,
      };
    } catch (error) {
      logger.error(`Error estimating gas for token transfer:`, error);
      throw new Error("Failed to estimate gas");
    }
  }
}

/**
 * Create a new Carbon Credit Token contract instance
 * @param contractAddress Contract address
 * @param network Blockchain network
 * @param useTestnet Whether to use testnet
 * @returns Carbon Credit Token contract instance
 */
export function createCarbonCreditToken(
  contractAddress: string,
  network: SupportedNetwork = SupportedNetwork.POLYGON,
  useTestnet: boolean = true
): CarbonCreditToken {
  return new CarbonCreditToken(contractAddress, network, useTestnet);
}
