/**
 * Custom Prisma error classes for use in our application.
 * This allows us to handle Prisma errors in a consistent way without
 * importing from @prisma/client/runtime/library which uses node: imports.
 */

/**
 * PrismaClientKnownRequestError is thrown by the Prisma Client for known errors.
 */
export class PrismaClientKnownRequestError extends Error {
  code: string;
  meta?: Record<string, any>;
  clientVersion: string;

  constructor(message: string, { code, meta, clientVersion }: { code: string; meta?: Record<string, any>; clientVersion: string }) {
    super(message);
    this.name = 'PrismaClientKnownRequestError';
    this.code = code;
    this.meta = meta;
    this.clientVersion = clientVersion;
  }
}

/**
 * PrismaClientUnknownRequestError is thrown by the Prisma Client for unknown errors.
 */
export class PrismaClientUnknownRequestError extends Error {
  clientVersion: string;

  constructor(message: string, { clientVersion }: { clientVersion: string }) {
    super(message);
    this.name = 'PrismaClientUnknownRequestError';
    this.clientVersion = clientVersion;
  }
}

/**
 * PrismaClientRustPanicError is thrown by the Prisma Client for Rust panics.
 */
export class PrismaClientRustPanicError extends Error {
  clientVersion: string;

  constructor(message: string, { clientVersion }: { clientVersion: string }) {
    super(message);
    this.name = 'PrismaClientRustPanicError';
    this.clientVersion = clientVersion;
  }
}

/**
 * PrismaClientInitializationError is thrown by the Prisma Client for initialization errors.
 */
export class PrismaClientInitializationError extends Error {
  clientVersion: string;

  constructor(message: string, { clientVersion }: { clientVersion: string }) {
    super(message);
    this.name = 'PrismaClientInitializationError';
    this.clientVersion = clientVersion;
  }
}

/**
 * PrismaClientValidationError is thrown by the Prisma Client for validation errors.
 */
export class PrismaClientValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PrismaClientValidationError';
  }
}
