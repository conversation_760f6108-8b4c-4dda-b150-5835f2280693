import type {
  ComplianceStatus,
  KycLevel,
  AmlCheckResult,
  KycVerificationResult,
  ComplianceCheckType,
  ComplianceDocument,
  ComplianceDocumentType,
  ComplianceCheck,
  ComplianceCheckResult,
  ComplianceRiskLevel
} from './types';
import { ComplianceService } from './service';
import { KycService } from './kyc';
import { AmlService } from './aml';
import { TaxReportingService } from './tax-reporting';

/**
 * Compliance manager
 */
export class ComplianceManager {
  /**
   * Perform KYC verification for a user
   * @param userId User ID
   * @param organizationId Organization ID
   * @param level KYC level to verify
   * @param documents Verification documents
   * @returns KYC verification result
   */
  static async performKycVerification(
    userId: string,
    organizationId: string,
    level: KycLevel,
    documents: ComplianceDocument[]
  ): Promise<KycVerificationResult> {
    return KycService.performKycVerification(userId, organizationId, level, documents);
  }

  /**
   * Get KYC status for a user
   * @param userId User ID
   * @returns KYC status
   */
  static async getKycStatus(userId: string): Promise<{
    status: ComplianceStatus;
    level: KycLevel;
    expiresAt?: Date;
    documents: ComplianceDocument[];
    lastChecked?: Date;
  }> {
    return KycService.getKycStatus(userId);
  }

  /**
   * Perform AML check for a user or organization
   * @param params Check parameters
   * @returns AML check result
   */
  static async performAmlCheck(params: {
    userId?: string;
    organizationId?: string;
    walletAddress?: string;
    transactionHash?: string;
    amount?: number;
  }): Promise<AmlCheckResult> {
    return AmlService.performAmlCheck(params);
  }

  /**
   * Get AML status for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns AML status
   */
  static async getAmlStatus(userId?: string, organizationId?: string): Promise<{
    status: ComplianceStatus;
    riskLevel: ComplianceRiskLevel;
    lastChecked?: Date;
    expiresAt?: Date;
  }> {
    return AmlService.getAmlStatus(userId, organizationId);
  }

  /**
   * Generate tax report for a user or organization
   * @param params Report parameters
   * @returns Report URL
   */
  static async generateTaxReport(params: {
    userId?: string;
    organizationId?: string;
    year: number;
    quarter?: number;
    format?: 'pdf' | 'csv' | 'xlsx';
  }): Promise<string> {
    return TaxReportingService.generateTaxReport(params);
  }

  /**
   * Get tax reports for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns List of tax reports
   */
  static async getTaxReports(userId?: string, organizationId?: string): Promise<{
    id: string;
    year: number;
    quarter?: number;
    format: string;
    url: string;
    createdAt: Date;
  }[]> {
    return TaxReportingService.getTaxReports(userId, organizationId);
  }

  /**
   * Record a compliance check
   * @param check Compliance check data
   * @returns Created compliance check
   */
  static async recordComplianceCheck(check: Omit<ComplianceCheck, 'id' | 'createdAt'>): Promise<ComplianceCheck> {
    return ComplianceService.recordComplianceCheck(check);
  }

  /**
   * Get compliance checks for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @param type Check type
   * @returns List of compliance checks
   */
  static async getComplianceChecks(
    userId?: string,
    organizationId?: string,
    type?: ComplianceCheckType
  ): Promise<ComplianceCheck[]> {
    return ComplianceService.getComplianceChecks(userId, organizationId, type);
  }

  /**
   * Get required documents for a KYC level
   * @param level KYC level
   * @returns List of required document types
   */
  static getRequiredDocumentsForLevel(level: KycLevel): ComplianceDocumentType[] {
    return KycService.getRequiredDocumentsForLevel(level);
  }
}

// Create a singleton instance
const complianceManager = new ComplianceManager();

// Export the singleton instance
export { complianceManager };

// Export types
export type {
  ComplianceStatus,
  KycLevel,
  AmlCheckResult,
  KycVerificationResult,
  ComplianceCheckType,
  ComplianceDocument,
  ComplianceDocumentType,
  ComplianceCheck,
  ComplianceCheckResult,
  ComplianceRiskLevel
} from './types';
