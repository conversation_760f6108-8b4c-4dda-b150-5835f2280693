import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { auditManager } from "@/lib/audit";
import { ComplianceAuditService } from "@/lib/audit/compliance-audit";
import { notificationService } from "@/lib/notifications";
import { headers } from "next/headers";
import {
  ComplianceStatus,
  KycLevel,
  KycVerificationResult,
  ComplianceDocument,
  ComplianceDocumentType,
  ComplianceCheckResult,
  ComplianceCheckType,
  ComplianceRiskLevel
} from "./types";
import { DocumentValidationService } from "./document-validation";
import { getKycAmlProvider } from "./third-party";
import { Prisma } from "@prisma/client";

/**
 * KYC service
 */
export class KycService {
  /**
   * Perform KYC verification for a user
   * @param userId User ID
   * @param organizationId Organization ID
   * @param level KYC level to verify
   * @param documents Verification documents
   * @returns KYC verification result
   */
  static async performKycVerification(
    userId: string,
    organizationId: string,
    level: KycLevel,
    documents: ComplianceDocument[]
  ): Promise<KycVerificationResult> {
    try {
      logger.info(`Performing KYC verification for user ${userId}, organization ${organizationId}, level ${level}`);

      // Check if user exists
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      // Check if organization matches
      if (user.organizationId !== organizationId) {
        throw new Error(`User ${userId} does not belong to organization ${organizationId}`);
      }

      // Check if required documents are provided
      const requiredDocuments = this.getRequiredDocumentsForLevel(level);
      const providedDocumentTypes = documents.map(doc => doc.type);

      const missingDocuments = requiredDocuments.filter(
        docType => !providedDocumentTypes.includes(docType)
      );

      if (missingDocuments.length > 0) {
        return {
          success: false,
          status: ComplianceStatus.REJECTED,
          level: KycLevel.NONE,
          message: `Missing required documents: ${missingDocuments.join(", ")}`,
        };
      }

      // Save documents to database and validate them
      const savedDocuments = await Promise.all(
        documents.map(async (doc) => {
          // Create the document record first
          const savedDoc = await db.complianceDocument.create({
            data: {
              type: doc.type,
              name: doc.name,
              url: doc.url,
              status: ComplianceStatus.IN_REVIEW,
              notes: doc.notes,
              metadata: doc.metadata as Prisma.JsonObject,
              expiresAt: doc.expiresAt,
              user: { connect: { id: userId } },
              organization: { connect: { id: organizationId } },
            },
          });

          // Validate the document using the enhanced document validation service
          try {
            logger.info(`Validating document ${savedDoc.id} of type ${savedDoc.type}`);

            // Convert to ComplianceDocument type for validation
            const docForValidation: ComplianceDocument = {
              id: savedDoc.id,
              type: savedDoc.type as ComplianceDocumentType,
              name: savedDoc.name,
              url: savedDoc.url,
              status: savedDoc.status as ComplianceStatus,
              notes: savedDoc.notes || undefined,
              metadata: savedDoc.metadata as Record<string, any> || undefined,
              expiresAt: savedDoc.expiresAt || undefined,
              userId: userId,
              organizationId: organizationId,
              createdAt: savedDoc.createdAt,
              updatedAt: savedDoc.updatedAt,
            };

            // Perform document validation
            await DocumentValidationService.validateDocument(docForValidation);

            // Document validation is handled asynchronously and results are stored in the database
            // We don't need to wait for the results here
          } catch (validationError) {
            logger.error(`Error validating document ${savedDoc.id}:`, validationError);
            // Continue with the process even if validation fails
            // The document will be marked for manual review
          }

          return savedDoc;
        })
      );

      // Create or update KYC record
      const existingKyc = await db.kycVerification.findFirst({
        where: {
          userId,
          organizationId,
        },
      });

      const kyc = existingKyc
        ? await db.kycVerification.update({
            where: { id: existingKyc.id },
            data: {
              status: ComplianceStatus.IN_REVIEW,
              level,
              lastChecked: new Date(),
              documents: {
                connect: savedDocuments.map(doc => ({ id: doc.id })),
              },
            },
          })
        : await db.kycVerification.create({
            data: {
              status: ComplianceStatus.IN_REVIEW,
              level,
              lastChecked: new Date(),
              user: { connect: { id: userId } },
              organization: { connect: { id: organizationId } },
              documents: {
                connect: savedDocuments.map(doc => ({ id: doc.id })),
              },
            },
          });

      // Perform identity verification with third-party provider
      let verificationResult;
      try {
        // Get the KYC/AML provider
        const provider = getKycAmlProvider();

        // Convert saved documents to ComplianceDocument type for verification
        const docsForVerification: ComplianceDocument[] = savedDocuments.map(doc => ({
          id: doc.id,
          type: doc.type as ComplianceDocumentType,
          name: doc.name,
          url: doc.url,
          status: doc.status as ComplianceStatus,
          notes: doc.notes || undefined,
          metadata: doc.metadata as Record<string, any> || undefined,
          expiresAt: doc.expiresAt || undefined,
          userId: userId,
          organizationId: organizationId,
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt,
        }));

        // Perform identity verification
        verificationResult = await provider.verifyIdentity(userId, docsForVerification);

        logger.info(`Identity verification for user ${userId} completed with result: ${verificationResult.isVerified ? 'Verified' : 'Not Verified'} (confidence: ${verificationResult.confidence.toFixed(2)})`);

        // Update KYC record with verification result
        await db.kycVerification.update({
          where: { id: kyc.id },
          data: {
            metadata: {
              ...(kyc.metadata as Record<string, any> || {}),
              identityVerification: {
                isVerified: verificationResult.isVerified,
                confidence: verificationResult.confidence,
                referenceId: verificationResult.referenceId,
                details: verificationResult.details,
                errors: verificationResult.errors,
                warnings: verificationResult.warnings,
                verifiedAt: new Date().toISOString(),
                provider: provider.getProviderName(),
              },
            },
          },
        });
      } catch (verificationError) {
        logger.error(`Error performing identity verification for user ${userId}:`, verificationError);
        // Continue with the process even if verification fails
        // The KYC will be marked for manual review
      }

      // Record compliance check
      await db.complianceCheck.create({
        data: {
          type: ComplianceCheckType.KYC,
          result: verificationResult?.isVerified
            ? ComplianceCheckResult.PASS
            : ComplianceCheckResult.MANUAL_REVIEW,
          riskLevel: verificationResult?.isVerified
            ? (verificationResult.confidence > 0.8 ? ComplianceRiskLevel.LOW : ComplianceRiskLevel.MEDIUM)
            : ComplianceRiskLevel.MEDIUM,
          details: {
            level,
            documents: savedDocuments.map(doc => doc.id),
            identityVerification: verificationResult ? {
              isVerified: verificationResult.isVerified,
              confidence: verificationResult.confidence,
              referenceId: verificationResult.referenceId,
            } : undefined,
          } as Prisma.JsonObject,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
        },
      });

      // Get IP address and user agent from headers
      const headersList = headers();
      const ipAddress = headersList.get("x-forwarded-for") ||
                        headersList.get("x-real-ip") ||
                        "unknown";
      const userAgent = headersList.get("user-agent") || "unknown";

      // Create enhanced audit log
      await ComplianceAuditService.logKycVerificationRequest({
        userId,
        organizationId,
        level,
        documentIds: savedDocuments.map(doc => doc.id),
        documentTypes: savedDocuments.map(doc => doc.type as ComplianceDocumentType),
        ipAddress,
        userAgent,
      });

      // Notify admins
      const admins = await db.user.findMany({
        where: {
          role: "ADMIN",
        },
      });

      for (const admin of admins) {
        await notificationService.createNotification({
          userId: admin.id,
          title: "KYC Verification Requested",
          message: `${user.name} from ${user.organization?.name} has requested KYC verification for level ${level}.`,
          type: "COMPLIANCE",
          priority: "HIGH",
          actionUrl: `/admin/compliance/kyc/${kyc.id}`,
          actionLabel: "Review",
        });
      }

      // Return result
      return {
        success: true,
        status: ComplianceStatus.IN_REVIEW,
        level,
        message: "KYC verification request submitted successfully. It will be reviewed by our team.",
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      };
    } catch (error) {
      logger.error(`Error performing KYC verification:`, error);
      throw new Error(`Failed to perform KYC verification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get KYC status for a user
   * @param userId User ID
   * @returns KYC status
   */
  static async getKycStatus(userId: string): Promise<{
    status: ComplianceStatus;
    level: KycLevel;
    expiresAt?: Date;
    documents: ComplianceDocument[];
    lastChecked?: Date;
  }> {
    try {
      // Get KYC verification record
      const kyc = await db.kycVerification.findFirst({
        where: {
          userId,
        },
        include: {
          documents: true,
        },
        orderBy: {
          lastChecked: "desc",
        },
      });

      if (!kyc) {
        return {
          status: ComplianceStatus.PENDING,
          level: KycLevel.NONE,
          documents: [],
        };
      }

      // Check if KYC is expired
      const isExpired = kyc.expiresAt && kyc.expiresAt < new Date();

      // Format documents
      const documents = kyc.documents.map(doc => ({
        id: doc.id,
        type: doc.type as ComplianceDocumentType,
        name: doc.name,
        url: doc.url,
        status: doc.status as ComplianceStatus,
        notes: doc.notes || undefined,
        metadata: doc.metadata as Record<string, any> || undefined,
        expiresAt: doc.expiresAt || undefined,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
      }));

      return {
        status: isExpired ? ComplianceStatus.EXPIRED : kyc.status as ComplianceStatus,
        level: kyc.level as KycLevel,
        expiresAt: kyc.expiresAt || undefined,
        documents,
        lastChecked: kyc.lastChecked || undefined,
      };
    } catch (error) {
      logger.error(`Error getting KYC status:`, error);
      throw new Error(`Failed to get KYC status: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get required documents for a KYC level
   * @param level KYC level
   * @returns List of required document types
   */
  static getRequiredDocumentsForLevel(level: KycLevel): ComplianceDocumentType[] {
    switch (level) {
      case KycLevel.BASIC:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
        ];
      case KycLevel.INTERMEDIATE:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
          ComplianceDocumentType.PROOF_OF_ADDRESS,
        ];
      case KycLevel.ADVANCED:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
          ComplianceDocumentType.PROOF_OF_ADDRESS,
          ComplianceDocumentType.BANK_STATEMENT,
        ];
      case KycLevel.ENTERPRISE:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
          ComplianceDocumentType.PROOF_OF_ADDRESS,
          ComplianceDocumentType.BANK_STATEMENT,
          ComplianceDocumentType.BUSINESS_REGISTRATION,
          ComplianceDocumentType.ARTICLES_OF_INCORPORATION,
          ComplianceDocumentType.DIRECTOR_LIST,
          ComplianceDocumentType.SHAREHOLDER_LIST,
        ];
      default:
        return [];
    }
  }

  /**
   * Perform enhanced risk assessment for a user
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Risk assessment result
   */
  static async performRiskAssessment(
    userId: string,
    organizationId: string
  ): Promise<{
    riskLevel: ComplianceRiskLevel;
    riskScore: number;
    riskFactors: string[];
    recommendations: string[];
  }> {
    try {
      logger.info(`Performing risk assessment for user ${userId}, organization ${organizationId}`);

      // Get user and organization data
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
          kycVerifications: {
            include: {
              documents: true,
            },
            orderBy: {
              lastChecked: "desc",
            },
            take: 1,
          },
          complianceChecks: {
            orderBy: {
              createdAt: "desc",
            },
            take: 10,
          },
          transactions: {
            orderBy: {
              createdAt: "desc",
            },
            take: 20,
          },
        },
      });

      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      // Initialize risk factors and score
      const riskFactors: string[] = [];
      let riskScore = 50; // Start with a neutral score (0-100 scale)

      // 1. Check KYC verification status
      const kyc = user.kycVerifications[0];
      if (!kyc) {
        riskFactors.push("No KYC verification");
        riskScore += 20;
      } else if (kyc.status === ComplianceStatus.REJECTED) {
        riskFactors.push("KYC verification rejected");
        riskScore += 30;
      } else if (kyc.status === ComplianceStatus.EXPIRED) {
        riskFactors.push("KYC verification expired");
        riskScore += 15;
      } else if (kyc.status === ComplianceStatus.APPROVED) {
        // Reduce risk score for approved KYC
        riskScore -= 10;

        // Check KYC level
        if (kyc.level === KycLevel.ADVANCED || kyc.level === KycLevel.ENTERPRISE) {
          riskScore -= 10; // Further reduce for advanced verification
        }
      }

      // 2. Check document verification
      if (kyc) {
        const rejectedDocs = kyc.documents.filter(doc => doc.status === ComplianceStatus.REJECTED);
        if (rejectedDocs.length > 0) {
          riskFactors.push(`${rejectedDocs.length} rejected verification documents`);
          riskScore += 5 * rejectedDocs.length;
        }
      }

      // 3. Check transaction patterns
      const transactions = user.transactions;
      if (transactions.length > 0) {
        // Check for large transactions
        const largeTransactions = transactions.filter(tx => tx.amount > 10000);
        if (largeTransactions.length > 0) {
          riskFactors.push(`${largeTransactions.length} large transactions (>$10,000)`);
          riskScore += 5 * largeTransactions.length;
        }

        // Check for frequent transactions
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentTransactions = transactions.filter(tx => tx.createdAt > oneDayAgo);
        if (recentTransactions.length > 5) {
          riskFactors.push(`High transaction frequency (${recentTransactions.length} in 24h)`);
          riskScore += 5;
        }
      }

      // 4. Check compliance checks
      const complianceChecks = user.complianceChecks;
      const failedChecks = complianceChecks.filter(check => check.result === ComplianceCheckResult.FAIL);
      if (failedChecks.length > 0) {
        riskFactors.push(`${failedChecks.length} failed compliance checks`);
        riskScore += 10 * failedChecks.length;
      }

      // 5. Check organization factors
      const organization = user.organization;
      if (organization) {
        // Check organization age
        const orgAgeMonths = Math.floor((Date.now() - organization.createdAt.getTime()) / (30 * 24 * 60 * 60 * 1000));
        if (orgAgeMonths < 6) {
          riskFactors.push(`New organization (${orgAgeMonths} months old)`);
          riskScore += 10;
        }

        // Check high-risk jurisdictions
        const highRiskJurisdictions = ["Country1", "Country2", "Country3"]; // Replace with actual high-risk countries
        if (organization.country && highRiskJurisdictions.includes(organization.country)) {
          riskFactors.push(`Organization based in high-risk jurisdiction (${organization.country})`);
          riskScore += 20;
        }
      }

      // Ensure risk score is within bounds
      riskScore = Math.max(0, Math.min(100, riskScore));

      // Determine risk level based on score
      let riskLevel: ComplianceRiskLevel;
      if (riskScore < 30) {
        riskLevel = ComplianceRiskLevel.LOW;
      } else if (riskScore < 70) {
        riskLevel = ComplianceRiskLevel.MEDIUM;
      } else {
        riskLevel = ComplianceRiskLevel.HIGH;
      }

      // Generate recommendations
      const recommendations: string[] = [];
      if (!kyc || kyc.status !== ComplianceStatus.APPROVED) {
        recommendations.push("Complete KYC verification");
      }
      if (kyc && kyc.level < KycLevel.ADVANCED && riskScore > 50) {
        recommendations.push("Upgrade to advanced KYC verification");
      }
      if (riskFactors.some(factor => factor.includes("rejected"))) {
        recommendations.push("Resubmit rejected verification documents");
      }
      if (riskFactors.some(factor => factor.includes("large transactions"))) {
        recommendations.push("Provide source of funds documentation for large transactions");
      }

      // Record risk assessment
      await db.complianceCheck.create({
        data: {
          type: ComplianceCheckType.RISK_ASSESSMENT,
          result: riskLevel === ComplianceRiskLevel.HIGH
            ? ComplianceCheckResult.FAIL
            : ComplianceCheckResult.PASS,
          riskLevel,
          details: {
            riskScore,
            riskFactors,
            recommendations,
          } as Prisma.JsonObject,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
        },
      });

      // Create audit log
      await ComplianceAuditService.logRiskAssessment({
        userId,
        organizationId,
        riskLevel,
        riskScore,
        riskFactors,
      });

      // Return result
      return {
        riskLevel,
        riskScore,
        riskFactors,
        recommendations,
      };
    } catch (error) {
      logger.error(`Error performing risk assessment:`, error);
      throw new Error(`Failed to perform risk assessment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Verify a user against sanctions and PEP lists
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Verification result
   */
  static async verifySanctionsAndPep(
    userId: string,
    organizationId: string
  ): Promise<{
    passed: boolean;
    matches: Array<{
      listType: "SANCTIONS" | "PEP" | "ADVERSE_MEDIA";
      listName: string;
      matchScore: number;
      details: Record<string, any>;
    }>;
    referenceId: string;
  }> {
    try {
      logger.info(`Performing sanctions and PEP check for user ${userId}, organization ${organizationId}`);

      // Get user and organization data
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      // Get the KYC/AML provider
      const provider = getKycAmlProvider();

      // Perform sanctions and PEP check
      const result = await provider.checkSanctionsAndPep({
        firstName: user.name?.split(' ')[0] || '',
        lastName: user.name?.split(' ').slice(1).join(' ') || '',
        dateOfBirth: user.dateOfBirth?.toISOString().split('T')[0] || '',
        nationality: user.nationality || '',
        country: user.organization?.country || '',
        email: user.email,
      });

      logger.info(`Sanctions and PEP check for user ${userId} completed with ${result.matches.length} matches`);

      // Determine if the check passed
      const passed = result.matches.length === 0 ||
                    !result.matches.some(match => match.matchScore > 0.8);

      // Record compliance check
      await db.complianceCheck.create({
        data: {
          type: ComplianceCheckType.SANCTIONS_PEP,
          result: passed ? ComplianceCheckResult.PASS : ComplianceCheckResult.FAIL,
          riskLevel: passed ? ComplianceRiskLevel.LOW : ComplianceRiskLevel.HIGH,
          details: {
            matches: result.matches,
            referenceId: result.referenceId,
          } as Prisma.JsonObject,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
        },
      });

      // Create audit log
      await ComplianceAuditService.logSanctionsCheck({
        userId,
        organizationId,
        passed,
        matchCount: result.matches.length,
        referenceId: result.referenceId,
      });

      // If failed, notify compliance team
      if (!passed) {
        const admins = await db.user.findMany({
          where: {
            role: "ADMIN",
          },
        });

        for (const admin of admins) {
          await notificationService.createNotification({
            userId: admin.id,
            title: "Sanctions/PEP Match Alert",
            message: `${user.name} from ${user.organization?.name} has potential sanctions or PEP matches.`,
            type: "COMPLIANCE",
            priority: "CRITICAL",
            actionUrl: `/admin/compliance/users/${userId}`,
            actionLabel: "Review",
          });
        }
      }

      // Return result
      return {
        passed,
        matches: result.matches,
        referenceId: result.referenceId,
      };
    } catch (error) {
      logger.error(`Error performing sanctions and PEP check:`, error);
      throw new Error(`Failed to perform sanctions and PEP check: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
