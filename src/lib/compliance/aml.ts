import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { auditManager } from "@/lib/audit";
import { ComplianceAuditService } from "@/lib/audit/compliance-audit";
import { notificationService } from "@/lib/notifications";
import { headers } from "next/headers";
import {
  ComplianceStatus,
  AmlCheckResult,
  ComplianceCheckType,
  ComplianceCheckResult,
  ComplianceRiskLevel
} from "./types";
import { getKycAmlProvider, ThirdPartyAmlScreeningResult } from "./third-party";
import { Prisma } from "@prisma/client";

/**
 * AML service
 */
export class AmlService {
  /**
   * Perform AML check for a user or organization
   * @param params Check parameters
   * @returns AML check result
   */
  static async performAmlCheck(params: {
    userId?: string;
    organizationId?: string;
    walletAddress?: string;
    transactionHash?: string;
    amount?: number;
  }): Promise<AmlCheckResult> {
    try {
      const { userId, organizationId, walletAddress, transactionHash, amount } = params;

      logger.info(`Performing AML check for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''}${walletAddress ? `wallet ${walletAddress}` : ''}${transactionHash ? `transaction ${transactionHash}` : ''}`);

      // Validate parameters
      if (!userId && !organizationId && !walletAddress && !transactionHash) {
        throw new Error("At least one of userId, organizationId, walletAddress, or transactionHash is required");
      }

      // Get user and organization if IDs are provided
      const user = userId ? await db.user.findUnique({
        where: { id: userId },
      }) : null;

      const organization = organizationId ? await db.organization.findUnique({
        where: { id: organizationId },
      }) : null;

      // Get transaction details if hash is provided
      const transaction = transactionHash ? await db.transaction.findFirst({
        where: { transactionHash },
      }) : null;

      // Get the KYC/AML provider
      const provider = getKycAmlProvider();

      // Prepare screening parameters
      const screeningParams: any = {
        userId,
        organizationId,
        walletAddress,
        transactionHash,
        amount,
      };

      // Add user details if available
      if (user) {
        screeningParams.name = user.name || undefined;
        screeningParams.dateOfBirth = user.dateOfBirth?.toISOString().split('T')[0] || undefined;
        screeningParams.nationality = user.country || undefined;
      }

      // Add organization details if available
      if (organization) {
        screeningParams.name = organization.name;
        screeningParams.address = organization.address || undefined;
        screeningParams.country = organization.country || undefined;
      }

      // Perform AML screening with third-party provider
      let screeningResult: ThirdPartyAmlScreeningResult;
      let riskScore = 0;
      let riskLevel = ComplianceRiskLevel.LOW;
      let checkResult = ComplianceCheckResult.PASS;
      let details: Record<string, any> = {};

      try {
        // Call the third-party provider for AML screening
        screeningResult = await provider.performAmlScreening(screeningParams);

        // Map provider risk level to our risk level
        switch (screeningResult.riskLevel.toUpperCase()) {
          case 'HIGH':
            riskLevel = ComplianceRiskLevel.HIGH;
            break;
          case 'MEDIUM':
            riskLevel = ComplianceRiskLevel.MEDIUM;
            break;
          case 'CRITICAL':
            riskLevel = ComplianceRiskLevel.CRITICAL;
            break;
          default:
            riskLevel = ComplianceRiskLevel.LOW;
        }

        // Determine check result based on screening result
        if (!screeningResult.isPassed) {
          checkResult = ComplianceCheckResult.FAIL;
        } else if (riskLevel === ComplianceRiskLevel.HIGH || riskLevel === ComplianceRiskLevel.CRITICAL) {
          checkResult = ComplianceCheckResult.MANUAL_REVIEW;
        } else {
          checkResult = ComplianceCheckResult.PASS;
        }

        // Calculate risk score (0-100)
        if (riskLevel === ComplianceRiskLevel.CRITICAL) {
          riskScore = 100;
        } else if (riskLevel === ComplianceRiskLevel.HIGH) {
          riskScore = 70 + Math.floor(Math.random() * 30); // 70-99
        } else if (riskLevel === ComplianceRiskLevel.MEDIUM) {
          riskScore = 30 + Math.floor(Math.random() * 40); // 30-69
        } else {
          riskScore = Math.floor(Math.random() * 30); // 0-29
        }

        // Add screening details
        details = {
          providerName: provider.getProviderName(),
          referenceId: screeningResult.referenceId,
          matches: screeningResult.matches,
          errors: screeningResult.errors,
          warnings: screeningResult.warnings,
          riskScore,
          ...screeningParams,
        };

        logger.info(`AML screening completed for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''} with result: ${screeningResult.isPassed ? 'Passed' : 'Failed'}, risk level: ${riskLevel}`);
      } catch (screeningError) {
        // If third-party screening fails, fall back to local screening
        logger.warn(`Third-party AML screening failed, falling back to local screening:`, screeningError);

        // Fallback to local screening
        riskScore = 0;
        riskLevel = ComplianceRiskLevel.LOW;
        checkResult = ComplianceCheckResult.PASS;
        details = { fallbackScreening: true };

        // Check amount (if provided)
        if (amount) {
          if (amount > 100000) {
            riskScore += 50;
            details.highValueTransaction = true;
          } else if (amount > 10000) {
            riskScore += 20;
            details.mediumValueTransaction = true;
          }
        }

        // Check wallet address (if provided)
        if (walletAddress) {
          // Simulate checking against a sanctions list
          const isSanctioned = this.simulateSanctionsCheck(walletAddress);
          if (isSanctioned) {
            riskScore += 100;
            details.sanctionedWallet = true;
          }
        }

        // Determine risk level based on score
        if (riskScore >= 100) {
          riskLevel = ComplianceRiskLevel.CRITICAL;
          checkResult = ComplianceCheckResult.FAIL;
        } else if (riskScore >= 50) {
          riskLevel = ComplianceRiskLevel.HIGH;
          checkResult = ComplianceCheckResult.MANUAL_REVIEW;
        } else if (riskScore >= 20) {
          riskLevel = ComplianceRiskLevel.MEDIUM;
          checkResult = ComplianceCheckResult.PASS;
        } else {
          riskLevel = ComplianceRiskLevel.LOW;
          checkResult = ComplianceCheckResult.PASS;
        }
      }

      // Record compliance check with enhanced details
      const complianceCheck = await db.complianceCheck.create({
        data: {
          type: ComplianceCheckType.AML,
          result: checkResult,
          riskLevel,
          details: {
            riskScore,
            ...details,
            walletAddress,
            transactionHash,
            amount,
            checkDate: new Date().toISOString(),
            thirdPartyProvider: details.providerName || "Local",
            referenceId: details.referenceId,
          } as Prisma.JsonObject,
          ...(userId && { user: { connect: { id: userId } } }),
          ...(organizationId && { organization: { connect: { id: organizationId } } }),
          ...(walletAddress && { walletAddress }),
          ...(transactionHash && { transactionHash }),
        },
      });

      // Create or update AML record with enhanced details
      const existingAml = await db.amlCheck.findFirst({
        where: {
          ...(userId && { userId }),
          ...(organizationId && { organizationId }),
        },
      });

      // Determine expiry date based on risk level
      const expiryDays = riskLevel === ComplianceRiskLevel.LOW
        ? 90 // 90 days for low risk
        : riskLevel === ComplianceRiskLevel.MEDIUM
          ? 60 // 60 days for medium risk
          : 30; // 30 days for high/critical risk

      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiryDays);

      // Determine status based on check result
      const status = checkResult === ComplianceCheckResult.FAIL
        ? ComplianceStatus.REJECTED
        : checkResult === ComplianceCheckResult.MANUAL_REVIEW
          ? ComplianceStatus.IN_REVIEW
          : ComplianceStatus.APPROVED;

      // Prepare common data for update or create
      const amlData = {
        status,
        riskLevel,
        lastChecked: new Date(),
        expiresAt,
        checkMethod: details.providerName ? "THIRD_PARTY" : "LOCAL",
        checkProvider: details.providerName || "Local System",
        referenceId: details.referenceId,
        findings: {
          riskScore,
          matches: details.matches || [],
          warnings: details.warnings || [],
          errors: details.errors || [],
          checkDate: new Date().toISOString(),
          walletAddress,
          transactionHash,
          amount,
        } as Prisma.JsonObject,
        actionTaken: status === ComplianceStatus.REJECTED
          ? "Transaction blocked"
          : status === ComplianceStatus.IN_REVIEW
            ? "Flagged for review"
            : "Approved",
      };

      const aml = existingAml
        ? await db.amlCheck.update({
            where: { id: existingAml.id },
            data: amlData,
          })
        : await db.amlCheck.create({
            data: {
              ...amlData,
              ...(userId && { user: { connect: { id: userId } } }),
              ...(organizationId && { organization: { connect: { id: organizationId } } }),
            },
          });

      // Get IP address and user agent from headers
      const headersList = headers();
      const ipAddress = headersList.get("x-forwarded-for") ||
                        headersList.get("x-real-ip") ||
                        "unknown";
      const userAgent = headersList.get("user-agent") || "unknown";

      // Create enhanced audit log
      await ComplianceAuditService.logAmlCheck({
        userId,
        organizationId,
        walletAddress,
        transactionHash,
        amount,
        checkId: complianceCheck.id,
        riskLevel,
        riskScore,
        result: checkResult,
        details,
        ipAddress,
        userAgent,
      });

      // Create AML alerts for matches if any
      if (details.matches && details.matches.length > 0) {
        await Promise.all(details.matches.map(async (match: any) => {
          // Determine alert risk level based on match score
          const alertRiskLevel = match.score > 0.8
            ? ComplianceRiskLevel.HIGH
            : match.score > 0.6
              ? ComplianceRiskLevel.MEDIUM
              : ComplianceRiskLevel.LOW;

          // Create AML alert
          await db.amlAlert.create({
            data: {
              title: `${match.listType} List Match`,
              description: `${match.matchType} match found for ${match.name} with score ${match.score.toFixed(2)}`,
              riskLevel: alertRiskLevel,
              status: "OPEN",
              source: details.providerName || "Local System",
              relatedEntity: userId ? "USER" : organizationId ? "ORGANIZATION" : walletAddress ? "WALLET" : "TRANSACTION",
              relatedEntityId: userId || organizationId || walletAddress || transactionHash,
              amlCheck: { connect: { id: aml.id } },
            },
          });
        }));
      }

      // If high risk or failed, notify admins
      if (riskLevel === ComplianceRiskLevel.HIGH || riskLevel === ComplianceRiskLevel.CRITICAL) {
        const admins = await db.user.findMany({
          where: {
            role: "ADMIN",
          },
        });

        for (const admin of admins) {
          await notificationService.createNotification({
            userId: admin.id,
            title: "High Risk AML Alert",
            message: `A high risk AML check was performed for ${user?.name || organization?.name || walletAddress || transactionHash}. Risk level: ${riskLevel}.`,
            type: "COMPLIANCE",
            priority: "HIGH",
            actionUrl: `/admin/compliance/aml/${complianceCheck.id}`,
            actionLabel: "Review",
          });
        }
      }

      // Return result
      return {
        success: checkResult !== ComplianceCheckResult.FAIL,
        status: checkResult === ComplianceCheckResult.FAIL
          ? ComplianceStatus.REJECTED
          : checkResult === ComplianceCheckResult.MANUAL_REVIEW
            ? ComplianceStatus.IN_REVIEW
            : ComplianceStatus.APPROVED,
        riskLevel,
        message: this.getAmlResultMessage(checkResult, riskLevel),
        details: {
          riskScore,
          ...details,
        },
        expiresAt: aml.expiresAt,
      };
    } catch (error) {
      logger.error(`Error performing AML check:`, error);
      throw new Error(`Failed to perform AML check: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get AML status for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns AML status
   */
  static async getAmlStatus(userId?: string, organizationId?: string): Promise<{
    status: ComplianceStatus;
    riskLevel: ComplianceRiskLevel;
    lastChecked?: Date;
    expiresAt?: Date;
  }> {
    try {
      // Validate parameters
      if (!userId && !organizationId) {
        throw new Error("Either userId or organizationId is required");
      }

      // Get AML check record
      const aml = await db.amlCheck.findFirst({
        where: {
          ...(userId && { userId }),
          ...(organizationId && { organizationId }),
        },
        orderBy: {
          lastChecked: "desc",
        },
      });

      if (!aml) {
        return {
          status: ComplianceStatus.PENDING,
          riskLevel: ComplianceRiskLevel.LOW,
        };
      }

      // Check if AML check is expired
      const isExpired = aml.expiresAt && aml.expiresAt < new Date();

      return {
        status: isExpired ? ComplianceStatus.EXPIRED : aml.status as ComplianceStatus,
        riskLevel: aml.riskLevel as ComplianceRiskLevel,
        lastChecked: aml.lastChecked || undefined,
        expiresAt: aml.expiresAt || undefined,
      };
    } catch (error) {
      logger.error(`Error getting AML status:`, error);
      throw new Error(`Failed to get AML status: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Simulate sanctions check for a wallet address
   * @param walletAddress Wallet address
   * @returns Whether the wallet is sanctioned
   */
  private static simulateSanctionsCheck(walletAddress: string): boolean {
    // In a real implementation, this would call an external sanctions API
    // For now, we'll simulate a check based on the wallet address

    // Simulate a 1% chance of a wallet being sanctioned
    const isSanctioned = Math.random() < 0.01;

    return isSanctioned;
  }

  /**
   * Get AML result message based on result and risk level
   * @param result Check result
   * @param riskLevel Risk level
   * @returns Result message
   */
  private static getAmlResultMessage(result: ComplianceCheckResult, riskLevel: ComplianceRiskLevel): string {
    if (result === ComplianceCheckResult.FAIL) {
      return "AML check failed. This transaction cannot proceed.";
    }

    if (result === ComplianceCheckResult.MANUAL_REVIEW) {
      return "AML check requires manual review. Our compliance team will review this transaction.";
    }

    switch (riskLevel) {
      case ComplianceRiskLevel.LOW:
        return "AML check passed with low risk.";
      case ComplianceRiskLevel.MEDIUM:
        return "AML check passed with medium risk.";
      case ComplianceRiskLevel.HIGH:
        return "AML check passed but with high risk. Additional monitoring may be applied.";
      case ComplianceRiskLevel.CRITICAL:
        return "AML check identified critical risk. This transaction cannot proceed.";
      default:
        return "AML check completed.";
    }
  }
}
