import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { ComplianceDocumentType, ComplianceStatus } from "@prisma/client";

/**
 * Submit carbon credit for verification
 * @param carbonCreditId Carbon credit ID
 * @param documents Verification documents
 * @param userId User ID submitting the verification
 * @param organizationId Optional organization ID
 * @param notes Optional notes
 * @returns Created documents
 */
export async function submitCarbonCreditVerification(
  carbonCreditId: string,
  documents: {
    type: ComplianceDocumentType;
    name: string;
    url: string;
    notes?: string;
  }[],
  userId: string,
  organizationId?: string,
  notes?: string
) {
  try {
    logger.info(`Submitting carbon credit ${carbonCreditId} for verification`);

    // Check if the carbon credit exists and belongs to the user
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: carbonCreditId,
        userId,
      },
    });

    if (!carbonCredit) {
      throw new Error("Carbon credit not found or you do not have permission to submit it for verification");
    }

    // Create compliance documents for the carbon credit
    const createdDocuments = await Promise.all(
      documents.map(async doc => {
        return db.complianceDocument.create({
          data: {
            type: doc.type,
            name: doc.name,
            url: doc.url,
            status: ComplianceStatus.PENDING,
            notes: doc.notes,
            carbonCredit: {
              connect: {
                id: carbonCreditId,
              },
            },
            user: {
              connect: {
                id: userId,
              },
            },
            ...(organizationId
              ? {
                  organization: {
                    connect: {
                      id: organizationId,
                    },
                  },
                }
              : {}),
          },
        });
      })
    );

    // Create verification history entry
    await db.carbonCreditVerification.create({
      data: {
        status: "PENDING",
        notes: notes || "Submitted for verification",
        carbonCredit: {
          connect: {
            id: carbonCreditId,
          },
        },
        verifier: null,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "VERIFICATION_REQUESTED",
        description: `Carbon credit ${carbonCreditId} submitted for verification`,
        userId,
        organizationId,
        metadata: {
          carbonCreditId,
          documentCount: documents.length,
        },
      },
    });

    // Create notification for compliance officers
    const complianceOfficers = await db.userRole.findMany({
      where: {
        role: {
          name: "COMPLIANCE_OFFICER",
        },
      },
      select: {
        userId: true,
      },
    });

    for (const officer of complianceOfficers) {
      await db.notification.create({
        data: {
          title: "New Verification Request",
          message: `A new carbon credit has been submitted for verification`,
          type: "COMPLIANCE",
          priority: "NORMAL",
          user: {
            connect: {
              id: officer.userId,
            },
          },
          actionUrl: `/compliance/carbon-verification/pending`,
          actionLabel: "Review Request",
        },
      });
    }

    return createdDocuments;
  } catch (error) {
    logger.error(`Error submitting carbon credit for verification:`, error);
    throw new Error("Failed to submit carbon credit for verification");
  }
}

/**
 * Verify carbon credit
 * @param carbonCreditId Carbon credit ID
 * @param status Verification status
 * @param verifierId User ID performing the verification
 * @param options Additional verification options
 * @returns Updated verification status
 */
export async function verifyCarbonCredit(
  carbonCreditId: string,
  status: ComplianceStatus,
  verifierId: string,
  options?: {
    notes?: string;
    rejectionReason?: string;
  }
) {
  try {
    logger.info(`Verifying carbon credit ${carbonCreditId} with status ${status}`);

    // Get the carbon credit
    const carbonCredit = await db.carbonCredit.findUnique({
      where: {
        id: carbonCreditId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!carbonCredit) {
      throw new Error("Carbon credit not found");
    }

    // Update all compliance documents for this carbon credit
    await db.complianceDocument.updateMany({
      where: {
        carbonCreditId,
      },
      data: {
        status,
        verifier: verifierId,
        verificationDate: new Date(),
        rejectionReason: status === ComplianceStatus.REJECTED ? options?.rejectionReason : null,
      },
    });

    // Create verification history entry
    await db.carbonCreditVerification.create({
      data: {
        status: status === ComplianceStatus.APPROVED ? "VERIFIED" : 
               status === ComplianceStatus.REJECTED ? "REJECTED" : "PENDING_UPDATE",
        notes: options?.notes || `Verification ${status.toLowerCase()}`,
        carbonCredit: {
          connect: {
            id: carbonCreditId,
          },
        },
        verifier: verifierId,
      },
    });

    // Update carbon credit status if approved
    if (status === ComplianceStatus.APPROVED) {
      await db.carbonCredit.update({
        where: {
          id: carbonCreditId,
        },
        data: {
          status: "VERIFIED",
        },
      });
    }

    // Create audit log
    await db.auditLog.create({
      data: {
        type: status === ComplianceStatus.APPROVED ? "VERIFICATION_APPROVED" : 
              status === ComplianceStatus.REJECTED ? "VERIFICATION_REJECTED" : "VERIFICATION_UPDATE_REQUESTED",
        description: `Carbon credit ${carbonCreditId} verification ${status.toLowerCase()}`,
        userId: verifierId,
        metadata: {
          carbonCreditId,
          status,
          notes: options?.notes,
          rejectionReason: options?.rejectionReason,
        },
      },
    });

    // Create notification for the carbon credit owner
    await db.notification.create({
      data: {
        title: `Verification ${status === ComplianceStatus.APPROVED ? "Approved" : 
                status === ComplianceStatus.REJECTED ? "Rejected" : "Requires Update"}`,
        message: status === ComplianceStatus.APPROVED ? 
                "Your carbon credit has been verified successfully" : 
                status === ComplianceStatus.REJECTED ? 
                "Your carbon credit verification has been rejected" : 
                "Your carbon credit verification requires additional information",
        type: "COMPLIANCE",
        priority: status === ComplianceStatus.APPROVED ? "NORMAL" : "HIGH",
        user: {
          connect: {
            id: carbonCredit.userId,
          },
        },
        actionUrl: `/carbon-credits/${carbonCreditId}`,
        actionLabel: "View Carbon Credit",
      },
    });

    return {
      status,
      message: `Carbon credit verification ${status.toLowerCase()} successfully`,
    };
  } catch (error) {
    logger.error(`Error verifying carbon credit:`, error);
    throw new Error("Failed to verify carbon credit");
  }
}

/**
 * Get carbon credit verifications
 * @param options Query options
 * @returns Carbon credit verifications
 */
export async function getCarbonCreditVerifications(options: {
  carbonCreditId?: string;
  status?: ComplianceStatus;
  userId?: string;
  isAdmin?: boolean;
  limit?: number;
  offset?: number;
}) {
  try {
    const {
      carbonCreditId,
      status,
      userId,
      isAdmin = false,
      limit = 20,
      offset = 0,
    } = options;

    // Build filter for documents
    let filter: any = {};

    if (carbonCreditId) {
      filter.carbonCreditId = carbonCreditId;
    } else if (isAdmin) {
      // Admin can see all pending verifications
      if (status) {
        filter.status = status;
      }
    } else if (userId) {
      // Regular users can only see their own carbon credits
      filter.carbonCredit = {
        userId,
      };

      if (status) {
        filter.status = status;
      }
    }

    // Get verification documents
    const documents = await db.complianceDocument.findMany({
      where: {
        ...filter,
        type: {
          in: [
            ComplianceDocumentType.PROJECT_DESCRIPTION,
            ComplianceDocumentType.METHODOLOGY,
            ComplianceDocumentType.VERIFICATION_REPORT,
            ComplianceDocumentType.VALIDATION_REPORT,
            ComplianceDocumentType.REGISTRY_CERTIFICATE,
            ComplianceDocumentType.MONITORING_REPORT,
          ],
        },
      },
      include: {
        carbonCredit: {
          select: {
            id: true,
            name: true,
            standard: true,
            vintage: true,
            quantity: true,
            status: true,
            userId: true,
            organizationId: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Group documents by carbon credit
    const verificationsByCredit = documents.reduce((acc: any, doc) => {
      if (!acc[doc.carbonCreditId!]) {
        acc[doc.carbonCreditId!] = {
          carbonCredit: doc.carbonCredit,
          documents: [],
          status: null,
        };
      }
      
      acc[doc.carbonCreditId!].documents.push(doc);
      
      // Use the most recent document status as the overall status
      if (!acc[doc.carbonCreditId!].status || new Date(doc.updatedAt) > new Date(acc[doc.carbonCreditId!].updatedAt)) {
        acc[doc.carbonCreditId!].status = doc.status;
        acc[doc.carbonCreditId!].updatedAt = doc.updatedAt;
      }
      
      return acc;
    }, {});

    // Convert to array
    const verifications = Object.values(verificationsByCredit);

    // Get total count
    const totalCount = Object.keys(verificationsByCredit).length;

    return {
      verifications,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + verifications.length < totalCount,
      },
    };
  } catch (error) {
    logger.error(`Error getting carbon credit verifications:`, error);
    throw new Error("Failed to get carbon credit verifications");
  }
}

/**
 * Get verification history for a carbon credit
 * @param carbonCreditId Carbon credit ID
 * @returns Verification history
 */
export async function getVerificationHistory(carbonCreditId: string) {
  try {
    logger.info(`Getting verification history for carbon credit ${carbonCreditId}`);

    // Get verification history entries
    const history = await db.carbonCreditVerification.findMany({
      where: {
        carbonCreditId,
      },
      include: {
        verifier: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get compliance documents
    const documents = await db.complianceDocument.findMany({
      where: {
        carbonCreditId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      history,
      documents,
    };
  } catch (error) {
    logger.error(`Error getting verification history:`, error);
    throw new Error("Failed to get verification history");
  }
}
