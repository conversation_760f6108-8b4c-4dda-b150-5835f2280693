import { logger } from "@/lib/logger";
import { db } from "@/lib/db";

/**
 * Calculate median value from an array of numbers
 * @param values Array of numbers
 * @returns Median value
 */
function calculateMedian(values: number[]): number {
  if (values.length === 0) return 0;

  // Sort the values
  const sorted = [...values].sort((a, b) => a - b);

  // Get the middle index
  const middle = Math.floor(sorted.length / 2);

  // If the length is odd, return the middle value
  if (sorted.length % 2 === 1) {
    return sorted[middle];
  }

  // If the length is even, return the average of the two middle values
  return (sorted[middle - 1] + sorted[middle]) / 2;
}

/**
 * Generate a compliance report
 * @param reportType Report type
 * @param name Report name
 * @param startDate Start date
 * @param endDate End date
 * @param generatedBy User ID who generated the report
 * @param options Additional report options
 * @returns Generated report
 */
export async function generateComplianceReport(
  reportType: string,
  name: string,
  startDate: Date,
  endDate: Date,
  generatedBy: string,
  options?: {
    description?: string;
    filters?: any;
    format?: string;
    organizationId?: string;
  }
) {
  try {
    logger.info(`Generating ${reportType} compliance report: ${name}`);

    // Generate report data based on report type
    let reportData: any = {};

    if (reportType === "KYC") {
      // Get KYC verification data with enhanced details
      const kycVerifications = await db.kycVerification.findMany({
        where: {
          ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
          updatedAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.filters?.status ? { status: options.filters.status } : {}),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              country: true,
              createdAt: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              country: true,
              registrationNumber: true,
              taxId: true,
              status: true,
            },
          },
          documents: true,
          verificationHistory: true,
        },
      });

      // Get compliance checks related to KYC
      const kycComplianceChecks = await db.complianceCheck.findMany({
        where: {
          type: "KYC",
          checkDate: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
        },
        orderBy: {
          checkDate: "desc",
        },
      });

      // Get document validation statistics
      const documentStats = await db.complianceDocument.groupBy({
        by: ['status', 'type'],
        where: {
          updatedAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
        },
        _count: {
          id: true,
        },
      });

      // Process document statistics
      const documentStatsByType: Record<string, any> = {};
      const documentStatsByStatus: Record<string, number> = {};

      documentStats.forEach(stat => {
        // By type
        if (!documentStatsByType[stat.type]) {
          documentStatsByType[stat.type] = {
            total: 0,
            approved: 0,
            rejected: 0,
            pending: 0,
          };
        }

        documentStatsByType[stat.type].total += stat._count.id;

        if (stat.status === "APPROVED") {
          documentStatsByType[stat.type].approved += stat._count.id;
        } else if (stat.status === "REJECTED") {
          documentStatsByType[stat.type].rejected += stat._count.id;
        } else {
          documentStatsByType[stat.type].pending += stat._count.id;
        }

        // By status
        if (!documentStatsByStatus[stat.status]) {
          documentStatsByStatus[stat.status] = 0;
        }

        documentStatsByStatus[stat.status] += stat._count.id;
      });

      // Calculate verification times
      const verificationTimes = kycVerifications
        .filter(v => v.verificationHistory && v.verificationHistory.length >= 2)
        .map(v => {
          const requestEvent = v.verificationHistory.find(h => h.status === "PENDING" || h.status === "IN_REVIEW");
          const completionEvent = v.verificationHistory.find(h =>
            h.status === "APPROVED" || h.status === "REJECTED" || h.status === "EXPIRED"
          );

          if (requestEvent && completionEvent) {
            const requestTime = new Date(requestEvent.timestamp);
            const completionTime = new Date(completionEvent.timestamp);
            const durationMs = completionTime.getTime() - requestTime.getTime();
            const durationHours = durationMs / (1000 * 60 * 60);

            return {
              kycId: v.id,
              userId: v.userId,
              organizationId: v.organizationId,
              requestTime,
              completionTime,
              durationHours,
              status: completionEvent.status,
            };
          }

          return null;
        })
        .filter(Boolean);

      // Calculate average verification time
      const avgVerificationTimeHours = verificationTimes.length > 0
        ? verificationTimes.reduce((sum, vt) => sum + vt!.durationHours, 0) / verificationTimes.length
        : 0;

      reportData = {
        kycVerifications,
        kycComplianceChecks,
        documentStats: {
          byType: documentStatsByType,
          byStatus: documentStatsByStatus,
        },
        verificationMetrics: {
          verificationTimes,
          avgVerificationTimeHours,
          medianVerificationTimeHours: calculateMedian(verificationTimes.map(vt => vt!.durationHours)),
        },
        summary: {
          total: kycVerifications.length,
          approved: kycVerifications.filter(v => v.status === "APPROVED").length,
          pending: kycVerifications.filter(v => v.status === "PENDING").length,
          rejected: kycVerifications.filter(v => v.status === "REJECTED").length,
          expired: kycVerifications.filter(v => v.status === "EXPIRED").length,
          requiresUpdate: kycVerifications.filter(v => v.status === "REQUIRES_UPDATE").length,
          documentValidation: {
            total: Object.values(documentStatsByStatus).reduce((sum, count) => sum + count, 0),
            approved: documentStatsByStatus["APPROVED"] || 0,
            rejected: documentStatsByStatus["REJECTED"] || 0,
            pending: documentStatsByStatus["PENDING"] || 0,
            inReview: documentStatsByStatus["IN_REVIEW"] || 0,
          },
          avgProcessingTimeHours: avgVerificationTimeHours,
        },
      };
    } else if (reportType === "AML") {
      // Get AML check data with enhanced details
      const amlChecks = await db.amlCheck.findMany({
        where: {
          ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
          updatedAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.filters?.status ? { status: options.filters.status } : {}),
          ...(options?.filters?.riskLevel ? { riskLevel: options.filters.riskLevel } : {}),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              country: true,
              createdAt: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              country: true,
              registrationNumber: true,
              taxId: true,
              status: true,
            },
          },
          amlAlerts: {
            include: {
              amlCheck: true,
            },
          },
        },
      });

      // Get compliance checks related to AML
      const amlComplianceChecks = await db.complianceCheck.findMany({
        where: {
          type: "AML",
          checkDate: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
        },
        orderBy: {
          checkDate: "desc",
        },
      });

      // Get all AML alerts
      const amlAlerts = await db.amlAlert.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          amlCheck: {
            ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
          },
        },
        include: {
          amlCheck: {
            select: {
              id: true,
              status: true,
              riskLevel: true,
              userId: true,
              organizationId: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Get all AML rules
      const amlRules = await db.amlRule.findMany({
        where: {
          amlCheck: {
            ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
          },
        },
        orderBy: {
          priority: "desc",
        },
      });

      // Calculate alert statistics
      const alertsByRiskLevel = {
        LOW: amlAlerts.filter(a => a.riskLevel === "LOW").length,
        MEDIUM: amlAlerts.filter(a => a.riskLevel === "MEDIUM").length,
        HIGH: amlAlerts.filter(a => a.riskLevel === "HIGH").length,
        CRITICAL: amlAlerts.filter(a => a.riskLevel === "CRITICAL").length,
      };

      const alertsByStatus = {
        OPEN: amlAlerts.filter(a => a.status === "OPEN").length,
        IN_PROGRESS: amlAlerts.filter(a => a.status === "IN_PROGRESS").length,
        RESOLVED: amlAlerts.filter(a => a.status === "RESOLVED").length,
        CLOSED: amlAlerts.filter(a => a.status === "CLOSED").length,
        FALSE_POSITIVE: amlAlerts.filter(a => a.status === "FALSE_POSITIVE").length,
      };

      // Calculate average resolution time for alerts
      const resolvedAlerts = amlAlerts.filter(a => a.status === "RESOLVED" || a.status === "CLOSED");
      const alertResolutionTimes = resolvedAlerts
        .filter(a => a.resolvedAt)
        .map(a => {
          const createdAt = new Date(a.createdAt);
          const resolvedAt = new Date(a.resolvedAt!);
          const durationMs = resolvedAt.getTime() - createdAt.getTime();
          const durationHours = durationMs / (1000 * 60 * 60);

          return {
            alertId: a.id,
            createdAt,
            resolvedAt,
            durationHours,
            riskLevel: a.riskLevel,
          };
        });

      const avgAlertResolutionTimeHours = alertResolutionTimes.length > 0
        ? alertResolutionTimes.reduce((sum, art) => sum + art.durationHours, 0) / alertResolutionTimes.length
        : 0;

      // Calculate provider statistics
      const checksByProvider = amlChecks.reduce((acc: Record<string, number>, check) => {
        const provider = check.checkProvider || "Unknown";
        acc[provider] = (acc[provider] || 0) + 1;
        return acc;
      }, {});

      reportData = {
        amlChecks,
        amlComplianceChecks,
        amlAlerts,
        amlRules,
        alertStats: {
          byRiskLevel: alertsByRiskLevel,
          byStatus: alertsByStatus,
          resolutionTimes: alertResolutionTimes,
          avgResolutionTimeHours: avgAlertResolutionTimeHours,
          medianResolutionTimeHours: calculateMedian(alertResolutionTimes.map(art => art.durationHours)),
        },
        providerStats: {
          checksByProvider,
        },
        summary: {
          total: amlChecks.length,
          approved: amlChecks.filter(c => c.status === "APPROVED").length,
          pending: amlChecks.filter(c => c.status === "PENDING").length,
          rejected: amlChecks.filter(c => c.status === "REJECTED").length,
          lowRisk: amlChecks.filter(c => c.riskLevel === "LOW").length,
          mediumRisk: amlChecks.filter(c => c.riskLevel === "MEDIUM").length,
          highRisk: amlChecks.filter(c => c.riskLevel === "HIGH").length,
          criticalRisk: amlChecks.filter(c => c.riskLevel === "CRITICAL").length,
          alertCount: amlAlerts.length,
          openAlertCount: alertsByStatus.OPEN + alertsByStatus.IN_PROGRESS,
          resolvedAlertCount: alertsByStatus.RESOLVED + alertsByStatus.CLOSED,
          falsePositiveCount: alertsByStatus.FALSE_POSITIVE,
          avgAlertResolutionTimeHours,
          providers: Object.keys(checksByProvider).length,
        },
      };
    } else if (reportType === "VERIFICATION") {
      // Get carbon credit verification data
      const carbonCreditVerifications = await db.carbonCreditVerification.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.filters?.status ? { status: options.filters.status } : {}),
        },
        include: {
          carbonCredit: {
            select: {
              id: true,
              name: true,
              standard: true,
              vintage: true,
              quantity: true,
              status: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
              complianceDocuments: true,
            },
          },
        },
      });

      reportData = {
        carbonCreditVerifications,
        summary: {
          total: carbonCreditVerifications.length,
          verified: carbonCreditVerifications.filter(v => v.status === "VERIFIED").length,
          pending: carbonCreditVerifications.filter(v => v.status === "PENDING").length,
          rejected: carbonCreditVerifications.filter(v => v.status === "REJECTED").length,
          pendingUpdate: carbonCreditVerifications.filter(v => v.status === "PENDING_UPDATE").length,
        },
      };
    } else if (reportType === "AUDIT") {
      // Get audit log data
      const auditLogs = await db.auditLog.findMany({
        where: {
          ...(options?.organizationId ? { organizationId: options.organizationId } : {}),
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(options?.filters?.type ? { type: options.filters.type } : {}),
          ...(options?.filters?.userId ? { userId: options.filters.userId } : {}),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Group audit logs by type
      const logsByType = auditLogs.reduce((acc: any, log) => {
        if (!acc[log.type]) {
          acc[log.type] = [];
        }
        acc[log.type].push(log);
        return acc;
      }, {});

      reportData = {
        auditLogs,
        logsByType,
        summary: {
          total: auditLogs.length,
          uniqueUsers: new Set(auditLogs.map(log => log.userId)).size,
          typeCounts: Object.entries(logsByType).map(([type, logs]) => ({
            type,
            count: (logs as any[]).length,
          })),
        },
      };
    } else if (reportType === "CUSTOM") {
      // Custom report logic based on filters
      reportData = {
        filters: options?.filters,
        // Additional custom data would be generated here based on specific requirements
      };
    }

    // Create report record
    const report = await db.complianceReport.create({
      data: {
        name,
        description: options?.description,
        type: reportType,
        format: options?.format || "PDF",
        startDate,
        endDate,
        filters: options?.filters,
        data: reportData,
        generatedBy,
        ...(options?.organizationId
          ? {
              organization: {
                connect: {
                  id: options.organizationId,
                },
              },
            }
          : {}),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "COMPLIANCE_REPORT_GENERATED",
        description: `Generated ${reportType} compliance report: ${name}`,
        userId: generatedBy,
        organizationId: options?.organizationId,
        metadata: {
          reportId: report.id,
          reportType,
          startDate,
          endDate,
        },
      },
    });

    return {
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        format: report.format,
        createdAt: report.createdAt,
        // Include summary data for immediate display
        summary: reportData.summary,
      },
      message: "Compliance report generated successfully",
    };
  } catch (error) {
    logger.error(`Error generating compliance report:`, error);
    throw new Error("Failed to generate compliance report");
  }
}

/**
 * Get compliance reports
 * @param options Query options
 * @returns Compliance reports
 */
export async function getComplianceReports(options: {
  reportType?: string;
  userId?: string;
  organizationId?: string;
  limit?: number;
  offset?: number;
}) {
  try {
    const {
      reportType,
      userId,
      organizationId,
      limit = 20,
      offset = 0,
    } = options;

    // Build filter
    const filter: any = {};

    if (reportType) {
      filter.type = reportType;
    }

    if (userId) {
      filter.generatedBy = userId;
    }

    if (organizationId) {
      filter.organizationId = organizationId;
    }

    // Get reports
    const reports = await db.complianceReport.findMany({
      where: filter,
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get total count
    const totalCount = await db.complianceReport.count({
      where: filter,
    });

    return {
      reports,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + reports.length < totalCount,
      },
    };
  } catch (error) {
    logger.error(`Error getting compliance reports:`, error);
    throw new Error("Failed to get compliance reports");
  }
}

/**
 * Get compliance report by ID
 * @param reportId Report ID
 * @returns Compliance report
 */
export async function getComplianceReportById(reportId: string) {
  try {
    logger.info(`Getting compliance report ${reportId}`);

    // Get report
    const report = await db.complianceReport.findUnique({
      where: {
        id: reportId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!report) {
      throw new Error("Compliance report not found");
    }

    return report;
  } catch (error) {
    logger.error(`Error getting compliance report:`, error);
    throw new Error("Failed to get compliance report");
  }
}

/**
 * Generate compliance dashboard data
 * @param organizationId Organization ID
 * @returns Dashboard data
 */
export async function generateComplianceDashboard(organizationId?: string) {
  try {
    logger.info(`Generating compliance dashboard${organizationId ? ` for organization ${organizationId}` : ''}`);

    // Get KYC verification summary
    const kycVerifications = await db.kycVerification.findMany({
      where: {
        ...(organizationId ? { organizationId } : {}),
      },
    });

    const kycSummary = {
      total: kycVerifications.length,
      approved: kycVerifications.filter(v => v.status === "APPROVED").length,
      pending: kycVerifications.filter(v => v.status === "PENDING").length,
      rejected: kycVerifications.filter(v => v.status === "REJECTED").length,
      expired: kycVerifications.filter(v => v.status === "EXPIRED").length,
      requiresUpdate: kycVerifications.filter(v => v.status === "REQUIRES_UPDATE").length,
    };

    // Get AML check summary
    const amlChecks = await db.amlCheck.findMany({
      where: {
        ...(organizationId ? { organizationId } : {}),
      },
    });

    const amlSummary = {
      total: amlChecks.length,
      approved: amlChecks.filter(c => c.status === "APPROVED").length,
      pending: amlChecks.filter(c => c.status === "PENDING").length,
      rejected: amlChecks.filter(c => c.status === "REJECTED").length,
      lowRisk: amlChecks.filter(c => c.riskLevel === "LOW").length,
      mediumRisk: amlChecks.filter(c => c.riskLevel === "MEDIUM").length,
      highRisk: amlChecks.filter(c => c.riskLevel === "HIGH").length,
      criticalRisk: amlChecks.filter(c => c.riskLevel === "CRITICAL").length,
    };

    // Get carbon credit verification summary
    const carbonCreditVerifications = await db.carbonCreditVerification.findMany({
      where: {
        carbonCredit: {
          ...(organizationId ? { organizationId } : {}),
        },
      },
    });

    const verificationSummary = {
      total: carbonCreditVerifications.length,
      verified: carbonCreditVerifications.filter(v => v.status === "VERIFIED").length,
      pending: carbonCreditVerifications.filter(v => v.status === "PENDING").length,
      rejected: carbonCreditVerifications.filter(v => v.status === "REJECTED").length,
      pendingUpdate: carbonCreditVerifications.filter(v => v.status === "PENDING_UPDATE").length,
    };

    // Get recent reports
    const recentReports = await db.complianceReport.findMany({
      where: {
        ...(organizationId ? { organizationId } : {}),
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    // Get recent audit logs
    const recentAuditLogs = await db.auditLog.findMany({
      where: {
        ...(organizationId ? { organizationId } : {}),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    return {
      kycSummary,
      amlSummary,
      verificationSummary,
      recentReports,
      recentAuditLogs,
    };
  } catch (error) {
    logger.error(`Error generating compliance dashboard:`, error);
    throw new Error("Failed to generate compliance dashboard");
  }
}
