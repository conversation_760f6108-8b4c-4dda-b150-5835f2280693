import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { auditManager } from "@/lib/audit";
import { ComplianceAuditService } from "@/lib/audit/compliance-audit";
import { notificationService } from "@/lib/notifications";
import { headers } from "next/headers";
import { Prisma } from "@prisma/client";

/**
 * Tax reporting service
 */
export class TaxReportingService {
  /**
   * Generate tax report for a user or organization
   * @param params Report parameters
   * @returns Report URL
   */
  static async generateTaxReport(params: {
    userId?: string;
    organizationId?: string;
    year: number;
    quarter?: number;
    format?: 'pdf' | 'csv' | 'xlsx';
  }): Promise<string> {
    try {
      const { userId, organizationId, year, quarter, format = 'pdf' } = params;

      logger.info(`Generating tax report for ${userId ? `user ${userId}` : ''}${organizationId ? `organization ${organizationId}` : ''}, year ${year}${quarter ? `, quarter ${quarter}` : ''}, format ${format}`);

      // Validate parameters
      if (!userId && !organizationId) {
        throw new Error("Either userId or organizationId is required");
      }

      // Get user and organization if IDs are provided
      const user = userId ? await db.user.findUnique({
        where: { id: userId },
      }) : null;

      const organization = organizationId ? await db.organization.findUnique({
        where: { id: organizationId },
      }) : null;

      if (userId && !user) {
        throw new Error(`User not found: ${userId}`);
      }

      if (organizationId && !organization) {
        throw new Error(`Organization not found: ${organizationId}`);
      }

      // Calculate date range for the report
      const { startDate, endDate } = this.getDateRangeForReport(year, quarter);

      // Get transactions for the date range
      const transactions = await db.transaction.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(userId && {
            wallet: {
              userId,
            },
          }),
          ...(organizationId && {
            wallet: {
              organizationId,
            },
          }),
        },
        include: {
          wallet: true,
          order: {
            include: {
              carbonCredit: true,
            },
          },
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      // Get carbon credit transactions
      const carbonCreditTransactions = await db.carbonCreditTransaction.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(userId && { userId }),
          ...(organizationId && { organizationId }),
        },
        include: {
          carbonCredit: true,
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      // Generate report data
      const reportData = {
        user: user ? {
          id: user.id,
          name: user.name,
          email: user.email,
        } : null,
        organization: organization ? {
          id: organization.id,
          name: organization.name,
          legalName: organization.legalName,
          taxId: organization.taxId,
        } : null,
        period: {
          year,
          quarter,
          startDate,
          endDate,
        },
        transactions: transactions.map(tx => ({
          id: tx.id,
          type: tx.type,
          amount: tx.amount,
          fee: tx.fee,
          status: tx.status,
          createdAt: tx.createdAt,
          wallet: {
            id: tx.wallet.id,
            address: tx.wallet.address,
            network: tx.wallet.network,
          },
          order: tx.order ? {
            id: tx.order.id,
            type: tx.order.type,
            quantity: tx.order.quantity,
            price: tx.order.price,
            carbonCredit: tx.order.carbonCredit ? {
              id: tx.order.carbonCredit.id,
              name: tx.order.carbonCredit.name,
              vintage: tx.order.carbonCredit.vintage,
              standard: tx.order.carbonCredit.standard,
              methodology: tx.order.carbonCredit.methodology,
            } : null,
          } : null,
        })),
        carbonCreditTransactions: carbonCreditTransactions.map(tx => ({
          id: tx.id,
          type: tx.type,
          quantity: tx.quantity,
          price: tx.price,
          createdAt: tx.createdAt,
          carbonCredit: {
            id: tx.carbonCredit.id,
            name: tx.carbonCredit.name,
            vintage: tx.carbonCredit.vintage,
            standard: tx.carbonCredit.standard,
            methodology: tx.carbonCredit.methodology,
          },
        })),
      };

      // In a real implementation, this would generate a PDF, CSV, or XLSX file
      // For now, we'll simulate generating a report and returning a URL

      // Create a tax report record
      const taxReport = await db.taxReport.create({
        data: {
          year,
          quarter,
          format,
          status: "COMPLETED",
          data: reportData as Prisma.JsonObject,
          ...(userId && { user: { connect: { id: userId } } }),
          ...(organizationId && { organization: { connect: { id: organizationId } } }),
        },
      });

      // Generate a URL for the report
      const reportUrl = `/api/tax-reports/${taxReport.id}/download?format=${format}`;

      // Update the report with the URL
      await db.taxReport.update({
        where: { id: taxReport.id },
        data: {
          url: reportUrl,
        },
      });

      // Get IP address and user agent from headers
      const headersList = headers();
      const ipAddress = headersList.get("x-forwarded-for") ||
                        headersList.get("x-real-ip") ||
                        "unknown";
      const userAgent = headersList.get("user-agent") || "unknown";

      // Create enhanced audit log
      await ComplianceAuditService.logTaxReportGeneration({
        userId,
        organizationId,
        reportId: taxReport.id,
        year,
        quarter,
        format,
        ipAddress,
        userAgent,
      });

      // Notify user
      if (userId) {
        await notificationService.createNotification({
          userId,
          title: "Tax Report Generated",
          message: `Your tax report for ${year}${quarter ? `, Q${quarter}` : ''} is now available.`,
          type: "TAX",
          priority: "NORMAL",
          actionUrl: reportUrl,
          actionLabel: "Download",
        });
      }

      return reportUrl;
    } catch (error) {
      logger.error(`Error generating tax report:`, error);
      throw new Error(`Failed to generate tax report: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get tax reports for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns List of tax reports
   */
  static async getTaxReports(userId?: string, organizationId?: string): Promise<{
    id: string;
    year: number;
    quarter?: number;
    format: string;
    url: string;
    createdAt: Date;
  }[]> {
    try {
      // Validate parameters
      if (!userId && !organizationId) {
        throw new Error("Either userId or organizationId is required");
      }

      // Get tax reports
      const taxReports = await db.taxReport.findMany({
        where: {
          ...(userId && { userId }),
          ...(organizationId && { organizationId }),
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return taxReports.map(report => ({
        id: report.id,
        year: report.year,
        quarter: report.quarter || undefined,
        format: report.format,
        url: report.url || `/api/tax-reports/${report.id}/download?format=${report.format}`,
        createdAt: report.createdAt,
      }));
    } catch (error) {
      logger.error(`Error getting tax reports:`, error);
      throw new Error(`Failed to get tax reports: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get date range for a tax report
   * @param year Year
   * @param quarter Quarter (optional)
   * @returns Start and end dates
   */
  private static getDateRangeForReport(year: number, quarter?: number): { startDate: Date; endDate: Date } {
    if (quarter) {
      // Calculate date range for the quarter
      const startMonth = (quarter - 1) * 3;
      const endMonth = startMonth + 3;

      const startDate = new Date(year, startMonth, 1);
      const endDate = new Date(year, endMonth, 0, 23, 59, 59, 999);

      return { startDate, endDate };
    } else {
      // Calculate date range for the year
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 12, 0, 23, 59, 59, 999);

      return { startDate, endDate };
    }
  }
}
