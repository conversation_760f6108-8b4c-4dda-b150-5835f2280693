/**
 * Compliance status
 */
export enum ComplianceStatus {
  PENDING = "PENDING",
  IN_REVIEW = "IN_REVIEW",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  EXPIRED = "EXPIRED"
}

/**
 * KYC level
 */
export enum KycLevel {
  NONE = "NONE",
  BASIC = "BASIC",
  INTERMEDIATE = "INTERMEDIATE",
  ADVANCED = "ADVANCED"
}

/**
 * Compliance risk level
 */
export enum ComplianceRiskLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL"
}

/**
 * Compliance check type
 */
export enum ComplianceCheckType {
  KYC = "KYC",
  AML = "AML",
  TRANSACTION_MONITORING = "TRANSACTION_MONITORING",
  SANCTIONS = "SANCTIONS",
  PEP = "PEP",
  ADVERSE_MEDIA = "ADVERSE_MEDIA"
}

/**
 * Compliance document type
 */
export enum ComplianceDocumentType {
  PASSPORT = "PASSPORT",
  DRIVERS_LICENSE = "DRIVERS_LICENSE",
  NATIONAL_ID = "NATIONAL_ID",
  UTILITY_BILL = "UTILITY_BILL",
  BANK_STATEMENT = "BANK_STATEMENT",
  BUSINESS_REGISTRATION = "BUSINESS_REGISTRATION",
  TAX_CERTIFICATE = "TAX_CERTIFICATE",
  PROOF_OF_ADDRESS = "PROOF_OF_ADDRESS",
  SELFIE = "SELFIE",
  ARTICLES_OF_INCORPORATION = "ARTICLES_OF_INCORPORATION",
  CERTIFICATE_OF_INCORPORATION = "CERTIFICATE_OF_INCORPORATION",
  MEMORANDUM_OF_ASSOCIATION = "MEMORANDUM_OF_ASSOCIATION",
  SHAREHOLDER_REGISTER = "SHAREHOLDER_REGISTER",
  DIRECTOR_REGISTER = "DIRECTOR_REGISTER",
  BENEFICIAL_OWNER_DECLARATION = "BENEFICIAL_OWNER_DECLARATION",
  OTHER = "OTHER"
}

/**
 * Compliance check result
 */
export enum ComplianceCheckResult {
  PASS = "PASS",
  FAIL = "FAIL",
  MANUAL_REVIEW = "MANUAL_REVIEW"
}

/**
 * Compliance document
 */
export interface ComplianceDocument {
  id?: string;
  type: ComplianceDocumentType;
  name: string;
  url: string;
  status: ComplianceStatus;
  notes?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Compliance check
 */
export interface ComplianceCheck {
  id: string;
  type: ComplianceCheckType;
  userId?: string;
  organizationId?: string;
  walletAddress?: string;
  transactionHash?: string;
  result: ComplianceCheckResult;
  riskLevel: ComplianceRiskLevel;
  details?: Record<string, any>;
  createdAt: Date;
}

/**
 * KYC verification result
 */
export interface KycVerificationResult {
  success: boolean;
  status: ComplianceStatus;
  level: KycLevel;
  message: string;
  details?: Record<string, any>;
  expiresAt?: Date;
}

/**
 * AML check result
 */
export interface AmlCheckResult {
  success: boolean;
  status: ComplianceStatus;
  riskLevel: ComplianceRiskLevel;
  message: string;
  details?: Record<string, any>;
  expiresAt?: Date;
}
