import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { ComplianceAuditService } from "@/lib/audit/compliance-audit";
import { headers } from "next/headers";
import {
  ComplianceCheck,
  ComplianceCheckType,
  ComplianceCheckResult,
  ComplianceRiskLevel
} from "./types";
import { Prisma } from "@prisma/client";

/**
 * Compliance service
 */
export class ComplianceService {
  /**
   * Record a compliance check
   * @param check Compliance check data
   * @returns Created compliance check
   */
  static async recordComplianceCheck(check: Omit<ComplianceCheck, 'id' | 'createdAt'>): Promise<ComplianceCheck> {
    try {
      logger.info(`Recording compliance check of type ${check.type}`);

      const complianceCheck = await db.complianceCheck.create({
        data: {
          type: check.type,
          result: check.result,
          riskLevel: check.riskLevel,
          details: check.details as Prisma.JsonObject,
          ...(check.userId && { user: { connect: { id: check.userId } } }),
          ...(check.organizationId && { organization: { connect: { id: check.organizationId } } }),
          ...(check.walletAddress && { walletAddress: check.walletAddress }),
          ...(check.transactionHash && { transactionHash: check.transactionHash }),
        },
      });

      // Get IP address and user agent from headers
      const headersList = headers();
      const ipAddress = headersList.get("x-forwarded-for") ||
                        headersList.get("x-real-ip") ||
                        "unknown";
      const userAgent = headersList.get("user-agent") || "unknown";

      // Create enhanced audit log
      await ComplianceAuditService.logComplianceCheck({
        userId: check.userId,
        organizationId: check.organizationId,
        checkId: complianceCheck.id,
        checkType: check.type as ComplianceCheckType,
        result: check.result,
        riskLevel: check.riskLevel,
        details: check.details,
        walletAddress: check.walletAddress,
        transactionHash: check.transactionHash,
        ipAddress,
        userAgent,
      });

      return {
        id: complianceCheck.id,
        type: complianceCheck.type as ComplianceCheckType,
        userId: complianceCheck.userId || undefined,
        organizationId: complianceCheck.organizationId || undefined,
        walletAddress: complianceCheck.walletAddress || undefined,
        transactionHash: complianceCheck.transactionHash || undefined,
        result: complianceCheck.result as ComplianceCheckResult,
        riskLevel: complianceCheck.riskLevel as ComplianceRiskLevel,
        details: complianceCheck.details as Record<string, any>,
        createdAt: complianceCheck.createdAt,
      };
    } catch (error) {
      logger.error(`Error recording compliance check:`, error);
      throw new Error(`Failed to record compliance check: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get compliance checks for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @param type Check type
   * @returns List of compliance checks
   */
  static async getComplianceChecks(
    userId?: string,
    organizationId?: string,
    type?: ComplianceCheckType
  ): Promise<ComplianceCheck[]> {
    try {
      // Build query
      const where: Prisma.ComplianceCheckWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(type && { type }),
      };

      // Get compliance checks
      const complianceChecks = await db.complianceCheck.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
      });

      return complianceChecks.map(check => ({
        id: check.id,
        type: check.type as ComplianceCheckType,
        userId: check.userId || undefined,
        organizationId: check.organizationId || undefined,
        walletAddress: check.walletAddress || undefined,
        transactionHash: check.transactionHash || undefined,
        result: check.result as ComplianceCheckResult,
        riskLevel: check.riskLevel as ComplianceRiskLevel,
        details: check.details as Record<string, any>,
        createdAt: check.createdAt,
      }));
    } catch (error) {
      logger.error(`Error getting compliance checks:`, error);
      throw new Error(`Failed to get compliance checks: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Check if a transaction is compliant
   * @param params Check parameters
   * @returns Whether the transaction is compliant
   */
  static async isTransactionCompliant(params: {
    userId?: string;
    organizationId?: string;
    walletAddress?: string;
    transactionHash?: string;
    amount?: number;
  }): Promise<boolean> {
    try {
      const { userId, organizationId } = params;

      // Check if user or organization exists
      if (userId) {
        const user = await db.user.findUnique({
          where: { id: userId },
          include: {
            kycVerification: true,
            amlCheck: true,
          },
        });

        if (!user) {
          throw new Error(`User not found: ${userId}`);
        }

        // Check if KYC is approved
        const kycApproved = user.kycVerification?.status === "APPROVED" &&
          (!user.kycVerification.expiresAt || user.kycVerification.expiresAt > new Date());

        // Check if AML is approved
        const amlApproved = user.amlCheck?.status === "APPROVED" &&
          (!user.amlCheck.expiresAt || user.amlCheck.expiresAt > new Date());

        if (!kycApproved || !amlApproved) {
          return false;
        }
      }

      if (organizationId) {
        const organization = await db.organization.findUnique({
          where: { id: organizationId },
          include: {
            kycVerification: true,
            amlCheck: true,
          },
        });

        if (!organization) {
          throw new Error(`Organization not found: ${organizationId}`);
        }

        // Check if KYC is approved
        const kycApproved = organization.kycVerification?.status === "APPROVED" &&
          (!organization.kycVerification.expiresAt || organization.kycVerification.expiresAt > new Date());

        // Check if AML is approved
        const amlApproved = organization.amlCheck?.status === "APPROVED" &&
          (!organization.amlCheck.expiresAt || organization.amlCheck.expiresAt > new Date());

        if (!kycApproved || !amlApproved) {
          return false;
        }
      }

      // If we get here, the transaction is compliant
      return true;
    } catch (error) {
      logger.error(`Error checking transaction compliance:`, error);
      throw new Error(`Failed to check transaction compliance: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
