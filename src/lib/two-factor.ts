import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { authenticator } from "otplib";
import { AuditLogType } from "@prisma/client";
import { cookies } from "next/headers";
import QRCode from "qrcode";

/**
 * Two-factor authentication service
 *
 * This module provides utilities for implementing two-factor authentication
 * using TOTP (Time-based One-Time Password) according to RFC 6238.
 */

// Configure the authenticator
authenticator.options = {
  window: 1, // Allow 1 step before and after the current step (30 seconds window)
};

/**
 * Generate a new TOTP secret for a user
 * @param userId The user ID
 * @param email The user's email
 * @returns The secret and QR code data URL
 */
export async function generateTwoFactorSecret(userId: string, email: string) {
  try {
    // Generate a new secret
    const secret = authenticator.generateSecret();

    // Create a QR code for the secret
    const appName = process.env.APP_NAME || "Carbonix";
    const otpAuthUrl = authenticator.keyuri(email, appName, secret);
    const qrCodeDataUrl = await QRCode.toDataURL(otpAuthUrl);

    // Store the secret in the database
    await db.twoFactorSecret.upsert({
      where: { userId },
      update: { secret },
      create: {
        userId,
        secret,
      },
    });

    // Log the action
    await db.auditLog.create({
      data: {
        type: AuditLogType.TWO_FACTOR_ENABLED,
        description: `Two-factor authentication secret generated for user ${email}`,
        userId,
        ipAddress: (await cookies()).get("ip")?.value,
        userAgent: (await cookies()).get("user-agent")?.value,
      },
    });

    return {
      secret,
      qrCodeDataUrl,
    };
  } catch (error) {
    logger.error("Failed to generate two-factor secret", error);
    throw new Error("Failed to generate two-factor secret");
  }
}

/**
 * Verify a TOTP token
 * @param userId The user ID
 * @param token The token to verify
 * @returns True if the token is valid, false otherwise
 */
export async function verifyTwoFactorToken(userId: string, token: string): Promise<boolean> {
  try {
    // Get the secret from the database
    const twoFactorSecret = await db.twoFactorSecret.findUnique({
      where: { userId },
    });

    if (!twoFactorSecret) {
      logger.warn(`Two-factor secret not found for user ${userId}`);
      return false;
    }

    // Verify the token
    const isValid = authenticator.verify({
      token,
      secret: twoFactorSecret.secret,
    });

    // Log the verification attempt
    await db.auditLog.create({
      data: {
        type: isValid ? AuditLogType.TWO_FACTOR_SUCCESS : AuditLogType.TWO_FACTOR_FAILURE,
        description: `Two-factor authentication ${isValid ? 'succeeded' : 'failed'} for user ${userId}`,
        userId,
        ipAddress: (await cookies()).get("ip")?.value,
        userAgent: (await cookies()).get("user-agent")?.value,
      },
    });

    return isValid;
  } catch (error) {
    logger.error("Failed to verify two-factor token", error);
    return false;
  }
}

/**
 * Enable two-factor authentication for a user
 * @param userId The user ID
 * @returns True if two-factor authentication was enabled, false otherwise
 */
export async function enableTwoFactor(userId: string): Promise<boolean> {
  try {
    // Update the user record
    await db.user.update({
      where: { id: userId },
      data: { twoFactorEnabled: true },
    });

    // Get the user for logging
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    // Log the action
    await db.auditLog.create({
      data: {
        type: AuditLogType.TWO_FACTOR_ENABLED,
        description: `Two-factor authentication enabled for user ${user?.email}`,
        userId,
        ipAddress: (await cookies()).get("ip")?.value,
        userAgent: (await cookies()).get("user-agent")?.value,
      },
    });

    return true;
  } catch (error) {
    logger.error("Failed to enable two-factor authentication", error);
    return false;
  }
}

/**
 * Disable two-factor authentication for a user
 * @param userId The user ID
 * @returns True if two-factor authentication was disabled, false otherwise
 */
export async function disableTwoFactor(userId: string): Promise<boolean> {
  try {
    // Update the user record
    await db.user.update({
      where: { id: userId },
      data: { twoFactorEnabled: false },
    });

    // Delete the secret
    await db.twoFactorSecret.delete({
      where: { userId },
    });

    // Get the user for logging
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    // Log the action
    await db.auditLog.create({
      data: {
        type: AuditLogType.TWO_FACTOR_DISABLED,
        description: `Two-factor authentication disabled for user ${user?.email}`,
        userId,
        ipAddress: (await cookies()).get("ip")?.value,
        userAgent: (await cookies()).get("user-agent")?.value,
      },
    });

    return true;
  } catch (error) {
    logger.error("Failed to disable two-factor authentication", error);
    return false;
  }
}

/**
 * Generate backup codes for a user
 * @param userId The user ID
 * @returns Array of backup codes
 */
export async function generateBackupCodes(userId: string): Promise<string[]> {
  try {
    // Generate 10 random backup codes
    const backupCodes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // Generate a random 8-character code
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      backupCodes.push(code);
    }

    // Store the backup codes in the database
    // TODO: Implement backup codes in the Prisma schema
    // For now, we'll just return the codes without storing them
    logger.warn("Backup codes are not being stored in the database");

    // Get the user for logging
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    // Log the action
    await db.auditLog.create({
      data: {
        type: "BACKUP_CODES_GENERATED" as any, // TODO: Add this to AuditLogType enum
        description: `Backup codes generated for user ${user?.email}`,
        userId,
        ipAddress: (await cookies()).get("ip")?.value,
        userAgent: (await cookies()).get("user-agent")?.value,
      },
    });

    return backupCodes;
  } catch (error) {
    logger.error("Failed to generate backup codes", error);
    throw new Error("Failed to generate backup codes");
  }
}

/**
 * Verify a backup code
 * @param userId The user ID
 * @param code The backup code to verify
 * @returns True if the code is valid, false otherwise
 */
export async function verifyBackupCode(userId: string, code: string): Promise<boolean> {
  try {
    // TODO: Implement backup codes in the Prisma schema
    // For now, we'll just return false as we can't verify codes
    logger.warn("Backup code verification is not implemented");
    return false;

    // Get the user for logging
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    // Log the action
    await db.auditLog.create({
      data: {
        type: "BACKUP_CODE_USED" as any, // TODO: Add this to AuditLogType enum
        description: `Backup code used for user ${user?.email}`,
        userId,
        ipAddress: (await cookies()).get("ip")?.value,
        userAgent: (await cookies()).get("user-agent")?.value,
      },
    });

    return true;
  } catch (error) {
    logger.error("Failed to verify backup code", error);
    return false;
  }
}
