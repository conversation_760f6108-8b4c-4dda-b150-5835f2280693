import { Alchemy, Network, Utils } from "alchemy-sdk";
import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import {
  SupportedNetwork,
  getNetworkConfig,
  DEFAULT_NETWORK,
  USE_TESTNET
} from "@/lib/blockchain-config";
import { ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Blockchain client for interacting with different networks
 */
export class BlockchainClient {
  private alchemy: Alchemy;
  private provider: ethers.JsonRpcProvider;
  private network: SupportedNetwork;
  private useTestnet: boolean;

  /**
   * Create a new blockchain client
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  constructor(network: SupportedNetwork = DEFAULT_NETWORK, useTestnet: boolean = USE_TESTNET) {
    this.network = network;
    this.useTestnet = useTestnet;

    const networkConfig = getNetworkConfig(network, useTestnet);

    // Initialize Alchemy SDK
    this.alchemy = new Alchemy({
      apiKey: process.env.ALCHEMY_API_KEY,
      network: networkConfig.alchemyNetwork,
    });

    // Initialize provider
    this.provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);

    logger.info(`Initialized blockchain client for ${networkConfig.name}`);
  }

  /**
   * Get the current network
   * @returns Current network
   */
  getNetwork(): SupportedNetwork {
    return this.network;
  }

  /**
   * Get the provider
   * @returns Provider
   */
  getProvider(): ethers.JsonRpcProvider {
    return this.provider;
  }

  /**
   * Get the Alchemy SDK instance
   * @returns Alchemy SDK instance
   */
  getAlchemy(): Alchemy {
    return this.alchemy;
  }

  /**
   * Get the network configuration
   * @returns Network configuration
   */
  getNetworkConfig() {
    return getNetworkConfig(this.network, this.useTestnet);
  }

  /**
   * Get the Alchemy instance
   * @returns Alchemy instance
   */
  getAlchemy(): Alchemy {
    return this.alchemy;
  }

  /**
   * Get the provider
   * @returns Provider
   */
  getProvider(): ethers.JsonRpcProvider {
    return this.provider;
  }

  /**
   * Get the network configuration
   * @returns Network configuration
   */
  getNetworkConfig() {
    return getNetworkConfig(this.network, this.useTestnet);
  }

  /**
   * Create a new wallet
   * @returns Object containing wallet address and encrypted private key
   */
  async createWallet() {
    try {
      // Generate a new random wallet
      const wallet = ethers.Wallet.createRandom();
      const address = wallet.address;

      // Encrypt the private key with a password (in production, this would be user-provided)
      const encryptedKey = await wallet.encrypt(process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret");

      logger.info(`Created new wallet with address: ${address} on ${this.network}`);

      return {
        address,
        encryptedKey,
        network: this.network,
        chainId: this.getNetworkConfig().chainId,
        isTestnet: this.useTestnet,
      };
    } catch (error) {
      logger.error("Error creating wallet:", error);
      throw new Error("Failed to create wallet");
    }
  }

  /**
   * Get wallet balance
   * @param address Wallet address
   * @returns Wallet balance in native currency
   */
  async getWalletBalance(address: string) {
    try {
      // Get the balance in wei
      const balanceWei = await this.alchemy.core.getBalance(address);

      // Convert to ETH/MATIC/etc.
      const balanceFormatted = Utils.formatEther(balanceWei);

      logger.info(`Retrieved balance for wallet ${address}: ${balanceFormatted} on ${this.network}`);

      return {
        wei: balanceWei.toString(),
        formatted: balanceFormatted,
      };
    } catch (error) {
      logger.error(`Error getting wallet balance for ${address}:`, error);
      throw new Error("Failed to get wallet balance");
    }
  }

  /**
   * Get a wallet from an encrypted private key
   * @param encryptedKey Encrypted private key
   * @returns Wallet
   */
  async getWalletFromEncryptedKey(encryptedKey: string): Promise<ethers.Wallet> {
    try {
      // Decrypt the private key
      const wallet = await ethers.Wallet.fromEncryptedJson(
        encryptedKey,
        process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
      );

      // Connect the wallet to the provider
      return wallet.connect(this.provider);
    } catch (error) {
      logger.error("Error decrypting private key:", error);
      throw new ApiError("Failed to decrypt private key", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Get current gas price
   * @returns Gas price information
   */
  async getGasPrice() {
    try {
      // Get fee data from the provider
      const feeData = await this.provider.getFeeData();

      // Use EIP-1559 gas pricing if available, otherwise fall back to legacy gas price
      const maxFeePerGas = feeData.maxFeePerGas || feeData.gasPrice;
      const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas || ethers.parseUnits("1", "gwei");

      if (!maxFeePerGas) {
        throw new Error("Failed to get gas price");
      }

      return {
        maxFeePerGas,
        maxPriorityFeePerGas,
        gasPrice: feeData.gasPrice,
        formatted: {
          maxFeePerGas: ethers.formatUnits(maxFeePerGas, "gwei"),
          maxPriorityFeePerGas: ethers.formatUnits(maxPriorityFeePerGas, "gwei"),
          gasPrice: feeData.gasPrice ? ethers.formatUnits(feeData.gasPrice, "gwei") : undefined,
        },
      };
    } catch (error) {
      logger.error("Error getting gas price:", error);
      throw new ApiError("Failed to get gas price", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Estimate gas for a transaction
   * @param tx Transaction request
   * @returns Gas estimation
   */
  async estimateGas(tx: ethers.TransactionRequest): Promise<ethers.BigNumberish> {
    try {
      return await this.provider.estimateGas(tx);
    } catch (error) {
      logger.error("Error estimating gas:", error);
      throw new ApiError("Failed to estimate gas", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Send transaction with gas optimization
   * @param encryptedKey Encrypted private key of the sender
   * @param to Recipient address
   * @param amount Amount to send in ETH/MATIC/etc.
   * @param options Transaction options
   * @returns Transaction details
   */
  async sendTransaction(
    encryptedKey: string,
    to: string,
    amount: string,
    options: {
      gasLimit?: number;
      maxFeePerGas?: string;
      maxPriorityFeePerGas?: string;
      nonce?: number;
      data?: string;
    } = {}
  ) {
    try {
      // Get wallet from encrypted key
      const connectedWallet = await this.getWalletFromEncryptedKey(encryptedKey);

      // Convert amount from ETH/MATIC/etc. to wei
      const amountWei = ethers.parseEther(amount);

      // Get the current gas price if not provided
      let gasPrice;
      let maxFeePerGas;
      let maxPriorityFeePerGas;

      // Get the current fee data
      const feeData = await this.provider.getFeeData();

      // Check if the network supports EIP-1559
      const supportsEIP1559 = feeData.maxFeePerGas !== null && feeData.maxPriorityFeePerGas !== null;

      if (supportsEIP1559) {
        // Use EIP-1559 fee structure
        maxFeePerGas = options.maxFeePerGas
          ? ethers.parseUnits(options.maxFeePerGas, "gwei")
          : feeData.maxFeePerGas;

        maxPriorityFeePerGas = options.maxPriorityFeePerGas
          ? ethers.parseUnits(options.maxPriorityFeePerGas, "gwei")
          : feeData.maxPriorityFeePerGas;

        logger.info(`Using EIP-1559 fees: maxFeePerGas=${ethers.formatUnits(maxFeePerGas || 0, "gwei")} gwei, maxPriorityFeePerGas=${ethers.formatUnits(maxPriorityFeePerGas || 0, "gwei")} gwei`);
      } else {
        // Use legacy fee structure
        gasPrice = feeData.gasPrice;
        logger.info(`Using legacy gas price: ${ethers.formatUnits(gasPrice || 0, "gwei")} gwei`);
      }

      // Get the nonce if not provided
      const nonce = options.nonce !== undefined
        ? options.nonce
        : await this.provider.getTransactionCount(wallet.address);

      // Estimate gas limit if not provided
      const gasLimit = options.gasLimit || await this.provider.estimateGas({
        from: wallet.address,
        to,
        value: amountWei,
        data: options.data || "0x",
      });

      // Add a buffer to the gas limit for safety
      const gasLimitWithBuffer = Math.floor(gasLimit * 1.1);

      // Create transaction object
      const txRequest: any = {
        to,
        value: amountWei,
        nonce,
        data: options.data || "0x",
      };

      // Add gas parameters based on network support
      if (supportsEIP1559) {
        txRequest.maxFeePerGas = maxFeePerGas;
        txRequest.maxPriorityFeePerGas = maxPriorityFeePerGas;
        txRequest.gasLimit = gasLimitWithBuffer;
        txRequest.type = 2; // EIP-1559 transaction
      } else {
        txRequest.gasPrice = gasPrice;
        txRequest.gasLimit = gasLimitWithBuffer;
      }

      // Send the transaction
      const tx = await connectedWallet.sendTransaction(txRequest);

      logger.info(`Transaction sent: ${tx.hash} on ${this.network}`);

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      logger.info(`Transaction confirmed in block ${receipt?.blockNumber}`);

      return {
        hash: tx.hash,
        from: wallet.address,
        to,
        value: amount,
        blockNumber: receipt?.blockNumber,
        gasUsed: receipt?.gasUsed?.toString(),
        effectiveGasPrice: receipt?.gasPrice?.toString(),
        status: receipt?.status === 1 ? "success" : "failed",
        network: this.network,
        chainId: this.getNetworkConfig().chainId,
      };
    } catch (error) {
      logger.error("Error sending transaction:", error);
      throw new ApiError("Failed to send transaction", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Send ERC-20 tokens
   * @param encryptedKey Encrypted private key of the sender
   * @param to Recipient address
   * @param tokenAddress Token contract address
   * @param amount Amount to send
   * @param options Transaction options
   * @returns Transaction details
   */
  async sendToken(
    encryptedKey: string,
    to: string,
    tokenAddress: string,
    amount: string,
    options: {
      gasLimit?: number;
      maxFeePerGas?: string;
      maxPriorityFeePerGas?: string;
      nonce?: number;
    } = {}
  ) {
    try {
      // Get token metadata to determine decimals
      const tokenMetadata = await this.alchemy.core.getTokenMetadata(tokenAddress);
      const decimals = tokenMetadata.decimals || 18;

      // Create ERC-20 interface
      const erc20Interface = new ethers.Interface([
        "function transfer(address to, uint256 amount) returns (bool)",
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
      ]);

      // Encode the transfer function call
      const data = erc20Interface.encodeFunctionData("transfer", [
        to,
        ethers.parseUnits(amount, decimals),
      ]);

      // Send the transaction
      const result = await this.sendTransaction(
        encryptedKey,
        tokenAddress,
        "0", // No ETH value for token transfers
        {
          ...options,
          data,
        }
      );

      return {
        ...result,
        tokenAddress,
        tokenSymbol: tokenMetadata.symbol,
        tokenDecimals: decimals,
        tokenAmount: amount,
      };
    } catch (error) {
      logger.error("Error sending token:", error);
      throw new Error("Failed to send token");
    }
  }

  /**
   * Get gas price estimates
   * @returns Gas price estimates
   */
  async getGasPrices() {
    try {
      const feeData = await this.provider.getFeeData();

      // Check if the network supports EIP-1559
      const supportsEIP1559 = feeData.maxFeePerGas !== null && feeData.maxPriorityFeePerGas !== null;

      if (supportsEIP1559) {
        // EIP-1559 fee structure
        const baseFeePerGas = feeData.lastBaseFeePerGas || ethers.parseUnits("1", "gwei");
        const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas || ethers.parseUnits("1", "gwei");

        // Calculate different priority fee levels
        const slowPriorityFee = maxPriorityFeePerGas * BigInt(8) / BigInt(10); // 80%
        const avgPriorityFee = maxPriorityFeePerGas;
        const fastPriorityFee = maxPriorityFeePerGas * BigInt(12) / BigInt(10); // 120%

        // Calculate max fee = base fee + priority fee
        const slowMaxFee = baseFeePerGas + slowPriorityFee;
        const avgMaxFee = baseFeePerGas + avgPriorityFee;
        const fastMaxFee = baseFeePerGas + fastPriorityFee;

        return {
          eip1559: true,
          slow: {
            maxFeePerGas: ethers.formatUnits(slowMaxFee, "gwei"),
            maxPriorityFeePerGas: ethers.formatUnits(slowPriorityFee, "gwei"),
          },
          average: {
            maxFeePerGas: ethers.formatUnits(avgMaxFee, "gwei"),
            maxPriorityFeePerGas: ethers.formatUnits(avgPriorityFee, "gwei"),
          },
          fast: {
            maxFeePerGas: ethers.formatUnits(fastMaxFee, "gwei"),
            maxPriorityFeePerGas: ethers.formatUnits(fastPriorityFee, "gwei"),
          },
          baseFeePerGas: ethers.formatUnits(baseFeePerGas, "gwei"),
        };
      } else {
        // Legacy fee structure
        const gasPrice = feeData.gasPrice || ethers.parseUnits("1", "gwei");

        return {
          eip1559: false,
          slow: {
            gasPrice: ethers.formatUnits(gasPrice * BigInt(8) / BigInt(10), "gwei"), // 80%
          },
          average: {
            gasPrice: ethers.formatUnits(gasPrice, "gwei"),
          },
          fast: {
            gasPrice: ethers.formatUnits(gasPrice * BigInt(12) / BigInt(10), "gwei"), // 120%
          },
        };
      }
    } catch (error) {
      logger.error("Error getting gas prices:", error);
      throw new Error("Failed to get gas prices");
    }
  }

  /**
   * Estimate gas for a transaction
   * @param from Sender address
   * @param to Recipient address
   * @param value Amount to send in ETH/MATIC/etc.
   * @param data Transaction data
   * @returns Estimated gas
   */
  async estimateGas(from: string, to: string, value: string, data: string = "0x") {
    try {
      const valueWei = ethers.parseEther(value);

      const gasEstimate = await this.provider.estimateGas({
        from,
        to,
        value: valueWei,
        data,
      });

      // Add a buffer for safety
      const gasEstimateWithBuffer = Math.floor(Number(gasEstimate) * 1.1);

      logger.info(`Estimated gas: ${gasEstimateWithBuffer} for transaction from ${from} to ${to}`);

      return gasEstimateWithBuffer;
    } catch (error) {
      logger.error("Error estimating gas:", error);
      throw new Error("Failed to estimate gas");
    }
  }

  /**
   * Get transaction receipt
   * @param txHash Transaction hash
   * @returns Transaction receipt
   */
  async getTransactionReceipt(txHash: string) {
    try {
      const receipt = await this.provider.getTransactionReceipt(txHash);

      if (!receipt) {
        return null;
      }

      return {
        hash: receipt.hash,
        blockNumber: receipt.blockNumber,
        blockHash: receipt.blockHash,
        status: receipt.status === 1 ? "success" : "failed",
        from: receipt.from,
        to: receipt.to,
        gasUsed: receipt.gasUsed.toString(),
        effectiveGasPrice: receipt.gasPrice?.toString(),
        cumulativeGasUsed: receipt.cumulativeGasUsed.toString(),
        logs: receipt.logs.map(log => ({
          address: log.address,
          topics: log.topics,
          data: log.data,
        })),
      };
    } catch (error) {
      logger.error(`Error getting transaction receipt for ${txHash}:`, error);
      throw new ApiError("Failed to get transaction receipt", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Get transaction details
   * @param txHash Transaction hash
   * @returns Transaction details
   */
  async getTransaction(txHash: string) {
    try {
      const tx = await this.provider.getTransaction(txHash);

      if (!tx) {
        return null;
      }

      return {
        hash: tx.hash,
        blockNumber: tx.blockNumber,
        from: tx.from,
        to: tx.to,
        value: ethers.formatEther(tx.value),
        gasLimit: tx.gasLimit.toString(),
        gasPrice: tx.gasPrice ? ethers.formatUnits(tx.gasPrice, "gwei") : undefined,
        maxFeePerGas: tx.maxFeePerGas ? ethers.formatUnits(tx.maxFeePerGas, "gwei") : undefined,
        maxPriorityFeePerGas: tx.maxPriorityFeePerGas ? ethers.formatUnits(tx.maxPriorityFeePerGas, "gwei") : undefined,
        nonce: tx.nonce,
        data: tx.data,
      };
    } catch (error) {
      logger.error(`Error getting transaction details for ${txHash}:`, error);
      throw new ApiError("Failed to get transaction details", ErrorType.INTERNAL, 500);
    }
  }
}

// Create blockchain clients for each network
const blockchainClients: Record<SupportedNetwork, BlockchainClient> = {
  [SupportedNetwork.ETHEREUM]: new BlockchainClient(SupportedNetwork.ETHEREUM),
  [SupportedNetwork.POLYGON]: new BlockchainClient(SupportedNetwork.POLYGON),
  [SupportedNetwork.ARBITRUM]: new BlockchainClient(SupportedNetwork.ARBITRUM),
  [SupportedNetwork.OPTIMISM]: new BlockchainClient(SupportedNetwork.OPTIMISM),
  [SupportedNetwork.BASE]: new BlockchainClient(SupportedNetwork.BASE),
};

/**
 * Get blockchain client for a specific network
 * @param network Network to use
 * @returns Blockchain client
 */
export function getBlockchainClient(network: SupportedNetwork = DEFAULT_NETWORK): BlockchainClient {
  return blockchainClients[network];
}
