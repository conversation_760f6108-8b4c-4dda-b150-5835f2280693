import { 
  PaymentStatus, 
  PaymentMethod, 
  PaymentType, 
  SubscriptionPlan, 
  SubscriptionStatus,
  BillingStatus,
  BillingType
} from '@prisma/client';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { 
  PaymentProvider, 
  Payment, 
  PaymentCreationData,
  PaymentMethodData,
  PaymentMethodCreationData,
  Subscription,
  SubscriptionCreationData,
  BillingRecord,
  BillingRecordCreationData
} from '../types';

// Mock Stripe implementation
// In a real implementation, this would use the Stripe SDK
class StripeClient {
  async createPaymentIntent(data: any): Promise<any> {
    return {
      id: `pi_${Math.random().toString(36).substring(2, 15)}`,
      amount: data.amount,
      currency: data.currency,
      status: 'succeeded',
      payment_method: data.payment_method,
      description: data.description,
      metadata: data.metadata,
    };
  }

  async retrievePaymentIntent(id: string): Promise<any> {
    return {
      id,
      amount: 1000,
      currency: 'usd',
      status: 'succeeded',
      payment_method: 'pm_card_visa',
      description: 'Payment for subscription',
      metadata: {},
    };
  }

  async createPaymentMethod(data: any): Promise<any> {
    return {
      id: `pm_${Math.random().toString(36).substring(2, 15)}`,
      type: data.type,
      card: {
        last4: data.card?.last4 || '4242',
        exp_month: data.card?.exp_month || 12,
        exp_year: data.card?.exp_year || 2025,
        brand: data.card?.brand || 'visa',
      },
      billing_details: {
        name: data.billing_details?.name,
        address: data.billing_details?.address,
      },
      metadata: data.metadata,
    };
  }

  async retrievePaymentMethod(id: string): Promise<any> {
    return {
      id,
      type: 'card',
      card: {
        last4: '4242',
        exp_month: 12,
        exp_year: 2025,
        brand: 'visa',
      },
      billing_details: {
        name: 'John Doe',
        address: {
          line1: '123 Main St',
          city: 'San Francisco',
          state: 'CA',
          postal_code: '94111',
          country: 'US',
        },
      },
      metadata: {},
    };
  }

  async createSubscription(data: any): Promise<any> {
    return {
      id: `sub_${Math.random().toString(36).substring(2, 15)}`,
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
      trial_end: data.trial_end,
      cancel_at: null,
      items: {
        data: [
          {
            price: {
              product: data.plan,
            },
          },
        ],
      },
      metadata: data.metadata,
    };
  }

  async retrieveSubscription(id: string): Promise<any> {
    return {
      id,
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
      trial_end: null,
      cancel_at: null,
      items: {
        data: [
          {
            price: {
              product: 'BUSINESS',
            },
          },
        ],
      },
      metadata: {},
    };
  }

  async cancelSubscription(id: string): Promise<any> {
    return {
      id,
      status: 'canceled',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
      trial_end: null,
      cancel_at: Math.floor(Date.now() / 1000),
      items: {
        data: [
          {
            price: {
              product: 'BUSINESS',
            },
          },
        ],
      },
      metadata: {},
    };
  }

  async createInvoice(data: any): Promise<any> {
    return {
      id: `in_${Math.random().toString(36).substring(2, 15)}`,
      amount_due: data.amount,
      currency: data.currency,
      status: 'open',
      description: data.description,
      due_date: Math.floor(data.due_date / 1000),
      customer: data.customer,
      subscription: data.subscription,
      number: `INV-${Math.floor(Math.random() * 10000)}`,
      hosted_invoice_url: `https://example.com/invoice/${Math.random().toString(36).substring(2, 15)}`,
      metadata: data.metadata,
    };
  }

  async retrieveInvoice(id: string): Promise<any> {
    return {
      id,
      amount_due: 1000,
      currency: 'usd',
      status: 'open',
      description: 'Invoice for subscription',
      due_date: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,
      customer: 'cus_123',
      subscription: 'sub_123',
      number: 'INV-1234',
      hosted_invoice_url: 'https://example.com/invoice/123',
      metadata: {},
    };
  }
}

/**
 * Stripe payment provider
 */
export class StripeProvider implements PaymentProvider {
  private client: StripeClient;
  name: string = 'stripe';

  constructor() {
    this.client = new StripeClient();
  }

  /**
   * Create a payment
   * @param data Payment creation data
   * @returns Created payment
   */
  async createPayment(data: PaymentCreationData): Promise<Payment> {
    try {
      logger.info(`Creating Stripe payment: ${data.amount} ${data.currency} for ${data.description}`);
      
      // Get payment method
      const paymentMethod = await db.paymentMethod.findFirst({
        where: {
          userId: data.userId,
          ...(data.organizationId && { organizationId: data.organizationId }),
          isDefault: true,
        },
      });
      
      if (!paymentMethod) {
        throw new Error("No default payment method found");
      }
      
      // Create payment intent in Stripe
      const paymentIntent = await this.client.createPaymentIntent({
        amount: data.amount,
        currency: data.currency,
        payment_method: paymentMethod.externalId,
        description: data.description,
        metadata: {
          userId: data.userId,
          ...(data.organizationId && { organizationId: data.organizationId }),
          ...(data.billingId && { billingId: data.billingId }),
          ...(data.subscriptionId && { subscriptionId: data.subscriptionId }),
          ...(data.transactionId && { transactionId: data.transactionId }),
          ...data.metadata,
        },
      });
      
      // Create payment in database
      const payment = await db.payment.create({
        data: {
          amount: data.amount,
          currency: data.currency,
          status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
          method: data.method,
          type: data.type,
          description: data.description,
          user: {
            connect: { id: data.userId },
          },
          ...(data.organizationId && {
            organization: {
              connect: { id: data.organizationId },
            },
          }),
          ...(data.billingId && {
            billing: {
              connect: { id: data.billingId },
            },
          }),
          ...(data.subscriptionId && {
            subscription: {
              connect: { id: data.subscriptionId },
            },
          }),
          ...(data.transactionId && {
            transaction: {
              connect: { id: data.transactionId },
            },
          }),
          externalId: paymentIntent.id,
          metadata: data.metadata,
        },
      });
      
      // If payment is successful and there's a billing record, update it
      if (payment.status === PaymentStatus.COMPLETED && data.billingId) {
        await db.billingRecord.update({
          where: { id: data.billingId },
          data: {
            status: BillingStatus.PAID,
            paidAt: new Date(),
          },
        });
      }
      
      return payment;
    } catch (error) {
      logger.error(`Error creating Stripe payment: ${data.amount} ${data.currency} for ${data.description}`, error);
      throw new Error(`Failed to create payment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get a payment
   * @param paymentId Payment ID
   * @returns Payment
   */
  async getPayment(paymentId: string): Promise<Payment> {
    try {
      // Get payment from database
      const payment = await db.payment.findUnique({
        where: { id: paymentId },
      });
      
      if (!payment) {
        throw new Error(`Payment not found: ${paymentId}`);
      }
      
      // If payment has an external ID, get the latest status from Stripe
      if (payment.externalId) {
        const paymentIntent = await this.client.retrievePaymentIntent(payment.externalId);
        
        // Update payment status if it has changed
        const newStatus = this.mapStripeStatusToPaymentStatus(paymentIntent.status);
        if (payment.status !== newStatus) {
          await db.payment.update({
            where: { id: paymentId },
            data: {
              status: newStatus,
            },
          });
          
          // If payment is now completed and there's a billing record, update it
          if (newStatus === PaymentStatus.COMPLETED && payment.billingId) {
            await db.billingRecord.update({
              where: { id: payment.billingId },
              data: {
                status: BillingStatus.PAID,
                paidAt: new Date(),
              },
            });
          }
          
          // Return updated payment
          return {
            ...payment,
            status: newStatus,
          };
        }
      }
      
      return payment;
    } catch (error) {
      logger.error(`Error getting Stripe payment ${paymentId}:`, error);
      throw new Error(`Failed to get payment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a payment method
   * @param data Payment method creation data
   * @returns Created payment method
   */
  async createPaymentMethod(data: PaymentMethodCreationData): Promise<PaymentMethodData> {
    try {
      logger.info(`Creating Stripe payment method for user ${data.userId}`);
      
      // Create payment method in Stripe
      const stripePaymentMethod = await this.client.createPaymentMethod({
        type: this.mapPaymentMethodToStripeType(data.type),
        card: {
          last4: data.lastFour,
          exp_month: data.expiryMonth,
          exp_year: data.expiryYear,
          brand: data.cardBrand,
        },
        billing_details: {
          name: data.cardholderName,
          address: data.billingAddress,
        },
        metadata: {
          userId: data.userId,
          ...(data.organizationId && { organizationId: data.organizationId }),
          ...data.metadata,
        },
      });
      
      // If this is the default payment method, unset any existing default
      if (data.isDefault) {
        await db.paymentMethod.updateMany({
          where: {
            userId: data.userId,
            ...(data.organizationId && { organizationId: data.organizationId }),
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
      }
      
      // Create payment method in database
      const paymentMethod = await db.paymentMethod.create({
        data: {
          type: data.type,
          user: {
            connect: { id: data.userId },
          },
          ...(data.organizationId && {
            organization: {
              connect: { id: data.organizationId },
            },
          }),
          isDefault: data.isDefault ?? false,
          lastFour: stripePaymentMethod.card.last4,
          expiryMonth: stripePaymentMethod.card.exp_month,
          expiryYear: stripePaymentMethod.card.exp_year,
          cardBrand: stripePaymentMethod.card.brand,
          cardholderName: data.cardholderName,
          billingAddress: data.billingAddress as any,
          externalId: stripePaymentMethod.id,
          metadata: data.metadata,
        },
      });
      
      return paymentMethod;
    } catch (error) {
      logger.error(`Error creating Stripe payment method for user ${data.userId}:`, error);
      throw new Error(`Failed to create payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get a payment method
   * @param paymentMethodId Payment method ID
   * @returns Payment method
   */
  async getPaymentMethod(paymentMethodId: string): Promise<PaymentMethodData> {
    try {
      // Get payment method from database
      const paymentMethod = await db.paymentMethod.findUnique({
        where: { id: paymentMethodId },
      });
      
      if (!paymentMethod) {
        throw new Error(`Payment method not found: ${paymentMethodId}`);
      }
      
      return paymentMethod;
    } catch (error) {
      logger.error(`Error getting Stripe payment method ${paymentMethodId}:`, error);
      throw new Error(`Failed to get payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a subscription
   * @param data Subscription creation data
   * @returns Created subscription
   */
  async createSubscription(data: SubscriptionCreationData): Promise<Subscription> {
    try {
      logger.info(`Creating Stripe subscription for organization ${data.organizationId} with plan ${data.plan}`);
      
      // Get payment method
      const paymentMethod = await db.paymentMethod.findFirst({
        where: {
          organizationId: data.organizationId,
          isDefault: true,
        },
      });
      
      if (!paymentMethod) {
        throw new Error("No default payment method found for organization");
      }
      
      // Create subscription in Stripe
      const stripeSubscription = await this.client.createSubscription({
        customer: data.organizationId,
        payment_method: paymentMethod.externalId,
        plan: data.plan,
        trial_end: data.trialEndDate ? Math.floor(data.trialEndDate.getTime() / 1000) : undefined,
        metadata: {
          userId: data.userId,
          organizationId: data.organizationId,
          ...data.metadata,
        },
      });
      
      // Create subscription in database
      const subscription = await db.subscription.create({
        data: {
          plan: data.plan,
          status: this.mapStripeStatusToSubscriptionStatus(stripeSubscription.status),
          startDate: data.startDate || new Date(),
          trialEndDate: data.trialEndDate,
          user: {
            connect: { id: data.userId },
          },
          organization: {
            connect: { id: data.organizationId },
          },
          currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
          currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          externalId: stripeSubscription.id,
          metadata: data.metadata,
        },
      });
      
      // Create initial billing record
      await this.createBillingRecord({
        amount: this.getPlanAmount(data.plan),
        currency: 'USD',
        type: BillingType.SUBSCRIPTION,
        description: `Subscription to ${data.plan} plan`,
        dueDate: new Date(stripeSubscription.current_period_end * 1000),
        userId: data.userId,
        organizationId: data.organizationId,
        subscriptionId: subscription.id,
        metadata: {
          plan: data.plan,
          period: 'initial',
        },
      });
      
      return subscription;
    } catch (error) {
      logger.error(`Error creating Stripe subscription for organization ${data.organizationId}:`, error);
      throw new Error(`Failed to create subscription: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get a subscription
   * @param subscriptionId Subscription ID
   * @returns Subscription
   */
  async getSubscription(subscriptionId: string): Promise<Subscription> {
    try {
      // Get subscription from database
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
      });
      
      if (!subscription) {
        throw new Error(`Subscription not found: ${subscriptionId}`);
      }
      
      // If subscription has an external ID, get the latest status from Stripe
      if (subscription.externalId) {
        const stripeSubscription = await this.client.retrieveSubscription(subscription.externalId);
        
        // Update subscription status if it has changed
        const newStatus = this.mapStripeStatusToSubscriptionStatus(stripeSubscription.status);
        if (subscription.status !== newStatus) {
          await db.subscription.update({
            where: { id: subscriptionId },
            data: {
              status: newStatus,
              currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
              currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
              canceledAt: stripeSubscription.cancel_at ? new Date(stripeSubscription.cancel_at * 1000) : null,
            },
          });
          
          // Return updated subscription
          return {
            ...subscription,
            status: newStatus,
            currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
            currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
            canceledAt: stripeSubscription.cancel_at ? new Date(stripeSubscription.cancel_at * 1000) : null,
          };
        }
      }
      
      return subscription;
    } catch (error) {
      logger.error(`Error getting Stripe subscription ${subscriptionId}:`, error);
      throw new Error(`Failed to get subscription: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Cancel a subscription
   * @param subscriptionId Subscription ID
   * @returns Canceled subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<Subscription> {
    try {
      // Get subscription from database
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
      });
      
      if (!subscription) {
        throw new Error(`Subscription not found: ${subscriptionId}`);
      }
      
      // If subscription has an external ID, cancel it in Stripe
      if (subscription.externalId) {
        const stripeSubscription = await this.client.cancelSubscription(subscription.externalId);
        
        // Update subscription in database
        const updatedSubscription = await db.subscription.update({
          where: { id: subscriptionId },
          data: {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
            endDate: new Date(stripeSubscription.current_period_end * 1000),
          },
        });
        
        return updatedSubscription;
      } else {
        // If no external ID, just update the database
        const updatedSubscription = await db.subscription.update({
          where: { id: subscriptionId },
          data: {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
            endDate: new Date(),
          },
        });
        
        return updatedSubscription;
      }
    } catch (error) {
      logger.error(`Error canceling Stripe subscription ${subscriptionId}:`, error);
      throw new Error(`Failed to cancel subscription: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a billing record
   * @param data Billing record creation data
   * @returns Created billing record
   */
  async createBillingRecord(data: BillingRecordCreationData): Promise<BillingRecord> {
    try {
      logger.info(`Creating Stripe billing record: ${data.amount} ${data.currency} for ${data.description}`);
      
      // Generate invoice number
      const invoiceNumber = `INV-${Math.floor(Math.random() * 10000)}-${new Date().getFullYear()}`;
      
      // Create invoice in Stripe
      const stripeInvoice = await this.client.createInvoice({
        amount: data.amount,
        currency: data.currency,
        description: data.description,
        due_date: data.dueDate,
        customer: data.organizationId,
        subscription: data.subscriptionId,
        metadata: {
          userId: data.userId,
          organizationId: data.organizationId,
          ...(data.subscriptionId && { subscriptionId: data.subscriptionId }),
          ...(data.transactionId && { transactionId: data.transactionId }),
          ...data.metadata,
        },
      });
      
      // Create billing record in database
      const billingRecord = await db.billingRecord.create({
        data: {
          amount: data.amount,
          currency: data.currency,
          status: BillingStatus.PENDING,
          type: data.type,
          description: data.description,
          dueDate: data.dueDate,
          user: {
            connect: { id: data.userId },
          },
          organization: {
            connect: { id: data.organizationId },
          },
          ...(data.subscriptionId && {
            subscription: {
              connect: { id: data.subscriptionId },
            },
          }),
          ...(data.transactionId && {
            transaction: {
              connect: { id: data.transactionId },
            },
          }),
          invoiceNumber,
          invoiceUrl: stripeInvoice.hosted_invoice_url,
          externalId: stripeInvoice.id,
          metadata: data.metadata,
        },
      });
      
      return billingRecord;
    } catch (error) {
      logger.error(`Error creating Stripe billing record: ${data.amount} ${data.currency} for ${data.description}`, error);
      throw new Error(`Failed to create billing record: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get a billing record
   * @param billingId Billing record ID
   * @returns Billing record
   */
  async getBillingRecord(billingId: string): Promise<BillingRecord> {
    try {
      // Get billing record from database
      const billingRecord = await db.billingRecord.findUnique({
        where: { id: billingId },
      });
      
      if (!billingRecord) {
        throw new Error(`Billing record not found: ${billingId}`);
      }
      
      return billingRecord;
    } catch (error) {
      logger.error(`Error getting Stripe billing record ${billingId}:`, error);
      throw new Error(`Failed to get billing record: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Map Stripe payment status to internal payment status
   * @param stripeStatus Stripe payment status
   * @returns Internal payment status
   */
  private mapStripeStatusToPaymentStatus(stripeStatus: string): PaymentStatus {
    switch (stripeStatus) {
      case 'succeeded':
        return PaymentStatus.COMPLETED;
      case 'processing':
        return PaymentStatus.PROCESSING;
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
        return PaymentStatus.PENDING;
      case 'canceled':
        return PaymentStatus.CANCELED;
      default:
        return PaymentStatus.FAILED;
    }
  }

  /**
   * Map Stripe subscription status to internal subscription status
   * @param stripeStatus Stripe subscription status
   * @returns Internal subscription status
   */
  private mapStripeStatusToSubscriptionStatus(stripeStatus: string): SubscriptionStatus {
    switch (stripeStatus) {
      case 'active':
        return SubscriptionStatus.ACTIVE;
      case 'trialing':
        return SubscriptionStatus.TRIAL;
      case 'past_due':
        return SubscriptionStatus.PAST_DUE;
      case 'canceled':
        return SubscriptionStatus.CANCELED;
      case 'unpaid':
        return SubscriptionStatus.UNPAID;
      default:
        return SubscriptionStatus.INACTIVE;
    }
  }

  /**
   * Map internal payment method to Stripe payment method type
   * @param method Internal payment method
   * @returns Stripe payment method type
   */
  private mapPaymentMethodToStripeType(method: PaymentMethod): string {
    switch (method) {
      case PaymentMethod.CREDIT_CARD:
        return 'card';
      case PaymentMethod.BANK_TRANSFER:
        return 'bank_transfer';
      case PaymentMethod.PAYPAL:
        return 'paypal';
      default:
        return 'card';
    }
  }

  /**
   * Get amount for a subscription plan
   * @param plan Subscription plan
   * @returns Plan amount in cents
   */
  private getPlanAmount(plan: SubscriptionPlan): number {
    switch (plan) {
      case SubscriptionPlan.FREE:
        return 0;
      case SubscriptionPlan.STARTER:
        return 4900;
      case SubscriptionPlan.BUSINESS:
        return 9900;
      case SubscriptionPlan.ENTERPRISE:
        return 19900;
      default:
        return 0;
    }
  }
}
