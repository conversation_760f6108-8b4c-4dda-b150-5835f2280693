import { 
  PaymentStatus, 
  PaymentMethod, 
  PaymentType, 
  SubscriptionPlan, 
  SubscriptionStatus,
  BillingStatus,
  BillingType
} from '@prisma/client';

/**
 * Payment interface
 */
export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  method: PaymentMethod;
  type: PaymentType;
  description: string;
  userId: string;
  organizationId?: string;
  billingId?: string;
  subscriptionId?: string;
  transactionId?: string;
  externalId?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Payment creation data
 */
export interface PaymentCreationData {
  amount: number;
  currency: string;
  method: PaymentMethod;
  type: PaymentType;
  description: string;
  userId: string;
  organizationId?: string;
  billingId?: string;
  subscriptionId?: string;
  transactionId?: string;
  metadata?: any;
}

/**
 * Payment method interface
 */
export interface PaymentMethodData {
  id: string;
  type: PaymentMethod;
  userId: string;
  organizationId?: string;
  isDefault: boolean;
  lastFour?: string;
  expiryMonth?: number;
  expiryYear?: number;
  cardBrand?: string;
  cardholderName?: string;
  billingAddress?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Payment method creation data
 */
export interface PaymentMethodCreationData {
  type: PaymentMethod;
  userId: string;
  organizationId?: string;
  isDefault?: boolean;
  lastFour?: string;
  expiryMonth?: number;
  expiryYear?: number;
  cardBrand?: string;
  cardholderName?: string;
  billingAddress?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  metadata?: any;
}

/**
 * Subscription interface
 */
export interface Subscription {
  id: string;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  startDate: Date;
  endDate?: Date;
  trialEndDate?: Date;
  canceledAt?: Date;
  userId: string;
  organizationId: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  externalId?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Subscription creation data
 */
export interface SubscriptionCreationData {
  plan: SubscriptionPlan;
  userId: string;
  organizationId: string;
  startDate?: Date;
  trialEndDate?: Date;
  metadata?: any;
}

/**
 * Billing record interface
 */
export interface BillingRecord {
  id: string;
  amount: number;
  currency: string;
  status: BillingStatus;
  type: BillingType;
  description: string;
  dueDate: Date;
  paidAt?: Date;
  userId: string;
  organizationId: string;
  subscriptionId?: string;
  transactionId?: string;
  invoiceNumber: string;
  invoiceUrl?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Billing record creation data
 */
export interface BillingRecordCreationData {
  amount: number;
  currency: string;
  type: BillingType;
  description: string;
  dueDate: Date;
  userId: string;
  organizationId: string;
  subscriptionId?: string;
  transactionId?: string;
  metadata?: any;
}

/**
 * Payment provider interface
 */
export interface PaymentProvider {
  name: string;
  createPayment(data: PaymentCreationData): Promise<Payment>;
  getPayment(paymentId: string): Promise<Payment>;
  createPaymentMethod(data: PaymentMethodCreationData): Promise<PaymentMethodData>;
  getPaymentMethod(paymentMethodId: string): Promise<PaymentMethodData>;
  createSubscription(data: SubscriptionCreationData): Promise<Subscription>;
  getSubscription(subscriptionId: string): Promise<Subscription>;
  cancelSubscription(subscriptionId: string): Promise<Subscription>;
  createBillingRecord(data: BillingRecordCreationData): Promise<BillingRecord>;
  getBillingRecord(billingId: string): Promise<BillingRecord>;
}

/**
 * Payment filter interface
 */
export interface PaymentFilter {
  userId?: string;
  organizationId?: string;
  status?: PaymentStatus;
  method?: PaymentMethod;
  type?: PaymentType;
  startDate?: Date;
  endDate?: Date;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Payment pagination interface
 */
export interface PaymentPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Payment response interface
 */
export interface PaymentResponse {
  payments: Payment[];
  pagination: PaymentPagination;
}

/**
 * Subscription filter interface
 */
export interface SubscriptionFilter {
  userId?: string;
  organizationId?: string;
  plan?: SubscriptionPlan;
  status?: SubscriptionStatus;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Subscription pagination interface
 */
export interface SubscriptionPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Subscription response interface
 */
export interface SubscriptionResponse {
  subscriptions: Subscription[];
  pagination: SubscriptionPagination;
}

/**
 * Billing filter interface
 */
export interface BillingFilter {
  userId?: string;
  organizationId?: string;
  status?: BillingStatus;
  type?: BillingType;
  startDate?: Date;
  endDate?: Date;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Billing pagination interface
 */
export interface BillingPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Billing response interface
 */
export interface BillingResponse {
  billingRecords: BillingRecord[];
  pagination: BillingPagination;
}
