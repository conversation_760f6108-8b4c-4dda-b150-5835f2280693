import { 
  PaymentStatus, 
  PaymentMethod, 
  PaymentType, 
  SubscriptionPlan, 
  SubscriptionStatus,
  BillingStatus,
  BillingType,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { 
  Payment, 
  PaymentCreationData,
  PaymentMethodData,
  PaymentMethodCreationData,
  Subscription,
  SubscriptionCreationData,
  BillingRecord,
  BillingRecordCreationData,
  PaymentFilter,
  PaymentResponse,
  SubscriptionFilter,
  SubscriptionResponse,
  BillingFilter,
  BillingResponse
} from './types';
import { StripeProvider } from './providers/stripe';
import { notificationService } from '@/lib/notifications';

/**
 * Payment service
 */
export class PaymentService {
  private static provider = new StripeProvider();

  /**
   * Create a payment
   * @param data Payment creation data
   * @returns Created payment
   */
  static async createPayment(data: PaymentCreationData): Promise<Payment> {
    try {
      logger.info(`Creating payment: ${data.amount} ${data.currency} for ${data.description}`);
      
      // Create payment using provider
      const payment = await this.provider.createPayment(data);
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "PAYMENT_CREATED",
          description: `Payment created: ${data.amount} ${data.currency} for ${data.description}`,
          userId: data.userId,
          organizationId: data.organizationId,
          metadata: {
            paymentId: payment.id,
            amount: data.amount,
            currency: data.currency,
            method: data.method,
            type: data.type,
            description: data.description,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId: data.userId,
        title: "Payment Processed",
        message: `Your payment of ${data.amount} ${data.currency} for ${data.description} has been processed.`,
        type: "BILLING",
        priority: "NORMAL",
        organizationId: data.organizationId,
      });
      
      return payment;
    } catch (error) {
      logger.error(`Error creating payment: ${data.amount} ${data.currency} for ${data.description}`, error);
      throw new Error(`Failed to create payment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get payment by ID
   * @param id Payment ID
   * @returns Payment
   */
  static async getPaymentById(id: string): Promise<Payment | null> {
    try {
      // Get payment from provider
      return await this.provider.getPayment(id);
    } catch (error) {
      logger.error(`Error getting payment ${id}:`, error);
      throw new Error(`Failed to get payment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get payments
   * @param filter Filter options
   * @returns Payments and pagination
   */
  static async getPayments(filter: PaymentFilter = {}): Promise<PaymentResponse> {
    try {
      const {
        userId,
        organizationId,
        status,
        method,
        type,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        search,
        limit = 10,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = filter;
      
      // Build query
      const where: Prisma.PaymentWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(status && { status }),
        ...(method && { method }),
        ...(type && { type }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
        ...(minAmount !== undefined && { amount: { gte: minAmount } }),
        ...(maxAmount !== undefined && { amount: { lte: maxAmount } }),
        ...(search && {
          OR: [
            { description: { contains: search, mode: "insensitive" } },
          ],
        }),
      };
      
      // Get payments
      const payments = await db.payment.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.payment.count({ where });
      
      return {
        payments,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting payments:", error);
      throw new Error(`Failed to get payments: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a payment method
   * @param data Payment method creation data
   * @returns Created payment method
   */
  static async createPaymentMethod(data: PaymentMethodCreationData): Promise<PaymentMethodData> {
    try {
      logger.info(`Creating payment method for user ${data.userId}`);
      
      // Create payment method using provider
      const paymentMethod = await this.provider.createPaymentMethod(data);
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "PAYMENT_METHOD_CREATED",
          description: `Payment method created: ${data.type}`,
          userId: data.userId,
          organizationId: data.organizationId,
          metadata: {
            paymentMethodId: paymentMethod.id,
            type: data.type,
            isDefault: data.isDefault,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId: data.userId,
        title: "Payment Method Added",
        message: `A new payment method has been added to your account.`,
        type: "BILLING",
        priority: "NORMAL",
        organizationId: data.organizationId,
      });
      
      return paymentMethod;
    } catch (error) {
      logger.error(`Error creating payment method for user ${data.userId}:`, error);
      throw new Error(`Failed to create payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get payment method by ID
   * @param id Payment method ID
   * @returns Payment method
   */
  static async getPaymentMethodById(id: string): Promise<PaymentMethodData | null> {
    try {
      // Get payment method from provider
      return await this.provider.getPaymentMethod(id);
    } catch (error) {
      logger.error(`Error getting payment method ${id}:`, error);
      throw new Error(`Failed to get payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get payment methods for a user
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Payment methods
   */
  static async getPaymentMethods(userId: string, organizationId?: string): Promise<PaymentMethodData[]> {
    try {
      // Get payment methods from database
      return await db.paymentMethod.findMany({
        where: {
          userId,
          ...(organizationId && { organizationId }),
        },
        orderBy: [
          { isDefault: "desc" },
          { createdAt: "desc" },
        ],
      });
    } catch (error) {
      logger.error(`Error getting payment methods for user ${userId}:`, error);
      throw new Error(`Failed to get payment methods: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Set default payment method
   * @param id Payment method ID
   * @param userId User ID
   * @returns Updated payment method
   */
  static async setDefaultPaymentMethod(id: string, userId: string): Promise<PaymentMethodData> {
    try {
      // Get payment method
      const paymentMethod = await db.paymentMethod.findUnique({
        where: { id },
      });
      
      if (!paymentMethod) {
        throw new Error(`Payment method not found: ${id}`);
      }
      
      if (paymentMethod.userId !== userId) {
        throw new Error("You do not have permission to update this payment method");
      }
      
      // Unset any existing default payment methods
      await db.paymentMethod.updateMany({
        where: {
          userId,
          organizationId: paymentMethod.organizationId,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
      
      // Set this payment method as default
      const updatedPaymentMethod = await db.paymentMethod.update({
        where: { id },
        data: {
          isDefault: true,
        },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "PAYMENT_METHOD_UPDATED",
          description: `Payment method set as default: ${paymentMethod.type}`,
          userId,
          organizationId: paymentMethod.organizationId,
          metadata: {
            paymentMethodId: id,
            type: paymentMethod.type,
          },
        },
      });
      
      return updatedPaymentMethod;
    } catch (error) {
      logger.error(`Error setting default payment method ${id}:`, error);
      throw new Error(`Failed to set default payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Delete payment method
   * @param id Payment method ID
   * @param userId User ID
   * @returns Deleted payment method
   */
  static async deletePaymentMethod(id: string, userId: string): Promise<PaymentMethodData> {
    try {
      // Get payment method
      const paymentMethod = await db.paymentMethod.findUnique({
        where: { id },
      });
      
      if (!paymentMethod) {
        throw new Error(`Payment method not found: ${id}`);
      }
      
      if (paymentMethod.userId !== userId) {
        throw new Error("You do not have permission to delete this payment method");
      }
      
      // Check if this is the only payment method
      const paymentMethodCount = await db.paymentMethod.count({
        where: {
          userId,
          organizationId: paymentMethod.organizationId,
        },
      });
      
      if (paymentMethodCount === 1) {
        throw new Error("Cannot delete the only payment method");
      }
      
      // Check if this is the default payment method
      if (paymentMethod.isDefault) {
        // Find another payment method to set as default
        const anotherPaymentMethod = await db.paymentMethod.findFirst({
          where: {
            userId,
            organizationId: paymentMethod.organizationId,
            id: { not: id },
          },
        });
        
        if (anotherPaymentMethod) {
          await db.paymentMethod.update({
            where: { id: anotherPaymentMethod.id },
            data: {
              isDefault: true,
            },
          });
        }
      }
      
      // Delete payment method
      const deletedPaymentMethod = await db.paymentMethod.delete({
        where: { id },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "PAYMENT_METHOD_DELETED",
          description: `Payment method deleted: ${paymentMethod.type}`,
          userId,
          organizationId: paymentMethod.organizationId,
          metadata: {
            paymentMethodId: id,
            type: paymentMethod.type,
          },
        },
      });
      
      return deletedPaymentMethod;
    } catch (error) {
      logger.error(`Error deleting payment method ${id}:`, error);
      throw new Error(`Failed to delete payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a subscription
   * @param data Subscription creation data
   * @returns Created subscription
   */
  static async createSubscription(data: SubscriptionCreationData): Promise<Subscription> {
    try {
      logger.info(`Creating subscription for organization ${data.organizationId} with plan ${data.plan}`);
      
      // Check if organization already has a subscription
      const existingSubscription = await db.subscription.findFirst({
        where: {
          organizationId: data.organizationId,
          status: {
            in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIAL],
          },
        },
      });
      
      if (existingSubscription) {
        throw new Error("Organization already has an active subscription");
      }
      
      // Create subscription using provider
      const subscription = await this.provider.createSubscription(data);
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "SUBSCRIPTION_CREATED",
          description: `Subscription created: ${data.plan}`,
          userId: data.userId,
          organizationId: data.organizationId,
          metadata: {
            subscriptionId: subscription.id,
            plan: data.plan,
            startDate: subscription.startDate,
            trialEndDate: subscription.trialEndDate,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId: data.userId,
        title: "Subscription Created",
        message: `Your subscription to the ${data.plan} plan has been created.`,
        type: "BILLING",
        priority: "HIGH",
        organizationId: data.organizationId,
      });
      
      // Send organization-wide notification
      await notificationService.createOrganizationNotification(
        data.organizationId,
        {
          title: "Subscription Created",
          message: `Your organization has subscribed to the ${data.plan} plan.`,
          type: "BILLING",
          priority: "NORMAL",
        },
        [data.userId] // Exclude the user who created the subscription
      );
      
      return subscription;
    } catch (error) {
      logger.error(`Error creating subscription for organization ${data.organizationId}:`, error);
      throw new Error(`Failed to create subscription: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get subscription by ID
   * @param id Subscription ID
   * @returns Subscription
   */
  static async getSubscriptionById(id: string): Promise<Subscription | null> {
    try {
      // Get subscription from provider
      return await this.provider.getSubscription(id);
    } catch (error) {
      logger.error(`Error getting subscription ${id}:`, error);
      throw new Error(`Failed to get subscription: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get subscriptions
   * @param filter Filter options
   * @returns Subscriptions and pagination
   */
  static async getSubscriptions(filter: SubscriptionFilter = {}): Promise<SubscriptionResponse> {
    try {
      const {
        userId,
        organizationId,
        plan,
        status,
        startDate,
        endDate,
        search,
        limit = 10,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = filter;
      
      // Build query
      const where: Prisma.SubscriptionWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(plan && { plan }),
        ...(status && { status }),
        ...(startDate && { startDate: { gte: startDate } }),
        ...(endDate && { endDate: { lte: endDate } }),
      };
      
      // Get subscriptions
      const subscriptions = await db.subscription.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.subscription.count({ where });
      
      return {
        subscriptions,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting subscriptions:", error);
      throw new Error(`Failed to get subscriptions: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Cancel subscription
   * @param id Subscription ID
   * @param userId User ID
   * @returns Canceled subscription
   */
  static async cancelSubscription(id: string, userId: string): Promise<Subscription> {
    try {
      // Get subscription
      const subscription = await db.subscription.findUnique({
        where: { id },
        include: {
          organization: true,
        },
      });
      
      if (!subscription) {
        throw new Error(`Subscription not found: ${id}`);
      }
      
      // Check if user has permission
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === subscription.organizationId;
      
      if (!isAdmin && !isOrgAdmin) {
        throw new Error("You do not have permission to cancel this subscription");
      }
      
      // Cancel subscription using provider
      const canceledSubscription = await this.provider.cancelSubscription(id);
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "SUBSCRIPTION_CANCELED",
          description: `Subscription canceled: ${subscription.plan}`,
          userId,
          organizationId: subscription.organizationId,
          metadata: {
            subscriptionId: id,
            plan: subscription.plan,
            canceledAt: canceledSubscription.canceledAt,
            endDate: canceledSubscription.endDate,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId,
        title: "Subscription Canceled",
        message: `Your subscription to the ${subscription.plan} plan has been canceled.`,
        type: "BILLING",
        priority: "HIGH",
        organizationId: subscription.organizationId,
      });
      
      // Send organization-wide notification
      await notificationService.createOrganizationNotification(
        subscription.organizationId,
        {
          title: "Subscription Canceled",
          message: `Your organization's subscription to the ${subscription.plan} plan has been canceled.`,
          type: "BILLING",
          priority: "HIGH",
        },
        [userId] // Exclude the user who canceled the subscription
      );
      
      return canceledSubscription;
    } catch (error) {
      logger.error(`Error canceling subscription ${id}:`, error);
      throw new Error(`Failed to cancel subscription: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a billing record
   * @param data Billing record creation data
   * @returns Created billing record
   */
  static async createBillingRecord(data: BillingRecordCreationData): Promise<BillingRecord> {
    try {
      logger.info(`Creating billing record: ${data.amount} ${data.currency} for ${data.description}`);
      
      // Create billing record using provider
      const billingRecord = await this.provider.createBillingRecord(data);
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "BILLING_RECORD_CREATED",
          description: `Billing record created: ${data.amount} ${data.currency} for ${data.description}`,
          userId: data.userId,
          organizationId: data.organizationId,
          metadata: {
            billingId: billingRecord.id,
            amount: data.amount,
            currency: data.currency,
            type: data.type,
            description: data.description,
            dueDate: data.dueDate,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId: data.userId,
        title: "New Invoice",
        message: `A new invoice for ${data.amount} ${data.currency} has been created.`,
        type: "BILLING",
        priority: "NORMAL",
        actionUrl: billingRecord.invoiceUrl,
        actionLabel: "View Invoice",
        organizationId: data.organizationId,
      });
      
      return billingRecord;
    } catch (error) {
      logger.error(`Error creating billing record: ${data.amount} ${data.currency} for ${data.description}`, error);
      throw new Error(`Failed to create billing record: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get billing record by ID
   * @param id Billing record ID
   * @returns Billing record
   */
  static async getBillingRecordById(id: string): Promise<BillingRecord | null> {
    try {
      // Get billing record from provider
      return await this.provider.getBillingRecord(id);
    } catch (error) {
      logger.error(`Error getting billing record ${id}:`, error);
      throw new Error(`Failed to get billing record: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get billing records
   * @param filter Filter options
   * @returns Billing records and pagination
   */
  static async getBillingRecords(filter: BillingFilter = {}): Promise<BillingResponse> {
    try {
      const {
        userId,
        organizationId,
        status,
        type,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        search,
        limit = 10,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = filter;
      
      // Build query
      const where: Prisma.BillingRecordWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(status && { status }),
        ...(type && { type }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
        ...(minAmount !== undefined && { amount: { gte: minAmount } }),
        ...(maxAmount !== undefined && { amount: { lte: maxAmount } }),
        ...(search && {
          OR: [
            { description: { contains: search, mode: "insensitive" } },
            { invoiceNumber: { contains: search, mode: "insensitive" } },
          ],
        }),
      };
      
      // Get billing records
      const billingRecords = await db.billingRecord.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.billingRecord.count({ where });
      
      return {
        billingRecords,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting billing records:", error);
      throw new Error(`Failed to get billing records: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Pay a billing record
   * @param id Billing record ID
   * @param userId User ID
   * @param paymentMethodId Payment method ID
   * @returns Updated billing record
   */
  static async payBillingRecord(id: string, userId: string, paymentMethodId?: string): Promise<BillingRecord> {
    try {
      // Get billing record
      const billingRecord = await db.billingRecord.findUnique({
        where: { id },
        include: {
          organization: true,
        },
      });
      
      if (!billingRecord) {
        throw new Error(`Billing record not found: ${id}`);
      }
      
      // Check if billing record is already paid
      if (billingRecord.status === BillingStatus.PAID) {
        throw new Error("Billing record is already paid");
      }
      
      // Check if user has permission
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === billingRecord.organizationId;
      
      if (!isAdmin && !isOrgAdmin) {
        throw new Error("You do not have permission to pay this billing record");
      }
      
      // Get payment method
      let paymentMethod;
      if (paymentMethodId) {
        paymentMethod = await db.paymentMethod.findUnique({
          where: { id: paymentMethodId },
        });
        
        if (!paymentMethod) {
          throw new Error(`Payment method not found: ${paymentMethodId}`);
        }
        
        if (paymentMethod.organizationId !== billingRecord.organizationId) {
          throw new Error("Payment method does not belong to the organization");
        }
      } else {
        paymentMethod = await db.paymentMethod.findFirst({
          where: {
            organizationId: billingRecord.organizationId,
            isDefault: true,
          },
        });
        
        if (!paymentMethod) {
          throw new Error("No default payment method found for organization");
        }
      }
      
      // Create payment
      const payment = await this.createPayment({
        amount: billingRecord.amount,
        currency: billingRecord.currency,
        method: paymentMethod.type,
        type: PaymentType.INVOICE,
        description: billingRecord.description,
        userId,
        organizationId: billingRecord.organizationId,
        billingId: billingRecord.id,
      });
      
      // Update billing record
      const updatedBillingRecord = await db.billingRecord.update({
        where: { id },
        data: {
          status: BillingStatus.PAID,
          paidAt: new Date(),
        },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "BILLING_RECORD_PAID",
          description: `Billing record paid: ${billingRecord.amount} ${billingRecord.currency} for ${billingRecord.description}`,
          userId,
          organizationId: billingRecord.organizationId,
          metadata: {
            billingId: billingRecord.id,
            paymentId: payment.id,
            amount: billingRecord.amount,
            currency: billingRecord.currency,
            paidAt: updatedBillingRecord.paidAt,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId,
        title: "Invoice Paid",
        message: `Your invoice for ${billingRecord.amount} ${billingRecord.currency} has been paid.`,
        type: "BILLING",
        priority: "NORMAL",
        organizationId: billingRecord.organizationId,
      });
      
      return updatedBillingRecord;
    } catch (error) {
      logger.error(`Error paying billing record ${id}:`, error);
      throw new Error(`Failed to pay billing record: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
