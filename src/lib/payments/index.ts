import { 
  PaymentStatus, 
  PaymentMethod, 
  PaymentType, 
  SubscriptionPlan, 
  SubscriptionStatus,
  BillingStatus,
  BillingType
} from '@prisma/client';
import { 
  Payment, 
  PaymentCreationData,
  PaymentMethodData,
  PaymentMethodCreationData,
  Subscription,
  SubscriptionCreationData,
  BillingRecord,
  BillingRecordCreationData,
  PaymentFilter,
  PaymentResponse,
  SubscriptionFilter,
  SubscriptionResponse,
  BillingFilter,
  BillingResponse
} from './types';
import { PaymentService } from './service';

/**
 * Payment manager
 */
export class PaymentManager {
  /**
   * Create a payment
   * @param data Payment creation data
   * @returns Created payment
   */
  static async createPayment(data: PaymentCreationData): Promise<Payment> {
    return PaymentService.createPayment(data);
  }

  /**
   * Get payment by ID
   * @param id Payment ID
   * @returns Payment
   */
  static async getPaymentById(id: string): Promise<Payment | null> {
    return PaymentService.getPaymentById(id);
  }

  /**
   * Get payments
   * @param filter Filter options
   * @returns Payments and pagination
   */
  static async getPayments(filter: PaymentFilter = {}): Promise<PaymentResponse> {
    return PaymentService.getPayments(filter);
  }

  /**
   * Create a payment method
   * @param data Payment method creation data
   * @returns Created payment method
   */
  static async createPaymentMethod(data: PaymentMethodCreationData): Promise<PaymentMethodData> {
    return PaymentService.createPaymentMethod(data);
  }

  /**
   * Get payment method by ID
   * @param id Payment method ID
   * @returns Payment method
   */
  static async getPaymentMethodById(id: string): Promise<PaymentMethodData | null> {
    return PaymentService.getPaymentMethodById(id);
  }

  /**
   * Get payment methods for a user
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Payment methods
   */
  static async getPaymentMethods(userId: string, organizationId?: string): Promise<PaymentMethodData[]> {
    return PaymentService.getPaymentMethods(userId, organizationId);
  }

  /**
   * Set default payment method
   * @param id Payment method ID
   * @param userId User ID
   * @returns Updated payment method
   */
  static async setDefaultPaymentMethod(id: string, userId: string): Promise<PaymentMethodData> {
    return PaymentService.setDefaultPaymentMethod(id, userId);
  }

  /**
   * Delete payment method
   * @param id Payment method ID
   * @param userId User ID
   * @returns Deleted payment method
   */
  static async deletePaymentMethod(id: string, userId: string): Promise<PaymentMethodData> {
    return PaymentService.deletePaymentMethod(id, userId);
  }

  /**
   * Create a subscription
   * @param data Subscription creation data
   * @returns Created subscription
   */
  static async createSubscription(data: SubscriptionCreationData): Promise<Subscription> {
    return PaymentService.createSubscription(data);
  }

  /**
   * Get subscription by ID
   * @param id Subscription ID
   * @returns Subscription
   */
  static async getSubscriptionById(id: string): Promise<Subscription | null> {
    return PaymentService.getSubscriptionById(id);
  }

  /**
   * Get subscriptions
   * @param filter Filter options
   * @returns Subscriptions and pagination
   */
  static async getSubscriptions(filter: SubscriptionFilter = {}): Promise<SubscriptionResponse> {
    return PaymentService.getSubscriptions(filter);
  }

  /**
   * Cancel subscription
   * @param id Subscription ID
   * @param userId User ID
   * @returns Canceled subscription
   */
  static async cancelSubscription(id: string, userId: string): Promise<Subscription> {
    return PaymentService.cancelSubscription(id, userId);
  }

  /**
   * Create a billing record
   * @param data Billing record creation data
   * @returns Created billing record
   */
  static async createBillingRecord(data: BillingRecordCreationData): Promise<BillingRecord> {
    return PaymentService.createBillingRecord(data);
  }

  /**
   * Get billing record by ID
   * @param id Billing record ID
   * @returns Billing record
   */
  static async getBillingRecordById(id: string): Promise<BillingRecord | null> {
    return PaymentService.getBillingRecordById(id);
  }

  /**
   * Get billing records
   * @param filter Filter options
   * @returns Billing records and pagination
   */
  static async getBillingRecords(filter: BillingFilter = {}): Promise<BillingResponse> {
    return PaymentService.getBillingRecords(filter);
  }

  /**
   * Pay a billing record
   * @param id Billing record ID
   * @param userId User ID
   * @param paymentMethodId Payment method ID
   * @returns Updated billing record
   */
  static async payBillingRecord(id: string, userId: string, paymentMethodId?: string): Promise<BillingRecord> {
    return PaymentService.payBillingRecord(id, userId, paymentMethodId);
  }
}

// Create a singleton instance
const paymentManager = new PaymentManager();

// Export the singleton instance
export { paymentManager };

// Export types and enums
export {
  PaymentStatus,
  PaymentMethod,
  PaymentType,
  SubscriptionPlan,
  SubscriptionStatus,
  BillingStatus,
  BillingType,
  Payment,
  PaymentCreationData,
  PaymentMethodData,
  PaymentMethodCreationData,
  Subscription,
  SubscriptionCreationData,
  BillingRecord,
  BillingRecordCreationData,
  PaymentFilter,
  PaymentResponse,
  SubscriptionFilter,
  SubscriptionResponse,
  BillingFilter,
  BillingResponse
};
