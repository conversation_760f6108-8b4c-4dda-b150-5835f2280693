import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { notificationService, NotificationChannel } from "@/lib/notifications";

/**
 * Payment method type
 */
export enum PaymentMethodType {
  CREDIT_CARD = "CREDIT_CARD",
  BANK_TRANSFER = "BANK_TRANSFER",
  CRYPTO = "CRYPTO",
  INVOICE = "INVOICE",
}

/**
 * Payment status
 */
export enum PaymentStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  REFUNDED = "REFUNDED",
}

/**
 * Payment method
 */
export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  details: any;
  isDefault: boolean;
}

/**
 * Payment request
 */
export interface PaymentRequest {
  amount: number;
  currency: string;
  description: string;
  metadata?: any;
  paymentMethodId?: string;
  userId: string;
  organizationId: string;
}

/**
 * Payment service for processing payments
 */
export class PaymentService {
  /**
   * Process a payment
   * @param request Payment request
   * @returns Payment result
   */
  async processPayment(request: PaymentRequest) {
    try {
      logger.info(`Processing payment of ${request.amount} ${request.currency} for user ${request.userId}`);
      
      // Get user details
      const user = await db.user.findUnique({
        where: { id: request.userId },
        include: {
          organization: true,
        },
      });
      
      if (!user) {
        throw new Error("User not found");
      }
      
      // Get payment method if provided
      let paymentMethod;
      if (request.paymentMethodId) {
        paymentMethod = await db.paymentMethod.findUnique({
          where: { id: request.paymentMethodId },
        });
        
        if (!paymentMethod) {
          throw new Error("Payment method not found");
        }
        
        // Check if payment method belongs to the user or organization
        if (paymentMethod.userId !== request.userId && paymentMethod.organizationId !== request.organizationId) {
          throw new Error("Payment method does not belong to the user or organization");
        }
      } else {
        // Get default payment method
        paymentMethod = await db.paymentMethod.findFirst({
          where: {
            OR: [
              { userId: request.userId, isDefault: true },
              { organizationId: request.organizationId, isDefault: true },
            ],
          },
        });
        
        if (!paymentMethod) {
          throw new Error("No default payment method found");
        }
      }
      
      // Create payment record
      const payment = await db.payment.create({
        data: {
          amount: request.amount,
          currency: request.currency,
          description: request.description,
          status: PaymentStatus.PROCESSING,
          metadata: request.metadata || {},
          user: {
            connect: { id: request.userId },
          },
          organization: {
            connect: { id: request.organizationId },
          },
          paymentMethod: {
            connect: { id: paymentMethod.id },
          },
        },
      });
      
      // Process payment based on payment method type
      let paymentResult;
      switch (paymentMethod.type) {
        case PaymentMethodType.CREDIT_CARD:
          paymentResult = await this.processCreditCardPayment(payment.id, paymentMethod, request);
          break;
        case PaymentMethodType.BANK_TRANSFER:
          paymentResult = await this.processBankTransferPayment(payment.id, paymentMethod, request);
          break;
        case PaymentMethodType.CRYPTO:
          paymentResult = await this.processCryptoPayment(payment.id, paymentMethod, request);
          break;
        case PaymentMethodType.INVOICE:
          paymentResult = await this.processInvoicePayment(payment.id, paymentMethod, request);
          break;
        default:
          throw new Error(`Unsupported payment method type: ${paymentMethod.type}`);
      }
      
      // Update payment record
      const updatedPayment = await db.payment.update({
        where: { id: payment.id },
        data: {
          status: paymentResult.success ? PaymentStatus.COMPLETED : PaymentStatus.FAILED,
          transactionId: paymentResult.transactionId,
          errorMessage: paymentResult.errorMessage,
        },
      });
      
      // Log audit event
      await auditService.log(
        paymentResult.success ? "PAYMENT_COMPLETED" : "PAYMENT_FAILED",
        `Payment ${paymentResult.success ? "completed" : "failed"}: ${request.amount} ${request.currency}`,
        {
          paymentId: payment.id,
          amount: request.amount,
          currency: request.currency,
          description: request.description,
          paymentMethodType: paymentMethod.type,
        },
        request.userId,
        request.organizationId
      );
      
      // Send notification
      await notificationService.createNotification(
        request.userId,
        paymentResult.success ? "Payment Successful" : "Payment Failed",
        paymentResult.success
          ? `Your payment of ${request.amount} ${request.currency} for ${request.description} was successful.`
          : `Your payment of ${request.amount} ${request.currency} for ${request.description} failed: ${paymentResult.errorMessage}`,
        "TRANSACTION",
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          paymentId: payment.id,
          amount: request.amount,
          currency: request.currency,
          description: request.description,
        }
      );
      
      return {
        success: paymentResult.success,
        paymentId: payment.id,
        transactionId: paymentResult.transactionId,
        status: updatedPayment.status,
        errorMessage: paymentResult.errorMessage,
      };
    } catch (error) {
      logger.error("Error processing payment:", error);
      throw new Error(`Failed to process payment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Process credit card payment
   * @param paymentId Payment ID
   * @param paymentMethod Payment method
   * @param request Payment request
   * @returns Payment result
   */
  private async processCreditCardPayment(paymentId: string, paymentMethod: any, request: PaymentRequest) {
    try {
      // In a real implementation, this would integrate with a payment processor like Stripe
      logger.info(`Processing credit card payment ${paymentId} with method ${paymentMethod.id}`);
      
      // Simulate payment processing
      const success = Math.random() > 0.1; // 90% success rate for simulation
      
      if (success) {
        return {
          success: true,
          transactionId: `cc_${Date.now()}_${Math.floor(Math.random() * 1000000)}`,
          errorMessage: null,
        };
      } else {
        return {
          success: false,
          transactionId: null,
          errorMessage: "Credit card payment failed: Card declined",
        };
      }
    } catch (error) {
      logger.error(`Error processing credit card payment ${paymentId}:`, error);
      return {
        success: false,
        transactionId: null,
        errorMessage: `Credit card payment failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
  
  /**
   * Process bank transfer payment
   * @param paymentId Payment ID
   * @param paymentMethod Payment method
   * @param request Payment request
   * @returns Payment result
   */
  private async processBankTransferPayment(paymentId: string, paymentMethod: any, request: PaymentRequest) {
    try {
      // In a real implementation, this would integrate with a bank transfer API
      logger.info(`Processing bank transfer payment ${paymentId} with method ${paymentMethod.id}`);
      
      // Simulate payment processing
      const success = Math.random() > 0.1; // 90% success rate for simulation
      
      if (success) {
        return {
          success: true,
          transactionId: `bt_${Date.now()}_${Math.floor(Math.random() * 1000000)}`,
          errorMessage: null,
        };
      } else {
        return {
          success: false,
          transactionId: null,
          errorMessage: "Bank transfer failed: Insufficient funds",
        };
      }
    } catch (error) {
      logger.error(`Error processing bank transfer payment ${paymentId}:`, error);
      return {
        success: false,
        transactionId: null,
        errorMessage: `Bank transfer failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
  
  /**
   * Process crypto payment
   * @param paymentId Payment ID
   * @param paymentMethod Payment method
   * @param request Payment request
   * @returns Payment result
   */
  private async processCryptoPayment(paymentId: string, paymentMethod: any, request: PaymentRequest) {
    try {
      // In a real implementation, this would integrate with a crypto payment processor
      logger.info(`Processing crypto payment ${paymentId} with method ${paymentMethod.id}`);
      
      // Simulate payment processing
      const success = Math.random() > 0.1; // 90% success rate for simulation
      
      if (success) {
        return {
          success: true,
          transactionId: `crypto_${Date.now()}_${Math.floor(Math.random() * 1000000)}`,
          errorMessage: null,
        };
      } else {
        return {
          success: false,
          transactionId: null,
          errorMessage: "Crypto payment failed: Transaction rejected",
        };
      }
    } catch (error) {
      logger.error(`Error processing crypto payment ${paymentId}:`, error);
      return {
        success: false,
        transactionId: null,
        errorMessage: `Crypto payment failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
  
  /**
   * Process invoice payment
   * @param paymentId Payment ID
   * @param paymentMethod Payment method
   * @param request Payment request
   * @returns Payment result
   */
  private async processInvoicePayment(paymentId: string, paymentMethod: any, request: PaymentRequest) {
    try {
      // In a real implementation, this would generate an invoice and mark payment as pending
      logger.info(`Processing invoice payment ${paymentId} with method ${paymentMethod.id}`);
      
      // Invoice payments are always "successful" in terms of generation, but payment is pending
      return {
        success: true,
        transactionId: `inv_${Date.now()}_${Math.floor(Math.random() * 1000000)}`,
        errorMessage: null,
      };
    } catch (error) {
      logger.error(`Error processing invoice payment ${paymentId}:`, error);
      return {
        success: false,
        transactionId: null,
        errorMessage: `Invoice payment failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
  
  /**
   * Create a payment method
   * @param userId User ID
   * @param organizationId Organization ID
   * @param type Payment method type
   * @param details Payment method details
   * @param isDefault Whether this is the default payment method
   * @returns Created payment method
   */
  async createPaymentMethod(
    userId: string,
    organizationId: string,
    type: PaymentMethodType,
    details: any,
    isDefault: boolean = false
  ) {
    try {
      logger.info(`Creating ${type} payment method for user ${userId}`);
      
      // If setting as default, unset any existing default payment methods
      if (isDefault) {
        await db.paymentMethod.updateMany({
          where: {
            OR: [
              { userId, isDefault: true },
              { organizationId, isDefault: true },
            ],
          },
          data: {
            isDefault: false,
          },
        });
      }
      
      // Create payment method
      const paymentMethod = await db.paymentMethod.create({
        data: {
          type,
          details,
          isDefault,
          user: {
            connect: { id: userId },
          },
          organization: {
            connect: { id: organizationId },
          },
        },
      });
      
      // Log audit event
      await auditService.log(
        "PAYMENT_METHOD_CREATED",
        `Payment method created: ${type}`,
        {
          paymentMethodId: paymentMethod.id,
          type,
          isDefault,
        },
        userId,
        organizationId
      );
      
      return paymentMethod;
    } catch (error) {
      logger.error("Error creating payment method:", error);
      throw new Error(`Failed to create payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Get payment methods for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Payment methods
   */
  async getPaymentMethods(userId: string, organizationId: string) {
    try {
      const paymentMethods = await db.paymentMethod.findMany({
        where: {
          OR: [
            { userId },
            { organizationId },
          ],
        },
        orderBy: {
          isDefault: "desc",
        },
      });
      
      return paymentMethods;
    } catch (error) {
      logger.error("Error getting payment methods:", error);
      throw new Error(`Failed to get payment methods: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Delete a payment method
   * @param paymentMethodId Payment method ID
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Deleted payment method
   */
  async deletePaymentMethod(paymentMethodId: string, userId: string, organizationId: string) {
    try {
      // Check if payment method exists and belongs to the user or organization
      const paymentMethod = await db.paymentMethod.findFirst({
        where: {
          id: paymentMethodId,
          OR: [
            { userId },
            { organizationId },
          ],
        },
      });
      
      if (!paymentMethod) {
        throw new Error("Payment method not found or does not belong to the user or organization");
      }
      
      // Delete payment method
      await db.paymentMethod.delete({
        where: { id: paymentMethodId },
      });
      
      // Log audit event
      await auditService.log(
        "PAYMENT_METHOD_DELETED",
        `Payment method deleted: ${paymentMethod.type}`,
        {
          paymentMethodId,
          type: paymentMethod.type,
        },
        userId,
        organizationId
      );
      
      return paymentMethod;
    } catch (error) {
      logger.error("Error deleting payment method:", error);
      throw new Error(`Failed to delete payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Set a payment method as default
   * @param paymentMethodId Payment method ID
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Updated payment method
   */
  async setDefaultPaymentMethod(paymentMethodId: string, userId: string, organizationId: string) {
    try {
      // Check if payment method exists and belongs to the user or organization
      const paymentMethod = await db.paymentMethod.findFirst({
        where: {
          id: paymentMethodId,
          OR: [
            { userId },
            { organizationId },
          ],
        },
      });
      
      if (!paymentMethod) {
        throw new Error("Payment method not found or does not belong to the user or organization");
      }
      
      // Unset any existing default payment methods
      await db.paymentMethod.updateMany({
        where: {
          OR: [
            { userId, isDefault: true },
            { organizationId, isDefault: true },
          ],
        },
        data: {
          isDefault: false,
        },
      });
      
      // Set the specified payment method as default
      const updatedPaymentMethod = await db.paymentMethod.update({
        where: { id: paymentMethodId },
        data: {
          isDefault: true,
        },
      });
      
      // Log audit event
      await auditService.log(
        "PAYMENT_METHOD_UPDATED",
        `Payment method set as default: ${paymentMethod.type}`,
        {
          paymentMethodId,
          type: paymentMethod.type,
        },
        userId,
        organizationId
      );
      
      return updatedPaymentMethod;
    } catch (error) {
      logger.error("Error setting default payment method:", error);
      throw new Error(`Failed to set default payment method: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Get payment history for a user or organization
   * @param userId User ID
   * @param organizationId Organization ID
   * @param limit Maximum number of payments to return
   * @param offset Offset for pagination
   * @returns Payment history
   */
  async getPaymentHistory(userId: string, organizationId: string, limit: number = 20, offset: number = 0) {
    try {
      const payments = await db.payment.findMany({
        where: {
          OR: [
            { userId },
            { organizationId },
          ],
        },
        include: {
          paymentMethod: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
        skip: offset,
      });
      
      const totalCount = await db.payment.count({
        where: {
          OR: [
            { userId },
            { organizationId },
          ],
        },
      });
      
      return {
        payments,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount,
        },
      };
    } catch (error) {
      logger.error("Error getting payment history:", error);
      throw new Error(`Failed to get payment history: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}

// Export singleton instance
export const paymentService = new PaymentService();
