import { 
  CarbonCreditStatus, 
  VerificationStatus, 
  CarbonCreditDocumentType, 
  DocumentStatus,
  OrderType,
  OrderStatus,
  TransactionType,
  TransactionStatus,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { NotificationService } from '@/lib/notification-service';

/**
 * Carbon credit creation data interface
 */
export interface CarbonCreditCreationData {
  name: string;
  description?: string;
  quantity: number;
  price: number;
  minPurchaseQuantity?: number;
  vintage: number;
  standard: string;
  methodology: string;
  location?: string;
  country?: string;
  projectId?: string;
  serialNumber?: string;
  certificationDate?: Date;
  expirationDate?: Date;
  verificationBody?: string;
  images?: string[];
  documents?: {
    name: string;
    type: CarbonCreditDocumentType;
    url: string;
    notes?: string;
  }[];
  tokenId?: string;
  contractAddress?: string;
  chainId?: number;
  metadata?: any;
  userId: string;
  organizationId: string;
}

/**
 * Carbon credit service for managing carbon credits
 */
export class CarbonCreditService {
  /**
   * Create a new carbon credit
   * @param data Carbon credit creation data
   * @returns Created carbon credit
   */
  static async createCarbonCredit(data: CarbonCreditCreationData) {
    try {
      // Create the carbon credit
      const carbonCredit = await db.carbonCredit.create({
        data: {
          name: data.name,
          description: data.description,
          quantity: data.quantity,
          availableQuantity: data.quantity, // Initially, all credits are available
          price: data.price,
          minPurchaseQuantity: data.minPurchaseQuantity,
          vintage: data.vintage,
          standard: data.standard,
          methodology: data.methodology,
          location: data.location,
          country: data.country,
          projectId: data.projectId,
          serialNumber: data.serialNumber,
          certificationDate: data.certificationDate,
          expirationDate: data.expirationDate,
          verificationBody: data.verificationBody,
          status: CarbonCreditStatus.PENDING,
          verificationStatus: VerificationStatus.PENDING,
          images: data.images,
          tokenId: data.tokenId,
          contractAddress: data.contractAddress,
          chainId: data.chainId,
          metadata: data.metadata as Prisma.JsonObject,
          user: { connect: { id: data.userId } },
          organization: { connect: { id: data.organizationId } },
        },
      });

      // Add documents if provided
      if (data.documents && data.documents.length > 0) {
        for (const doc of data.documents) {
          await db.carbonCreditDocument.create({
            data: {
              name: doc.name,
              type: doc.type,
              url: doc.url,
              notes: doc.notes,
              status: DocumentStatus.PENDING,
              carbonCredit: { connect: { id: carbonCredit.id } },
            },
          });
        }
      }

      // Add initial price history
      await db.carbonCreditPrice.create({
        data: {
          price: data.price,
          reason: 'Initial listing',
          carbonCredit: { connect: { id: carbonCredit.id } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'CARBON_CREDIT_CREATED',
          description: `Carbon credit ${carbonCredit.name} created`,
          user: { connect: { id: data.userId } },
          organization: { connect: { id: data.organizationId } },
          metadata: {
            carbonCreditId: carbonCredit.id,
            carbonCreditName: carbonCredit.name,
            quantity: data.quantity,
            price: data.price,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Carbon Credit Created',
        message: `Your carbon credit ${carbonCredit.name} has been created and is pending verification.`,
        type: 'CREDIT',
        userId: data.userId,
        organizationId: data.organizationId,
        actionUrl: `/dashboard/carbon-credits/${carbonCredit.id}`,
        actionLabel: 'View Carbon Credit',
      });

      // Send notification to platform admins
      const platformAdmins = await db.user.findMany({
        where: { role: 'ADMIN' },
      });

      for (const admin of platformAdmins) {
        await NotificationService.createNotification({
          title: 'New Carbon Credit',
          message: `A new carbon credit ${carbonCredit.name} has been created and needs verification.`,
          type: 'CREDIT',
          priority: 'HIGH',
          userId: admin.id,
          actionUrl: `/admin/carbon-credits/${carbonCredit.id}/verification`,
          actionLabel: 'Verify Carbon Credit',
        });
      }

      logger.info(`Carbon credit ${carbonCredit.id} created by user ${data.userId}`);

      return carbonCredit;
    } catch (error) {
      logger.error('Error creating carbon credit:', error);
      throw new Error('Failed to create carbon credit');
    }
  }

  /**
   * Verify a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param verifierId Verifier user ID
   * @param status Verification status
   * @param notes Optional notes
   * @returns Updated carbon credit
   */
  static async verifyCarbonCredit(
    carbonCreditId: string,
    verifierId: string,
    status: VerificationStatus,
    notes?: string
  ) {
    try {
      // Get the carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          user: true,
          organization: true,
        },
      });

      if (!carbonCredit) {
        throw new Error('Carbon credit not found');
      }

      // Update the carbon credit status
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id: carbonCreditId },
        data: {
          verificationStatus: status,
          // If verified, update the status to VERIFIED
          ...(status === VerificationStatus.VERIFIED && { status: CarbonCreditStatus.VERIFIED }),
        },
      });

      // Create a verification record
      await db.carbonCreditVerification.create({
        data: {
          status,
          verifier: 'Platform Admin',
          notes,
          carbonCredit: { connect: { id: carbonCreditId } },
        },
      });

      // Update document statuses
      await db.carbonCreditDocument.updateMany({
        where: {
          carbonCreditId,
          status: DocumentStatus.PENDING,
        },
        data: {
          status: status === VerificationStatus.VERIFIED ? DocumentStatus.APPROVED : DocumentStatus.REJECTED,
          notes,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'CARBON_CREDIT_UPDATED',
          description: `Carbon credit ${carbonCredit.name} verification status updated to ${status}`,
          user: { connect: { id: verifierId } },
          organization: { connect: { id: carbonCredit.organizationId } },
          metadata: {
            carbonCreditId,
            carbonCreditName: carbonCredit.name,
            status,
            notes,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the carbon credit owner
      await NotificationService.createNotification({
        title: 'Carbon Credit Verification Update',
        message: `Your carbon credit ${carbonCredit.name} verification status has been updated to ${status}.${notes ? ` Notes: ${notes}` : ''}`,
        type: 'VERIFICATION',
        priority: 'HIGH',
        userId: carbonCredit.userId,
        organizationId: carbonCredit.organizationId,
        actionUrl: `/dashboard/carbon-credits/${carbonCreditId}`,
        actionLabel: 'View Carbon Credit',
      });

      logger.info(`Carbon credit ${carbonCreditId} verification status updated to ${status} by user ${verifierId}`);

      return updatedCarbonCredit;
    } catch (error) {
      logger.error('Error verifying carbon credit:', error);
      throw new Error('Failed to verify carbon credit');
    }
  }

  /**
   * List a carbon credit on the marketplace
   * @param carbonCreditId Carbon credit ID
   * @param userId User ID
   * @param price Optional new price
   * @returns Updated carbon credit
   */
  static async listCarbonCredit(carbonCreditId: string, userId: string, price?: number) {
    try {
      // Get the carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          organization: true,
        },
      });

      if (!carbonCredit) {
        throw new Error('Carbon credit not found');
      }

      // Check if the carbon credit is verified
      if (carbonCredit.verificationStatus !== VerificationStatus.VERIFIED) {
        throw new Error('Carbon credit must be verified before listing');
      }

      // Check if the user has permission to list the carbon credit
      if (carbonCredit.userId !== userId) {
        const user = await db.user.findUnique({
          where: { id: userId },
        });

        if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
          throw new Error('You do not have permission to list this carbon credit');
        }
      }

      // Update the carbon credit
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id: carbonCreditId },
        data: {
          status: CarbonCreditStatus.LISTED,
          listingDate: new Date(),
          ...(price && { price }),
        },
      });

      // If price was updated, add to price history
      if (price && price !== carbonCredit.price) {
        await db.carbonCreditPrice.create({
          data: {
            price,
            reason: 'Price update on listing',
            carbonCredit: { connect: { id: carbonCreditId } },
          },
        });
      }

      // Calculate listing fee
      const listingFee = carbonCredit.price * carbonCredit.availableQuantity * carbonCredit.organization.listingFeeRate;

      // Create a billing record for the listing fee
      await db.billingRecord.create({
        data: {
          amount: listingFee,
          description: `Listing fee for ${carbonCredit.name}`,
          type: 'LISTING_FEE',
          organization: { connect: { id: carbonCredit.organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'CARBON_CREDIT_UPDATED',
          description: `Carbon credit ${carbonCredit.name} listed on marketplace`,
          user: { connect: { id: userId } },
          organization: { connect: { id: carbonCredit.organizationId } },
          metadata: {
            carbonCreditId,
            carbonCreditName: carbonCredit.name,
            price: updatedCarbonCredit.price,
            availableQuantity: updatedCarbonCredit.availableQuantity,
            listingFee,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the carbon credit owner
      await NotificationService.createNotification({
        title: 'Carbon Credit Listed',
        message: `Your carbon credit ${carbonCredit.name} has been listed on the marketplace.`,
        type: 'CREDIT',
        userId: carbonCredit.userId,
        organizationId: carbonCredit.organizationId,
        actionUrl: `/dashboard/carbon-credits/${carbonCreditId}`,
        actionLabel: 'View Carbon Credit',
      });

      // Send notification to all users about the new listing
      const users = await db.user.findMany({
        where: {
          NOT: {
            id: carbonCredit.userId,
          },
        },
      });

      for (const user of users) {
        await NotificationService.createNotification({
          title: 'New Carbon Credit Listing',
          message: `${carbonCredit.name} is now available on the marketplace.`,
          type: 'MARKETPLACE',
          userId: user.id,
          actionUrl: `/marketplace/${carbonCreditId}`,
          actionLabel: 'View Listing',
        });
      }

      logger.info(`Carbon credit ${carbonCreditId} listed by user ${userId}`);

      return updatedCarbonCredit;
    } catch (error) {
      logger.error('Error listing carbon credit:', error);
      throw error;
    }
  }

  /**
   * Update carbon credit price
   * @param carbonCreditId Carbon credit ID
   * @param userId User ID
   * @param price New price
   * @param reason Reason for price change
   * @returns Updated carbon credit
   */
  static async updatePrice(carbonCreditId: string, userId: string, price: number, reason?: string) {
    try {
      // Get the carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
      });

      if (!carbonCredit) {
        throw new Error('Carbon credit not found');
      }

      // Check if the user has permission to update the price
      if (carbonCredit.userId !== userId) {
        const user = await db.user.findUnique({
          where: { id: userId },
        });

        if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
          throw new Error('You do not have permission to update this carbon credit');
        }
      }

      // Update the carbon credit
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id: carbonCreditId },
        data: {
          price,
        },
      });

      // Add to price history
      await db.carbonCreditPrice.create({
        data: {
          price,
          reason: reason || 'Price update',
          carbonCredit: { connect: { id: carbonCreditId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'CARBON_CREDIT_UPDATED',
          description: `Carbon credit ${carbonCredit.name} price updated to ${price}`,
          user: { connect: { id: userId } },
          organization: { connect: { id: carbonCredit.organizationId } },
          metadata: {
            carbonCreditId,
            carbonCreditName: carbonCredit.name,
            oldPrice: carbonCredit.price,
            newPrice: price,
            reason,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the carbon credit owner
      await NotificationService.createNotification({
        title: 'Carbon Credit Price Updated',
        message: `The price of your carbon credit ${carbonCredit.name} has been updated to $${price}.`,
        type: 'CREDIT',
        userId: carbonCredit.userId,
        organizationId: carbonCredit.organizationId,
        actionUrl: `/dashboard/carbon-credits/${carbonCreditId}`,
        actionLabel: 'View Carbon Credit',
      });

      logger.info(`Carbon credit ${carbonCreditId} price updated to ${price} by user ${userId}`);

      return updatedCarbonCredit;
    } catch (error) {
      logger.error('Error updating carbon credit price:', error);
      throw error;
    }
  }

  /**
   * Create a buy order for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param buyerId Buyer user ID
   * @param quantity Quantity to buy
   * @returns Created order
   */
  static async createBuyOrder(carbonCreditId: string, buyerId: string, quantity: number) {
    try {
      // Get the carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          user: true,
          organization: true,
        },
      });

      if (!carbonCredit) {
        throw new Error('Carbon credit not found');
      }

      // Check if the carbon credit is listed
      if (carbonCredit.status !== CarbonCreditStatus.LISTED) {
        throw new Error('Carbon credit is not available for purchase');
      }

      // Check if there's enough quantity available
      if (carbonCredit.availableQuantity < quantity) {
        throw new Error('Not enough quantity available');
      }

      // Check if the quantity meets the minimum purchase requirement
      if (carbonCredit.minPurchaseQuantity && quantity < carbonCredit.minPurchaseQuantity) {
        throw new Error(`Minimum purchase quantity is ${carbonCredit.minPurchaseQuantity}`);
      }

      // Get the buyer
      const buyer = await db.user.findUnique({
        where: { id: buyerId },
        include: {
          organization: true,
        },
      });

      if (!buyer) {
        throw new Error('Buyer not found');
      }

      // Check if the buyer is trying to buy their own carbon credit
      if (carbonCredit.userId === buyerId) {
        throw new Error('You cannot buy your own carbon credit');
      }

      // Calculate the total price
      const totalPrice = carbonCredit.price * quantity;

      // Calculate the transaction fee
      const transactionFee = totalPrice * carbonCredit.organization.transactionFeeRate;

      // Create the order
      const order = await db.order.create({
        data: {
          type: OrderType.BUY,
          quantity,
          price: carbonCredit.price,
          status: OrderStatus.PENDING,
          buyer: { connect: { id: buyerId } },
          seller: { connect: { id: carbonCredit.userId } },
          carbonCredit: { connect: { id: carbonCreditId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'ORDER_CREATED',
          description: `Buy order created for ${quantity} units of ${carbonCredit.name}`,
          user: { connect: { id: buyerId } },
          organization: { connect: { id: buyer.organizationId! } },
          metadata: {
            orderId: order.id,
            carbonCreditId,
            carbonCreditName: carbonCredit.name,
            quantity,
            price: carbonCredit.price,
            totalPrice,
            transactionFee,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the buyer
      await NotificationService.createNotification({
        title: 'Buy Order Created',
        message: `You have created a buy order for ${quantity} units of ${carbonCredit.name} at $${carbonCredit.price} each.`,
        type: 'ORDER',
        userId: buyerId,
        organizationId: buyer.organizationId!,
        actionUrl: `/dashboard/orders/${order.id}`,
        actionLabel: 'View Order',
      });

      // Send notification to the seller
      await NotificationService.createNotification({
        title: 'New Buy Order',
        message: `${buyer.name || buyer.email} has placed a buy order for ${quantity} units of ${carbonCredit.name}.`,
        type: 'ORDER',
        priority: 'HIGH',
        userId: carbonCredit.userId,
        organizationId: carbonCredit.organizationId,
        actionUrl: `/dashboard/orders/${order.id}`,
        actionLabel: 'View Order',
      });

      logger.info(`Buy order ${order.id} created by user ${buyerId} for carbon credit ${carbonCreditId}`);

      return order;
    } catch (error) {
      logger.error('Error creating buy order:', error);
      throw error;
    }
  }

  /**
   * Process an order (accept or reject)
   * @param orderId Order ID
   * @param sellerId Seller user ID
   * @param action Action to take (accept or reject)
   * @returns Updated order
   */
  static async processOrder(orderId: string, sellerId: string, action: 'accept' | 'reject') {
    try {
      // Get the order
      const order = await db.order.findUnique({
        where: { id: orderId },
        include: {
          buyer: true,
          seller: true,
          carbonCredit: true,
        },
      });

      if (!order) {
        throw new Error('Order not found');
      }

      // Check if the user is the seller
      if (order.sellerId !== sellerId) {
        throw new Error('You do not have permission to process this order');
      }

      // Check if the order is pending
      if (order.status !== OrderStatus.PENDING) {
        throw new Error('Order has already been processed');
      }

      if (action === 'accept') {
        // Check if there's enough quantity available
        if (order.carbonCredit.availableQuantity < order.quantity) {
          throw new Error('Not enough quantity available');
        }

        // Update the order status
        const updatedOrder = await db.order.update({
          where: { id: orderId },
          data: {
            status: OrderStatus.COMPLETED,
          },
        });

        // Update the carbon credit available quantity
        await db.carbonCredit.update({
          where: { id: order.carbonCreditId },
          data: {
            availableQuantity: {
              decrement: order.quantity,
            },
            // If all quantity is sold, update the status
            ...(order.carbonCredit.availableQuantity - order.quantity <= 0 && {
              status: CarbonCreditStatus.SOLD,
            }),
          },
        });

        // Calculate the total price
        const totalPrice = order.price * order.quantity;

        // Get the seller's organization
        const sellerOrg = await db.organization.findUnique({
          where: { id: order.seller.organizationId! },
        });

        if (!sellerOrg) {
          throw new Error('Seller organization not found');
        }

        // Calculate the transaction fee
        const transactionFee = totalPrice * sellerOrg.transactionFeeRate;

        // Create a transaction record
        const transaction = await db.transaction.create({
          data: {
            amount: totalPrice,
            fee: transactionFee,
            type: TransactionType.SALE,
            status: TransactionStatus.COMPLETED,
            order: { connect: { id: orderId } },
            // For simplicity, we're using the first wallet of the seller
            wallet: {
              connect: {
                id: (
                  await db.wallet.findFirst({
                    where: { userId: sellerId },
                  })
                )?.id,
              },
            },
          },
        });

        // Create a billing record for the transaction fee
        await db.billingRecord.create({
          data: {
            amount: transactionFee,
            description: `Transaction fee for order ${orderId}`,
            type: 'TRANSACTION_FEE',
            organization: { connect: { id: sellerOrg.id } },
          },
        });

        // Create an audit log
        await db.auditLog.create({
          data: {
            type: 'ORDER_UPDATED',
            description: `Order ${orderId} accepted by seller`,
            user: { connect: { id: sellerId } },
            organization: { connect: { id: sellerOrg.id } },
            metadata: {
              orderId,
              carbonCreditId: order.carbonCreditId,
              carbonCreditName: order.carbonCredit.name,
              quantity: order.quantity,
              price: order.price,
              totalPrice,
              transactionFee,
              transactionId: transaction.id,
            } as Prisma.JsonObject,
          },
        });

        // Send notification to the buyer
        await NotificationService.createNotification({
          title: 'Order Accepted',
          message: `Your order for ${order.quantity} units of ${order.carbonCredit.name} has been accepted.`,
          type: 'ORDER',
          priority: 'HIGH',
          userId: order.buyerId,
          organizationId: order.buyer.organizationId!,
          actionUrl: `/dashboard/orders/${orderId}`,
          actionLabel: 'View Order',
        });

        // Send notification to the seller
        await NotificationService.createNotification({
          title: 'Order Completed',
          message: `You have accepted the order for ${order.quantity} units of ${order.carbonCredit.name}.`,
          type: 'ORDER',
          userId: sellerId,
          organizationId: sellerOrg.id,
          actionUrl: `/dashboard/orders/${orderId}`,
          actionLabel: 'View Order',
        });

        logger.info(`Order ${orderId} accepted by seller ${sellerId}`);

        return updatedOrder;
      } else {
        // Update the order status
        const updatedOrder = await db.order.update({
          where: { id: orderId },
          data: {
            status: OrderStatus.CANCELLED,
          },
        });

        // Create an audit log
        await db.auditLog.create({
          data: {
            type: 'ORDER_UPDATED',
            description: `Order ${orderId} rejected by seller`,
            user: { connect: { id: sellerId } },
            organization: { connect: { id: order.seller.organizationId! } },
            metadata: {
              orderId,
              carbonCreditId: order.carbonCreditId,
              carbonCreditName: order.carbonCredit.name,
              quantity: order.quantity,
              price: order.price,
            } as Prisma.JsonObject,
          },
        });

        // Send notification to the buyer
        await NotificationService.createNotification({
          title: 'Order Rejected',
          message: `Your order for ${order.quantity} units of ${order.carbonCredit.name} has been rejected.`,
          type: 'ORDER',
          userId: order.buyerId,
          organizationId: order.buyer.organizationId!,
          actionUrl: `/dashboard/orders/${orderId}`,
          actionLabel: 'View Order',
        });

        // Send notification to the seller
        await NotificationService.createNotification({
          title: 'Order Rejected',
          message: `You have rejected the order for ${order.quantity} units of ${order.carbonCredit.name}.`,
          type: 'ORDER',
          userId: sellerId,
          organizationId: order.seller.organizationId!,
          actionUrl: `/dashboard/orders/${orderId}`,
          actionLabel: 'View Order',
        });

        logger.info(`Order ${orderId} rejected by seller ${sellerId}`);

        return updatedOrder;
      }
    } catch (error) {
      logger.error('Error processing order:', error);
      throw error;
    }
  }

  /**
   * Retire carbon credits
   * @param carbonCreditId Carbon credit ID
   * @param userId User ID
   * @param quantity Quantity to retire
   * @param reason Reason for retirement
   * @param beneficiary Beneficiary of retirement
   * @returns Updated carbon credit
   */
  static async retireCarbonCredits(
    carbonCreditId: string,
    userId: string,
    quantity: number,
    reason?: string,
    beneficiary?: string
  ) {
    try {
      // Get the carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
      });

      if (!carbonCredit) {
        throw new Error('Carbon credit not found');
      }

      // Check if the user has permission to retire the carbon credit
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Check if the user is the owner or an admin
      const isOwner = carbonCredit.userId === userId;
      const isAdmin = user.role === 'ADMIN' || user.role === 'ORGANIZATION_ADMIN';
      const isSameOrg = user.organizationId === carbonCredit.organizationId;

      if (!isOwner && !(isAdmin && isSameOrg)) {
        throw new Error('You do not have permission to retire this carbon credit');
      }

      // Check if there's enough quantity available
      if (carbonCredit.availableQuantity < quantity) {
        throw new Error('Not enough quantity available');
      }

      // Update the carbon credit
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id: carbonCreditId },
        data: {
          availableQuantity: {
            decrement: quantity,
          },
          // If all quantity is retired, update the status
          ...(carbonCredit.availableQuantity - quantity <= 0 && {
            status: CarbonCreditStatus.RETIRED,
          }),
          retirementDate: new Date(),
          retirementReason: reason,
          retirementBeneficiary: beneficiary,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'CARBON_CREDIT_UPDATED',
          description: `${quantity} units of ${carbonCredit.name} retired`,
          user: { connect: { id: userId } },
          organization: { connect: { id: carbonCredit.organizationId } },
          metadata: {
            carbonCreditId,
            carbonCreditName: carbonCredit.name,
            quantity,
            reason,
            beneficiary,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the carbon credit owner
      await NotificationService.createNotification({
        title: 'Carbon Credits Retired',
        message: `${quantity} units of ${carbonCredit.name} have been retired.${reason ? ` Reason: ${reason}` : ''}${beneficiary ? ` Beneficiary: ${beneficiary}` : ''}`,
        type: 'CREDIT',
        userId: carbonCredit.userId,
        organizationId: carbonCredit.organizationId,
        actionUrl: `/dashboard/carbon-credits/${carbonCreditId}`,
        actionLabel: 'View Carbon Credit',
      });

      logger.info(`${quantity} units of carbon credit ${carbonCreditId} retired by user ${userId}`);

      return updatedCarbonCredit;
    } catch (error) {
      logger.error('Error retiring carbon credits:', error);
      throw error;
    }
  }
}
