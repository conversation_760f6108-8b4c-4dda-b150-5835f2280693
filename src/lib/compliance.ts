// KYC verification levels
export enum KycLevel {
  BASIC = "Basic",
  INTERMEDIATE = "Intermediate",
  ADVANCED = "Advanced",
}

// Compliance document types
export enum ComplianceDocumentType {
  PASSPORT = "PASSPORT",
  SELFIE = "SELFIE",
  PROOF_OF_ADDRESS = "PROOF_OF_ADDRESS",
  BANK_STATEMENT = "BANK_STATEMENT",
  COMPANY_REGISTRATION = "COMPANY_REGISTRATION",
  TAX_CERTIFICATE = "TAX_CERTIFICATE",
}

// Verification status
export type VerificationStatus =
  | "NOT_STARTED"
  | "PENDING"
  | "APPROVED"
  | "REJECTED";

// Compliance status enum
export enum ComplianceStatus {
  PENDING = "PENDING",
  IN_REVIEW = "IN_REVIEW",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  EXPIRED = "EXPIRED"
}

// Verification step status
export type VerificationStepStatus =
  | "PENDING"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "FAILED";

// Verification step type
export type VerificationStepType =
  | "DOCUMENT_UPLOAD"
  | "IDENTITY_VERIFICATION"
  | "COMPLIANCE_CHECK";

// Verification step
export interface VerificationStep {
  type: VerificationStepType;
  title: string;
  description: string;
  status: VerificationStepStatus;
  timestamp?: string;
  message?: string;
  action?: string;
  estimatedCompletion?: string;
}

// Compliance risk levels
export enum ComplianceRiskLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}

// Risk assessment
export interface RiskAssessment {
  riskLevel: ComplianceRiskLevel;
  riskScore: number;
  riskFactors: string[];
  recommendations: string[];
  lastUpdated: string;
}

// Sanctions check
export interface SanctionsCheck {
  passed: boolean;
  matches: string[];
  lastChecked: string;
}

// Document verification result
export interface DocumentVerificationResult {
  documentType: ComplianceDocumentType;
  verified: boolean;
  issues: string[];
  verifiedAt?: string;
}

// KYC verification result
export interface KycVerificationResult {
  level: KycLevel;
  status: VerificationStatus;
  documents: DocumentVerificationResult[];
  submittedAt: string;
  updatedAt: string;
  expiresAt?: string;
  rejectionReason?: string;
}

// Compliance Manager class
export class ComplianceManager {
  /**
   * Verify KYC documents
   * @param userId User ID
   * @param organizationId Organization ID
   * @param level KYC level
   * @param documents Documents to verify
   * @returns Verification result
   */
  static async verifyKyc(
    userId: string,
    organizationId: string,
    level: KycLevel,
    documents: Array<{
      type: ComplianceDocumentType;
      name: string;
      url: string;
    }>
  ) {
    // In a real implementation, this would call a KYC provider API
    // For now, we'll just simulate a successful verification

    return {
      status: ComplianceStatus.APPROVED,
      level,
      documents: documents.map(doc => ({
        ...doc,
        status: ComplianceStatus.APPROVED,
      })),
      submittedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
    };
  }

  /**
   * Check AML status
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns AML status
   */
  static async checkAml(userId: string, organizationId: string) {
    // In a real implementation, this would call an AML provider API
    // For now, we'll just simulate a successful check

    return {
      status: ComplianceStatus.APPROVED,
      riskLevel: ComplianceRiskLevel.LOW,
      lastChecked: new Date().toISOString(),
      nextCheckDue: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    };
  }

  /**
   * Generate tax report
   * @param organizationId Organization ID
   * @param year Year
   * @param quarter Quarter
   * @returns Tax report
   */
  static async generateTaxReport(organizationId: string, year: number, quarter?: number) {
    // In a real implementation, this would generate a tax report
    // For now, we'll just simulate a successful report generation

    return {
      id: `tax-report-${organizationId}-${year}-${quarter || 'full'}`,
      organizationId,
      year,
      quarter,
      status: "COMPLETED",
      url: "https://example.com/tax-report.pdf",
      generatedAt: new Date().toISOString(),
    };
  }
}

// Helper functions

// Get badge variant for risk level
export function getRiskLevelBadge(level: ComplianceRiskLevel): "success" | "warning" | "destructive" | "outline" {
  switch (level) {
    case ComplianceRiskLevel.LOW:
      return "success";
    case ComplianceRiskLevel.MEDIUM:
      return "warning";
    case ComplianceRiskLevel.HIGH:
      return "destructive";
    default:
      return "outline";
  }
}

// Get badge variant for verification status
export function getVerificationStatusBadge(status: VerificationStatus): "success" | "warning" | "destructive" | "outline" {
  switch (status) {
    case "APPROVED":
      return "success";
    case "PENDING":
      return "warning";
    case "REJECTED":
      return "destructive";
    default:
      return "outline";
  }
}

// Get required documents for KYC level
export function getRequiredDocumentsForLevel(level: KycLevel): ComplianceDocumentType[] {
  switch (level) {
    case KycLevel.BASIC:
      return [
        ComplianceDocumentType.PASSPORT,
        ComplianceDocumentType.SELFIE,
      ];
    case KycLevel.INTERMEDIATE:
      return [
        ComplianceDocumentType.PASSPORT,
        ComplianceDocumentType.SELFIE,
        ComplianceDocumentType.PROOF_OF_ADDRESS,
      ];
    case KycLevel.ADVANCED:
      return [
        ComplianceDocumentType.PASSPORT,
        ComplianceDocumentType.SELFIE,
        ComplianceDocumentType.PROOF_OF_ADDRESS,
        ComplianceDocumentType.BANK_STATEMENT,
        ComplianceDocumentType.COMPANY_REGISTRATION,
        ComplianceDocumentType.TAX_CERTIFICATE,
      ];
    default:
      return [];
  }
}
