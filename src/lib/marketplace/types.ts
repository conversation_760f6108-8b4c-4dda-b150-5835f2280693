import { CarbonCreditStatus, VerificationStatus } from '@prisma/client';
import { OrderBook, MarketSummary } from '@/lib/orders/types';

/**
 * Marketplace listing interface
 */
export interface MarketplaceListing {
  id: string;
  carbonCreditId: string;
  projectId: string;
  vintage: number;
  standard: string;
  methodology: string;
  description: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  status: CarbonCreditStatus;
  verificationStatus: VerificationStatus;
  organizationId: string;
  organizationName?: string;
  userId: string;
  userName?: string;
  listingDate: Date;
  expirationDate?: Date;
  orderBook?: OrderBook;
  marketSummary?: MarketSummary;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Marketplace filter interface
 */
export interface MarketplaceFilter {
  projectId?: string;
  vintage?: number;
  standard?: string;
  methodology?: string;
  organizationId?: string;
  minPrice?: number;
  maxPrice?: number;
  minQuantity?: number;
  maxQuantity?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Marketplace pagination interface
 */
export interface MarketplacePagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Marketplace response interface
 */
export interface MarketplaceResponse {
  listings: MarketplaceListing[];
  pagination: MarketplacePagination;
}

/**
 * Marketplace statistics interface
 */
export interface MarketplaceStatistics {
  totalListings: number;
  totalVolume: number;
  totalValue: number;
  averagePrice: number;
  topStandards: {
    standard: string;
    count: number;
    volume: number;
  }[];
  topMethodologies: {
    methodology: string;
    count: number;
    volume: number;
  }[];
  priceRange: {
    min: number;
    max: number;
    avg: number;
  };
  vintageDistribution: {
    vintage: number;
    count: number;
    volume: number;
  }[];
}

/**
 * Featured listing interface
 */
export interface FeaturedListing {
  id: string;
  carbonCreditId: string;
  projectId: string;
  vintage: number;
  standard: string;
  methodology: string;
  description: string;
  quantity: number;
  price: number;
  organizationId: string;
  organizationName?: string;
  featuredUntil: Date;
  createdAt: Date;
}

/**
 * Marketplace search result interface
 */
export interface MarketplaceSearchResult {
  listings: MarketplaceListing[];
  pagination: MarketplacePagination;
  facets: {
    standards: { value: string; count: number }[];
    methodologies: { value: string; count: number }[];
    vintages: { value: number; count: number }[];
    organizations: { value: string; name: string; count: number }[];
    priceRanges: { min: number; max: number; count: number }[];
  };
}
