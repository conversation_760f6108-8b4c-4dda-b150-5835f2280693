import { 
  MarketplaceListing, 
  MarketplaceFilter, 
  MarketplaceResponse,
  MarketplaceStatistics,
  FeaturedListing,
  MarketplaceSearchResult
} from './types';
import { MarketplaceService } from './service';

/**
 * Marketplace manager
 */
export class MarketplaceManager {
  /**
   * Get marketplace listings
   * @param filter Filter options
   * @returns Marketplace listings and pagination
   */
  static async getListings(filter: MarketplaceFilter = {}): Promise<MarketplaceResponse> {
    return MarketplaceService.getListings(filter);
  }

  /**
   * Get marketplace listing by ID
   * @param id Listing ID (carbon credit ID)
   * @returns Marketplace listing
   */
  static async getListingById(id: string): Promise<MarketplaceListing | null> {
    return MarketplaceService.getListingById(id);
  }

  /**
   * Get marketplace statistics
   * @returns Marketplace statistics
   */
  static async getStatistics(): Promise<MarketplaceStatistics> {
    return MarketplaceService.getStatistics();
  }

  /**
   * Get featured listings
   * @param limit Number of featured listings to get
   * @returns Featured listings
   */
  static async getFeaturedListings(limit: number = 5): Promise<FeaturedListing[]> {
    return MarketplaceService.getFeaturedListings(limit);
  }

  /**
   * Create a featured listing
   * @param carbonCreditId Carbon credit ID
   * @param featuredUntil Date until which the listing is featured
   * @param userId User ID creating the featured listing
   * @returns Created featured listing
   */
  static async createFeaturedListing(
    carbonCreditId: string,
    featuredUntil: Date,
    userId: string
  ): Promise<FeaturedListing> {
    return MarketplaceService.createFeaturedListing(carbonCreditId, featuredUntil, userId);
  }

  /**
   * Search marketplace listings with facets
   * @param filter Filter options
   * @returns Search results with facets
   */
  static async searchListings(filter: MarketplaceFilter = {}): Promise<MarketplaceSearchResult> {
    return MarketplaceService.searchListings(filter);
  }
}

// Create a singleton instance
const marketplaceManager = new MarketplaceManager();

// Export the singleton instance
export { marketplaceManager };

// Export types
export {
  MarketplaceListing,
  MarketplaceFilter,
  MarketplaceResponse,
  MarketplaceStatistics,
  FeaturedListing,
  MarketplaceSearchResult
};
