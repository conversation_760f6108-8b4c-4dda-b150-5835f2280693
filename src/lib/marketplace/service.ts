import { CarbonCreditStatus, VerificationStatus, Prisma } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { 
  MarketplaceListing, 
  MarketplaceFilter, 
  MarketplaceResponse,
  MarketplaceStatistics,
  FeaturedListing,
  MarketplaceSearchResult
} from './types';
import { orderManager } from '@/lib/orders';
import { auditManager } from '@/lib/audit';

/**
 * Marketplace service
 */
export class MarketplaceService {
  /**
   * Get marketplace listings
   * @param filter Filter options
   * @returns Marketplace listings and pagination
   */
  static async getListings(filter: MarketplaceFilter = {}): Promise<MarketplaceResponse> {
    try {
      const {
        projectId,
        vintage,
        standard,
        methodology,
        organizationId,
        minPrice,
        maxPrice,
        minQuantity,
        maxQuantity,
        search,
        sortBy = "listingDate",
        sortOrder = "desc",
        limit = 10,
        offset = 0,
      } = filter;
      
      // Build query
      const where: Prisma.CarbonCreditWhereInput = {
        status: CarbonCreditStatus.LISTED,
        verificationStatus: VerificationStatus.VERIFIED,
        availableQuantity: { gt: 0 },
        ...(projectId && { projectId }),
        ...(vintage && { vintage }),
        ...(standard && { standard }),
        ...(methodology && { methodology }),
        ...(organizationId && { organizationId }),
        ...(minPrice !== undefined && { price: { gte: minPrice } }),
        ...(maxPrice !== undefined && { price: { lte: maxPrice } }),
        ...(minQuantity !== undefined && { availableQuantity: { gte: minQuantity } }),
        ...(maxQuantity !== undefined && { availableQuantity: { lte: maxQuantity } }),
        ...(search && {
          OR: [
            { projectId: { contains: search, mode: "insensitive" } },
            { description: { contains: search, mode: "insensitive" } },
            { standard: { contains: search, mode: "insensitive" } },
            { methodology: { contains: search, mode: "insensitive" } },
            { organization: { name: { contains: search, mode: "insensitive" } } },
          ],
        }),
      };
      
      // Get carbon credits
      const carbonCredits = await db.carbonCredit.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.carbonCredit.count({ where });
      
      // Convert to marketplace listings
      const listings: MarketplaceListing[] = carbonCredits.map((credit) => ({
        id: credit.id,
        carbonCreditId: credit.id,
        projectId: credit.projectId,
        vintage: credit.vintage,
        standard: credit.standard,
        methodology: credit.methodology,
        description: credit.description,
        quantity: credit.quantity,
        availableQuantity: credit.availableQuantity,
        price: credit.price,
        status: credit.status,
        verificationStatus: credit.verificationStatus,
        organizationId: credit.organizationId,
        organizationName: credit.organization?.name,
        userId: credit.userId,
        userName: credit.user?.name,
        listingDate: credit.listingDate!,
        expirationDate: credit.expirationDate || undefined,
        createdAt: credit.createdAt,
        updatedAt: credit.updatedAt,
      }));
      
      // Get order books and market summaries for each listing
      await Promise.all(
        listings.map(async (listing) => {
          try {
            listing.orderBook = await orderManager.getOrderBook(listing.carbonCreditId);
            listing.marketSummary = await orderManager.getMarketSummary(listing.carbonCreditId);
          } catch (error) {
            logger.warn(`Error getting order book or market summary for carbon credit ${listing.carbonCreditId}:`, error);
          }
        })
      );
      
      return {
        listings,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting marketplace listings:", error);
      throw new Error(`Failed to get marketplace listings: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get marketplace listing by ID
   * @param id Listing ID (carbon credit ID)
   * @returns Marketplace listing
   */
  static async getListingById(id: string): Promise<MarketplaceListing | null> {
    try {
      // Get carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      
      if (!carbonCredit) {
        return null;
      }
      
      // Convert to marketplace listing
      const listing: MarketplaceListing = {
        id: carbonCredit.id,
        carbonCreditId: carbonCredit.id,
        projectId: carbonCredit.projectId,
        vintage: carbonCredit.vintage,
        standard: carbonCredit.standard,
        methodology: carbonCredit.methodology,
        description: carbonCredit.description,
        quantity: carbonCredit.quantity,
        availableQuantity: carbonCredit.availableQuantity,
        price: carbonCredit.price,
        status: carbonCredit.status,
        verificationStatus: carbonCredit.verificationStatus,
        organizationId: carbonCredit.organizationId,
        organizationName: carbonCredit.organization?.name,
        userId: carbonCredit.userId,
        userName: carbonCredit.user?.name,
        listingDate: carbonCredit.listingDate!,
        expirationDate: carbonCredit.expirationDate || undefined,
        createdAt: carbonCredit.createdAt,
        updatedAt: carbonCredit.updatedAt,
      };
      
      // Get order book and market summary
      try {
        listing.orderBook = await orderManager.getOrderBook(listing.carbonCreditId);
        listing.marketSummary = await orderManager.getMarketSummary(listing.carbonCreditId);
      } catch (error) {
        logger.warn(`Error getting order book or market summary for carbon credit ${listing.carbonCreditId}:`, error);
      }
      
      // Create audit log for viewing listing
      await auditManager.createAuditLog({
        type: "MARKETPLACE_LISTING_VIEWED",
        description: `Marketplace listing viewed: ${listing.projectId} (${listing.vintage})`,
        metadata: {
          listingId: listing.id,
          projectId: listing.projectId,
          vintage: listing.vintage,
          standard: listing.standard,
          methodology: listing.methodology,
        },
      });
      
      return listing;
    } catch (error) {
      logger.error(`Error getting marketplace listing ${id}:`, error);
      throw new Error(`Failed to get marketplace listing: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get marketplace statistics
   * @returns Marketplace statistics
   */
  static async getStatistics(): Promise<MarketplaceStatistics> {
    try {
      // Get total listings
      const totalListings = await db.carbonCredit.count({
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
      });
      
      // Get total volume and value
      const volumeAndValue = await db.carbonCredit.aggregate({
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
        _sum: {
          availableQuantity: true,
        },
      });
      
      const totalVolume = volumeAndValue._sum.availableQuantity || 0;
      
      // Get average price
      const avgPrice = await db.carbonCredit.aggregate({
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
        _avg: {
          price: true,
        },
      });
      
      const averagePrice = avgPrice._avg.price || 0;
      const totalValue = totalVolume * averagePrice;
      
      // Get top standards
      const standardsResult = await db.carbonCredit.groupBy({
        by: ['standard'],
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
        _count: {
          id: true,
        },
        _sum: {
          availableQuantity: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 5,
      });
      
      const topStandards = standardsResult.map((result) => ({
        standard: result.standard,
        count: result._count.id,
        volume: result._sum.availableQuantity || 0,
      }));
      
      // Get top methodologies
      const methodologiesResult = await db.carbonCredit.groupBy({
        by: ['methodology'],
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
        _count: {
          id: true,
        },
        _sum: {
          availableQuantity: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 5,
      });
      
      const topMethodologies = methodologiesResult.map((result) => ({
        methodology: result.methodology,
        count: result._count.id,
        volume: result._sum.availableQuantity || 0,
      }));
      
      // Get price range
      const priceRange = await db.carbonCredit.aggregate({
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
        _min: {
          price: true,
        },
        _max: {
          price: true,
        },
        _avg: {
          price: true,
        },
      });
      
      // Get vintage distribution
      const vintageResult = await db.carbonCredit.groupBy({
        by: ['vintage'],
        where: {
          status: CarbonCreditStatus.LISTED,
          verificationStatus: VerificationStatus.VERIFIED,
          availableQuantity: { gt: 0 },
        },
        _count: {
          id: true,
        },
        _sum: {
          availableQuantity: true,
        },
        orderBy: {
          vintage: 'desc',
        },
        take: 10,
      });
      
      const vintageDistribution = vintageResult.map((result) => ({
        vintage: result.vintage,
        count: result._count.id,
        volume: result._sum.availableQuantity || 0,
      }));
      
      return {
        totalListings,
        totalVolume,
        totalValue,
        averagePrice,
        topStandards,
        topMethodologies,
        priceRange: {
          min: priceRange._min.price || 0,
          max: priceRange._max.price || 0,
          avg: priceRange._avg.price || 0,
        },
        vintageDistribution,
      };
    } catch (error) {
      logger.error("Error getting marketplace statistics:", error);
      throw new Error(`Failed to get marketplace statistics: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get featured listings
   * @param limit Number of featured listings to get
   * @returns Featured listings
   */
  static async getFeaturedListings(limit: number = 5): Promise<FeaturedListing[]> {
    try {
      // Get featured listings
      const featuredListings = await db.featuredListing.findMany({
        where: {
          featuredUntil: {
            gte: new Date(),
          },
          carbonCredit: {
            status: CarbonCreditStatus.LISTED,
            verificationStatus: VerificationStatus.VERIFIED,
            availableQuantity: { gt: 0 },
          },
        },
        include: {
          carbonCredit: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });
      
      // Convert to featured listings
      return featuredListings.map((featured) => ({
        id: featured.id,
        carbonCreditId: featured.carbonCreditId,
        projectId: featured.carbonCredit.projectId,
        vintage: featured.carbonCredit.vintage,
        standard: featured.carbonCredit.standard,
        methodology: featured.carbonCredit.methodology,
        description: featured.carbonCredit.description,
        quantity: featured.carbonCredit.quantity,
        price: featured.carbonCredit.price,
        organizationId: featured.carbonCredit.organizationId,
        organizationName: featured.carbonCredit.organization?.name,
        featuredUntil: featured.featuredUntil,
        createdAt: featured.createdAt,
      }));
    } catch (error) {
      logger.error("Error getting featured listings:", error);
      throw new Error(`Failed to get featured listings: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a featured listing
   * @param carbonCreditId Carbon credit ID
   * @param featuredUntil Date until which the listing is featured
   * @param userId User ID creating the featured listing
   * @returns Created featured listing
   */
  static async createFeaturedListing(
    carbonCreditId: string,
    featuredUntil: Date,
    userId: string
  ): Promise<FeaturedListing> {
    try {
      // Get carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          organization: true,
        },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${carbonCreditId}`);
      }
      
      // Check if carbon credit is listed
      if (carbonCredit.status !== CarbonCreditStatus.LISTED) {
        throw new Error(`Carbon credit is not listed: ${carbonCreditId}`);
      }
      
      // Check if carbon credit is verified
      if (carbonCredit.verificationStatus !== VerificationStatus.VERIFIED) {
        throw new Error(`Carbon credit is not verified: ${carbonCreditId}`);
      }
      
      // Check if user has permission
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === carbonCredit.organizationId;
      
      if (!isAdmin && !isOrgAdmin) {
        throw new Error("You do not have permission to create a featured listing");
      }
      
      // Create featured listing
      const featuredListing = await db.featuredListing.create({
        data: {
          carbonCredit: {
            connect: { id: carbonCreditId },
          },
          featuredUntil,
          createdBy: {
            connect: { id: userId },
          },
        },
        include: {
          carbonCredit: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });
      
      // Create audit log
      await auditManager.createAuditLog({
        type: "FEATURED_LISTING_CREATED",
        description: `Featured listing created for carbon credit ${carbonCreditId}`,
        userId,
        organizationId: carbonCredit.organizationId,
        metadata: {
          featuredListingId: featuredListing.id,
          carbonCreditId,
          featuredUntil,
        },
      });
      
      // Convert to featured listing
      return {
        id: featuredListing.id,
        carbonCreditId: featuredListing.carbonCreditId,
        projectId: featuredListing.carbonCredit.projectId,
        vintage: featuredListing.carbonCredit.vintage,
        standard: featuredListing.carbonCredit.standard,
        methodology: featuredListing.carbonCredit.methodology,
        description: featuredListing.carbonCredit.description,
        quantity: featuredListing.carbonCredit.quantity,
        price: featuredListing.carbonCredit.price,
        organizationId: featuredListing.carbonCredit.organizationId,
        organizationName: featuredListing.carbonCredit.organization?.name,
        featuredUntil: featuredListing.featuredUntil,
        createdAt: featuredListing.createdAt,
      };
    } catch (error) {
      logger.error(`Error creating featured listing for carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to create featured listing: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Search marketplace listings with facets
   * @param filter Filter options
   * @returns Search results with facets
   */
  static async searchListings(filter: MarketplaceFilter = {}): Promise<MarketplaceSearchResult> {
    try {
      // Get listings
      const { listings, pagination } = await this.getListings(filter);
      
      // Get facets
      const facets = await this.getSearchFacets(filter);
      
      return {
        listings,
        pagination,
        facets,
      };
    } catch (error) {
      logger.error("Error searching marketplace listings:", error);
      throw new Error(`Failed to search marketplace listings: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get search facets
   * @param filter Filter options
   * @returns Search facets
   */
  private static async getSearchFacets(filter: MarketplaceFilter = {}): Promise<any> {
    try {
      // Build base query
      const baseWhere: Prisma.CarbonCreditWhereInput = {
        status: CarbonCreditStatus.LISTED,
        verificationStatus: VerificationStatus.VERIFIED,
        availableQuantity: { gt: 0 },
        ...(filter.projectId && { projectId: filter.projectId }),
        ...(filter.vintage && { vintage: filter.vintage }),
        ...(filter.standard && { standard: filter.standard }),
        ...(filter.methodology && { methodology: filter.methodology }),
        ...(filter.organizationId && { organizationId: filter.organizationId }),
        ...(filter.minPrice !== undefined && { price: { gte: filter.minPrice } }),
        ...(filter.maxPrice !== undefined && { price: { lte: filter.maxPrice } }),
        ...(filter.minQuantity !== undefined && { availableQuantity: { gte: filter.minQuantity } }),
        ...(filter.maxQuantity !== undefined && { availableQuantity: { lte: filter.maxQuantity } }),
        ...(filter.search && {
          OR: [
            { projectId: { contains: filter.search, mode: "insensitive" } },
            { description: { contains: filter.search, mode: "insensitive" } },
            { standard: { contains: filter.search, mode: "insensitive" } },
            { methodology: { contains: filter.search, mode: "insensitive" } },
            { organization: { name: { contains: filter.search, mode: "insensitive" } } },
          ],
        }),
      };
      
      // Get standard facets
      const standardsResult = await db.carbonCredit.groupBy({
        by: ['standard'],
        where: baseWhere,
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
      });
      
      const standards = standardsResult.map((result) => ({
        value: result.standard,
        count: result._count.id,
      }));
      
      // Get methodology facets
      const methodologiesResult = await db.carbonCredit.groupBy({
        by: ['methodology'],
        where: baseWhere,
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
      });
      
      const methodologies = methodologiesResult.map((result) => ({
        value: result.methodology,
        count: result._count.id,
      }));
      
      // Get vintage facets
      const vintagesResult = await db.carbonCredit.groupBy({
        by: ['vintage'],
        where: baseWhere,
        _count: {
          id: true,
        },
        orderBy: {
          vintage: 'desc',
        },
      });
      
      const vintages = vintagesResult.map((result) => ({
        value: result.vintage,
        count: result._count.id,
      }));
      
      // Get organization facets
      const organizationsResult = await db.carbonCredit.groupBy({
        by: ['organizationId'],
        where: baseWhere,
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
      });
      
      // Get organization names
      const organizationIds = organizationsResult.map((result) => result.organizationId);
      const organizations = await db.organization.findMany({
        where: {
          id: {
            in: organizationIds,
          },
        },
        select: {
          id: true,
          name: true,
        },
      });
      
      const organizationMap = organizations.reduce((map, org) => {
        map[org.id] = org.name;
        return map;
      }, {} as Record<string, string>);
      
      const organizationFacets = organizationsResult.map((result) => ({
        value: result.organizationId,
        name: organizationMap[result.organizationId] || "Unknown Organization",
        count: result._count.id,
      }));
      
      // Get price range facets
      const priceRanges = [
        { min: 0, max: 5 },
        { min: 5, max: 10 },
        { min: 10, max: 20 },
        { min: 20, max: 50 },
        { min: 50, max: 100 },
        { min: 100, max: Number.MAX_SAFE_INTEGER },
      ];
      
      const priceRangeFacets = await Promise.all(
        priceRanges.map(async (range) => {
          const count = await db.carbonCredit.count({
            where: {
              ...baseWhere,
              price: {
                gte: range.min,
                ...(range.max < Number.MAX_SAFE_INTEGER && { lt: range.max }),
              },
            },
          });
          
          return {
            min: range.min,
            max: range.max < Number.MAX_SAFE_INTEGER ? range.max : null,
            count,
          };
        })
      );
      
      return {
        standards,
        methodologies,
        vintages,
        organizations: organizationFacets,
        priceRanges: priceRangeFacets,
      };
    } catch (error) {
      logger.error("Error getting search facets:", error);
      throw new Error(`Failed to get search facets: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
