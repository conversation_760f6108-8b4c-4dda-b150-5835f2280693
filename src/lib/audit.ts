import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { AuditLogType } from "@prisma/client";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

/**
 * Audit service for logging important actions
 */
export class AuditService {
  /**
   * Log an audit event
   * @param type Audit log type
   * @param description Description of the action
   * @param metadata Additional metadata
   * @param userId User ID (optional, will try to get from session if not provided)
   * @param organizationId Organization ID (optional, will try to get from session if not provided)
   * @returns Created audit log
   */
  async log(
    type: AuditLogType,
    description: string,
    metadata?: any,
    userId?: string,
    organizationId?: string
  ) {
    try {
      // Try to get user from session if not provided
      if (!userId) {
        const session = await auth();
        userId = session?.user?.id;

        // If organization ID is not provided, get it from the session
        if (!organizationId && session?.user?.organizationId) {
          organizationId = session.user.organizationId;
        }
      }

      // Get IP address and user agent from headers
      const headersList = headers();
      const ipAddress = headersList.get("x-forwarded-for") ||
                        headersList.get("x-real-ip") ||
                        "unknown";
      const userAgent = headersList.get("user-agent") || "unknown";

      // Create audit log
      const auditLog = await db.auditLog.create({
        data: {
          type,
          description,
          metadata: metadata ? metadata : undefined,
          ipAddress,
          userAgent,
          userId,
          organizationId,
        },
      });

      logger.info(`Audit log created: ${type} - ${description}`);

      return auditLog;
    } catch (error) {
      logger.error(`Error creating audit log (${type}):`, error);
      // Don't throw error to prevent disrupting the main flow
      return null;
    }
  }

  /**
   * Log user creation
   * @param userId User ID
   * @param email User email
   * @param organizationId Organization ID (if applicable)
   */
  async logUserCreated(userId: string, email: string, organizationId?: string) {
    return this.log(
      AuditLogType.USER_CREATED,
      `User created: ${email}`,
      { userId, email },
      userId,
      organizationId
    );
  }

  /**
   * Log user update
   * @param userId User ID
   * @param email User email
   * @param changes Changes made
   * @param organizationId Organization ID (if applicable)
   */
  async logUserUpdated(userId: string, email: string, changes: any, organizationId?: string) {
    return this.log(
      AuditLogType.USER_UPDATED,
      `User updated: ${email}`,
      { userId, email, changes },
      userId,
      organizationId
    );
  }

  /**
   * Log organization creation
   * @param organizationId Organization ID
   * @param name Organization name
   * @param userId User ID who created the organization
   */
  async logOrganizationCreated(organizationId: string, name: string, userId: string) {
    return this.log(
      AuditLogType.ORGANIZATION_CREATED,
      `Organization created: ${name}`,
      { organizationId, name },
      userId,
      organizationId
    );
  }

  /**
   * Log organization update
   * @param organizationId Organization ID
   * @param name Organization name
   * @param changes Changes made
   * @param userId User ID who updated the organization
   */
  async logOrganizationUpdated(organizationId: string, name: string, changes: any, userId: string) {
    return this.log(
      AuditLogType.ORGANIZATION_UPDATED,
      `Organization updated: ${name}`,
      { organizationId, name, changes },
      userId,
      organizationId
    );
  }

  /**
   * Log carbon credit creation
   * @param creditId Carbon credit ID
   * @param name Carbon credit name
   * @param userId User ID who created the credit
   * @param organizationId Organization ID
   */
  async logCarbonCreditCreated(creditId: string, name: string, userId: string, organizationId: string) {
    return this.log(
      AuditLogType.CARBON_CREDIT_CREATED,
      `Carbon credit created: ${name}`,
      { creditId, name },
      userId,
      organizationId
    );
  }

  /**
   * Log carbon credit update
   * @param creditId Carbon credit ID
   * @param name Carbon credit name
   * @param changes Changes made
   * @param userId User ID who updated the credit
   * @param organizationId Organization ID
   */
  async logCarbonCreditUpdated(creditId: string, name: string, changes: any, userId: string, organizationId: string) {
    return this.log(
      AuditLogType.CARBON_CREDIT_UPDATED,
      `Carbon credit updated: ${name}`,
      { creditId, name, changes },
      userId,
      organizationId
    );
  }

  /**
   * Log order creation
   * @param orderId Order ID
   * @param type Order type
   * @param userId User ID who created the order
   * @param organizationId Organization ID
   */
  async logOrderCreated(orderId: string, type: string, userId: string, organizationId: string) {
    return this.log(
      AuditLogType.ORDER_CREATED,
      `Order created: ${type}`,
      { orderId, type },
      userId,
      organizationId
    );
  }

  /**
   * Log order update
   * @param orderId Order ID
   * @param type Order type
   * @param changes Changes made
   * @param userId User ID who updated the order
   * @param organizationId Organization ID
   */
  async logOrderUpdated(orderId: string, type: string, changes: any, userId: string, organizationId: string) {
    return this.log(
      AuditLogType.ORDER_UPDATED,
      `Order updated: ${type}`,
      { orderId, type, changes },
      userId,
      organizationId
    );
  }

  /**
   * Log transaction creation
   * @param transactionId Transaction ID
   * @param type Transaction type
   * @param amount Transaction amount
   * @param userId User ID associated with the transaction
   * @param organizationId Organization ID
   */
  async logTransactionCreated(transactionId: string, type: string, amount: number, userId: string, organizationId: string) {
    return this.log(
      AuditLogType.TRANSACTION_CREATED,
      `Transaction created: ${type} - ${amount}`,
      { transactionId, type, amount },
      userId,
      organizationId
    );
  }

  /**
   * Log wallet creation
   * @param walletId Wallet ID
   * @param address Wallet address
   * @param userId User ID associated with the wallet
   * @param organizationId Organization ID
   */
  async logWalletCreated(walletId: string, address: string, userId: string, organizationId?: string) {
    return this.log(
      AuditLogType.WALLET_CREATED,
      `Wallet created: ${address}`,
      { walletId, address },
      userId,
      organizationId
    );
  }

  /**
   * Log successful login
   * @param userId User ID
   * @param email User email
   * @param organizationId Organization ID (if applicable)
   */
  async logLoginSuccess(userId: string, email: string, organizationId?: string) {
    return this.log(
      AuditLogType.LOGIN_SUCCESS,
      `Login successful: ${email}`,
      { userId, email },
      userId,
      organizationId
    );
  }

  /**
   * Log failed login attempt
   * @param email User email
   */
  async logLoginFailed(email: string) {
    return this.log(
      AuditLogType.LOGIN_FAILED,
      `Login failed: ${email}`,
      { email }
    );
  }

  /**
   * Log password reset
   * @param userId User ID
   * @param email User email
   */
  async logPasswordReset(userId: string, email: string) {
    return this.log(
      AuditLogType.PASSWORD_RESET,
      `Password reset: ${email}`,
      { userId, email },
      userId
    );
  }

  /**
   * Log email verification
   * @param userId User ID
   * @param email User email
   */
  async logEmailVerified(userId: string, email: string) {
    return this.log(
      AuditLogType.EMAIL_VERIFIED,
      `Email verified: ${email}`,
      { userId, email },
      userId
    );
  }
}

// Export singleton instance
export const auditService = new AuditService();

// For backwards compatibility with existing code
export const auditManager = auditService;
