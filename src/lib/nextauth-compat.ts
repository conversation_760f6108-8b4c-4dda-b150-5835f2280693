/**
 * NextAuth Compatibility Layer
 * 
 * This module provides a compatibility layer for code using NextAuth v4 APIs
 * with our NextAuth v5 implementation.
 */
import { auth } from '@/lib/auth';
import type { Session } from 'next-auth';

/**
 * Compatibility function for NextAuth v4's getServerSession
 * This will allow existing v4 code to work with our v5 implementation
 */
export async function getServerSession(): Promise<Session | null> {
  return await auth();
}

// Export for direct import in API routes
export { getServerSession as default };