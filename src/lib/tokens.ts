import { randomBytes, createHash } from "crypto";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

// Token expiration time (in seconds)
const RESET_TOKEN_EXPIRES_IN = 3600; // 1 hour
const VERIFICATION_TOKEN_EXPIRES_IN = 86400; // 24 hours
const INVITATION_TOKEN_EXPIRES_IN = 604800; // 7 days

/**
 * Generate a password reset token for a user
 */
export async function generatePasswordResetToken(userId: string): Promise<string> {
  const token = randomBytes(32).toString("hex");
  const hashedToken = createHash("sha256").update(token).digest("hex");

  // Store the token in the database
  const expires = new Date(Date.now() + RESET_TOKEN_EXPIRES_IN * 1000);

  await db.passwordResetToken.create({
    data: {
      token: hashedToken,
      expires,
      userId,
    },
  });

  return token;
}

/**
 * Verify a password reset token
 */
export async function verifyPasswordResetToken(token: string): Promise<string | null> {
  const hashedToken = createHash("sha256").update(token).digest("hex");

  // Find the token in the database
  const resetToken = await db.passwordResetToken.findUnique({
    where: {
      token: hashedToken,
      expires: {
        gt: new Date(),
      },
    },
    include: {
      user: true,
    },
  });

  if (!resetToken) {
    return null;
  }

  // Delete the token after use
  await db.passwordResetToken.delete({
    where: {
      id: resetToken.id,
    },
  });

  return resetToken.userId;
}

/**
 * Generate an email verification token for a user
 */
export async function generateVerificationToken(userId: string, email: string): Promise<string> {
  const token = randomBytes(32).toString("hex");
  const hashedToken = createHash("sha256").update(token).digest("hex");

  // Store the token in the database
  const expires = new Date(Date.now() + VERIFICATION_TOKEN_EXPIRES_IN * 1000);

  await db.verificationToken.create({
    data: {
      token: hashedToken,
      expires,
      userId,
      email,
    },
  });

  return token;
}

/**
 * Verify an email verification token
 */
export async function verifyEmailVerificationToken(token: string): Promise<string | null> {
  return await verifyEmailToken(token);
}

/**
 * Verify an email token (alias for verifyEmailVerificationToken for API consistency)
 */
export async function verifyEmailToken(token: string): Promise<string | null> {
  const hashedToken = createHash("sha256").update(token).digest("hex");

  // Find the token in the database
  const verificationToken = await db.verificationToken.findUnique({
    where: {
      token: hashedToken,
      expires: {
        gt: new Date(),
      },
    },
  });

  if (!verificationToken) {
    return null;
  }

  // Delete the token after use
  await db.verificationToken.delete({
    where: {
      id: verificationToken.id,
    },
  });

  return verificationToken.userId;
}

/**
 * Generate a random token
 * @param length Token length (default: 32)
 * @returns Random token string
 */
export function generateRandomToken(length: number = 32): string {
  return randomBytes(length).toString("hex");
}

/**
 * Generate a unique invitation token for the new invitation system
 * @returns Unique invitation token
 */
export async function generateInvitationToken(): Promise<string> {
  // Generate a random token
  let token = generateRandomToken(16);

  // Check if token already exists
  let existingInvitation = await db.invitation.findUnique({
    where: { token },
  });

  // If token already exists, generate a new one
  while (existingInvitation) {
    token = generateRandomToken(16);
    existingInvitation = await db.invitation.findUnique({
      where: { token },
    });
  }

  return token;
}

/**
 * Verify an invitation token for the new invitation system
 * @param token Invitation token to verify
 * @returns Invitation data if valid, null otherwise
 */
export async function verifyInvitationToken(token: string) {
  try {
    // Find the invitation in the database
    const invitation = await db.invitation.findUnique({
      where: {
        token,
        expires: {
          gt: new Date(),
        },
        status: "PENDING",
      },
    });

    if (!invitation) {
      return null;
    }

    return invitation;
  } catch (error) {
    logger.error("Error verifying invitation token:", error);
    return null;
  }
}

/**
 * Generate an API key
 * @returns API key
 */
export async function generateApiKey(): Promise<string> {
  // Generate a random token with prefix
  let apiKey = `ce_${generateRandomToken(24)}`;

  // Check if API key already exists
  let existingApiKey = await db.apiKey.findUnique({
    where: { key: apiKey },
  });

  // If API key already exists, generate a new one
  while (existingApiKey) {
    apiKey = `ce_${generateRandomToken(24)}`;
    existingApiKey = await db.apiKey.findUnique({
      where: { key: apiKey },
    });
  }

  return apiKey;
}

/**
 * Generate a webhook secret
 * @returns Webhook secret
 */
export function generateWebhookSecret(): string {
  return `whsec_${generateRandomToken(32)}`;
}

/**
 * Generate an organization invitation token (legacy)
 * @param userId User ID to invite
 * @param organizationId Organization ID to invite to
 * @param role Role to assign to the user
 * @returns Invitation token
 */
export async function generateLegacyInvitationToken(
  userId: string,
  organizationId: string,
  role: string
): Promise<string> {
  const token = randomBytes(32).toString("hex");
  const hashedToken = createHash("sha256").update(token).digest("hex");

  // Store the token in the database
  const expires = new Date(Date.now() + INVITATION_TOKEN_EXPIRES_IN * 1000);

  // Store the invitation data in a custom format
  // In a production app, you would create a dedicated InvitationToken model
  const metadata = JSON.stringify({
    userId,
    organizationId,
    role,
  });

  await db.verificationToken.create({
    data: {
      token: hashedToken,
      expires,
      userId,
      email: metadata, // Repurposing the email field to store invitation metadata
    },
  });

  return token;
}

/**
 * Verify an organization invitation token (legacy)
 * @param token Invitation token
 * @returns Object with userId, organizationId, and role if valid, null otherwise
 */
export async function verifyLegacyInvitationToken(token: string): Promise<{ userId: string; organizationId: string; role: string } | null> {
  const hashedToken = createHash("sha256").update(token).digest("hex");

  // Find the token in the database
  const invitationToken = await db.verificationToken.findUnique({
    where: {
      token: hashedToken,
      expires: {
        gt: new Date(),
      },
    },
  });

  if (!invitationToken) {
    return null;
  }

  try {
    // Parse the metadata from the email field
    const metadata = JSON.parse(invitationToken.email);

    // Delete the token after use
    await db.verificationToken.delete({
      where: {
        id: invitationToken.id,
      },
    });

    return {
      userId: metadata.userId,
      organizationId: metadata.organizationId,
      role: metadata.role,
    };
  } catch (error) {
    logger.error("Error parsing invitation token metadata:", error);
    return null;
  }
}
