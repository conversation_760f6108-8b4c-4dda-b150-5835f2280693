/**
 * Tenant Isolation System
 *
 * This module provides a unified system for enforcing tenant isolation across the application.
 * It consolidates the logic from the previous multi-tenant.ts and tenant-middleware.ts files
 * into a single, reusable implementation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { UserRole } from '@prisma/client';

/**
 * Tenant context for database operations
 */
export interface TenantContext {
  userId: string;
  organizationId?: string;
  isAdmin?: boolean;
  role?: string;
}

/**
 * Resource access options
 */
export interface ResourceAccessOptions {
  resourceType?: string;
  resourceId?: string;
  organizationId?: string;
  organizationIdParam?: string;
  resourceIdParam?: string;
}

/**
 * Get tenant context from user ID
 */
export async function getTenantContext(userId: string): Promise<TenantContext> {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        organizationId: true,
        role: true
      }
    });

    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    return {
      userId: user.id,
      organizationId: user.organizationId || undefined,
      isAdmin: user.role === UserRole.ADMIN,
      role: user.role
    };
  } catch (error) {
    logger.error('Error getting tenant context:', error);
    return { userId };
  }
}

/**
 * Apply tenant isolation to a database query
 * This ensures that users can only access data from their own organization
 * unless they are platform admins.
 */
export function withTenantQuery<T extends Record<string, any>>(
  query: T,
  context: TenantContext
): T {
  // If user is a platform admin, they can access all data
  if (context.isAdmin) {
    return query;
  }

  // If no organization ID, return empty query (no access)
  if (!context.organizationId) {
    logger.warn(`User ${context.userId} has no organization ID, denying access`);
    return {
      ...query,
      where: {
        ...query.where,
        id: 'no-access' // This ensures no results are returned
      }
    };
  }

  // Add organization filter to query
  return {
    ...query,
    where: {
      ...query.where,
      organizationId: context.organizationId
    }
  };
}

/**
 * Apply tenant isolation to an order query
 * This is a special case because orders don't have an organizationId field directly
 * but are related to users who have organizationId
 */
export function withOrderTenantIsolation<T extends Record<string, any>>(
  query: T,
  context: TenantContext
): T {
  // If user is a platform admin, they can access all data
  if (context.isAdmin) {
    return query;
  }

  // If no organization ID, return empty query (no access)
  if (!context.organizationId) {
    logger.warn(`User ${context.userId} has no organization ID, denying access`);
    return {
      ...query,
      where: {
        ...query.where,
        id: 'no-access' // This ensures no results are returned
      }
    };
  }

  // Add organization filter to query through buyer or seller
  return {
    ...query,
    where: {
      ...query.where,
      OR: [
        {
          buyer: {
            organizationId: context.organizationId
          }
        },
        {
          seller: {
            organizationId: context.organizationId
          }
        }
      ]
    }
  };
}

/**
 * Check if a user has access to a specific organization
 */
export async function canAccessOrganization(
  userId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        organizationId: true,
        role: true
      }
    });

    if (!user) {
      return false;
    }

    // Platform admins can access all organizations
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // Users can only access their own organization
    return user.organizationId === organizationId;
  } catch (error) {
    logger.error('Error checking organization access:', error);
    return false;
  }
}

/**
 * Check if a user has access to a specific resource
 */
export async function canAccessResource(
  userId: string,
  resourceType: string,
  resourceId: string
): Promise<boolean> {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        organizationId: true,
        role: true
      }
    });

    if (!user) {
      return false;
    }

    // Platform admins can access all resources
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // If user has no organization, they can't access any resources
    if (!user.organizationId) {
      return false;
    }

    // Check resource ownership based on type
    switch (resourceType) {
      case 'organization':
        return resourceId === user.organizationId;

      case 'team':
        const team = await db.team.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return team?.organizationId === user.organizationId;

      case 'carbon_credit':
        const carbonCredit = await db.carbonCredit.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return carbonCredit?.organizationId === user.organizationId;

      case 'wallet':
        const wallet = await db.wallet.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return wallet?.organizationId === user.organizationId;

      case 'user':
        const targetUser = await db.user.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return targetUser?.organizationId === user.organizationId;

      case 'project':
        const project = await db.project.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return project?.organizationId === user.organizationId;

      case 'department':
        const department = await db.department.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return department?.organizationId === user.organizationId;

      case 'division':
        const division = await db.division.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return division?.organizationId === user.organizationId;

      case 'marketplace_listing':
        const listing = await db.marketplaceListing.findUnique({
          where: { id: resourceId },
          select: { organizationId: true }
        });
        return listing?.organizationId === user.organizationId;

      case 'marketplace_watchlist':
        const watchlist = await db.marketplaceWatchlist.findUnique({
          where: { id: resourceId },
          select: { userId: true }
        });
        return watchlist?.userId === userId;

      default:
        logger.warn(`Unknown resource type: ${resourceType}`);
        return false;
    }
  } catch (error) {
    logger.error('Error checking resource access:', error);
    return false;
  }
}

/**
 * Get the current user from the session
 * This is a helper function used by the middleware
 */
async function getCurrentUser() {
  const session = await auth();

  if (!session?.user) {
    return null;
  }

  // Check if user has email
  if (!session.user.email) {
    logger.warn('User has no email in session');
    return null;
  }

  // Get the user from the database
  const user = await db.user.findUnique({
    where: { email: session.user.email },
    select: { id: true, organizationId: true, role: true },
  });

  if (!user) {
    logger.warn(`User not found for email: ${session.user.email}`);
    return null;
  }

  return user;
}

/**
 * Unified tenant isolation middleware
 *
 * This middleware provides a flexible way to enforce tenant isolation for API routes.
 * It can handle general tenant isolation, organization-specific routes, and resource-specific routes.
 *
 * @param options Configuration options for the middleware
 * @returns A middleware function that enforces tenant isolation
 */
export function createTenantMiddleware(options: ResourceAccessOptions = {}) {
  return function tenantMiddleware(
    handler: (req: NextRequest, context: { params: any }) => Promise<NextResponse>
  ) {
    return async (req: NextRequest, context: { params?: any } = {}) => {
      try {
        // Ensure params is properly awaited if it exists
        const params = context?.params ? await context.params : {};

        // Get the current user
        const user = await getCurrentUser();

        if (!user) {
          return NextResponse.json(
            { error: "Unauthorized" },
            { status: 401 }
          );
        }

        // Determine what type of isolation to enforce

        // Case 1: Resource-specific isolation
        if (options.resourceType || (params.resourceType && params.resourceId)) {
          const resourceType = options.resourceType || params.resourceType;
          const resourceIdParam = options.resourceIdParam || 'id';
          const resourceId = params[resourceIdParam] || params.resourceId;

          if (!resourceId) {
            return NextResponse.json(
              { error: `Resource ID is required (param: ${resourceIdParam})` },
              { status: 400 }
            );
          }

          // Check if the user can access this resource
          const hasAccess = await canAccessResource(user.id, resourceType, resourceId);

          if (!hasAccess) {
            logger.warn(`Tenant isolation: User ${user.id} attempted to access resource ${resourceType}:${resourceId}`);
            return NextResponse.json(
              { error: 'You do not have access to this resource' },
              { status: 403 }
            );
          }
        }

        // Case 2: Organization-specific isolation
        else if (options.organizationId || options.organizationIdParam ||
                params.organizationId || params.orgId || params.id) {
          const organizationIdParam = options.organizationIdParam || 'id';
          const organizationId = options.organizationId ||
                                params.organizationId ||
                                params.orgId ||
                                params[organizationIdParam];

          if (!organizationId) {
            return NextResponse.json(
              { error: 'Organization ID is required' },
              { status: 400 }
            );
          }

          // Check if the user can access this organization
          const hasAccess = await canAccessOrganization(user.id, organizationId);

          if (!hasAccess) {
            logger.warn(`Tenant isolation: User ${user.id} attempted to access organization ${organizationId}`);
            return NextResponse.json(
              { error: 'You do not have access to this organization' },
              { status: 403 }
            );
          }
        }

        // Add user and organization to the request headers
        req.headers.set('X-User-ID', user.id);
        if (user.organizationId) {
          req.headers.set('X-Organization-ID', user.organizationId);
        }
        req.headers.set('X-User-Role', user.role || 'USER');

        // Proceed to the handler with awaited params
        return handler(req, { params });
      } catch (error) {
        logger.error('Error in tenant middleware:', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

/**
 * Middleware to enforce general tenant isolation
 * This is a backward-compatible wrapper around createTenantMiddleware
 */
export const withTenantIsolation = createTenantMiddleware();

/**
 * Middleware to enforce tenant isolation for a specific organization
 * This is a backward-compatible wrapper around createTenantMiddleware
 */
export function withOrganizationIsolation(organizationIdParam: string = 'id') {
  return createTenantMiddleware({ organizationIdParam });
}

/**
 * Middleware to enforce tenant isolation for a specific resource
 * This is a backward-compatible wrapper around createTenantMiddleware
 */
export function withResourceIsolation(
  resourceType: string,
  resourceIdParam: string = 'id'
) {
  return createTenantMiddleware({ resourceType, resourceIdParam });
}
