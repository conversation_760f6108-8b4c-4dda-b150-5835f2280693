/**
 * Blockchain Transaction Service
 * 
 * This module provides a unified service for handling blockchain transactions
 * across different networks with standardized error handling and retry logic.
 */

import { ethers } from "ethers";
import { Alchemy } from "alchemy-sdk";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { createAlchemyInstance } from "./alchemy-config";
import { ApiError, ErrorType } from "@/lib/error-handler";

// Transaction types
export enum TransactionType {
  TRANSFER = "TRANSFER",
  CONTRACT_INTERACTION = "CONTRACT_INTERACTION",
  TOKEN_TRANSFER = "TOKEN_TRANSFER",
  NFT_TRANSFER = "NFT_TRANSFER",
  TOKENIZE = "TOKENIZE",
  RETIRE = "RETIRE",
}

// Transaction status
export enum TransactionStatus {
  PENDING = "PENDING",
  MINING = "MINING",
  CONFIRMED = "CONFIRMED",
  FAILED = "FAILED",
  DROPPED = "DROPPED",
}

// Transaction options
export interface TransactionOptions {
  maxRetries?: number;
  retryDelayMs?: number;
  maxRetryDelayMs?: number;
  retryBackoffFactor?: number;
  waitForConfirmation?: boolean;
  minConfirmations?: number;
  gasLimitMultiplier?: number;
  gasPriceMultiplier?: number;
  useGasManager?: boolean;
}

// Default transaction options
const DEFAULT_TRANSACTION_OPTIONS: TransactionOptions = {
  maxRetries: 5,
  retryDelayMs: 2000, // 2 seconds
  maxRetryDelayMs: 60000, // 1 minute
  retryBackoffFactor: 1.5,
  waitForConfirmation: true,
  minConfirmations: 1,
  gasLimitMultiplier: 1.2, // Add 20% to estimated gas limit
  gasPriceMultiplier: 1.1, // Add 10% to gas price
  useGasManager: false,
};

// Transaction request
export interface TransactionRequest {
  to: string;
  data?: string;
  value?: string;
  gasLimit?: string;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  nonce?: number;
}

// Transaction result
export interface TransactionResult {
  hash: string;
  from: string;
  to: string;
  value: string;
  network: SupportedNetwork;
  chainId: number;
  blockNumber?: number;
  gasUsed?: string;
  effectiveGasPrice?: string;
  status?: "success" | "failed";
  confirmations?: number;
  error?: string;
  receipt?: any;
  userOpHash?: string; // For account abstraction
  isSmartAccount?: boolean;
}

/**
 * Blockchain Transaction Service
 * 
 * Provides methods for sending and managing blockchain transactions
 * with standardized error handling and retry logic.
 */
export class TransactionService {
  private network: SupportedNetwork;
  private useTestnet: boolean;
  private alchemy: Alchemy;
  private provider: ethers.JsonRpcProvider;

  /**
   * Create a new transaction service
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  constructor(network: SupportedNetwork = SupportedNetwork.ETHEREUM, useTestnet: boolean = true) {
    this.network = network;
    this.useTestnet = useTestnet;

    // Initialize Alchemy SDK
    this.alchemy = createAlchemyInstance(network, useTestnet);

    // Initialize provider
    const networkConfig = getNetworkConfig(network, useTestnet);
    this.provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);

    logger.info(`Initialized transaction service for ${networkConfig.name}`);
  }

  /**
   * Send a transaction with retry logic
   * @param wallet Ethers wallet
   * @param txRequest Transaction request
   * @param options Transaction options
   * @returns Transaction result
   */
  async sendTransaction(
    wallet: ethers.Wallet,
    txRequest: TransactionRequest,
    options: TransactionOptions = {}
  ): Promise<TransactionResult> {
    // Merge options with defaults
    const opts = { ...DEFAULT_TRANSACTION_OPTIONS, ...options };
    
    // Connect wallet to provider
    const connectedWallet = wallet.connect(this.provider);
    
    // Get network config
    const networkConfig = getNetworkConfig(this.network, this.useTestnet);
    
    // Prepare transaction request
    const preparedTxRequest = await this.prepareTransactionRequest(txRequest, opts);
    
    // Initialize retry counter
    let retries = 0;
    let lastError: Error | null = null;
    
    // Retry loop
    while (retries <= opts.maxRetries!) {
      try {
        logger.info(`Sending transaction attempt ${retries + 1}/${opts.maxRetries! + 1} on ${this.network}`);
        
        // Send the transaction
        const tx = await connectedWallet.sendTransaction(preparedTxRequest);
        
        logger.info(`Transaction sent: ${tx.hash} on ${this.network}`);
        
        // Wait for confirmation if requested
        if (opts.waitForConfirmation) {
          const receipt = await this.waitForTransaction(tx.hash, opts);
          
          logger.info(`Transaction confirmed in block ${receipt?.blockNumber}`);
          
          return {
            hash: tx.hash,
            from: wallet.address,
            to: txRequest.to,
            value: txRequest.value || "0",
            network: this.network,
            chainId: networkConfig.chainId,
            blockNumber: receipt?.blockNumber,
            gasUsed: receipt?.gasUsed?.toString(),
            effectiveGasPrice: receipt?.gasPrice?.toString(),
            status: receipt?.status === 1 ? "success" : "failed",
            confirmations: receipt?.confirmations,
          };
        } else {
          // Return immediately without waiting for confirmation
          return {
            hash: tx.hash,
            from: wallet.address,
            to: txRequest.to,
            value: txRequest.value || "0",
            network: this.network,
            chainId: networkConfig.chainId,
          };
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Log the error
        logger.warn(`Transaction attempt ${retries + 1} failed:`, lastError);
        
        // Check if we should retry
        if (retries < opts.maxRetries! && this.shouldRetryTransaction(error)) {
          // Calculate delay with exponential backoff
          const delay = Math.min(
            opts.retryDelayMs! * Math.pow(opts.retryBackoffFactor!, retries),
            opts.maxRetryDelayMs!
          );
          
          logger.info(`Retrying transaction in ${delay}ms...`);
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Increment retry counter
          retries++;
          
          // Update gas price for next attempt
          if (preparedTxRequest.gasPrice) {
            const currentGasPrice = ethers.parseUnits(preparedTxRequest.gasPrice, "wei");
            preparedTxRequest.gasPrice = ethers.formatUnits(
              currentGasPrice * BigInt(Math.floor(opts.gasPriceMultiplier! * 100)) / BigInt(100),
              "wei"
            );
          }
          
          // Continue to next retry
          continue;
        }
        
        // If we've exhausted retries or shouldn't retry, throw the error
        throw new ApiError(
          `Transaction failed after ${retries} retries: ${lastError.message}`,
          ErrorType.BLOCKCHAIN,
          500
        );
      }
    }
    
    // This should never be reached due to the throw in the catch block
    throw new ApiError(
      `Transaction failed after ${retries} retries`,
      ErrorType.BLOCKCHAIN,
      500
    );
  }

  /**
   * Wait for a transaction to be confirmed
   * @param txHash Transaction hash
   * @param options Transaction options
   * @returns Transaction receipt
   */
  async waitForTransaction(
    txHash: string,
    options: TransactionOptions = {}
  ): Promise<ethers.TransactionReceipt | null> {
    // Merge options with defaults
    const opts = { ...DEFAULT_TRANSACTION_OPTIONS, ...options };
    
    // Initialize retry counter
    let retries = 0;
    
    // Retry loop
    while (retries <= opts.maxRetries!) {
      try {
        logger.info(`Waiting for transaction ${txHash} confirmation...`);
        
        // Wait for transaction receipt
        const receipt = await this.provider.waitForTransaction(
          txHash,
          opts.minConfirmations
        );
        
        return receipt;
      } catch (error) {
        logger.warn(`Error waiting for transaction ${txHash}:`, error);
        
        // Check if we should retry
        if (retries < opts.maxRetries! && this.shouldRetryTransaction(error)) {
          // Calculate delay with exponential backoff
          const delay = Math.min(
            opts.retryDelayMs! * Math.pow(opts.retryBackoffFactor!, retries),
            opts.maxRetryDelayMs!
          );
          
          logger.info(`Retrying wait in ${delay}ms...`);
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Increment retry counter
          retries++;
          
          // Continue to next retry
          continue;
        }
        
        // If we've exhausted retries or shouldn't retry, return null
        return null;
      }
    }
    
    // If we've exhausted retries, return null
    return null;
  }

  /**
   * Get transaction status
   * @param txHash Transaction hash
   * @returns Transaction status
   */
  async getTransactionStatus(txHash: string): Promise<{
    status: TransactionStatus;
    confirmations?: number;
    blockNumber?: number;
    gasUsed?: string;
    effectiveGasPrice?: string;
    error?: string;
  }> {
    try {
      // Get transaction
      const tx = await this.provider.getTransaction(txHash);
      
      // If transaction is not found, it's pending or doesn't exist
      if (!tx) {
        // Check if transaction is in the mempool
        const isPending = await this.isTransactionPending(txHash);
        
        if (isPending) {
          return { status: TransactionStatus.PENDING };
        } else {
          // If not in mempool and not found, it might have been dropped
          return {
            status: TransactionStatus.DROPPED,
            error: "Transaction not found in blockchain or mempool",
          };
        }
      }
      
      // If transaction has a block number, it's being mined or confirmed
      if (tx.blockNumber) {
        // Get receipt to check status
        const receipt = await this.provider.getTransactionReceipt(txHash);
        
        if (!receipt) {
          // Transaction is being mined
          return {
            status: TransactionStatus.MINING,
            blockNumber: Number(tx.blockNumber),
          };
        }
        
        // Get current block number to calculate confirmations
        const currentBlock = await this.provider.getBlockNumber();
        const confirmations = Number(currentBlock) - Number(receipt.blockNumber);
        
        // Check if transaction was successful
        if (receipt.status === 1) {
          return {
            status: TransactionStatus.CONFIRMED,
            confirmations,
            blockNumber: Number(receipt.blockNumber),
            gasUsed: receipt.gasUsed.toString(),
            effectiveGasPrice: receipt.gasPrice?.toString(),
          };
        } else {
          return {
            status: TransactionStatus.FAILED,
            confirmations,
            blockNumber: Number(receipt.blockNumber),
            gasUsed: receipt.gasUsed.toString(),
            effectiveGasPrice: receipt.gasPrice?.toString(),
            error: "Transaction execution failed",
          };
        }
      } else {
        // Transaction is pending
        return { status: TransactionStatus.PENDING };
      }
    } catch (error) {
      logger.error(`Error getting transaction status for ${txHash}:`, error);
      
      return {
        status: TransactionStatus.FAILED,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Check if a transaction is pending
   * @param txHash Transaction hash
   * @returns Whether the transaction is pending
   */
  async isTransactionPending(txHash: string): Promise<boolean> {
    try {
      // Use Alchemy's pendingTransactions API
      const pendingTxs = await this.alchemy.core.getTransactionReceipts({
        hashList: [txHash],
      });
      
      return pendingTxs.some(tx => tx && !tx.blockNumber);
    } catch (error) {
      logger.error(`Error checking if transaction ${txHash} is pending:`, error);
      return false;
    }
  }

  /**
   * Prepare a transaction request by adding gas estimates
   * @param txRequest Transaction request
   * @param options Transaction options
   * @returns Prepared transaction request
   */
  async prepareTransactionRequest(
    txRequest: TransactionRequest,
    options: TransactionOptions = {}
  ): Promise<ethers.TransactionRequest> {
    // Merge options with defaults
    const opts = { ...DEFAULT_TRANSACTION_OPTIONS, ...options };
    
    // Create a copy of the request
    const preparedRequest: ethers.TransactionRequest = {
      to: txRequest.to,
      data: txRequest.data,
      value: txRequest.value ? ethers.parseEther(txRequest.value) : undefined,
    };
    
    // Add gas limit if provided
    if (txRequest.gasLimit) {
      preparedRequest.gasLimit = ethers.parseUnits(txRequest.gasLimit, "wei");
    } else {
      // Estimate gas limit
      try {
        const estimatedGas = await this.provider.estimateGas({
          to: txRequest.to,
          data: txRequest.data,
          value: txRequest.value ? ethers.parseEther(txRequest.value) : undefined,
        });
        
        // Add buffer to estimated gas
        preparedRequest.gasLimit = estimatedGas * BigInt(Math.floor(opts.gasLimitMultiplier! * 100)) / BigInt(100);
      } catch (error) {
        logger.warn("Error estimating gas limit:", error);
        // Use a default gas limit if estimation fails
        preparedRequest.gasLimit = ethers.parseUnits("500000", "wei");
      }
    }
    
    // Add gas price if provided
    if (txRequest.gasPrice) {
      preparedRequest.gasPrice = ethers.parseUnits(txRequest.gasPrice, "wei");
    } else if (txRequest.maxFeePerGas && txRequest.maxPriorityFeePerGas) {
      // Use EIP-1559 gas parameters if provided
      preparedRequest.maxFeePerGas = ethers.parseUnits(txRequest.maxFeePerGas, "wei");
      preparedRequest.maxPriorityFeePerGas = ethers.parseUnits(txRequest.maxPriorityFeePerGas, "wei");
    } else {
      // Get current gas price
      try {
        const feeData = await this.provider.getFeeData();
        
        if (feeData.maxFeePerGas && feeData.maxPriorityFeePerGas) {
          // Use EIP-1559 gas parameters
          preparedRequest.maxFeePerGas = feeData.maxFeePerGas * BigInt(Math.floor(opts.gasPriceMultiplier! * 100)) / BigInt(100);
          preparedRequest.maxPriorityFeePerGas = feeData.maxPriorityFeePerGas * BigInt(Math.floor(opts.gasPriceMultiplier! * 100)) / BigInt(100);
        } else if (feeData.gasPrice) {
          // Use legacy gas price
          preparedRequest.gasPrice = feeData.gasPrice * BigInt(Math.floor(opts.gasPriceMultiplier! * 100)) / BigInt(100);
        }
      } catch (error) {
        logger.warn("Error getting fee data:", error);
        // Don't set gas price if we can't get it
      }
    }
    
    // Add nonce if provided
    if (txRequest.nonce !== undefined) {
      preparedRequest.nonce = txRequest.nonce;
    }
    
    return preparedRequest;
  }

  /**
   * Determine if a transaction error should be retried
   * @param error Error to check
   * @returns Whether the error should be retried
   */
  private shouldRetryTransaction(error: any): boolean {
    // Check for common retryable errors
    const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase();
    
    // Network errors
    if (
      errorMessage.includes("network error") ||
      errorMessage.includes("timeout") ||
      errorMessage.includes("connection refused") ||
      errorMessage.includes("connection reset") ||
      errorMessage.includes("connection closed") ||
      errorMessage.includes("server responded with error")
    ) {
      return true;
    }
    
    // Nonce errors
    if (
      errorMessage.includes("nonce too low") ||
      errorMessage.includes("nonce too high") ||
      errorMessage.includes("replacement transaction underpriced")
    ) {
      return true;
    }
    
    // Gas price errors
    if (
      errorMessage.includes("gas price too low") ||
      errorMessage.includes("insufficient funds for gas") ||
      errorMessage.includes("fee cap higher than") ||
      errorMessage.includes("max fee per gas less than")
    ) {
      return true;
    }
    
    // Transaction underpriced
    if (errorMessage.includes("transaction underpriced")) {
      return true;
    }
    
    // Rate limiting
    if (
      errorMessage.includes("rate limit") ||
      errorMessage.includes("too many requests")
    ) {
      return true;
    }
    
    // RPC errors that might be temporary
    if (
      errorMessage.includes("internal error") ||
      errorMessage.includes("temporarily unavailable") ||
      errorMessage.includes("service unavailable") ||
      errorMessage.includes("try again later")
    ) {
      return true;
    }
    
    // Default to not retrying for other errors
    return false;
  }

  /**
   * Get the current network
   * @returns Current network
   */
  getNetwork(): SupportedNetwork {
    return this.network;
  }

  /**
   * Get whether using testnet
   * @returns Whether using testnet
   */
  isTestnet(): boolean {
    return this.useTestnet;
  }

  /**
   * Get the Alchemy instance
   * @returns Alchemy instance
   */
  getAlchemy(): Alchemy {
    return this.alchemy;
  }

  /**
   * Get the provider
   * @returns Provider
   */
  getProvider(): ethers.JsonRpcProvider {
    return this.provider;
  }

  /**
   * Get network configuration
   * @returns Network configuration
   */
  getNetworkConfig() {
    return getNetworkConfig(this.network, this.useTestnet);
  }
}

// Create a singleton instance for the default network
const defaultTransactionService = new TransactionService();

export default defaultTransactionService;
