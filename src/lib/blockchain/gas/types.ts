import { SupportedNetwork } from '@/lib/blockchain-config';

/**
 * Gas optimization strategy
 */
export enum GasStrategy {
  SLOW = "slow",
  AVERAGE = "average",
  FAST = "fast",
  CUSTOM = "custom",
}

/**
 * Gas optimization options
 */
export interface GasOptions {
  strategy?: GasStrategy;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  gasPrice?: string;
  gasLimit?: number;
  nonce?: number;
}

/**
 * Gas estimation result
 */
export interface GasEstimationResult {
  gasEstimate: string;
  gasPrice: string;
  totalGasCost: string;
  totalGasCostUsd: string;
  nativeCurrency: string;
  network: SupportedNetwork;
  operations: {
    name: string;
    gasEstimate: string;
  }[];
}

/**
 * Gas price history entry
 */
export interface GasPriceHistoryEntry {
  blockNumber: number;
  timestamp: string;
  baseFeePerGas: string;
}

/**
 * Gas strategy recommendation
 */
export interface GasStrategyRecommendation {
  strategy: GasStrategy;
  reason: string;
}
