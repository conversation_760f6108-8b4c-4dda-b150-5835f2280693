import { GasEstimation } from './estimation';
import { GasOptimization } from './optimization';
import { 
  GasStrategy, 
  GasOptions, 
  GasEstimationResult,
  GasPriceHistoryEntry,
  GasStrategyRecommendation
} from './types';
import { BlockchainClient } from '@/lib/blockchain/core/client';
import { SupportedNetwork } from '@/lib/blockchain/config/networks';

/**
 * Gas service for estimating and optimizing gas for blockchain transactions
 */
export class GasService {
  private client: BlockchainClient;
  private network: SupportedNetwork;
  private useTestnet: boolean;

  /**
   * Create a new gas service
   * @param client Blockchain client
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  constructor(client: BlockchainClient, network: SupportedNetwork, useTestnet: boolean = true) {
    this.client = client;
    this.network = network;
    this.useTestnet = useTestnet;
  }

  /**
   * Estimate gas for tokenizing a carbon credit
   * @param tokenId Token ID
   * @param amount Amount to tokenize
   * @param ownerAddress Owner wallet address
   * @param metadata Metadata for the carbon credit
   * @returns Gas estimation details
   */
  async estimateTokenizationGas(
    tokenId: number,
    amount: number,
    ownerAddress: string,
    metadata: {
      projectId: string;
      vintage: number;
      standard: string;
      methodology: string;
    }
  ): Promise<GasEstimationResult> {
    return GasEstimation.estimateTokenizationGas(
      tokenId,
      amount,
      ownerAddress,
      metadata,
      this.network,
      this.useTestnet
    );
  }

  /**
   * Estimate gas for transferring a carbon credit
   * @param fromAddress Sender address
   * @param toAddress Recipient address
   * @param tokenId Token ID
   * @param amount Amount to transfer
   * @returns Gas estimation details
   */
  async estimateTransferGas(
    fromAddress: string,
    toAddress: string,
    tokenId: number,
    amount: number
  ): Promise<GasEstimationResult> {
    return GasEstimation.estimateTransferGas(
      fromAddress,
      toAddress,
      tokenId,
      amount,
      this.network,
      this.useTestnet
    );
  }

  /**
   * Estimate gas for retiring a carbon credit
   * @param ownerAddress Owner address
   * @param tokenId Token ID
   * @param amount Amount to retire
   * @returns Gas estimation details
   */
  async estimateRetirementGas(
    ownerAddress: string,
    tokenId: number,
    amount: number
  ): Promise<GasEstimationResult> {
    return GasEstimation.estimateRetirementGas(
      ownerAddress,
      tokenId,
      amount,
      this.network,
      this.useTestnet
    );
  }

  /**
   * Get optimized gas parameters
   * @param options Gas options
   * @returns Optimized gas parameters
   */
  async getOptimizedGasParams(options: GasOptions = {}) {
    return GasOptimization.getOptimizedGasParams(this.client, options);
  }

  /**
   * Calculate gas cost in native currency
   * @param gasLimit Gas limit
   * @param gasPrice Gas price in Gwei
   * @returns Gas cost in native currency
   */
  calculateGasCost(gasLimit: number, gasPrice: string): string {
    return GasOptimization.calculateGasCost(gasLimit, gasPrice);
  }

  /**
   * Calculate EIP-1559 gas cost in native currency
   * @param gasLimit Gas limit
   * @param maxFeePerGas Max fee per gas in Gwei
   * @returns Maximum gas cost in native currency
   */
  calculateEIP1559GasCost(gasLimit: number, maxFeePerGas: string): string {
    return GasOptimization.calculateEIP1559GasCost(gasLimit, maxFeePerGas);
  }

  /**
   * Get gas price history
   * @param blocks Number of blocks to look back
   * @returns Gas price history
   */
  async getGasPriceHistory(blocks: number = 10): Promise<GasPriceHistoryEntry[]> {
    return GasOptimization.getGasPriceHistory(this.client, blocks);
  }

  /**
   * Recommend gas strategy based on transaction urgency and current network conditions
   * @param urgency Transaction urgency (0-100)
   * @returns Recommended gas strategy
   */
  async recommendGasStrategy(urgency: number): Promise<GasStrategyRecommendation> {
    return GasOptimization.recommendGasStrategy(this.client, urgency);
  }
}

// Export types
export {
  GasStrategy,
  GasOptions,
  GasEstimationResult,
  GasPriceHistoryEntry,
  GasStrategyRecommendation
};
