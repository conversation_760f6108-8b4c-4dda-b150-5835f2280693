import { logger } from "@/lib/logger";
import { BlockchainClient } from "./client";
import { SupportedNetwork } from "../config/networks";

export type TransactionStatus = "pending" | "mining" | "confirmed" | "failed" | "dropped";

export interface TransactionStatusResponse {
  status: TransactionStatus;
  confirmations?: number;
  blockNumber?: number;
  gasUsed?: string;
  effectiveGasPrice?: string;
  error?: string;
  estimatedTime?: number; // in seconds
}

/**
 * Transaction status service
 */
export class TransactionStatusService {
  private client: BlockchainClient;
  private network: SupportedNetwork;
  private isTestnet: boolean;

  /**
   * Create a new transaction status service
   * @param client Blockchain client
   * @param network Network
   * @param isTestnet Whether using testnet
   */
  constructor(client: BlockchainClient, network: SupportedNetwork, isTestnet: boolean) {
    this.client = client;
    this.network = network;
    this.isTestnet = isTestnet;
  }

  /**
   * Get transaction status
   * @param txHash Transaction hash
   * @returns Transaction status
   */
  async getTransactionStatus(txHash: string): Promise<TransactionStatusResponse> {
    try {
      logger.info(`Getting status for transaction ${txHash} on ${this.network} ${this.isTestnet ? 'testnet' : 'mainnet'}`);
      
      // Get transaction
      const tx = await this.client.getTransaction(txHash);
      
      // If transaction is not found, it's pending or doesn't exist
      if (!tx) {
        // Check if transaction is in the mempool
        const isPending = await this.client.isTransactionPending(txHash);
        
        if (isPending) {
          return {
            status: "pending",
            estimatedTime: this.estimateTimeForPendingTransaction(),
          };
        } else {
          // If not in mempool and not found, it might have been dropped
          return {
            status: "dropped",
            error: "Transaction not found in blockchain or mempool",
          };
        }
      }
      
      // If transaction has a block number, it's being mined or confirmed
      if (tx.blockNumber) {
        // Get receipt to check status
        const receipt = await this.client.getTransactionReceipt(txHash);
        
        if (!receipt) {
          // Transaction is being mined
          return {
            status: "mining",
            blockNumber: tx.blockNumber,
            estimatedTime: this.estimateTimeForMiningTransaction(),
          };
        }
        
        // Get current block number to calculate confirmations
        const currentBlock = await this.client.getBlockNumber();
        const confirmations = currentBlock - receipt.blockNumber;
        
        // Check if transaction was successful
        if (receipt.status === "success") {
          return {
            status: "confirmed",
            confirmations,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed,
            effectiveGasPrice: receipt.effectiveGasPrice,
          };
        } else {
          return {
            status: "failed",
            confirmations,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed,
            effectiveGasPrice: receipt.effectiveGasPrice,
            error: "Transaction execution failed",
          };
        }
      } else {
        // Transaction is pending
        return {
          status: "pending",
          estimatedTime: this.estimateTimeForPendingTransaction(),
        };
      }
    } catch (error) {
      logger.error(`Error getting transaction status for ${txHash}:`, error);
      
      return {
        status: "failed",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
  
  /**
   * Estimate time for a pending transaction
   * @returns Estimated time in seconds
   */
  private estimateTimeForPendingTransaction(): number {
    // These are rough estimates based on network
    switch (this.network) {
      case SupportedNetwork.ETHEREUM:
        return this.isTestnet ? 30 : 60; // Ethereum has longer block times
      case SupportedNetwork.POLYGON:
        return this.isTestnet ? 10 : 15; // Polygon has faster block times
      case SupportedNetwork.ARBITRUM:
        return this.isTestnet ? 5 : 10; // Arbitrum is very fast
      case SupportedNetwork.OPTIMISM:
        return this.isTestnet ? 5 : 10; // Optimism is very fast
      case SupportedNetwork.BASE:
        return this.isTestnet ? 5 : 10; // Base is very fast
      default:
        return 30; // Default estimate
    }
  }
  
  /**
   * Estimate time for a mining transaction
   * @returns Estimated time in seconds
   */
  private estimateTimeForMiningTransaction(): number {
    // Mining should be faster than pending
    switch (this.network) {
      case SupportedNetwork.ETHEREUM:
        return this.isTestnet ? 15 : 30;
      case SupportedNetwork.POLYGON:
        return this.isTestnet ? 5 : 10;
      case SupportedNetwork.ARBITRUM:
        return this.isTestnet ? 3 : 5;
      case SupportedNetwork.OPTIMISM:
        return this.isTestnet ? 3 : 5;
      case SupportedNetwork.BASE:
        return this.isTestnet ? 3 : 5;
      default:
        return 15;
    }
  }
}
