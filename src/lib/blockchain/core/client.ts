import { Alchemy, Network } from "alchemy-sdk";
import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "../config/networks";
import { ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Blockchain client for interacting with different networks
 */
export class BlockchainClient {
  private alchemy: Alchemy;
  private provider: ethers.JsonRpcProvider;
  private network: SupportedNetwork;
  private useTestnet: boolean;

  /**
   * Create a new blockchain client
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  constructor(network: SupportedNetwork, useTestnet: boolean) {
    this.network = network;
    this.useTestnet = useTestnet;

    const networkConfig = getNetworkConfig(network, useTestnet);

    // Initialize Alchemy SDK
    this.alchemy = new Alchemy({
      apiKey: process.env.ALCHEMY_API_KEY,
      network: networkConfig.alchemyNetwork,
    });

    // Initialize provider
    this.provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);

    logger.info(`Initialized blockchain client for ${networkConfig.name}`);
  }

  /**
   * Get the current network
   * @returns Current network
   */
  getNetwork(): SupportedNetwork {
    return this.network;
  }

  /**
   * Get the network configuration
   * @returns Network configuration
   */
  getNetworkConfig() {
    return getNetworkConfig(this.network, this.useTestnet);
  }

  /**
   * Get the current gas price
   * @returns Gas price in wei
   */
  async getGasPrice(): Promise<string> {
    try {
      const gasPrice = await this.provider.getFeeData();
      return gasPrice.gasPrice?.toString() || "0";
    } catch (error) {
      logger.error("Error getting gas price:", error);
      throw new Error("Failed to get gas price");
    }
  }

  /**
   * Estimate gas for a transaction
   * @param txData Transaction data
   * @returns Estimated gas limit
   */
  async estimateGas(txData: { to: string; data: string; value?: string }): Promise<string> {
    try {
      const gasLimit = await this.provider.estimateGas({
        to: txData.to,
        data: txData.data,
        value: txData.value ? ethers.parseEther(txData.value) : undefined,
      });
      return gasLimit.toString();
    } catch (error) {
      logger.error("Error estimating gas:", error);
      throw new Error("Failed to estimate gas");
    }
  }

  /**
   * Get transaction receipt
   * @param txHash Transaction hash
   * @returns Transaction receipt
   */
  async getTransactionReceipt(txHash: string) {
    try {
      const receipt = await this.provider.getTransactionReceipt(txHash);

      if (!receipt) {
        return null;
      }

      return {
        hash: receipt.hash,
        blockNumber: receipt.blockNumber,
        blockHash: receipt.blockHash,
        status: receipt.status === 1 ? "success" : "failed",
        from: receipt.from,
        to: receipt.to,
        gasUsed: receipt.gasUsed.toString(),
        effectiveGasPrice: receipt.gasPrice?.toString(),
        cumulativeGasUsed: receipt.cumulativeGasUsed.toString(),
        logs: receipt.logs.map(log => ({
          address: log.address,
          topics: log.topics,
          data: log.data,
        })),
      };
    } catch (error) {
      logger.error(`Error getting transaction receipt for ${txHash}:`, error);
      throw new ApiError("Failed to get transaction receipt", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Get transaction details
   * @param txHash Transaction hash
   * @returns Transaction details
   */
  async getTransaction(txHash: string) {
    try {
      const tx = await this.provider.getTransaction(txHash);

      if (!tx) {
        return null;
      }

      return {
        hash: tx.hash,
        blockNumber: tx.blockNumber,
        from: tx.from,
        to: tx.to,
        value: ethers.formatEther(tx.value),
        gasLimit: tx.gasLimit.toString(),
        gasPrice: tx.gasPrice ? ethers.formatUnits(tx.gasPrice, "gwei") : undefined,
        maxFeePerGas: tx.maxFeePerGas ? ethers.formatUnits(tx.maxFeePerGas, "gwei") : undefined,
        maxPriorityFeePerGas: tx.maxPriorityFeePerGas ? ethers.formatUnits(tx.maxPriorityFeePerGas, "gwei") : undefined,
        nonce: tx.nonce,
        data: tx.data,
      };
    } catch (error) {
      logger.error(`Error getting transaction details for ${txHash}:`, error);
      throw new ApiError("Failed to get transaction details", ErrorType.INTERNAL, 500);
    }
  }

  /**
   * Check if a transaction is pending
   * @param txHash Transaction hash
   * @returns Whether the transaction is pending
   */
  async isTransactionPending(txHash: string): Promise<boolean> {
    try {
      // Check if transaction is in the mempool
      const pendingTransactions = await this.alchemy.core.getTransactionReceipts({
        hashesOrReceipts: [txHash],
      });
      
      // If we get a null receipt, the transaction is pending
      return pendingTransactions.receipts.length === 0 || pendingTransactions.receipts[0] === null;
    } catch (error) {
      logger.error(`Error checking if transaction ${txHash} is pending:`, error);
      // Default to false if there's an error
      return false;
    }
  }

  /**
   * Get current block number
   * @returns Current block number
   */
  async getBlockNumber(): Promise<number> {
    try {
      const blockNumber = await this.provider.getBlockNumber();
      return blockNumber;
    } catch (error) {
      logger.error("Error getting block number:", error);
      throw new Error("Failed to get block number");
    }
  }
}
