import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { createLightAccountFromEncryptedKey, getSmartAccountAddress } from "@/lib/blockchain/account-abstraction";

/**
 * Wallet security settings interface
 */
export interface WalletSecuritySettings {
  twoFactorEnabled?: boolean;
  twoFactorType?: string;
  whitelistedAddresses?: string[];
  blacklistedAddresses?: string[];
  delayedWithdrawals?: boolean;
  withdrawalDelayHours?: number;
  notificationsEnabled?: boolean;
  autoLockEnabled?: boolean;
  autoLockTimeoutMinutes?: number;
  spendingNotifications?: boolean;
  unusualActivityDetection?: boolean;
  securityReviewFrequency?: number;
}

/**
 * Calculate security score for a wallet based on enabled security features
 * @param walletId Wallet ID
 * @returns Security score (0-100)
 */
export async function calculateWalletSecurityScore(walletId: string): Promise<number> {
  try {
    // Get wallet with security settings
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
      include: {
        securitySettings: true,
      },
    });

    if (!wallet) {
      throw new Error("Wallet not found");
    }

    let score = 0;
    const securitySettings = wallet.securitySettings;

    // Base score for having a wallet
    score += 10;

    // Recovery settings
    if (wallet.recoveryEnabled) {
      score += 20;
    }

    if (!securitySettings) {
      return score;
    }

    // Two-factor authentication
    if (securitySettings.twoFactorEnabled) {
      score += 25;
    }

    // Whitelisted addresses
    if (securitySettings.whitelistedAddresses && securitySettings.whitelistedAddresses.length > 0) {
      score += 10;
    }

    // Delayed withdrawals
    if (securitySettings.delayedWithdrawals) {
      score += 15;
    }

    // Auto-lock
    if (securitySettings.autoLockEnabled) {
      score += 10;
    }

    // Notifications
    if (securitySettings.notificationsEnabled) {
      score += 5;
    }

    // Spending notifications
    if (securitySettings.spendingNotifications) {
      score += 5;
    }

    // Unusual activity detection
    if (securitySettings.unusualActivityDetection) {
      score += 10;
    }

    // Transaction limits
    if (wallet.transactionLimitDaily || wallet.transactionLimitPerTx) {
      score += 10;
    }

    // Approval requirements
    if (wallet.requireApprovals) {
      score += 15;
    }

    // Cap the score at 100
    return Math.min(score, 100);
  } catch (error) {
    logger.error(`Error calculating wallet security score:`, error);
    throw new Error("Failed to calculate wallet security score");
  }
}

/**
 * Check if a transaction is allowed based on security settings
 * @param walletId Wallet ID
 * @param toAddress Recipient address
 * @param amount Amount to send
 * @returns Whether the transaction is allowed and any restrictions
 */
export async function checkTransactionSecurity(
  walletId: string,
  toAddress: string,
  amount: number
): Promise<{
  allowed: boolean;
  requiresApproval: boolean;
  delayRequired: boolean;
  delayHours: number;
  reason?: string;
}> {
  try {
    // Get wallet with security settings
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
      include: {
        securitySettings: true,
      },
    });

    if (!wallet) {
      throw new Error("Wallet not found");
    }

    const securitySettings = wallet.securitySettings;

    // Check if wallet has enough balance
    if (amount > wallet.balance) {
      return {
        allowed: false,
        requiresApproval: false,
        delayRequired: false,
        delayHours: 0,
        reason: "Insufficient balance",
      };
    }

    // Check if address is blacklisted
    if (
      securitySettings?.blacklistedAddresses &&
      securitySettings.blacklistedAddresses.includes(toAddress)
    ) {
      return {
        allowed: false,
        requiresApproval: false,
        delayRequired: false,
        delayHours: 0,
        reason: "Recipient address is blacklisted",
      };
    }

    // Check if address is not in whitelist when whitelist is enabled
    if (
      securitySettings?.whitelistedAddresses &&
      securitySettings.whitelistedAddresses.length > 0 &&
      !securitySettings.whitelistedAddresses.includes(toAddress)
    ) {
      return {
        allowed: false,
        requiresApproval: true,
        delayRequired: false,
        delayHours: 0,
        reason: "Recipient address is not in whitelist",
      };
    }

    // Check transaction limits
    if (wallet.transactionLimitPerTx && amount > wallet.transactionLimitPerTx) {
      return {
        allowed: false,
        requiresApproval: true,
        delayRequired: false,
        delayHours: 0,
        reason: `Transaction amount exceeds the per-transaction limit of ${wallet.transactionLimitPerTx}`,
      };
    }

    // Check daily transaction limit
    if (wallet.transactionLimitDaily) {
      // Get transactions from the last 24 hours
      const oneDayAgo = new Date();
      oneDayAgo.setHours(oneDayAgo.getHours() - 24);

      const recentTransactions = await db.transaction.findMany({
        where: {
          walletId,
          createdAt: {
            gte: oneDayAgo,
          },
          status: {
            in: ["COMPLETED", "PENDING"],
          },
        },
      });

      const dailyTotal = recentTransactions.reduce((sum, tx) => sum + tx.amount, 0);

      if (dailyTotal + amount > wallet.transactionLimitDaily) {
        return {
          allowed: false,
          requiresApproval: true,
          delayRequired: false,
          delayHours: 0,
          reason: `Transaction would exceed daily limit of ${wallet.transactionLimitDaily}`,
        };
      }
    }

    // Check if approval is required
    const requiresApproval = wallet.requireApprovals || false;

    // Check if delay is required
    const delayRequired = securitySettings?.delayedWithdrawals || false;
    const delayHours = securitySettings?.withdrawalDelayHours || 0;

    return {
      allowed: true,
      requiresApproval,
      delayRequired,
      delayHours,
    };
  } catch (error) {
    logger.error(`Error checking transaction security:`, error);
    throw new Error("Failed to check transaction security");
  }
}

/**
 * Create a wallet recovery setup
 * @param walletId Wallet ID
 * @param recoveryType Recovery type
 * @param recoveryData Recovery data
 * @returns Recovery setup details
 */
export async function setupWalletRecovery(
  walletId: string,
  recoveryType: string,
  recoveryData: any
): Promise<{
  recoveryEnabled: boolean;
  recoveryType: string;
}> {
  try {
    // Update wallet with recovery settings
    const updatedWallet = await db.wallet.update({
      where: { id: walletId },
      data: {
        recoveryEnabled: true,
        recoveryType,
        recoveryData,
      },
    });

    logger.info(`Recovery setup for wallet ${walletId} with type ${recoveryType}`);

    return {
      recoveryEnabled: updatedWallet.recoveryEnabled,
      recoveryType: updatedWallet.recoveryType,
    };
  } catch (error) {
    logger.error(`Error setting up wallet recovery:`, error);
    throw new Error("Failed to set up wallet recovery");
  }
}

/**
 * Recover a smart account
 * @param walletId Wallet ID
 * @param newEncryptedKey New encrypted private key
 * @param network Network to use
 * @param isTestnet Whether to use testnet
 * @returns Recovery details
 */
export async function recoverSmartAccount(
  walletId: string,
  newEncryptedKey: string,
  network: SupportedNetwork,
  isTestnet: boolean = true
): Promise<{
  success: boolean;
  smartAccountAddress: string;
  message: string;
}> {
  try {
    // Get wallet
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
    });

    if (!wallet) {
      throw new Error("Wallet not found");
    }

    if (!wallet.isSmartWallet) {
      throw new Error("Wallet is not a smart wallet");
    }

    if (!wallet.smartAccountAddress) {
      throw new Error("Smart account address not found");
    }

    // Create Light Account client from new encrypted key
    const lightAccountClient = await createLightAccountFromEncryptedKey(
      newEncryptedKey,
      network,
      isTestnet
    );

    // Get the smart account address
    const newSmartAccountAddress = await getSmartAccountAddress(lightAccountClient);

    // In a real implementation, we would verify that the new account can control the old smart account
    // For now, we'll just update the wallet with the new encrypted key

    // Update wallet with new encrypted key
    await db.wallet.update({
      where: { id: walletId },
      data: {
        encryptedKey: newEncryptedKey,
        recoveryData: {
          recoveredAt: new Date(),
          previousSmartAccountAddress: wallet.smartAccountAddress,
          newSmartAccountAddress,
        },
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "WALLET_RECOVERY",
        description: `Recovered smart account for wallet ${walletId}`,
        userId: wallet.userId,
        organizationId: wallet.organizationId,
        metadata: {
          walletId,
          previousSmartAccountAddress: wallet.smartAccountAddress,
          newSmartAccountAddress,
        },
      },
    });

    logger.info(`Recovered smart account for wallet ${walletId}`);

    return {
      success: true,
      smartAccountAddress: newSmartAccountAddress,
      message: "Smart account recovered successfully",
    };
  } catch (error) {
    logger.error(`Error recovering smart account:`, error);
    throw new Error(`Failed to recover smart account: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Detect unusual activity for a wallet
 * @param walletId Wallet ID
 * @param transaction Transaction details
 * @returns Whether the transaction is unusual and why
 */
export async function detectUnusualActivity(
  walletId: string,
  transaction: {
    toAddress: string;
    amount: number;
    tokenAddress?: string;
  }
): Promise<{
  isUnusual: boolean;
  riskLevel: "LOW" | "MEDIUM" | "HIGH";
  reasons: string[];
}> {
  try {
    // Get wallet with security settings
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
      include: {
        securitySettings: true,
        transactions: {
          orderBy: {
            createdAt: "desc",
          },
          take: 20,
        },
      },
    });

    if (!wallet) {
      throw new Error("Wallet not found");
    }

    const reasons: string[] = [];
    let riskLevel: "LOW" | "MEDIUM" | "HIGH" = "LOW";

    // Check if unusual activity detection is enabled
    if (!wallet.securitySettings?.unusualActivityDetection) {
      return {
        isUnusual: false,
        riskLevel: "LOW",
        reasons: [],
      };
    }

    // Check if this is a new recipient
    const hasTransactedWithRecipient = wallet.transactions.some(
      tx => tx.counterpartyAddress === transaction.toAddress
    );

    if (!hasTransactedWithRecipient) {
      reasons.push("New recipient address");
      riskLevel = "MEDIUM";
    }

    // Check if transaction amount is unusually large
    const averageAmount =
      wallet.transactions.reduce((sum, tx) => sum + tx.amount, 0) / wallet.transactions.length;

    if (transaction.amount > averageAmount * 3) {
      reasons.push("Transaction amount is significantly larger than average");
      riskLevel = "HIGH";
    }

    // Check if there have been many transactions recently (potential account takeover)
    const oneDayAgo = new Date();
    oneDayAgo.setHours(oneDayAgo.getHours() - 24);

    const recentTransactionsCount = wallet.transactions.filter(
      tx => new Date(tx.createdAt) >= oneDayAgo
    ).length;

    if (recentTransactionsCount > 10) {
      reasons.push("Unusually high number of transactions in the last 24 hours");
      riskLevel = "HIGH";
    }

    return {
      isUnusual: reasons.length > 0,
      riskLevel,
      reasons,
    };
  } catch (error) {
    logger.error(`Error detecting unusual activity:`, error);
    throw new Error("Failed to detect unusual activity");
  }
}
