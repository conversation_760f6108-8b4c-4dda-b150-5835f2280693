import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork } from "@/lib/blockchain/config/networks";
import { getCarbonCreditContractAddress } from "@/lib/blockchain/config/contracts";
import { BlockchainClient } from "@/lib/blockchain/core/client";

// Carbon Credit Token ABI
const CARBON_CREDIT_ABI = [
  // ERC-1155 standard functions
  "function balanceOf(address account, uint256 id) view returns (uint256)",
  "function balanceOfBatch(address[] calldata accounts, uint256[] calldata ids) view returns (uint256[] memory)",
  "function setApprovalForAll(address operator, bool approved)",
  "function isApprovedForAll(address account, address operator) view returns (bool)",
  "function safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes calldata data)",
  "function safeBatchTransferFrom(address from, address to, uint256[] calldata ids, uint256[] calldata amounts, bytes calldata data)",
  
  // Carbon credit specific functions
  "function mint(address to, uint256 id, uint256 amount, bytes calldata data)",
  "function retire(uint256 id, uint256 amount)",
  "function setURI(uint256 id, string calldata newuri)",
  "function uri(uint256 id) view returns (string memory)",
  
  // Carbon credit metadata functions
  "function getTokenMetadata(uint256 id) view returns (string memory projectId, uint256 vintage, string memory standard, string memory methodology, bool isRetired)",
  "function getTokenSupply(uint256 id) view returns (uint256 totalSupply, uint256 retiredSupply, uint256 availableSupply)",
  
  // Events
  "event TransferSingle(address indexed operator, address indexed from, address indexed to, uint256 id, uint256 value)",
  "event TransferBatch(address indexed operator, address indexed from, address indexed to, uint256[] ids, uint256[] values)",
  "event ApprovalForAll(address indexed account, address indexed operator, bool approved)",
  "event URI(string value, uint256 indexed id)",
  "event TokenMinted(address indexed to, uint256 indexed id, uint256 amount)",
  "event TokenRetired(address indexed account, uint256 indexed id, uint256 amount)",
];

/**
 * Carbon credit token metadata
 */
export interface CarbonCreditMetadata {
  projectId: string;
  vintage: number;
  standard: string;
  methodology: string;
  isRetired: boolean;
}

/**
 * Carbon credit token supply
 */
export interface CarbonCreditSupply {
  totalSupply: number;
  retiredSupply: number;
  availableSupply: number;
}

/**
 * Carbon credit contract interface
 */
export class CarbonCreditContract {
  private client: BlockchainClient;
  private contract: ethers.Contract;
  private network: SupportedNetwork;
  private useTestnet: boolean;

  /**
   * Create a new carbon credit contract instance
   * @param client Blockchain client
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  constructor(client: BlockchainClient, network: SupportedNetwork, useTestnet: boolean = true) {
    this.client = client;
    this.network = network;
    this.useTestnet = useTestnet;
    
    const contractAddress = getCarbonCreditContractAddress(network, useTestnet);
    const provider = client.getProvider();
    
    this.contract = new ethers.Contract(contractAddress, CARBON_CREDIT_ABI, provider);
  }

  /**
   * Get the contract address
   * @returns Contract address
   */
  getAddress(): string {
    return this.contract.target as string;
  }

  /**
   * Get the contract instance
   * @returns Contract instance
   */
  getContract(): ethers.Contract {
    return this.contract;
  }

  /**
   * Get the balance of a token for an account
   * @param account Account address
   * @param tokenId Token ID
   * @returns Token balance
   */
  async getBalance(account: string, tokenId: number): Promise<number> {
    try {
      const balance = await this.contract.balanceOf(account, tokenId);
      return Number(balance);
    } catch (error) {
      logger.error(`Error getting balance for account ${account} and token ${tokenId}:`, error);
      throw new Error(`Failed to get balance: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get the balances of multiple tokens for multiple accounts
   * @param accounts Account addresses
   * @param tokenIds Token IDs
   * @returns Token balances
   */
  async getBalanceBatch(accounts: string[], tokenIds: number[]): Promise<number[]> {
    try {
      const balances = await this.contract.balanceOfBatch(accounts, tokenIds);
      return balances.map((balance: bigint) => Number(balance));
    } catch (error) {
      logger.error(`Error getting batch balances:`, error);
      throw new Error(`Failed to get batch balances: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Set approval for all tokens to an operator
   * @param operator Operator address
   * @param approved Whether to approve or revoke
   * @param signer Signer to use for the transaction
   * @returns Transaction response
   */
  async setApprovalForAll(operator: string, approved: boolean, signer: ethers.Signer): Promise<ethers.TransactionResponse> {
    try {
      const contract = this.contract.connect(signer);
      return await contract.setApprovalForAll(operator, approved);
    } catch (error) {
      logger.error(`Error setting approval for operator ${operator}:`, error);
      throw new Error(`Failed to set approval: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Check if an operator is approved for all tokens
   * @param account Account address
   * @param operator Operator address
   * @returns Whether the operator is approved
   */
  async isApprovedForAll(account: string, operator: string): Promise<boolean> {
    try {
      return await this.contract.isApprovedForAll(account, operator);
    } catch (error) {
      logger.error(`Error checking approval for account ${account} and operator ${operator}:`, error);
      throw new Error(`Failed to check approval: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Transfer tokens from one account to another
   * @param from Sender address
   * @param to Recipient address
   * @param tokenId Token ID
   * @param amount Amount to transfer
   * @param data Additional data
   * @param signer Signer to use for the transaction
   * @returns Transaction response
   */
  async safeTransferFrom(
    from: string,
    to: string,
    tokenId: number,
    amount: number,
    data: string = "0x",
    signer: ethers.Signer
  ): Promise<ethers.TransactionResponse> {
    try {
      const contract = this.contract.connect(signer);
      return await contract.safeTransferFrom(from, to, tokenId, amount, data);
    } catch (error) {
      logger.error(`Error transferring token ${tokenId} from ${from} to ${to}:`, error);
      throw new Error(`Failed to transfer token: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Mint new tokens
   * @param to Recipient address
   * @param tokenId Token ID
   * @param amount Amount to mint
   * @param metadata Token metadata
   * @param signer Signer to use for the transaction
   * @returns Transaction response
   */
  async mint(
    to: string,
    tokenId: number,
    amount: number,
    metadata: {
      projectId: string;
      vintage: number;
      standard: string;
      methodology: string;
    },
    signer: ethers.Signer
  ): Promise<ethers.TransactionResponse> {
    try {
      const contract = this.contract.connect(signer);
      const data = ethers.toUtf8Bytes(JSON.stringify(metadata));
      return await contract.mint(to, tokenId, amount, data);
    } catch (error) {
      logger.error(`Error minting token ${tokenId} to ${to}:`, error);
      throw new Error(`Failed to mint token: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Retire tokens
   * @param tokenId Token ID
   * @param amount Amount to retire
   * @param signer Signer to use for the transaction
   * @returns Transaction response
   */
  async retire(
    tokenId: number,
    amount: number,
    signer: ethers.Signer
  ): Promise<ethers.TransactionResponse> {
    try {
      const contract = this.contract.connect(signer);
      return await contract.retire(tokenId, amount);
    } catch (error) {
      logger.error(`Error retiring token ${tokenId}:`, error);
      throw new Error(`Failed to retire token: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Set token URI
   * @param tokenId Token ID
   * @param uri Token URI
   * @param signer Signer to use for the transaction
   * @returns Transaction response
   */
  async setURI(
    tokenId: number,
    uri: string,
    signer: ethers.Signer
  ): Promise<ethers.TransactionResponse> {
    try {
      const contract = this.contract.connect(signer);
      return await contract.setURI(tokenId, uri);
    } catch (error) {
      logger.error(`Error setting URI for token ${tokenId}:`, error);
      throw new Error(`Failed to set URI: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get token URI
   * @param tokenId Token ID
   * @returns Token URI
   */
  async getURI(tokenId: number): Promise<string> {
    try {
      return await this.contract.uri(tokenId);
    } catch (error) {
      logger.error(`Error getting URI for token ${tokenId}:`, error);
      throw new Error(`Failed to get URI: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get token metadata
   * @param tokenId Token ID
   * @returns Token metadata
   */
  async getTokenMetadata(tokenId: number): Promise<CarbonCreditMetadata> {
    try {
      const metadata = await this.contract.getTokenMetadata(tokenId);
      return {
        projectId: metadata[0],
        vintage: Number(metadata[1]),
        standard: metadata[2],
        methodology: metadata[3],
        isRetired: metadata[4],
      };
    } catch (error) {
      logger.error(`Error getting metadata for token ${tokenId}:`, error);
      throw new Error(`Failed to get token metadata: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get token supply
   * @param tokenId Token ID
   * @returns Token supply
   */
  async getTokenSupply(tokenId: number): Promise<CarbonCreditSupply> {
    try {
      const supply = await this.contract.getTokenSupply(tokenId);
      return {
        totalSupply: Number(supply[0]),
        retiredSupply: Number(supply[1]),
        availableSupply: Number(supply[2]),
      };
    } catch (error) {
      logger.error(`Error getting supply for token ${tokenId}:`, error);
      throw new Error(`Failed to get token supply: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
