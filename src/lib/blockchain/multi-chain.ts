import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { createWallet, getWalletBalance, getTokenBalances } from "@/lib/blockchain";
import { createLightAccount, getSmartAccountAddress } from "@/lib/blockchain/account-abstraction";
import { db } from "@/lib/db";

/**
 * Create a wallet on a specific network
 * @param network Network to create wallet on
 * @param isTestnet Whether to use testnet
 * @param name Optional wallet name
 * @param isSmartWallet Whether to create a smart wallet
 * @param userId User ID
 * @param organizationId Optional organization ID
 * @returns Created wallet details
 */
export async function createNetworkWallet(
  network: SupportedNetwork,
  isTestnet: boolean,
  name: string | undefined,
  isSmartWallet: boolean,
  userId: string,
  organizationId?: string
) {
  try {
    // Get network configuration
    const networkConfig = getNetworkConfig(network, isTestnet);

    // Create a new wallet using the blockchain integration
    const walletResult = await createWallet(isSmartWallet, network, isTestnet);
    const { address, encryptedKey, smartAccountAddress } = walletResult;

    // Create wallet in database
    const wallet = await db.wallet.create({
      data: {
        name: name || `${network} ${isTestnet ? "Testnet" : "Mainnet"} Wallet`,
        address,
        encryptedKey,
        network,
        chainId: networkConfig.chainId,
        isTestnet,
        isSmartWallet,
        smartAccountAddress: smartAccountAddress || null,
        balance: 0,
        lastSyncedAt: new Date(),
        user: {
          connect: {
            id: userId,
          },
        },
        ...(organizationId
          ? {
              organization: {
                connect: {
                  id: organizationId,
                },
              },
            }
          : {}),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "WALLET_CREATED",
        description: `Created new ${network} ${isTestnet ? "testnet" : "mainnet"} wallet`,
        userId,
        organizationId,
        metadata: {
          walletId: wallet.id,
          network,
          isTestnet,
          isSmartWallet,
          smartAccountAddress: smartAccountAddress || null,
        },
      },
    });

    logger.info(`Created new ${network} wallet for user ${userId}`);

    return wallet;
  } catch (error) {
    logger.error(`Error creating ${network} wallet:`, error);
    throw new Error(`Failed to create ${network} wallet`);
  }
}

/**
 * Get wallets for a user across all networks
 * @param userId User ID
 * @param includeTestnets Whether to include testnet wallets
 * @returns Wallets grouped by network
 */
export async function getUserWallets(userId: string, includeTestnets: boolean = true) {
  try {
    // Get user's wallets
    const wallets = await db.wallet.findMany({
      where: {
        userId,
        ...(includeTestnets ? {} : { isTestnet: false }),
      },
      include: {
        transactions: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
        tokens: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Group wallets by network
    const walletsByNetwork = wallets.reduce((acc: any, wallet) => {
      if (!acc[wallet.network]) {
        acc[wallet.network] = [];
      }
      acc[wallet.network].push(wallet);
      return acc;
    }, {});

    // Get network summaries
    const networkSummaries = Object.entries(walletsByNetwork).map(([network, networkWallets]: [string, any[]]) => {
      const totalBalance = networkWallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      const walletCount = networkWallets.length;
      const testnetCount = networkWallets.filter(wallet => wallet.isTestnet).length;
      const mainnetCount = walletCount - testnetCount;

      return {
        network,
        totalBalance,
        walletCount,
        testnetCount,
        mainnetCount,
        wallets: networkWallets.map(wallet => ({
          id: wallet.id,
          address: wallet.address,
          name: wallet.name,
          balance: wallet.balance,
          isTestnet: wallet.isTestnet,
          isSmartWallet: wallet.isSmartWallet,
          chainId: wallet.chainId,
          lastSyncedAt: wallet.lastSyncedAt,
          tokens: wallet.tokens,
          recentTransactions: wallet.transactions,
        })),
      };
    });

    return {
      networks: networkSummaries,
      totalWallets: wallets.length,
    };
  } catch (error) {
    logger.error(`Error getting user wallets:`, error);
    throw new Error("Failed to get user wallets");
  }
}

/**
 * Sync wallet balances across all networks
 * @param userId User ID
 * @returns Updated wallet balances
 */
export async function syncWalletBalances(userId: string) {
  try {
    // Get user's wallets
    const wallets = await db.wallet.findMany({
      where: {
        userId,
      },
    });

    // Update balances for each wallet
    const updatedWallets = await Promise.all(
      wallets.map(async wallet => {
        try {
          // Get wallet balance
          const balance = await getWalletBalance(
            wallet.address,
            wallet.network as SupportedNetwork,
            wallet.isTestnet
          );

          // Get token balances
          const tokens = await getTokenBalances(
            wallet.address,
            wallet.network as SupportedNetwork,
            wallet.isTestnet
          );

          // Update wallet in database
          const updatedWallet = await db.wallet.update({
            where: { id: wallet.id },
            data: {
              balance: typeof balance === "number" ? balance : parseFloat(balance.eth),
              lastSyncedAt: new Date(),
            },
          });

          // Update or create tokens
          for (const token of tokens) {
            await db.token.upsert({
              where: {
                walletId_address: {
                  walletId: wallet.id,
                  address: token.address,
                },
              },
              create: {
                address: token.address,
                name: token.name || "Unknown Token",
                symbol: token.symbol || "???",
                decimals: token.decimals || 18,
                balance: token.balance,
                wallet: {
                  connect: {
                    id: wallet.id,
                  },
                },
              },
              update: {
                balance: token.balance,
                name: token.name || "Unknown Token",
                symbol: token.symbol || "???",
                decimals: token.decimals || 18,
              },
            });
          }

          return {
            ...updatedWallet,
            tokens,
          };
        } catch (error) {
          logger.error(`Error syncing wallet ${wallet.id}:`, error);
          return wallet;
        }
      })
    );

    return updatedWallets;
  } catch (error) {
    logger.error(`Error syncing wallet balances:`, error);
    throw new Error("Failed to sync wallet balances");
  }
}

/**
 * Get total portfolio value across all networks
 * @param userId User ID
 * @returns Portfolio value and breakdown
 */
export async function getPortfolioValue(userId: string) {
  try {
    // Sync wallet balances first
    await syncWalletBalances(userId);

    // Get user's wallets with tokens
    const wallets = await db.wallet.findMany({
      where: {
        userId,
      },
      include: {
        tokens: true,
      },
    });

    // Calculate total value and breakdown
    let totalValue = 0;
    const networkValues: Record<string, number> = {};
    const assetValues: Record<string, number> = {};

    // Add native token values
    for (const wallet of wallets) {
      totalValue += wallet.balance;

      // Add to network values
      if (!networkValues[wallet.network]) {
        networkValues[wallet.network] = 0;
      }
      networkValues[wallet.network] += wallet.balance;

      // Add to asset values
      const nativeSymbol = getNetworkConfig(wallet.network as SupportedNetwork, wallet.isTestnet).nativeCurrency.symbol;
      if (!assetValues[nativeSymbol]) {
        assetValues[nativeSymbol] = 0;
      }
      assetValues[nativeSymbol] += wallet.balance;

      // Add token values
      // In a real implementation, this would convert token values to a common currency
      for (const token of wallet.tokens) {
        // For simplicity, we're not converting token values to ETH or USD
        // In a real implementation, you would use price feeds or oracles
        totalValue += token.balance;

        // Add to network values
        networkValues[wallet.network] += token.balance;

        // Add to asset values
        if (!assetValues[token.symbol]) {
          assetValues[token.symbol] = 0;
        }
        assetValues[token.symbol] += token.balance;
      }
    }

    return {
      totalValue,
      networkValues,
      assetValues,
    };
  } catch (error) {
    logger.error(`Error getting portfolio value:`, error);
    throw new Error("Failed to get portfolio value");
  }
}
