/**
 * Smart Wallet Module
 *
 * This module provides comprehensive functionality for creating and managing smart wallets
 * using Alchemy's Account Abstraction SDK. It supports:
 * - Creating smart wallets with secure key management
 * - Social recovery for lost keys
 * - Transaction batching for gas optimization
 * - Multi-chain support
 */

import { Alchemy, Network } from "alchemy-sdk";
import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain/config/networks";
import { db } from "@/lib/db";

// Import Alchemy Account Abstraction SDK components
import {
  BaseSmartContractAccount,
  createBundlerClient,
  LocalAccountSigner,
  type SmartAccountSigner,
  type UserOperationResponse,
  type UserOperationReceipt
} from "@alchemy/aa-core";
import { AlchemyGasManagerService } from "@alchemy/aa-alchemy";
import { type Chain } from "viem";
import {
  sepolia,
  polygon,
  polygonMumbai,
  optimism,
  optimismSepolia,
  arbitrum,
  arbitrumSepolia,
  base,
  baseSepolia
} from "viem/chains";

// Environment variables
const ALCHEMY_API_KEY = process.env.ALCHEMY_API_KEY || "";
const WALLET_ENCRYPTION_KEY = process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret";
const GAS_MANAGER_POLICY_ID = process.env.ALCHEMY_GAS_MANAGER_POLICY_ID || "";

/**
 * Get the default Light Account factory address for a chain
 * @param chain Viem chain
 * @returns Factory address
 */
function getDefaultLightAccountFactoryAddress(chain: Chain): string {
  // These are placeholder addresses - in a real implementation, you would use the actual factory addresses
  // from the Alchemy documentation or configuration
  switch (chain.id) {
    case 1: // Ethereum Mainnet
      return "******************************************";
    case ********: // Sepolia
      return "******************************************";
    case 137: // Polygon
      return "******************************************";
    case 80001: // Mumbai
      return "******************************************";
    case 10: // Optimism
      return "******************************************";
    case ********: // Optimism Sepolia
      return "******************************************";
    case 42161: // Arbitrum
      return "******************************************";
    case 421614: // Arbitrum Sepolia
      return "******************************************";
    case 8453: // Base
      return "******************************************";
    case 84532: // Base Sepolia
      return "******************************************";
    default:
      return "******************************************";
  }
}

/**
 * Get the viem chain for a network
 * @param network Network
 * @param useTestnet Whether to use testnet
 * @returns Viem chain
 */
export function getViemChain(network: SupportedNetwork, useTestnet: boolean): Chain {
  switch (network) {
    case SupportedNetwork.ETHEREUM:
      return useTestnet ? sepolia : undefined as unknown as Chain; // Mainnet would be imported from viem/chains
    case SupportedNetwork.POLYGON:
      return useTestnet ? polygonMumbai : polygon;
    case SupportedNetwork.OPTIMISM:
      return useTestnet ? optimismSepolia : optimism;
    case SupportedNetwork.ARBITRUM:
      return useTestnet ? arbitrumSepolia : arbitrum;
    case SupportedNetwork.BASE:
      return useTestnet ? baseSepolia : base;
    default:
      return useTestnet ? sepolia : undefined as unknown as Chain;
  }
}

/**
 * Create a new Smart Wallet using Alchemy's Account Kit
 * @param userId User ID for reference
 * @param network Blockchain network to use
 * @param useTestnet Whether to use testnet
 * @returns Smart wallet details
 */
export async function createSmartWallet(
  userId: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
) {
  try {
    // Generate a new EOA (Externally Owned Account) as the owner
    const ownerWallet = ethers.Wallet.createRandom();
    const ownerAddress = ownerWallet.address;

    // Encrypt the owner's private key
    const encryptedOwnerKey = await ownerWallet.encrypt(WALLET_ENCRYPTION_KEY);

    // Get network configuration
    const networkConfig = getNetworkConfig(network, useTestnet);

    // Get the viem chain for the network
    const chain = getViemChain(network, useTestnet);

    // Create a signer from the owner's private key
    const signer = LocalAccountSigner.privateKeyToAccountSigner(ownerWallet.privateKey);

    // Create an Alchemy client
    const client = createBundlerClient({
      url: `https://eth-${chain.name.toLowerCase()}.g.alchemy.com/v2/${ALCHEMY_API_KEY}`,
      chain,
    });

    // Create a smart account
    const account = new BaseSmartContractAccount({
      rpcClient: client,
      owner: signer,
      chain,
      factoryAddress: getDefaultLightAccountFactoryAddress(chain),
    });

    // Get the smart wallet address
    const smartWalletAddress = await account.getAddress();

    // Get the factory address
    const factoryAddress = getDefaultLightAccountFactoryAddress(chain);

    // For implementation address, we'll use a placeholder since it's not directly exposed by the SDK
    const implementationAddress = "0x"; // This would be determined from the contract

    logger.info(`Created smart wallet for user ${userId}: ${smartWalletAddress} on ${networkConfig.name}`);

    return {
      smartWalletAddress,
      ownerAddress,
      encryptedOwnerKey,
      factoryAddress,
      implementationAddress,
      network: network,
      chainId: networkConfig.chainId,
      isTestnet: useTestnet
    };
  } catch (error) {
    logger.error(`Error creating smart wallet for user ${userId}:`, error);
    throw new Error(`Failed to create smart wallet: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Add a guardian to a smart wallet for social recovery
 * @param walletId Wallet ID in the database
 * @param guardianAddress Guardian address
 * @returns Updated wallet with guardian
 */
export async function addGuardian(walletId: string, guardianAddress: string) {
  try {
    // Get the wallet from the database
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
    });

    if (!wallet) {
      throw new Error(`Wallet not found: ${walletId}`);
    }

    if (!wallet.isSmartWallet) {
      throw new Error(`Wallet is not a smart wallet: ${walletId}`);
    }

    // Add guardian to the database
    const guardian = await db.walletGuardian.create({
      data: {
        address: guardianAddress,
        status: "ACTIVE",
        wallet: { connect: { id: walletId } },
      },
    });

    // In a real implementation, we would also add the guardian to the smart contract
    // This would require a specific smart contract that supports social recovery

    logger.info(`Added guardian ${guardianAddress} to wallet ${walletId}`);

    return guardian;
  } catch (error) {
    logger.error(`Error adding guardian to wallet ${walletId}:`, error);
    throw new Error(`Failed to add guardian: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Initiate social recovery for a wallet
 * @param walletId Wallet ID in the database
 * @param recoveryInitiator Address initiating the recovery
 * @returns Recovery details
 */
export async function initiateRecovery(walletId: string, recoveryInitiator: string) {
  try {
    // Get the wallet from the database
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
      include: {
        guardians: true,
      },
    });

    if (!wallet) {
      throw new Error(`Wallet not found: ${walletId}`);
    }

    if (!wallet.isSmartWallet) {
      throw new Error(`Wallet is not a smart wallet: ${walletId}`);
    }

    // Check if the initiator is a guardian
    const isGuardian = wallet.guardians.some(g => g.address.toLowerCase() === recoveryInitiator.toLowerCase());

    if (!isGuardian) {
      throw new Error(`Recovery initiator is not a guardian: ${recoveryInitiator}`);
    }

    // Create a recovery request
    const recovery = await db.walletRecovery.create({
      data: {
        status: "PENDING",
        initiator: recoveryInitiator,
        timelock: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24-hour timelock
        wallet: { connect: { id: walletId } },
      },
    });

    logger.info(`Initiated recovery for wallet ${walletId} by ${recoveryInitiator}`);

    return recovery;
  } catch (error) {
    logger.error(`Error initiating recovery for wallet ${walletId}:`, error);
    throw new Error(`Failed to initiate recovery: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Approve a recovery request
 * @param recoveryId Recovery ID in the database
 * @param guardianAddress Guardian address approving the recovery
 * @returns Approval status
 */
export async function approveRecovery(recoveryId: string, guardianAddress: string) {
  try {
    // Get the recovery request from the database
    const recovery = await db.walletRecovery.findUnique({
      where: { id: recoveryId },
      include: {
        wallet: {
          include: {
            guardians: true,
          },
        },
        approvals: true,
      },
    });

    if (!recovery) {
      throw new Error(`Recovery request not found: ${recoveryId}`);
    }

    if (recovery.status !== "PENDING") {
      throw new Error(`Recovery request is not pending: ${recoveryId}`);
    }

    // Check if the guardian is valid
    const isGuardian = recovery.wallet.guardians.some(g => g.address.toLowerCase() === guardianAddress.toLowerCase());

    if (!isGuardian) {
      throw new Error(`Address is not a guardian: ${guardianAddress}`);
    }

    // Check if the guardian has already approved
    const hasApproved = recovery.approvals.some(a => a.guardian.address.toLowerCase() === guardianAddress.toLowerCase());

    if (hasApproved) {
      throw new Error(`Guardian has already approved: ${guardianAddress}`);
    }

    // Create an approval
    await db.walletRecoveryApproval.create({
      data: {
        recovery: { connect: { id: recoveryId } },
        guardian: { connect: { address: guardianAddress } },
      },
    });

    // Check if we have enough approvals
    const requiredApprovals = Math.ceil(recovery.wallet.guardians.length * 0.6); // 60% threshold
    const currentApprovals = recovery.approvals.length + 1; // +1 for the new approval

    let isComplete = false;

    // If we have enough approvals, update the recovery status
    if (currentApprovals >= requiredApprovals) {
      await db.walletRecovery.update({
        where: { id: recoveryId },
        data: { status: "APPROVED" },
      });

      isComplete = true;
    }

    logger.info(`Recovery ${recoveryId} approved by guardian ${guardianAddress}`);

    return {
      approvals: currentApprovals,
      requiredApprovals,
      isComplete,
    };
  } catch (error) {
    logger.error(`Error approving recovery ${recoveryId}:`, error);
    throw new Error(`Failed to approve recovery: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Execute a recovery request
 * @param recoveryId Recovery ID in the database
 * @param newOwnerAddress New owner address
 * @returns Updated wallet
 */
export async function executeRecovery(recoveryId: string, newOwnerAddress: string) {
  try {
    // Get the recovery request from the database
    const recovery = await db.walletRecovery.findUnique({
      where: { id: recoveryId },
      include: {
        wallet: true,
        approvals: true,
      },
    });

    if (!recovery) {
      throw new Error(`Recovery request not found: ${recoveryId}`);
    }

    if (recovery.status !== "APPROVED") {
      throw new Error(`Recovery request is not approved: ${recoveryId}`);
    }

    // Check if the timelock has expired
    if (recovery.timelock > new Date()) {
      throw new Error(`Recovery timelock has not expired: ${recoveryId}`);
    }

    // Generate a new wallet with the new owner
    const wallet = recovery.wallet;

    // In a real implementation, we would update the smart contract owner
    // For now, we'll just update the database

    // Update the wallet with the new owner
    const updatedWallet = await db.wallet.update({
      where: { id: wallet.id },
      data: {
        ownerAddress: newOwnerAddress,
      },
    });

    // Update the recovery status
    await db.walletRecovery.update({
      where: { id: recoveryId },
      data: { status: "EXECUTED" },
    });

    logger.info(`Recovery ${recoveryId} executed with new owner ${newOwnerAddress}`);

    return updatedWallet;
  } catch (error) {
    logger.error(`Error executing recovery ${recoveryId}:`, error);
    throw new Error(`Failed to execute recovery: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Send tokens from a smart wallet
 * @param encryptedOwnerKey Encrypted private key of the wallet owner
 * @param smartWalletAddress Smart wallet address
 * @param to Recipient address
 * @param tokenAddress Token contract address (use 'ETH' for native ETH)
 * @param amount Amount to send
 * @param network Blockchain network
 * @param useTestnet Whether to use testnet
 * @returns Transaction details
 */
export async function sendTokens(
  encryptedOwnerKey: string,
  smartWalletAddress: string,
  to: string,
  tokenAddress: string,
  amount: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true,
  useGasManager: boolean = false
) {
  try {
    // Decrypt the owner's private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedOwnerKey,
      WALLET_ENCRYPTION_KEY
    );

    // Get the viem chain for the network
    const chain = getViemChain(network, useTestnet);

    // Create a signer from the owner's private key
    const signer = LocalAccountSigner.privateKeyToAccountSigner(wallet.privateKey);

    // Create an Alchemy client with gas manager if requested
    const client = createBundlerClient({
      url: `https://eth-${chain.name.toLowerCase()}.g.alchemy.com/v2/${ALCHEMY_API_KEY}`,
      chain,
    });

    // Create a smart account
    const account = new BaseSmartContractAccount({
      rpcClient: client,
      owner: signer,
      chain,
      factoryAddress: getDefaultLightAccountFactoryAddress(chain),
    });

    // Verify the smart wallet address
    const calculatedAddress = await account.getAddress();
    if (calculatedAddress.toLowerCase() !== smartWalletAddress.toLowerCase()) {
      throw new Error(`Address mismatch: expected ${smartWalletAddress}, got ${calculatedAddress}`);
    }

    let txData: string;
    let value: bigint = BigInt(0);

    // Handle ETH or token transfer
    if (tokenAddress === 'ETH') {
      // For ETH transfers, we use empty data and set the value
      txData = '0x';
      value = ethers.parseEther(amount);
    } else {
      // For token transfers, we create the ERC20 transfer data
      const erc20Interface = new ethers.Interface([
        "function transfer(address to, uint256 amount) returns (bool)"
      ]);

      txData = erc20Interface.encodeFunctionData("transfer", [
        to,
        ethers.parseUnits(amount, 18) // Assuming 18 decimals, should be fetched dynamically
      ]);
    }

    // Send the transaction
    const userOp = await account.sendUserOperation({
      target: tokenAddress === 'ETH' ? to : tokenAddress,
      data: txData,
      value
    });

    // Wait for the transaction to be mined
    const receipt = await client.waitForUserOperationReceipt({ hash: userOp.hash });

    logger.info(`Transaction sent from smart wallet ${smartWalletAddress}: ${receipt?.transactionHash}`);

    return {
      hash: receipt?.transactionHash || userOp.hash,
      from: smartWalletAddress,
      to,
      amount,
      network,
      chainId: chain.id,
      isSmartAccount: true,
      userOpHash: userOp.hash,
    };
  } catch (error) {
    logger.error(`Error sending tokens from smart wallet ${smartWalletAddress}:`, error);
    throw new Error(`Failed to send tokens: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Batch multiple transactions in a single user operation
 * @param encryptedOwnerKey Encrypted private key of the wallet owner
 * @param smartWalletAddress Smart wallet address
 * @param transactions Array of transactions to batch
 * @param network Blockchain network
 * @param useTestnet Whether to use testnet
 * @returns Transaction details
 */
export async function batchTransactions(
  encryptedOwnerKey: string,
  smartWalletAddress: string,
  transactions: Array<{
    to: string;
    data: string;
    value: string;
  }>,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true,
  useGasManager: boolean = false
) {
  try {
    // Decrypt the owner's private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedOwnerKey,
      WALLET_ENCRYPTION_KEY
    );

    // Get the viem chain for the network
    const chain = getViemChain(network, useTestnet);

    // Create a signer from the owner's private key
    const signer = LocalAccountSigner.privateKeyToAccountSigner(wallet.privateKey);

    // Create an Alchemy client with gas manager if requested
    const client = createBundlerClient({
      url: `https://eth-${chain.name.toLowerCase()}.g.alchemy.com/v2/${ALCHEMY_API_KEY}`,
      chain,
    });

    // Create a smart account
    const account = new BaseSmartContractAccount({
      rpcClient: client,
      owner: signer,
      chain,
      factoryAddress: getDefaultLightAccountFactoryAddress(chain),
    });

    // Verify the smart wallet address
    const calculatedAddress = await account.getAddress();
    if (calculatedAddress.toLowerCase() !== smartWalletAddress.toLowerCase()) {
      throw new Error(`Address mismatch: expected ${smartWalletAddress}, got ${calculatedAddress}`);
    }

    // Prepare batch transactions
    const userOpPromises = transactions.map(tx => {
      return account.sendUserOperation({
        target: tx.to,
        data: tx.data,
        value: tx.value ? BigInt(tx.value) : BigInt(0)
      });
    });

    // Execute all transactions
    const userOps = await Promise.all(userOpPromises);

    // Wait for all transactions to be mined
    const receipts = await Promise.all(userOps.map(op =>
      client.waitForUserOperationReceipt({ hash: op.hash })
    ));

    logger.info(`Batch transaction sent from smart wallet ${smartWalletAddress}: ${userOps.map(op => op.hash).join(', ')}`);

    return {
      userOpHashes: userOps.map(op => op.hash),
      transactionHashes: receipts.map(r => r?.transactionHash).filter(Boolean),
      from: smartWalletAddress,
      network,
      chainId: chain.id,
      isSmartAccount: true,
    };
  } catch (error) {
    logger.error(`Error sending batch transactions from smart wallet ${smartWalletAddress}:`, error);
    throw new Error(`Failed to send batch transactions: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}