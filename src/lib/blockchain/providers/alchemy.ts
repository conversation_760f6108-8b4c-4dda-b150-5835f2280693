/**
 * Alchemy Integration Module
 *
 * This module provides integration with Alchemy's API and services,
 * including Account Abstraction, NFT API, and other Alchemy-specific features.
 */

import { Alchemy, Network, AlchemySettings, OwnedNftsResponse, NftTokenType } from "alchemy-sdk";
import { ethers } from "ethers";
// Import Alchemy Account Abstraction SDK components
import { LightSmartContractAccount, getDefaultLightAccountFactoryAddress, AlchemyProvider, LocalAccountSigner } from "@/lib/blockchain/alchemy";
import { Chain } from "viem";
import { sepolia, polygon, polygonMumbai, optimism, optimismSepolia, arbitrum, arbitrumSepolia, base, baseSepolia } from "viem/chains";
import { SupportedNetwork, getNetworkConfig } from "../../blockchain-config";
import { logger } from "../../logger";

/**
 * Map network to Alchemy Network enum
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Alchemy Network enum
 */
function getAlchemyNetwork(network: SupportedNetwork, useTestnet: boolean): Network {
  const config = getNetworkConfig(network, useTestnet);
  return config.alchemyNetwork;
}

/**
 * Map network to viem chain
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Viem Chain
 */
function getViemChain(network: SupportedNetwork, useTestnet: boolean): Chain {
  if (useTestnet) {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia;
      case SupportedNetwork.POLYGON:
        return polygonMumbai;
      case SupportedNetwork.OPTIMISM:
        return optimismSepolia;
      case SupportedNetwork.ARBITRUM:
        return arbitrumSepolia;
      case SupportedNetwork.BASE:
        return baseSepolia;
      default:
        return sepolia;
    }
  } else {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia; // Replace with mainnet in production
      case SupportedNetwork.POLYGON:
        return polygon;
      case SupportedNetwork.OPTIMISM:
        return optimism;
      case SupportedNetwork.ARBITRUM:
        return arbitrum;
      case SupportedNetwork.BASE:
        return base;
      default:
        return sepolia; // Replace with mainnet in production
    }
  }
}

/**
 * Create an Alchemy SDK instance
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Alchemy SDK instance
 */
export function createAlchemyClient(
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
): Alchemy {
  const settings: AlchemySettings = {
    apiKey: process.env.ALCHEMY_API_KEY,
    network: getAlchemyNetwork(network, useTestnet),
  };

  return new Alchemy(settings);
}

/**
 * Create a smart contract account provider using Alchemy
 * @param privateKey Private key of the owner
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Smart contract account provider
 */
export async function createSmartAccountProvider(
  privateKey: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
) {
  const chain = getViemChain(network, useTestnet);

  // Create a signer from the private key
  const signer = LocalAccountSigner.privateKeyToAccountSigner(privateKey);

  // Create an Alchemy Provider
  const provider = new AlchemyProvider({
    apiKey: process.env.ALCHEMY_API_KEY!,
    chain,
  }).connect(
    (rpcClient: any) =>
      new LightSmartContractAccount({
        rpcClient,
        owner: signer,
        chain,
        factoryAddress: getDefaultLightAccountFactoryAddress(chain),
      } as any)
  );

  return provider;
}

/**
 * Get NFTs owned by a wallet
 * @param walletAddress Wallet address
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Owned NFTs
 */
export async function getOwnedNFTs(
  walletAddress: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
): Promise<OwnedNftsResponse> {
  try {
    const alchemy = createAlchemyClient(network, useTestnet);
    return await alchemy.nft.getNftsForOwner(walletAddress);
  } catch (error) {
    logger.error(`Error getting NFTs for ${walletAddress}:`, error);
    throw new Error(`Failed to get NFTs: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get carbon credit NFTs owned by a wallet
 * @param walletAddress Wallet address
 * @param carbonCreditContractAddress Carbon credit contract address
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Carbon credit NFTs
 */
export async function getCarbonCreditNFTs(
  walletAddress: string,
  carbonCreditContractAddress: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
) {
  try {
    const alchemy = createAlchemyClient(network, useTestnet);

    // Get NFTs from the specific contract
    const nfts = await alchemy.nft.getNftsForOwner(walletAddress, {
      contractAddresses: [carbonCreditContractAddress],
    });

    return nfts;
  } catch (error) {
    logger.error(`Error getting carbon credit NFTs for ${walletAddress}:`, error);
    throw new Error(`Failed to get carbon credit NFTs: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get gas price from Alchemy
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Gas price in wei
 */
export async function getGasPrice(
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
): Promise<string> {
  try {
    const alchemy = createAlchemyClient(network, useTestnet);
    const provider = await alchemy.config.getProvider();
    const gasPrice = await provider.getFeeData();

    // Return maxFeePerGas if available (EIP-1559), otherwise fallback to gasPrice
    return gasPrice.maxFeePerGas?.toString() || gasPrice.gasPrice?.toString() || "0";
  } catch (error) {
    logger.error(`Error getting gas price:`, error);
    throw new Error(`Failed to get gas price: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Estimate gas for a transaction
 * @param txData Transaction data
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Estimated gas
 */
export async function estimateGas(
  txData: {
    to: string;
    data: string;
    value?: string;
  },
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
): Promise<string> {
  try {
    const alchemy = createAlchemyClient(network, useTestnet);
    const provider = await alchemy.config.getProvider();

    const gasEstimate = await provider.estimateGas({
      to: txData.to,
      data: txData.data,
      value: txData.value ? ethers.parseEther(txData.value) : undefined,
    });

    return gasEstimate.toString();
  } catch (error) {
    logger.error(`Error estimating gas:`, error);
    throw new Error(`Failed to estimate gas: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}
