/**
 * Blockchain Integration Module
 *
 * This module provides a unified interface for interacting with various blockchain networks
 * and smart contract functionality specific to carbon credit trading.
 */

import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import {
  SupportedNetwork,
  NetworkConfig,
  getNetworkConfig,
  getNetworkByChainId
} from "./config/networks";
import {
  getContractAddresses,
  getCarbonCreditContractAddress,
  getCarbonCreditMarketplaceContractAddress,
  getCarbonCreditVerifierContractAddress,
  getCarbonCreditRetirementContractAddress
} from "./config/contracts";
import { BlockchainClient } from "./core/client";
import { GasService, GasStrategy, GasOptions } from "./gas";
import { CarbonCreditContract, CarbonCreditMetadata, CarbonCreditSupply } from "./contracts/carbon-credit";
import { WalletManager } from "./wallets";
import { TransactionStatusService } from "./core/transaction-status";

/**
 * Blockchain service for interacting with blockchain networks
 */
export class BlockchainService {
  private network: SupportedNetwork;
  private useTestnet: boolean;
  private client: BlockchainClient;
  private gasService: GasService;
  private carbonCreditContract: CarbonCreditContract;
  private walletManager: WalletManager;
  private transactionStatusService: TransactionStatusService;

  /**
   * Create a new blockchain service
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  constructor(network: SupportedNetwork = SupportedNetwork.POLYGON, useTestnet: boolean = true) {
    this.network = network;
    this.useTestnet = useTestnet;
    this.client = new BlockchainClient(network, useTestnet);
    this.gasService = new GasService(this.client, network, useTestnet);
    this.carbonCreditContract = new CarbonCreditContract(this.client, network, useTestnet);
    this.walletManager = new WalletManager(this.client, network, useTestnet);
    this.transactionStatusService = new TransactionStatusService(this.client, network, useTestnet);
  }

  /**
   * Get the blockchain client
   * @returns Blockchain client
   */
  getClient(): BlockchainClient {
    return this.client;
  }

  /**
   * Get the gas service
   * @returns Gas service
   */
  getGasService(): GasService {
    return this.gasService;
  }

  /**
   * Get the carbon credit contract
   * @returns Carbon credit contract
   */
  getCarbonCreditContract(): CarbonCreditContract {
    return this.carbonCreditContract;
  }

  /**
   * Get the wallet manager
   * @returns Wallet manager
   */
  getWalletManager(): WalletManager {
    return this.walletManager;
  }

  /**
   * Get the transaction status service
   * @returns Transaction status service
   */
  getTransactionStatusService(): TransactionStatusService {
    return this.transactionStatusService;
  }

  /**
   * Get the network configuration
   * @returns Network configuration
   */
  getNetworkConfig(): NetworkConfig {
    return getNetworkConfig(this.network, this.useTestnet);
  }

  /**
   * Get the contract addresses
   * @returns Contract addresses
   */
  getContractAddresses() {
    return getContractAddresses(this.network, this.useTestnet);
  }

  /**
   * Change the network
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   */
  changeNetwork(network: SupportedNetwork, useTestnet: boolean = true) {
    this.network = network;
    this.useTestnet = useTestnet;
    this.client = new BlockchainClient(network, useTestnet);
    this.gasService = new GasService(this.client, network, useTestnet);
    this.carbonCreditContract = new CarbonCreditContract(this.client, network, useTestnet);
    this.walletManager = new WalletManager(this.client, network, useTestnet);
    this.transactionStatusService = new TransactionStatusService(this.client, network, useTestnet);
  }

  /**
   * Get the current network
   * @returns Current network and whether it's a testnet
   */
  getCurrentNetwork(): { network: SupportedNetwork; isTestnet: boolean } {
    return { network: this.network, isTestnet: this.useTestnet };
  }

  /**
   * Get transaction status
   * @param txHash Transaction hash
   * @param network Network to use
   * @param isTestnet Whether to use testnet
   * @returns Transaction status
   */
  async getTransactionStatus(
    txHash: string,
    network: SupportedNetwork = this.network,
    isTestnet: boolean = this.useTestnet
  ) {
    // If network or testnet setting is different, create a temporary service
    if (network !== this.network || isTestnet !== this.useTestnet) {
      const client = new BlockchainClient(network, isTestnet);
      const service = new TransactionStatusService(client, network, isTestnet);
      return service.getTransactionStatus(txHash);
    }

    // Otherwise use the existing service
    return this.transactionStatusService.getTransactionStatus(txHash);
  }

  /**
   * Initialize a blockchain client for a specific network
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   * @returns Blockchain client instance
   */
  static initializeBlockchainClient(
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true
  ) {
    return new BlockchainClient(network, useTestnet);
  }

  /**
   * Get gas price for a specific network
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   * @returns Current gas price in Gwei
   */
  static async getGasPrice(
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true
  ) {
    const client = new BlockchainClient(network, useTestnet);
    return client.getGasPrice();
  }

  /**
   * Estimate gas cost for a transaction
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   * @param txData Transaction data
   * @returns Estimated gas cost in native currency
   */
  static async estimateTransactionCost(
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true,
    txData: {
      to: string;
      data: string;
      value?: string;
    }
  ) {
    const client = new BlockchainClient(network, useTestnet);
    const gasPrice = await client.getGasPrice();
    const gasLimit = await client.estimateGas(txData);

    // Calculate cost in wei
    const costInWei = BigInt(gasPrice) * BigInt(gasLimit);

    // Convert to ether
    const costInEther = Number(costInWei) / 1e18;

    return {
      gasPrice,
      gasLimit,
      costInWei: costInWei.toString(),
      costInEther
    };
  }

  /**
   * Get transaction status
   * @param txHash Transaction hash
   * @param network Network
   * @param useTestnet Whether using testnet
   * @returns Transaction status
   */
  async getTransactionStatus(txHash: string, network?: SupportedNetwork, useTestnet?: boolean): Promise<TransactionStatusResponse> {
    // If network and useTestnet are provided, temporarily switch to that network
    if (network && useTestnet !== undefined) {
      const originalNetwork = this.network;
      const originalTestnet = this.useTestnet;
      
      // Temporarily change network
      this.changeNetwork(network, useTestnet);
      
      try {
        // Get transaction status
        const status = await this.transactionStatusService.getTransactionStatus(txHash);
        
        // Change back to original network
        this.changeNetwork(originalNetwork, originalTestnet);
        
        return status;
      } catch (error) {
        // Make sure to change back to original network even if there's an error
        this.changeNetwork(originalNetwork, originalTestnet);
        throw error;
      }
    }
    
    // Otherwise, use the current network
    return this.transactionStatusService.getTransactionStatus(txHash);
  }
}

// Create a singleton instance with default network
const blockchainService = new BlockchainService();

// Export the singleton instance
export { blockchainService };

// Export estimateTransactionCost directly for easier imports from API routes
export const estimateTransactionCost = BlockchainService.estimateTransactionCost;

// Export types and enums
export {
  SupportedNetwork,
  NetworkConfig,
  GasStrategy,
  GasOptions,
  CarbonCreditMetadata,
  CarbonCreditSupply,
};

// Export utility functions
export {
  getNetworkConfig,
  getNetworkByChainId,
  getCarbonCreditContractAddress,
  getCarbonCreditMarketplaceContractAddress,
  getCarbonCreditVerifierContractAddress,
  getCarbonCreditRetirementContractAddress,
};
