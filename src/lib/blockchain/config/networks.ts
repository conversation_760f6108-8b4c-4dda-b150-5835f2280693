/**
 * Supported blockchain networks
 */
export enum SupportedNetwork {
  ETHEREUM = "ethereum",
  POLYGON = "polygon",
  ARBITRUM = "arbitrum",
  OPTIMISM = "optimism",
  BASE = "base",
}

/**
 * Network configuration interface
 */
export interface NetworkConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  isTestnet: boolean;
}

/**
 * Get network configuration
 * @param network Network to get configuration for
 * @param useTestnet Whether to use testnet
 * @returns Network configuration
 */
export function getNetworkConfig(
  network: SupportedNetwork,
  useTestnet: boolean = true
): NetworkConfig {
  switch (network) {
    case SupportedNetwork.ETHEREUM:
      return useTestnet
        ? {
            chainId: 11155111,
            name: "<PERSON><PERSON>",
            rpcUrl: process.env.ETHEREUM_SEPOLIA_RPC_URL || "https://rpc.sepolia.org",
            blockExplorerUrl: "https://sepolia.etherscan.io",
            nativeCurrency: {
              name: "<PERSON><PERSON> Ether",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: true,
          }
        : {
            chainId: 1,
            name: "Ethereum Mainnet",
            rpcUrl: process.env.ETHEREUM_MAINNET_RPC_URL || "https://eth.llamarpc.com",
            blockExplorerUrl: "https://etherscan.io",
            nativeCurrency: {
              name: "Ether",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: false,
          };

    case SupportedNetwork.POLYGON:
      return useTestnet
        ? {
            chainId: 80001,
            name: "Mumbai",
            rpcUrl: process.env.POLYGON_MUMBAI_RPC_URL || "https://rpc-mumbai.maticvigil.com",
            blockExplorerUrl: "https://mumbai.polygonscan.com",
            nativeCurrency: {
              name: "MATIC",
              symbol: "MATIC",
              decimals: 18,
            },
            isTestnet: true,
          }
        : {
            chainId: 137,
            name: "Polygon Mainnet",
            rpcUrl: process.env.POLYGON_MAINNET_RPC_URL || "https://polygon-rpc.com",
            blockExplorerUrl: "https://polygonscan.com",
            nativeCurrency: {
              name: "MATIC",
              symbol: "MATIC",
              decimals: 18,
            },
            isTestnet: false,
          };

    case SupportedNetwork.ARBITRUM:
      return useTestnet
        ? {
            chainId: 421614,
            name: "Arbitrum Sepolia",
            rpcUrl: process.env.ARBITRUM_SEPOLIA_RPC_URL || "https://sepolia-rollup.arbitrum.io/rpc",
            blockExplorerUrl: "https://sepolia.arbiscan.io",
            nativeCurrency: {
              name: "Ethereum",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: true,
          }
        : {
            chainId: 42161,
            name: "Arbitrum One",
            rpcUrl: process.env.ARBITRUM_MAINNET_RPC_URL || "https://arb1.arbitrum.io/rpc",
            blockExplorerUrl: "https://arbiscan.io",
            nativeCurrency: {
              name: "Ethereum",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: false,
          };

    case SupportedNetwork.OPTIMISM:
      return useTestnet
        ? {
            chainId: 11155420,
            name: "Optimism Sepolia",
            rpcUrl: process.env.OPTIMISM_SEPOLIA_RPC_URL || "https://sepolia.optimism.io",
            blockExplorerUrl: "https://sepolia-optimism.etherscan.io",
            nativeCurrency: {
              name: "Ethereum",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: true,
          }
        : {
            chainId: 10,
            name: "Optimism",
            rpcUrl: process.env.OPTIMISM_MAINNET_RPC_URL || "https://mainnet.optimism.io",
            blockExplorerUrl: "https://optimistic.etherscan.io",
            nativeCurrency: {
              name: "Ethereum",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: false,
          };

    case SupportedNetwork.BASE:
      return useTestnet
        ? {
            chainId: 84532,
            name: "Base Sepolia",
            rpcUrl: process.env.BASE_SEPOLIA_RPC_URL || "https://sepolia.base.org",
            blockExplorerUrl: "https://sepolia.basescan.org",
            nativeCurrency: {
              name: "Ethereum",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: true,
          }
        : {
            chainId: 8453,
            name: "Base",
            rpcUrl: process.env.BASE_MAINNET_RPC_URL || "https://mainnet.base.org",
            blockExplorerUrl: "https://basescan.org",
            nativeCurrency: {
              name: "Ethereum",
              symbol: "ETH",
              decimals: 18,
            },
            isTestnet: false,
          };

    default:
      throw new Error(`Unsupported network: ${network}`);
  }
}

/**
 * Get network by chain ID
 * @param chainId Chain ID
 * @returns Network and whether it's a testnet
 */
export function getNetworkByChainId(chainId: number): { network: SupportedNetwork; isTestnet: boolean } {
  switch (chainId) {
    case 1:
      return { network: SupportedNetwork.ETHEREUM, isTestnet: false };
    case 11155111:
      return { network: SupportedNetwork.ETHEREUM, isTestnet: true };
    case 137:
      return { network: SupportedNetwork.POLYGON, isTestnet: false };
    case 80001:
      return { network: SupportedNetwork.POLYGON, isTestnet: true };
    case 42161:
      return { network: SupportedNetwork.ARBITRUM, isTestnet: false };
    case 421614:
      return { network: SupportedNetwork.ARBITRUM, isTestnet: true };
    case 10:
      return { network: SupportedNetwork.OPTIMISM, isTestnet: false };
    case 11155420:
      return { network: SupportedNetwork.OPTIMISM, isTestnet: true };
    case 8453:
      return { network: SupportedNetwork.BASE, isTestnet: false };
    case 84532:
      return { network: SupportedNetwork.BASE, isTestnet: true };
    default:
      throw new Error(`Unsupported chain ID: ${chainId}`);
  }
}
