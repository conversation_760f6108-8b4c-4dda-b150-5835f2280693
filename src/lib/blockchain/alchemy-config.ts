/**
 * Alchemy SDK Configuration
 *
 * This module provides utilities for configuring and creating Alchemy SDK instances
 * for different networks and use cases.
 */

import { Alchemy, Network, AlchemySettings } from "alchemy-sdk";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { sepolia, polygon, polygonMumbai, optimism, optimismSepolia, arbitrum, arbitrumSepolia, base, baseSepolia } from "viem/chains";
import { alchemy as alchemyTransport } from "@account-kit/infra";
import { logger } from "@/lib/logger";

// Get Alchemy API key from environment variables
const ALCHEMY_API_KEY = process.env.ALCHEMY_API_KEY || "";

// Check if API key is set
if (!ALCHEMY_API_KEY) {
  logger.warn("ALCHEMY_API_KEY environment variable is not set. Blockchain functionality may be limited.");
}

// Map SupportedNetwork to Alchemy Network
export function getAlchemyNetwork(network: SupportedNetwork, useTestnet: boolean): Network {
  if (useTestnet) {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return Network.ETH_SEPOLIA;
      case SupportedNetwork.POLYGON:
        return Network.MATIC_MUMBAI;
      case SupportedNetwork.OPTIMISM:
        return Network.OPT_SEPOLIA;
      case SupportedNetwork.ARBITRUM:
        return Network.ARB_SEPOLIA;
      case SupportedNetwork.BASE:
        return Network.BASE_SEPOLIA;
      default:
        return Network.ETH_SEPOLIA;
    }
  } else {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return Network.ETH_MAINNET;
      case SupportedNetwork.POLYGON:
        return Network.MATIC_MAINNET;
      case SupportedNetwork.OPTIMISM:
        return Network.OPT_MAINNET;
      case SupportedNetwork.ARBITRUM:
        return Network.ARB_MAINNET;
      case SupportedNetwork.BASE:
        return Network.BASE_MAINNET;
      default:
        return Network.ETH_MAINNET;
    }
  }
}

// Map SupportedNetwork to viem chain
export function getViemChain(network: SupportedNetwork, useTestnet: boolean) {
  if (useTestnet) {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia;
      case SupportedNetwork.POLYGON:
        return polygonMumbai;
      case SupportedNetwork.OPTIMISM:
        return optimismSepolia;
      case SupportedNetwork.ARBITRUM:
        return arbitrumSepolia;
      case SupportedNetwork.BASE:
        return baseSepolia;
      default:
        return sepolia;
    }
  } else {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia; // Replace with mainnet in production
      case SupportedNetwork.POLYGON:
        return polygon;
      case SupportedNetwork.OPTIMISM:
        return optimism;
      case SupportedNetwork.ARBITRUM:
        return arbitrum;
      case SupportedNetwork.BASE:
        return base;
      default:
        return sepolia; // Replace with mainnet in production
    }
  }
}

/**
 * Create Alchemy SDK instance with enhanced configuration
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Alchemy SDK instance
 */
export function createAlchemyInstance(network: SupportedNetwork, useTestnet: boolean): Alchemy {
  const alchemyNetwork = getAlchemyNetwork(network, useTestnet);

  // Enhanced settings with retry logic and additional features
  const settings: AlchemySettings = {
    apiKey: ALCHEMY_API_KEY,
    network: alchemyNetwork,
    maxRetries: 5,
    retryDelay: 1000,
    retryBackoff: true,
    batchRequests: true,
  };

  logger.info(`Creating Alchemy instance for network: ${alchemyNetwork}`);

  return new Alchemy(settings);
}

/**
 * Create Alchemy transport for Account Kit
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Alchemy transport
 */
export function createAlchemyTransport(network: SupportedNetwork, useTestnet: boolean) {
  return alchemyTransport({
    apiKey: ALCHEMY_API_KEY,
    chain: getViemChain(network, useTestnet)
  });
}

// Default Alchemy instance (using Sepolia testnet)
export const defaultAlchemy = new Alchemy({
  apiKey: ALCHEMY_API_KEY,
  network: Network.ETH_SEPOLIA,
  maxRetries: 5,
  retryDelay: 1000,
  retryBackoff: true,
  batchRequests: true,
});
