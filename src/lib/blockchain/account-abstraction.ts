/**
 * Account Abstraction Module
 *
 * This module provides a unified interface for working with Ethereum account abstraction
 * using the Alchemy SDK. It supports creating and managing smart accounts, sending user
 * operations, and estimating gas costs.
 */

import { SupportedNetwork } from "@/lib/blockchain-config";
import { getVie<PERSON><PERSON>hain } from "./alchemy-config";
import { logger } from "@/lib/logger";
import { ethers } from "ethers";
import {
  createAlchemySmartAccountClient,
  sendUserOperation as sendAlchemyUserOperation,
  estimateUserOperationGas as estimateAlchemyUserOperationGas,
  getSmartAccountAddress as getAlchemy<PERSON><PERSON><PERSON><PERSON>untAddress,
  LocalAccountSigner,
  type SmartAccountClient,
  type SmartAccountSigner,
} from "./alchemy";

// Alchemy API key from environment variables
const ALCHEMY_API_KEY = process.env.ALCHEMY_API_KEY || "";
if (!ALCHEMY_API_KEY) {
  logger.warn("ALCHEMY_API_KEY environment variable is not set. Blockchain functionality may be limited.");
}

// Gas Manager Policy ID from environment variables
const GAS_MANAGER_POLICY_ID = process.env.ALCHEMY_GAS_MANAGER_POLICY_ID || "";
if (!GAS_MANAGER_POLICY_ID) {
  logger.warn("ALCHEMY_GAS_MANAGER_POLICY_ID environment variable is not set. Gas sponsorship will not be available.");
}

/**
 * Create a smart account client
 * @param privateKey Private key of the owner
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @param useGasManager Whether to use Alchemy's Gas Manager for sponsored transactions
 * @returns Smart account client
 */
export async function createLightAccount(
  privateKey: string,
  network: SupportedNetwork,
  useTestnet: boolean = true,
  useGasManager: boolean = false
): Promise<SmartAccountClient> {
  try {
    const chain = getViemChain(network, useTestnet);

    // Create a signer from the private key
    const signer = LocalAccountSigner.privateKeyToAccountSigner(privateKey);

    // Create smart account client
    const client = createAlchemySmartAccountClient({
      apiKey: ALCHEMY_API_KEY,
      chain,
      signer,
      useGasManager,
      gasManagerConfig: useGasManager ? {
        policyId: GAS_MANAGER_POLICY_ID,
        entryPoint: "******************************************", // Standard ERC-4337 EntryPoint
      } : undefined,
    });

    logger.info(`Created smart account client for network: ${network}, testnet: ${useTestnet}`);

    return client;
  } catch (error) {
    logger.error("Error creating smart account client:", error);
    throw new Error(`Failed to create smart account client: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Create a smart account from an encrypted private key
 * @param encryptedPrivateKey Encrypted private key
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @param useGasManager Whether to use Alchemy's Gas Manager for sponsored transactions
 * @returns Smart account client
 */
export async function createLightAccountFromEncryptedKey(
  encryptedPrivateKey: string,
  network: SupportedNetwork,
  useTestnet: boolean = true,
  useGasManager: boolean = false
): Promise<SmartAccountClient> {
  try {
    // Decrypt the private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedPrivateKey,
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );

    // Create smart account client
    return createLightAccount(wallet.privateKey, network, useTestnet, useGasManager);
  } catch (error) {
    logger.error("Error creating smart account from encrypted key:", error);
    throw new Error(`Failed to create smart account from encrypted key: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Send a user operation using a smart account client
 * @param client Smart account client
 * @param to Target address
 * @param data Transaction data
 * @param value Transaction value
 * @returns Transaction hash and receipt
 */
export async function sendUserOperation(
  client: SmartAccountClient,
  to: string,
  data: string,
  value: bigint = BigInt(0)
) {
  try {
    // Send user operation
    const result = await sendAlchemyUserOperation(client, {
      target: to,
      data,
      value,
    });

    logger.info(`User operation sent: ${result.hash}`);

    return {
      hash: result.hash,
      txHash: result.receipt?.receipt?.transactionHash || "",
      receipt: result.receipt,
    };
  } catch (error) {
    logger.error("Error sending user operation:", error);
    throw new Error(`Failed to send user operation: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Send a batch of user operations using a smart account client
 * @param client Smart account client
 * @param operations Array of operations with target, data, and value
 * @returns Transaction hash and receipt
 */
export async function sendBatchUserOperation(
  client: SmartAccountClient,
  operations: Array<{
    to: string;
    data: string;
    value?: bigint;
  }>
) {
  try {
    // Format operations for the client
    const formattedOperations = operations.map(op => ({
      target: op.to,
      data: op.data,
      value: op.value || BigInt(0),
    }));

    // Send batch user operation
    const result = await sendAlchemyUserOperation(client, formattedOperations);

    logger.info(`Batch user operation sent: ${result.hash}`);

    return {
      hash: result.hash,
      txHash: result.receipt?.receipt?.transactionHash || "",
      receipt: result.receipt,
    };
  } catch (error) {
    logger.error("Error sending batch user operation:", error);
    throw new Error(`Failed to send batch user operation: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Estimate gas for a user operation
 * @param client Smart account client
 * @param to Target address
 * @param data Transaction data
 * @param value Transaction value
 * @returns Gas estimation
 */
export async function estimateUserOperationGas(
  client: SmartAccountClient,
  to: string,
  data: string,
  value: bigint = BigInt(0)
) {
  try {
    // Estimate gas
    const gasEstimation = await estimateAlchemyUserOperationGas(client, {
      target: to,
      data,
      value,
    });

    logger.info(`Estimated gas for user operation: ${JSON.stringify(gasEstimation)}`);

    return gasEstimation;
  } catch (error) {
    logger.error("Error estimating user operation gas:", error);
    throw new Error(`Failed to estimate user operation gas: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get the smart account address
 * @param client Smart account client
 * @returns Smart account address
 */
export async function getSmartAccountAddress(client: SmartAccountClient): Promise<string> {
  try {
    const address = await getAlchemySmartAccountAddress(client);

    logger.info(`Smart account address: ${address}`);

    return address;
  } catch (error) {
    logger.error("Error getting smart account address:", error);
    throw new Error(`Failed to get smart account address: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}
