import { 
  SubscriptionPlan, 
  SubscriptionStatus, 
  BillingType,
  BillingStatus,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { NotificationService } from '@/lib/notification-service';

/**
 * Subscription plan details interface
 */
export interface SubscriptionPlanDetails {
  name: SubscriptionPlan;
  price: number;
  features: string[];
  limits: {
    maxUsers: number;
    maxCarbonCredits: number;
    maxTransactions: number;
    maxTeams: number;
    supportLevel: string;
    customFees: boolean;
    analyticsAccess: boolean;
    apiAccess: boolean;
  };
}

/**
 * Subscription service for managing subscriptions
 */
export class SubscriptionService {
  /**
   * Get subscription plan details
   * @param plan Subscription plan
   * @returns Subscription plan details
   */
  static getPlanDetails(plan: SubscriptionPlan): SubscriptionPlanDetails {
    switch (plan) {
      case 'FREE':
        return {
          name: 'FREE',
          price: 0,
          features: [
            'Basic carbon credit listing',
            'Up to 5 users',
            'Standard transaction fees',
            'Basic analytics',
          ],
          limits: {
            maxUsers: 5,
            maxCarbonCredits: 10,
            maxTransactions: 100,
            maxTeams: 1,
            supportLevel: 'Email',
            customFees: false,
            analyticsAccess: false,
            apiAccess: false,
          },
        };
      case 'BASIC':
        return {
          name: 'BASIC',
          price: 99,
          features: [
            'Enhanced carbon credit listing',
            'Up to 20 users',
            'Reduced transaction fees',
            'Standard analytics',
            'Email support',
          ],
          limits: {
            maxUsers: 20,
            maxCarbonCredits: 50,
            maxTransactions: 500,
            maxTeams: 3,
            supportLevel: 'Email',
            customFees: false,
            analyticsAccess: true,
            apiAccess: false,
          },
        };
      case 'PREMIUM':
        return {
          name: 'PREMIUM',
          price: 299,
          features: [
            'Advanced carbon credit listing',
            'Up to 50 users',
            'Lower transaction fees',
            'Advanced analytics',
            'Priority support',
            'API access',
          ],
          limits: {
            maxUsers: 50,
            maxCarbonCredits: 200,
            maxTransactions: 2000,
            maxTeams: 10,
            supportLevel: 'Priority',
            customFees: true,
            analyticsAccess: true,
            apiAccess: true,
          },
        };
      case 'ENTERPRISE':
        return {
          name: 'ENTERPRISE',
          price: 999,
          features: [
            'Unlimited carbon credit listing',
            'Unlimited users',
            'Lowest transaction fees',
            'Custom analytics',
            'Dedicated support',
            'Full API access',
            'Custom integrations',
          ],
          limits: {
            maxUsers: Infinity,
            maxCarbonCredits: Infinity,
            maxTransactions: Infinity,
            maxTeams: Infinity,
            supportLevel: 'Dedicated',
            customFees: true,
            analyticsAccess: true,
            apiAccess: true,
          },
        };
      default:
        return this.getPlanDetails('FREE');
    }
  }

  /**
   * Create a new subscription
   * @param organizationId Organization ID
   * @param plan Subscription plan
   * @param userId User ID creating the subscription
   * @returns Created subscription
   */
  static async createSubscription(organizationId: string, plan: SubscriptionPlan, userId: string) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: {
          subscription: true,
        },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the user has permission to create a subscription
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to create a subscription');
      }

      // If the organization already has a subscription, cancel it
      if (organization.subscription) {
        await this.cancelSubscription(organization.subscription.id, userId);
      }

      // Get plan details
      const planDetails = this.getPlanDetails(plan);

      // Create the subscription
      const subscription = await db.subscription.create({
        data: {
          plan,
          startDate: new Date(),
          status: SubscriptionStatus.ACTIVE,
          organization: { connect: { id: organizationId } },
        },
      });

      // Create a billing record for the subscription
      await db.billingRecord.create({
        data: {
          amount: planDetails.price,
          description: `Subscription to ${plan} plan`,
          type: BillingType.SUBSCRIPTION,
          status: BillingStatus.PENDING,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
          organization: { connect: { id: organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'SUBSCRIPTION_CREATED',
          description: `Subscription to ${plan} plan created`,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            subscriptionId: subscription.id,
            plan,
            price: planDetails.price,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Subscription Created',
        message: `Your organization has subscribed to the ${plan} plan.`,
        type: 'SUBSCRIPTION',
        userId,
        organizationId,
        actionUrl: `/dashboard/subscription`,
        actionLabel: 'View Subscription',
      });

      // Send notification to all organization users
      const orgUsers = await db.user.findMany({
        where: {
          organizationId,
          NOT: {
            id: userId,
          },
        },
      });

      for (const orgUser of orgUsers) {
        await NotificationService.createNotification({
          title: 'New Subscription',
          message: `Your organization has subscribed to the ${plan} plan.`,
          type: 'SUBSCRIPTION',
          userId: orgUser.id,
          organizationId,
          actionUrl: `/dashboard/subscription`,
          actionLabel: 'View Subscription',
        });
      }

      logger.info(`Subscription to ${plan} plan created for organization ${organizationId} by user ${userId}`);

      return subscription;
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription
   * @param subscriptionId Subscription ID
   * @param userId User ID cancelling the subscription
   * @returns Updated subscription
   */
  static async cancelSubscription(subscriptionId: string, userId: string) {
    try {
      // Get the subscription
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: {
          organization: true,
        },
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Check if the user has permission to cancel the subscription
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to cancel this subscription');
      }

      // Update the subscription
      const updatedSubscription = await db.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: SubscriptionStatus.CANCELLED,
          endDate: new Date(),
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'SUBSCRIPTION_UPDATED',
          description: `Subscription to ${subscription.plan} plan cancelled`,
          user: { connect: { id: userId } },
          organization: { connect: { id: subscription.organizationId } },
          metadata: {
            subscriptionId,
            plan: subscription.plan,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Subscription Cancelled',
        message: `Your organization's subscription to the ${subscription.plan} plan has been cancelled.`,
        type: 'SUBSCRIPTION',
        userId,
        organizationId: subscription.organizationId,
        actionUrl: `/dashboard/subscription`,
        actionLabel: 'View Subscription',
      });

      // Send notification to all organization users
      const orgUsers = await db.user.findMany({
        where: {
          organizationId: subscription.organizationId,
          NOT: {
            id: userId,
          },
        },
      });

      for (const orgUser of orgUsers) {
        await NotificationService.createNotification({
          title: 'Subscription Cancelled',
          message: `Your organization's subscription to the ${subscription.plan} plan has been cancelled.`,
          type: 'SUBSCRIPTION',
          userId: orgUser.id,
          organizationId: subscription.organizationId,
          actionUrl: `/dashboard/subscription`,
          actionLabel: 'View Subscription',
        });
      }

      logger.info(`Subscription ${subscriptionId} cancelled by user ${userId}`);

      return updatedSubscription;
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Renew a subscription
   * @param subscriptionId Subscription ID
   * @param userId User ID renewing the subscription
   * @returns Updated subscription
   */
  static async renewSubscription(subscriptionId: string, userId: string) {
    try {
      // Get the subscription
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: {
          organization: true,
        },
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Check if the user has permission to renew the subscription
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to renew this subscription');
      }

      // Get plan details
      const planDetails = this.getPlanDetails(subscription.plan);

      // Update the subscription
      const updatedSubscription = await db.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: SubscriptionStatus.ACTIVE,
          startDate: new Date(),
          endDate: null,
        },
      });

      // Create a billing record for the subscription
      await db.billingRecord.create({
        data: {
          amount: planDetails.price,
          description: `Renewal of ${subscription.plan} plan subscription`,
          type: BillingType.SUBSCRIPTION,
          status: BillingStatus.PENDING,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
          organization: { connect: { id: subscription.organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'SUBSCRIPTION_UPDATED',
          description: `Subscription to ${subscription.plan} plan renewed`,
          user: { connect: { id: userId } },
          organization: { connect: { id: subscription.organizationId } },
          metadata: {
            subscriptionId,
            plan: subscription.plan,
            price: planDetails.price,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Subscription Renewed',
        message: `Your organization's subscription to the ${subscription.plan} plan has been renewed.`,
        type: 'SUBSCRIPTION',
        userId,
        organizationId: subscription.organizationId,
        actionUrl: `/dashboard/subscription`,
        actionLabel: 'View Subscription',
      });

      logger.info(`Subscription ${subscriptionId} renewed by user ${userId}`);

      return updatedSubscription;
    } catch (error) {
      logger.error('Error renewing subscription:', error);
      throw error;
    }
  }

  /**
   * Change subscription plan
   * @param subscriptionId Subscription ID
   * @param plan New subscription plan
   * @param userId User ID changing the plan
   * @returns Updated subscription
   */
  static async changePlan(subscriptionId: string, plan: SubscriptionPlan, userId: string) {
    try {
      // Get the subscription
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: {
          organization: true,
        },
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Check if the user has permission to change the plan
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to change this subscription');
      }

      // Get plan details
      const planDetails = this.getPlanDetails(plan);

      // Update the subscription
      const updatedSubscription = await db.subscription.update({
        where: { id: subscriptionId },
        data: {
          plan,
          startDate: new Date(),
          status: SubscriptionStatus.ACTIVE,
          endDate: null,
        },
      });

      // Create a billing record for the subscription
      await db.billingRecord.create({
        data: {
          amount: planDetails.price,
          description: `Upgrade to ${plan} plan`,
          type: BillingType.SUBSCRIPTION,
          status: BillingStatus.PENDING,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
          organization: { connect: { id: subscription.organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'SUBSCRIPTION_UPDATED',
          description: `Subscription plan changed from ${subscription.plan} to ${plan}`,
          user: { connect: { id: userId } },
          organization: { connect: { id: subscription.organizationId } },
          metadata: {
            subscriptionId,
            oldPlan: subscription.plan,
            newPlan: plan,
            price: planDetails.price,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Subscription Plan Changed',
        message: `Your organization's subscription has been changed from ${subscription.plan} to ${plan}.`,
        type: 'SUBSCRIPTION',
        userId,
        organizationId: subscription.organizationId,
        actionUrl: `/dashboard/subscription`,
        actionLabel: 'View Subscription',
      });

      // Send notification to all organization users
      const orgUsers = await db.user.findMany({
        where: {
          organizationId: subscription.organizationId,
          NOT: {
            id: userId,
          },
        },
      });

      for (const orgUser of orgUsers) {
        await NotificationService.createNotification({
          title: 'Subscription Plan Changed',
          message: `Your organization's subscription has been changed from ${subscription.plan} to ${plan}.`,
          type: 'SUBSCRIPTION',
          userId: orgUser.id,
          organizationId: subscription.organizationId,
          actionUrl: `/dashboard/subscription`,
          actionLabel: 'View Subscription',
        });
      }

      logger.info(`Subscription ${subscriptionId} plan changed from ${subscription.plan} to ${plan} by user ${userId}`);

      return updatedSubscription;
    } catch (error) {
      logger.error('Error changing subscription plan:', error);
      throw error;
    }
  }

  /**
   * Check if an organization has reached its subscription limits
   * @param organizationId Organization ID
   * @returns Limit check results
   */
  static async checkSubscriptionLimits(organizationId: string) {
    try {
      // Get the organization with its subscription
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: {
          subscription: true,
        },
      });

      if (!organization || !organization.subscription) {
        throw new Error('Organization or subscription not found');
      }

      // Get plan details
      const planDetails = this.getPlanDetails(organization.subscription.plan);

      // Count users
      const userCount = await db.user.count({
        where: { organizationId },
      });

      // Count carbon credits
      const carbonCreditCount = await db.carbonCredit.count({
        where: { organizationId },
      });

      // Count transactions
      const transactionCount = await db.transaction.count({
        where: {
          wallet: {
            organizationId,
          },
        },
      });

      // Count teams
      const teamCount = await db.team.count({
        where: { organizationId },
      });

      return {
        plan: organization.subscription.plan,
        limits: planDetails.limits,
        usage: {
          users: {
            used: userCount,
            limit: planDetails.limits.maxUsers,
            percentage: planDetails.limits.maxUsers === Infinity ? 0 : (userCount / planDetails.limits.maxUsers) * 100,
            exceeded: planDetails.limits.maxUsers !== Infinity && userCount > planDetails.limits.maxUsers,
          },
          carbonCredits: {
            used: carbonCreditCount,
            limit: planDetails.limits.maxCarbonCredits,
            percentage: planDetails.limits.maxCarbonCredits === Infinity ? 0 : (carbonCreditCount / planDetails.limits.maxCarbonCredits) * 100,
            exceeded: planDetails.limits.maxCarbonCredits !== Infinity && carbonCreditCount > planDetails.limits.maxCarbonCredits,
          },
          transactions: {
            used: transactionCount,
            limit: planDetails.limits.maxTransactions,
            percentage: planDetails.limits.maxTransactions === Infinity ? 0 : (transactionCount / planDetails.limits.maxTransactions) * 100,
            exceeded: planDetails.limits.maxTransactions !== Infinity && transactionCount > planDetails.limits.maxTransactions,
          },
          teams: {
            used: teamCount,
            limit: planDetails.limits.maxTeams,
            percentage: planDetails.limits.maxTeams === Infinity ? 0 : (teamCount / planDetails.limits.maxTeams) * 100,
            exceeded: planDetails.limits.maxTeams !== Infinity && teamCount > planDetails.limits.maxTeams,
          },
        },
        features: {
          customFees: planDetails.limits.customFees,
          analyticsAccess: planDetails.limits.analyticsAccess,
          apiAccess: planDetails.limits.apiAccess,
          supportLevel: planDetails.limits.supportLevel,
        },
      };
    } catch (error) {
      logger.error('Error checking subscription limits:', error);
      throw error;
    }
  }
}
