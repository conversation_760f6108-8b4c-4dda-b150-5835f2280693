import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { Announcement } from './types';
import { Prisma } from '@prisma/client';

/**
 * Announcement service
 */
export class AnnouncementService {
  /**
   * Get active announcements
   * @param options Options for filtering announcements
   * @returns Active announcements
   */
  static async getActiveAnnouncements(options: {
    userId?: string;
    organizationId?: string;
    includeAll?: boolean;
    isAdmin?: boolean;
  } = {}): Promise<Announcement[]> {
    try {
      const { userId, organizationId, includeAll = false, isAdmin = false } = options;
      
      // Current date for filtering active announcements
      const now = new Date();
      
      // Build query
      const query: Prisma.AnnouncementWhereInput = {
        ...((!includeAll || !isAdmin) && {
          startDate: { lte: now },
          endDate: { gte: now },
          published: true,
        }),
      };
      
      // Filter by audience
      if (!isAdmin) {
        if (userId) {
          // For authenticated users
          query.OR = [
            { targetAudience: "ALL" },
            { targetAudience: "AUTHENTICATED" },
          ];
          
          // Add organization-specific targeting if user belongs to an organization
          if (organizationId) {
            query.OR.push({
              targetAudience: "ORGANIZATION",
              targetOrganizationIds: {
                has: organizationId,
              },
            });
          }
        } else {
          // For unauthenticated users, only show public announcements
          query.targetAudience = "ALL";
        }
      }
      
      // Get announcements
      const announcements = await db.announcement.findMany({
        where: query,
        orderBy: [
          { priority: "desc" },
          { startDate: "desc" },
        ],
      });
      
      return announcements;
    } catch (error) {
      logger.error("Error getting active announcements:", error);
      throw new Error(`Failed to get active announcements: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get announcement by ID
   * @param id Announcement ID
   * @returns Announcement
   */
  static async getAnnouncementById(id: string): Promise<Announcement | null> {
    try {
      return await db.announcement.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error getting announcement ${id}:`, error);
      throw new Error(`Failed to get announcement: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a new announcement
   * @param data Announcement data
   * @param createdById User ID of the creator
   * @returns Created announcement
   */
  static async createAnnouncement(
    data: Omit<Announcement, 'id' | 'createdById' | 'createdAt' | 'updatedAt'>,
    createdById: string
  ): Promise<Announcement> {
    try {
      const announcement = await db.announcement.create({
        data: {
          ...data,
          createdBy: {
            connect: { id: createdById },
          },
        },
      });
      
      logger.info(`Announcement created: ${announcement.id} by user ${createdById}`);
      return announcement;
    } catch (error) {
      logger.error("Error creating announcement:", error);
      throw new Error(`Failed to create announcement: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Update an announcement
   * @param id Announcement ID
   * @param data Announcement data
   * @returns Updated announcement
   */
  static async updateAnnouncement(
    id: string,
    data: Partial<Omit<Announcement, 'id' | 'createdById' | 'createdAt' | 'updatedAt'>>
  ): Promise<Announcement> {
    try {
      const announcement = await db.announcement.update({
        where: { id },
        data,
      });
      
      logger.info(`Announcement updated: ${announcement.id}`);
      return announcement;
    } catch (error) {
      logger.error(`Error updating announcement ${id}:`, error);
      throw new Error(`Failed to update announcement: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Delete an announcement
   * @param id Announcement ID
   * @returns Deleted announcement
   */
  static async deleteAnnouncement(id: string): Promise<Announcement> {
    try {
      const announcement = await db.announcement.delete({
        where: { id },
      });
      
      logger.info(`Announcement deleted: ${announcement.id}`);
      return announcement;
    } catch (error) {
      logger.error(`Error deleting announcement ${id}:`, error);
      throw new Error(`Failed to delete announcement: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Publish an announcement
   * @param id Announcement ID
   * @returns Published announcement
   */
  static async publishAnnouncement(id: string): Promise<Announcement> {
    try {
      const announcement = await db.announcement.update({
        where: { id },
        data: {
          published: true,
        },
      });
      
      logger.info(`Announcement published: ${announcement.id}`);
      return announcement;
    } catch (error) {
      logger.error(`Error publishing announcement ${id}:`, error);
      throw new Error(`Failed to publish announcement: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Unpublish an announcement
   * @param id Announcement ID
   * @returns Unpublished announcement
   */
  static async unpublishAnnouncement(id: string): Promise<Announcement> {
    try {
      const announcement = await db.announcement.update({
        where: { id },
        data: {
          published: false,
        },
      });
      
      logger.info(`Announcement unpublished: ${announcement.id}`);
      return announcement;
    } catch (error) {
      logger.error(`Error unpublishing announcement ${id}:`, error);
      throw new Error(`Failed to unpublish announcement: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
