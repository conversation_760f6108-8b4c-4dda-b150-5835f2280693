import { NotificationType, NotificationPriority, Prisma } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { NotificationData } from '../types';

/**
 * In-app notification channel
 */
export class InAppNotificationChannel {
  /**
   * Send an in-app notification
   * @param data Notification data
   * @returns Created notification
   */
  static async send(data: NotificationData) {
    try {
      // Create notification in database
      const notification = await db.notification.create({
        data: {
          title: data.title,
          message: data.message,
          type: data.type,
          priority: data.priority || NotificationPriority.NORMAL,
          user: {
            connect: { id: data.userId }
          },
          ...(data.organizationId && {
            organization: {
              connect: { id: data.organizationId }
            }
          }),
          actionUrl: data.actionUrl,
          actionLabel: data.actionLabel,
          icon: data.icon,
          expiresAt: data.expiresAt,
          metadata: data.metadata as Prisma.JsonObject,
        },
      });

      logger.info(`Created in-app notification: ${notification.id} for user ${data.userId}`);
      return notification;
    } catch (error) {
      logger.error(`Error creating in-app notification for user ${data.userId}:`, error);
      throw new Error(`Failed to create in-app notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Mark a notification as read
   * @param notificationId Notification ID
   * @param userId User ID
   * @returns Updated notification
   */
  static async markAsRead(notificationId: string, userId: string) {
    try {
      // Check if notification exists and belongs to the user
      const notification = await db.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!notification) {
        throw new Error(`Notification not found or does not belong to user: ${notificationId}`);
      }

      // Update notification
      const updatedNotification = await db.notification.update({
        where: { id: notificationId },
        data: {
          read: true,
          readAt: new Date(),
        },
      });

      logger.info(`Marked notification ${notificationId} as read for user ${userId}`);
      return updatedNotification;
    } catch (error) {
      logger.error(`Error marking notification ${notificationId} as read:`, error);
      throw new Error(`Failed to mark notification as read: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId User ID
   * @returns Number of updated notifications
   */
  static async markAllAsRead(userId: string) {
    try {
      const result = await db.notification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: {
          read: true,
          readAt: new Date(),
        },
      });

      logger.info(`Marked ${result.count} notifications as read for user ${userId}`);
      return result.count;
    } catch (error) {
      logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      throw new Error(`Failed to mark all notifications as read: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Delete a notification
   * @param notificationId Notification ID
   * @param userId User ID
   * @returns Deleted notification
   */
  static async delete(notificationId: string, userId: string) {
    try {
      // Check if notification exists and belongs to the user
      const notification = await db.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!notification) {
        throw new Error(`Notification not found or does not belong to user: ${notificationId}`);
      }

      // Delete notification
      const deletedNotification = await db.notification.delete({
        where: { id: notificationId },
      });

      logger.info(`Deleted notification ${notificationId} for user ${userId}`);
      return deletedNotification;
    } catch (error) {
      logger.error(`Error deleting notification ${notificationId}:`, error);
      throw new Error(`Failed to delete notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Notifications and pagination
   */
  static async getNotifications(
    userId: string,
    filter: {
      read?: boolean;
      type?: NotificationType;
      priority?: NotificationPriority;
      limit?: number;
      offset?: number;
    } = {}
  ) {
    try {
      const { read, type, priority, limit = 10, offset = 0 } = filter;

      // Build query
      const where: Prisma.NotificationWhereInput = {
        userId,
        ...(read !== undefined && { read }),
        ...(type && { type }),
        ...(priority && { priority }),
        ...(this.getExpirationFilter()),
      };

      // Get notifications
      const notifications = await db.notification.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: offset,
        take: limit,
      });

      // Get total count
      const total = await db.notification.count({ where });

      return {
        notifications,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error(`Error getting notifications for user ${userId}:`, error);
      throw new Error(`Failed to get notifications: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get unread notifications count for a user
   * @param userId User ID
   * @returns Unread notifications count
   */
  static async getUnreadCount(userId: string) {
    try {
      const count = await db.notification.count({
        where: {
          userId,
          read: false,
          ...(this.getExpirationFilter()),
        },
      });

      return count;
    } catch (error) {
      logger.error(`Error getting unread notifications count for user ${userId}:`, error);
      throw new Error(`Failed to get unread notifications count: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Delete expired notifications
   * @returns Number of deleted notifications
   */
  static async deleteExpiredNotifications() {
    try {
      const result = await db.notification.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      logger.info(`Deleted ${result.count} expired notifications`);
      return result.count;
    } catch (error) {
      logger.error("Error deleting expired notifications:", error);
      throw new Error(`Failed to delete expired notifications: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get expiration filter for notifications
   * @returns Expiration filter
   */
  private static getExpirationFilter() {
    return {
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ],
    };
  }
}
