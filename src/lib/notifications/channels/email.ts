import { logger } from '@/lib/logger';
import { sendEmail } from '@/lib/email';
import { getEmailTemplate, getEmailSubject, EmailTemplateType } from '@/lib/email/templates';
import { NotificationData, EmailNotificationData } from '../types';
import { NotificationType } from '@prisma/client';
import { db } from '@/lib/db';

/**
 * Email notification channel
 */
export class EmailNotificationChannel {
  /**
   * Send an email notification
   * @param data Notification data
   * @returns Success status
   */
  static async send(data: NotificationData) {
    try {
      // Get user email
      const user = await db.user.findUnique({
        where: { id: data.userId },
        select: { email: true, name: true },
      });

      if (!user || !user.email) {
        logger.warn(`Cannot send email notification to user ${data.userId}: No email address found`);
        return false;
      }

      // Map notification type to email template type
      const templateType = this.mapNotificationTypeToEmailTemplate(data.type);
      
      // Prepare template data
      const templateData = {
        name: user.name || "User",
        title: data.title,
        message: data.message,
        actionUrl: data.actionUrl,
        actionLabel: data.actionLabel,
        ...data.metadata,
      };

      // Get email subject
      const subject = getEmailSubject(templateType, templateData);

      // Send email
      await sendEmail({
        to: user.email,
        subject,
        html: getEmailTemplate(templateType, templateData),
      });

      logger.info(`Sent email notification to ${user.email} for user ${data.userId}: ${data.title}`);
      return true;
    } catch (error) {
      logger.error(`Error sending email notification to user ${data.userId}:`, error);
      throw new Error(`Failed to send email notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Send a custom email notification
   * @param data Email notification data
   * @returns Success status
   */
  static async sendCustomEmail(data: EmailNotificationData) {
    try {
      // Send email
      await sendEmail({
        to: data.to,
        subject: data.subject,
        html: getEmailTemplate(data.templateType as EmailTemplateType, data.templateData),
      });

      logger.info(`Sent custom email notification to ${data.to}: ${data.subject}`);
      return true;
    } catch (error) {
      logger.error(`Error sending custom email notification to ${data.to}:`, error);
      throw new Error(`Failed to send custom email notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Map notification type to email template type
   * @param type Notification type
   * @returns Email template type
   */
  private static mapNotificationTypeToEmailTemplate(type: NotificationType): EmailTemplateType {
    switch (type) {
      case "SYSTEM":
        return EmailTemplateType.ANNOUNCEMENT;
      case "TRANSACTION":
        return EmailTemplateType.TRANSACTION_CONFIRMATION;
      case "CARBON_CREDIT":
        return EmailTemplateType.CARBON_CREDIT_VERIFICATION;
      case "BILLING":
        return EmailTemplateType.BILLING_INVOICE;
      case "VERIFICATION":
        return EmailTemplateType.VERIFICATION;
      case "SECURITY":
        return EmailTemplateType.PASSWORD_RESET;
      case "INVITATION":
        return EmailTemplateType.INVITATION;
      case "WELCOME":
        return EmailTemplateType.WELCOME;
      default:
        return EmailTemplateType.ANNOUNCEMENT;
    }
  }
}
