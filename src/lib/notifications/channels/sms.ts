import { logger } from '@/lib/logger';
import { NotificationData } from '../types';
import { db } from '@/lib/db';

/**
 * SMS notification channel
 */
export class SmsNotificationChannel {
  /**
   * Send an SMS notification
   * @param data Notification data
   * @returns Success status
   */
  static async send(data: NotificationData) {
    try {
      // Get user's phone number
      const user = await db.user.findUnique({
        where: { id: data.userId },
        select: { phone: true },
      });

      if (!user?.phone) {
        logger.warn(`Cannot send SMS notification to user ${data.userId}: No phone number found`);
        return false;
      }

      // In a real implementation, this would send an SMS using a service like Twilio
      // For now, we'll just log it
      logger.info(`Would send SMS notification to ${user.phone} for user ${data.userId}: ${data.title}`);

      // Create a short message (SMS has character limits)
      const message = this.createShortMessage(data);

      // In a real implementation, this would call an SMS service
      logger.debug(`SMS content: ${message}`);

      return true;
    } catch (error) {
      logger.error(`Error sending SMS notification to user ${data.userId}:`, error);
      throw new Error(`Failed to send SMS notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a short message for SMS
   * @param data Notification data
   * @returns Short message
   */
  private static createShortMessage(data: NotificationData): string {
    // SMS typically has a 160 character limit
    const maxLength = 160;
    
    // Start with the title
    let message = data.title;
    
    // Add the message if there's room
    if (message.length + 2 + data.message.length <= maxLength) {
      message += `: ${data.message}`;
    }
    
    // Add the action URL if there's room
    if (data.actionUrl && message.length + 2 + data.actionUrl.length <= maxLength) {
      message += ` ${data.actionUrl}`;
    }
    
    // Truncate if still too long
    if (message.length > maxLength) {
      message = message.substring(0, maxLength - 3) + "...";
    }
    
    return message;
  }

  /**
   * Verify a phone number
   * @param userId User ID
   * @param phone Phone number
   * @returns Verification code
   */
  static async verifyPhone(userId: string, phone: string) {
    try {
      // Generate a verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      
      // Store the verification code
      await db.phoneVerification.create({
        data: {
          userId,
          phone,
          code: verificationCode,
          expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
        },
      });
      
      // In a real implementation, this would send an SMS with the verification code
      logger.info(`Would send verification code ${verificationCode} to ${phone} for user ${userId}`);
      
      return verificationCode;
    } catch (error) {
      logger.error(`Error verifying phone for user ${userId}:`, error);
      throw new Error(`Failed to verify phone: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Confirm a phone verification
   * @param userId User ID
   * @param phone Phone number
   * @param code Verification code
   * @returns Success status
   */
  static async confirmPhoneVerification(userId: string, phone: string, code: string) {
    try {
      // Find the verification record
      const verification = await db.phoneVerification.findFirst({
        where: {
          userId,
          phone,
          code,
          expiresAt: {
            gt: new Date(),
          },
        },
      });
      
      if (!verification) {
        logger.warn(`Invalid or expired verification code for user ${userId} and phone ${phone}`);
        return false;
      }
      
      // Update the user's phone number
      await db.user.update({
        where: { id: userId },
        data: {
          phone,
          phoneVerified: true,
        },
      });
      
      // Delete the verification record
      await db.phoneVerification.delete({
        where: { id: verification.id },
      });
      
      logger.info(`Phone ${phone} verified for user ${userId}`);
      return true;
    } catch (error) {
      logger.error(`Error confirming phone verification for user ${userId}:`, error);
      throw new Error(`Failed to confirm phone verification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
