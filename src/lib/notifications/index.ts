import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { 
  NotificationData, 
  NotificationChannel, 
  NotificationFilter, 
  NotificationResponse,
  NotificationPreferences,
  Announcement
} from './types';
import { InAppNotificationChannel } from './channels/in-app';
import { EmailNotificationChannel } from './channels/email';
import { PushNotificationChannel } from './channels/push';
import { SmsNotificationChannel } from './channels/sms';
import { NotificationPreferencesService } from './preferences';
import { AnnouncementService } from './announcements';
import { NotificationType, NotificationPriority } from '@prisma/client';

/**
 * Notification service
 */
export class NotificationService {
  /**
   * Create a notification
   * @param data Notification data
   * @returns Created notification
   */
  static async createNotification(data: NotificationData) {
    try {
      logger.info(`Creating notification for user ${data.userId}: ${data.title}`);
      
      // Get user with notification preferences
      const user = await db.user.findUnique({
        where: { id: data.userId },
        include: {
          notificationPreferences: true,
        },
      });

      if (!user) {
        throw new Error(`User not found: ${data.userId}`);
      }

      // Determine notification channels
      const channels = data.channels || [NotificationChannel.IN_APP];
      const enabledChannels = await this.getEnabledChannels(
        data.type,
        channels,
        user.id,
        user.notificationPreferences
      );

      // Send notifications through enabled channels
      const results = {
        inApp: false,
        email: false,
        push: false,
        sms: false,
      };

      // Create in-app notification if enabled
      if (enabledChannels.includes(NotificationChannel.IN_APP)) {
        await InAppNotificationChannel.send(data);
        results.inApp = true;
      }

      // Send email notification if enabled
      if (enabledChannels.includes(NotificationChannel.EMAIL) && user.email) {
        await EmailNotificationChannel.send(data);
        results.email = true;
      }

      // Send push notification if enabled
      if (enabledChannels.includes(NotificationChannel.PUSH)) {
        await PushNotificationChannel.send(data);
        results.push = true;
      }

      // Send SMS notification if enabled
      if (enabledChannels.includes(NotificationChannel.SMS) && user.phone) {
        await SmsNotificationChannel.send(data);
        results.sms = true;
      }

      return results;
    } catch (error) {
      logger.error(`Error creating notification for user ${data.userId}:`, error);
      throw new Error(`Failed to create notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create notifications for multiple users
   * @param userIds User IDs
   * @param data Notification data without userId
   * @returns Created notifications
   */
  static async createNotificationsForUsers(
    userIds: string[],
    data: Omit<NotificationData, 'userId'>
  ) {
    try {
      const results = [];

      for (const userId of userIds) {
        const result = await this.createNotification({
          ...data,
          userId,
        });

        results.push({
          userId,
          ...result,
        });
      }

      return results;
    } catch (error) {
      logger.error('Error creating notifications for multiple users:', error);
      throw new Error(`Failed to create notifications for multiple users: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create organization-wide notification
   * @param organizationId Organization ID
   * @param data Notification data without userId
   * @param excludeUserIds User IDs to exclude
   * @returns Created notifications
   */
  static async createOrganizationNotification(
    organizationId: string,
    data: Omit<NotificationData, 'userId'>,
    excludeUserIds: string[] = []
  ) {
    try {
      // Get all users in the organization
      const users = await db.user.findMany({
        where: {
          organizationId,
          id: {
            notIn: excludeUserIds,
          },
        },
        select: {
          id: true,
        },
      });

      const userIds = users.map((user) => user.id);

      // Create notifications for all users
      return this.createNotificationsForUsers(userIds, {
        ...data,
        organizationId,
      });
    } catch (error) {
      logger.error(`Error creating organization notification for ${organizationId}:`, error);
      throw new Error(`Failed to create organization notification: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Notifications and pagination
   */
  static async getNotifications(userId: string, filter: NotificationFilter = {}): Promise<NotificationResponse> {
    return InAppNotificationChannel.getNotifications(userId, filter);
  }

  /**
   * Get unread notifications count for a user
   * @param userId User ID
   * @returns Unread notifications count
   */
  static async getUnreadCount(userId: string): Promise<number> {
    return InAppNotificationChannel.getUnreadCount(userId);
  }

  /**
   * Mark notification as read
   * @param notificationId Notification ID
   * @param userId User ID
   * @returns Updated notification
   */
  static async markAsRead(notificationId: string, userId: string) {
    return InAppNotificationChannel.markAsRead(notificationId, userId);
  }

  /**
   * Mark all notifications as read for a user
   * @param userId User ID
   * @returns Number of updated notifications
   */
  static async markAllAsRead(userId: string): Promise<number> {
    return InAppNotificationChannel.markAllAsRead(userId);
  }

  /**
   * Delete notification
   * @param notificationId Notification ID
   * @param userId User ID
   * @returns Deleted notification
   */
  static async deleteNotification(notificationId: string, userId: string) {
    return InAppNotificationChannel.delete(notificationId, userId);
  }

  /**
   * Delete expired notifications
   * @returns Number of deleted notifications
   */
  static async deleteExpiredNotifications(): Promise<number> {
    return InAppNotificationChannel.deleteExpiredNotifications();
  }

  /**
   * Get notification preferences for a user
   * @param userId User ID
   * @returns Notification preferences
   */
  static async getPreferences(userId: string): Promise<NotificationPreferences> {
    return NotificationPreferencesService.getPreferences(userId);
  }

  /**
   * Update notification preferences for a user
   * @param userId User ID
   * @param preferences Notification preferences
   * @returns Updated notification preferences
   */
  static async updatePreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> {
    return NotificationPreferencesService.updatePreferences(userId, preferences);
  }

  /**
   * Get active announcements
   * @param options Options for filtering announcements
   * @returns Active announcements
   */
  static async getActiveAnnouncements(options: {
    userId?: string;
    organizationId?: string;
    includeAll?: boolean;
    isAdmin?: boolean;
  } = {}): Promise<Announcement[]> {
    return AnnouncementService.getActiveAnnouncements(options);
  }

  /**
   * Get announcement by ID
   * @param id Announcement ID
   * @returns Announcement
   */
  static async getAnnouncementById(id: string): Promise<Announcement | null> {
    return AnnouncementService.getAnnouncementById(id);
  }

  /**
   * Create a new announcement
   * @param data Announcement data
   * @param createdById User ID of the creator
   * @returns Created announcement
   */
  static async createAnnouncement(
    data: Omit<Announcement, 'id' | 'createdById' | 'createdAt' | 'updatedAt'>,
    createdById: string
  ): Promise<Announcement> {
    return AnnouncementService.createAnnouncement(data, createdById);
  }

  /**
   * Update an announcement
   * @param id Announcement ID
   * @param data Announcement data
   * @returns Updated announcement
   */
  static async updateAnnouncement(
    id: string,
    data: Partial<Omit<Announcement, 'id' | 'createdById' | 'createdAt' | 'updatedAt'>>
  ): Promise<Announcement> {
    return AnnouncementService.updateAnnouncement(id, data);
  }

  /**
   * Delete an announcement
   * @param id Announcement ID
   * @returns Deleted announcement
   */
  static async deleteAnnouncement(id: string): Promise<Announcement> {
    return AnnouncementService.deleteAnnouncement(id);
  }

  /**
   * Register a push token for a user
   * @param userId User ID
   * @param token Push token
   * @param deviceInfo Device information
   * @returns Created push token
   */
  static async registerPushToken(userId: string, token: string, deviceInfo: any) {
    return PushNotificationChannel.registerToken(userId, token, deviceInfo);
  }

  /**
   * Unregister a push token
   * @param token Push token
   * @returns Success status
   */
  static async unregisterPushToken(token: string): Promise<boolean> {
    return PushNotificationChannel.unregisterToken(token);
  }

  /**
   * Get enabled notification channels based on user preferences
   * @param type Notification type
   * @param channels Requested channels
   * @param userId User ID
   * @param preferences User notification preferences
   * @returns Enabled channels
   */
  private static async getEnabledChannels(
    type: NotificationType,
    channels: NotificationChannel[],
    userId: string,
    preferences: NotificationPreferences | null
  ): Promise<NotificationChannel[]> {
    if (!preferences) {
      // Get preferences from database if not provided
      preferences = await NotificationPreferencesService.getPreferences(userId);
    }

    const enabledChannels: NotificationChannel[] = [];

    // Check if in-app notifications are enabled
    if (
      channels.includes(NotificationChannel.IN_APP) &&
      preferences.inAppNotifications &&
      await NotificationPreferencesService.isChannelEnabled(userId, type, 'inApp')
    ) {
      enabledChannels.push(NotificationChannel.IN_APP);
    }

    // Check if email notifications are enabled
    if (
      channels.includes(NotificationChannel.EMAIL) &&
      preferences.emailNotifications &&
      await NotificationPreferencesService.isChannelEnabled(userId, type, 'email')
    ) {
      enabledChannels.push(NotificationChannel.EMAIL);
    }

    // Check if push notifications are enabled
    if (
      channels.includes(NotificationChannel.PUSH) &&
      preferences.pushNotifications &&
      await NotificationPreferencesService.isChannelEnabled(userId, type, 'push')
    ) {
      enabledChannels.push(NotificationChannel.PUSH);
    }

    // Check if SMS notifications are enabled
    if (
      channels.includes(NotificationChannel.SMS) &&
      preferences.smsNotifications &&
      await NotificationPreferencesService.isChannelEnabled(userId, type, 'sms')
    ) {
      enabledChannels.push(NotificationChannel.SMS);
    }

    return enabledChannels;
  }
}

// Create a singleton instance
const notificationService = new NotificationService();

// Export the singleton instance
export { notificationService };

// Export types and enums
export {
  NotificationChannel,
  NotificationType,
  NotificationPriority,
  NotificationData,
  NotificationFilter,
  NotificationResponse,
  NotificationPreferences,
  Announcement
};
