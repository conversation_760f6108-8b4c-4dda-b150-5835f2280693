import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { NotificationPreferences } from './types';
import { NotificationType } from '@prisma/client';

/**
 * Notification preferences service
 */
export class NotificationPreferencesService {
  /**
   * Get notification preferences for a user
   * @param userId User ID
   * @returns Notification preferences
   */
  static async getPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      // Get user preferences
      const preferences = await db.notificationPreferences.findUnique({
        where: { userId },
      });

      // If no preferences exist, create default preferences
      if (!preferences) {
        return this.createDefaultPreferences(userId);
      }

      return preferences;
    } catch (error) {
      logger.error(`Error getting notification preferences for user ${userId}:`, error);
      throw new Error(`Failed to get notification preferences: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Update notification preferences for a user
   * @param userId User ID
   * @param preferences Notification preferences
   * @returns Updated notification preferences
   */
  static async updatePreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> {
    try {
      // Check if preferences exist
      const existingPreferences = await db.notificationPreferences.findUnique({
        where: { userId },
      });

      if (existingPreferences) {
        // Update existing preferences
        return await db.notificationPreferences.update({
          where: { userId },
          data: preferences,
        });
      } else {
        // Create new preferences
        return await db.notificationPreferences.create({
          data: {
            userId,
            ...this.getDefaultPreferences(),
            ...preferences,
          },
        });
      }
    } catch (error) {
      logger.error(`Error updating notification preferences for user ${userId}:`, error);
      throw new Error(`Failed to update notification preferences: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create default notification preferences for a user
   * @param userId User ID
   * @returns Default notification preferences
   */
  static async createDefaultPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      return await db.notificationPreferences.create({
        data: {
          userId,
          ...this.getDefaultPreferences(),
        },
      });
    } catch (error) {
      logger.error(`Error creating default notification preferences for user ${userId}:`, error);
      throw new Error(`Failed to create default notification preferences: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get default notification preferences
   * @returns Default notification preferences
   */
  static getDefaultPreferences(): Omit<NotificationPreferences, 'userId'> {
    return {
      emailNotifications: true,
      inAppNotifications: true,
      pushNotifications: false,
      smsNotifications: false,
      
      // System notifications
      systemEmailEnabled: true,
      systemInAppEnabled: true,
      systemPushEnabled: false,
      systemSmsEnabled: false,
      
      // Transaction notifications
      transactionEmailEnabled: true,
      transactionInAppEnabled: true,
      transactionPushEnabled: false,
      transactionSmsEnabled: false,
      
      // Credit notifications
      creditEmailEnabled: true,
      creditInAppEnabled: true,
      creditPushEnabled: false,
      creditSmsEnabled: false,
      
      // Billing notifications
      billingEmailEnabled: true,
      billingInAppEnabled: true,
      billingPushEnabled: false,
      billingSmsEnabled: false,
      
      // Document notifications
      documentEmailEnabled: true,
      documentInAppEnabled: true,
      documentPushEnabled: false,
      documentSmsEnabled: false,
      
      // Security notifications
      securityEmailEnabled: true,
      securityInAppEnabled: true,
      securityPushEnabled: false,
      securitySmsEnabled: false,
      
      // Marketing notifications
      marketingEmailEnabled: false,
      marketingInAppEnabled: false,
      marketingPushEnabled: false,
      marketingSmsEnabled: false,
    };
  }

  /**
   * Check if a notification channel is enabled for a user and notification type
   * @param userId User ID
   * @param type Notification type
   * @param channel Notification channel
   * @returns Whether the channel is enabled
   */
  static async isChannelEnabled(userId: string, type: NotificationType, channel: 'email' | 'inApp' | 'push' | 'sms'): Promise<boolean> {
    try {
      // Get user preferences
      const preferences = await this.getPreferences(userId);

      // Check if the channel is enabled globally
      const globalChannelEnabled = preferences[`${channel}Notifications`];
      if (!globalChannelEnabled) {
        return false;
      }

      // Check if the channel is enabled for the notification type
      const preferenceKey = this.getPreferenceKey(type, channel);
      return preferences[preferenceKey] !== false; // Default to true if preference doesn't exist
    } catch (error) {
      logger.error(`Error checking if channel ${channel} is enabled for user ${userId} and type ${type}:`, error);
      throw new Error(`Failed to check if channel is enabled: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get preference key for a notification type and channel
   * @param type Notification type
   * @param channel Notification channel
   * @returns Preference key
   */
  private static getPreferenceKey(type: NotificationType, channel: string): string {
    let typePrefix = "";

    switch (type) {
      case "SYSTEM":
        typePrefix = "system";
        break;
      case "TRANSACTION":
        typePrefix = "transaction";
        break;
      case "CARBON_CREDIT":
        typePrefix = "credit";
        break;
      case "BILLING":
        typePrefix = "billing";
        break;
      case "DOCUMENT":
        typePrefix = "document";
        break;
      case "SECURITY":
        typePrefix = "security";
        break;
      case "MARKETING":
        typePrefix = "marketing";
        break;
      default:
        typePrefix = "system"; // Default to system preferences
    }

    const channelSuffix = channel.charAt(0).toUpperCase() + channel.slice(1);
    return `${typePrefix}${channelSuffix}Enabled` as keyof NotificationPreferences;
  }
}
