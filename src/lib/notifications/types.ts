import { NotificationType, NotificationPriority } from '@prisma/client';

/**
 * Notification channel types
 */
export enum NotificationChannel {
  IN_APP = "IN_APP",
  EMAIL = "EMAIL",
  SMS = "SMS",
  PUSH = "PUSH",
}

/**
 * Notification data interface
 */
export interface NotificationData {
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  userId: string;
  organizationId?: string;
  actionUrl?: string;
  actionLabel?: string;
  icon?: string;
  expiresAt?: Date;
  metadata?: any;
  channels?: NotificationChannel[];
}

/**
 * Email notification data interface
 */
export interface EmailNotificationData {
  to: string;
  subject: string;
  templateType: string;
  templateData: Record<string, any>;
}

/**
 * Notification preference interface
 */
export interface NotificationPreferences {
  userId: string;
  emailNotifications: boolean;
  inAppNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  
  // System notifications
  systemEmailEnabled: boolean;
  systemInAppEnabled: boolean;
  systemPushEnabled: boolean;
  systemSmsEnabled: boolean;
  
  // Transaction notifications
  transactionEmailEnabled: boolean;
  transactionInAppEnabled: boolean;
  transactionPushEnabled: boolean;
  transactionSmsEnabled: boolean;
  
  // Credit notifications
  creditEmailEnabled: boolean;
  creditInAppEnabled: boolean;
  creditPushEnabled: boolean;
  creditSmsEnabled: boolean;
  
  // Billing notifications
  billingEmailEnabled: boolean;
  billingInAppEnabled: boolean;
  billingPushEnabled: boolean;
  billingSmsEnabled: boolean;
  
  // Document notifications
  documentEmailEnabled: boolean;
  documentInAppEnabled: boolean;
  documentPushEnabled: boolean;
  documentSmsEnabled: boolean;
  
  // Security notifications
  securityEmailEnabled: boolean;
  securityInAppEnabled: boolean;
  securityPushEnabled: boolean;
  securitySmsEnabled: boolean;
  
  // Marketing notifications
  marketingEmailEnabled: boolean;
  marketingInAppEnabled: boolean;
  marketingPushEnabled: boolean;
  marketingSmsEnabled: boolean;
}

/**
 * Notification filter interface
 */
export interface NotificationFilter {
  userId?: string;
  organizationId?: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  read?: boolean;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

/**
 * Notification pagination interface
 */
export interface NotificationPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Notification response interface
 */
export interface NotificationResponse {
  notifications: any[];
  pagination: NotificationPagination;
}

/**
 * Announcement interface
 */
export interface Announcement {
  id: string;
  title: string;
  message: string;
  type: 'INFO' | 'WARNING' | 'CRITICAL' | 'SUCCESS';
  startDate: Date;
  endDate: Date;
  actionUrl?: string;
  actionLabel?: string;
  dismissible: boolean;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  targetAudience: 'ALL' | 'AUTHENTICATED' | 'ORGANIZATION';
  targetOrganizationIds?: string[];
  published: boolean;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
}
