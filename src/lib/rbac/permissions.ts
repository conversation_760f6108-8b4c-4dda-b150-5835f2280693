/**
 * RBAC Permissions
 * 
 * This module defines all atomic permissions in the system.
 */

import { PermissionCategory } from './types';

/**
 * Permission definition
 */
export interface Permission {
  name: string;
  displayName: string;
  description: string;
  category: PermissionCategory;
}

/**
 * Organization permissions
 */
export const ORGANIZATION_PERMISSIONS: Permission[] = [
  {
    name: 'create:organization',
    displayName: 'Create Organization',
    description: 'Create a new organization',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'read:organization',
    displayName: 'View Organization',
    description: 'View organization details',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'update:organization',
    displayName: 'Update Organization',
    description: 'Update organization details',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'delete:organization',
    displayName: 'Delete Organization',
    description: 'Delete an organization',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'manage:organization:settings',
    displayName: 'Manage Organization Settings',
    description: 'Manage organization settings',
    category: PermissionCategory.ORGANIZATION,
  },
];

/**
 * User management permissions
 */
export const USER_MANAGEMENT_PERMISSIONS: Permission[] = [
  {
    name: 'invite:user',
    displayName: 'Invite User',
    description: 'Invite a user to the organization',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'read:user',
    displayName: 'View User',
    description: 'View user details',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'update:user',
    displayName: 'Update User',
    description: 'Update user details',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'remove:user',
    displayName: 'Remove User',
    description: 'Remove a user from the organization',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'update:user:role',
    displayName: 'Update User Role',
    description: 'Update a user\'s role',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'assign:custom:role',
    displayName: 'Assign Custom Role',
    description: 'Assign a custom role to a user',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'grant:permission',
    displayName: 'Grant Permission',
    description: 'Grant a specific permission to a user',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'revoke:permission',
    displayName: 'Revoke Permission',
    description: 'Revoke a specific permission from a user',
    category: PermissionCategory.USER_MANAGEMENT,
  },
];

/**
 * Carbon credit permissions
 */
export const CARBON_CREDIT_PERMISSIONS: Permission[] = [
  {
    name: 'create:carbon_credit',
    displayName: 'Create Carbon Credit',
    description: 'Create a new carbon credit',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'read:carbon_credit',
    displayName: 'View Carbon Credit',
    description: 'View carbon credit details',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'update:carbon_credit',
    displayName: 'Update Carbon Credit',
    description: 'Update carbon credit details',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'delete:carbon_credit',
    displayName: 'Delete Carbon Credit',
    description: 'Delete a carbon credit',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'list:carbon_credit',
    displayName: 'List Carbon Credit',
    description: 'List a carbon credit on the marketplace',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'unlist:carbon_credit',
    displayName: 'Unlist Carbon Credit',
    description: 'Remove a carbon credit from the marketplace',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'tokenize:carbon_credit',
    displayName: 'Tokenize Carbon Credit',
    description: 'Tokenize a carbon credit',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'retire:carbon_credit',
    displayName: 'Retire Carbon Credit',
    description: 'Retire a carbon credit',
    category: PermissionCategory.CARBON_CREDIT,
  },
  {
    name: 'verify:carbon_credit',
    displayName: 'Verify Carbon Credit',
    description: 'Verify a carbon credit',
    category: PermissionCategory.CARBON_CREDIT,
  },
];

/**
 * Wallet permissions
 */
export const WALLET_PERMISSIONS: Permission[] = [
  {
    name: 'create:wallet',
    displayName: 'Create Wallet',
    description: 'Create a new wallet',
    category: PermissionCategory.WALLET,
  },
  {
    name: 'read:wallet',
    displayName: 'View Wallet',
    description: 'View wallet details',
    category: PermissionCategory.WALLET,
  },
  {
    name: 'update:wallet',
    displayName: 'Update Wallet',
    description: 'Update wallet details',
    category: PermissionCategory.WALLET,
  },
  {
    name: 'delete:wallet',
    displayName: 'Delete Wallet',
    description: 'Delete a wallet',
    category: PermissionCategory.WALLET,
  },
  {
    name: 'transfer:tokens',
    displayName: 'Transfer Tokens',
    description: 'Transfer tokens from a wallet',
    category: PermissionCategory.WALLET,
  },
  {
    name: 'view:transactions',
    displayName: 'View Transactions',
    description: 'View wallet transactions',
    category: PermissionCategory.WALLET,
  },
];

/**
 * Team permissions
 */
export const TEAM_PERMISSIONS: Permission[] = [
  {
    name: 'create:team',
    displayName: 'Create Team',
    description: 'Create a new team',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'read:team',
    displayName: 'View Team',
    description: 'View team details',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'update:team',
    displayName: 'Update Team',
    description: 'Update team details',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'delete:team',
    displayName: 'Delete Team',
    description: 'Delete a team',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'add:team:member',
    displayName: 'Add Team Member',
    description: 'Add a member to a team',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'remove:team:member',
    displayName: 'Remove Team Member',
    description: 'Remove a member from a team',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'update:team:member:role',
    displayName: 'Update Team Member Role',
    description: 'Update a team member\'s role',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'create:team:role',
    displayName: 'Create Team Role',
    description: 'Create a new team role',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'update:team:role',
    displayName: 'Update Team Role',
    description: 'Update a team role',
    category: PermissionCategory.TEAM,
  },
  {
    name: 'delete:team:role',
    displayName: 'Delete Team Role',
    description: 'Delete a team role',
    category: PermissionCategory.TEAM,
  },
];

/**
 * Admin permissions
 */
export const ADMIN_PERMISSIONS: Permission[] = [
  {
    name: 'view:admin:dashboard',
    displayName: 'View Admin Dashboard',
    description: 'Access the admin dashboard',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'manage:platform',
    displayName: 'Manage Platform',
    description: 'Manage platform-wide settings',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'view:all:organizations',
    displayName: 'View All Organizations',
    description: 'View all organizations on the platform',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'view:all:users',
    displayName: 'View All Users',
    description: 'View all users on the platform',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'view:all:carbon_credits',
    displayName: 'View All Carbon Credits',
    description: 'View all carbon credits on the platform',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'view:all:transactions',
    displayName: 'View All Transactions',
    description: 'View all transactions on the platform',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'approve:organization',
    displayName: 'Approve Organization',
    description: 'Approve an organization',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'reject:organization',
    displayName: 'Reject Organization',
    description: 'Reject an organization',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'suspend:organization',
    displayName: 'Suspend Organization',
    description: 'Suspend an organization',
    category: PermissionCategory.ADMIN,
  },
  {
    name: 'manage:platform:settings',
    displayName: 'Manage Platform Settings',
    description: 'Manage platform-wide settings',
    category: PermissionCategory.ADMIN,
  },
];

/**
 * Compliance permissions
 */
export const COMPLIANCE_PERMISSIONS: Permission[] = [
  {
    name: 'view:audit:logs',
    displayName: 'View Audit Logs',
    description: 'View audit logs',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'view:kyc:verification',
    displayName: 'View KYC Verification',
    description: 'View KYC verification details',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'approve:kyc:verification',
    displayName: 'Approve KYC Verification',
    description: 'Approve KYC verification',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'reject:kyc:verification',
    displayName: 'Reject KYC Verification',
    description: 'Reject KYC verification',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'view:aml:checks',
    displayName: 'View AML Checks',
    description: 'View AML check details',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'perform:aml:check',
    displayName: 'Perform AML Check',
    description: 'Perform an AML check',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'view:compliance:documents',
    displayName: 'View Compliance Documents',
    description: 'View compliance documents',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'upload:compliance:document',
    displayName: 'Upload Compliance Document',
    description: 'Upload a compliance document',
    category: PermissionCategory.COMPLIANCE,
  },
  {
    name: 'verify:compliance:document',
    displayName: 'Verify Compliance Document',
    description: 'Verify a compliance document',
    category: PermissionCategory.COMPLIANCE,
  },
];

/**
 * Enterprise-specific permissions
 */
export const ENTERPRISE_PERMISSIONS: Permission[] = [
  {
    name: 'create:department',
    displayName: 'Create Department',
    description: 'Create a new department',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'update:department',
    displayName: 'Update Department',
    description: 'Update a department',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'delete:department',
    displayName: 'Delete Department',
    description: 'Delete a department',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'create:division',
    displayName: 'Create Division',
    description: 'Create a new division',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'update:division',
    displayName: 'Update Division',
    description: 'Update a division',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'delete:division',
    displayName: 'Delete Division',
    description: 'Delete a division',
    category: PermissionCategory.ORGANIZATION,
  },
  {
    name: 'create:custom:role',
    displayName: 'Create Custom Role',
    description: 'Create a new custom role',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'update:custom:role',
    displayName: 'Update Custom Role',
    description: 'Update a custom role',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'delete:custom:role',
    displayName: 'Delete Custom Role',
    description: 'Delete a custom role',
    category: PermissionCategory.USER_MANAGEMENT,
  },
  {
    name: 'view:analytics:advanced',
    displayName: 'View Advanced Analytics',
    description: 'Access advanced analytics',
    category: PermissionCategory.REPORTING,
  },
  {
    name: 'manage:rbac:settings',
    displayName: 'Manage RBAC Settings',
    description: 'Manage RBAC settings for the organization',
    category: PermissionCategory.SETTINGS,
  },
];

/**
 * All permissions
 */
export const ALL_PERMISSIONS: Permission[] = [
  ...ORGANIZATION_PERMISSIONS,
  ...USER_MANAGEMENT_PERMISSIONS,
  ...CARBON_CREDIT_PERMISSIONS,
  ...WALLET_PERMISSIONS,
  ...TEAM_PERMISSIONS,
  ...ADMIN_PERMISSIONS,
  ...COMPLIANCE_PERMISSIONS,
  ...ENTERPRISE_PERMISSIONS,
];

/**
 * Get a permission by name
 */
export function getPermissionByName(name: string): Permission | undefined {
  return ALL_PERMISSIONS.find(permission => permission.name === name);
}

/**
 * Get permissions by category
 */
export function getPermissionsByCategory(category: PermissionCategory): Permission[] {
  return ALL_PERMISSIONS.filter(permission => permission.category === category);
}
