/**
 * Enterprise RBAC
 * 
 * This module provides enterprise-specific RBAC features.
 */

import { db } from '@/lib/db';
import { IDepartment, IDivision, IOrganizationRbacSettings } from './types';
import { logger } from '@/lib/logger';

/**
 * Create a department
 */
export async function createDepartment(
  organizationId: string,
  name: string,
  description?: string,
  code?: string,
  parentId?: string
): Promise<string | null> {
  try {
    // Check if a department with this name already exists in the organization
    const existingDepartment = await db.department.findFirst({
      where: {
        name,
        organizationId,
      },
    });
    
    if (existingDepartment) {
      logger.warn(`Department ${name} already exists in organization ${organizationId}`);
      return null;
    }
    
    // Create the department
    const department = await db.department.create({
      data: {
        name,
        description,
        code,
        parentId,
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });
    
    // Log the department creation
    await db.auditLog.create({
      data: {
        type: 'DEPARTMENT_CREATED',
        description: `Department ${name} created for organization ${organizationId}`,
        organizationId,
        metadata: {
          departmentId: department.id,
          departmentName: name,
          parentId,
        } as any,
      },
    });
    
    return department.id;
  } catch (error) {
    logger.error('Error creating department:', error);
    return null;
  }
}

/**
 * Update a department
 */
export async function updateDepartment(
  departmentId: string,
  name?: string,
  description?: string,
  code?: string,
  parentId?: string
): Promise<boolean> {
  try {
    // Get the department
    const department = await db.department.findUnique({
      where: { id: departmentId },
    });
    
    if (!department) {
      logger.warn(`Department not found: ${departmentId}`);
      return false;
    }
    
    // Update the department
    await db.department.update({
      where: { id: departmentId },
      data: {
        name: name || undefined,
        description: description || undefined,
        code: code || undefined,
        parentId: parentId === null ? null : parentId || undefined,
      },
    });
    
    // Log the department update
    await db.auditLog.create({
      data: {
        type: 'DEPARTMENT_UPDATED',
        description: `Department ${department.name} updated`,
        organizationId: department.organizationId,
        metadata: {
          departmentId,
          name,
          description,
          code,
          parentId,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error updating department:', error);
    return false;
  }
}

/**
 * Delete a department
 */
export async function deleteDepartment(departmentId: string): Promise<boolean> {
  try {
    // Get the department
    const department = await db.department.findUnique({
      where: { id: departmentId },
      include: {
        users: true,
        divisions: true,
        children: true,
      },
    });
    
    if (!department) {
      logger.warn(`Department not found: ${departmentId}`);
      return false;
    }
    
    // Check if the department has users, divisions, or child departments
    if (department.users.length > 0 || department.divisions.length > 0 || department.children.length > 0) {
      logger.warn(`Cannot delete department ${departmentId} because it has users, divisions, or child departments`);
      return false;
    }
    
    // Delete the department
    await db.department.delete({
      where: { id: departmentId },
    });
    
    // Log the department deletion
    await db.auditLog.create({
      data: {
        type: 'DEPARTMENT_DELETED',
        description: `Department ${department.name} deleted`,
        organizationId: department.organizationId,
        metadata: {
          departmentId,
          departmentName: department.name,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error deleting department:', error);
    return false;
  }
}

/**
 * Create a division
 */
export async function createDivision(
  organizationId: string,
  name: string,
  description?: string,
  code?: string,
  departmentId?: string
): Promise<string | null> {
  try {
    // Check if a division with this name already exists in the organization
    const existingDivision = await db.division.findFirst({
      where: {
        name,
        organizationId,
      },
    });
    
    if (existingDivision) {
      logger.warn(`Division ${name} already exists in organization ${organizationId}`);
      return null;
    }
    
    // Create the division
    const division = await db.division.create({
      data: {
        name,
        description,
        code,
        departmentId,
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });
    
    // Log the division creation
    await db.auditLog.create({
      data: {
        type: 'DIVISION_CREATED',
        description: `Division ${name} created for organization ${organizationId}`,
        organizationId,
        metadata: {
          divisionId: division.id,
          divisionName: name,
          departmentId,
        } as any,
      },
    });
    
    return division.id;
  } catch (error) {
    logger.error('Error creating division:', error);
    return null;
  }
}

/**
 * Update a division
 */
export async function updateDivision(
  divisionId: string,
  name?: string,
  description?: string,
  code?: string,
  departmentId?: string
): Promise<boolean> {
  try {
    // Get the division
    const division = await db.division.findUnique({
      where: { id: divisionId },
    });
    
    if (!division) {
      logger.warn(`Division not found: ${divisionId}`);
      return false;
    }
    
    // Update the division
    await db.division.update({
      where: { id: divisionId },
      data: {
        name: name || undefined,
        description: description || undefined,
        code: code || undefined,
        departmentId: departmentId === null ? null : departmentId || undefined,
      },
    });
    
    // Log the division update
    await db.auditLog.create({
      data: {
        type: 'DIVISION_UPDATED',
        description: `Division ${division.name} updated`,
        organizationId: division.organizationId,
        metadata: {
          divisionId,
          name,
          description,
          code,
          departmentId,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error updating division:', error);
    return false;
  }
}

/**
 * Delete a division
 */
export async function deleteDivision(divisionId: string): Promise<boolean> {
  try {
    // Get the division
    const division = await db.division.findUnique({
      where: { id: divisionId },
      include: {
        users: true,
      },
    });
    
    if (!division) {
      logger.warn(`Division not found: ${divisionId}`);
      return false;
    }
    
    // Check if the division has users
    if (division.users.length > 0) {
      logger.warn(`Cannot delete division ${divisionId} because it has users`);
      return false;
    }
    
    // Delete the division
    await db.division.delete({
      where: { id: divisionId },
    });
    
    // Log the division deletion
    await db.auditLog.create({
      data: {
        type: 'DIVISION_DELETED',
        description: `Division ${division.name} deleted`,
        organizationId: division.organizationId,
        metadata: {
          divisionId,
          divisionName: division.name,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error deleting division:', error);
    return false;
  }
}

/**
 * Assign a user to a department
 */
export async function assignUserToDepartment(
  userId: string,
  departmentId: string
): Promise<boolean> {
  try {
    // Update the user
    await db.user.update({
      where: { id: userId },
      data: {
        departmentId,
      },
    });
    
    // Get the department name for logging
    const department = await db.department.findUnique({
      where: { id: departmentId },
      select: { name: true },
    });
    
    // Log the assignment
    await db.auditLog.create({
      data: {
        type: 'USER_ASSIGNED_TO_DEPARTMENT',
        description: `User ${userId} assigned to department ${department?.name || departmentId}`,
        userId,
        metadata: {
          departmentId,
          departmentName: department?.name,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error assigning user to department:', error);
    return false;
  }
}

/**
 * Assign a user to a division
 */
export async function assignUserToDivision(
  userId: string,
  divisionId: string
): Promise<boolean> {
  try {
    // Update the user
    await db.user.update({
      where: { id: userId },
      data: {
        divisionId,
      },
    });
    
    // Get the division name for logging
    const division = await db.division.findUnique({
      where: { id: divisionId },
      select: { name: true },
    });
    
    // Log the assignment
    await db.auditLog.create({
      data: {
        type: 'USER_ASSIGNED_TO_DIVISION',
        description: `User ${userId} assigned to division ${division?.name || divisionId}`,
        userId,
        metadata: {
          divisionId,
          divisionName: division?.name,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error assigning user to division:', error);
    return false;
  }
}

/**
 * Get or create RBAC settings for an organization
 */
export async function getOrCreateRbacSettings(
  organizationId: string
): Promise<IOrganizationRbacSettings | null> {
  try {
    // Check if settings already exist
    let settings = await db.organizationRbacSettings.findUnique({
      where: { organizationId },
    });
    
    if (!settings) {
      // Create default settings
      settings = await db.organizationRbacSettings.create({
        data: {
          organizationId,
          enableCustomRoles: false,
          enableResourcePermissions: false,
          enableRoleHierarchy: false,
          enableTemporaryAccess: false,
          enablePermissionRequests: false,
          permissionRequestExpiry: 72,
          temporaryAccessMaxDuration: 168,
          requireApprovalForRoles: true,
          requireApprovalForPermissions: true,
        },
      });
    }
    
    return settings as IOrganizationRbacSettings;
  } catch (error) {
    logger.error('Error getting or creating RBAC settings:', error);
    return null;
  }
}

/**
 * Update RBAC settings for an organization
 */
export async function updateRbacSettings(
  organizationId: string,
  settings: Partial<IOrganizationRbacSettings>
): Promise<boolean> {
  try {
    // Update the settings
    await db.organizationRbacSettings.upsert({
      where: { organizationId },
      update: {
        enableCustomRoles: settings.enableCustomRoles,
        enableResourcePermissions: settings.enableResourcePermissions,
        enableRoleHierarchy: settings.enableRoleHierarchy,
        enableTemporaryAccess: settings.enableTemporaryAccess,
        enablePermissionRequests: settings.enablePermissionRequests,
        permissionRequestExpiry: settings.permissionRequestExpiry,
        temporaryAccessMaxDuration: settings.temporaryAccessMaxDuration,
        requireApprovalForRoles: settings.requireApprovalForRoles,
        requireApprovalForPermissions: settings.requireApprovalForPermissions,
      },
      create: {
        organizationId,
        enableCustomRoles: settings.enableCustomRoles ?? false,
        enableResourcePermissions: settings.enableResourcePermissions ?? false,
        enableRoleHierarchy: settings.enableRoleHierarchy ?? false,
        enableTemporaryAccess: settings.enableTemporaryAccess ?? false,
        enablePermissionRequests: settings.enablePermissionRequests ?? false,
        permissionRequestExpiry: settings.permissionRequestExpiry ?? 72,
        temporaryAccessMaxDuration: settings.temporaryAccessMaxDuration ?? 168,
        requireApprovalForRoles: settings.requireApprovalForRoles ?? true,
        requireApprovalForPermissions: settings.requireApprovalForPermissions ?? true,
      },
    });
    
    // Log the settings update
    await db.auditLog.create({
      data: {
        type: 'RBAC_SETTINGS_UPDATED',
        description: `RBAC settings updated for organization ${organizationId}`,
        organizationId,
        metadata: settings as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error updating RBAC settings:', error);
    return false;
  }
}
