/**
 * Enhanced Role-Based Access Control (RBAC) System
 * 
 * This module provides a comprehensive RBAC system with:
 * - Granular permissions
 * - Dynamic role-permission mapping
 * - Custom roles with specific permission sets
 * - Resource-level permissions
 * - Team-level RBAC integration
 * - Role hierarchy
 * - Permission auditing and monitoring
 * - Dynamic permission adjustments
 * - Enterprise-specific role management
 */

export * from './types';
export * from './permissions';
export * from './roles';
export * from './resource-permissions';
export * from './team-permissions';
export * from './permission-service';
export * from './role-service';
export * from './rbac-service';
export * from './enterprise-rbac';
