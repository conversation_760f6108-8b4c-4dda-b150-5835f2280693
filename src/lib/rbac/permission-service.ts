/**
 * Permission Service
 * 
 * This module provides services for managing permissions.
 */

import { db } from '@/lib/db';
import { ALL_PERMISSIONS, getPermissionByName } from './permissions';
import { PermissionContext, PermissionCheckResult } from './types';
import { logger } from '@/lib/logger';

/**
 * Initialize permissions in the database
 */
export async function initializePermissions(): Promise<void> {
  try {
    // Check if permissions already exist
    const existingPermissions = await db.permission.count();
    
    if (existingPermissions > 0) {
      logger.info(`Permissions already initialized (${existingPermissions} found)`);
      return;
    }
    
    // Create all permissions
    for (const permission of ALL_PERMISSIONS) {
      await db.permission.create({
        data: {
          name: permission.name,
          displayName: permission.displayName,
          description: permission.description,
          category: permission.category,
        },
      });
    }
    
    logger.info(`Initialized ${ALL_PERMISSIONS.length} permissions`);
  } catch (error) {
    logger.error('Error initializing permissions:', error);
    throw error;
  }
}

/**
 * Grant a direct permission to a user
 */
export async function grantDirectPermission(
  userId: string,
  permissionName: string,
  grantedBy?: string,
  expiresAt?: Date,
  conditions?: Record<string, any>,
  scope?: Record<string, any>
): Promise<boolean> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      logger.warn(`Permission not found: ${permissionName}`);
      return false;
    }
    
    // Check if the user already has this permission
    const existingGrant = await db.permissionGrant.findUnique({
      where: {
        userId_permissionId: {
          userId,
          permissionId: permission.id,
        },
      },
    });
    
    if (existingGrant) {
      // Update the existing grant
      await db.permissionGrant.update({
        where: {
          id: existingGrant.id,
        },
        data: {
          expiresAt,
          grantedBy,
          conditions: conditions as any,
          scope: scope as any,
        },
      });
    } else {
      // Create a new grant
      await db.permissionGrant.create({
        data: {
          userId,
          permissionId: permission.id,
          expiresAt,
          grantedBy,
          conditions: conditions as any,
          scope: scope as any,
        },
      });
    }
    
    // Log the permission grant
    await db.auditLog.create({
      data: {
        type: 'PERMISSION_GRANTED',
        description: `Permission ${permissionName} granted to user ${userId}`,
        userId: grantedBy,
        metadata: {
          permissionName,
          grantedToUserId: userId,
          expiresAt,
          conditions,
          scope,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error granting direct permission:', error);
    return false;
  }
}

/**
 * Revoke a direct permission from a user
 */
export async function revokeDirectPermission(
  userId: string,
  permissionName: string,
  revokedBy?: string
): Promise<boolean> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      logger.warn(`Permission not found: ${permissionName}`);
      return false;
    }
    
    // Delete the permission grant
    await db.permissionGrant.deleteMany({
      where: {
        userId,
        permissionId: permission.id,
      },
    });
    
    // Log the permission revocation
    await db.auditLog.create({
      data: {
        type: 'PERMISSION_REVOKED',
        description: `Permission ${permissionName} revoked from user ${userId}`,
        userId: revokedBy,
        metadata: {
          permissionName,
          revokedFromUserId: userId,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error revoking direct permission:', error);
    return false;
  }
}

/**
 * Check if a user has a direct permission grant
 */
export async function checkDirectPermission(
  permissionName: string,
  context: PermissionContext
): Promise<PermissionCheckResult> {
  const { userId } = context;
  
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      return { granted: false };
    }
    
    // Check if the user has a direct grant for this permission
    const grant = await db.permissionGrant.findUnique({
      where: {
        userId_permissionId: {
          userId,
          permissionId: permission.id,
        },
      },
    });
    
    if (!grant) {
      return { granted: false };
    }
    
    // Check if the grant has expired
    if (grant.expiresAt && new Date(grant.expiresAt) < new Date()) {
      return { granted: false };
    }
    
    // Check if there are conditions and evaluate them
    if (grant.conditions) {
      const conditions = grant.conditions as Record<string, any>;
      const conditionsMet = evaluatePermissionConditions(conditions, context);
      
      if (!conditionsMet) {
        return { granted: false };
      }
    }
    
    return {
      granted: true,
      source: 'direct_grant',
      expiresAt: grant.expiresAt || undefined,
      conditions: grant.conditions as Record<string, any>,
    };
  } catch (error) {
    logger.error('Error checking direct permission:', error);
    return { granted: false };
  }
}

/**
 * Grant a temporary permission to a user
 */
export async function grantTemporaryPermission(
  userId: string,
  permissionName: string,
  expiresAt: Date,
  reason: string,
  grantedBy: string,
  resourceType?: string,
  resourceId?: string,
  conditions?: Record<string, any>
): Promise<boolean> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      logger.warn(`Permission not found: ${permissionName}`);
      return false;
    }
    
    // Create the temporary permission
    await db.temporaryPermission.create({
      data: {
        userId,
        permissionId: permission.id,
        expiresAt,
        reason,
        grantedBy,
        resourceType,
        resourceId,
        conditions: conditions as any,
      },
    });
    
    // Log the temporary permission grant
    await db.auditLog.create({
      data: {
        type: 'TEMPORARY_PERMISSION_GRANTED',
        description: `Temporary permission ${permissionName} granted to user ${userId}`,
        userId: grantedBy,
        metadata: {
          permissionName,
          grantedToUserId: userId,
          expiresAt,
          reason,
          resourceType,
          resourceId,
          conditions,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error granting temporary permission:', error);
    return false;
  }
}

/**
 * Check if a user has a temporary permission
 */
export async function checkTemporaryPermission(
  permissionName: string,
  context: PermissionContext
): Promise<PermissionCheckResult> {
  const { userId, resourceType, resourceId } = context;
  
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      return { granted: false };
    }
    
    // Build the query for temporary permissions
    const query: any = {
      userId,
      permissionId: permission.id,
      expiresAt: {
        gt: new Date(),
      },
    };
    
    // If resource type and ID are provided, check for a specific resource
    if (resourceType && resourceId) {
      query.resourceType = resourceType;
      query.resourceId = resourceId;
    }
    
    // Check if the user has a temporary permission
    const tempPermission = await db.temporaryPermission.findFirst({
      where: query,
    });
    
    if (!tempPermission) {
      return { granted: false };
    }
    
    // Check if there are conditions and evaluate them
    if (tempPermission.conditions) {
      const conditions = tempPermission.conditions as Record<string, any>;
      const conditionsMet = evaluatePermissionConditions(conditions, context);
      
      if (!conditionsMet) {
        return { granted: false };
      }
    }
    
    return {
      granted: true,
      source: 'temporary',
      expiresAt: tempPermission.expiresAt,
      conditions: tempPermission.conditions as Record<string, any>,
    };
  } catch (error) {
    logger.error('Error checking temporary permission:', error);
    return { granted: false };
  }
}

/**
 * Request a permission
 */
export async function requestPermission(
  userId: string,
  permissionName: string,
  reason: string,
  resourceType?: string,
  resourceId?: string,
  duration?: number
): Promise<boolean> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      logger.warn(`Permission not found: ${permissionName}`);
      return false;
    }
    
    // Get the user's organization
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });
    
    if (!user?.organizationId) {
      logger.warn(`User ${userId} has no organization`);
      return false;
    }
    
    // Get the organization's RBAC settings
    const rbacSettings = await db.organizationRbacSettings.findUnique({
      where: { organizationId: user.organizationId },
    });
    
    // Calculate expiration date for the request
    const expiryHours = rbacSettings?.permissionRequestExpiry || 72; // Default to 72 hours
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiryHours);
    
    // Create the permission request
    await db.permissionRequest.create({
      data: {
        userId,
        permissionId: permission.id,
        status: 'PENDING',
        reason,
        resourceType,
        resourceId,
        duration,
        expiresAt,
      },
    });
    
    // Log the permission request
    await db.auditLog.create({
      data: {
        type: 'PERMISSION_REQUESTED',
        description: `Permission ${permissionName} requested by user ${userId}`,
        userId,
        metadata: {
          permissionName,
          reason,
          resourceType,
          resourceId,
          duration,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error requesting permission:', error);
    return false;
  }
}

/**
 * Approve a permission request
 */
export async function approvePermissionRequest(
  requestId: string,
  approverId: string,
  notes?: string
): Promise<boolean> {
  try {
    // Get the request
    const request = await db.permissionRequest.findUnique({
      where: { id: requestId },
      include: {
        permission: true,
        user: true,
      },
    });
    
    if (!request) {
      logger.warn(`Permission request not found: ${requestId}`);
      return false;
    }
    
    if (request.status !== 'PENDING') {
      logger.warn(`Permission request ${requestId} is not pending`);
      return false;
    }
    
    // Update the request
    await db.permissionRequest.update({
      where: { id: requestId },
      data: {
        status: 'APPROVED',
        approverId,
        approvalNotes: notes,
      },
    });
    
    // Calculate expiration date if duration was specified
    let expiresAt: Date | undefined;
    if (request.duration) {
      expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + request.duration);
    }
    
    // Grant the permission
    if (request.resourceType && request.resourceId) {
      // Grant resource-specific permission
      await grantTemporaryPermission(
        request.userId,
        request.permission.name,
        expiresAt || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default to 30 days
        `Approved from request: ${request.reason}`,
        approverId,
        request.resourceType,
        request.resourceId
      );
    } else {
      // Grant direct permission
      await grantDirectPermission(
        request.userId,
        request.permission.name,
        approverId,
        expiresAt
      );
    }
    
    // Log the approval
    await db.auditLog.create({
      data: {
        type: 'PERMISSION_REQUEST_APPROVED',
        description: `Permission request for ${request.permission.name} approved for user ${request.userId}`,
        userId: approverId,
        metadata: {
          requestId,
          permissionName: request.permission.name,
          userId: request.userId,
          notes,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error approving permission request:', error);
    return false;
  }
}

/**
 * Reject a permission request
 */
export async function rejectPermissionRequest(
  requestId: string,
  approverId: string,
  notes?: string
): Promise<boolean> {
  try {
    // Get the request
    const request = await db.permissionRequest.findUnique({
      where: { id: requestId },
      include: {
        permission: true,
      },
    });
    
    if (!request) {
      logger.warn(`Permission request not found: ${requestId}`);
      return false;
    }
    
    if (request.status !== 'PENDING') {
      logger.warn(`Permission request ${requestId} is not pending`);
      return false;
    }
    
    // Update the request
    await db.permissionRequest.update({
      where: { id: requestId },
      data: {
        status: 'REJECTED',
        approverId,
        approvalNotes: notes,
      },
    });
    
    // Log the rejection
    await db.auditLog.create({
      data: {
        type: 'PERMISSION_REQUEST_REJECTED',
        description: `Permission request for ${request.permission.name} rejected for user ${request.userId}`,
        userId: approverId,
        metadata: {
          requestId,
          permissionName: request.permission.name,
          userId: request.userId,
          notes,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error rejecting permission request:', error);
    return false;
  }
}

/**
 * Evaluate permission conditions
 */
function evaluatePermissionConditions(
  conditions: Record<string, any>,
  context: PermissionContext
): boolean {
  // Implement condition evaluation logic here
  // This is a simple example - in a real implementation, you would have more complex conditions
  
  // Check organization condition
  if (conditions.organizationId && conditions.organizationId !== context.organizationId) {
    return false;
  }
  
  // Check team condition
  if (conditions.teamId && conditions.teamId !== context.teamId) {
    return false;
  }
  
  // Check resource conditions
  if (conditions.resourceType && conditions.resourceType !== context.resourceType) {
    return false;
  }
  
  if (conditions.resourceId && conditions.resourceId !== context.resourceId) {
    return false;
  }
  
  // Check metadata conditions
  if (conditions.metadata && context.metadata) {
    for (const [key, value] of Object.entries(conditions.metadata)) {
      if (context.metadata[key] !== value) {
        return false;
      }
    }
  }
  
  // Check time-based conditions
  if (conditions.validFrom && new Date(conditions.validFrom) > new Date()) {
    return false;
  }
  
  if (conditions.validUntil && new Date(conditions.validUntil) < new Date()) {
    return false;
  }
  
  return true;
}

/**
 * Log permission usage
 */
export async function logPermissionUsage(
  userId: string,
  permissionName: string,
  success: boolean,
  resourceType?: string,
  resourceId?: string,
  action?: string,
  metadata?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      logger.warn(`Permission not found: ${permissionName}`);
      return;
    }
    
    // Create the usage log
    await db.permissionUsageLog.create({
      data: {
        userId,
        permissionId: permission.id,
        resourceType,
        resourceId,
        action: action || permissionName,
        success,
        ipAddress,
        userAgent,
        metadata: metadata as any,
      },
    });
  } catch (error) {
    logger.error('Error logging permission usage:', error);
  }
}
