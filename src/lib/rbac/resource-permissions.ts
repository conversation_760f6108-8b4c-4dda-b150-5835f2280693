/**
 * Resource-Based Access Control
 *
 * This module provides utilities for resource-level permissions.
 */

import { db } from '@/lib/db';
import { ResourceType, PermissionContext, PermissionCheckResult } from './types';
import { logger } from '@/lib/logger';

/**
 * Check if a user has permission to access a specific resource
 */
export async function checkResourcePermission(
  permissionName: string,
  context: PermissionContext
): Promise<PermissionCheckResult> {
  const { userId, resourceType, resourceId } = context;

  if (!resourceType || !resourceId) {
    return { granted: false };
  }

  try {
    // First, ensure tenant isolation - verify the user can access this resource
    const { canAccessResource } = await import('@/lib/tenant-isolation');
    const hasAccess = await canAccessResource(userId, resourceType, resourceId);

    if (!hasAccess) {
      logger.warn(`Tenant isolation in resource permission: User ${userId} attempted to access resource ${resourceType}:${resourceId} from another organization`);
      return { granted: false };
    }

    // Check if there's a specific resource permission
    const resourcePermission = await db.resourcePermission.findFirst({
      where: {
        permission: {
          name: permissionName,
        },
        resourceType,
        resourceId,
      },
      include: {
        permission: true,
      },
    });

    if (resourcePermission) {
      // Check if there are conditions and evaluate them
      if (resourcePermission.conditions) {
        const conditions = resourcePermission.conditions as Record<string, any>;
        const conditionsMet = evaluateResourceConditions(conditions, context);

        if (!conditionsMet) {
          return { granted: false };
        }
      }

      return {
        granted: true,
        source: 'resource',
        conditions: resourcePermission.conditions as Record<string, any>,
      };
    }

    // Check resource ownership
    const isOwner = await checkResourceOwnership(userId, resourceType, resourceId);
    if (isOwner) {
      return {
        granted: true,
        source: 'resource',
      };
    }

    // Check organization ownership
    if (context.organizationId) {
      const isOrgResource = await checkOrganizationResourceOwnership(
        context.organizationId,
        resourceType,
        resourceId
      );

      if (isOrgResource) {
        // For organization resources, we need to check if the user has organization-level permission
        // This will be handled by the main permission check in rbac-service.ts
        return { granted: false };
      }
    }

    return { granted: false };
  } catch (error) {
    logger.error('Error checking resource permission:', error);
    return { granted: false };
  }
}

/**
 * Check if a user owns a resource
 */
async function checkResourceOwnership(
  userId: string,
  resourceType: ResourceType,
  resourceId: string
): Promise<boolean> {
  try {
    switch (resourceType) {
      case ResourceType.CARBON_CREDIT:
        const carbonCredit = await db.carbonCredit.findUnique({
          where: { id: resourceId },
          select: { userId: true },
        });
        return carbonCredit?.userId === userId;

      case ResourceType.WALLET:
        const wallet = await db.wallet.findUnique({
          where: { id: resourceId },
          select: { userId: true },
        });
        return wallet?.userId === userId;

      case ResourceType.TEAM:
        const teamMember = await db.teamMember.findFirst({
          where: {
            teamId: resourceId,
            userId,
            role: 'ADMIN',
          },
        });
        return !!teamMember;

      case ResourceType.USER:
        return resourceId === userId;

      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking resource ownership:', error);
    return false;
  }
}

/**
 * Check if a resource belongs to an organization
 */
async function checkOrganizationResourceOwnership(
  organizationId: string,
  resourceType: ResourceType,
  resourceId: string
): Promise<boolean> {
  try {
    switch (resourceType) {
      case ResourceType.CARBON_CREDIT:
        const carbonCredit = await db.carbonCredit.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return carbonCredit?.organizationId === organizationId;

      case ResourceType.WALLET:
        const wallet = await db.wallet.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return wallet?.organizationId === organizationId;

      case ResourceType.TEAM:
        const team = await db.team.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return team?.organizationId === organizationId;

      case ResourceType.USER:
        const user = await db.user.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return user?.organizationId === organizationId;

      case ResourceType.DEPARTMENT:
        const department = await db.department.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return department?.organizationId === organizationId;

      case ResourceType.DIVISION:
        const division = await db.division.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return division?.organizationId === organizationId;

      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking organization resource ownership:', error);
    return false;
  }
}

/**
 * Evaluate resource permission conditions
 */
function evaluateResourceConditions(
  conditions: Record<string, any>,
  context: PermissionContext
): boolean {
  // Implement condition evaluation logic here
  // This is a simple example - in a real implementation, you would have more complex conditions

  // Check user condition
  if (conditions.userId && conditions.userId !== context.userId) {
    return false;
  }

  // Check organization condition
  if (conditions.organizationId && conditions.organizationId !== context.organizationId) {
    return false;
  }

  // Check team condition
  if (conditions.teamId && conditions.teamId !== context.teamId) {
    return false;
  }

  // Check metadata conditions
  if (conditions.metadata && context.metadata) {
    for (const [key, value] of Object.entries(conditions.metadata)) {
      if (context.metadata[key] !== value) {
        return false;
      }
    }
  }

  // Check time-based conditions
  if (conditions.validFrom && new Date(conditions.validFrom) > new Date()) {
    return false;
  }

  if (conditions.validUntil && new Date(conditions.validUntil) < new Date()) {
    return false;
  }

  return true;
}

/**
 * Grant a resource permission
 */
export async function grantResourcePermission(
  permissionName: string,
  resourceType: ResourceType,
  resourceId: string,
  conditions?: Record<string, any>
): Promise<boolean> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });

    if (!permission) {
      return false;
    }

    // Create or update the resource permission
    await db.resourcePermission.upsert({
      where: {
        permissionId_resourceType_resourceId: {
          permissionId: permission.id,
          resourceType,
          resourceId: resourceId || '',
        },
      },
      update: {
        conditions: conditions as any,
      },
      create: {
        permissionId: permission.id,
        resourceType,
        resourceId,
        conditions: conditions as any,
      },
    });

    return true;
  } catch (error) {
    console.error('Error granting resource permission:', error);
    return false;
  }
}

/**
 * Revoke a resource permission
 */
export async function revokeResourcePermission(
  permissionName: string,
  resourceType: ResourceType,
  resourceId: string
): Promise<boolean> {
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });

    if (!permission) {
      return false;
    }

    // Delete the resource permission
    await db.resourcePermission.deleteMany({
      where: {
        permissionId: permission.id,
        resourceType,
        resourceId,
      },
    });

    return true;
  } catch (error) {
    console.error('Error revoking resource permission:', error);
    return false;
  }
}
