/**
 * Team-Based Access Control
 * 
 * This module provides utilities for team-level permissions.
 */

import { db } from '@/lib/db';
import { PermissionContext, PermissionCheckResult, AccessLevel } from './types';

/**
 * Check if a user has permission through team membership
 */
export async function checkTeamPermission(
  permissionName: string,
  context: PermissionContext
): Promise<PermissionCheckResult> {
  const { userId, resourceType, resourceId } = context;
  
  try {
    // Get all teams the user is a member of
    const teamMemberships = await db.teamMember.findMany({
      where: {
        userId,
      },
      include: {
        team: true,
        teamRole: true,
      },
    });
    
    if (!teamMemberships.length) {
      return { granted: false };
    }
    
    // Check if any team has the required permission
    for (const membership of teamMemberships) {
      // Check team role permissions first (new RBAC system)
      if (membership.teamRole) {
        const rolePermissions = membership.teamRole.permissions as Record<string, any>;
        if (rolePermissions && rolePermissions[permissionName]) {
          return {
            granted: true,
            source: 'team',
          };
        }
      }
      
      // Check legacy team permissions (for backward compatibility)
      const teamPermissions = membership.team.permissions as Record<string, any>;
      if (teamPermissions && teamPermissions[permissionName]) {
        return {
          granted: true,
          source: 'team',
        };
      }
      
      // If we're checking for a specific resource, check resource scopes
      if (resourceType && resourceId) {
        const hasResourceAccess = await checkTeamResourceAccess(
          membership.teamId,
          permissionName,
          resourceType,
          resourceId
        );
        
        if (hasResourceAccess) {
          return {
            granted: true,
            source: 'team',
          };
        }
      }
    }
    
    return { granted: false };
  } catch (error) {
    console.error('Error checking team permission:', error);
    return { granted: false };
  }
}

/**
 * Check if a team has access to a specific resource
 */
async function checkTeamResourceAccess(
  teamId: string,
  permissionName: string,
  resourceType: string,
  resourceId: string
): Promise<boolean> {
  try {
    // Get the resource scope for this team and resource
    const resourceScope = await db.resourceScope.findUnique({
      where: {
        teamId_resourceType_resourceId: {
          teamId,
          resourceType,
          resourceId: resourceId || '',
        },
      },
    });
    
    if (!resourceScope) {
      return false;
    }
    
    // Check if the access level is sufficient for the permission
    return hasAccessLevelForPermission(resourceScope.accessLevel, permissionName);
  } catch (error) {
    console.error('Error checking team resource access:', error);
    return false;
  }
}

/**
 * Check if an access level is sufficient for a permission
 */
function hasAccessLevelForPermission(accessLevel: AccessLevel, permissionName: string): boolean {
  // If access level is NONE, no permissions are granted
  if (accessLevel === 'NONE') {
    return false;
  }
  
  // If access level is ADMIN, all permissions are granted
  if (accessLevel === 'ADMIN') {
    return true;
  }
  
  // For READ access level, only read permissions are granted
  if (accessLevel === 'READ') {
    return permissionName.startsWith('read:') || permissionName.startsWith('view:');
  }
  
  // For WRITE access level, read and write permissions are granted
  if (accessLevel === 'WRITE') {
    return (
      permissionName.startsWith('read:') ||
      permissionName.startsWith('view:') ||
      permissionName.startsWith('create:') ||
      permissionName.startsWith('update:') ||
      permissionName.startsWith('list:') ||
      permissionName.startsWith('unlist:')
    );
  }
  
  return false;
}

/**
 * Grant a team permission
 */
export async function grantTeamPermission(
  teamId: string,
  permissionName: string
): Promise<boolean> {
  try {
    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });
    
    if (!team) {
      return false;
    }
    
    // Update the team permissions
    const permissions = (team.permissions as Record<string, any>) || {};
    permissions[permissionName] = true;
    
    await db.team.update({
      where: { id: teamId },
      data: {
        permissions: permissions as any,
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error granting team permission:', error);
    return false;
  }
}

/**
 * Revoke a team permission
 */
export async function revokeTeamPermission(
  teamId: string,
  permissionName: string
): Promise<boolean> {
  try {
    // Get the team
    const team = await db.team.findUnique({
      where: { id: teamId },
    });
    
    if (!team) {
      return false;
    }
    
    // Update the team permissions
    const permissions = (team.permissions as Record<string, any>) || {};
    delete permissions[permissionName];
    
    await db.team.update({
      where: { id: teamId },
      data: {
        permissions: permissions as any,
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error revoking team permission:', error);
    return false;
  }
}

/**
 * Grant a team resource access
 */
export async function grantTeamResourceAccess(
  teamId: string,
  resourceType: string,
  resourceId: string,
  accessLevel: AccessLevel
): Promise<boolean> {
  try {
    // Create or update the resource scope
    await db.resourceScope.upsert({
      where: {
        teamId_resourceType_resourceId: {
          teamId,
          resourceType,
          resourceId: resourceId || '',
        },
      },
      update: {
        accessLevel,
      },
      create: {
        teamId,
        resourceType,
        resourceId,
        accessLevel,
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error granting team resource access:', error);
    return false;
  }
}

/**
 * Revoke a team resource access
 */
export async function revokeTeamResourceAccess(
  teamId: string,
  resourceType: string,
  resourceId: string
): Promise<boolean> {
  try {
    // Delete the resource scope
    await db.resourceScope.deleteMany({
      where: {
        teamId,
        resourceType,
        resourceId,
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error revoking team resource access:', error);
    return false;
  }
}
