/**
 * RBAC Types
 */

import { 
  UserRole as PrismaUserRole,
  TeamRoleType,
  AccessLevel,
  RequestStatus
} from '@prisma/client';

/**
 * Legacy user roles (for backward compatibility)
 */
export enum UserRole {
  ADMIN = "ADMIN",
  ORGANIZATION_ADMIN = "ORGANIZATION_ADMIN",
  USER = "USER",
}

/**
 * Permission categories
 */
export enum PermissionCategory {
  ORGANIZATION = "organization",
  USER_MANAGEMENT = "user_management",
  CARBON_CREDIT = "carbon_credit",
  WALLET = "wallet",
  ADMIN = "admin",
  TEAM = "team",
  COMPLIANCE = "compliance",
  BILLING = "billing",
  MARKETPLACE = "marketplace",
  REPORTING = "reporting",
  SETTINGS = "settings",
}

/**
 * Resource types for resource-level permissions
 */
export enum ResourceType {
  ORGANIZATION = "organization",
  USER = "user",
  CARBON_CREDIT = "carbon_credit",
  WALLET = "wallet",
  TEAM = "team",
  DOCUMENT = "document",
  TRANSACTION = "transaction",
  ORDER = "order",
  SUBSCRIPTION = "subscription",
  PAYMENT_METHOD = "payment_method",
  DEPARTMENT = "department",
  DIVISION = "division",
}

/**
 * Permission interface
 */
export interface IPermission {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  category: PermissionCategory;
}

/**
 * Custom role interface
 */
export interface ICustomRole {
  id: string;
  name: string;
  description?: string;
  isSystemRole: boolean;
  parentRoleId?: string;
  organizationId?: string;
  permissions: string[]; // Permission IDs
}

/**
 * Resource permission interface
 */
export interface IResourcePermission {
  permissionId: string;
  resourceType: ResourceType;
  resourceId?: string;
  conditions?: Record<string, any>;
}

/**
 * Team role interface
 */
export interface ITeamRole {
  id: string;
  name: string;
  description?: string;
  teamId: string;
  permissions: Record<string, any>; // Team-specific permissions
}

/**
 * Permission grant interface
 */
export interface IPermissionGrant {
  userId: string;
  permissionId: string;
  expiresAt?: Date;
  grantedBy?: string;
  conditions?: Record<string, any>;
  scope?: Record<string, any>;
}

/**
 * Temporary permission interface
 */
export interface ITemporaryPermission {
  userId: string;
  permissionId: string;
  expiresAt: Date;
  reason: string;
  grantedBy: string;
  resourceType?: ResourceType;
  resourceId?: string;
  conditions?: Record<string, any>;
}

/**
 * Permission request interface
 */
export interface IPermissionRequest {
  userId: string;
  permissionId: string;
  status: RequestStatus;
  reason: string;
  resourceType?: ResourceType;
  resourceId?: string;
  duration?: number; // Hours
  approverId?: string;
  approvalNotes?: string;
}

/**
 * Permission check context
 */
export interface PermissionContext {
  userId: string;
  organizationId?: string;
  resourceType?: ResourceType;
  resourceId?: string;
  teamId?: string;
  metadata?: Record<string, any>;
}

/**
 * Permission check result
 */
export interface PermissionCheckResult {
  granted: boolean;
  source?: 'role' | 'direct_grant' | 'temporary' | 'team' | 'resource';
  expiresAt?: Date;
  conditions?: Record<string, any>;
}

/**
 * Organization RBAC settings interface
 */
export interface IOrganizationRbacSettings {
  organizationId: string;
  enableCustomRoles: boolean;
  enableResourcePermissions: boolean;
  enableRoleHierarchy: boolean;
  enableTemporaryAccess: boolean;
  enablePermissionRequests: boolean;
  permissionRequestExpiry: number;
  temporaryAccessMaxDuration: number;
  requireApprovalForRoles: boolean;
  requireApprovalForPermissions: boolean;
}

/**
 * Department interface
 */
export interface IDepartment {
  id: string;
  name: string;
  description?: string;
  code?: string;
  parentId?: string;
  organizationId: string;
}

/**
 * Division interface
 */
export interface IDivision {
  id: string;
  name: string;
  description?: string;
  code?: string;
  organizationId: string;
  departmentId?: string;
}
