/**
 * Role Service
 * 
 * This module provides services for managing roles.
 */

import { db } from '@/lib/db';
import { SYSTEM_ROLES, PREDEFINED_CUSTOM_ROLES } from './roles';
import { PermissionContext, PermissionCheckResult } from './types';
import { logger } from '@/lib/logger';

/**
 * Initialize system roles in the database
 */
export async function initializeRoles(): Promise<void> {
  try {
    // Check if roles already exist
    const existingRoles = await db.customRole.count({
      where: {
        isSystemRole: true,
      },
    });
    
    if (existingRoles > 0) {
      logger.info(`System roles already initialized (${existingRoles} found)`);
      return;
    }
    
    // Get all permissions
    const permissions = await db.permission.findMany();
    const permissionMap = new Map(permissions.map(p => [p.name, p.id]));
    
    // Create system roles
    for (const [roleName, roleData] of Object.entries(SYSTEM_ROLES)) {
      // Create the role
      const role = await db.customRole.create({
        data: {
          name: roleData.name,
          description: roleData.description,
          isSystemRole: true,
          parentRoleId: roleData.parentRole ? SYSTEM_ROLES[roleData.parentRole].name : null,
        },
      });
      
      // Add permissions to the role
      for (const permissionName of roleData.permissions) {
        const permissionId = permissionMap.get(permissionName);
        if (permissionId) {
          await db.rolePermission.create({
            data: {
              roleId: role.id,
              permissionId,
            },
          });
        }
      }
    }
    
    logger.info(`Initialized ${Object.keys(SYSTEM_ROLES).length} system roles`);
  } catch (error) {
    logger.error('Error initializing roles:', error);
    throw error;
  }
}

/**
 * Create a custom role
 */
export async function createCustomRole(
  name: string,
  description: string,
  organizationId: string,
  permissions: string[],
  parentRoleId?: string
): Promise<string | null> {
  try {
    // Check if a role with this name already exists in the organization
    const existingRole = await db.customRole.findFirst({
      where: {
        name,
        organizationId,
      },
    });
    
    if (existingRole) {
      logger.warn(`Role ${name} already exists in organization ${organizationId}`);
      return null;
    }
    
    // Create the role
    const role = await db.customRole.create({
      data: {
        name,
        description,
        isSystemRole: false,
        parentRoleId,
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });
    
    // Add permissions to the role
    for (const permissionName of permissions) {
      const permission = await db.permission.findUnique({
        where: { name: permissionName },
      });
      
      if (permission) {
        await db.rolePermission.create({
          data: {
            roleId: role.id,
            permissionId: permission.id,
          },
        });
      }
    }
    
    // Log the role creation
    await db.auditLog.create({
      data: {
        type: 'ROLE_CREATED',
        description: `Custom role ${name} created for organization ${organizationId}`,
        organizationId,
        metadata: {
          roleName: name,
          permissions,
          parentRoleId,
        } as any,
      },
    });
    
    return role.id;
  } catch (error) {
    logger.error('Error creating custom role:', error);
    return null;
  }
}

/**
 * Update a custom role
 */
export async function updateCustomRole(
  roleId: string,
  name?: string,
  description?: string,
  permissions?: string[],
  parentRoleId?: string
): Promise<boolean> {
  try {
    // Get the role
    const role = await db.customRole.findUnique({
      where: { id: roleId },
    });
    
    if (!role) {
      logger.warn(`Role not found: ${roleId}`);
      return false;
    }
    
    // Check if this is a system role
    if (role.isSystemRole) {
      logger.warn(`Cannot update system role: ${roleId}`);
      return false;
    }
    
    // Update the role
    await db.customRole.update({
      where: { id: roleId },
      data: {
        name: name || undefined,
        description: description || undefined,
        parentRoleId: parentRoleId === null ? null : parentRoleId || undefined,
      },
    });
    
    // Update permissions if provided
    if (permissions) {
      // Remove existing permissions
      await db.rolePermission.deleteMany({
        where: { roleId },
      });
      
      // Add new permissions
      for (const permissionName of permissions) {
        const permission = await db.permission.findUnique({
          where: { name: permissionName },
        });
        
        if (permission) {
          await db.rolePermission.create({
            data: {
              roleId,
              permissionId: permission.id,
            },
          });
        }
      }
    }
    
    // Log the role update
    await db.auditLog.create({
      data: {
        type: 'ROLE_UPDATED',
        description: `Custom role ${role.name} updated`,
        organizationId: role.organizationId || undefined,
        metadata: {
          roleId,
          name,
          description,
          permissions,
          parentRoleId,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error updating custom role:', error);
    return false;
  }
}

/**
 * Delete a custom role
 */
export async function deleteCustomRole(roleId: string): Promise<boolean> {
  try {
    // Get the role
    const role = await db.customRole.findUnique({
      where: { id: roleId },
    });
    
    if (!role) {
      logger.warn(`Role not found: ${roleId}`);
      return false;
    }
    
    // Check if this is a system role
    if (role.isSystemRole) {
      logger.warn(`Cannot delete system role: ${roleId}`);
      return false;
    }
    
    // Delete role permissions
    await db.rolePermission.deleteMany({
      where: { roleId },
    });
    
    // Delete user role assignments
    await db.userCustomRole.deleteMany({
      where: { roleId },
    });
    
    // Delete the role
    await db.customRole.delete({
      where: { id: roleId },
    });
    
    // Log the role deletion
    await db.auditLog.create({
      data: {
        type: 'ROLE_DELETED',
        description: `Custom role ${role.name} deleted`,
        organizationId: role.organizationId || undefined,
        metadata: {
          roleId,
          roleName: role.name,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error deleting custom role:', error);
    return false;
  }
}

/**
 * Assign a role to a user
 */
export async function assignRoleToUser(
  userId: string,
  roleId: string,
  grantedBy?: string,
  expiresAt?: Date,
  scope?: Record<string, any>
): Promise<boolean> {
  try {
    // Check if the user already has this role
    const existingRole = await db.userCustomRole.findUnique({
      where: {
        userId_roleId: {
          userId,
          roleId,
        },
      },
    });
    
    if (existingRole) {
      // Update the existing role assignment
      await db.userCustomRole.update({
        where: {
          id: existingRole.id,
        },
        data: {
          expiresAt,
          grantedBy,
          scope: scope as any,
        },
      });
    } else {
      // Create a new role assignment
      await db.userCustomRole.create({
        data: {
          userId,
          roleId,
          expiresAt,
          grantedBy,
          scope: scope as any,
        },
      });
    }
    
    // Get the role name for logging
    const role = await db.customRole.findUnique({
      where: { id: roleId },
      select: { name: true },
    });
    
    // Log the role assignment
    await db.auditLog.create({
      data: {
        type: 'ROLE_ASSIGNED',
        description: `Role ${role?.name || roleId} assigned to user ${userId}`,
        userId: grantedBy,
        metadata: {
          roleId,
          roleName: role?.name,
          userId,
          expiresAt,
          scope,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error assigning role to user:', error);
    return false;
  }
}

/**
 * Remove a role from a user
 */
export async function removeRoleFromUser(
  userId: string,
  roleId: string,
  removedBy?: string
): Promise<boolean> {
  try {
    // Delete the role assignment
    await db.userCustomRole.deleteMany({
      where: {
        userId,
        roleId,
      },
    });
    
    // Get the role name for logging
    const role = await db.customRole.findUnique({
      where: { id: roleId },
      select: { name: true },
    });
    
    // Log the role removal
    await db.auditLog.create({
      data: {
        type: 'ROLE_REMOVED',
        description: `Role ${role?.name || roleId} removed from user ${userId}`,
        userId: removedBy,
        metadata: {
          roleId,
          roleName: role?.name,
          userId,
        } as any,
      },
    });
    
    return true;
  } catch (error) {
    logger.error('Error removing role from user:', error);
    return false;
  }
}

/**
 * Check if a user has a role
 */
export async function checkUserRole(
  userId: string,
  roleName: string
): Promise<boolean> {
  try {
    // Check if this is a system role
    if (roleName === 'ADMIN' || roleName === 'ORGANIZATION_ADMIN' || roleName === 'USER') {
      // Check the user's system role
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true },
      });
      
      return user?.role === roleName;
    }
    
    // Check if the user has this custom role
    const role = await db.customRole.findFirst({
      where: { name: roleName },
    });
    
    if (!role) {
      return false;
    }
    
    const userRole = await db.userCustomRole.findFirst({
      where: {
        userId,
        roleId: role.id,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
    });
    
    return !!userRole;
  } catch (error) {
    logger.error('Error checking user role:', error);
    return false;
  }
}

/**
 * Check if a user has permission through a role
 */
export async function checkRolePermission(
  permissionName: string,
  context: PermissionContext
): Promise<PermissionCheckResult> {
  const { userId } = context;
  
  try {
    // Get the permission
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });
    
    if (!permission) {
      return { granted: false };
    }
    
    // Check system roles first
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });
    
    if (!user) {
      return { granted: false };
    }
    
    // Check if the system role has this permission
    const systemRole = SYSTEM_ROLES[user.role];
    if (systemRole && systemRole.permissions.includes(permissionName)) {
      return {
        granted: true,
        source: 'role',
      };
    }
    
    // Check custom roles
    const userRoles = await db.userCustomRole.findMany({
      where: {
        userId,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });
    
    for (const userRole of userRoles) {
      // Check if this role has the permission
      const hasPermission = userRole.role.permissions.some(
        rp => rp.permission.name === permissionName
      );
      
      if (hasPermission) {
        // Check if there are conditions and evaluate them
        if (userRole.scope) {
          const scope = userRole.scope as Record<string, any>;
          const scopeAllows = evaluateRoleScope(scope, context);
          
          if (!scopeAllows) {
            continue;
          }
        }
        
        return {
          granted: true,
          source: 'role',
          expiresAt: userRole.expiresAt || undefined,
        };
      }
      
      // Check parent roles if role hierarchy is enabled
      if (userRole.role.parentRoleId) {
        const hasPermissionThroughParent = await checkPermissionThroughParentRoles(
          userRole.role.parentRoleId,
          permissionName
        );
        
        if (hasPermissionThroughParent) {
          return {
            granted: true,
            source: 'role',
            expiresAt: userRole.expiresAt || undefined,
          };
        }
      }
    }
    
    return { granted: false };
  } catch (error) {
    logger.error('Error checking role permission:', error);
    return { granted: false };
  }
}

/**
 * Check if a role has a permission through its parent roles
 */
async function checkPermissionThroughParentRoles(
  roleId: string,
  permissionName: string
): Promise<boolean> {
  try {
    // Get the role
    const role = await db.customRole.findUnique({
      where: { id: roleId },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });
    
    if (!role) {
      return false;
    }
    
    // Check if this role has the permission
    const hasPermission = role.permissions.some(
      rp => rp.permission.name === permissionName
    );
    
    if (hasPermission) {
      return true;
    }
    
    // Check parent role if it exists
    if (role.parentRoleId) {
      return checkPermissionThroughParentRoles(role.parentRoleId, permissionName);
    }
    
    return false;
  } catch (error) {
    logger.error('Error checking permission through parent roles:', error);
    return false;
  }
}

/**
 * Evaluate role scope
 */
function evaluateRoleScope(
  scope: Record<string, any>,
  context: PermissionContext
): boolean {
  // Implement scope evaluation logic here
  // This is a simple example - in a real implementation, you would have more complex scope rules
  
  // Check resource type scope
  if (scope.resourceTypes && context.resourceType) {
    if (!scope.resourceTypes.includes(context.resourceType)) {
      return false;
    }
  }
  
  // Check resource ID scope
  if (scope.resourceIds && context.resourceId) {
    if (!scope.resourceIds.includes(context.resourceId)) {
      return false;
    }
  }
  
  // Check team scope
  if (scope.teamIds && context.teamId) {
    if (!scope.teamIds.includes(context.teamId)) {
      return false;
    }
  }
  
  // Check department scope
  if (scope.departmentIds) {
    // Get the user's department
    const user = db.user.findUnique({
      where: { id: context.userId },
      select: { departmentId: true },
    });
    
    if (!user || !user.departmentId || !scope.departmentIds.includes(user.departmentId)) {
      return false;
    }
  }
  
  // Check division scope
  if (scope.divisionIds) {
    // Get the user's division
    const user = db.user.findUnique({
      where: { id: context.userId },
      select: { divisionId: true },
    });
    
    if (!user || !user.divisionId || !scope.divisionIds.includes(user.divisionId)) {
      return false;
    }
  }
  
  return true;
}
