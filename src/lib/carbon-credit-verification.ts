/**
 * Carbon Credit Verification Module
 * 
 * This module provides functionality for verifying carbon credits,
 * including validation of project details, documentation, and standards compliance.
 */

import { db } from "./db";
import { logger } from "./logger";
import { notificationService } from "./notifications";
import { auditService } from "./audit";
import { VerificationStatus, AuditLogType, CarbonCreditStatus } from "@prisma/client";

/**
 * Verification requirement level
 */
export enum VerificationRequirementLevel {
  REQUIRED = "REQUIRED",
  RECOMMENDED = "RECOMMENDED",
  OPTIONAL = "OPTIONAL",
}

/**
 * Verification requirement
 */
export interface VerificationRequirement {
  id: string;
  name: string;
  description: string;
  level: VerificationRequirementLevel;
  documentTypes: string[];
  standardSpecific?: string[];
  methodologySpecific?: string[];
}

/**
 * Standard verification requirements
 */
export const VERIFICATION_REQUIREMENTS: VerificationRequirement[] = [
  {
    id: "project_documentation",
    name: "Project Documentation",
    description: "Official project documentation including project design document (PDD)",
    level: VerificationRequirementLevel.REQUIRED,
    documentTypes: ["PROJECT_DESCRIPTION"],
  },
  {
    id: "methodology_documentation",
    name: "Methodology Documentation",
    description: "Documentation of the methodology used for carbon credit calculation",
    level: VerificationRequirementLevel.REQUIRED,
    documentTypes: ["METHODOLOGY"],
  },
  {
    id: "verification_report",
    name: "Verification Report",
    description: "Third-party verification report",
    level: VerificationRequirementLevel.REQUIRED,
    documentTypes: ["VERIFICATION_REPORT"],
  },
  {
    id: "validation_report",
    name: "Validation Report",
    description: "Third-party validation report",
    level: VerificationRequirementLevel.REQUIRED,
    documentTypes: ["VALIDATION_REPORT"],
  },
  {
    id: "monitoring_report",
    name: "Monitoring Report",
    description: "Monitoring report for the project",
    level: VerificationRequirementLevel.REQUIRED,
    documentTypes: ["MONITORING_REPORT"],
  },
  {
    id: "certificate",
    name: "Carbon Credit Certificate",
    description: "Official certificate for the carbon credits",
    level: VerificationRequirementLevel.REQUIRED,
    documentTypes: ["CERTIFICATE"],
  },
  {
    id: "additionality_proof",
    name: "Additionality Proof",
    description: "Documentation proving the additionality of the project",
    level: VerificationRequirementLevel.RECOMMENDED,
    documentTypes: ["OTHER"],
  },
  {
    id: "community_impact",
    name: "Community Impact Assessment",
    description: "Assessment of the project's impact on local communities",
    level: VerificationRequirementLevel.RECOMMENDED,
    documentTypes: ["OTHER"],
  },
  {
    id: "biodiversity_impact",
    name: "Biodiversity Impact Assessment",
    description: "Assessment of the project's impact on biodiversity",
    level: VerificationRequirementLevel.RECOMMENDED,
    documentTypes: ["OTHER"],
  },
];

/**
 * Verification result
 */
export interface VerificationResult {
  isValid: boolean;
  status: VerificationStatus;
  missingRequirements: VerificationRequirement[];
  recommendedRequirements: VerificationRequirement[];
  message: string;
}

/**
 * Carbon Credit Verification Service
 */
export class CarbonCreditVerificationService {
  /**
   * Check if a carbon credit meets all verification requirements
   * @param carbonCreditId Carbon credit ID
   * @returns Verification result
   */
  static async checkVerificationRequirements(carbonCreditId: string): Promise<VerificationResult> {
    try {
      logger.info(`Checking verification requirements for carbon credit ${carbonCreditId}`);

      // Get the carbon credit with its documents
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          documents: true,
        },
      });

      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${carbonCreditId}`);
      }

      // Get the document types
      const documentTypes = carbonCredit.documents.map(doc => doc.type);

      // Check required requirements
      const missingRequirements = VERIFICATION_REQUIREMENTS.filter(req => {
        if (req.level === VerificationRequirementLevel.REQUIRED) {
          // Check if any of the required document types are present
          return !req.documentTypes.some(type => documentTypes.includes(type));
        }
        return false;
      });

      // Check recommended requirements
      const recommendedRequirements = VERIFICATION_REQUIREMENTS.filter(req => {
        if (req.level === VerificationRequirementLevel.RECOMMENDED) {
          // Check if any of the recommended document types are present
          return !req.documentTypes.some(type => documentTypes.includes(type));
        }
        return false;
      });

      // Determine verification status
      let status = VerificationStatus.PENDING;
      let message = "";

      if (missingRequirements.length === 0) {
        status = VerificationStatus.VERIFIED;
        message = "All required verification requirements are met.";
      } else {
        status = VerificationStatus.REJECTED;
        message = `Missing ${missingRequirements.length} required verification requirements.`;
      }

      return {
        isValid: missingRequirements.length === 0,
        status,
        missingRequirements,
        recommendedRequirements,
        message,
      };
    } catch (error) {
      logger.error(`Error checking verification requirements for carbon credit ${carbonCreditId}:`, error);
      throw error;
    }
  }

  /**
   * Verify a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param verifierId User ID of the verifier
   * @param notes Verification notes
   * @returns Updated carbon credit
   */
  static async verifyCarbonCredit(carbonCreditId: string, verifierId: string, notes?: string) {
    try {
      logger.info(`Verifying carbon credit ${carbonCreditId} by user ${verifierId}`);

      // Check verification requirements
      const verificationResult = await this.checkVerificationRequirements(carbonCreditId);

      // Get the verifier
      const verifier = await db.user.findUnique({
        where: { id: verifierId },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      });

      if (!verifier) {
        throw new Error(`Verifier not found: ${verifierId}`);
      }

      // Check if the verifier has permission to verify carbon credits
      if (verifier.role !== "ADMIN" && verifier.role !== "ORGANIZATION_ADMIN") {
        throw new Error("You do not have permission to verify carbon credits");
      }

      // Update the carbon credit verification status
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id: carbonCreditId },
        data: {
          verificationStatus: verificationResult.status,
          // If verified, update the status to VERIFIED
          ...(verificationResult.status === VerificationStatus.VERIFIED && {
            status: CarbonCreditStatus.VERIFIED,
          }),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Create a verification record
      await db.carbonCreditVerification.create({
        data: {
          status: verificationResult.status,
          verifier: verifier.name,
          verifierEmail: verifier.email,
          notes: notes || verificationResult.message,
          carbonCredit: {
            connect: { id: carbonCreditId },
          },
          metadata: {
            missingRequirements: verificationResult.missingRequirements.map(req => req.id),
            recommendedRequirements: verificationResult.recommendedRequirements.map(req => req.id),
          },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: AuditLogType.CARBON_CREDIT_VERIFIED,
          description: `Carbon credit ${updatedCarbonCredit.name} verified with status ${verificationResult.status}`,
          user: {
            connect: { id: verifierId },
          },
          organization: updatedCarbonCredit.organizationId
            ? { connect: { id: updatedCarbonCredit.organizationId } }
            : undefined,
          metadata: {
            carbonCreditId,
            verificationStatus: verificationResult.status,
            notes: notes || verificationResult.message,
          },
        },
      });

      // Send notification to the carbon credit owner
      await notificationService.createNotification(
        updatedCarbonCredit.user.id,
        "Carbon Credit Verification Update",
        `Your carbon credit ${updatedCarbonCredit.name} has been ${
          verificationResult.status === VerificationStatus.VERIFIED ? "verified" : "rejected"
        }.`,
        "VERIFICATION",
        ["IN_APP", "EMAIL"],
        {
          carbonCreditId,
          verificationStatus: verificationResult.status,
          message: verificationResult.message,
        }
      );

      return updatedCarbonCredit;
    } catch (error) {
      logger.error(`Error verifying carbon credit ${carbonCreditId}:`, error);
      throw error;
    }
  }

  /**
   * Get verification history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @returns Verification history
   */
  static async getVerificationHistory(carbonCreditId: string) {
    try {
      const verificationHistory = await db.carbonCreditVerification.findMany({
        where: {
          carbonCreditId,
        },
        orderBy: {
          timestamp: "desc",
        },
      });

      return verificationHistory;
    } catch (error) {
      logger.error(`Error getting verification history for carbon credit ${carbonCreditId}:`, error);
      throw error;
    }
  }
}
