# Storage System

This module provides a unified interface for file storage operations in the Carbonix platform. It's designed to be extensible, allowing for different storage providers (local file system, cloud storage, etc.) to be used.

## Features

- **Provider-agnostic API**: Use the same API regardless of the underlying storage provider
- **Local file system provider**: Store files on the local file system
- **Extensible architecture**: Easily add new storage providers (S3, Azure, etc.)
- **File metadata**: Store and retrieve metadata for files
- **URL generation**: Generate URLs for accessing files
- **Unique file names**: Generate unique file names to prevent collisions

## Usage

### Basic Usage

```typescript
import { uploadFile, deleteFile, getFileUrl } from '@/lib/storage';

// Upload a file
const uploadResult = await uploadFile(
  fileBuffer,
  'path/to/file.pdf',
  'application/pdf',
  { userId: '123', documentType: 'BUSINESS_REGISTRATION' }
);

if (uploadResult.success) {
  console.log(`File uploaded successfully: ${uploadResult.url}`);
} else {
  console.error(`Failed to upload file: ${uploadResult.error}`);
}

// Delete a file
const deleteResult = await deleteFile('path/to/file.pdf');

if (deleteResult.success) {
  console.log('File deleted successfully');
} else {
  console.error(`Failed to delete file: ${deleteResult.error}`);
}

// Get a URL for a file
const url = getFileUrl('path/to/file.pdf');
```

### Advanced Usage

```typescript
import { 
  uploadFile, 
  deleteFile, 
  getFileUrl, 
  fileExists, 
  getFileMetadata, 
  generateUniqueFileName 
} from '@/lib/storage';

// Generate a unique file name
const fileName = generateUniqueFileName('original-file.pdf', 'user-123');

// Check if a file exists
const exists = await fileExists('path/to/file.pdf');

// Get file metadata
const metadata = await getFileMetadata('path/to/file.pdf');
if (metadata) {
  console.log(`File size: ${metadata.size} bytes`);
  console.log(`Content type: ${metadata.contentType}`);
  console.log(`Created at: ${metadata.createdAt}`);
}
```

## Configuration

The storage system can be configured using environment variables:

- `STORAGE_PROVIDER`: The storage provider to use (default: `local`)
- `STORAGE_BASE_URL`: The base URL for accessing files (default: `http://localhost:3000`)
- `STORAGE_ROOT_DIR`: The root directory for local file storage (default: `public/uploads`)
- `STORAGE_USE_RELATIVE_URLS`: Whether to use relative URLs (default: `false`)

## Adding a New Storage Provider

To add a new storage provider, create a new file in the `storage` directory that implements the `StorageProvider` interface:

```typescript
import { StorageProvider, UploadResult, DeleteResult, FileMetadata } from './types';

export class MyStorageProvider implements StorageProvider {
  async uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    contentType: string,
    metadata?: Record<string, any>
  ): Promise<UploadResult> {
    // Implementation
  }

  async deleteFile(fileName: string): Promise<DeleteResult> {
    // Implementation
  }

  getFileUrl(fileName: string, options?: { signed?: boolean; expiresIn?: number }): string {
    // Implementation
  }

  async fileExists(fileName: string): Promise<boolean> {
    // Implementation
  }

  async getFileMetadata(fileName: string): Promise<FileMetadata | null> {
    // Implementation
  }
}
```

Then, update the `createStorageProvider` function in `index.ts` to use your new provider:

```typescript
function createStorageProvider(): StorageProvider {
  const providerType = process.env.STORAGE_PROVIDER || 'local';
  
  switch (providerType.toLowerCase()) {
    case 'local':
      return new LocalStorageProvider(defaultConfig);
    
    case 'my-provider':
      return new MyStorageProvider(config);
    
    default:
      return new LocalStorageProvider(defaultConfig);
  }
}
```

## Migration from Legacy Storage API

If you're migrating from the legacy storage API, you can use the compatibility layer in `@/lib/storage.ts`:

```typescript
// Old code
import { uploadFile, deleteFile, getFileUrl } from '@/lib/storage';

// New code
import { uploadFile, deleteFile, getFileUrl } from '@/lib/storage/index';
```

The compatibility layer will log a warning when used, encouraging migration to the new API.
