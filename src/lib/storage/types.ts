/**
 * Storage System Types
 * 
 * This file defines the types and interfaces for the storage system.
 * The storage system is designed to be extensible, allowing for different
 * storage providers (local file system, cloud storage, etc.) to be used.
 */

/**
 * File metadata
 */
export interface FileMetadata {
  /** Original file name */
  originalName: string;
  /** Content type (MIME type) */
  contentType: string;
  /** File size in bytes */
  size: number;
  /** Creation timestamp */
  createdAt: Date;
  /** Last modified timestamp */
  updatedAt: Date;
}

/**
 * Upload result
 */
export interface UploadResult {
  /** Whether the upload was successful */
  success: boolean;
  /** URL to access the file */
  url: string;
  /** File path within the storage system */
  path: string;
  /** File metadata */
  metadata?: FileMetadata;
  /** Error message if upload failed */
  error?: string;
}

/**
 * Delete result
 */
export interface DeleteResult {
  /** Whether the delete was successful */
  success: boolean;
  /** Error message if delete failed */
  error?: string;
}

/**
 * Storage provider interface
 * 
 * This interface defines the methods that all storage providers must implement.
 */
export interface StorageProvider {
  /**
   * Upload a file to storage
   * @param fileBuffer File buffer
   * @param fileName File name (with path)
   * @param contentType Content type (MIME type)
   * @param metadata Additional metadata
   * @returns Upload result
   */
  uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    contentType: string,
    metadata?: Record<string, any>
  ): Promise<UploadResult>;

  /**
   * Delete a file from storage
   * @param fileName File name (with path)
   * @returns Delete result
   */
  deleteFile(fileName: string): Promise<DeleteResult>;

  /**
   * Get a URL for a file
   * @param fileName File name (with path)
   * @param options Options for URL generation
   * @returns File URL
   */
  getFileUrl(fileName: string, options?: { 
    /** Whether to generate a signed URL with expiration */
    signed?: boolean;
    /** Expiration time in seconds for signed URLs */
    expiresIn?: number;
  }): string;

  /**
   * Check if a file exists
   * @param fileName File name (with path)
   * @returns Whether the file exists
   */
  fileExists(fileName: string): Promise<boolean>;

  /**
   * Get file metadata
   * @param fileName File name (with path)
   * @returns File metadata
   */
  getFileMetadata(fileName: string): Promise<FileMetadata | null>;
}

/**
 * Storage configuration
 */
export interface StorageConfig {
  /** Base URL for accessing files */
  baseUrl: string;
  /** Root directory for local file storage */
  rootDir?: string;
  /** Whether to use relative URLs */
  useRelativeUrls?: boolean;
}
