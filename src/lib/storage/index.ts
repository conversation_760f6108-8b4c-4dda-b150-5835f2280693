/**
 * Storage System
 *
 * This module provides a unified interface for file storage operations.
 * It supports different storage providers and can be configured to use
 * local file system or cloud storage services.
 */

import path from 'path';
import type { StorageProvider, UploadResult, DeleteResult, FileMetadata, StorageConfig } from './types';
import { LocalStorageProvider } from './local-provider';
import { logger } from '@/lib/logger';

// Default storage configuration
const defaultConfig: StorageConfig = {
  baseUrl: process.env.STORAGE_BASE_URL || 'http://localhost:3000',
  rootDir: process.env.STORAGE_ROOT_DIR || path.join(process.cwd(), 'public', 'uploads'),
  useRelativeUrls: process.env.STORAGE_USE_RELATIVE_URLS === 'true',
};

// Create storage provider based on configuration
function createStorageProvider(): StorageProvider {
  const providerType = process.env.STORAGE_PROVIDER || 'local';

  switch (providerType.toLowerCase()) {
    case 'local':
      logger.info('Using local file system storage provider');
      return new LocalStorageProvider(defaultConfig);

    // Add more providers here as needed
    // case 's3':
    //   return new S3StorageProvider(config);

    default:
      logger.info('Using default local file system storage provider');
      return new LocalStorageProvider(defaultConfig);
  }
}

// Create the storage provider instance
const storageProvider = createStorageProvider();

/**
 * Upload a file to storage
 * @param fileBuffer File buffer
 * @param fileName File name (with path)
 * @param contentType Content type (MIME type)
 * @param metadata Additional metadata
 * @returns Upload result
 */
export async function uploadFile(
  fileBuffer: Buffer,
  fileName: string,
  contentType: string,
  metadata?: Record<string, any>
): Promise<UploadResult> {
  return storageProvider.uploadFile(fileBuffer, fileName, contentType, metadata);
}

/**
 * Delete a file from storage
 * @param fileName File name (with path)
 * @returns Delete result
 */
export async function deleteFile(fileName: string): Promise<DeleteResult> {
  return storageProvider.deleteFile(fileName);
}

/**
 * Get a URL for a file
 * @param fileName File name (with path)
 * @param options Options for URL generation
 * @returns File URL
 */
export function getFileUrl(
  fileName: string,
  options?: { signed?: boolean; expiresIn?: number }
): string {
  return storageProvider.getFileUrl(fileName, options);
}

/**
 * Check if a file exists
 * @param fileName File name (with path)
 * @returns Whether the file exists
 */
export async function fileExists(fileName: string): Promise<boolean> {
  return storageProvider.fileExists(fileName);
}

/**
 * Get file metadata
 * @param fileName File name (with path)
 * @returns File metadata
 */
export async function getFileMetadata(fileName: string): Promise<FileMetadata | null> {
  return storageProvider.getFileMetadata(fileName);
}

/**
 * Generate a unique file name
 * @param originalName Original file name
 * @param prefix Optional prefix
 * @returns Unique file name
 */
export function generateUniqueFileName(originalName: string, prefix?: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 9);
  const ext = path.extname(originalName);
  const baseName = path.basename(originalName, ext);

  if (prefix) {
    return `${prefix}/${timestamp}-${random}-${baseName}${ext}`;
  }

  return `${timestamp}-${random}-${baseName}${ext}`;
}

// Export the storage provider for advanced usage
export { storageProvider };

// Export types
export type { StorageProvider, UploadResult, DeleteResult, FileMetadata, StorageConfig } from './types';
