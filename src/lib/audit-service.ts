import { 
  AuditLogType,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';

/**
 * Audit log creation data interface
 */
export interface AuditLogCreationData {
  type: AuditLogType;
  description: string;
  userId?: string;
  organizationId?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Audit service for managing audit logs
 */
export class AuditService {
  /**
   * Create a new audit log
   * @param data Audit log creation data
   * @returns Created audit log
   */
  static async createAuditLog(data: AuditLogCreationData) {
    try {
      // Create the audit log
      const auditLog = await db.auditLog.create({
        data: {
          type: data.type,
          description: data.description,
          ...(data.userId && {
            user: { connect: { id: data.userId } },
          }),
          ...(data.organizationId && {
            organization: { connect: { id: data.organizationId } },
          }),
          metadata: data.metadata as Prisma.JsonObject,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
        },
      });

      logger.info(`Audit log created: ${auditLog.id} - ${data.description}`);

      return auditLog;
    } catch (error) {
      logger.error('Error creating audit log:', error);
      throw new Error('Failed to create audit log');
    }
  }

  /**
   * Get audit logs
   * @param options Query options
   * @returns Audit logs
   */
  static async getAuditLogs(options: {
    userId?: string;
    organizationId?: string;
    type?: AuditLogType;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const { userId, organizationId, type, startDate, endDate, limit = 50, offset = 0 } = options;

      // Build where clause
      const where: Prisma.AuditLogWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(type && { type }),
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
      };

      // Get audit logs
      const auditLogs = await db.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });

      // Get total count
      const totalCount = await db.auditLog.count({ where });

      return {
        auditLogs,
        totalCount,
        hasMore: offset + limit < totalCount,
      };
    } catch (error) {
      logger.error('Error getting audit logs:', error);
      throw new Error('Failed to get audit logs');
    }
  }

  /**
   * Get audit logs for a user
   * @param userId User ID
   * @param options Query options
   * @returns Audit logs
   */
  static async getUserAuditLogs(userId: string, options: {
    type?: AuditLogType;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}) {
    return this.getAuditLogs({
      userId,
      ...options,
    });
  }

  /**
   * Get audit logs for an organization
   * @param organizationId Organization ID
   * @param options Query options
   * @returns Audit logs
   */
  static async getOrganizationAuditLogs(organizationId: string, options: {
    type?: AuditLogType;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}) {
    return this.getAuditLogs({
      organizationId,
      ...options,
    });
  }

  /**
   * Get audit log types
   * @returns Audit log types
   */
  static getAuditLogTypes() {
    return Object.values(AuditLogType);
  }

  /**
   * Get audit log type description
   * @param type Audit log type
   * @returns Audit log type description
   */
  static getAuditLogTypeDescription(type: AuditLogType) {
    const descriptions: Record<AuditLogType, string> = {
      USER_CREATED: 'User created',
      USER_UPDATED: 'User updated',
      USER_DELETED: 'User deleted',
      ORGANIZATION_CREATED: 'Organization created',
      ORGANIZATION_UPDATED: 'Organization updated',
      ORGANIZATION_DELETED: 'Organization deleted',
      CARBON_CREDIT_CREATED: 'Carbon credit created',
      CARBON_CREDIT_UPDATED: 'Carbon credit updated',
      CARBON_CREDIT_DELETED: 'Carbon credit deleted',
      ORDER_CREATED: 'Order created',
      ORDER_UPDATED: 'Order updated',
      ORDER_DELETED: 'Order deleted',
      TRANSACTION_CREATED: 'Transaction created',
      TRANSACTION_UPDATED: 'Transaction updated',
      WALLET_CREATED: 'Wallet created',
      WALLET_UPDATED: 'Wallet updated',
      SUBSCRIPTION_CREATED: 'Subscription created',
      SUBSCRIPTION_UPDATED: 'Subscription updated',
      LOGIN_SUCCESS: 'Login successful',
      LOGIN_FAILED: 'Login failed',
      PASSWORD_RESET: 'Password reset',
      EMAIL_VERIFIED: 'Email verified',
      TEAM_CREATED: 'Team created',
      TEAM_UPDATED: 'Team updated',
      TEAM_DELETED: 'Team deleted',
      INVITATION_SENT: 'Invitation sent',
      INVITATION_ACCEPTED: 'Invitation accepted',
      DOCUMENT_UPLOADED: 'Document uploaded',
      DOCUMENT_VERIFIED: 'Document verified',
      PAYMENT_PROCESSED: 'Payment processed',
      BILLING_CREATED: 'Billing record created',
    };

    return descriptions[type] || type;
  }
}
