import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { auditService } from "@/lib/audit";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { OrderType, OrderStatus, TransactionStatus, TransactionType, AuditLogType } from "@prisma/client";
import { ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Order matching engine for carbon credit trading
 */
export class OrderMatchingEngine {
  /**
   * Match buy and sell orders for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param matchingStrategy Optional matching strategy ("PRICE_TIME", "PRO_RATA", "FIFO", or "VOLUME_WEIGHTED")
   * @returns Matched orders
   */
  static async matchOrders(
    carbonCreditId: string,
    matchingStrategy: "PRICE_TIME" | "PRO_RATA" | "FIFO" | "VOLUME_WEIGHTED" = "PRICE_TIME"
  ) {
    try {
      logger.info(`Matching orders for carbon credit ${carbonCreditId}`);

      // Get the carbon credit to check for minimum purchase quantity
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        select: {
          minPurchaseQuantity: true,
          organization: {
            select: {
              transactionFeeRate: true,
            },
          },
        },
      });

      if (!carbonCredit) {
        throw new ApiError("Carbon credit not found", ErrorType.NOT_FOUND, 404);
      }

      // Get all pending buy orders for the carbon credit, sorted by price (highest first) and creation date
      const buyOrders = await db.order.findMany({
        where: {
          carbonCreditId,
          type: OrderType.BUY,
          status: OrderStatus.PENDING,
        },
        orderBy: [
          { price: "desc" },
          { createdAt: "asc" },
        ],
        include: {
          buyer: {
            select: {
              id: true,
              name: true,
              email: true,
              organizationId: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              transactionFeeRate: true,
            },
          },
        },
      });

      // Get all pending sell orders for the carbon credit, sorted by price (lowest first) and creation date
      const sellOrders = await db.order.findMany({
        where: {
          carbonCreditId,
          type: OrderType.SELL,
          status: OrderStatus.PENDING,
        },
        orderBy: [
          { price: "asc" },
          { createdAt: "asc" },
        ],
        include: {
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
              organizationId: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              transactionFeeRate: true,
            },
          },
          carbonCredit: {
            select: {
              id: true,
              name: true,
              vintage: true,
              standard: true,
              methodology: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                  transactionFeeRate: true,
                },
              },
            },
          },
        },
      });

      // If there are no buy or sell orders, return empty array
      if (buyOrders.length === 0 || sellOrders.length === 0) {
        return [];
      }

      // Match orders based on the selected strategy
      const matches = [];
      const processedBuyOrders = new Set<string>();
      const processedSellOrders = new Set<string>();

      if (matchingStrategy === "PRICE_TIME") {
        // Price-Time Priority Matching (default)
        for (const buyOrder of buyOrders) {
          // Skip processed buy orders
          if (processedBuyOrders.has(buyOrder.id)) {
            continue;
          }

          let remainingBuyQuantity = buyOrder.quantity;

          for (const sellOrder of sellOrders) {
            // Skip processed sell orders
            if (processedSellOrders.has(sellOrder.id)) {
              continue;
            }

            // Check if the buy price is greater than or equal to the sell price
            if (buyOrder.price >= sellOrder.price) {
              // Determine the quantity to match (minimum of remaining buy quantity and sell quantity)
              const matchQuantity = Math.min(remainingBuyQuantity, sellOrder.quantity);

              // Check if the match quantity meets the minimum purchase quantity
              if (carbonCredit.minPurchaseQuantity && matchQuantity < carbonCredit.minPurchaseQuantity) {
                // Skip this match if it doesn't meet the minimum purchase quantity
                continue;
              }

              // Determine the execution price (usually the sell order price in a price-time priority model)
              const executionPrice = sellOrder.price;

              // Calculate the total value of the transaction
              const totalValue = matchQuantity * executionPrice;

              // Get the organization's transaction fee rate or use the default
              const sellerOrgFeeRate = sellOrder.organization.transactionFeeRate || 0.02; // Default to 2%
              const buyerOrgFeeRate = buyOrder.organization.transactionFeeRate || 0.02; // Default to 2%

              // Calculate platform fees for both parties
              const sellerFee = totalValue * sellerOrgFeeRate;
              const buyerFee = totalValue * buyerOrgFeeRate;

              // Add the match to the result
              matches.push({
                buyOrderId: buyOrder.id,
                sellOrderId: sellOrder.id,
                quantity: matchQuantity,
                price: executionPrice,
                totalValue,
                sellerFee,
                buyerFee,
                buyerId: buyOrder.buyer.id,
                sellerId: sellOrder.seller.id,
                buyerOrgId: buyOrder.buyer.organizationId,
                sellerOrgId: sellOrder.seller.organizationId,
                carbonCreditId,
                carbonCreditName: sellOrder.carbonCredit.name,
                buyerName: buyOrder.buyer.name,
                sellerName: sellOrder.seller.name,
                buyerEmail: buyOrder.buyer.email,
                sellerEmail: sellOrder.seller.email,
              });

              // Update remaining buy quantity
              remainingBuyQuantity -= matchQuantity;

              // If the sell order is fully matched, mark it as processed
              if (matchQuantity === sellOrder.quantity) {
                processedSellOrders.add(sellOrder.id);
              }

              // If the buy order is fully matched, mark it as processed and break the inner loop
              if (remainingBuyQuantity === 0) {
                processedBuyOrders.add(buyOrder.id);
                break;
              }
            }
          }
        }
      } else if (matchingStrategy === "PRO_RATA") {
        // Pro-Rata Matching (for high-volume markets)
        // Find the highest buy price and lowest sell price
        if (buyOrders.length > 0 && sellOrders.length > 0) {
          const highestBuyPrice = buyOrders[0].price;
          const lowestSellPrice = sellOrders[0].price;

          // Check if there's a match
          if (highestBuyPrice >= lowestSellPrice) {
            // Determine the execution price (usually the midpoint in pro-rata matching)
            const executionPrice = (highestBuyPrice + lowestSellPrice) / 2;

            // Get all eligible buy orders (price >= execution price)
            const eligibleBuyOrders = buyOrders.filter(order => order.price >= executionPrice);

            // Get all eligible sell orders (price <= execution price)
            const eligibleSellOrders = sellOrders.filter(order => order.price <= executionPrice);

            // Calculate total buy and sell quantities
            const totalBuyQuantity = eligibleBuyOrders.reduce((sum, order) => sum + order.quantity, 0);
            const totalSellQuantity = eligibleSellOrders.reduce((sum, order) => sum + order.quantity, 0);

            // Determine the total matched quantity (minimum of total buy and sell quantities)
            const totalMatchedQuantity = Math.min(totalBuyQuantity, totalSellQuantity);

            // Allocate the matched quantity pro-rata to buy orders
            let remainingMatchedQuantity = totalMatchedQuantity;

            for (const buyOrder of eligibleBuyOrders) {
              // Calculate the pro-rata allocation for this buy order
              const buyAllocation = (buyOrder.quantity / totalBuyQuantity) * totalMatchedQuantity;
              const buyMatchQuantity = Math.min(buyAllocation, remainingMatchedQuantity);

              if (buyMatchQuantity > 0) {
                // Allocate the matched quantity pro-rata to sell orders
                let remainingBuyMatchQuantity = buyMatchQuantity;

                for (const sellOrder of eligibleSellOrders) {
                  // Calculate the pro-rata allocation for this sell order
                  const sellAllocation = (sellOrder.quantity / totalSellQuantity) * buyMatchQuantity;
                  const matchQuantity = Math.min(sellAllocation, remainingBuyMatchQuantity);

                  if (matchQuantity > 0) {
                    // Check if the match quantity meets the minimum purchase quantity
                    if (carbonCredit.minPurchaseQuantity && matchQuantity < carbonCredit.minPurchaseQuantity) {
                      // Skip this match if it doesn't meet the minimum purchase quantity
                      continue;
                    }

                    // Calculate the total value of the transaction
                    const totalValue = matchQuantity * executionPrice;

                    // Get the organization's transaction fee rate or use the default
                    const sellerOrgFeeRate = sellOrder.organization.transactionFeeRate || 0.02; // Default to 2%
                    const buyerOrgFeeRate = buyOrder.organization.transactionFeeRate || 0.02; // Default to 2%

                    // Calculate platform fees for both parties
                    const sellerFee = totalValue * sellerOrgFeeRate;
                    const buyerFee = totalValue * buyerOrgFeeRate;

                    // Add the match to the result
                    matches.push({
                      buyOrderId: buyOrder.id,
                      sellOrderId: sellOrder.id,
                      quantity: matchQuantity,
                      price: executionPrice,
                      totalValue,
                      sellerFee,
                      buyerFee,
                      buyerId: buyOrder.buyer.id,
                      sellerId: sellOrder.seller.id,
                      buyerOrgId: buyOrder.buyer.organizationId,
                      sellerOrgId: sellOrder.seller.organizationId,
                      carbonCreditId,
                      carbonCreditName: sellOrder.carbonCredit.name,
                      buyerName: buyOrder.buyer.name,
                      sellerName: sellOrder.seller.name,
                      buyerEmail: buyOrder.buyer.email,
                      sellerEmail: sellOrder.seller.email,
                    });

                    // Update remaining quantities
                    remainingBuyMatchQuantity -= matchQuantity;

                    // If we've allocated all of this buy order's match quantity, break the inner loop
                    if (remainingBuyMatchQuantity === 0) {
                      break;
                    }
                  }
                }

                // Update remaining matched quantity
                remainingMatchedQuantity -= buyMatchQuantity;

                // If we've allocated all of the total matched quantity, break the outer loop
                if (remainingMatchedQuantity === 0) {
                  break;
                }
              }
            }
          }
        }
      } else if (matchingStrategy === "FIFO") {
        // First-In-First-Out Matching (time priority only)
        // Sort orders by creation time only
        const timeOrderedBuyOrders = [...buyOrders].sort((a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        const timeOrderedSellOrders = [...sellOrders].sort((a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        for (const buyOrder of timeOrderedBuyOrders) {
          // Skip processed buy orders
          if (processedBuyOrders.has(buyOrder.id)) {
            continue;
          }

          let remainingBuyQuantity = buyOrder.quantity;

          for (const sellOrder of timeOrderedSellOrders) {
            // Skip processed sell orders
            if (processedSellOrders.has(sellOrder.id)) {
              continue;
            }

            // Check if the buy price is greater than or equal to the sell price
            if (buyOrder.price >= sellOrder.price) {
              // Determine the quantity to match (minimum of remaining buy quantity and sell quantity)
              const matchQuantity = Math.min(remainingBuyQuantity, sellOrder.quantity);

              // Check if the match quantity meets the minimum purchase quantity
              if (carbonCredit.minPurchaseQuantity && matchQuantity < carbonCredit.minPurchaseQuantity) {
                // Skip this match if it doesn't meet the minimum purchase quantity
                continue;
              }

              // In FIFO, we use the sell order price as the execution price
              const executionPrice = sellOrder.price;

              // Calculate the total value of the transaction
              const totalValue = matchQuantity * executionPrice;

              // Get the organization's transaction fee rate or use the default
              const sellerOrgFeeRate = sellOrder.organization.transactionFeeRate || 0.02; // Default to 2%
              const buyerOrgFeeRate = buyOrder.organization.transactionFeeRate || 0.02; // Default to 2%

              // Calculate platform fees for both parties
              const sellerFee = totalValue * sellerOrgFeeRate;
              const buyerFee = totalValue * buyerOrgFeeRate;

              // Add the match to the result
              matches.push({
                buyOrderId: buyOrder.id,
                sellOrderId: sellOrder.id,
                quantity: matchQuantity,
                price: executionPrice,
                totalValue,
                sellerFee,
                buyerFee,
                buyerId: buyOrder.buyer.id,
                sellerId: sellOrder.seller.id,
                buyerOrgId: buyOrder.buyer.organizationId,
                sellerOrgId: sellOrder.seller.organizationId,
                carbonCreditId,
                carbonCreditName: sellOrder.carbonCredit.name,
                buyerName: buyOrder.buyer.name,
                sellerName: sellOrder.seller.name,
                buyerEmail: buyOrder.buyer.email,
                sellerEmail: sellOrder.seller.email,
              });

              // Update remaining buy quantity
              remainingBuyQuantity -= matchQuantity;

              // If the sell order is fully matched, mark it as processed
              if (matchQuantity === sellOrder.quantity) {
                processedSellOrders.add(sellOrder.id);
              }

              // If the buy order is fully matched, mark it as processed and break the inner loop
              if (remainingBuyQuantity === 0) {
                processedBuyOrders.add(buyOrder.id);
                break;
              }
            }
          }
        }
      } else if (matchingStrategy === "VOLUME_WEIGHTED") {
        // Volume-Weighted Matching (prioritizes larger orders)
        // Sort orders by volume (quantity) first, then by price and time
        const volumeOrderedBuyOrders = [...buyOrders].sort((a, b) => {
          // First sort by quantity (descending)
          if (b.quantity !== a.quantity) {
            return b.quantity - a.quantity;
          }
          // Then by price (descending)
          if (b.price !== a.price) {
            return b.price - a.price;
          }
          // Finally by time (ascending)
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });

        const volumeOrderedSellOrders = [...sellOrders].sort((a, b) => {
          // First sort by quantity (descending)
          if (b.quantity !== a.quantity) {
            return b.quantity - a.quantity;
          }
          // Then by price (ascending)
          if (a.price !== b.price) {
            return a.price - b.price;
          }
          // Finally by time (ascending)
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });

        for (const buyOrder of volumeOrderedBuyOrders) {
          // Skip processed buy orders
          if (processedBuyOrders.has(buyOrder.id)) {
            continue;
          }

          let remainingBuyQuantity = buyOrder.quantity;

          for (const sellOrder of volumeOrderedSellOrders) {
            // Skip processed sell orders
            if (processedSellOrders.has(sellOrder.id)) {
              continue;
            }

            // Check if the buy price is greater than or equal to the sell price
            if (buyOrder.price >= sellOrder.price) {
              // Determine the quantity to match (minimum of remaining buy quantity and sell quantity)
              const matchQuantity = Math.min(remainingBuyQuantity, sellOrder.quantity);

              // Check if the match quantity meets the minimum purchase quantity
              if (carbonCredit.minPurchaseQuantity && matchQuantity < carbonCredit.minPurchaseQuantity) {
                // Skip this match if it doesn't meet the minimum purchase quantity
                continue;
              }

              // For volume-weighted matching, we use the volume-weighted average price
              // This is calculated as the weighted average of the buy and sell prices,
              // weighted by their respective quantities
              const executionPrice = (
                (buyOrder.price * buyOrder.quantity) +
                (sellOrder.price * sellOrder.quantity)
              ) / (buyOrder.quantity + sellOrder.quantity);

              // Calculate the total value of the transaction
              const totalValue = matchQuantity * executionPrice;

              // Get the organization's transaction fee rate or use the default
              const sellerOrgFeeRate = sellOrder.organization.transactionFeeRate || 0.02; // Default to 2%
              const buyerOrgFeeRate = buyOrder.organization.transactionFeeRate || 0.02; // Default to 2%

              // Calculate platform fees for both parties
              const sellerFee = totalValue * sellerOrgFeeRate;
              const buyerFee = totalValue * buyerOrgFeeRate;

              // Add the match to the result
              matches.push({
                buyOrderId: buyOrder.id,
                sellOrderId: sellOrder.id,
                quantity: matchQuantity,
                price: executionPrice,
                totalValue,
                sellerFee,
                buyerFee,
                buyerId: buyOrder.buyer.id,
                sellerId: sellOrder.seller.id,
                buyerOrgId: buyOrder.buyer.organizationId,
                sellerOrgId: sellOrder.seller.organizationId,
                carbonCreditId,
                carbonCreditName: sellOrder.carbonCredit.name,
                buyerName: buyOrder.buyer.name,
                sellerName: sellOrder.seller.name,
                buyerEmail: buyOrder.buyer.email,
                sellerEmail: sellOrder.seller.email,
              });

              // Update remaining buy quantity
              remainingBuyQuantity -= matchQuantity;

              // If the sell order is fully matched, mark it as processed
              if (matchQuantity === sellOrder.quantity) {
                processedSellOrders.add(sellOrder.id);
              }

              // If the buy order is fully matched, mark it as processed and break the inner loop
              if (remainingBuyQuantity === 0) {
                processedBuyOrders.add(buyOrder.id);
                break;
              }
            }
          }
        }
      }

      // Process matches
      const processedMatches = [];

      for (const match of matches) {
        // Create a transaction record
        const transaction = await db.transaction.create({
          data: {
            type: TransactionType.TRADE,
            amount: match.totalValue,
            status: TransactionStatus.COMPLETED,
            quantity: match.quantity,
            price: match.price,
            buyerFee: match.buyerFee,
            sellerFee: match.sellerFee,
            buyer: {
              connect: { id: match.buyerId },
            },
            seller: {
              connect: { id: match.sellerId },
            },
            carbonCredit: {
              connect: { id: match.carbonCreditId },
            },
            buyOrder: {
              connect: { id: match.buyOrderId },
            },
            sellOrder: {
              connect: { id: match.sellOrderId },
            },
          },
        });

        // Get the buy order to check if it's partially or fully matched
        const buyOrder = await db.order.findUnique({
          where: { id: match.buyOrderId },
          select: { quantity: true, matchedQuantity: true },
        });

        const newMatchedQuantity = (buyOrder?.matchedQuantity || 0) + match.quantity;
        const isBuyOrderFullyMatched = newMatchedQuantity >= (buyOrder?.quantity || 0);

        // Update the buy order status and matched quantity
        await db.order.update({
          where: { id: match.buyOrderId },
          data: {
            status: isBuyOrderFullyMatched ? OrderStatus.COMPLETED : OrderStatus.PARTIALLY_FILLED,
            matchedQuantity: newMatchedQuantity,
            updatedAt: new Date(),
          },
        });

        // Get the sell order to check if it's partially or fully matched
        const sellOrder = await db.order.findUnique({
          where: { id: match.sellOrderId },
          select: { quantity: true, matchedQuantity: true },
        });

        const newSellMatchedQuantity = (sellOrder?.matchedQuantity || 0) + match.quantity;
        const isSellOrderFullyMatched = newSellMatchedQuantity >= (sellOrder?.quantity || 0);

        // Update the sell order status and matched quantity
        await db.order.update({
          where: { id: match.sellOrderId },
          data: {
            status: isSellOrderFullyMatched ? OrderStatus.COMPLETED : OrderStatus.PARTIALLY_FILLED,
            matchedQuantity: newSellMatchedQuantity,
            updatedAt: new Date(),
          },
        });

        // Update the carbon credit available quantity
        await db.carbonCredit.update({
          where: { id: match.carbonCreditId },
          data: {
            availableQuantity: {
              decrement: match.quantity,
            },
          },
        });

        // Create seller fee billing record
        await db.billing.create({
          data: {
            type: "TRANSACTION_FEE",
            amount: match.sellerFee,
            status: "PAID",
            description: `Seller transaction fee for carbon credit trade (${transaction.id})`,
            organization: {
              connect: { id: match.sellerOrgId },
            },
            transaction: {
              connect: { id: transaction.id },
            },
          },
        });

        // Create buyer fee billing record
        await db.billing.create({
          data: {
            type: "TRANSACTION_FEE",
            amount: match.buyerFee,
            status: "PAID",
            description: `Buyer transaction fee for carbon credit trade (${transaction.id})`,
            organization: {
              connect: { id: match.buyerOrgId },
            },
            transaction: {
              connect: { id: transaction.id },
            },
          },
        });

        // Create audit logs
        await auditService.log(
          AuditLogType.TRADE_EXECUTED,
          `Trade executed: Buy order ${match.buyOrderId} and Sell order ${match.sellOrderId} for ${match.quantity} tons of ${match.carbonCreditName} at $${match.price} per ton`,
          match.buyerId,
          match.buyerOrgId,
          {
            transactionId: transaction.id,
            buyOrderId: match.buyOrderId,
            sellOrderId: match.sellOrderId,
            quantity: match.quantity,
            price: match.price,
            totalValue: match.totalValue,
            buyerFee: match.buyerFee,
            carbonCreditName: match.carbonCreditName,
          }
        );

        await auditService.log(
          AuditLogType.TRADE_EXECUTED,
          `Trade executed: Buy order ${match.buyOrderId} and Sell order ${match.sellOrderId} for ${match.quantity} tons of ${match.carbonCreditName} at $${match.price} per ton`,
          match.sellerId,
          match.sellerOrgId,
          {
            transactionId: transaction.id,
            buyOrderId: match.buyOrderId,
            sellOrderId: match.sellOrderId,
            quantity: match.quantity,
            price: match.price,
            totalValue: match.totalValue,
            sellerFee: match.sellerFee,
            carbonCreditName: match.carbonCreditName,
          }
        );

        // Send notifications to buyer and seller
        await notificationService.createNotification(
          match.buyerId,
          "Order Matched",
          `Your buy order for ${match.quantity} tons of ${match.carbonCreditName} has been matched at $${match.price} per ton.`,
          "TRADE",
          [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
          {
            transactionId: transaction.id,
            carbonCreditId: match.carbonCreditId,
            quantity: match.quantity,
            price: match.price,
            totalValue: match.totalValue,
            fee: match.buyerFee,
            carbonCreditName: match.carbonCreditName,
          },
          `/dashboard/transactions/${transaction.id}`,
          "View Transaction"
        );

        await notificationService.createNotification(
          match.sellerId,
          "Order Matched",
          `Your sell order for ${match.quantity} tons of ${match.carbonCreditName} has been matched at $${match.price} per ton.`,
          "TRADE",
          [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
          {
            transactionId: transaction.id,
            carbonCreditId: match.carbonCreditId,
            quantity: match.quantity,
            price: match.price,
            totalValue: match.totalValue,
            fee: match.sellerFee,
            carbonCreditName: match.carbonCreditName,
          },
          `/dashboard/transactions/${transaction.id}`,
          "View Transaction"
        );

        // Add the processed match to the result
        processedMatches.push({
          ...match,
          transactionId: transaction.id,
        });
      }

      logger.info(`Matched ${processedMatches.length} orders for carbon credit ${carbonCreditId}`);

      return processedMatches;
    } catch (error) {
      logger.error("Error matching orders:", error);
      throw new Error(`Failed to match orders: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Match all pending orders across all carbon credits
   * @param matchingStrategy Optional matching strategy to use for all carbon credits
   * @returns Matched orders
   */
  static async matchAllOrders(
    matchingStrategy: "PRICE_TIME" | "PRO_RATA" | "FIFO" | "VOLUME_WEIGHTED" = "PRICE_TIME"
  ) {
    try {
      logger.info(`Matching all pending orders using ${matchingStrategy} strategy`);

      // Get all carbon credits with pending orders
      const carbonCreditsWithOrders = await db.carbonCredit.findMany({
        where: {
          OR: [
            {
              buyOrders: {
                some: {
                  status: OrderStatus.PENDING,
                },
              },
            },
            {
              sellOrders: {
                some: {
                  status: OrderStatus.PENDING,
                },
              },
            },
          ],
        },
        select: {
          id: true,
          // Include additional fields to help determine the best matching strategy per carbon credit
          standard: true,
          vintage: true,
          availableQuantity: true,
          buyOrders: {
            where: {
              status: OrderStatus.PENDING,
            },
            select: {
              id: true,
              quantity: true,
            },
          },
          sellOrders: {
            where: {
              status: OrderStatus.PENDING,
            },
            select: {
              id: true,
              quantity: true,
            },
          },
        },
      });

      // Match orders for each carbon credit
      const allMatches = [];

      for (const carbonCredit of carbonCreditsWithOrders) {
        // Determine the best matching strategy for this carbon credit
        // This can be customized based on market conditions, carbon credit type, etc.
        let creditMatchingStrategy = matchingStrategy;

        // Example: Use volume-weighted for high-volume markets
        const totalBuyVolume = carbonCredit.buyOrders.reduce((sum, order) => sum + order.quantity, 0);
        const totalSellVolume = carbonCredit.sellOrders.reduce((sum, order) => sum + order.quantity, 0);
        const totalVolume = totalBuyVolume + totalSellVolume;

        // If no specific strategy was provided, use an adaptive approach
        if (matchingStrategy === "PRICE_TIME" && !matchingStrategy) {
          // For high-volume markets, use PRO_RATA
          if (totalVolume > 10000 || carbonCredit.buyOrders.length > 20 || carbonCredit.sellOrders.length > 20) {
            creditMatchingStrategy = "PRO_RATA";
          }
          // For markets with large orders, use VOLUME_WEIGHTED
          else if (carbonCredit.buyOrders.some(o => o.quantity > 1000) || carbonCredit.sellOrders.some(o => o.quantity > 1000)) {
            creditMatchingStrategy = "VOLUME_WEIGHTED";
          }
          // For standard markets, use PRICE_TIME
          else {
            creditMatchingStrategy = "PRICE_TIME";
          }
        }

        logger.info(`Using ${creditMatchingStrategy} strategy for carbon credit ${carbonCredit.id} (${totalVolume} total volume)`);

        const matches = await this.matchOrders(carbonCredit.id, creditMatchingStrategy);
        allMatches.push(...matches);
      }

      logger.info(`Matched ${allMatches.length} orders across ${carbonCreditsWithOrders.length} carbon credits`);

      return allMatches;
    } catch (error) {
      logger.error("Error matching all orders:", error);
      throw new Error(`Failed to match all orders: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a new order
   * @param userId User ID
   * @param organizationId Organization ID
   * @param type Order type (BUY or SELL)
   * @param carbonCreditId Carbon credit ID
   * @param quantity Quantity
   * @param price Price per ton
   * @returns Created order
   */
  static async createOrder(
    userId: string,
    organizationId: string,
    type: "BUY" | "SELL",
    carbonCreditId: string,
    quantity: number,
    price: number
  ) {
    try {
      logger.info(`Creating ${type} order for carbon credit ${carbonCreditId}`);

      // Check if the carbon credit exists
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
      });

      if (!carbonCredit) {
        throw new Error("Carbon credit not found");
      }

      // For sell orders, check if the organization owns the carbon credit
      if (type === "SELL" && carbonCredit.organizationId !== organizationId) {
        throw new Error("You can only sell carbon credits owned by your organization");
      }

      // For sell orders, check if there's enough quantity available
      if (type === "SELL" && carbonCredit.availableQuantity < quantity) {
        throw new Error(`Not enough quantity available. Available: ${carbonCredit.availableQuantity} tons`);
      }

      // Create the order
      const order = await db.order.create({
        data: {
          type,
          quantity,
          price,
          matchedQuantity: 0, // Initialize matched quantity to 0
          status: OrderStatus.PENDING,
          buyer: {
            connect: { id: userId },
          },
          seller: {
            connect: { id: userId },
          },
          carbonCredit: {
            connect: { id: carbonCreditId },
          },
          organization: {
            connect: { id: organizationId },
          },
        },
      });

      // Create audit log
      await auditService.log(
        type === "BUY" ? AuditLogType.BUY_ORDER_CREATED : AuditLogType.SELL_ORDER_CREATED,
        `${type} order created for ${quantity} tons of carbon credit ${carbonCredit.name} at $${price} per ton`,
        userId,
        organizationId,
        {
          orderId: order.id,
          carbonCreditId,
          carbonCreditName: carbonCredit.name,
          quantity,
          price,
          totalValue: quantity * price,
        }
      );

      // Try to match the order immediately
      await this.matchOrders(carbonCreditId);

      return order;
    } catch (error) {
      logger.error("Error creating order:", error);
      throw new Error(`Failed to create order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Cancel an order
   * @param orderId Order ID
   * @param userId User ID
   * @returns Cancelled order
   */
  static async cancelOrder(orderId: string, userId: string) {
    try {
      logger.info(`Cancelling order ${orderId}`);

      // Check if the order exists and belongs to the user
      const order = await db.order.findFirst({
        where: {
          id: orderId,
          OR: [
            { buyerId: userId },
            { sellerId: userId },
          ],
          status: OrderStatus.PENDING, // Only pending orders can be cancelled
        },
        include: {
          carbonCredit: true,
        },
      });

      if (!order) {
        throw new Error("Order not found or cannot be cancelled");
      }

      // Update the order status
      const updatedOrder = await db.order.update({
        where: { id: orderId },
        data: {
          status: OrderStatus.CANCELLED,
          updatedAt: new Date(),
        },
      });

      // Create audit log
      await auditService.log(
        AuditLogType.ORDER_CANCELLED,
        `${order.type} order cancelled for ${order.quantity} tons of carbon credit ${order.carbonCredit.name} at $${order.price} per ton`,
        userId,
        (await db.user.findUnique({ where: { id: userId } }))!.organizationId!,
        {
          orderId,
          orderType: order.type,
          carbonCreditId: order.carbonCredit.id,
          carbonCreditName: order.carbonCredit.name,
          quantity: order.quantity,
          price: order.price,
        }
      );

      return updatedOrder;
    } catch (error) {
      logger.error("Error cancelling order:", error);
      throw new Error(`Failed to cancel order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
