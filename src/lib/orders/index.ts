import { OrderStatus, OrderType, OrderSide } from '@prisma/client';
import { 
  Order, 
  OrderCreationData, 
  OrderUpdateData,
  OrderFilter,
  OrderResponse,
  OrderMatchResult,
  OrderBook,
  MarketSummary,
  Transaction,
  TransactionCreationData
} from './types';
import { OrderService } from './service';
import { OrderBookService } from './order-book';

/**
 * Order manager
 */
export class OrderManager {
  /**
   * Create an order
   * @param data Order creation data
   * @returns Created order
   */
  static async createOrder(data: OrderCreationData): Promise<Order> {
    return OrderService.createOrder(data);
  }

  /**
   * Get order by ID
   * @param id Order ID
   * @returns Order
   */
  static async getOrderById(id: string): Promise<Order | null> {
    return OrderService.getOrderById(id);
  }

  /**
   * Update an order
   * @param id Order ID
   * @param data Order update data
   * @param userId User ID performing the update
   * @returns Updated order
   */
  static async updateOrder(id: string, data: OrderUpdateData, userId: string): Promise<Order> {
    return OrderService.updateOrder(id, data, userId);
  }

  /**
   * Cancel an order
   * @param id Order ID
   * @param userId User ID performing the cancellation
   * @returns Canceled order
   */
  static async cancelOrder(id: string, userId: string): Promise<Order> {
    return OrderService.cancelOrder(id, userId);
  }

  /**
   * Get orders
   * @param filter Filter options
   * @returns Orders and pagination
   */
  static async getOrders(filter: OrderFilter = {}): Promise<OrderResponse> {
    return OrderService.getOrders(filter);
  }

  /**
   * Match an order
   * @param orderId Order ID
   * @returns Match result
   */
  static async matchOrder(orderId: string): Promise<OrderMatchResult> {
    return OrderService.matchOrder(orderId);
  }

  /**
   * Get transaction by ID
   * @param id Transaction ID
   * @returns Transaction
   */
  static async getTransactionById(id: string): Promise<Transaction | null> {
    return OrderService.getTransactionById(id);
  }

  /**
   * Get transactions
   * @param filter Filter options
   * @returns Transactions
   */
  static async getTransactions(filter: {
    userId?: string;
    organizationId?: string;
    carbonCreditId?: string;
    orderId?: string;
    type?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}): Promise<any> {
    return OrderService.getTransactions(filter);
  }

  /**
   * Get order book for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param depth Depth of the order book (number of price levels)
   * @returns Order book
   */
  static async getOrderBook(carbonCreditId: string, depth: number = 10): Promise<OrderBook> {
    return OrderBookService.getOrderBook(carbonCreditId, depth);
  }

  /**
   * Get market summary for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param period Period in hours (default: 24)
   * @returns Market summary
   */
  static async getMarketSummary(carbonCreditId: string, period: number = 24): Promise<MarketSummary> {
    return OrderBookService.getMarketSummary(carbonCreditId, period);
  }

  /**
   * Get market summaries for all carbon credits
   * @param period Period in hours (default: 24)
   * @returns Market summaries
   */
  static async getAllMarketSummaries(period: number = 24): Promise<MarketSummary[]> {
    return OrderBookService.getAllMarketSummaries(period);
  }

  /**
   * Get price history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param period Period in days (default: 30)
   * @param interval Interval in hours (default: 24)
   * @returns Price history
   */
  static async getPriceHistory(
    carbonCreditId: string,
    period: number = 30,
    interval: number = 24
  ): Promise<{ timestamp: Date; price: number; volume: number }[]> {
    return OrderBookService.getPriceHistory(carbonCreditId, period, interval);
  }
}

// Create a singleton instance
const orderManager = new OrderManager();

// Export the singleton instance
export { orderManager };

// Export types and enums
export {
  OrderStatus,
  OrderType,
  OrderSide,
  Order,
  OrderCreationData,
  OrderUpdateData,
  OrderFilter,
  OrderResponse,
  OrderMatchResult,
  OrderBook,
  MarketSummary,
  Transaction,
  TransactionCreationData
};
