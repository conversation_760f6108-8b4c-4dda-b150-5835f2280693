import { OrderStatus, OrderType, OrderSide, Prisma } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { 
  Order, 
  OrderCreationData, 
  OrderUpdateData,
  OrderFilter,
  OrderResponse,
  OrderMatchResult,
  Transaction,
  TransactionCreationData
} from './types';
import { notificationService } from '@/lib/notifications';
import { auditManager } from '@/lib/audit';

/**
 * Order service
 */
export class OrderService {
  /**
   * Create an order
   * @param data Order creation data
   * @returns Created order
   */
  static async createOrder(data: OrderCreationData): Promise<Order> {
    try {
      logger.info(`Creating ${data.side} order for carbon credit ${data.carbonCreditId}: ${data.quantity} @ ${data.price}`);
      
      // Get carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: data.carbonCreditId },
        include: {
          organization: true,
          user: true,
        },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${data.carbonCreditId}`);
      }
      
      // Check if carbon credit is listed
      if (carbonCredit.status !== "LISTED") {
        throw new Error(`Carbon credit is not listed: ${data.carbonCreditId}`);
      }
      
      // Get user
      const user = await db.user.findUnique({
        where: { id: data.userId },
        include: {
          organization: true,
        },
      });
      
      if (!user) {
        throw new Error(`User not found: ${data.userId}`);
      }
      
      // Validate order
      if (data.side === OrderSide.SELL) {
        // Check if user is the owner of the carbon credit
        if (carbonCredit.userId !== data.userId) {
          throw new Error("You do not own this carbon credit");
        }
        
        // Check if quantity is available
        if (data.quantity > carbonCredit.availableQuantity) {
          throw new Error(`Insufficient quantity available: ${carbonCredit.availableQuantity}`);
        }
        
        // Update carbon credit available quantity
        await db.carbonCredit.update({
          where: { id: data.carbonCreditId },
          data: {
            availableQuantity: {
              decrement: data.quantity,
            },
          },
        });
      }
      
      // Create order
      const order = await db.order.create({
        data: {
          type: data.type,
          side: data.side,
          status: OrderStatus.PENDING,
          quantity: data.quantity,
          price: data.price,
          expiresAt: data.expiresAt,
          carbonCredit: {
            connect: { id: data.carbonCreditId },
          },
          ...(data.side === OrderSide.BUY
            ? {
                buyer: {
                  connect: { id: data.userId },
                },
              }
            : {
                seller: {
                  connect: { id: data.userId },
                },
              }),
        },
      });
      
      // Create audit log
      await auditManager.createAuditLog({
        type: "ORDER_CREATED",
        description: `${data.side} order created for carbon credit ${data.carbonCreditId}: ${data.quantity} @ ${data.price}`,
        userId: data.userId,
        organizationId: user.organizationId || undefined,
        metadata: {
          orderId: order.id,
          carbonCreditId: data.carbonCreditId,
          type: data.type,
          side: data.side,
          quantity: data.quantity,
          price: data.price,
          expiresAt: data.expiresAt,
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId: data.userId,
        title: `${data.side} Order Created`,
        message: `Your ${data.side.toLowerCase()} order for ${data.quantity} carbon credits at ${data.price} has been created.`,
        type: "TRANSACTION",
        priority: "NORMAL",
        actionUrl: `/dashboard/orders/${order.id}`,
        actionLabel: "View Order",
        organizationId: user.organizationId || undefined,
      });
      
      // If this is a market order, try to match it immediately
      if (data.type === OrderType.MARKET) {
        await this.matchOrder(order.id);
      }
      
      return order;
    } catch (error) {
      logger.error(`Error creating order for carbon credit ${data.carbonCreditId}:`, error);
      throw new Error(`Failed to create order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get order by ID
   * @param id Order ID
   * @returns Order
   */
  static async getOrderById(id: string): Promise<Order | null> {
    try {
      return await db.order.findUnique({
        where: { id },
        include: {
          carbonCredit: true,
          buyer: {
            select: {
              id: true,
              name: true,
              email: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          matchedOrder: true,
          transactions: true,
        },
      });
    } catch (error) {
      logger.error(`Error getting order ${id}:`, error);
      throw new Error(`Failed to get order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Update an order
   * @param id Order ID
   * @param data Order update data
   * @param userId User ID performing the update
   * @returns Updated order
   */
  static async updateOrder(id: string, data: OrderUpdateData, userId: string): Promise<Order> {
    try {
      // Get order
      const order = await db.order.findUnique({
        where: { id },
        include: {
          carbonCredit: true,
        },
      });
      
      if (!order) {
        throw new Error(`Order not found: ${id}`);
      }
      
      // Check if user has permission
      const isOwner = (order.side === OrderSide.BUY && order.buyerId === userId) ||
                      (order.side === OrderSide.SELL && order.sellerId === userId);
      
      if (!isOwner) {
        throw new Error("You do not have permission to update this order");
      }
      
      // Check if order can be updated
      if (order.status !== OrderStatus.PENDING) {
        throw new Error(`Cannot update order with status ${order.status}`);
      }
      
      // If quantity is being updated for a sell order, check if it's valid
      if (data.quantity !== undefined && order.side === OrderSide.SELL && data.quantity > order.quantity) {
        // Get carbon credit
        const carbonCredit = await db.carbonCredit.findUnique({
          where: { id: order.carbonCreditId },
        });
        
        if (!carbonCredit) {
          throw new Error(`Carbon credit not found: ${order.carbonCreditId}`);
        }
        
        // Check if additional quantity is available
        const additionalQuantity = data.quantity - order.quantity;
        if (additionalQuantity > carbonCredit.availableQuantity) {
          throw new Error(`Insufficient quantity available: ${carbonCredit.availableQuantity}`);
        }
        
        // Update carbon credit available quantity
        await db.carbonCredit.update({
          where: { id: order.carbonCreditId },
          data: {
            availableQuantity: {
              decrement: additionalQuantity,
            },
          },
        });
      }
      
      // If quantity is being reduced for a sell order, return the difference to available quantity
      if (data.quantity !== undefined && order.side === OrderSide.SELL && data.quantity < order.quantity) {
        const returnedQuantity = order.quantity - data.quantity;
        
        // Update carbon credit available quantity
        await db.carbonCredit.update({
          where: { id: order.carbonCreditId },
          data: {
            availableQuantity: {
              increment: returnedQuantity,
            },
          },
        });
      }
      
      // Update order
      const updatedOrder = await db.order.update({
        where: { id },
        data: {
          ...(data.quantity !== undefined && { quantity: data.quantity }),
          ...(data.price !== undefined && { price: data.price }),
          ...(data.expiresAt !== undefined && { expiresAt: data.expiresAt }),
          ...(data.status !== undefined && { status: data.status }),
        },
      });
      
      // Create audit log
      await auditManager.createAuditLog({
        type: "ORDER_UPDATED",
        description: `Order ${id} updated`,
        userId,
        metadata: {
          orderId: id,
          previousState: {
            quantity: order.quantity,
            price: order.price,
            expiresAt: order.expiresAt,
            status: order.status,
          },
          newState: {
            quantity: updatedOrder.quantity,
            price: updatedOrder.price,
            expiresAt: updatedOrder.expiresAt,
            status: updatedOrder.status,
          },
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId,
        title: "Order Updated",
        message: `Your ${order.side.toLowerCase()} order has been updated.`,
        type: "TRANSACTION",
        priority: "NORMAL",
        actionUrl: `/dashboard/orders/${id}`,
        actionLabel: "View Order",
      });
      
      return updatedOrder;
    } catch (error) {
      logger.error(`Error updating order ${id}:`, error);
      throw new Error(`Failed to update order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Cancel an order
   * @param id Order ID
   * @param userId User ID performing the cancellation
   * @returns Canceled order
   */
  static async cancelOrder(id: string, userId: string): Promise<Order> {
    try {
      // Get order
      const order = await db.order.findUnique({
        where: { id },
        include: {
          carbonCredit: true,
        },
      });
      
      if (!order) {
        throw new Error(`Order not found: ${id}`);
      }
      
      // Check if user has permission
      const isOwner = (order.side === OrderSide.BUY && order.buyerId === userId) ||
                      (order.side === OrderSide.SELL && order.sellerId === userId);
      
      if (!isOwner) {
        throw new Error("You do not have permission to cancel this order");
      }
      
      // Check if order can be canceled
      if (order.status !== OrderStatus.PENDING) {
        throw new Error(`Cannot cancel order with status ${order.status}`);
      }
      
      // If this is a sell order, return the quantity to available quantity
      if (order.side === OrderSide.SELL) {
        // Update carbon credit available quantity
        await db.carbonCredit.update({
          where: { id: order.carbonCreditId },
          data: {
            availableQuantity: {
              increment: order.quantity,
            },
          },
        });
      }
      
      // Update order
      const canceledOrder = await db.order.update({
        where: { id },
        data: {
          status: OrderStatus.CANCELED,
        },
      });
      
      // Create audit log
      await auditManager.createAuditLog({
        type: "ORDER_CANCELED",
        description: `Order ${id} canceled`,
        userId,
        metadata: {
          orderId: id,
          carbonCreditId: order.carbonCreditId,
          side: order.side,
          quantity: order.quantity,
          price: order.price,
        },
      });
      
      // Send notification
      await notificationService.createNotification({
        userId,
        title: "Order Canceled",
        message: `Your ${order.side.toLowerCase()} order has been canceled.`,
        type: "TRANSACTION",
        priority: "NORMAL",
        actionUrl: `/dashboard/orders/${id}`,
        actionLabel: "View Order",
      });
      
      return canceledOrder;
    } catch (error) {
      logger.error(`Error canceling order ${id}:`, error);
      throw new Error(`Failed to cancel order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get orders
   * @param filter Filter options
   * @returns Orders and pagination
   */
  static async getOrders(filter: OrderFilter = {}): Promise<OrderResponse> {
    try {
      const {
        userId,
        organizationId,
        carbonCreditId,
        type,
        side,
        status,
        minPrice,
        maxPrice,
        minQuantity,
        maxQuantity,
        startDate,
        endDate,
        search,
        limit = 10,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = filter;
      
      // Build query
      const where: Prisma.OrderWhereInput = {
        ...(carbonCreditId && { carbonCreditId }),
        ...(type && { type }),
        ...(side && { side }),
        ...(status && { status }),
        ...(minPrice !== undefined && { price: { gte: minPrice } }),
        ...(maxPrice !== undefined && { price: { lte: maxPrice } }),
        ...(minQuantity !== undefined && { quantity: { gte: minQuantity } }),
        ...(maxQuantity !== undefined && { quantity: { lte: maxQuantity } }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
      };
      
      // Add user filter
      if (userId) {
        where.OR = [
          { buyerId: userId },
          { sellerId: userId },
        ];
      }
      
      // Add organization filter
      if (organizationId) {
        where.OR = [
          { buyer: { organizationId } },
          { seller: { organizationId } },
        ];
      }
      
      // Get orders
      const orders = await db.order.findMany({
        where,
        include: {
          carbonCredit: {
            select: {
              id: true,
              projectId: true,
              vintage: true,
              standard: true,
              methodology: true,
            },
          },
          buyer: {
            select: {
              id: true,
              name: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          seller: {
            select: {
              id: true,
              name: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.order.count({ where });
      
      return {
        orders,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting orders:", error);
      throw new Error(`Failed to get orders: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Match an order
   * @param orderId Order ID
   * @returns Match result
   */
  static async matchOrder(orderId: string): Promise<OrderMatchResult> {
    try {
      // Get order
      const order = await db.order.findUnique({
        where: { id: orderId },
        include: {
          carbonCredit: true,
        },
      });
      
      if (!order) {
        return {
          success: false,
          message: `Order not found: ${orderId}`,
          order: order as any,
        };
      }
      
      // Check if order can be matched
      if (order.status !== OrderStatus.PENDING) {
        return {
          success: false,
          message: `Cannot match order with status ${order.status}`,
          order,
        };
      }
      
      // Find matching orders
      const matchingSide = order.side === OrderSide.BUY ? OrderSide.SELL : OrderSide.BUY;
      
      let matchingOrders;
      
      if (order.type === OrderType.MARKET) {
        // For market orders, match with any pending limit order for the same carbon credit
        matchingOrders = await db.order.findMany({
          where: {
            carbonCreditId: order.carbonCreditId,
            side: matchingSide,
            status: OrderStatus.PENDING,
            type: OrderType.LIMIT,
          },
          orderBy: {
            // For buy market orders, match with the lowest priced sell orders first
            // For sell market orders, match with the highest priced buy orders first
            price: order.side === OrderSide.BUY ? "asc" : "desc",
          },
        });
      } else {
        // For limit orders, match with pending orders that meet the price criteria
        matchingOrders = await db.order.findMany({
          where: {
            carbonCreditId: order.carbonCreditId,
            side: matchingSide,
            status: OrderStatus.PENDING,
            // For buy limit orders, match with sell orders with price <= buy order price
            // For sell limit orders, match with buy orders with price >= sell order price
            ...(order.side === OrderSide.BUY
              ? { price: { lte: order.price } }
              : { price: { gte: order.price } }),
          },
          orderBy: {
            // For buy limit orders, match with the lowest priced sell orders first
            // For sell limit orders, match with the highest priced buy orders first
            price: order.side === OrderSide.BUY ? "asc" : "desc",
          },
        });
      }
      
      if (matchingOrders.length === 0) {
        return {
          success: false,
          message: "No matching orders found",
          order,
        };
      }
      
      // Match with the first order
      const matchedOrder = matchingOrders[0];
      
      // Determine the execution price (use the limit order's price)
      const executionPrice = order.type === OrderType.MARKET ? matchedOrder.price : order.price;
      
      // Determine the execution quantity (minimum of the two orders)
      const executionQuantity = Math.min(order.quantity, matchedOrder.quantity);
      
      // Calculate the transaction amount
      const transactionAmount = executionQuantity * executionPrice;
      
      // Calculate the fee (0.5% of transaction amount)
      const fee = transactionAmount * 0.005;
      
      // Determine buyer and seller
      const buyerId = order.side === OrderSide.BUY ? order.buyerId : matchedOrder.buyerId;
      const sellerId = order.side === OrderSide.SELL ? order.sellerId : matchedOrder.sellerId;
      
      // Create transaction
      const transaction = await this.createTransaction({
        type: "TRADE",
        amount: transactionAmount,
        fee,
        orderId: order.id,
        carbonCreditId: order.carbonCreditId,
        buyerId,
        sellerId,
      });
      
      // Update orders
      let updatedOrder, updatedMatchedOrder;
      
      // If the execution quantity equals the order quantity, mark the order as completed
      if (executionQuantity === order.quantity) {
        updatedOrder = await db.order.update({
          where: { id: order.id },
          data: {
            status: OrderStatus.COMPLETED,
            matchedOrder: {
              connect: { id: matchedOrder.id },
            },
          },
        });
      } else {
        // Otherwise, reduce the order quantity
        updatedOrder = await db.order.update({
          where: { id: order.id },
          data: {
            quantity: order.quantity - executionQuantity,
            matchedOrder: {
              connect: { id: matchedOrder.id },
            },
          },
        });
      }
      
      // If the execution quantity equals the matched order quantity, mark the matched order as completed
      if (executionQuantity === matchedOrder.quantity) {
        updatedMatchedOrder = await db.order.update({
          where: { id: matchedOrder.id },
          data: {
            status: OrderStatus.COMPLETED,
            matchedOrder: {
              connect: { id: order.id },
            },
          },
        });
      } else {
        // Otherwise, reduce the matched order quantity
        updatedMatchedOrder = await db.order.update({
          where: { id: matchedOrder.id },
          data: {
            quantity: matchedOrder.quantity - executionQuantity,
            matchedOrder: {
              connect: { id: order.id },
            },
          },
        });
      }
      
      // Update carbon credit
      if (order.side === OrderSide.BUY) {
        // If this is a buy order, update the carbon credit owner
        await db.carbonCredit.update({
          where: { id: order.carbonCreditId },
          data: {
            user: {
              connect: { id: buyerId! },
            },
          },
        });
      }
      
      // Create audit log
      await auditManager.createAuditLog({
        type: "ORDER_MATCHED",
        description: `Order ${order.id} matched with order ${matchedOrder.id}`,
        userId: buyerId!,
        metadata: {
          orderId: order.id,
          matchedOrderId: matchedOrder.id,
          carbonCreditId: order.carbonCreditId,
          executionQuantity,
          executionPrice,
          transactionAmount,
          fee,
        },
      });
      
      // Send notifications
      await notificationService.createNotification({
        userId: buyerId!,
        title: "Order Matched",
        message: `Your buy order has been matched and executed for ${executionQuantity} carbon credits at ${executionPrice}.`,
        type: "TRANSACTION",
        priority: "HIGH",
        actionUrl: `/dashboard/orders/${order.side === OrderSide.BUY ? order.id : matchedOrder.id}`,
        actionLabel: "View Order",
      });
      
      await notificationService.createNotification({
        userId: sellerId!,
        title: "Order Matched",
        message: `Your sell order has been matched and executed for ${executionQuantity} carbon credits at ${executionPrice}.`,
        type: "TRANSACTION",
        priority: "HIGH",
        actionUrl: `/dashboard/orders/${order.side === OrderSide.SELL ? order.id : matchedOrder.id}`,
        actionLabel: "View Order",
      });
      
      return {
        success: true,
        message: `Order matched successfully: ${executionQuantity} @ ${executionPrice}`,
        order: updatedOrder,
        matchedOrder: updatedMatchedOrder,
        transaction,
      };
    } catch (error) {
      logger.error(`Error matching order ${orderId}:`, error);
      throw new Error(`Failed to match order: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Create a transaction
   * @param data Transaction creation data
   * @returns Created transaction
   */
  static async createTransaction(data: TransactionCreationData): Promise<Transaction> {
    try {
      logger.info(`Creating transaction: ${data.amount} for carbon credit ${data.carbonCreditId}`);
      
      // Create transaction
      const transaction = await db.transaction.create({
        data: {
          type: data.type,
          status: "COMPLETED",
          amount: data.amount,
          fee: data.fee,
          ...(data.orderId && {
            order: {
              connect: { id: data.orderId },
            },
          }),
          carbonCredit: {
            connect: { id: data.carbonCreditId },
          },
          ...(data.buyerId && {
            buyer: {
              connect: { id: data.buyerId },
            },
          }),
          ...(data.sellerId && {
            seller: {
              connect: { id: data.sellerId },
            },
          }),
        },
      });
      
      // Create audit log
      await auditManager.createAuditLog({
        type: "TRANSACTION_CREATED",
        description: `Transaction created: ${data.amount} for carbon credit ${data.carbonCreditId}`,
        userId: data.buyerId || data.sellerId!,
        metadata: {
          transactionId: transaction.id,
          type: data.type,
          amount: data.amount,
          fee: data.fee,
          carbonCreditId: data.carbonCreditId,
          orderId: data.orderId,
          buyerId: data.buyerId,
          sellerId: data.sellerId,
        },
      });
      
      return transaction;
    } catch (error) {
      logger.error(`Error creating transaction for carbon credit ${data.carbonCreditId}:`, error);
      throw new Error(`Failed to create transaction: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get transaction by ID
   * @param id Transaction ID
   * @returns Transaction
   */
  static async getTransactionById(id: string): Promise<Transaction | null> {
    try {
      return await db.transaction.findUnique({
        where: { id },
        include: {
          carbonCredit: true,
          order: true,
          buyer: {
            select: {
              id: true,
              name: true,
              email: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error getting transaction ${id}:`, error);
      throw new Error(`Failed to get transaction: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get transactions
   * @param filter Filter options
   * @returns Transactions
   */
  static async getTransactions(filter: {
    userId?: string;
    organizationId?: string;
    carbonCreditId?: string;
    orderId?: string;
    type?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}): Promise<any> {
    try {
      const {
        userId,
        organizationId,
        carbonCreditId,
        orderId,
        type,
        status,
        startDate,
        endDate,
        limit = 10,
        offset = 0,
      } = filter;
      
      // Build query
      const where: Prisma.TransactionWhereInput = {
        ...(carbonCreditId && { carbonCreditId }),
        ...(orderId && { orderId }),
        ...(type && { type }),
        ...(status && { status }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
      };
      
      // Add user filter
      if (userId) {
        where.OR = [
          { buyerId: userId },
          { sellerId: userId },
        ];
      }
      
      // Add organization filter
      if (organizationId) {
        where.OR = [
          { buyer: { organizationId } },
          { seller: { organizationId } },
        ];
      }
      
      // Get transactions
      const transactions = await db.transaction.findMany({
        where,
        include: {
          carbonCredit: {
            select: {
              id: true,
              projectId: true,
              vintage: true,
              standard: true,
              methodology: true,
            },
          },
          order: {
            select: {
              id: true,
              type: true,
              side: true,
              status: true,
              quantity: true,
              price: true,
            },
          },
          buyer: {
            select: {
              id: true,
              name: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          seller: {
            select: {
              id: true,
              name: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.transaction.count({ where });
      
      return {
        transactions,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting transactions:", error);
      throw new Error(`Failed to get transactions: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
