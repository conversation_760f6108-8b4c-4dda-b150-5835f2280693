import { OrderStatus, OrderType, OrderSide } from '@prisma/client';

/**
 * Order interface
 */
export interface Order {
  id: string;
  type: OrderType;
  side: OrderSide;
  status: OrderStatus;
  quantity: number;
  price: number;
  expiresAt?: Date;
  carbonCreditId: string;
  buyerId?: string;
  sellerId?: string;
  matchedOrderId?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Order creation data
 */
export interface OrderCreationData {
  type: OrderType;
  side: OrderSide;
  quantity: number;
  price: number;
  expiresAt?: Date;
  carbonCreditId: string;
  userId: string;
}

/**
 * Order update data
 */
export interface OrderUpdateData {
  quantity?: number;
  price?: number;
  expiresAt?: Date;
  status?: OrderStatus;
}

/**
 * Order match result
 */
export interface OrderMatchResult {
  success: boolean;
  message: string;
  order: Order;
  matchedOrder?: Order;
  transaction?: any;
}

/**
 * Order filter interface
 */
export interface OrderFilter {
  userId?: string;
  organizationId?: string;
  carbonCreditId?: string;
  type?: OrderType;
  side?: OrderSide;
  status?: OrderStatus;
  minPrice?: number;
  maxPrice?: number;
  minQuantity?: number;
  maxQuantity?: number;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Order pagination interface
 */
export interface OrderPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Order response interface
 */
export interface OrderResponse {
  orders: Order[];
  pagination: OrderPagination;
}

/**
 * Order book entry
 */
export interface OrderBookEntry {
  price: number;
  quantity: number;
  orderCount: number;
}

/**
 * Order book
 */
export interface OrderBook {
  carbonCreditId: string;
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
  spread: number;
  lastPrice?: number;
  lastUpdated: Date;
}

/**
 * Market summary
 */
export interface MarketSummary {
  carbonCreditId: string;
  lastPrice?: number;
  highPrice?: number;
  lowPrice?: number;
  volume: number;
  volumeChange: number;
  percentChange: number;
  orderCount: number;
  lastUpdated: Date;
}

/**
 * Transaction interface
 */
export interface Transaction {
  id: string;
  type: 'TRADE' | 'FEE' | 'REFUND';
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  amount: number;
  fee: number;
  orderId?: string;
  carbonCreditId: string;
  buyerId?: string;
  sellerId?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Transaction creation data
 */
export interface TransactionCreationData {
  type: 'TRADE' | 'FEE' | 'REFUND';
  amount: number;
  fee: number;
  orderId?: string;
  carbonCreditId: string;
  buyerId?: string;
  sellerId?: string;
}
