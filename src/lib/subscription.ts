import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { SubscriptionPlan, SubscriptionStatus } from "@prisma/client";
import { auditService } from "@/lib/audit";

/**
 * Subscription plan features and limits
 */
export const SUBSCRIPTION_PLANS = {
  FREE: {
    name: "Free",
    price: 0,
    billingCycle: "monthly",
    features: {
      maxUsers: 2,
      maxCarbonCredits: 5,
      maxWallets: 1,
      supportedNetworks: 1,
      customFees: false,
      analytics: false,
      apiAccess: false,
      support: "email",
    },
  },
  BASIC: {
    name: "Basic",
    price: 49,
    billingCycle: "monthly",
    features: {
      maxUsers: 5,
      maxCarbonCredits: 20,
      maxWallets: 3,
      supportedNetworks: 2,
      customFees: false,
      analytics: true,
      apiAccess: false,
      support: "email",
    },
  },
  PREMIUM: {
    name: "Premium",
    price: 199,
    billingCycle: "monthly",
    features: {
      maxUsers: 15,
      maxCarbonCredits: 100,
      maxWallets: 10,
      supportedNetworks: 5,
      customFees: true,
      analytics: true,
      apiAccess: true,
      support: "priority",
    },
  },
  ENTERPRISE: {
    name: "Enterprise",
    price: 499,
    billingCycle: "monthly",
    features: {
      maxUsers: -1, // Unlimited
      maxCarbonCredits: -1, // Unlimited
      maxWallets: -1, // Unlimited
      supportedNetworks: -1, // All supported networks
      customFees: true,
      analytics: true,
      apiAccess: true,
      support: "dedicated",
    },
  },
};

/**
 * Subscription service for managing organization subscriptions
 */
export class SubscriptionService {
  /**
   * Create a new subscription
   * @param organizationId Organization ID
   * @param plan Subscription plan
   * @param userId User ID who created the subscription
   * @returns Created subscription
   */
  async createSubscription(
    organizationId: string,
    plan: SubscriptionPlan = SubscriptionPlan.FREE,
    userId: string
  ) {
    try {
      // Check if organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: { subscription: true },
      });

      if (!organization) {
        throw new Error("Organization not found");
      }

      // Check if organization already has a subscription
      if (organization.subscription) {
        throw new Error("Organization already has a subscription");
      }

      // Calculate end date (1 month from now for monthly subscriptions)
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 1);

      // Create subscription
      const subscription = await db.subscription.create({
        data: {
          plan,
          endDate,
          status: SubscriptionStatus.ACTIVE,
          organization: {
            connect: { id: organizationId },
          },
        },
      });

      // Log subscription creation
      await auditService.log(
        "SUBSCRIPTION_CREATED",
        `Subscription created: ${plan}`,
        { organizationId, plan },
        userId,
        organizationId
      );

      logger.info(`Subscription created for organization ${organizationId}: ${plan}`);

      return subscription;
    } catch (error) {
      logger.error(`Error creating subscription for organization ${organizationId}:`, error);
      throw new Error("Failed to create subscription");
    }
  }

  /**
   * Update an existing subscription
   * @param subscriptionId Subscription ID
   * @param plan New subscription plan
   * @param userId User ID who updated the subscription
   * @returns Updated subscription
   */
  async updateSubscription(
    subscriptionId: string,
    plan: SubscriptionPlan,
    userId: string
  ) {
    try {
      // Check if subscription exists
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: { organization: true },
      });

      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // Update subscription
      const updatedSubscription = await db.subscription.update({
        where: { id: subscriptionId },
        data: {
          plan,
          // Reset end date if changing plan
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        },
      });

      // Log subscription update
      await auditService.log(
        "SUBSCRIPTION_UPDATED",
        `Subscription updated: ${plan}`,
        { subscriptionId, plan },
        userId,
        subscription.organizationId
      );

      logger.info(`Subscription updated for organization ${subscription.organizationId}: ${plan}`);

      return updatedSubscription;
    } catch (error) {
      logger.error(`Error updating subscription ${subscriptionId}:`, error);
      throw new Error("Failed to update subscription");
    }
  }

  /**
   * Cancel a subscription
   * @param subscriptionId Subscription ID
   * @param userId User ID who cancelled the subscription
   * @returns Cancelled subscription
   */
  async cancelSubscription(subscriptionId: string, userId: string) {
    try {
      // Check if subscription exists
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: { organization: true },
      });

      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // Update subscription status
      const cancelledSubscription = await db.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: SubscriptionStatus.CANCELLED,
        },
      });

      // Log subscription cancellation
      await auditService.log(
        "SUBSCRIPTION_UPDATED",
        "Subscription cancelled",
        { subscriptionId },
        userId,
        subscription.organizationId
      );

      logger.info(`Subscription cancelled for organization ${subscription.organizationId}`);

      return cancelledSubscription;
    } catch (error) {
      logger.error(`Error cancelling subscription ${subscriptionId}:`, error);
      throw new Error("Failed to cancel subscription");
    }
  }

  /**
   * Renew a subscription
   * @param subscriptionId Subscription ID
   * @param userId User ID who renewed the subscription
   * @returns Renewed subscription
   */
  async renewSubscription(subscriptionId: string, userId: string) {
    try {
      // Check if subscription exists
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: { organization: true },
      });

      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // Calculate new end date (1 month from current end date)
      const newEndDate = new Date(subscription.endDate || Date.now());
      newEndDate.setMonth(newEndDate.getMonth() + 1);

      // Update subscription
      const renewedSubscription = await db.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: SubscriptionStatus.ACTIVE,
          endDate: newEndDate,
        },
      });

      // Log subscription renewal
      await auditService.log(
        "SUBSCRIPTION_UPDATED",
        "Subscription renewed",
        { subscriptionId, newEndDate },
        userId,
        subscription.organizationId
      );

      logger.info(`Subscription renewed for organization ${subscription.organizationId}`);

      return renewedSubscription;
    } catch (error) {
      logger.error(`Error renewing subscription ${subscriptionId}:`, error);
      throw new Error("Failed to renew subscription");
    }
  }

  /**
   * Check if an organization has access to a feature
   * @param organizationId Organization ID
   * @param feature Feature to check
   * @returns Whether the organization has access to the feature
   */
  async hasFeatureAccess(organizationId: string, feature: string) {
    try {
      // Get organization with subscription
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: { subscription: true },
      });

      if (!organization) {
        throw new Error("Organization not found");
      }

      // If no subscription, use FREE plan
      const plan = organization.subscription?.plan || SubscriptionPlan.FREE;
      const planFeatures = SUBSCRIPTION_PLANS[plan].features;

      // Check if feature exists and is enabled
      return feature in planFeatures && planFeatures[feature];
    } catch (error) {
      logger.error(`Error checking feature access for organization ${organizationId}:`, error);
      return false;
    }
  }

  /**
   * Check if an organization is within its limits
   * @param organizationId Organization ID
   * @param limitType Limit type to check
   * @param currentCount Current count (optional, will be fetched if not provided)
   * @returns Whether the organization is within its limits
   */
  async isWithinLimits(
    organizationId: string,
    limitType: "maxUsers" | "maxCarbonCredits" | "maxWallets",
    currentCount?: number
  ) {
    try {
      // Get organization with subscription
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: { subscription: true },
      });

      if (!organization) {
        throw new Error("Organization not found");
      }

      // If no subscription, use FREE plan
      const plan = organization.subscription?.plan || SubscriptionPlan.FREE;
      const planLimits = SUBSCRIPTION_PLANS[plan].features;

      // If limit is -1, it's unlimited
      if (planLimits[limitType] === -1) {
        return true;
      }

      // If current count is not provided, fetch it
      if (currentCount === undefined) {
        switch (limitType) {
          case "maxUsers":
            currentCount = await db.user.count({
              where: { organizationId },
            });
            break;
          case "maxCarbonCredits":
            currentCount = await db.carbonCredit.count({
              where: { organizationId },
            });
            break;
          case "maxWallets":
            currentCount = await db.wallet.count({
              where: { organizationId },
            });
            break;
        }
      }

      // Check if within limits
      return currentCount < planLimits[limitType];
    } catch (error) {
      logger.error(`Error checking limits for organization ${organizationId}:`, error);
      return false;
    }
  }

  /**
   * Get subscription details
   * @param subscriptionId Subscription ID
   * @returns Subscription details with plan features
   */
  async getSubscriptionDetails(subscriptionId: string) {
    try {
      // Get subscription
      const subscription = await db.subscription.findUnique({
        where: { id: subscriptionId },
        include: { organization: true },
      });

      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // Get plan details
      const planDetails = SUBSCRIPTION_PLANS[subscription.plan];

      return {
        ...subscription,
        planDetails,
      };
    } catch (error) {
      logger.error(`Error getting subscription details for ${subscriptionId}:`, error);
      throw new Error("Failed to get subscription details");
    }
  }
}

// Export singleton instance
export const subscriptionService = new SubscriptionService();
