import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { NotificationType, NotificationPriority } from "@prisma/client";
import { emailService } from "@/lib/email";

/**
 * Notification delivery channel
 */
export enum NotificationChannel {
  IN_APP = "in_app",
  EMAIL = "email",
  SMS = "sms",
  WEBHOOK = "webhook",
}

/**
 * Notification service for managing user notifications
 */
export class NotificationService {
  /**
   * Create a new notification
   * @param userId User ID
   * @param title Notification title
   * @param message Notification message
   * @param type Notification type
   * @param channels Delivery channels
   * @param metadata Additional metadata
   * @param actionUrl Optional URL for action button
   * @param actionLabel Optional label for action button
   * @param priority Optional priority level
   * @returns Created notification
   */
  async createNotification(
    userId: string,
    title: string,
    message: string,
    type: NotificationType = NotificationType.SYSTEM,
    channels: NotificationChannel[] = [NotificationChannel.IN_APP],
    metadata?: any,
    actionUrl?: string,
    actionLabel?: string,
    priority: NotificationPriority = NotificationPriority.NORMAL
  ) {
    try {
      // Create in-app notification if requested
      let notification;
      if (channels.includes(NotificationChannel.IN_APP)) {
        // Get user's organization ID for the notification
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { organizationId: true },
        });

        notification = await db.notification.create({
          data: {
            title,
            message,
            type,
            priority,
            metadata: metadata || {},
            actionUrl,
            actionLabel,
            user: {
              connect: { id: userId },
            },
            ...(user?.organizationId && {
              organization: {
                connect: { id: user.organizationId },
              },
            }),
          },
        });
      }

      // Get user details for other notification channels if we haven't already fetched them
      const user = await db.user.findUnique({
        where: { id: userId },
        select: {
          email: true,
          name: true,
          organizationId: true,
          organization: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Send email notification if requested
      if (channels.includes(NotificationChannel.EMAIL) && user.email) {
        await this.sendEmailNotification(
          user.email,
          user.name || "User",
          title,
          message,
          type,
          metadata,
          actionUrl,
          actionLabel
        );
      }

      // Send SMS notification if requested
      if (channels.includes(NotificationChannel.SMS)) {
        // This would be implemented with a SMS service like Twilio
        // await this.sendSmsNotification(user.phone, message);
        logger.info(`SMS notification would be sent to user ${userId}`);
      }

      // Send webhook notification if requested
      if (channels.includes(NotificationChannel.WEBHOOK)) {
        // This would be implemented with organization-specific webhooks
        // await this.sendWebhookNotification(user.organizationId, title, message, type, metadata);
        logger.info(`Webhook notification would be sent for user ${userId}`);
      }

      logger.info(`Notification created for user ${userId}: ${title}`);

      return notification;
    } catch (error) {
      logger.error(`Error creating notification for user ${userId}:`, error);
      throw new Error("Failed to create notification");
    }
  }

  /**
   * Send email notification
   * @param email Recipient email
   * @param name Recipient name
   * @param title Notification title
   * @param message Notification message
   * @param type Notification type
   * @param metadata Additional metadata
   * @param actionUrl Optional URL for action button
   * @param actionLabel Optional label for action button
   */
  private async sendEmailNotification(
    email: string,
    name: string,
    title: string,
    message: string,
    type: NotificationType,
    metadata?: any,
    actionUrl?: string,
    actionLabel?: string
  ) {
    try {
      // Create email content based on notification type
      let subject = title;
      let htmlContent = `<p>Hello ${name},</p><p>${message}</p>`;

      // Add action button if URL and label are provided
      const actionButton = actionUrl && actionLabel
        ? `<p><a href="${actionUrl}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">${actionLabel}</a></p>`
        : '';

      // Add specific content based on notification type
      switch (type) {
        case NotificationType.ORDER:
          subject = `Order Update: ${title}`;
          htmlContent = `
            <p>Hello ${name},</p>
            <p>There's an update regarding your order:</p>
            <p><strong>${title}</strong></p>
            <p>${message}</p>
            ${actionButton}
            <p>You can view more details in your dashboard.</p>
          `;
          break;
        case NotificationType.TRANSACTION:
          subject = `Transaction Update: ${title}`;
          htmlContent = `
            <p>Hello ${name},</p>
            <p>There's an update regarding your transaction:</p>
            <p><strong>${title}</strong></p>
            <p>${message}</p>
            ${actionButton}
            <p>You can view more details in your dashboard.</p>
          `;
          break;
        case NotificationType.CREDIT:
          subject = `Carbon Credit Update: ${title}`;
          htmlContent = `
            <p>Hello ${name},</p>
            <p>There's an update regarding your carbon credits:</p>
            <p><strong>${title}</strong></p>
            <p>${message}</p>
            ${actionButton}
            <p>You can view more details in your dashboard.</p>
          `;
          break;
      }

      // Send email
      await emailService.sendEmail({
        to: email,
        subject,
        html: htmlContent,
      });

      logger.info(`Email notification sent to ${email}: ${title}`);
    } catch (error) {
      logger.error(`Error sending email notification to ${email}:`, error);
      // Don't throw error to prevent disrupting the main flow
    }
  }

  /**
   * Mark notification as read
   * @param notificationId Notification ID
   * @param userId User ID
   * @returns Updated notification
   */
  async markAsRead(notificationId: string, userId: string) {
    try {
      // Check if notification exists and belongs to the user
      const notification = await db.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!notification) {
        throw new Error("Notification not found");
      }

      // Update notification
      const updatedNotification = await db.notification.update({
        where: { id: notificationId },
        data: { read: true },
      });

      logger.info(`Notification ${notificationId} marked as read by user ${userId}`);

      return updatedNotification;
    } catch (error) {
      logger.error(`Error marking notification ${notificationId} as read:`, error);
      throw new Error("Failed to mark notification as read");
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId User ID
   * @returns Number of notifications marked as read
   */
  async markAllAsRead(userId: string) {
    try {
      // Update all unread notifications for the user
      const result = await db.notification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: { read: true },
      });

      logger.info(`${result.count} notifications marked as read for user ${userId}`);

      return result.count;
    } catch (error) {
      logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      throw new Error("Failed to mark all notifications as read");
    }
  }

  /**
   * Delete a notification
   * @param notificationId Notification ID
   * @param userId User ID
   * @returns Deleted notification
   */
  async deleteNotification(notificationId: string, userId: string) {
    try {
      // Check if notification exists and belongs to the user
      const notification = await db.notification.findFirst({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (!notification) {
        throw new Error("Notification not found");
      }

      // Delete notification
      await db.notification.delete({
        where: { id: notificationId },
      });

      logger.info(`Notification ${notificationId} deleted by user ${userId}`);

      return notification;
    } catch (error) {
      logger.error(`Error deleting notification ${notificationId}:`, error);
      throw new Error("Failed to delete notification");
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param limit Maximum number of notifications to return
   * @param offset Offset for pagination
   * @param unreadOnly Whether to return only unread notifications
   * @returns Notifications
   */
  async getNotifications(
    userId: string,
    limit: number = 20,
    offset: number = 0,
    unreadOnly: boolean = false
  ) {
    try {
      // Build where clause
      const where: any = { userId };
      if (unreadOnly) {
        where.read = false;
      }

      // Get notifications
      const notifications = await db.notification.findMany({
        where,
        orderBy: { createdAt: "desc" },
        take: limit,
        skip: offset,
      });

      // Get total count
      const totalCount = await db.notification.count({ where });
      const unreadCount = await db.notification.count({
        where: { userId, read: false },
      });

      logger.info(`Retrieved ${notifications.length} notifications for user ${userId}`);

      return {
        notifications,
        pagination: {
          total: totalCount,
          unread: unreadCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount,
        },
      };
    } catch (error) {
      logger.error(`Error getting notifications for user ${userId}:`, error);
      throw new Error("Failed to get notifications");
    }
  }

  /**
   * Create a system notification for all users in an organization
   * @param organizationId Organization ID
   * @param title Notification title
   * @param message Notification message
   * @param channels Delivery channels
   * @param metadata Additional metadata
   * @param actionUrl Optional URL for action button
   * @param actionLabel Optional label for action button
   * @param priority Optional priority level
   * @returns Number of notifications created
   */
  async notifyOrganization(
    organizationId: string,
    title: string,
    message: string,
    channels: NotificationChannel[] = [NotificationChannel.IN_APP],
    metadata?: any,
    actionUrl?: string,
    actionLabel?: string,
    priority: NotificationPriority = NotificationPriority.NORMAL
  ) {
    try {
      // Get all users in the organization
      const users = await db.user.findMany({
        where: { organizationId },
        select: { id: true },
      });

      // Create notifications for each user
      const notificationPromises = users.map((user) =>
        this.createNotification(
          user.id,
          title,
          message,
          NotificationType.SYSTEM,
          channels,
          metadata,
          actionUrl,
          actionLabel,
          priority
        )
      );

      await Promise.all(notificationPromises);

      logger.info(
        `Created ${users.length} notifications for organization ${organizationId}: ${title}`
      );

      return users.length;
    } catch (error) {
      logger.error(
        `Error creating notifications for organization ${organizationId}:`,
        error
      );
      throw new Error("Failed to create organization notifications");
    }
  }

  /**
   * Create a system notification for all users
   * @param title Notification title
   * @param message Notification message
   * @param channels Delivery channels
   * @param metadata Additional metadata
   * @param actionUrl Optional URL for action button
   * @param actionLabel Optional label for action button
   * @param priority Optional priority level
   * @returns Number of notifications created
   */
  async notifyAllUsers(
    title: string,
    message: string,
    channels: NotificationChannel[] = [NotificationChannel.IN_APP],
    metadata?: any,
    actionUrl?: string,
    actionLabel?: string,
    priority: NotificationPriority = NotificationPriority.NORMAL
  ) {
    try {
      // Get all users
      const users = await db.user.findMany({
        select: { id: true },
      });

      // Create notifications for each user
      const notificationPromises = users.map((user) =>
        this.createNotification(
          user.id,
          title,
          message,
          NotificationType.SYSTEM,
          channels,
          metadata,
          actionUrl,
          actionLabel,
          priority
        )
      );

      await Promise.all(notificationPromises);

      logger.info(`Created ${users.length} notifications for all users: ${title}`);

      return users.length;
    } catch (error) {
      logger.error(`Error creating notifications for all users:`, error);
      throw new Error("Failed to create notifications for all users");
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
