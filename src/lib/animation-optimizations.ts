/**
 * Animation optimization utilities
 * This file contains utilities for optimizing animations and respecting user preferences
 */

import { useEffect, useState } from 'react';
import { useReducedMotion } from 'framer-motion';

/**
 * Hook to check if the browser supports the will-change property
 * @returns Whether the browser supports will-change
 */
export function useSupportsWillChange() {
  const [supportsWillChange, setSupportsWillChange] = useState(false);

  useEffect(() => {
    // Check if the browser supports will-change
    const testEl = document.createElement('div');
    if ('willChange' in testEl.style) {
      setSupportsWillChange(true);
    }
  }, []);

  return supportsWillChange;
}

/**
 * Hook to optimize animations based on device performance
 * @returns Optimized animation settings
 */
export function useOptimizedAnimations() {
  const prefersReducedMotion = useReducedMotion();
  const supportsWillChange = useSupportsWillChange();
  const [isLowPowerMode, setIsLowPowerMode] = useState(false);
  const [isLowPerformanceDevice, setIsLowPerformanceDevice] = useState(false);

  useEffect(() => {
    // Check if the device is likely to be low performance
    const isLowPerformance = () => {
      // Check for low memory (if available)
      if ('deviceMemory' in navigator) {
        // @ts-ignore - deviceMemory is not in the TypeScript types yet
        if (navigator.deviceMemory < 4) {
          return true;
        }
      }

      // Check for low CPU cores (if available)
      if ('hardwareConcurrency' in navigator) {
        if (navigator.hardwareConcurrency < 4) {
          return true;
        }
      }

      // Check if it's a mobile device (simplified check)
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );

      // Mobile devices are more likely to have performance constraints
      if (isMobile) {
        return true;
      }

      return false;
    };

    setIsLowPerformanceDevice(isLowPerformance());

    // Try to detect low power mode (not directly possible, but we can make educated guesses)
    // For example, check battery status if available
    if ('getBattery' in navigator) {
      // @ts-ignore - getBattery is not in the TypeScript types yet
      navigator.getBattery().then((battery) => {
        // If battery is discharging and below 20%, assume low power mode
        if (battery.discharging && battery.level < 0.2) {
          setIsLowPowerMode(true);
        }

        // Listen for battery changes
        battery.addEventListener('levelchange', () => {
          setIsLowPowerMode(battery.discharging && battery.level < 0.2);
        });
      });
    }
  }, []);

  // Determine if animations should be simplified
  const shouldSimplifyAnimations = prefersReducedMotion || isLowPowerMode || isLowPerformanceDevice;

  // Return optimized settings
  return {
    prefersReducedMotion,
    isLowPowerMode,
    isLowPerformanceDevice,
    shouldSimplifyAnimations,
    willChange: supportsWillChange ? 'transform, opacity' : undefined,
    // Reduce animation durations for low-performance devices
    durationMultiplier: isLowPerformanceDevice ? 0.7 : 1,
    // Disable staggered animations for low-performance devices
    enableStaggering: !isLowPerformanceDevice && !prefersReducedMotion,
  };
}

/**
 * Hook to lazy load animations
 * @param threshold Intersection threshold (0-1)
 * @returns Ref and inView status
 */
export function useLazyAnimation(threshold = 0.1) {
  const [ref, setRef] = useState<HTMLElement | null>(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setInView(entry.isIntersecting);
      },
      {
        threshold,
        rootMargin: '100px', // Start loading a bit before the element is in view
      }
    );

    observer.observe(ref);

    return () => {
      observer.disconnect();
    };
  }, [ref, threshold]);

  return { ref: setRef, inView };
}

/**
 * Hook to get animation properties based on user preferences and device capabilities
 * @returns Animation properties
 */
export function useAnimationProperties() {
  const {
    shouldSimplifyAnimations,
    willChange,
    durationMultiplier,
    enableStaggering,
  } = useOptimizedAnimations();

  return {
    // If animations should be simplified, return simplified properties
    transition: shouldSimplifyAnimations
      ? {
          duration: 0.2 * durationMultiplier,
          ease: 'easeOut',
        }
      : {
          type: 'spring',
          stiffness: 400,
          damping: 17,
          mass: 1,
        },
    // Add will-change for better performance on supported browsers
    style: willChange ? { willChange } : undefined,
    // Disable staggering if needed
    staggerChildren: enableStaggering ? 0.05 : 0,
  };
}

/**
 * Get optimized animation properties for form components
 * This helps prevent focus loss during animations
 * @returns Animation properties for form components
 */
export function getFormAnimationProps(isFocused = false) {
  const { shouldSimplifyAnimations, durationMultiplier } = useOptimizedAnimations();

  // When an element is focused, use minimal animations to prevent focus loss
  if (isFocused) {
    return {
      // Minimal animations for focused form components
      initial: { opacity: 0.98 },
      animate: {
        opacity: 1,
        transition: { duration: 0.1 * durationMultiplier }
      },
      exit: {
        opacity: 0.98,
        transition: { duration: 0.1 * durationMultiplier }
      },
      // No layout animations for focused elements
      layout: false,
    };
  }

  // For non-focused elements, use normal or simplified animations
  if (shouldSimplifyAnimations) {
    return {
      // Minimal animations for form components when simplified
      initial: { opacity: 0.95 },
      animate: {
        opacity: 1,
        transition: { duration: 0.15 * durationMultiplier }
      },
      exit: {
        opacity: 0.95,
        transition: { duration: 0.1 * durationMultiplier }
      },
      // No layout animations for simplified mode
      layout: false,
    };
  }

  return {
    // Normal animations for form components
    initial: { opacity: 0, y: 5 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.2 * durationMultiplier,
        ease: [0.25, 1.2, 0.5, 1] // Gentle spring
      }
    },
    exit: {
      opacity: 0,
      y: 5,
      transition: {
        duration: 0.15 * durationMultiplier,
        ease: [0.4, 0, 0.2, 1] // Gentle ease out
      }
    },
    // Use layout="position" for smoother animations
    layout: "position",
  };
}
