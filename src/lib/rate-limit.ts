import { NextRequest, NextResponse } from "next/server";
import { logger } from "@/lib/logger";

interface RateLimitOptions {
  limit: number;
  windowMs: number;
  identifier?: string; // Optional custom identifier
}

// In-memory store for rate limiting
// In a production environment, you would use Redis or another distributed store
const rateLimitStore = new Map<string, { count: number; resetTime: number; lastRequest: number }>();

// Clean up the rate limit store periodically to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of rateLimitStore.entries()) {
    // Remove entries that haven't been accessed in the last hour
    if (now - data.lastRequest > 60 * 60 * 1000) {
      rateLimitStore.delete(key);
    }
  }
}, 15 * 60 * 1000); // Run every 15 minutes

/**
 * Rate limiting middleware
 *
 * @param req The request object
 * @param options Rate limiting options
 * @returns NextResponse if rate limit exceeded, null otherwise
 */
export async function rateLimit(
  req: NextRequest,
  options: RateLimitOptions = { limit: 10, windowMs: 60 * 1000 }
): Promise<NextResponse | null> {
  try {
    // Get the identifier for this request (IP by default)
    const identifier = options.identifier || getRequestIdentifier(req);

    // Get the current time
    const now = Date.now();

    // Get the rate limit data for this identifier
    const rateLimitData = rateLimitStore.get(identifier);

    if (!rateLimitData) {
      // First request from this identifier
      rateLimitStore.set(identifier, {
        count: 1,
        resetTime: now + options.windowMs,
        lastRequest: now,
      });
      return null;
    }

    if (now > rateLimitData.resetTime) {
      // Reset the rate limit
      rateLimitStore.set(identifier, {
        count: 1,
        resetTime: now + options.windowMs,
        lastRequest: now,
      });
      return null;
    }

    // Increment the request count and update last request time
    rateLimitData.count += 1;
    rateLimitData.lastRequest = now;
    rateLimitStore.set(identifier, rateLimitData);

    // Check if the rate limit has been exceeded
    if (rateLimitData.count > options.limit) {
      const retryAfterSeconds = Math.ceil((rateLimitData.resetTime - now) / 1000);

      // Log rate limit exceeded
      logger.warn(`Rate limit exceeded for ${identifier}. Count: ${rateLimitData.count}, Limit: ${options.limit}`);

      return NextResponse.json(
        {
          error: "Too many requests, please try again later",
          retryAfter: retryAfterSeconds,
        },
        {
          status: 429,
          headers: {
            "Retry-After": `${retryAfterSeconds}`,
            "X-RateLimit-Limit": `${options.limit}`,
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": `${Math.ceil(rateLimitData.resetTime / 1000)}`,
          },
        }
      );
    }

    // Add rate limit headers to successful responses
    const remaining = options.limit - rateLimitData.count;
    req.headers.set("X-RateLimit-Limit", `${options.limit}`);
    req.headers.set("X-RateLimit-Remaining", `${remaining}`);
    req.headers.set("X-RateLimit-Reset", `${Math.ceil(rateLimitData.resetTime / 1000)}`);

    return null;
  } catch (error) {
    // Log the error but don't block the request
    logger.error("Rate limiting error", error);
    return null;
  }
}

/**
 * Get a unique identifier for the request
 * @param req The request object
 * @returns A unique identifier for the request
 */
function getRequestIdentifier(req: NextRequest): string {
  // Get the IP address
  const ip = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "anonymous";

  // For API routes, include the path to separate rate limits by endpoint
  const path = req.nextUrl.pathname;
  if (path.startsWith("/api/")) {
    // Extract the main API endpoint (e.g., /api/users, /api/auth)
    const apiEndpoint = path.split("/").slice(0, 3).join("/");
    return `${ip}:${apiEndpoint}`;
  }

  return ip;
}

/**
 * Apply different rate limits based on the user's role
 * @param req The request object
 * @param userRole The user's role
 * @param path The request path
 * @returns Rate limit options
 */
export function getRoleLimits(req: NextRequest, userRole?: string): RateLimitOptions {
  const path = req.nextUrl.pathname;

  // Higher limits for authenticated users
  if (userRole) {
    // Admin users get higher limits
    if (userRole === "ADMIN") {
      return { limit: 100, windowMs: 60 * 1000 };
    }

    // Organization admins get medium limits
    if (userRole === "ORGANIZATION_ADMIN") {
      return { limit: 50, windowMs: 60 * 1000 };
    }

    // Regular authenticated users
    return { limit: 30, windowMs: 60 * 1000 };
  }

  // Stricter limits for authentication endpoints
  if (path.startsWith("/api/auth")) {
    // Higher limits in development mode
    if (process.env.NODE_ENV === "development") {
      return { limit: 100, windowMs: 60 * 1000 }; // 100 requests per minute in development
    }
    return { limit: 5, windowMs: 60 * 1000 }; // 5 requests per minute in production
  }

  // Default limits for unauthenticated users
  return { limit: 20, windowMs: 60 * 1000 };
}
