import { ethers } from "ethers";
import { Alchemy } from "alchemy-sdk";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { getCarbonCreditContractAddress } from "@/lib/blockchain";

// Carbon Credit Token ABI (only the functions we need for gas estimation)
const CARBON_CREDIT_ABI = [
  "function mint(address to, uint256 id, uint256 amount, bytes calldata data)",
  "function safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes calldata data)",
  "function retire(uint256 id, uint256 amount)",
  "function setURI(uint256 id, string calldata newuri)",
];

/**
 * Gas estimation service for blockchain transactions
 */
export class GasEstimationService {
  /**
   * Estimate gas for tokenizing a carbon credit
   * @param tokenId Token ID
   * @param amount Amount to tokenize
   * @param ownerAddress Owner wallet address
   * @param metadata Metadata for the carbon credit
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   * @returns Gas estimation details
   */
  async estimateTokenizationGas(
    tokenId: number,
    amount: number,
    ownerAddress: string,
    metadata: {
      projectId: string;
      vintage: number;
      standard: string;
      methodology: string;
    },
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true
  ) {
    try {
      logger.info(`Estimating gas for tokenizing ${amount} tokens with ID ${tokenId} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);
      
      // Get the network configuration
      const networkConfig = getNetworkConfig(network, useTestnet);
      
      // Create a provider
      const provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);
      
      // Get the contract address
      const contractAddress = getCarbonCreditContractAddress(network, useTestnet);
      
      // Create the contract interface
      const contractInterface = new ethers.Interface(CARBON_CREDIT_ABI);
      
      // Encode the function data for minting
      const mintData = contractInterface.encodeFunctionData("mint", [
        ownerAddress,
        tokenId,
        ethers.parseUnits(amount.toString(), 0), // Amount as a whole number
        ethers.toUtf8Bytes(JSON.stringify(metadata)),
      ]);
      
      // Estimate gas for minting
      const mintGasEstimate = await provider.estimateGas({
        to: contractAddress,
        data: mintData,
        from: ownerAddress,
      });
      
      // Encode the function data for setting the URI
      const uriData = contractInterface.encodeFunctionData("setURI", [
        tokenId,
        `https://carbon-exchange.com/api/carbon-credits/${metadata.projectId}/metadata`,
      ]);
      
      // Estimate gas for setting the URI
      const uriGasEstimate = await provider.estimateGas({
        to: contractAddress,
        data: uriData,
        from: ownerAddress,
      });
      
      // Get the current gas price
      const feeData = await provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits("50", "gwei"); // Default to 50 gwei if not available
      
      // Calculate the total gas cost
      const totalGas = mintGasEstimate + uriGasEstimate;
      const totalGasCost = totalGas * gasPrice;
      
      // Convert gas cost to ETH
      const gasCostEth = ethers.formatEther(totalGasCost);
      
      // Get the native currency price in USD (this would typically come from an API)
      const nativeCurrencyPrice = await this.getNativeCurrencyPrice(network);
      
      // Calculate the gas cost in USD
      const gasCostUsd = parseFloat(gasCostEth) * nativeCurrencyPrice;
      
      return {
        gasEstimate: totalGas.toString(),
        gasPrice: ethers.formatUnits(gasPrice, "gwei"),
        totalGasCost: gasCostEth,
        totalGasCostUsd: gasCostUsd.toFixed(2),
        nativeCurrency: networkConfig.nativeCurrency.symbol,
        network,
        operations: [
          {
            name: "Mint Tokens",
            gasEstimate: mintGasEstimate.toString(),
          },
          {
            name: "Set Token URI",
            gasEstimate: uriGasEstimate.toString(),
          },
        ],
      };
    } catch (error) {
      logger.error("Error estimating tokenization gas:", error);
      throw new Error(`Failed to estimate tokenization gas: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Estimate gas for transferring a carbon credit
   * @param fromAddress Sender address
   * @param toAddress Recipient address
   * @param tokenId Token ID
   * @param amount Amount to transfer
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   * @returns Gas estimation details
   */
  async estimateTransferGas(
    fromAddress: string,
    toAddress: string,
    tokenId: number,
    amount: number,
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true
  ) {
    try {
      logger.info(`Estimating gas for transferring ${amount} tokens with ID ${tokenId} from ${fromAddress} to ${toAddress} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);
      
      // Get the network configuration
      const networkConfig = getNetworkConfig(network, useTestnet);
      
      // Create a provider
      const provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);
      
      // Get the contract address
      const contractAddress = getCarbonCreditContractAddress(network, useTestnet);
      
      // Create the contract interface
      const contractInterface = new ethers.Interface(CARBON_CREDIT_ABI);
      
      // Encode the function data for transferring
      const data = contractInterface.encodeFunctionData("safeTransferFrom", [
        fromAddress,
        toAddress,
        tokenId,
        ethers.parseUnits(amount.toString(), 0), // Amount as a whole number
        "0x", // No additional data
      ]);
      
      // Estimate gas
      const gasEstimate = await provider.estimateGas({
        to: contractAddress,
        data,
        from: fromAddress,
      });
      
      // Get the current gas price
      const feeData = await provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits("50", "gwei"); // Default to 50 gwei if not available
      
      // Calculate the total gas cost
      const totalGasCost = gasEstimate * gasPrice;
      
      // Convert gas cost to ETH
      const gasCostEth = ethers.formatEther(totalGasCost);
      
      // Get the native currency price in USD (this would typically come from an API)
      const nativeCurrencyPrice = await this.getNativeCurrencyPrice(network);
      
      // Calculate the gas cost in USD
      const gasCostUsd = parseFloat(gasCostEth) * nativeCurrencyPrice;
      
      return {
        gasEstimate: gasEstimate.toString(),
        gasPrice: ethers.formatUnits(gasPrice, "gwei"),
        totalGasCost: gasCostEth,
        totalGasCostUsd: gasCostUsd.toFixed(2),
        nativeCurrency: networkConfig.nativeCurrency.symbol,
        network,
        operations: [
          {
            name: "Transfer Tokens",
            gasEstimate: gasEstimate.toString(),
          },
        ],
      };
    } catch (error) {
      logger.error("Error estimating transfer gas:", error);
      throw new Error(`Failed to estimate transfer gas: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Estimate gas for retiring a carbon credit
   * @param ownerAddress Owner address
   * @param tokenId Token ID
   * @param amount Amount to retire
   * @param network Network to use
   * @param useTestnet Whether to use testnet
   * @returns Gas estimation details
   */
  async estimateRetirementGas(
    ownerAddress: string,
    tokenId: number,
    amount: number,
    network: SupportedNetwork = SupportedNetwork.POLYGON,
    useTestnet: boolean = true
  ) {
    try {
      logger.info(`Estimating gas for retiring ${amount} tokens with ID ${tokenId} for ${ownerAddress} on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`);
      
      // Get the network configuration
      const networkConfig = getNetworkConfig(network, useTestnet);
      
      // Create a provider
      const provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);
      
      // Get the contract address
      const contractAddress = getCarbonCreditContractAddress(network, useTestnet);
      
      // Create the contract interface
      const contractInterface = new ethers.Interface(CARBON_CREDIT_ABI);
      
      // Encode the function data for retiring
      const data = contractInterface.encodeFunctionData("retire", [
        tokenId,
        ethers.parseUnits(amount.toString(), 0), // Amount as a whole number
      ]);
      
      // Estimate gas
      const gasEstimate = await provider.estimateGas({
        to: contractAddress,
        data,
        from: ownerAddress,
      });
      
      // Get the current gas price
      const feeData = await provider.getFeeData();
      const gasPrice = feeData.gasPrice || ethers.parseUnits("50", "gwei"); // Default to 50 gwei if not available
      
      // Calculate the total gas cost
      const totalGasCost = gasEstimate * gasPrice;
      
      // Convert gas cost to ETH
      const gasCostEth = ethers.formatEther(totalGasCost);
      
      // Get the native currency price in USD (this would typically come from an API)
      const nativeCurrencyPrice = await this.getNativeCurrencyPrice(network);
      
      // Calculate the gas cost in USD
      const gasCostUsd = parseFloat(gasCostEth) * nativeCurrencyPrice;
      
      return {
        gasEstimate: gasEstimate.toString(),
        gasPrice: ethers.formatUnits(gasPrice, "gwei"),
        totalGasCost: gasCostEth,
        totalGasCostUsd: gasCostUsd.toFixed(2),
        nativeCurrency: networkConfig.nativeCurrency.symbol,
        network,
        operations: [
          {
            name: "Retire Tokens",
            gasEstimate: gasEstimate.toString(),
          },
        ],
      };
    } catch (error) {
      logger.error("Error estimating retirement gas:", error);
      throw new Error(`Failed to estimate retirement gas: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  
  /**
   * Get the native currency price in USD
   * This is a placeholder function that would typically call an API
   * @param network Network to get the price for
   * @returns Native currency price in USD
   */
  private async getNativeCurrencyPrice(network: SupportedNetwork): Promise<number> {
    // In a real implementation, this would call a price API
    // For now, we'll return hardcoded values
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return 3500; // ETH price in USD
      case SupportedNetwork.POLYGON:
        return 0.6; // MATIC price in USD
      case SupportedNetwork.ARBITRUM:
        return 3500; // ETH price in USD (Arbitrum uses ETH)
      case SupportedNetwork.OPTIMISM:
        return 3500; // ETH price in USD (Optimism uses ETH)
      case SupportedNetwork.BASE:
        return 3500; // ETH price in USD (Base uses ETH)
      default:
        return 3500; // Default to ETH price
    }
  }
}

// Export singleton instance
export const gasEstimationService = new GasEstimationService();
