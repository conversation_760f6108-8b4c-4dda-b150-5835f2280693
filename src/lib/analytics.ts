import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { CarbonCreditStatus, OrderStatus, TransactionStatus, UserRole } from "@prisma/client";

/**
 * Analytics service for generating platform and organization reports
 */
export class AnalyticsService {
  /**
   * Get platform overview statistics
   * @returns Platform statistics
   */
  async getPlatformStats() {
    try {
      // Get user statistics
      const totalUsers = await db.user.count();
      const totalOrganizations = await db.organization.count();
      const activeOrganizations = await db.organization.count({
        where: { status: "ACTIVE" },
      });

      // Get carbon credit statistics
      const totalCarbonCredits = await db.carbonCredit.count();
      const listedCarbonCredits = await db.carbonCredit.count({
        where: { status: "LISTED" },
      });
      const soldCarbonCredits = await db.carbonCredit.count({
        where: { status: "SOLD" },
      });
      const retiredCarbonCredits = await db.carbonCredit.count({
        where: { status: "RETIRED" },
      });

      // Get transaction statistics
      const totalTransactions = await db.transaction.count();
      const completedTransactions = await db.transaction.count({
        where: { status: "COMPLETED" },
      });
      const totalTransactionVolume = await db.transaction.aggregate({
        _sum: { amount: true },
        where: { status: "COMPLETED" },
      });
      const totalFees = await db.transaction.aggregate({
        _sum: { fee: true },
        where: { status: "COMPLETED" },
      });

      // Get order statistics
      const totalOrders = await db.order.count();
      const completedOrders = await db.order.count({
        where: { status: "COMPLETED" },
      });
      const pendingOrders = await db.order.count({
        where: { status: "PENDING" },
      });

      // Get subscription statistics
      const subscriptionsByPlan = await db.subscription.groupBy({
        by: ["plan"],
        _count: true,
      });

      return {
        users: {
          total: totalUsers,
          organizations: {
            total: totalOrganizations,
            active: activeOrganizations,
          },
        },
        carbonCredits: {
          total: totalCarbonCredits,
          listed: listedCarbonCredits,
          sold: soldCarbonCredits,
          retired: retiredCarbonCredits,
        },
        transactions: {
          total: totalTransactions,
          completed: completedTransactions,
          volume: totalTransactionVolume._sum.amount || 0,
          fees: totalFees._sum.fee || 0,
        },
        orders: {
          total: totalOrders,
          completed: completedOrders,
          pending: pendingOrders,
        },
        subscriptions: subscriptionsByPlan.reduce((acc, item) => {
          acc[item.plan.toLowerCase()] = item._count;
          return acc;
        }, {} as Record<string, number>),
      };
    } catch (error) {
      logger.error("Error getting platform stats:", error);
      throw new Error("Failed to get platform statistics");
    }
  }

  /**
   * Get organization statistics
   * @param organizationId Organization ID
   * @returns Organization statistics
   */
  async getOrganizationStats(organizationId: string) {
    try {
      // Get organization details
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        include: {
          subscription: true,
        },
      });

      if (!organization) {
        throw new Error("Organization not found");
      }

      // Get user statistics
      const totalUsers = await db.user.count({
        where: { organizationId },
      });

      // Get carbon credit statistics
      const totalCarbonCredits = await db.carbonCredit.count({
        where: { organizationId },
      });
      const listedCarbonCredits = await db.carbonCredit.count({
        where: { organizationId, status: "LISTED" },
      });
      const soldCarbonCredits = await db.carbonCredit.count({
        where: { organizationId, status: "SOLD" },
      });
      const retiredCarbonCredits = await db.carbonCredit.count({
        where: { organizationId, status: "RETIRED" },
      });

      // Get carbon credit volume
      const carbonCreditVolume = await db.carbonCredit.aggregate({
        _sum: { quantity: true },
        where: { organizationId },
      });

      // Get transaction statistics
      const userIds = await db.user.findMany({
        where: { organizationId },
        select: { id: true },
      });
      const userIdList = userIds.map((user) => user.id);

      // Get buy orders
      const buyOrders = await db.order.findMany({
        where: {
          buyerId: { in: userIdList },
          status: "COMPLETED",
        },
        include: {
          transactions: true,
        },
      });

      // Get sell orders
      const sellOrders = await db.order.findMany({
        where: {
          sellerId: { in: userIdList },
          status: "COMPLETED",
        },
        include: {
          transactions: true,
        },
      });

      // Calculate transaction volume and fees
      const buyVolume = buyOrders.reduce(
        (sum, order) => sum + order.price * order.quantity,
        0
      );
      const sellVolume = sellOrders.reduce(
        (sum, order) => sum + order.price * order.quantity,
        0
      );
      const totalFees = [...buyOrders, ...sellOrders].reduce(
        (sum, order) =>
          sum +
          order.transactions.reduce(
            (orderSum, tx) => (tx.type === "FEE" ? orderSum + tx.amount : orderSum),
            0
          ),
        0
      );

      return {
        organization: {
          id: organization.id,
          name: organization.name,
          status: organization.status,
          subscription: organization.subscription?.plan || "NONE",
          transactionFeeRate: organization.transactionFeeRate,
          listingFeeRate: organization.listingFeeRate,
        },
        users: {
          total: totalUsers,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          listed: listedCarbonCredits,
          sold: soldCarbonCredits,
          retired: retiredCarbonCredits,
          volume: carbonCreditVolume._sum.quantity || 0,
        },
        transactions: {
          buyVolume,
          sellVolume,
          totalVolume: buyVolume + sellVolume,
          fees: totalFees,
        },
      };
    } catch (error) {
      logger.error(`Error getting organization stats for ${organizationId}:`, error);
      throw new Error("Failed to get organization statistics");
    }
  }

  /**
   * Get carbon credit market trends
   * @param period Period in days (default: 30)
   * @returns Market trends
   */
  async getMarketTrends(period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      // Get completed orders in the period
      const orders = await db.order.findMany({
        where: {
          status: "COMPLETED",
          updatedAt: {
            gte: startDate,
          },
        },
        include: {
          carbonCredit: {
            select: {
              standard: true,
              methodology: true,
              vintage: true,
            },
          },
        },
        orderBy: {
          updatedAt: "asc",
        },
      });

      // Group orders by day
      const ordersByDay = orders.reduce((acc, order) => {
        const date = order.updatedAt.toISOString().split("T")[0];
        if (!acc[date]) {
          acc[date] = [];
        }
        acc[date].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate daily volume and average price
      const dailyStats = Object.entries(ordersByDay).map(([date, dayOrders]) => {
        const volume = dayOrders.reduce((sum, order) => sum + order.quantity, 0);
        const value = dayOrders.reduce(
          (sum, order) => sum + order.price * order.quantity,
          0
        );
        const averagePrice = volume > 0 ? value / volume : 0;

        return {
          date,
          volume,
          value,
          averagePrice,
          orderCount: dayOrders.length,
        };
      });

      // Group by standard
      const standardGroups = orders.reduce((acc, order) => {
        const standard = order.carbonCredit.standard;
        if (!acc[standard]) {
          acc[standard] = [];
        }
        acc[standard].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      const standardStats = Object.entries(standardGroups).map(
        ([standard, standardOrders]) => {
          const volume = standardOrders.reduce((sum, order) => sum + order.quantity, 0);
          const value = standardOrders.reduce(
            (sum, order) => sum + order.price * order.quantity,
            0
          );
          const averagePrice = volume > 0 ? value / volume : 0;

          return {
            standard,
            volume,
            value,
            averagePrice,
            orderCount: standardOrders.length,
          };
        }
      );

      // Group by methodology
      const methodologyGroups = orders.reduce((acc, order) => {
        const methodology = order.carbonCredit.methodology;
        if (!acc[methodology]) {
          acc[methodology] = [];
        }
        acc[methodology].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      const methodologyStats = Object.entries(methodologyGroups).map(
        ([methodology, methodologyOrders]) => {
          const volume = methodologyOrders.reduce(
            (sum, order) => sum + order.quantity,
            0
          );
          const value = methodologyOrders.reduce(
            (sum, order) => sum + order.price * order.quantity,
            0
          );
          const averagePrice = volume > 0 ? value / volume : 0;

          return {
            methodology,
            volume,
            value,
            averagePrice,
            orderCount: methodologyOrders.length,
          };
        }
      );

      // Group by vintage
      const vintageGroups = orders.reduce((acc, order) => {
        const vintage = order.carbonCredit.vintage.toString();
        if (!acc[vintage]) {
          acc[vintage] = [];
        }
        acc[vintage].push(order);
        return acc;
      }, {} as Record<string, any[]>);

      const vintageStats = Object.entries(vintageGroups).map(
        ([vintage, vintageOrders]) => {
          const volume = vintageOrders.reduce((sum, order) => sum + order.quantity, 0);
          const value = vintageOrders.reduce(
            (sum, order) => sum + order.price * order.quantity,
            0
          );
          const averagePrice = volume > 0 ? value / volume : 0;

          return {
            vintage: parseInt(vintage),
            volume,
            value,
            averagePrice,
            orderCount: vintageOrders.length,
          };
        }
      );

      return {
        period,
        totalVolume: orders.reduce((sum, order) => sum + order.quantity, 0),
        totalValue: orders.reduce(
          (sum, order) => sum + order.price * order.quantity,
          0
        ),
        orderCount: orders.length,
        dailyStats,
        standardStats,
        methodologyStats,
        vintageStats,
      };
    } catch (error) {
      logger.error(`Error getting market trends for period ${period}:`, error);
      throw new Error("Failed to get market trends");
    }
  }

  /**
   * Get user activity report
   * @param userId User ID
   * @returns User activity report
   */
  async getUserActivity(userId: string) {
    try {
      // Get user details
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Get carbon credits
      const carbonCredits = await db.carbonCredit.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      });

      // Get buy orders
      const buyOrders = await db.order.findMany({
        where: { buyerId: userId },
        include: {
          carbonCredit: true,
          transactions: true,
        },
        orderBy: { createdAt: "desc" },
      });

      // Get sell orders
      const sellOrders = await db.order.findMany({
        where: { sellerId: userId },
        include: {
          carbonCredit: true,
          transactions: true,
        },
        orderBy: { createdAt: "desc" },
      });

      // Get wallets
      const wallets = await db.wallet.findMany({
        where: { userId },
        include: {
          tokens: true,
          nfts: true,
        },
      });

      // Calculate statistics
      const totalCarbonCredits = carbonCredits.reduce(
        (sum, credit) => sum + credit.quantity,
        0
      );
      const totalBuyVolume = buyOrders
        .filter((order) => order.status === "COMPLETED")
        .reduce((sum, order) => sum + order.price * order.quantity, 0);
      const totalSellVolume = sellOrders
        .filter((order) => order.status === "COMPLETED")
        .reduce((sum, order) => sum + order.price * order.quantity, 0);
      const totalFees = [...buyOrders, ...sellOrders]
        .filter((order) => order.status === "COMPLETED")
        .reduce(
          (sum, order) =>
            sum +
            order.transactions.reduce(
              (orderSum, tx) => (tx.type === "FEE" ? orderSum + tx.amount : orderSum),
              0
            ),
          0
        );

      return {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          organization: user.organization
            ? {
                id: user.organization.id,
                name: user.organization.name,
              }
            : null,
        },
        carbonCredits: {
          count: carbonCredits.length,
          totalQuantity: totalCarbonCredits,
          byStatus: {
            pending: carbonCredits.filter((c) => c.status === "PENDING").length,
            verified: carbonCredits.filter((c) => c.status === "VERIFIED").length,
            listed: carbonCredits.filter((c) => c.status === "LISTED").length,
            sold: carbonCredits.filter((c) => c.status === "SOLD").length,
            retired: carbonCredits.filter((c) => c.status === "RETIRED").length,
          },
        },
        orders: {
          buy: {
            count: buyOrders.length,
            volume: totalBuyVolume,
            byStatus: {
              pending: buyOrders.filter((o) => o.status === "PENDING").length,
              matched: buyOrders.filter((o) => o.status === "MATCHED").length,
              completed: buyOrders.filter((o) => o.status === "COMPLETED").length,
              cancelled: buyOrders.filter((o) => o.status === "CANCELLED").length,
            },
          },
          sell: {
            count: sellOrders.length,
            volume: totalSellVolume,
            byStatus: {
              pending: sellOrders.filter((o) => o.status === "PENDING").length,
              matched: sellOrders.filter((o) => o.status === "MATCHED").length,
              completed: sellOrders.filter((o) => o.status === "COMPLETED").length,
              cancelled: sellOrders.filter((o) => o.status === "CANCELLED").length,
            },
          },
          fees: totalFees,
        },
        wallets: {
          count: wallets.length,
          tokens: wallets.reduce((sum, wallet) => sum + wallet.tokens.length, 0),
          nfts: wallets.reduce((sum, wallet) => sum + wallet.nfts.length, 0),
        },
      };
    } catch (error) {
      logger.error(`Error getting user activity for ${userId}:`, error);
      throw new Error("Failed to get user activity");
    }
  }

  /**
   * Generate audit trail report
   * @param filters Audit filters
   * @returns Audit trail report
   */
  async getAuditTrail(filters: {
    organizationId?: string;
    userId?: string;
    startDate?: Date;
    endDate?: Date;
    type?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      const {
        organizationId,
        userId,
        startDate,
        endDate,
        type,
        limit = 100,
        offset = 0,
      } = filters;

      // Build where clause
      const where: any = {};

      if (organizationId) {
        where.organization = { id: organizationId };
      }

      if (userId) {
        where.user = { id: userId };
      }

      if (startDate) {
        where.createdAt = { ...(where.createdAt || {}), gte: startDate };
      }

      if (endDate) {
        where.createdAt = { ...(where.createdAt || {}), lte: endDate };
      }

      if (type) {
        where.type = type;
      }

      // Get audit records
      const auditRecords = await db.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
        skip: offset,
      });

      // Get total count
      const totalCount = await db.auditLog.count({ where });

      return {
        records: auditRecords,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount,
        },
      };
    } catch (error) {
      logger.error("Error getting audit trail:", error);
      throw new Error("Failed to get audit trail");
    }
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();
