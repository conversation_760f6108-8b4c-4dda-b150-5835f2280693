/**
 * KYC/AML Validation Schemas
 * 
 * This module provides standardized validation schemas for KYC/AML-related forms.
 */

import { z } from "zod";
import { fileSchema, documentTypeSchema } from "./document-schemas";

/**
 * KYC level validation schema
 * Validates that the KYC level is one of the allowed values
 */
export const kycLevelSchema = z.enum(["BASIC", "INTERMEDIATE", "ADVANCED"], {
  errorMap: () => ({ message: "Please select a valid KYC level" }),
});

/**
 * KYC status validation schema
 * Validates that the KYC status is one of the allowed values
 */
export const kycStatusSchema = z.enum(["PENDING", "APPROVED", "REJECTED", "EXPIRED"], {
  errorMap: () => ({ message: "Please select a valid KYC status" }),
});

/**
 * Risk level validation schema
 * Validates that the risk level is one of the allowed values
 */
export const riskLevelSchema = z.enum(["LOW", "MEDIUM", "HIGH"], {
  errorMap: () => ({ message: "Please select a valid risk level" }),
});

/**
 * Individual KYC validation schema
 * Validates all fields required for individual KYC
 */
export const individualKycSchema = z.object({
  // Personal Information
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  middleName: z.string().optional(),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  gender: z.enum(["MALE", "FEMALE", "OTHER", "PREFER_NOT_TO_SAY"], {
    errorMap: () => ({ message: "Please select a valid gender" }),
  }).optional(),
  nationality: z.string().min(1, "Nationality is required"),
  countryOfResidence: z.string().min(1, "Country of residence is required"),
  
  // Contact Information
  email: z.string().email("Please enter a valid email address"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  
  // Address Information
  address: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postalCode: z.string().min(1, "Postal code is required"),
    country: z.string().min(1, "Country is required"),
  }),
  
  // Identity Documents
  identityDocument: z.object({
    type: documentTypeSchema,
    number: z.string().min(1, "Document number is required"),
    issuingCountry: z.string().min(1, "Issuing country is required"),
    issuingAuthority: z.string().optional(),
    issueDate: z.string().min(1, "Issue date is required"),
    expiryDate: z.string().min(1, "Expiry date is required"),
    frontImage: fileSchema,
    backImage: fileSchema.optional(),
  }),
  
  // Additional Information
  occupation: z.string().optional(),
  employer: z.string().optional(),
  sourceOfFunds: z.string().optional(),
  isPoliticallyExposed: z.boolean().default(false),
  politicalExposureDetails: z.string().optional(),
  
  // Terms and Consent
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions",
  }),
  acceptPrivacyPolicy: z.boolean().refine((val) => val === true, {
    message: "You must accept the privacy policy",
  }),
  acceptDataProcessing: z.boolean().refine((val) => val === true, {
    message: "You must accept the data processing agreement",
  }),
}).refine(
  (data) => {
    if (data.isPoliticallyExposed && !data.politicalExposureDetails) {
      return false;
    }
    return true;
  },
  {
    message: "Please provide details about your political exposure",
    path: ["politicalExposureDetails"],
  }
);

/**
 * Corporate KYC validation schema
 * Validates all fields required for corporate KYC
 */
export const corporateKycSchema = z.object({
  // Company Information
  companyName: z.string().min(1, "Company name is required"),
  legalName: z.string().min(1, "Legal name is required"),
  registrationNumber: z.string().min(1, "Registration number is required"),
  incorporationDate: z.string().min(1, "Incorporation date is required"),
  incorporationCountry: z.string().min(1, "Incorporation country is required"),
  taxIdentificationNumber: z.string().min(1, "Tax identification number is required"),
  
  // Business Information
  businessType: z.string().min(1, "Business type is required"),
  industry: z.string().min(1, "Industry is required"),
  website: z.string().url("Please enter a valid URL").optional(),
  
  // Address Information
  registeredAddress: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postalCode: z.string().min(1, "Postal code is required"),
    country: z.string().min(1, "Country is required"),
  }),
  operatingAddress: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postalCode: z.string().min(1, "Postal code is required"),
    country: z.string().min(1, "Country is required"),
  }).optional(),
  
  // Contact Information
  contactPerson: z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    position: z.string().min(1, "Position is required"),
    email: z.string().email("Please enter a valid email address"),
    phoneNumber: z.string().min(1, "Phone number is required"),
  }),
  
  // Company Documents
  documents: z.array(
    z.object({
      type: documentTypeSchema,
      file: fileSchema,
      description: z.string().optional(),
      expiryDate: z.string().optional(),
    })
  ).min(1, "At least one document is required"),
  
  // Ownership Structure
  ownershipStructure: z.object({
    hasComplexStructure: z.boolean().default(false),
    structureDescription: z.string().optional(),
    structureDiagram: fileSchema.optional(),
  }),
  
  // Beneficial Owners
  beneficialOwners: z.array(
    z.object({
      firstName: z.string().min(1, "First name is required"),
      lastName: z.string().min(1, "Last name is required"),
      dateOfBirth: z.string().min(1, "Date of birth is required"),
      nationality: z.string().min(1, "Nationality is required"),
      ownershipPercentage: z.number().min(0, "Ownership percentage must be at least 0").max(100, "Ownership percentage cannot exceed 100%"),
      isPoliticallyExposed: z.boolean().default(false),
      politicalExposureDetails: z.string().optional(),
      identityDocument: z.object({
        type: documentTypeSchema,
        number: z.string().min(1, "Document number is required"),
        issuingCountry: z.string().min(1, "Issuing country is required"),
        issueDate: z.string().min(1, "Issue date is required"),
        expiryDate: z.string().min(1, "Expiry date is required"),
        frontImage: fileSchema,
        backImage: fileSchema.optional(),
      }),
    })
  ).min(1, "At least one beneficial owner is required"),
  
  // Directors
  directors: z.array(
    z.object({
      firstName: z.string().min(1, "First name is required"),
      lastName: z.string().min(1, "Last name is required"),
      position: z.string().min(1, "Position is required"),
      dateOfBirth: z.string().min(1, "Date of birth is required"),
      nationality: z.string().min(1, "Nationality is required"),
      isPoliticallyExposed: z.boolean().default(false),
      politicalExposureDetails: z.string().optional(),
      identityDocument: z.object({
        type: documentTypeSchema,
        number: z.string().min(1, "Document number is required"),
        issuingCountry: z.string().min(1, "Issuing country is required"),
        issueDate: z.string().min(1, "Issue date is required"),
        expiryDate: z.string().min(1, "Expiry date is required"),
        frontImage: fileSchema,
        backImage: fileSchema.optional(),
      }).optional(),
    })
  ).min(1, "At least one director is required"),
  
  // Financial Information
  financialInformation: z.object({
    annualRevenue: z.string().min(1, "Annual revenue is required"),
    sourceOfFunds: z.string().min(1, "Source of funds is required"),
    expectedTransactionVolume: z.string().min(1, "Expected transaction volume is required"),
    financialStatements: z.array(fileSchema).optional(),
  }),
  
  // Terms and Consent
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions",
  }),
  acceptPrivacyPolicy: z.boolean().refine((val) => val === true, {
    message: "You must accept the privacy policy",
  }),
  acceptDataProcessing: z.boolean().refine((val) => val === true, {
    message: "You must accept the data processing agreement",
  }),
}).refine(
  (data) => {
    if (data.ownershipStructure.hasComplexStructure && !data.ownershipStructure.structureDescription) {
      return false;
    }
    return true;
  },
  {
    message: "Please provide a description of the complex ownership structure",
    path: ["ownershipStructure.structureDescription"],
  }
);

/**
 * AML screening validation schema
 * Validates all fields required for AML screening
 */
export const amlScreeningSchema = z.object({
  entityType: z.enum(["INDIVIDUAL", "CORPORATE"], {
    errorMap: () => ({ message: "Please select a valid entity type" }),
  }),
  entityId: z.string().min(1, "Entity ID is required"),
  screeningType: z.enum(["INITIAL", "PERIODIC", "TRIGGERED"], {
    errorMap: () => ({ message: "Please select a valid screening type" }),
  }),
  screeningLevel: z.enum(["BASIC", "ENHANCED"], {
    errorMap: () => ({ message: "Please select a valid screening level" }),
  }),
  screeningDate: z.string().min(1, "Screening date is required"),
  screeningReason: z.string().optional(),
  watchlists: z.array(z.string()).min(1, "At least one watchlist is required"),
  additionalChecks: z.array(z.string()).optional(),
});

/**
 * Transaction monitoring validation schema
 * Validates all fields required for transaction monitoring
 */
export const transactionMonitoringSchema = z.object({
  entityId: z.string().min(1, "Entity ID is required"),
  entityType: z.enum(["INDIVIDUAL", "CORPORATE"], {
    errorMap: () => ({ message: "Please select a valid entity type" }),
  }),
  monitoringLevel: z.enum(["STANDARD", "ENHANCED"], {
    errorMap: () => ({ message: "Please select a valid monitoring level" }),
  }),
  riskLevel: riskLevelSchema,
  thresholds: z.object({
    singleTransactionAmount: z.number().positive("Single transaction amount must be positive"),
    dailyTransactionAmount: z.number().positive("Daily transaction amount must be positive"),
    monthlyTransactionAmount: z.number().positive("Monthly transaction amount must be positive"),
    unusualActivityPercentage: z.number().positive("Unusual activity percentage must be positive"),
  }),
  enabledAlerts: z.array(z.string()).min(1, "At least one alert must be enabled"),
  reviewFrequency: z.enum(["DAILY", "WEEKLY", "MONTHLY", "QUARTERLY"], {
    errorMap: () => ({ message: "Please select a valid review frequency" }),
  }),
});
