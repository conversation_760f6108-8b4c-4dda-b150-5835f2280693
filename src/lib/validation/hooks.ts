/**
 * Validation Hooks
 *
 * This module provides React hooks for form validation and error handling.
 */

import { useState, useCallback } from "react";
import { useForm, UseFormProps, FieldValues, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "@/components/ui/use-toast";
import { ValidationError, ValidationErrorType, formatApiError, handleFormError } from "./utils";
import { logger } from "@/lib/logger";

/**
 * Options for the useValidatedForm hook
 */
export interface UseValidatedFormOptions<T extends FieldValues> extends UseFormProps<T> {
  onError?: (error: ValidationError) => void;
  showToast?: boolean;
}

/**
 * Return type for the useValidatedForm hook
 */
export interface UseValidatedFormReturn<T extends FieldValues> extends UseFormReturn<T> {
  isSubmitting: boolean;
  formError: FormError | null;
  setFormError: (error: FormError | null) => void;
  handleSubmitWithValidation: (
    onValid: (data: T) => Promise<void> | void,
    onInvalid?: (error: FormError) => void
  ) => (e?: React.BaseSyntheticEvent) => Promise<void>;
  clearFormError: () => void;
  retrySubmission: () => Promise<void>;
  applyRecoveryOptions: (error: FormError) => void;
}

/**
 * Hook for form validation with Zod schemas
 * @param schema Zod schema to validate against
 * @param options Form options
 * @returns Form methods and validation utilities
 */
export function useValidatedForm<T extends FieldValues>(
  schema: z.Schema<T>,
  options?: UseValidatedFormOptions<T>
): UseValidatedFormReturn<T> {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<FormError | null>(null);
  const [lastValidData, setLastValidData] = useState<T | null>(null);
  const [lastSubmissionEvent, setLastSubmissionEvent] = useState<React.BaseSyntheticEvent | undefined>(undefined);
  const [lastOnValid, setLastOnValid] = useState<((data: T) => Promise<void> | void) | null>(null);
  const [lastOnInvalid, setLastOnInvalid] = useState<((error: FormError) => void) | null>(null);

  // Initialize form with Zod resolver
  const formMethods = useForm<T>({
    ...options,
    resolver: zodResolver(schema),
  });

  // Clear form error
  const clearFormError = useCallback(() => {
    setFormError(null);
  }, []);

  // Apply recovery options for form errors
  const applyRecoveryOptions = useCallback((error: FormError) => {
    if (!error.recoveryOptions) return;

    const { focusFirstError, scrollToError, clearForm, autoSave } = error.recoveryOptions;

    // Focus first error field
    if (focusFirstError && error.fieldErrors && error.fieldErrors.length > 0) {
      const firstErrorField = error.fieldErrors[0].path?.[0];
      if (firstErrorField) {
        setTimeout(() => {
          const element = document.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
          if (element) {
            element.focus();

            // Scroll to error if requested
            if (scrollToError) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }
        }, 100);
      }
    }

    // Clear form if requested
    if (clearForm) {
      formMethods.reset();
    }

    // Auto-save form data if requested
    if (autoSave && lastValidData) {
      try {
        const formKey = window.location.pathname;
        localStorage.setItem(`form_data_${formKey}`, JSON.stringify(lastValidData));
      } catch (e) {
        logger.error("Failed to auto-save form data:", e);
      }
    }
  }, [formMethods, lastValidData]);

  // Retry submission
  const retrySubmission = useCallback(async () => {
    if (lastOnValid && lastValidData) {
      clearFormError();
      setIsSubmitting(true);

      try {
        await lastOnValid(lastValidData);
      } catch (error) {
        const formattedError = handleFormError(error, {
          prefixFieldNames: true,
          includeDetails: true,
        });

        setFormError(formattedError);
        applyRecoveryOptions(formattedError);

        if (options?.showToast !== false) {
          toast({
            title: "Error",
            description: formattedError.message,
            variant: "destructive",
          });
        }

        if (lastOnInvalid) {
          lastOnInvalid(formattedError);
        }

        if (options?.onError) {
          options.onError(formattedError);
        }

        logger.error("Form retry submission error:", {
          error: formattedError,
          formData: lastValidData,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  }, [lastOnValid, lastValidData, clearFormError, applyRecoveryOptions, options, lastOnInvalid]);

  // Handle form submission with validation
  const handleSubmitWithValidation = useCallback(
    (
      onValid: (data: T) => Promise<void> | void,
      onInvalid?: (error: FormError) => void
    ) => {
      return async (e?: React.BaseSyntheticEvent) => {
        // Save callbacks for retry functionality
        setLastOnValid(onValid);
        setLastOnInvalid(onInvalid || null);

        // Save event for retry functionality
        if (e) {
          setLastSubmissionEvent(e);
        }

        // Clear previous errors
        clearFormError();

        // Start submission
        setIsSubmitting(true);

        try {
          // Use React Hook Form's handleSubmit
          await formMethods.handleSubmit(
            async (data) => {
              // Save valid data for retry functionality
              setLastValidData(data);

              try {
                // Call the onValid callback
                await onValid(data);
              } catch (error) {
                // Handle API errors
                const formattedError = handleFormError(error, {
                  prefixFieldNames: true,
                  includeDetails: true,
                });

                setFormError(formattedError);
                applyRecoveryOptions(formattedError);

                // Show toast if enabled
                if (options?.showToast !== false) {
                  toast({
                    title: "Error",
                    description: formattedError.message,
                    variant: "destructive",
                  });
                }

                // Always log the error for debugging
                console.error("Form submission error:", formattedError);

                // Call onError callback if provided
                if (options?.onError) {
                  options.onError(formattedError);
                }

                // Call onInvalid callback if provided
                if (onInvalid) {
                  onInvalid(formattedError);
                }

                // Log the error
                logger.error("Form submission error:", {
                  error: formattedError,
                  formData: data,
                });
              }
            },
            (errors) => {
              // Handle validation errors
              const formattedError = handleFormError(
                new z.ZodError(Object.entries(errors).map(([path, error]) => ({
                  code: "invalid_type",
                  expected: "any",
                  received: "invalid",
                  path: path.split("."),
                  message: error.message || "Invalid field",
                }))),
                {
                  prefixFieldNames: true,
                  includeDetails: true,
                }
              );

              setFormError(formattedError);
              applyRecoveryOptions(formattedError);

              // Show toast if enabled
              if (options?.showToast !== false) {
                toast({
                  title: "Validation Error",
                  description: formattedError.message,
                  variant: "destructive",
                });
              }

              // Call onError callback if provided
              if (options?.onError) {
                options.onError(formattedError);
              }

              // Call onInvalid callback if provided
              if (onInvalid) {
                onInvalid(formattedError);
              }

              // Log the error
              logger.error("Form validation error:", {
                error: errors,
                formData: formMethods.getValues(),
              });
            }
          )(e);
        } finally {
          // End submission
          setIsSubmitting(false);
        }
      };
    },
    [formMethods, clearFormError, options, applyRecoveryOptions]
  );

  return {
    ...formMethods,
    isSubmitting,
    formError,
    setFormError,
    handleSubmitWithValidation,
    clearFormError,
    retrySubmission,
    applyRecoveryOptions,
  };
}

/**
 * Return type for the useApiError hook
 */
export interface UseApiErrorReturn {
  error: ApiError | null;
  setError: (error: ApiError | null) => void;
  clearError: () => void;
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  handleApiRequest: <T>(
    requestFn: () => Promise<T>,
    options?: {
      onSuccess?: (data: T) => void;
      onError?: (error: ApiError) => void;
      showToast?: boolean;
      defaultErrorMessage?: string;
      userFriendlyMessages?: Record<string, string>;
      includeErrorDetails?: boolean;
      retryOnNetworkError?: boolean;
      maxRetries?: number;
      retryDelay?: number;
    }
  ) => Promise<T | null>;
  retryLastRequest: () => Promise<any>;
  isRetrying: boolean;
  retryCount: number;
  isRetryable: boolean;
}

/**
 * Hook for API error handling
 * @returns API error handling utilities
 */
export function useApiError(): UseApiErrorReturn {
  const [error, setError] = useState<ApiError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Store the last request for retry functionality
  const lastRequestRef = useRef<{
    requestFn: () => Promise<any>;
    options?: any;
  } | null>(null);

  // Check if the current error is retryable
  const isRetryable = useMemo(() => {
    return error?.retryable === true;
  }, [error]);

  // Handle API request with error handling
  const handleApiRequest = useCallback(
    async <T>(
      requestFn: () => Promise<T>,
      options?: {
        onSuccess?: (data: T) => void;
        onError?: (error: ApiError) => void;
        showToast?: boolean;
        defaultErrorMessage?: string;
        userFriendlyMessages?: Record<string, string>;
        includeErrorDetails?: boolean;
        retryOnNetworkError?: boolean;
        maxRetries?: number;
        retryDelay?: number;
      }
    ): Promise<T | null> => {
      // Store the request for retry functionality
      lastRequestRef.current = {
        requestFn,
        options,
      };

      // Clear previous errors
      setError(null);
      setIsLoading(true);

      // Reset retry count if this is a new request (not a retry)
      if (!isRetrying) {
        setRetryCount(0);
      }

      try {
        // Make the API request
        const data = await requestFn();

        // Call onSuccess callback if provided
        if (options?.onSuccess) {
          options.onSuccess(data);
        }

        return data;
      } catch (err) {
        // Format the error
        const formattedError = formatApiError(err, {
          defaultMessage: options?.defaultErrorMessage,
          includeDetails: options?.includeErrorDetails,
          userFriendlyMessages: options?.userFriendlyMessages,
        });

        setError(formattedError);

        // Show toast if enabled
        if (options?.showToast !== false) {
          toast({
            title: getErrorTitle(formattedError),
            description: formattedError.message,
            variant: "destructive",
          });
        }

        // Call onError callback if provided
        if (options?.onError) {
          options.onError(formattedError);
        }

        // Log the error
        logger.error("API request error:", {
          error: formattedError,
          retryCount,
          category: formattedError.category,
        });

        // Auto-retry for network errors if enabled
        if (
          options?.retryOnNetworkError !== false &&
          formattedError.category === ApiErrorCategory.NETWORK &&
          retryCount < (options?.maxRetries || 3)
        ) {
          const nextRetryCount = retryCount + 1;
          setRetryCount(nextRetryCount);

          // Exponential backoff for retries
          const delay = (options?.retryDelay || 1000) * Math.pow(2, retryCount);

          logger.info(`Retrying request in ${delay}ms (attempt ${nextRetryCount})`);

          // Show retry toast
          toast({
            title: "Retrying",
            description: `Retrying request in ${delay / 1000} seconds (attempt ${nextRetryCount})`,
            variant: "default",
          });

          // Wait for delay and retry
          setTimeout(() => {
            setIsRetrying(true);
            handleApiRequest(requestFn, options).finally(() => {
              setIsRetrying(false);
            });
          }, delay);
        }

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [retryCount, isRetrying]
  );

  // Retry the last request
  const retryLastRequest = useCallback(async () => {
    if (lastRequestRef.current) {
      setIsRetrying(true);
      setRetryCount((prev) => prev + 1);

      const result = await handleApiRequest(
        lastRequestRef.current.requestFn,
        lastRequestRef.current.options
      );

      setIsRetrying(false);
      return result;
    }
    return null;
  }, [handleApiRequest]);

  // Get error title based on error category
  const getErrorTitle = (error: ApiError): string => {
    switch (error.category) {
      case ApiErrorCategory.NETWORK:
        return "Network Error";
      case ApiErrorCategory.AUTHENTICATION:
        return "Authentication Error";
      case ApiErrorCategory.AUTHORIZATION:
        return "Authorization Error";
      case ApiErrorCategory.VALIDATION:
        return "Validation Error";
      case ApiErrorCategory.RESOURCE_NOT_FOUND:
        return "Not Found";
      case ApiErrorCategory.CONFLICT:
        return "Conflict Error";
      case ApiErrorCategory.RATE_LIMIT:
        return "Rate Limit Exceeded";
      case ApiErrorCategory.SERVER:
        return "Server Error";
      default:
        return "Error";
    }
  };

  return {
    error,
    setError,
    clearError: () => setError(null),
    isLoading,
    setIsLoading,
    handleApiRequest,
    retryLastRequest,
    isRetrying,
    retryCount,
    isRetryable,
  };
}
