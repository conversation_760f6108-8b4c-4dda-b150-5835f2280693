/**
 * Blockchain Validation Schemas
 * 
 * This module provides standardized validation schemas for blockchain-related forms.
 */

import { z } from "zod";
import { ethereumAddressSchema, transactionHashSchema, amountSchema } from "./schemas";

/**
 * Gas option validation schema
 * Validates that the gas option is one of the allowed values
 */
export const gasOptionSchema = z.enum(["standard", "fast", "instant"], {
  errorMap: () => ({ message: "Please select a valid gas option" }),
});

/**
 * Transaction validation schema
 * Validates all fields required for a blockchain transaction
 */
export const transactionSchema = z.object({
  to: ethereumAddressSchema,
  value: amountSchema,
  gasOption: gasOptionSchema,
  memo: z.string().optional(),
});

/**
 * Token transfer validation schema
 * Validates all fields required for a token transfer
 */
export const tokenTransferSchema = z.object({
  to: ethereumAddressSchema,
  tokenAddress: ethereumAddressSchema,
  amount: amountSchema,
  gasOption: gasOptionSchema,
  memo: z.string().optional(),
});

/**
 * Smart contract interaction validation schema
 * Validates all fields required for a smart contract interaction
 */
export const contractInteractionSchema = z.object({
  contractAddress: ethereumAddressSchema,
  methodName: z.string().min(1, "Method name is required"),
  methodParams: z.array(z.any()),
  value: z.string().optional(),
  gasOption: gasOptionSchema,
});

/**
 * Transaction confirmation validation schema
 * Validates all fields required for a transaction confirmation
 */
export const transactionConfirmationSchema = z.object({
  transactionHash: transactionHashSchema,
  confirmations: z.number().int().min(1, "At least 1 confirmation is required"),
});

/**
 * Wallet creation validation schema
 * Validates all fields required for wallet creation
 */
export const walletCreationSchema = z.object({
  walletType: z.enum(["SMART_WALLET", "REGULAR_WALLET"], {
    errorMap: () => ({ message: "Please select a valid wallet type" }),
  }),
  network: z.enum(["ETHEREUM", "POLYGON", "ARBITRUM", "OPTIMISM", "BASE"], {
    errorMap: () => ({ message: "Please select a valid network" }),
  }),
  securityLevel: z.enum(["STANDARD", "HIGH"], {
    errorMap: () => ({ message: "Please select a valid security level" }),
  }),
  testMode: z.boolean().default(true),
});

/**
 * Wallet recovery validation schema
 * Validates all fields required for wallet recovery
 */
export const walletRecoverySchema = z.object({
  recoveryMethod: z.enum(["SEED_PHRASE", "PRIVATE_KEY", "KEYSTORE"], {
    errorMap: () => ({ message: "Please select a valid recovery method" }),
  }),
  seedPhrase: z.string().min(12, "Seed phrase must be at least 12 words").optional(),
  privateKey: z.string().min(64, "Private key must be at least 64 characters").optional(),
  keystore: z.string().min(1, "Keystore is required").optional(),
  password: z.string().min(1, "Password is required").optional(),
}).refine(
  (data) => {
    if (data.recoveryMethod === "SEED_PHRASE" && !data.seedPhrase) return false;
    if (data.recoveryMethod === "PRIVATE_KEY" && !data.privateKey) return false;
    if (data.recoveryMethod === "KEYSTORE" && (!data.keystore || !data.password)) return false;
    return true;
  },
  {
    message: "Please provide the required recovery information",
    path: ["recoveryMethod"],
  }
);

/**
 * Gas estimation validation schema
 * Validates all fields required for gas estimation
 */
export const gasEstimationSchema = z.object({
  to: ethereumAddressSchema,
  data: z.string().optional(),
  value: z.string().optional(),
  gasLimit: z.number().int().min(21000, "Gas limit must be at least 21000").optional(),
  maxFeePerGas: z.number().int().min(1, "Max fee per gas must be at least 1").optional(),
  maxPriorityFeePerGas: z.number().int().min(1, "Max priority fee per gas must be at least 1").optional(),
});

/**
 * NFT minting validation schema
 * Validates all fields required for NFT minting
 */
export const nftMintingSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  image: z.instanceof(File, { message: "Image is required" }).optional(),
  imageUrl: z.string().url("Please enter a valid URL").optional(),
  attributes: z.array(
    z.object({
      trait_type: z.string().min(1, "Trait type is required"),
      value: z.union([z.string(), z.number(), z.boolean()]),
      display_type: z.string().optional(),
    })
  ).optional(),
  royalties: z.number().min(0, "Royalties must be at least 0").max(100, "Royalties cannot exceed 100%").optional(),
  collection: z.string().optional(),
  gasOption: gasOptionSchema,
}).refine(
  (data) => data.image || data.imageUrl,
  {
    message: "Either image or imageUrl is required",
    path: ["image"],
  }
);

/**
 * Carbon credit tokenization validation schema
 * Validates all fields required for carbon credit tokenization
 */
export const carbonCreditTokenizationSchema = z.object({
  carbonCreditId: z.string().min(1, "Carbon credit ID is required"),
  quantity: z.number().positive("Quantity must be positive"),
  vintage: z.number().int().min(2000, "Vintage year must be 2000 or later"),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  projectId: z.string().min(1, "Project ID is required"),
  verificationDocuments: z.array(z.instanceof(File, { message: "Verification document is required" })).optional(),
  gasOption: gasOptionSchema,
});

/**
 * Carbon credit retirement validation schema
 * Validates all fields required for carbon credit retirement
 */
export const carbonCreditRetirementSchema = z.object({
  carbonCreditId: z.string().min(1, "Carbon credit ID is required"),
  quantity: z.number().positive("Quantity must be positive"),
  beneficiary: z.string().min(1, "Beneficiary is required"),
  retirementReason: z.string().min(1, "Retirement reason is required"),
  retirementMessage: z.string().optional(),
  gasOption: gasOptionSchema,
});
