/**
 * Validation Schemas
 *
 * This module provides standardized validation schemas for use across the application.
 * It ensures consistent validation rules and error messages for all forms.
 */

import { z } from "zod";

// ===== Basic Validation Schemas =====

/**
 * Email validation schema
 * Validates that the input is a properly formatted email address
 */
export const emailSchema = z
  .string()
  .trim()
  .email("Please enter a valid email address")
  .min(5, "Email must be at least 5 characters")
  .max(255, "Email must be less than 255 characters");

/**
 * Simple email validation schema (less strict, for login forms)
 * Only validates that the input is a properly formatted email address
 */
export const simpleEmailSchema = z
  .string()
  .trim()
  .email("Please enter a valid email address");

/**
 * Password validation schema
 * Validates that the password meets security requirements
 */
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .max(100, "Password must be less than 100 characters")
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
  );

/**
 * Simple password validation schema (less strict, for login forms)
 * Only validates that the password is not empty
 */
export const simplePasswordSchema = z
  .string()
  .min(1, "Please enter your password");

/**
 * Name validation schema
 * Validates that the name contains only valid characters and is of appropriate length
 */
export const nameSchema = z
  .string()
  .trim()
  .min(2, "Name must be at least 2 characters")
  .max(100, "Name must be less than 100 characters")
  .regex(/^[a-zA-Z\s'-]+$/, "Name can only contain letters, spaces, hyphens, and apostrophes");

/**
 * Simple name validation schema (less strict)
 * Only validates that the name is of appropriate length
 */
export const simpleNameSchema = z
  .string()
  .trim()
  .min(2, "Name must be at least 2 characters")
  .max(100, "Name must be less than 100 characters");

/**
 * Organization name validation schema
 * Validates that the organization name is of appropriate length
 */
export const organizationNameSchema = z
  .string()
  .trim()
  .min(2, "Organization name must be at least 2 characters")
  .max(100, "Organization name must be less than 100 characters");

/**
 * URL validation schema
 * Validates that the input is a properly formatted URL
 */
export const urlSchema = z
  .string()
  .trim()
  .url("Please enter a valid URL")
  .max(2048, "URL must be less than 2048 characters")
  .optional()
  .or(z.literal(""));

/**
 * Phone number validation schema
 * Validates that the input is a properly formatted phone number
 */
export const phoneNumberSchema = z
  .string()
  .trim()
  .regex(/^\+?[0-9]{10,15}$/, "Please enter a valid phone number")
  .optional()
  .or(z.literal(""));

/**
 * Country validation schema
 * Validates that the country is selected
 */
export const countrySchema = z
  .string()
  .trim()
  .min(2, "Country is required");

/**
 * Address validation schema
 * Validates that the address is of appropriate length
 */
export const addressSchema = z
  .string()
  .trim()
  .min(5, "Address must be at least 5 characters")
  .max(255, "Address must be less than 255 characters")
  .optional()
  .or(z.literal(""));

/**
 * City validation schema
 * Validates that the city is of appropriate length
 */
export const citySchema = z
  .string()
  .trim()
  .min(2, "City must be at least 2 characters")
  .max(100, "City must be less than 100 characters")
  .optional()
  .or(z.literal(""));

/**
 * State/Province validation schema
 * Validates that the state/province is of appropriate length
 */
export const stateSchema = z
  .string()
  .trim()
  .min(2, "State/Province must be at least 2 characters")
  .max(100, "State/Province must be less than 100 characters")
  .optional()
  .or(z.literal(""));

/**
 * Postal code validation schema
 * Validates that the postal code is of appropriate length
 */
export const postalCodeSchema = z
  .string()
  .trim()
  .min(3, "Postal code must be at least 3 characters")
  .max(20, "Postal code must be less than 20 characters")
  .optional()
  .or(z.literal(""));

/**
 * Industry validation schema
 * Validates that the industry is selected
 */
export const industrySchema = z
  .string()
  .trim()
  .min(2, "Industry is required");

/**
 * Company size validation schema
 * Validates that the company size is selected
 */
export const companySizeSchema = z
  .enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"], {
    errorMap: () => ({ message: "Please select a company size" }),
  });

/**
 * Founded year validation schema
 * Validates that the founded year is within a reasonable range
 */
export const foundedYearSchema = z
  .number()
  .int("Year must be a whole number")
  .min(1800, "Year must be 1800 or later")
  .max(new Date().getFullYear(), "Year cannot be in the future")
  .optional()
  .or(z.literal(""));

// ===== Blockchain-Related Validation Schemas =====

/**
 * Ethereum address validation schema
 * Validates that the input is a properly formatted Ethereum address
 */
export const ethereumAddressSchema = z
  .string()
  .trim()
  .regex(/^0x[a-fA-F0-9]{40}$/, "Please enter a valid Ethereum address");

/**
 * Transaction hash validation schema
 * Validates that the input is a properly formatted transaction hash
 */
export const transactionHashSchema = z
  .string()
  .trim()
  .regex(/^0x[a-fA-F0-9]{64}$/, "Please enter a valid transaction hash");

/**
 * Amount validation schema (positive number with up to 18 decimal places)
 * Validates that the amount is a positive number with up to 18 decimal places
 */
export const amountSchema = z
  .string()
  .trim()
  .regex(/^\d+(\.\d{1,18})?$/, "Please enter a valid amount with up to 18 decimal places")
  .refine((val) => parseFloat(val) > 0, "Amount must be greater than 0");

/**
 * Quantity validation schema (positive number with up to 6 decimal places)
 * Validates that the quantity is a positive number with up to 6 decimal places
 */
export const quantitySchema = z
  .number()
  .positive("Quantity must be positive")
  .multipleOf(0.000001, "Quantity can have up to 6 decimal places");

/**
 * Price validation schema (positive number with up to 6 decimal places)
 * Validates that the price is a positive number with up to 6 decimal places
 */
export const priceSchema = z
  .number()
  .positive("Price must be positive")
  .multipleOf(0.000001, "Price can have up to 6 decimal places");

/**
 * Network validation schema
 * Validates that the network is selected
 */
export const networkSchema = z
  .enum(["ETHEREUM", "POLYGON", "ARBITRUM", "OPTIMISM", "BASE"], {
    errorMap: () => ({ message: "Please select a valid network" }),
  });

/**
 * Wallet type validation schema
 * Validates that the wallet type is selected
 */
export const walletTypeSchema = z
  .enum(["SMART_WALLET", "REGULAR_WALLET"], {
    errorMap: () => ({ message: "Please select a valid wallet type" }),
  });

// ===== Composite Validation Schemas =====

/**
 * Password with confirmation validation schema
 * Validates that the password meets security requirements and matches the confirmation
 */
export const passwordWithConfirmationSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Simple password with confirmation validation schema (less strict)
 * Validates that the password is of appropriate length and matches the confirmation
 */
export const simplePasswordWithConfirmationSchema = z.object({
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Registration form validation schema
 * Validates all fields required for user registration
 */
export const registrationSchema = z.object({
  name: simpleNameSchema,
  email: emailSchema,
  // Use simplePasswordSchema instead of passwordSchema for easier testing
  password: simplePasswordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Login form validation schema
 * Validates all fields required for user login
 */
export const loginSchema = z.object({
  email: simpleEmailSchema,
  password: simplePasswordSchema,
});

/**
 * Organization form validation schema
 * Validates all fields required for organization creation/update
 */
export const organizationSchema = z.object({
  // Essential fields (required)
  name: organizationNameSchema,
  industry: industrySchema,
  size: companySizeSchema,
  country: countrySchema,

  // Optional fields that can be filled later
  description: z.string().optional(),
  website: urlSchema,
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  address: addressSchema,
  city: citySchema,
  state: stateSchema,
  postalCode: postalCodeSchema,
  phoneNumber: phoneNumberSchema,
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  primaryContact: z.string().optional(),
  primaryContactEmail: z.string().email('Please enter a valid email').optional().or(z.literal("")),
  primaryContactPhone: phoneNumberSchema,
});

/**
 * Wallet creation validation schema
 * Validates all fields required for wallet creation
 */
export const walletCreationSchema = z.object({
  walletType: walletTypeSchema,
  network: networkSchema,
  securityLevel: z.enum(["STANDARD", "HIGH"]),
  testMode: z.boolean().default(true),
});

/**
 * Carbon credit validation schema
 * Validates all fields required for carbon credit creation
 */
export const carbonCreditSchema = z.object({
  name: simpleNameSchema,
  description: z.string().min(10, "Description must be at least 10 characters"),
  quantity: z.coerce.number().positive("Quantity must be positive"),
  price: z.coerce.number().positive("Price must be positive"),
  vintage: z.coerce.number().int().min(2000, "Vintage year must be 2000 or later"),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  location: z.string().optional(),
});

/**
 * Marketplace listing validation schema
 * Validates all fields required for marketplace listing creation
 */
export const marketplaceListingSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  carbonCreditId: z.string().min(1, "Carbon credit is required"),
  quantity: z.number().positive("Quantity must be positive"),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").default(1),
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"]).default("FIXED"),
  price: z.number().positive("Price must be positive").optional(),
  visibility: z.enum(["PUBLIC", "PRIVATE"]).default("PUBLIC"),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
});

/**
 * Send tokens validation schema
 * Validates all fields required for sending tokens
 */
export const sendTokensSchema = z.object({
  recipientAddress: ethereumAddressSchema,
  amount: z
    .string()
    .refine((val) => !isNaN(parseFloat(val)), { message: "Amount must be a number" })
    .refine((val) => parseFloat(val) > 0, { message: "Amount must be greater than 0" }),
  token: z.string().optional(),
  gasOption: z.enum(["standard", "fast", "instant"]),
  memo: z.string().optional(),
});

/**
 * Notification preferences validation schema
 * Validates all fields required for notification preferences
 */
export const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  inAppNotifications: z.boolean(),
  systemEmailEnabled: z.boolean(),
  systemInAppEnabled: z.boolean(),
  transactionEmailEnabled: z.boolean(),
  transactionInAppEnabled: z.boolean(),
  creditEmailEnabled: z.boolean(),
  creditInAppEnabled: z.boolean(),
  billingEmailEnabled: z.boolean(),
  billingInAppEnabled: z.boolean(),
  documentEmailEnabled: z.boolean(),
  documentInAppEnabled: z.boolean(),
  securityEmailEnabled: z.boolean(),
  securityInAppEnabled: z.boolean(),
  marketingEmailEnabled: z.boolean(),
  marketingInAppEnabled: z.boolean(),
});

/**
 * User profile validation schema
 * Validates all fields required for user profile information
 */
export const userProfileSchema = z.object({
  name: simpleNameSchema,
  email: emailSchema.optional(),
  jobTitle: z.string().optional(),
  phoneNumber: phoneNumberSchema,
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
});

/**
 * Password change validation schema
 * Validates all fields required for changing a password
 */
export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Forgot password validation schema
 * Validates the email field for requesting a password reset
 */
export const forgotPasswordSchema = z.object({
  email: simpleEmailSchema,
});

/**
 * Reset password validation schema
 * Validates the fields required for resetting a password
 */
export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string(),
  token: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});
