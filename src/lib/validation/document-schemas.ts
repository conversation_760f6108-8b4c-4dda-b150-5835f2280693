/**
 * Document Validation Schemas
 * 
 * This module provides standardized validation schemas for document-related forms.
 */

import { z } from "zod";

/**
 * File size validation
 * Maximum file size in bytes (10MB)
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * Allowed file types for documents
 */
export const ALLOWED_DOCUMENT_TYPES = [
  "application/pdf",
  "image/jpeg",
  "image/png",
  "image/heic",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

/**
 * Allowed file types for images
 */
export const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/webp",
  "image/heic",
];

/**
 * Document type validation schema
 * Validates that the document type is one of the allowed values
 */
export const documentTypeSchema = z.enum([
  "PASSPORT",
  "DRIVERS_LICENSE",
  "NATIONAL_ID",
  "UTILITY_BILL",
  "BANK_STATEMENT",
  "COMPANY_REGISTRATION",
  "TAX_CERTIFICATE",
  "PROOF_OF_ADDRESS",
  "CERTIFICATE_OF_INCORPORATION",
  "ARTICLES_OF_ASSOCIATION",
  "MEMORANDUM_OF_ASSOCIATION",
  "SHAREHOLDER_REGISTER",
  "DIRECTOR_REGISTER",
  "BENEFICIAL_OWNER_DECLARATION",
  "CARBON_CREDIT_CERTIFICATE",
  "VERIFICATION_REPORT",
  "METHODOLOGY_DOCUMENT",
  "PROJECT_DESCRIPTION",
  "MONITORING_REPORT",
  "OTHER",
], {
  errorMap: () => ({ message: "Please select a valid document type" }),
});

/**
 * Document status validation schema
 * Validates that the document status is one of the allowed values
 */
export const documentStatusSchema = z.enum([
  "PENDING",
  "APPROVED",
  "REJECTED",
  "EXPIRED",
], {
  errorMap: () => ({ message: "Please select a valid document status" }),
});

/**
 * File validation schema
 * Validates that the file meets the requirements
 */
export const fileSchema = z.instanceof(File, { message: "File is required" })
  .refine((file) => file.size <= MAX_FILE_SIZE, `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`)
  .refine(
    (file) => ALLOWED_DOCUMENT_TYPES.includes(file.type),
    `File type must be one of: ${ALLOWED_DOCUMENT_TYPES.join(", ")}`
  );

/**
 * Image validation schema
 * Validates that the image meets the requirements
 */
export const imageSchema = z.instanceof(File, { message: "Image is required" })
  .refine((file) => file.size <= MAX_FILE_SIZE, `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`)
  .refine(
    (file) => ALLOWED_IMAGE_TYPES.includes(file.type),
    `File type must be one of: ${ALLOWED_IMAGE_TYPES.join(", ")}`
  );

/**
 * Document upload validation schema
 * Validates all fields required for a document upload
 */
export const documentUploadSchema = z.object({
  documentType: documentTypeSchema,
  file: fileSchema,
  description: z.string().optional(),
  expiryDate: z.string().optional(),
  issuingCountry: z.string().optional(),
  issuingAuthority: z.string().optional(),
  documentNumber: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Bulk document upload validation schema
 * Validates all fields required for a bulk document upload
 */
export const bulkDocumentUploadSchema = z.object({
  documentType: documentTypeSchema,
  files: z.array(fileSchema).min(1, "At least one file is required"),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Document search validation schema
 * Validates all fields required for a document search
 */
export const documentSearchSchema = z.object({
  query: z.string().optional(),
  documentType: documentTypeSchema.optional(),
  status: documentStatusSchema.optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Document sharing validation schema
 * Validates all fields required for document sharing
 */
export const documentSharingSchema = z.object({
  documentIds: z.array(z.string()).min(1, "At least one document is required"),
  recipientEmails: z.array(z.string().email("Please enter a valid email address")).min(1, "At least one recipient is required"),
  message: z.string().optional(),
  expiryDate: z.string().optional(),
  password: z.string().optional(),
  allowDownload: z.boolean().default(true),
  notifyOnAccess: z.boolean().default(false),
});

/**
 * Document template validation schema
 * Validates all fields required for a document template
 */
export const documentTemplateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  documentType: documentTypeSchema,
  file: fileSchema,
  fields: z.array(
    z.object({
      name: z.string().min(1, "Field name is required"),
      type: z.enum(["TEXT", "NUMBER", "DATE", "CHECKBOX", "SIGNATURE", "DROPDOWN"], {
        errorMap: () => ({ message: "Please select a valid field type" }),
      }),
      required: z.boolean().default(false),
      placeholder: z.string().optional(),
      options: z.array(z.string()).optional(),
      defaultValue: z.union([z.string(), z.number(), z.boolean()]).optional(),
    })
  ).optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Document signing validation schema
 * Validates all fields required for document signing
 */
export const documentSigningSchema = z.object({
  documentId: z.string().min(1, "Document ID is required"),
  signers: z.array(
    z.object({
      name: z.string().min(1, "Name is required"),
      email: z.string().email("Please enter a valid email address"),
      role: z.string().optional(),
      order: z.number().int().min(1, "Order must be at least 1").optional(),
      fields: z.array(
        z.object({
          type: z.enum(["SIGNATURE", "INITIAL", "DATE", "TEXT", "CHECKBOX"], {
            errorMap: () => ({ message: "Please select a valid field type" }),
          }),
          page: z.number().int().min(1, "Page must be at least 1"),
          x: z.number().min(0, "X coordinate must be at least 0"),
          y: z.number().min(0, "Y coordinate must be at least 0"),
          width: z.number().positive("Width must be positive"),
          height: z.number().positive("Height must be positive"),
          required: z.boolean().default(true),
        })
      ).optional(),
    })
  ).min(1, "At least one signer is required"),
  message: z.string().optional(),
  expiryDate: z.string().optional(),
  reminderFrequency: z.enum(["NONE", "DAILY", "WEEKLY"], {
    errorMap: () => ({ message: "Please select a valid reminder frequency" }),
  }).default("NONE"),
  password: z.string().optional(),
});
