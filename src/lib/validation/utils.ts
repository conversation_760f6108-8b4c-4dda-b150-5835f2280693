/**
 * Validation Utilities
 *
 * This module provides utility functions for validation, error handling, and error formatting.
 */

import { z } from "zod";
import { logger } from "@/lib/logger";
import { toast } from "@/components/ui/use-toast";

/**
 * Error types for validation errors
 */
export enum ValidationErrorType {
  FIELD = "field",
  FORM = "form",
  API = "api",
  UNKNOWN = "unknown",
}

/**
 * Validation error interface
 */
export interface ValidationError {
  type: ValidationErrorType;
  message: string;
  path?: string[];
  details?: Record<string, any>;
}

/**
 * Format a Zod error into a standardized validation error
 * @param error Zod error to format
 * @param options Options for formatting
 * @returns Array of validation errors
 */
export function formatZodError(
  error: z.ZodError,
  options?: {
    prefixFieldNames?: boolean;
    includeDetails?: boolean;
    customMessages?: Record<string, string>;
  }
): ValidationError[] {
  return error.errors.map((e) => {
    // Get field name from path
    const fieldName = e.path.length > 0 ? e.path[e.path.length - 1].toString() : "";

    // Format field name for display (convert camelCase to Title Case)
    const formattedFieldName = fieldName
      ? fieldName.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())
      : "";

    // Use custom message if provided
    const pathString = e.path.join(".");
    const customMessage = options?.customMessages?.[pathString];

    // Create message with field name prefix if requested
    const message = customMessage || (options?.prefixFieldNames && formattedFieldName
      ? `${formattedFieldName}: ${e.message}`
      : e.message);

    return {
      type: ValidationErrorType.FIELD,
      message,
      path: e.path,
      details: options?.includeDetails ? {
        code: e.code,
        expected: e.expected,
        received: e.received,
        message: e.message,
      } : undefined,
    };
  });
}

/**
 * API error categories for better error handling
 */
export enum ApiErrorCategory {
  NETWORK = "network",
  AUTHENTICATION = "authentication",
  AUTHORIZATION = "authorization",
  VALIDATION = "validation",
  RESOURCE_NOT_FOUND = "resource_not_found",
  CONFLICT = "conflict",
  RATE_LIMIT = "rate_limit",
  SERVER = "server",
  UNKNOWN = "unknown",
}

/**
 * API error interface with additional fields
 */
export interface ApiError extends ValidationError {
  category: ApiErrorCategory;
  statusCode?: number;
  retryable?: boolean;
  errorCode?: string;
}

/**
 * Format an API error into a standardized validation error
 * @param error API error to format
 * @param options Options for formatting
 * @returns Validation error
 */
export function formatApiError(
  error: any,
  options?: {
    defaultMessage?: string;
    includeDetails?: boolean;
    userFriendlyMessages?: Record<string, string>;
  }
): ApiError {
  // If the error is already a ValidationError, enhance it with API error fields
  if (error && error.type && error.message) {
    return {
      ...error as ValidationError,
      category: (error as ApiError).category || ApiErrorCategory.UNKNOWN,
      retryable: (error as ApiError).retryable || false,
    };
  }

  // If the error is a string, create a simple API error
  if (typeof error === "string") {
    return {
      type: ValidationErrorType.API,
      message: error,
      category: ApiErrorCategory.UNKNOWN,
      retryable: false,
    };
  }

  // If the error is a network error
  if (error?.name === "NetworkError" || error?.message?.includes("network") || error?.message?.includes("Network")) {
    return {
      type: ValidationErrorType.API,
      message: "Network error. Please check your internet connection and try again.",
      category: ApiErrorCategory.NETWORK,
      retryable: true,
      details: options?.includeDetails ? error : undefined,
    };
  }

  // If the error is a response object with status code and data
  if (error?.response) {
    const { status, data } = error.response;
    const errorMessage = data?.message || data?.error || options?.defaultMessage || "An error occurred";

    // Get user-friendly message if available
    const userFriendlyMessage = options?.userFriendlyMessages?.[status] || errorMessage;

    // Determine error category based on status code
    let category = ApiErrorCategory.UNKNOWN;
    let retryable = false;

    if (status >= 400 && status < 500) {
      if (status === 401) {
        category = ApiErrorCategory.AUTHENTICATION;
      } else if (status === 403) {
        category = ApiErrorCategory.AUTHORIZATION;
      } else if (status === 404) {
        category = ApiErrorCategory.RESOURCE_NOT_FOUND;
      } else if (status === 409) {
        category = ApiErrorCategory.CONFLICT;
      } else if (status === 422) {
        category = ApiErrorCategory.VALIDATION;
      } else if (status === 429) {
        category = ApiErrorCategory.RATE_LIMIT;
        retryable = true;
      }
    } else if (status >= 500) {
      category = ApiErrorCategory.SERVER;
      retryable = true;
    }

    return {
      type: ValidationErrorType.API,
      message: userFriendlyMessage,
      category,
      statusCode: status,
      retryable,
      errorCode: data?.code,
      details: options?.includeDetails ? data : undefined,
    };
  }

  // If the error is an object with a message, create an API error with details
  if (error && error.message) {
    return {
      type: ValidationErrorType.API,
      message: error.message,
      category: ApiErrorCategory.UNKNOWN,
      retryable: false,
      details: options?.includeDetails ? error : undefined,
    };
  }

  // Default case: unknown error
  return {
    type: ValidationErrorType.UNKNOWN,
    message: options?.defaultMessage || "An unknown error occurred",
    category: ApiErrorCategory.UNKNOWN,
    retryable: false,
    details: options?.includeDetails ? error : undefined,
  };
}

/**
 * Validate data against a schema
 * @param schema Zod schema to validate against
 * @param data Data to validate
 * @returns Validated data or throws an error
 */
export function validateData<T>(schema: z.Schema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map((e) => e.message).join(", ");
      logger.error("Validation error:", { error: errorMessage, data });
      throw new Error(errorMessage);
    }
    logger.error("Unknown validation error:", { error, data });
    throw new Error("Invalid data");
  }
}

/**
 * Validate request data and return a response if validation fails
 * @param schema Zod schema to validate against
 * @param data Data to validate
 * @returns Validated data or null if validation fails
 */
export function validateRequest<T>(
  schema: z.Schema<T>,
  data: unknown
): { data: T | null; error: string | null } {
  try {
    const validatedData = schema.parse(data);
    return { data: validatedData, error: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map((e) => e.message).join(", ");
      logger.error("Request validation error:", { error: errorMessage, data });
      return { data: null, error: errorMessage };
    }
    logger.error("Unknown validation error:", { error, data });
    return { data: null, error: "Invalid data" };
  }
}

/**
 * Display validation errors as toast notifications
 * @param errors Validation errors to display
 */
export function displayValidationErrors(errors: ValidationError | ValidationError[]): void {
  const errorArray = Array.isArray(errors) ? errors : [errors];

  errorArray.forEach((error) => {
    toast({
      title: "Validation Error",
      description: error.message,
      variant: "destructive",
    });
  });
}

/**
 * Form error recovery options
 */
export interface FormErrorRecoveryOptions {
  retrySubmission?: boolean;
  clearForm?: boolean;
  focusFirstError?: boolean;
  scrollToError?: boolean;
  showErrorSummary?: boolean;
  autoSave?: boolean;
}

/**
 * Form error with recovery options
 */
export interface FormError extends ValidationError {
  fieldErrors?: ValidationError[];
  recoveryOptions?: FormErrorRecoveryOptions;
  recoveryInstructions?: string;
}

/**
 * Common validation error messages
 */
export const ValidationErrorMessages = {
  REQUIRED: "This field is required",
  INVALID_EMAIL: "Please enter a valid email address",
  INVALID_URL: "Please enter a valid URL",
  INVALID_PHONE: "Please enter a valid phone number",
  PASSWORD_TOO_SHORT: "Password must be at least 8 characters",
  PASSWORDS_DONT_MATCH: "Passwords do not match",
  INVALID_DATE: "Please enter a valid date",
  INVALID_NUMBER: "Please enter a valid number",
  NETWORK_ERROR: "Network error. Please check your internet connection and try again.",
  SERVER_ERROR: "Server error. Please try again later.",
  UNKNOWN_ERROR: "An unknown error occurred. Please try again.",
  FORM_VALIDATION_ERROR: "Please fix the validation errors and try again",
  FORM_SUBMISSION_ERROR: "An error occurred while submitting the form",
  SESSION_EXPIRED: "Your session has expired. Please log in again.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  RESOURCE_NOT_FOUND: "The requested resource was not found.",
  CONFLICT: "A conflict occurred. Please refresh and try again.",
  RATE_LIMIT: "Rate limit exceeded. Please try again later.",
};

/**
 * User-friendly error messages for API status codes
 */
export const ApiStatusErrorMessages: Record<number, string> = {
  400: "The request was invalid. Please check your input and try again.",
  401: "You are not authenticated. Please log in and try again.",
  403: "You do not have permission to perform this action.",
  404: "The requested resource was not found.",
  409: "A conflict occurred. The resource might have been modified by another user.",
  422: "Validation error. Please check your input and try again.",
  429: "Too many requests. Please try again later.",
  500: "Server error. Please try again later.",
  502: "Bad gateway. Please try again later.",
  503: "Service unavailable. Please try again later.",
  504: "Gateway timeout. Please try again later.",
};

/**
 * Handle form submission errors
 * @param error Error to handle
 * @param options Options for handling the error
 * @returns Formatted form error
 */
export function handleFormError(
  error: any,
  options?: {
    prefixFieldNames?: boolean;
    includeDetails?: boolean;
    customMessages?: Record<string, string>;
    recoveryOptions?: FormErrorRecoveryOptions;
  }
): FormError {
  // If the error is a Zod error, format it
  if (error instanceof z.ZodError) {
    const fieldErrors = formatZodError(error, {
      prefixFieldNames: options?.prefixFieldNames,
      includeDetails: options?.includeDetails,
      customMessages: options?.customMessages,
    });

    // Get the first error message for the summary
    const firstErrorMessage = fieldErrors.length > 0 ? fieldErrors[0].message : ValidationErrorMessages.FORM_VALIDATION_ERROR;

    return {
      type: ValidationErrorType.FORM,
      message: firstErrorMessage,
      fieldErrors,
      recoveryOptions: options?.recoveryOptions || {
        focusFirstError: true,
        scrollToError: true,
        showErrorSummary: true,
      },
      recoveryInstructions: "Please fix the validation errors highlighted below and try again.",
      details: options?.includeDetails ? { errors: fieldErrors, original: error } : undefined,
    };
  }

  // If the error is an API error, format it
  if (error && error.response) {
    try {
      const { status, data } = error.response;

      // Check if the response contains field validation errors
      const fieldErrors = data.errors ?
        Object.entries(data.errors).map(([field, message]) => ({
          type: ValidationErrorType.FIELD,
          message: Array.isArray(message) ? message[0] : message as string,
          path: [field],
        })) :
        [];

      // Get user-friendly message based on status code
      const userFriendlyMessage = options?.customMessages?.[status] ||
        ApiStatusErrorMessages[status] ||
        data.error ||
        data.message ||
        ValidationErrorMessages.FORM_SUBMISSION_ERROR;

      // Determine recovery options based on error type
      let recoveryOptions: FormErrorRecoveryOptions = options?.recoveryOptions || {};

      if (status === 401) {
        // Session expired, redirect to login
        recoveryOptions = {
          ...recoveryOptions,
          clearForm: false,
          retrySubmission: false,
        };
      } else if (status === 422) {
        // Validation error, focus on first error field
        recoveryOptions = {
          ...recoveryOptions,
          focusFirstError: true,
          scrollToError: true,
          showErrorSummary: true,
        };
      } else if (status >= 500) {
        // Server error, allow retry
        recoveryOptions = {
          ...recoveryOptions,
          retrySubmission: true,
          autoSave: true,
        };
      }

      // Create API error with field errors
      const apiError = formatApiError(error, {
        defaultMessage: ValidationErrorMessages.FORM_SUBMISSION_ERROR,
        includeDetails: options?.includeDetails,
        userFriendlyMessages: options?.customMessages,
      });

      return {
        ...apiError,
        type: ValidationErrorType.FORM,
        message: userFriendlyMessage,
        fieldErrors: fieldErrors.length > 0 ? fieldErrors : undefined,
        recoveryOptions,
        recoveryInstructions: fieldErrors.length > 0
          ? "Please fix the validation errors highlighted below and try again."
          : apiError.retryable
            ? "This appears to be a temporary error. Please try again in a few moments."
            : "Please check your input and try again.",
      };
    } catch (e) {
      // If we can't parse the response, return a generic error
      return {
        type: ValidationErrorType.FORM,
        message: ValidationErrorMessages.FORM_SUBMISSION_ERROR,
        category: ApiErrorCategory.UNKNOWN,
        retryable: false,
        recoveryOptions: options?.recoveryOptions || {
          retrySubmission: true,
        },
        recoveryInstructions: "Please try again. If the problem persists, contact support.",
        details: options?.includeDetails ? error : undefined,
      };
    }
  }

  // For network errors, provide specific recovery options
  if (error?.name === "NetworkError" || error?.message?.includes("network") || error?.message?.includes("Network")) {
    return {
      type: ValidationErrorType.FORM,
      message: ValidationErrorMessages.NETWORK_ERROR,
      category: ApiErrorCategory.NETWORK,
      retryable: true,
      recoveryOptions: options?.recoveryOptions || {
        retrySubmission: true,
        autoSave: true,
      },
      recoveryInstructions: "Please check your internet connection and try again.",
      details: options?.includeDetails ? error : undefined,
    };
  }

  // Default case: unknown error
  const apiError = formatApiError(error, {
    defaultMessage: ValidationErrorMessages.FORM_SUBMISSION_ERROR,
    includeDetails: options?.includeDetails,
    userFriendlyMessages: options?.customMessages,
  });

  return {
    ...apiError,
    type: ValidationErrorType.FORM,
    recoveryOptions: options?.recoveryOptions || {
      retrySubmission: apiError.retryable,
      autoSave: apiError.retryable,
    },
    recoveryInstructions: apiError.retryable
      ? "This appears to be a temporary error. Please try again in a few moments."
      : "Please check your input and try again. If the problem persists, contact support.",
  };
}

/**
 * Get field-specific error message from validation errors
 * @param errors Validation errors
 * @param fieldName Field name to get error for
 * @returns Error message for the field, or undefined if no error
 */
export function getFieldError(
  errors: ValidationError[],
  fieldName: string
): string | undefined {
  const error = errors.find(
    (e) => e.path && e.path.join(".") === fieldName
  );
  return error?.message;
}

/**
 * Check if a field has an error
 * @param errors Validation errors
 * @param fieldName Field name to check
 * @returns True if the field has an error, false otherwise
 */
export function hasFieldError(
  errors: ValidationError[],
  fieldName: string
): boolean {
  return !!getFieldError(errors, fieldName);
}
