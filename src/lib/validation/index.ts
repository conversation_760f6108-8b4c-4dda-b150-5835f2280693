/**
 * Validation Library
 *
 * This module provides a comprehensive validation library for the application.
 * It includes validation schemas, utilities, and hooks for form validation.
 */

// Export all basic schemas
export * from "./schemas";

// Export domain-specific schemas with explicit re-exports to avoid ambiguity
import * as blockchainSchemas from "./blockchain-schemas";
import * as financialSchemas from "./financial-schemas";
import * as documentSchemas from "./document-schemas";
import * as kycSchemas from "./kyc-schemas";

// Re-export domain-specific schemas
export {
  blockchainSchemas,
  financialSchemas,
  documentSchemas,
  kycSchemas
};

// Export all utilities
export * from "./utils";

// Export all hooks
export * from "./hooks";

// Re-export zod for convenience
export { z } from "zod";
export { zodResolver } from "@hookform/resolvers/zod";
