import { TokenizationStatus, VerificationStatus } from '@prisma/client';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { TokenizationResult } from './types';
import { blockchainService } from '@/lib/blockchain';
import { SupportedNetwork } from '@/lib/blockchain/config/networks';

/**
 * Carbon credit tokenization service
 */
export class CarbonCreditTokenization {
  /**
   * Tokenize a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param userId User ID performing the tokenization
   * @param options Tokenization options
   * @returns Tokenization result
   */
  static async tokenizeCarbonCredit(
    carbonCreditId: string,
    userId: string,
    options: {
      network?: SupportedNetwork;
      useTestnet?: boolean;
      walletAddress?: string;
    } = {}
  ): Promise<TokenizationResult> {
    try {
      logger.info(`Tokenizing carbon credit ${carbonCreditId} by user ${userId}`);
      
      // Get carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          organization: true,
          user: true,
        },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${carbonCreditId}`);
      }
      
      // Check if carbon credit is verified
      if (carbonCredit.verificationStatus !== VerificationStatus.VERIFIED) {
        return {
          success: false,
          message: "Carbon credit must be verified before tokenization",
          tokenizationStatus: TokenizationStatus.FAILED,
        };
      }
      
      // Check if carbon credit is already tokenized
      if (carbonCredit.tokenizationStatus === TokenizationStatus.TOKENIZED) {
        return {
          success: false,
          message: "Carbon credit is already tokenized",
          tokenizationStatus: TokenizationStatus.TOKENIZED,
          tokenId: carbonCredit.tokenId,
        };
      }
      
      // Check if user has permission
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === carbonCredit.organizationId;
      const isOwner = userId === carbonCredit.userId;
      
      if (!isAdmin && !isOrgAdmin && !isOwner) {
        throw new Error("You do not have permission to tokenize this carbon credit");
      }
      
      // Get wallet address
      const walletAddress = options.walletAddress || await this.getWalletAddress(userId, carbonCredit.organizationId);
      
      if (!walletAddress) {
        return {
          success: false,
          message: "No wallet address found for tokenization",
          tokenizationStatus: TokenizationStatus.FAILED,
        };
      }
      
      // Set network options
      const network = options.network || SupportedNetwork.POLYGON;
      const useTestnet = options.useTestnet !== undefined ? options.useTestnet : true;
      
      // Update blockchain service network if needed
      if (network !== blockchainService.getCurrentNetwork().network || useTestnet !== blockchainService.getCurrentNetwork().isTestnet) {
        blockchainService.changeNetwork(network, useTestnet);
      }
      
      // Generate token ID
      const tokenId = this.generateTokenId(carbonCredit);
      
      // Prepare metadata
      const metadata = {
        projectId: carbonCredit.projectId,
        vintage: carbonCredit.vintage,
        standard: carbonCredit.standard,
        methodology: carbonCredit.methodology,
      };
      
      try {
        // Update carbon credit to tokenizing status
        await db.carbonCredit.update({
          where: { id: carbonCreditId },
          data: {
            tokenizationStatus: TokenizationStatus.TOKENIZING,
            tokenId: tokenId.toString(),
          },
        });
        
        // Get carbon credit contract
        const carbonCreditContract = blockchainService.getCarbonCreditContract();
        
        // Get signer
        // In a real implementation, this would use a proper signing mechanism
        // For now, we'll just simulate it
        const signer = await this.getSigner(userId);
        
        // Mint tokens
        const tx = await carbonCreditContract.mint(
          walletAddress,
          tokenId,
          carbonCredit.quantity,
          metadata,
          signer
        );
        
        // Wait for transaction to be mined
        const receipt = await tx.wait();
        
        // Update carbon credit
        await db.carbonCredit.update({
          where: { id: carbonCreditId },
          data: {
            tokenizationStatus: TokenizationStatus.TOKENIZED,
            tokenId: tokenId.toString(),
            tokenizationNotes: `Tokenized on ${network} ${useTestnet ? 'testnet' : 'mainnet'}`,
            tokenizedAt: new Date(),
            tokenizedBy: {
              connect: { id: userId },
            },
          },
        });
        
        // Create tokenization record
        const tokenization = await db.carbonCreditTokenization.create({
          data: {
            carbonCreditId,
            tokenId: tokenId.toString(),
            network,
            isTestnet: useTestnet,
            walletAddress,
            transactionHash: receipt.hash,
            status: TokenizationStatus.TOKENIZED,
            userId,
          },
        });
        
        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_TOKENIZED",
            description: `Carbon credit ${carbonCreditId} tokenized with token ID ${tokenId}`,
            userId,
            organizationId: carbonCredit.organizationId,
            metadata: {
              carbonCreditId,
              tokenId: tokenId.toString(),
              network,
              isTestnet: useTestnet,
              walletAddress,
              transactionHash: receipt.hash,
              tokenizationId: tokenization.id,
            },
          },
        });
        
        return {
          success: true,
          message: "Carbon credit tokenized successfully",
          tokenizationStatus: TokenizationStatus.TOKENIZED,
          tokenId: tokenId.toString(),
          transactionHash: receipt.hash,
          details: {
            network,
            isTestnet: useTestnet,
            walletAddress,
          },
        };
      } catch (error) {
        // Update carbon credit to failed status
        await db.carbonCredit.update({
          where: { id: carbonCreditId },
          data: {
            tokenizationStatus: TokenizationStatus.FAILED,
            tokenizationNotes: `Tokenization failed: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
        });
        
        // Create tokenization record
        const tokenization = await db.carbonCreditTokenization.create({
          data: {
            carbonCreditId,
            tokenId: tokenId.toString(),
            network,
            isTestnet: useTestnet,
            walletAddress,
            status: TokenizationStatus.FAILED,
            errorMessage: error instanceof Error ? error.message : "Unknown error",
            userId,
          },
        });
        
        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_TOKENIZATION_FAILED",
            description: `Carbon credit ${carbonCreditId} tokenization failed: ${error instanceof Error ? error.message : "Unknown error"}`,
            userId,
            organizationId: carbonCredit.organizationId,
            metadata: {
              carbonCreditId,
              tokenId: tokenId.toString(),
              network,
              isTestnet: useTestnet,
              walletAddress,
              error: error instanceof Error ? error.message : "Unknown error",
              tokenizationId: tokenization.id,
            },
          },
        });
        
        return {
          success: false,
          message: `Tokenization failed: ${error instanceof Error ? error.message : "Unknown error"}`,
          tokenizationStatus: TokenizationStatus.FAILED,
          details: {
            network,
            isTestnet: useTestnet,
            walletAddress,
            error: error instanceof Error ? error.message : "Unknown error",
          },
        };
      }
    } catch (error) {
      logger.error(`Error tokenizing carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to tokenize carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Generate a token ID for a carbon credit
   * @param carbonCredit Carbon credit
   * @returns Token ID
   */
  private static generateTokenId(carbonCredit: any): number {
    // In a real implementation, this would use a more sophisticated algorithm
    // For now, we'll just use a simple hash of the carbon credit properties
    const hash = `${carbonCredit.projectId}-${carbonCredit.vintage}-${carbonCredit.standard}-${carbonCredit.methodology}`;
    const hashCode = Array.from(hash).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return Math.abs(hashCode) % 1000000000; // Limit to 9 digits
  }

  /**
   * Get wallet address for tokenization
   * @param userId User ID
   * @param organizationId Organization ID
   * @returns Wallet address
   */
  private static async getWalletAddress(userId: string, organizationId: string): Promise<string | null> {
    try {
      // Try to get user's wallet
      const userWallet = await db.wallet.findFirst({
        where: {
          userId,
          isPrimary: true,
        },
      });
      
      if (userWallet) {
        return userWallet.address;
      }
      
      // Try to get organization's wallet
      const organizationWallet = await db.wallet.findFirst({
        where: {
          organizationId,
          isPrimary: true,
        },
      });
      
      if (organizationWallet) {
        return organizationWallet.address;
      }
      
      return null;
    } catch (error) {
      logger.error(`Error getting wallet address for user ${userId} and organization ${organizationId}:`, error);
      throw new Error(`Failed to get wallet address: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get signer for blockchain transaction
   * @param userId User ID
   * @returns Signer
   */
  private static async getSigner(userId: string): Promise<any> {
    // In a real implementation, this would get a proper signer
    // For now, we'll just return a mock signer
    return {
      // Mock implementation
      connect: () => {},
      signMessage: () => {},
      signTransaction: () => {},
    };
  }

  /**
   * Get tokenization history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @returns Tokenization history
   */
  static async getTokenizationHistory(carbonCreditId: string): Promise<any[]> {
    try {
      return await db.carbonCreditTokenization.findMany({
        where: { carbonCreditId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } catch (error) {
      logger.error(`Error getting tokenization history for carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to get tokenization history: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
