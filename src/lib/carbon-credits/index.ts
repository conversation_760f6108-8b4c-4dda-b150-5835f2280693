import { 
  CarbonCreditStatus, 
  VerificationStatus, 
  TokenizationStatus 
} from '@prisma/client';
import { 
  CarbonCredit, 
  CarbonCreditCreationData, 
  CarbonCreditUpdateData,
  CarbonCreditFilter,
  CarbonCreditResponse,
  CarbonCreditBatchResult,
  VerificationResult,
  TokenizationResult
} from './types';
import { CarbonCreditService } from './service';
import { CarbonCreditVerification } from './verification';
import { CarbonCreditTokenization } from './tokenization';
import { SupportedNetwork } from '@/lib/blockchain/config/networks';

/**
 * Carbon credit manager
 */
export class CarbonCreditManager {
  /**
   * Create a new carbon credit
   * @param data Carbon credit creation data
   * @returns Created carbon credit
   */
  static async createCarbonCredit(data: CarbonCreditCreationData): Promise<CarbonCredit> {
    return CarbonCreditService.createCarbonCredit(data);
  }

  /**
   * Get carbon credit by ID
   * @param id Carbon credit ID
   * @returns Carbon credit
   */
  static async getCarbonCreditById(id: string): Promise<CarbonCredit | null> {
    return CarbonCreditService.getCarbonCreditById(id);
  }

  /**
   * Update a carbon credit
   * @param id Carbon credit ID
   * @param data Carbon credit update data
   * @param userId User ID performing the update
   * @returns Updated carbon credit
   */
  static async updateCarbonCredit(id: string, data: CarbonCreditUpdateData, userId: string): Promise<CarbonCredit> {
    return CarbonCreditService.updateCarbonCredit(id, data, userId);
  }

  /**
   * Delete a carbon credit
   * @param id Carbon credit ID
   * @param userId User ID performing the deletion
   * @returns Deleted carbon credit
   */
  static async deleteCarbonCredit(id: string, userId: string): Promise<CarbonCredit> {
    return CarbonCreditService.deleteCarbonCredit(id, userId);
  }

  /**
   * Get carbon credits
   * @param filter Filter options
   * @returns Carbon credits and pagination
   */
  static async getCarbonCredits(filter: CarbonCreditFilter = {}): Promise<CarbonCreditResponse> {
    return CarbonCreditService.getCarbonCredits(filter);
  }

  /**
   * Perform batch operation on carbon credits
   * @param ids Carbon credit IDs
   * @param operation Operation to perform
   * @param data Operation data
   * @param userId User ID performing the operation
   * @returns Batch operation result
   */
  static async batchOperation(
    ids: string[],
    operation: "update" | "delete" | "verify" | "tokenize",
    data: any,
    userId: string
  ): Promise<CarbonCreditBatchResult> {
    return CarbonCreditService.batchOperation(ids, operation, data, userId);
  }

  /**
   * Verify a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param verifierId User ID of the verifier
   * @param documents Verification documents
   * @returns Verification result
   */
  static async verifyCarbonCredit(
    carbonCreditId: string,
    verifierId: string,
    documents: {
      projectDocumentUrl: string;
      methodologyDocumentUrl: string;
      verificationReportUrl: string;
      additionalDocuments?: { name: string; url: string }[];
    }
  ): Promise<VerificationResult> {
    return CarbonCreditVerification.verifyCarbonCredit(carbonCreditId, verifierId, documents);
  }

  /**
   * Get verification history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @returns Verification history
   */
  static async getVerificationHistory(carbonCreditId: string): Promise<any[]> {
    return CarbonCreditVerification.getVerificationHistory(carbonCreditId);
  }

  /**
   * Tokenize a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param userId User ID performing the tokenization
   * @param options Tokenization options
   * @returns Tokenization result
   */
  static async tokenizeCarbonCredit(
    carbonCreditId: string,
    userId: string,
    options: {
      network?: SupportedNetwork;
      useTestnet?: boolean;
      walletAddress?: string;
    } = {}
  ): Promise<TokenizationResult> {
    return CarbonCreditTokenization.tokenizeCarbonCredit(carbonCreditId, userId, options);
  }

  /**
   * Get tokenization history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @returns Tokenization history
   */
  static async getTokenizationHistory(carbonCreditId: string): Promise<any[]> {
    return CarbonCreditTokenization.getTokenizationHistory(carbonCreditId);
  }
}

// Create a singleton instance
const carbonCreditManager = new CarbonCreditManager();

// Export the singleton instance
export { carbonCreditManager };

// Export types and enums
export {
  CarbonCreditStatus,
  VerificationStatus,
  TokenizationStatus,
  CarbonCredit,
  CarbonCreditCreationData,
  CarbonCreditUpdateData,
  CarbonCreditFilter,
  CarbonCreditResponse,
  CarbonCreditBatchResult,
  VerificationResult,
  TokenizationResult
};
