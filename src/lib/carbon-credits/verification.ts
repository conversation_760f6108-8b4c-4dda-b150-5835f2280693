import { VerificationStatus } from '@prisma/client';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { VerificationResult } from './types';

/**
 * Carbon credit verification service
 */
export class CarbonCreditVerification {
  /**
   * Verify a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @param verifierId User ID of the verifier
   * @param documents Verification documents
   * @returns Verification result
   */
  static async verifyCarbonCredit(
    carbonCreditId: string,
    verifierId: string,
    documents: {
      projectDocumentUrl: string;
      methodologyDocumentUrl: string;
      verificationReportUrl: string;
      additionalDocuments?: { name: string; url: string }[];
    }
  ): Promise<VerificationResult> {
    try {
      logger.info(`Verifying carbon credit ${carbonCreditId} by verifier ${verifierId}`);
      
      // Get carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: carbonCreditId },
        include: {
          organization: true,
          user: true,
        },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${carbonCreditId}`);
      }
      
      // Check if carbon credit is already verified
      if (carbonCredit.verificationStatus === VerificationStatus.VERIFIED) {
        return {
          success: false,
          message: "Carbon credit is already verified",
          verificationStatus: VerificationStatus.VERIFIED,
        };
      }
      
      // Check if carbon credit is rejected
      if (carbonCredit.verificationStatus === VerificationStatus.REJECTED) {
        return {
          success: false,
          message: "Carbon credit verification was previously rejected",
          verificationStatus: VerificationStatus.REJECTED,
        };
      }
      
      // Check if verifier has permission
      const verifier = await db.user.findUnique({
        where: { id: verifierId },
        select: { role: true },
      });
      
      if (verifier?.role !== "ADMIN" && verifier?.role !== "VERIFIER") {
        throw new Error("You do not have permission to verify carbon credits");
      }
      
      // Perform verification checks
      const verificationChecks = await this.performVerificationChecks(carbonCredit, documents);
      
      // If verification checks pass, update carbon credit
      if (verificationChecks.success) {
        // Create verification record
        const verification = await db.carbonCreditVerification.create({
          data: {
            carbonCreditId,
            verifierId,
            status: VerificationStatus.VERIFIED,
            notes: "Verification checks passed",
            projectDocumentUrl: documents.projectDocumentUrl,
            methodologyDocumentUrl: documents.methodologyDocumentUrl,
            verificationReportUrl: documents.verificationReportUrl,
            additionalDocuments: documents.additionalDocuments as any,
          },
        });
        
        // Update carbon credit
        await db.carbonCredit.update({
          where: { id: carbonCreditId },
          data: {
            verificationStatus: VerificationStatus.VERIFIED,
            verificationNotes: "Verification checks passed",
            verifiedAt: new Date(),
            verifiedBy: {
              connect: { id: verifierId },
            },
            status: "VERIFIED",
          },
        });
        
        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_VERIFIED",
            description: `Carbon credit ${carbonCreditId} verified`,
            userId: verifierId,
            organizationId: carbonCredit.organizationId,
            metadata: {
              carbonCreditId,
              verificationId: verification.id,
              documents,
            },
          },
        });
        
        return {
          success: true,
          message: "Carbon credit verified successfully",
          verificationStatus: VerificationStatus.VERIFIED,
          details: verificationChecks.details,
        };
      } else {
        // Create verification record
        const verification = await db.carbonCreditVerification.create({
          data: {
            carbonCreditId,
            verifierId,
            status: VerificationStatus.REJECTED,
            notes: verificationChecks.message,
            projectDocumentUrl: documents.projectDocumentUrl,
            methodologyDocumentUrl: documents.methodologyDocumentUrl,
            verificationReportUrl: documents.verificationReportUrl,
            additionalDocuments: documents.additionalDocuments as any,
          },
        });
        
        // Update carbon credit
        await db.carbonCredit.update({
          where: { id: carbonCreditId },
          data: {
            verificationStatus: VerificationStatus.REJECTED,
            verificationNotes: verificationChecks.message,
            verifiedAt: new Date(),
            verifiedBy: {
              connect: { id: verifierId },
            },
            status: "REJECTED",
          },
        });
        
        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_VERIFICATION_REJECTED",
            description: `Carbon credit ${carbonCreditId} verification rejected: ${verificationChecks.message}`,
            userId: verifierId,
            organizationId: carbonCredit.organizationId,
            metadata: {
              carbonCreditId,
              verificationId: verification.id,
              documents,
              reason: verificationChecks.message,
            },
          },
        });
        
        return {
          success: false,
          message: verificationChecks.message,
          verificationStatus: VerificationStatus.REJECTED,
          details: verificationChecks.details,
        };
      }
    } catch (error) {
      logger.error(`Error verifying carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to verify carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Perform verification checks
   * @param carbonCredit Carbon credit
   * @param documents Verification documents
   * @returns Verification checks result
   */
  private static async performVerificationChecks(
    carbonCredit: any,
    documents: {
      projectDocumentUrl: string;
      methodologyDocumentUrl: string;
      verificationReportUrl: string;
      additionalDocuments?: { name: string; url: string }[];
    }
  ): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Check if all required documents are provided
      if (!documents.projectDocumentUrl || !documents.methodologyDocumentUrl || !documents.verificationReportUrl) {
        return {
          success: false,
          message: "Missing required verification documents",
          details: {
            projectDocumentUrl: !!documents.projectDocumentUrl,
            methodologyDocumentUrl: !!documents.methodologyDocumentUrl,
            verificationReportUrl: !!documents.verificationReportUrl,
          },
        };
      }
      
      // Check if project document URL is valid
      if (!this.isValidUrl(documents.projectDocumentUrl)) {
        return {
          success: false,
          message: "Invalid project document URL",
          details: {
            projectDocumentUrl: documents.projectDocumentUrl,
          },
        };
      }
      
      // Check if methodology document URL is valid
      if (!this.isValidUrl(documents.methodologyDocumentUrl)) {
        return {
          success: false,
          message: "Invalid methodology document URL",
          details: {
            methodologyDocumentUrl: documents.methodologyDocumentUrl,
          },
        };
      }
      
      // Check if verification report URL is valid
      if (!this.isValidUrl(documents.verificationReportUrl)) {
        return {
          success: false,
          message: "Invalid verification report URL",
          details: {
            verificationReportUrl: documents.verificationReportUrl,
          },
        };
      }
      
      // Check if additional documents URLs are valid
      if (documents.additionalDocuments) {
        for (const doc of documents.additionalDocuments) {
          if (!this.isValidUrl(doc.url)) {
            return {
              success: false,
              message: `Invalid URL for additional document: ${doc.name}`,
              details: {
                additionalDocument: doc,
              },
            };
          }
        }
      }
      
      // In a real implementation, additional checks would be performed here
      // For example:
      // - Verify the project exists in a registry
      // - Verify the methodology is valid for the project
      // - Verify the vintage is valid
      // - Verify the quantity is valid
      // - Verify the organization is authorized to issue carbon credits
      
      // For now, we'll just return success
      return {
        success: true,
        message: "Verification checks passed",
        details: {
          projectDocumentUrl: documents.projectDocumentUrl,
          methodologyDocumentUrl: documents.methodologyDocumentUrl,
          verificationReportUrl: documents.verificationReportUrl,
          additionalDocuments: documents.additionalDocuments,
        },
      };
    } catch (error) {
      logger.error("Error performing verification checks:", error);
      throw new Error(`Failed to perform verification checks: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Check if a URL is valid
   * @param url URL to check
   * @returns Whether the URL is valid
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get verification history for a carbon credit
   * @param carbonCreditId Carbon credit ID
   * @returns Verification history
   */
  static async getVerificationHistory(carbonCreditId: string): Promise<any[]> {
    try {
      return await db.carbonCreditVerification.findMany({
        where: { carbonCreditId },
        include: {
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } catch (error) {
      logger.error(`Error getting verification history for carbon credit ${carbonCreditId}:`, error);
      throw new Error(`Failed to get verification history: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
