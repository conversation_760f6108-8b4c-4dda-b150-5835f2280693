import { 
  CarbonCreditStatus, 
  VerificationStatus, 
  TokenizationStatus,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { 
  CarbonCredit, 
  CarbonCreditCreationData, 
  CarbonCreditUpdateData,
  CarbonCreditFilter,
  CarbonCreditResponse,
  CarbonCreditBatchResult
} from './types';
import { notificationService } from '@/lib/notifications';

/**
 * Carbon credit service
 */
export class CarbonCreditService {
  /**
   * Create a new carbon credit
   * @param data Carbon credit creation data
   * @returns Created carbon credit
   */
  static async createCarbonCredit(data: CarbonCreditCreationData): Promise<CarbonCredit> {
    try {
      logger.info(`Creating carbon credit for project ${data.projectId} by user ${data.userId}`);
      
      // Create carbon credit
      const carbonCredit = await db.carbonCredit.create({
        data: {
          projectId: data.projectId,
          vintage: data.vintage,
          standard: data.standard,
          methodology: data.methodology,
          description: data.description,
          quantity: data.quantity,
          availableQuantity: data.quantity,
          price: data.price,
          status: CarbonCreditStatus.PENDING,
          verificationStatus: VerificationStatus.PENDING,
          tokenizationStatus: TokenizationStatus.NOT_TOKENIZED,
          expirationDate: data.expirationDate,
          organization: {
            connect: { id: data.organizationId }
          },
          user: {
            connect: { id: data.userId }
          },
        },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "CARBON_CREDIT_CREATED",
          description: `Carbon credit created for project ${data.projectId}`,
          userId: data.userId,
          organizationId: data.organizationId,
          metadata: {
            carbonCreditId: carbonCredit.id,
            projectId: data.projectId,
            vintage: data.vintage,
            standard: data.standard,
            methodology: data.methodology,
            quantity: data.quantity,
            price: data.price,
          },
        },
      });
      
      // Send notification to organization admins
      const organizationAdmins = await db.user.findMany({
        where: {
          organizationId: data.organizationId,
          role: "ORGANIZATION_ADMIN",
        },
        select: { id: true },
      });
      
      const adminIds = organizationAdmins.map((admin) => admin.id);
      
      if (adminIds.length > 0) {
        await notificationService.createNotificationsForUsers(adminIds, {
          title: "New Carbon Credit Created",
          message: `A new carbon credit for project ${data.projectId} has been created and is pending verification.`,
          type: "CARBON_CREDIT",
          priority: "NORMAL",
          actionUrl: `/dashboard/carbon-credits/${carbonCredit.id}`,
          actionLabel: "View Carbon Credit",
          organizationId: data.organizationId,
        });
      }
      
      // Send notification to platform admins
      const platformAdmins = await db.user.findMany({
        where: {
          role: "ADMIN",
        },
        select: { id: true },
      });
      
      const platformAdminIds = platformAdmins.map((admin) => admin.id);
      
      if (platformAdminIds.length > 0) {
        await notificationService.createNotificationsForUsers(platformAdminIds, {
          title: "New Carbon Credit Pending Verification",
          message: `A new carbon credit for project ${data.projectId} has been created and is pending verification.`,
          type: "CARBON_CREDIT",
          priority: "NORMAL",
          actionUrl: `/admin/carbon-credits/verification`,
          actionLabel: "Verify Carbon Credit",
        });
      }
      
      return carbonCredit;
    } catch (error) {
      logger.error(`Error creating carbon credit for project ${data.projectId}:`, error);
      throw new Error(`Failed to create carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get carbon credit by ID
   * @param id Carbon credit ID
   * @returns Carbon credit
   */
  static async getCarbonCreditById(id: string): Promise<CarbonCredit | null> {
    try {
      return await db.carbonCredit.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error getting carbon credit ${id}:`, error);
      throw new Error(`Failed to get carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Update a carbon credit
   * @param id Carbon credit ID
   * @param data Carbon credit update data
   * @param userId User ID performing the update
   * @returns Updated carbon credit
   */
  static async updateCarbonCredit(id: string, data: CarbonCreditUpdateData, userId: string): Promise<CarbonCredit> {
    try {
      // Get current carbon credit
      const currentCarbonCredit = await db.carbonCredit.findUnique({
        where: { id },
        include: {
          organization: true,
        },
      });
      
      if (!currentCarbonCredit) {
        throw new Error(`Carbon credit not found: ${id}`);
      }
      
      // Check if user has permission to update
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === currentCarbonCredit.organizationId;
      const isOwner = userId === currentCarbonCredit.userId;
      
      if (!isAdmin && !isOrgAdmin && !isOwner) {
        throw new Error("You do not have permission to update this carbon credit");
      }
      
      // Check if carbon credit can be updated
      if (
        currentCarbonCredit.status === CarbonCreditStatus.SOLD ||
        currentCarbonCredit.status === CarbonCreditStatus.RETIRED
      ) {
        throw new Error(`Cannot update carbon credit with status ${currentCarbonCredit.status}`);
      }
      
      // Update carbon credit
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id },
        data: {
          ...(data.description !== undefined && { description: data.description }),
          ...(data.price !== undefined && { price: data.price }),
          ...(data.expirationDate !== undefined && { expirationDate: data.expirationDate }),
          ...(data.status !== undefined && { 
            status: data.status,
            ...(data.status === CarbonCreditStatus.LISTED && { listingDate: new Date() }),
          }),
        },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "CARBON_CREDIT_UPDATED",
          description: `Carbon credit ${id} updated`,
          userId,
          organizationId: currentCarbonCredit.organizationId,
          metadata: {
            carbonCreditId: id,
            previousState: {
              description: currentCarbonCredit.description,
              price: currentCarbonCredit.price,
              expirationDate: currentCarbonCredit.expirationDate,
              status: currentCarbonCredit.status,
            },
            newState: {
              description: updatedCarbonCredit.description,
              price: updatedCarbonCredit.price,
              expirationDate: updatedCarbonCredit.expirationDate,
              status: updatedCarbonCredit.status,
            },
          },
        },
      });
      
      // Send notification if status changed to LISTED
      if (data.status === CarbonCreditStatus.LISTED && currentCarbonCredit.status !== CarbonCreditStatus.LISTED) {
        // Notify organization members
        await notificationService.createOrganizationNotification(
          currentCarbonCredit.organizationId,
          {
            title: "Carbon Credit Listed",
            message: `Carbon credit for project ${currentCarbonCredit.projectId} has been listed on the marketplace.`,
            type: "CARBON_CREDIT",
            priority: "NORMAL",
            actionUrl: `/dashboard/carbon-credits/${id}`,
            actionLabel: "View Carbon Credit",
          }
        );
      }
      
      return updatedCarbonCredit;
    } catch (error) {
      logger.error(`Error updating carbon credit ${id}:`, error);
      throw new Error(`Failed to update carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Delete a carbon credit
   * @param id Carbon credit ID
   * @param userId User ID performing the deletion
   * @returns Deleted carbon credit
   */
  static async deleteCarbonCredit(id: string, userId: string): Promise<CarbonCredit> {
    try {
      // Get current carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${id}`);
      }
      
      // Check if user has permission to delete
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === carbonCredit.organizationId;
      const isOwner = userId === carbonCredit.userId;
      
      if (!isAdmin && !isOrgAdmin && !isOwner) {
        throw new Error("You do not have permission to delete this carbon credit");
      }
      
      // Check if carbon credit can be deleted
      if (
        carbonCredit.status === CarbonCreditStatus.SOLD ||
        carbonCredit.status === CarbonCreditStatus.RETIRED ||
        carbonCredit.tokenizationStatus === TokenizationStatus.TOKENIZED
      ) {
        throw new Error(`Cannot delete carbon credit with status ${carbonCredit.status} or tokenization status ${carbonCredit.tokenizationStatus}`);
      }
      
      // Delete carbon credit
      const deletedCarbonCredit = await db.carbonCredit.delete({
        where: { id },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "CARBON_CREDIT_DELETED",
          description: `Carbon credit ${id} deleted`,
          userId,
          organizationId: carbonCredit.organizationId,
          metadata: {
            carbonCreditId: id,
            projectId: carbonCredit.projectId,
            vintage: carbonCredit.vintage,
            standard: carbonCredit.standard,
            methodology: carbonCredit.methodology,
            quantity: carbonCredit.quantity,
            price: carbonCredit.price,
          },
        },
      });
      
      return deletedCarbonCredit;
    } catch (error) {
      logger.error(`Error deleting carbon credit ${id}:`, error);
      throw new Error(`Failed to delete carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get carbon credits
   * @param filter Filter options
   * @returns Carbon credits and pagination
   */
  static async getCarbonCredits(filter: CarbonCreditFilter = {}): Promise<CarbonCreditResponse> {
    try {
      const {
        organizationId,
        userId,
        status,
        verificationStatus,
        tokenizationStatus,
        standard,
        methodology,
        vintage,
        minPrice,
        maxPrice,
        minQuantity,
        maxQuantity,
        search,
        limit = 10,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = filter;
      
      // Build query
      const where: Prisma.CarbonCreditWhereInput = {
        ...(organizationId && { organizationId }),
        ...(userId && { userId }),
        ...(status && { status }),
        ...(verificationStatus && { verificationStatus }),
        ...(tokenizationStatus && { tokenizationStatus }),
        ...(standard && { standard }),
        ...(methodology && { methodology }),
        ...(vintage && { vintage }),
        ...(minPrice !== undefined && { price: { gte: minPrice } }),
        ...(maxPrice !== undefined && { price: { lte: maxPrice } }),
        ...(minQuantity !== undefined && { availableQuantity: { gte: minQuantity } }),
        ...(maxQuantity !== undefined && { availableQuantity: { lte: maxQuantity } }),
        ...(search && {
          OR: [
            { projectId: { contains: search, mode: "insensitive" } },
            { description: { contains: search, mode: "insensitive" } },
            { standard: { contains: search, mode: "insensitive" } },
            { methodology: { contains: search, mode: "insensitive" } },
          ],
        }),
      };
      
      // Get carbon credits
      const carbonCredits = await db.carbonCredit.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: offset,
        take: limit,
      });
      
      // Get total count
      const total = await db.carbonCredit.count({ where });
      
      return {
        carbonCredits,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error getting carbon credits:", error);
      throw new Error(`Failed to get carbon credits: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Perform batch operation on carbon credits
   * @param ids Carbon credit IDs
   * @param operation Operation to perform
   * @param data Operation data
   * @param userId User ID performing the operation
   * @returns Batch operation result
   */
  static async batchOperation(
    ids: string[],
    operation: "update" | "delete" | "verify" | "tokenize",
    data: any,
    userId: string
  ): Promise<CarbonCreditBatchResult> {
    try {
      const result: CarbonCreditBatchResult = {
        success: true,
        successCount: 0,
        failureCount: 0,
        errors: [],
      };
      
      for (const id of ids) {
        try {
          switch (operation) {
            case "update":
              await this.updateCarbonCredit(id, data, userId);
              break;
            case "delete":
              await this.deleteCarbonCredit(id, userId);
              break;
            case "verify":
              await this.verifyCarbonCredit(id, data, userId);
              break;
            case "tokenize":
              await this.tokenizeCarbonCredit(id, data, userId);
              break;
          }
          
          result.successCount++;
        } catch (error) {
          result.failureCount++;
          result.errors.push({
            id,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }
      
      result.success = result.failureCount === 0;
      
      return result;
    } catch (error) {
      logger.error(`Error performing batch operation ${operation} on carbon credits:`, error);
      throw new Error(`Failed to perform batch operation: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Verify a carbon credit
   * @param id Carbon credit ID
   * @param data Verification data
   * @param userId User ID performing the verification
   * @returns Verified carbon credit
   */
  static async verifyCarbonCredit(
    id: string,
    data: {
      verificationStatus: VerificationStatus;
      verificationNotes?: string;
    },
    userId: string
  ): Promise<CarbonCredit> {
    try {
      // Get current carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id },
        include: {
          organization: true,
          user: true,
        },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${id}`);
      }
      
      // Check if user has permission to verify
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true },
      });
      
      if (user?.role !== "ADMIN" && user?.role !== "VERIFIER") {
        throw new Error("You do not have permission to verify carbon credits");
      }
      
      // Update carbon credit
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id },
        data: {
          verificationStatus: data.verificationStatus,
          verificationNotes: data.verificationNotes,
          verifiedAt: new Date(),
          verifiedBy: {
            connect: { id: userId },
          },
          // If verified, update status to VERIFIED
          ...(data.verificationStatus === VerificationStatus.VERIFIED && {
            status: CarbonCreditStatus.VERIFIED,
          }),
          // If rejected, update status to REJECTED
          ...(data.verificationStatus === VerificationStatus.REJECTED && {
            status: CarbonCreditStatus.REJECTED,
          }),
        },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "CARBON_CREDIT_VERIFIED",
          description: `Carbon credit ${id} verification status updated to ${data.verificationStatus}`,
          userId,
          organizationId: carbonCredit.organizationId,
          metadata: {
            carbonCreditId: id,
            previousStatus: carbonCredit.verificationStatus,
            newStatus: data.verificationStatus,
            verificationNotes: data.verificationNotes,
          },
        },
      });
      
      // Send notification to owner
      await notificationService.createNotification({
        userId: carbonCredit.userId,
        title: `Carbon Credit ${data.verificationStatus === VerificationStatus.VERIFIED ? "Verified" : "Rejected"}`,
        message: `Your carbon credit for project ${carbonCredit.projectId} has been ${data.verificationStatus === VerificationStatus.VERIFIED ? "verified" : "rejected"}.`,
        type: "CARBON_CREDIT",
        priority: "HIGH",
        actionUrl: `/dashboard/carbon-credits/${id}`,
        actionLabel: "View Carbon Credit",
        organizationId: carbonCredit.organizationId,
      });
      
      // Send notification to organization admins
      const organizationAdmins = await db.user.findMany({
        where: {
          organizationId: carbonCredit.organizationId,
          role: "ORGANIZATION_ADMIN",
          id: { not: carbonCredit.userId }, // Exclude owner who already received a notification
        },
        select: { id: true },
      });
      
      const adminIds = organizationAdmins.map((admin) => admin.id);
      
      if (adminIds.length > 0) {
        await notificationService.createNotificationsForUsers(adminIds, {
          title: `Carbon Credit ${data.verificationStatus === VerificationStatus.VERIFIED ? "Verified" : "Rejected"}`,
          message: `Carbon credit for project ${carbonCredit.projectId} has been ${data.verificationStatus === VerificationStatus.VERIFIED ? "verified" : "rejected"}.`,
          type: "CARBON_CREDIT",
          priority: "NORMAL",
          actionUrl: `/dashboard/carbon-credits/${id}`,
          actionLabel: "View Carbon Credit",
          organizationId: carbonCredit.organizationId,
        });
      }
      
      return updatedCarbonCredit;
    } catch (error) {
      logger.error(`Error verifying carbon credit ${id}:`, error);
      throw new Error(`Failed to verify carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Tokenize a carbon credit
   * @param id Carbon credit ID
   * @param data Tokenization data
   * @param userId User ID performing the tokenization
   * @returns Tokenized carbon credit
   */
  static async tokenizeCarbonCredit(
    id: string,
    data: {
      tokenId: string;
      tokenizationStatus: TokenizationStatus;
      tokenizationNotes?: string;
    },
    userId: string
  ): Promise<CarbonCredit> {
    try {
      // Get current carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id },
        include: {
          organization: true,
          user: true,
        },
      });
      
      if (!carbonCredit) {
        throw new Error(`Carbon credit not found: ${id}`);
      }
      
      // Check if carbon credit can be tokenized
      if (carbonCredit.verificationStatus !== VerificationStatus.VERIFIED) {
        throw new Error("Carbon credit must be verified before tokenization");
      }
      
      // Check if user has permission to tokenize
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true, organizationId: true },
      });
      
      const isAdmin = user?.role === "ADMIN";
      const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN" && user?.organizationId === carbonCredit.organizationId;
      const isOwner = userId === carbonCredit.userId;
      
      if (!isAdmin && !isOrgAdmin && !isOwner) {
        throw new Error("You do not have permission to tokenize this carbon credit");
      }
      
      // Update carbon credit
      const updatedCarbonCredit = await db.carbonCredit.update({
        where: { id },
        data: {
          tokenId: data.tokenId,
          tokenizationStatus: data.tokenizationStatus,
          tokenizationNotes: data.tokenizationNotes,
          tokenizedAt: new Date(),
          tokenizedBy: {
            connect: { id: userId },
          },
        },
      });
      
      // Create audit log
      await db.auditLog.create({
        data: {
          type: "CARBON_CREDIT_TOKENIZED",
          description: `Carbon credit ${id} tokenization status updated to ${data.tokenizationStatus}`,
          userId,
          organizationId: carbonCredit.organizationId,
          metadata: {
            carbonCreditId: id,
            previousStatus: carbonCredit.tokenizationStatus,
            newStatus: data.tokenizationStatus,
            tokenId: data.tokenId,
            tokenizationNotes: data.tokenizationNotes,
          },
        },
      });
      
      // Send notification to owner
      await notificationService.createNotification({
        userId: carbonCredit.userId,
        title: `Carbon Credit ${data.tokenizationStatus === TokenizationStatus.TOKENIZED ? "Tokenized" : "Tokenization Failed"}`,
        message: `Your carbon credit for project ${carbonCredit.projectId} has been ${data.tokenizationStatus === TokenizationStatus.TOKENIZED ? "tokenized" : "failed to tokenize"}.`,
        type: "CARBON_CREDIT",
        priority: "HIGH",
        actionUrl: `/dashboard/carbon-credits/${id}`,
        actionLabel: "View Carbon Credit",
        organizationId: carbonCredit.organizationId,
      });
      
      // Send notification to organization admins
      const organizationAdmins = await db.user.findMany({
        where: {
          organizationId: carbonCredit.organizationId,
          role: "ORGANIZATION_ADMIN",
          id: { not: carbonCredit.userId }, // Exclude owner who already received a notification
        },
        select: { id: true },
      });
      
      const adminIds = organizationAdmins.map((admin) => admin.id);
      
      if (adminIds.length > 0) {
        await notificationService.createNotificationsForUsers(adminIds, {
          title: `Carbon Credit ${data.tokenizationStatus === TokenizationStatus.TOKENIZED ? "Tokenized" : "Tokenization Failed"}`,
          message: `Carbon credit for project ${carbonCredit.projectId} has been ${data.tokenizationStatus === TokenizationStatus.TOKENIZED ? "tokenized" : "failed to tokenize"}.`,
          type: "CARBON_CREDIT",
          priority: "NORMAL",
          actionUrl: `/dashboard/carbon-credits/${id}`,
          actionLabel: "View Carbon Credit",
          organizationId: carbonCredit.organizationId,
        });
      }
      
      return updatedCarbonCredit;
    } catch (error) {
      logger.error(`Error tokenizing carbon credit ${id}:`, error);
      throw new Error(`Failed to tokenize carbon credit: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
