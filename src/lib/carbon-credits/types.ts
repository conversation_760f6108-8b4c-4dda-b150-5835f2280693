import { 
  CarbonCreditStatus, 
  VerificationStatus, 
  TokenizationStatus 
} from '@prisma/client';

/**
 * Carbon credit interface
 */
export interface CarbonCredit {
  id: string;
  projectId: string;
  vintage: number;
  standard: string;
  methodology: string;
  description: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  status: CarbonCreditStatus;
  verificationStatus: VerificationStatus;
  tokenizationStatus: TokenizationStatus;
  tokenId?: string;
  listingDate?: Date;
  expirationDate?: Date;
  organizationId: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Carbon credit creation data
 */
export interface CarbonCreditCreationData {
  projectId: string;
  vintage: number;
  standard: string;
  methodology: string;
  description: string;
  quantity: number;
  price: number;
  expirationDate?: Date;
  organizationId: string;
  userId: string;
}

/**
 * Carbon credit update data
 */
export interface CarbonCreditUpdateData {
  description?: string;
  price?: number;
  expirationDate?: Date;
  status?: CarbonCreditStatus;
}

/**
 * Carbon credit verification data
 */
export interface CarbonCreditVerificationData {
  verificationStatus: VerificationStatus;
  verificationNotes?: string;
  verifierId: string;
}

/**
 * Carbon credit tokenization data
 */
export interface CarbonCreditTokenizationData {
  tokenId: string;
  tokenizationStatus: TokenizationStatus;
  tokenizationNotes?: string;
  tokenizationDate?: Date;
}

/**
 * Carbon credit filter interface
 */
export interface CarbonCreditFilter {
  organizationId?: string;
  userId?: string;
  status?: CarbonCreditStatus;
  verificationStatus?: VerificationStatus;
  tokenizationStatus?: TokenizationStatus;
  standard?: string;
  methodology?: string;
  vintage?: number;
  minPrice?: number;
  maxPrice?: number;
  minQuantity?: number;
  maxQuantity?: number;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Carbon credit pagination interface
 */
export interface CarbonCreditPagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

/**
 * Carbon credit response interface
 */
export interface CarbonCreditResponse {
  carbonCredits: CarbonCredit[];
  pagination: CarbonCreditPagination;
}

/**
 * Carbon credit batch operation result
 */
export interface CarbonCreditBatchResult {
  success: boolean;
  successCount: number;
  failureCount: number;
  errors: { id: string; error: string }[];
}

/**
 * Carbon credit verification result
 */
export interface VerificationResult {
  success: boolean;
  message: string;
  verificationStatus: VerificationStatus;
  details?: any;
}

/**
 * Carbon credit tokenization result
 */
export interface TokenizationResult {
  success: boolean;
  message: string;
  tokenizationStatus: TokenizationStatus;
  tokenId?: string;
  transactionHash?: string;
  details?: any;
}
