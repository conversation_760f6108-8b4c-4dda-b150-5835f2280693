"use client";

import { useEffect, useState } from "react";

/**
 * Hook to detect if the user prefers reduced motion
 * @returns {boolean} True if the user prefers reduced motion
 */
export function useReducedMotion(): boolean {
  // Default to false (animations enabled) for SSR
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    // Check if the browser supports matchMedia
    if (typeof window !== "undefined" && window.matchMedia) {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      
      // Set initial value
      setPrefersReducedMotion(mediaQuery.matches);
      
      // Add listener for changes
      const handleChange = () => {
        setPrefersReducedMotion(mediaQuery.matches);
      };
      
      // Modern browsers
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener("change", handleChange);
        return () => {
          mediaQuery.removeEventListener("change", handleChange);
        };
      } 
      // Older browsers
      else if (mediaQuery.addListener) {
        mediaQuery.addListener(handleChange);
        return () => {
          mediaQuery.removeListener(handleChange);
        };
      }
    }
  }, []);

  return prefersReducedMotion;
}
