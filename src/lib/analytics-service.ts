import { 
  CarbonCreditStatus, 
  OrderStatus, 
  TransactionStatus,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';

/**
 * Time period for analytics
 */
export type TimePeriod = 'day' | 'week' | 'month' | 'year' | 'all';

/**
 * Analytics service for generating analytics and reports
 */
export class AnalyticsService {
  /**
   * Get date range for a time period
   * @param period Time period
   * @returns Start and end dates
   */
  private static getDateRange(period: TimePeriod): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
    }

    return { startDate, endDate };
  }

  /**
   * Get platform overview analytics
   * @param period Time period
   * @returns Platform overview analytics
   */
  static async getPlatformOverview(period: TimePeriod = 'month') {
    try {
      const { startDate, endDate } = this.getDateRange(period);

      // Get total organizations
      const totalOrganizations = await db.organization.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total users
      const totalUsers = await db.user.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get total orders
      const totalOrders = await db.order.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total orders by status
      const ordersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get total transactions
      const totalTransactions = await db.transaction.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total transaction volume
      const transactionVolume = await db.transaction.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: TransactionStatus.COMPLETED,
        },
        _sum: {
          amount: true,
        },
      });

      // Get total transaction fees
      const transactionFees = await db.transaction.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: TransactionStatus.COMPLETED,
        },
        _sum: {
          fee: true,
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits traded quantity
      const carbonCreditTradedQuantity = await db.order.aggregate({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get daily transaction volume for the period
      const dailyTransactionVolume = await db.$queryRaw<{ date: string; volume: number }[]>`
        SELECT 
          DATE_TRUNC('day', "createdAt") as date,
          SUM(amount) as volume
        FROM "Transaction"
        WHERE 
          "createdAt" >= ${startDate} AND 
          "createdAt" <= ${endDate} AND
          status = 'COMPLETED'
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        organizations: {
          total: totalOrganizations,
        },
        users: {
          total: totalUsers,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          tradedQuantity: carbonCreditTradedQuantity._sum.quantity || 0,
        },
        orders: {
          total: totalOrders,
          byStatus: ordersByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<OrderStatus, number>),
        },
        transactions: {
          total: totalTransactions,
          volume: transactionVolume._sum.amount || 0,
          fees: transactionFees._sum.fee || 0,
          dailyVolume: dailyTransactionVolume,
        },
      };
    } catch (error) {
      logger.error('Error getting platform overview:', error);
      throw error;
    }
  }

  /**
   * Get organization analytics
   * @param organizationId Organization ID
   * @param period Time period
   * @returns Organization analytics
   */
  static async getOrganizationAnalytics(organizationId: string, period: TimePeriod = 'month') {
    try {
      const { startDate, endDate } = this.getDateRange(period);

      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Get total users in the organization
      const totalUsers = await db.user.count({
        where: {
          organizationId,
        },
      });

      // Get new users in the period
      const newUsers = await db.user.count({
        where: {
          organizationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count({
        where: {
          organizationId,
        },
      });

      // Get new carbon credits in the period
      const newCarbonCredits = await db.carbonCredit.count({
        where: {
          organizationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        where: {
          organizationId,
        },
        _count: {
          id: true,
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        where: {
          organizationId,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits available quantity
      const carbonCreditAvailableQuantity = await db.carbonCredit.aggregate({
        where: {
          organizationId,
        },
        _sum: {
          availableQuantity: true,
        },
      });

      // Get total buy orders
      const totalBuyOrders = await db.order.count({
        where: {
          buyer: {
            organizationId,
          },
        },
      });

      // Get new buy orders in the period
      const newBuyOrders = await db.order.count({
        where: {
          buyer: {
            organizationId,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total sell orders
      const totalSellOrders = await db.order.count({
        where: {
          seller: {
            organizationId,
          },
        },
      });

      // Get new sell orders in the period
      const newSellOrders = await db.order.count({
        where: {
          seller: {
            organizationId,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get buy orders by status
      const buyOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          buyer: {
            organizationId,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get sell orders by status
      const sellOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          seller: {
            organizationId,
          },
        },
        _count: {
          id: true,
        },
      });

      // Get total buy volume
      const buyVolume = await db.order.aggregate({
        where: {
          buyer: {
            organizationId,
          },
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total sell volume
      const sellVolume = await db.order.aggregate({
        where: {
          seller: {
            organizationId,
          },
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total transaction fees paid
      const transactionFeesPaid = await db.billingRecord.aggregate({
        where: {
          organizationId,
          type: 'TRANSACTION_FEE',
          status: BillingStatus.PAID,
        },
        _sum: {
          amount: true,
        },
      });

      // Get total listing fees paid
      const listingFeesPaid = await db.billingRecord.aggregate({
        where: {
          organizationId,
          type: 'LISTING_FEE',
          status: BillingStatus.PAID,
        },
        _sum: {
          amount: true,
        },
      });

      // Get daily transaction volume for the period
      const dailyTransactionVolume = await db.$queryRaw<{ date: string; buyVolume: number; sellVolume: number }[]>`
        SELECT 
          DATE_TRUNC('day', o."createdAt") as date,
          SUM(CASE WHEN bu."organizationId" = ${organizationId} THEN o.quantity * o.price ELSE 0 END) as "buyVolume",
          SUM(CASE WHEN se."organizationId" = ${organizationId} THEN o.quantity * o.price ELSE 0 END) as "sellVolume"
        FROM "Order" o
        JOIN "User" bu ON o."buyerId" = bu.id
        JOIN "User" se ON o."sellerId" = se.id
        WHERE 
          o."createdAt" >= ${startDate} AND 
          o."createdAt" <= ${endDate} AND
          o.status = 'COMPLETED' AND
          (bu."organizationId" = ${organizationId} OR se."organizationId" = ${organizationId})
        GROUP BY DATE_TRUNC('day', o."createdAt")
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        organization: {
          id: organization.id,
          name: organization.name,
          status: organization.status,
          verificationStatus: organization.verificationStatus,
          transactionFeeRate: organization.transactionFeeRate,
          listingFeeRate: organization.listingFeeRate,
        },
        users: {
          total: totalUsers,
          new: newUsers,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          new: newCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          availableQuantity: carbonCreditAvailableQuantity._sum.availableQuantity || 0,
        },
        orders: {
          buy: {
            total: totalBuyOrders,
            new: newBuyOrders,
            byStatus: buyOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: buyVolume._sum.quantity || 0,
          },
          sell: {
            total: totalSellOrders,
            new: newSellOrders,
            byStatus: sellOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: sellVolume._sum.quantity || 0,
          },
        },
        fees: {
          transactionFees: transactionFeesPaid._sum.amount || 0,
          listingFees: listingFeesPaid._sum.amount || 0,
          total: (transactionFeesPaid._sum.amount || 0) + (listingFeesPaid._sum.amount || 0),
        },
        transactions: {
          dailyVolume: dailyTransactionVolume,
        },
      };
    } catch (error) {
      logger.error('Error getting organization analytics:', error);
      throw error;
    }
  }

  /**
   * Get carbon credit analytics
   * @param period Time period
   * @returns Carbon credit analytics
   */
  static async getCarbonCreditAnalytics(period: TimePeriod = 'month') {
    try {
      const { startDate, endDate } = this.getDateRange(period);

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count();

      // Get new carbon credits in the period
      const newCarbonCredits = await db.carbonCredit.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      });

      // Get carbon credits by standard
      const carbonCreditsByStandard = await db.carbonCredit.groupBy({
        by: ['standard'],
        _count: {
          id: true,
        },
      });

      // Get carbon credits by methodology
      const carbonCreditsByMethodology = await db.carbonCredit.groupBy({
        by: ['methodology'],
        _count: {
          id: true,
        },
      });

      // Get carbon credits by vintage
      const carbonCreditsByVintage = await db.carbonCredit.groupBy({
        by: ['vintage'],
        _count: {
          id: true,
        },
        orderBy: {
          vintage: 'desc',
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits available quantity
      const carbonCreditAvailableQuantity = await db.carbonCredit.aggregate({
        _sum: {
          availableQuantity: true,
        },
      });

      // Get total carbon credits traded quantity
      const carbonCreditTradedQuantity = await db.order.aggregate({
        where: {
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get average carbon credit price
      const averageCarbonCreditPrice = await db.carbonCredit.aggregate({
        where: {
          status: CarbonCreditStatus.LISTED,
        },
        _avg: {
          price: true,
        },
      });

      // Get price range
      const priceRange = await db.$queryRaw<{ min: number; max: number }[]>`
        SELECT 
          MIN(price) as min,
          MAX(price) as max
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
      `;

      // Get price by standard
      const priceByStandard = await db.$queryRaw<{ standard: string; avg: number }[]>`
        SELECT 
          standard,
          AVG(price) as avg
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
        GROUP BY standard
        ORDER BY avg DESC
      `;

      // Get price by methodology
      const priceByMethodology = await db.$queryRaw<{ methodology: string; avg: number }[]>`
        SELECT 
          methodology,
          AVG(price) as avg
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
        GROUP BY methodology
        ORDER BY avg DESC
      `;

      // Get price by vintage
      const priceByVintage = await db.$queryRaw<{ vintage: number; avg: number }[]>`
        SELECT 
          vintage,
          AVG(price) as avg
        FROM "CarbonCredit"
        WHERE status = 'LISTED'
        GROUP BY vintage
        ORDER BY vintage DESC
      `;

      // Get daily new listings
      const dailyNewListings = await db.$queryRaw<{ date: string; count: number }[]>`
        SELECT 
          DATE_TRUNC('day', "listingDate") as date,
          COUNT(*) as count
        FROM "CarbonCredit"
        WHERE 
          "listingDate" IS NOT NULL AND
          "listingDate" >= ${startDate} AND 
          "listingDate" <= ${endDate}
        GROUP BY DATE_TRUNC('day', "listingDate")
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        carbonCredits: {
          total: totalCarbonCredits,
          new: newCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          byStandard: carbonCreditsByStandard.reduce((acc, curr) => {
            acc[curr.standard] = curr._count.id;
            return acc;
          }, {} as Record<string, number>),
          byMethodology: carbonCreditsByMethodology.reduce((acc, curr) => {
            acc[curr.methodology] = curr._count.id;
            return acc;
          }, {} as Record<string, number>),
          byVintage: carbonCreditsByVintage.reduce((acc, curr) => {
            acc[curr.vintage] = curr._count.id;
            return acc;
          }, {} as Record<number, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          availableQuantity: carbonCreditAvailableQuantity._sum.availableQuantity || 0,
          tradedQuantity: carbonCreditTradedQuantity._sum.quantity || 0,
        },
        pricing: {
          average: averageCarbonCreditPrice._avg.price || 0,
          range: priceRange[0] || { min: 0, max: 0 },
          byStandard: priceByStandard,
          byMethodology: priceByMethodology,
          byVintage: priceByVintage,
        },
        listings: {
          daily: dailyNewListings,
        },
      };
    } catch (error) {
      logger.error('Error getting carbon credit analytics:', error);
      throw error;
    }
  }

  /**
   * Get user analytics
   * @param userId User ID
   * @param period Time period
   * @returns User analytics
   */
  static async getUserAnalytics(userId: string, period: TimePeriod = 'month') {
    try {
      const { startDate, endDate } = this.getDateRange(period);

      // Check if the user exists
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get total carbon credits
      const totalCarbonCredits = await db.carbonCredit.count({
        where: {
          userId,
        },
      });

      // Get new carbon credits in the period
      const newCarbonCredits = await db.carbonCredit.count({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get carbon credits by status
      const carbonCreditsByStatus = await db.carbonCredit.groupBy({
        by: ['status'],
        where: {
          userId,
        },
        _count: {
          id: true,
        },
      });

      // Get total carbon credits quantity
      const carbonCreditQuantity = await db.carbonCredit.aggregate({
        where: {
          userId,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total carbon credits available quantity
      const carbonCreditAvailableQuantity = await db.carbonCredit.aggregate({
        where: {
          userId,
        },
        _sum: {
          availableQuantity: true,
        },
      });

      // Get total buy orders
      const totalBuyOrders = await db.order.count({
        where: {
          buyerId: userId,
        },
      });

      // Get new buy orders in the period
      const newBuyOrders = await db.order.count({
        where: {
          buyerId: userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get total sell orders
      const totalSellOrders = await db.order.count({
        where: {
          sellerId: userId,
        },
      });

      // Get new sell orders in the period
      const newSellOrders = await db.order.count({
        where: {
          sellerId: userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get buy orders by status
      const buyOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          buyerId: userId,
        },
        _count: {
          id: true,
        },
      });

      // Get sell orders by status
      const sellOrdersByStatus = await db.order.groupBy({
        by: ['status'],
        where: {
          sellerId: userId,
        },
        _count: {
          id: true,
        },
      });

      // Get total buy volume
      const buyVolume = await db.order.aggregate({
        where: {
          buyerId: userId,
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get total sell volume
      const sellVolume = await db.order.aggregate({
        where: {
          sellerId: userId,
          status: OrderStatus.COMPLETED,
        },
        _sum: {
          quantity: true,
        },
      });

      // Get daily activity
      const dailyActivity = await db.$queryRaw<{ date: string; carbonCredits: number; buyOrders: number; sellOrders: number }[]>`
        SELECT 
          DATE_TRUNC('day', date) as date,
          SUM(CASE WHEN type = 'carbonCredit' THEN count ELSE 0 END) as "carbonCredits",
          SUM(CASE WHEN type = 'buyOrder' THEN count ELSE 0 END) as "buyOrders",
          SUM(CASE WHEN type = 'sellOrder' THEN count ELSE 0 END) as "sellOrders"
        FROM (
          SELECT 
            "createdAt" as date,
            'carbonCredit' as type,
            COUNT(*) as count
          FROM "CarbonCredit"
          WHERE 
            "userId" = ${userId} AND
            "createdAt" >= ${startDate} AND 
            "createdAt" <= ${endDate}
          GROUP BY "createdAt"
          
          UNION ALL
          
          SELECT 
            "createdAt" as date,
            'buyOrder' as type,
            COUNT(*) as count
          FROM "Order"
          WHERE 
            "buyerId" = ${userId} AND
            "createdAt" >= ${startDate} AND 
            "createdAt" <= ${endDate}
          GROUP BY "createdAt"
          
          UNION ALL
          
          SELECT 
            "createdAt" as date,
            'sellOrder' as type,
            COUNT(*) as count
          FROM "Order"
          WHERE 
            "sellerId" = ${userId} AND
            "createdAt" >= ${startDate} AND 
            "createdAt" <= ${endDate}
          GROUP BY "createdAt"
        ) as activity
        GROUP BY DATE_TRUNC('day', date)
        ORDER BY date ASC
      `;

      return {
        period,
        startDate,
        endDate,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organization: user.organization ? {
            id: user.organization.id,
            name: user.organization.name,
          } : null,
        },
        carbonCredits: {
          total: totalCarbonCredits,
          new: newCarbonCredits,
          byStatus: carbonCreditsByStatus.reduce((acc, curr) => {
            acc[curr.status] = curr._count.id;
            return acc;
          }, {} as Record<CarbonCreditStatus, number>),
          quantity: carbonCreditQuantity._sum.quantity || 0,
          availableQuantity: carbonCreditAvailableQuantity._sum.availableQuantity || 0,
        },
        orders: {
          buy: {
            total: totalBuyOrders,
            new: newBuyOrders,
            byStatus: buyOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: buyVolume._sum.quantity || 0,
          },
          sell: {
            total: totalSellOrders,
            new: newSellOrders,
            byStatus: sellOrdersByStatus.reduce((acc, curr) => {
              acc[curr.status] = curr._count.id;
              return acc;
            }, {} as Record<OrderStatus, number>),
            volume: sellVolume._sum.quantity || 0,
          },
        },
        activity: {
          daily: dailyActivity,
        },
      };
    } catch (error) {
      logger.error('Error getting user analytics:', error);
      throw error;
    }
  }
}
