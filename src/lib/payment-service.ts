import { 
  PaymentMethodType, 
  PaymentMethodStatus, 
  BillingStatus,
  BillingType,
  Prisma
} from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { NotificationService } from '@/lib/notification-service';

/**
 * Payment method creation data interface
 */
export interface PaymentMethodCreationData {
  type: PaymentMethodType;
  name: string;
  isDefault?: boolean;
  lastFour?: string;
  expiryMonth?: number;
  expiryYear?: number;
  billingAddress?: string;
  billingCity?: string;
  billingState?: string;
  billingZip?: string;
  billingCountry?: string;
  metadata?: any;
  organizationId: string;
  userId: string; // User creating the payment method
}

/**
 * Payment service for managing payment methods and billing
 */
export class PaymentService {
  /**
   * Create a new payment method
   * @param data Payment method creation data
   * @returns Created payment method
   */
  static async createPaymentMethod(data: PaymentMethodCreationData) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: data.organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the user has permission to create a payment method
      const user = await db.user.findUnique({
        where: { id: data.userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to create a payment method');
      }

      // If this is the default payment method, unset any existing default
      if (data.isDefault) {
        await db.paymentMethod.updateMany({
          where: {
            organizationId: data.organizationId,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Create the payment method
      const paymentMethod = await db.paymentMethod.create({
        data: {
          type: data.type,
          name: data.name,
          isDefault: data.isDefault || false,
          lastFour: data.lastFour,
          expiryMonth: data.expiryMonth,
          expiryYear: data.expiryYear,
          billingAddress: data.billingAddress,
          billingCity: data.billingCity,
          billingState: data.billingState,
          billingZip: data.billingZip,
          billingCountry: data.billingCountry,
          metadata: data.metadata as Prisma.JsonObject,
          organization: { connect: { id: data.organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'PAYMENT_PROCESSED',
          description: `Payment method ${paymentMethod.name} created`,
          user: { connect: { id: data.userId } },
          organization: { connect: { id: data.organizationId } },
          metadata: {
            paymentMethodId: paymentMethod.id,
            paymentMethodName: paymentMethod.name,
            paymentMethodType: paymentMethod.type,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Payment Method Added',
        message: `Payment method ${paymentMethod.name} has been added to your organization.`,
        type: 'PAYMENT',
        userId: data.userId,
        organizationId: data.organizationId,
        actionUrl: `/dashboard/payments/methods`,
        actionLabel: 'View Payment Methods',
      });

      logger.info(`Payment method ${paymentMethod.id} created for organization ${data.organizationId} by user ${data.userId}`);

      return paymentMethod;
    } catch (error) {
      logger.error('Error creating payment method:', error);
      throw error;
    }
  }

  /**
   * Update a payment method
   * @param paymentMethodId Payment method ID
   * @param userId User ID updating the payment method
   * @param data Payment method data to update
   * @returns Updated payment method
   */
  static async updatePaymentMethod(
    paymentMethodId: string,
    userId: string,
    data: Partial<PaymentMethodCreationData>
  ) {
    try {
      // Get the payment method
      const paymentMethod = await db.paymentMethod.findUnique({
        where: { id: paymentMethodId },
        include: {
          organization: true,
        },
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Check if the user has permission to update the payment method
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to update this payment method');
      }

      // If this is being set as the default payment method, unset any existing default
      if (data.isDefault) {
        await db.paymentMethod.updateMany({
          where: {
            organizationId: paymentMethod.organizationId,
            isDefault: true,
            NOT: {
              id: paymentMethodId,
            },
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Update the payment method
      const updatedPaymentMethod = await db.paymentMethod.update({
        where: { id: paymentMethodId },
        data: {
          name: data.name,
          isDefault: data.isDefault,
          lastFour: data.lastFour,
          expiryMonth: data.expiryMonth,
          expiryYear: data.expiryYear,
          billingAddress: data.billingAddress,
          billingCity: data.billingCity,
          billingState: data.billingState,
          billingZip: data.billingZip,
          billingCountry: data.billingCountry,
          metadata: data.metadata as Prisma.JsonObject,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'PAYMENT_PROCESSED',
          description: `Payment method ${paymentMethod.name} updated`,
          user: { connect: { id: userId } },
          organization: { connect: { id: paymentMethod.organizationId } },
          metadata: {
            paymentMethodId,
            paymentMethodName: paymentMethod.name,
            changes: data,
          } as Prisma.JsonObject,
        },
      });

      logger.info(`Payment method ${paymentMethodId} updated by user ${userId}`);

      return updatedPaymentMethod;
    } catch (error) {
      logger.error('Error updating payment method:', error);
      throw error;
    }
  }

  /**
   * Delete a payment method
   * @param paymentMethodId Payment method ID
   * @param userId User ID deleting the payment method
   * @returns Deleted payment method
   */
  static async deletePaymentMethod(paymentMethodId: string, userId: string) {
    try {
      // Get the payment method
      const paymentMethod = await db.paymentMethod.findUnique({
        where: { id: paymentMethodId },
        include: {
          organization: true,
        },
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Check if the user has permission to delete the payment method
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to delete this payment method');
      }

      // Check if this is the default payment method
      if (paymentMethod.isDefault) {
        // Find another payment method to set as default
        const anotherPaymentMethod = await db.paymentMethod.findFirst({
          where: {
            organizationId: paymentMethod.organizationId,
            NOT: {
              id: paymentMethodId,
            },
          },
        });

        if (anotherPaymentMethod) {
          await db.paymentMethod.update({
            where: { id: anotherPaymentMethod.id },
            data: {
              isDefault: true,
            },
          });
        }
      }

      // Delete the payment method
      const deletedPaymentMethod = await db.paymentMethod.delete({
        where: { id: paymentMethodId },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'PAYMENT_PROCESSED',
          description: `Payment method ${paymentMethod.name} deleted`,
          user: { connect: { id: userId } },
          organization: { connect: { id: paymentMethod.organizationId } },
          metadata: {
            paymentMethodId,
            paymentMethodName: paymentMethod.name,
            paymentMethodType: paymentMethod.type,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Payment Method Removed',
        message: `Payment method ${paymentMethod.name} has been removed from your organization.`,
        type: 'PAYMENT',
        userId,
        organizationId: paymentMethod.organizationId,
        actionUrl: `/dashboard/payments/methods`,
        actionLabel: 'View Payment Methods',
      });

      logger.info(`Payment method ${paymentMethodId} deleted by user ${userId}`);

      return deletedPaymentMethod;
    } catch (error) {
      logger.error('Error deleting payment method:', error);
      throw error;
    }
  }

  /**
   * Process a payment for a billing record
   * @param billingRecordId Billing record ID
   * @param paymentMethodId Payment method ID
   * @param userId User ID processing the payment
   * @returns Updated billing record
   */
  static async processPayment(billingRecordId: string, paymentMethodId: string, userId: string) {
    try {
      // Get the billing record
      const billingRecord = await db.billingRecord.findUnique({
        where: { id: billingRecordId },
        include: {
          organization: true,
        },
      });

      if (!billingRecord) {
        throw new Error('Billing record not found');
      }

      // Check if the billing record is already paid
      if (billingRecord.status === BillingStatus.PAID) {
        throw new Error('Billing record is already paid');
      }

      // Get the payment method
      const paymentMethod = await db.paymentMethod.findUnique({
        where: { id: paymentMethodId },
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Check if the payment method belongs to the organization
      if (paymentMethod.organizationId !== billingRecord.organizationId) {
        throw new Error('Payment method does not belong to this organization');
      }

      // Check if the user has permission to process the payment
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || (user.role !== 'ADMIN' && user.role !== 'ORGANIZATION_ADMIN')) {
        throw new Error('You do not have permission to process this payment');
      }

      // In a real implementation, this would call a payment processor API
      // For now, we'll just update the billing record

      // Update the billing record
      const updatedBillingRecord = await db.billingRecord.update({
        where: { id: billingRecordId },
        data: {
          status: BillingStatus.PAID,
          paidDate: new Date(),
          paymentMethod: { connect: { id: paymentMethodId } },
          // In a real implementation, we would generate an invoice and receipt
          invoiceNumber: `INV-${Date.now()}`,
          invoiceUrl: `https://example.com/invoices/INV-${Date.now()}.pdf`,
          receiptUrl: `https://example.com/receipts/REC-${Date.now()}.pdf`,
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'PAYMENT_PROCESSED',
          description: `Payment of $${billingRecord.amount} processed for ${billingRecord.description}`,
          user: { connect: { id: userId } },
          organization: { connect: { id: billingRecord.organizationId } },
          metadata: {
            billingRecordId,
            amount: billingRecord.amount,
            description: billingRecord.description,
            paymentMethodId,
            paymentMethodName: paymentMethod.name,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to the user
      await NotificationService.createNotification({
        title: 'Payment Processed',
        message: `Payment of $${billingRecord.amount} for ${billingRecord.description} has been processed.`,
        type: 'PAYMENT',
        userId,
        organizationId: billingRecord.organizationId,
        actionUrl: `/dashboard/payments/history`,
        actionLabel: 'View Payment History',
      });

      logger.info(`Payment of $${billingRecord.amount} processed for billing record ${billingRecordId} by user ${userId}`);

      return updatedBillingRecord;
    } catch (error) {
      logger.error('Error processing payment:', error);
      throw error;
    }
  }

  /**
   * Create a billing record
   * @param organizationId Organization ID
   * @param amount Amount to bill
   * @param description Description of the billing
   * @param type Billing type
   * @param userId User ID creating the billing record
   * @returns Created billing record
   */
  static async createBillingRecord(
    organizationId: string,
    amount: number,
    description: string,
    type: BillingType,
    userId: string
  ) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if the user has permission to create a billing record
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user || user.role !== 'ADMIN') {
        throw new Error('You do not have permission to create a billing record');
      }

      // Create the billing record
      const billingRecord = await db.billingRecord.create({
        data: {
          amount,
          description,
          type,
          status: BillingStatus.PENDING,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
          organization: { connect: { id: organizationId } },
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: 'BILLING_CREATED',
          description: `Billing record created for ${description}`,
          user: { connect: { id: userId } },
          organization: { connect: { id: organizationId } },
          metadata: {
            billingRecordId: billingRecord.id,
            amount,
            description,
            type,
          } as Prisma.JsonObject,
        },
      });

      // Send notification to organization admin
      const orgAdmin = await db.user.findFirst({
        where: {
          organizationId,
          role: 'ORGANIZATION_ADMIN',
        },
      });

      if (orgAdmin) {
        await NotificationService.createNotification({
          title: 'New Billing Record',
          message: `A new billing record of $${amount} for ${description} has been created for your organization.`,
          type: 'PAYMENT',
          priority: 'HIGH',
          userId: orgAdmin.id,
          organizationId,
          actionUrl: `/dashboard/payments/history`,
          actionLabel: 'View Billing History',
        });
      }

      logger.info(`Billing record ${billingRecord.id} created for organization ${organizationId} by user ${userId}`);

      return billingRecord;
    } catch (error) {
      logger.error('Error creating billing record:', error);
      throw error;
    }
  }

  /**
   * Get payment methods for an organization
   * @param organizationId Organization ID
   * @returns Payment methods
   */
  static async getPaymentMethods(organizationId: string) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Get payment methods
      const paymentMethods = await db.paymentMethod.findMany({
        where: { organizationId },
        orderBy: { isDefault: 'desc' },
      });

      return paymentMethods;
    } catch (error) {
      logger.error('Error getting payment methods:', error);
      throw error;
    }
  }

  /**
   * Get billing history for an organization
   * @param organizationId Organization ID
   * @param options Query options
   * @returns Billing records
   */
  static async getBillingHistory(
    organizationId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: BillingStatus;
      type?: BillingType;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ) {
    try {
      // Check if the organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      const { limit = 10, offset = 0, status, type, startDate, endDate } = options;

      // Build where clause
      const where: Prisma.BillingRecordWhereInput = {
        organizationId,
        ...(status && { status }),
        ...(type && { type }),
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
      };

      // Get billing records
      const billingRecords = await db.billingRecord.findMany({
        where,
        include: {
          paymentMethod: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      });

      // Get total count
      const totalCount = await db.billingRecord.count({ where });

      return {
        billingRecords,
        totalCount,
        hasMore: offset + limit < totalCount,
      };
    } catch (error) {
      logger.error('Error getting billing history:', error);
      throw error;
    }
  }
}
