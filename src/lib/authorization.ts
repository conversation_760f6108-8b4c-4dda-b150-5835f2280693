import { auth, getCurrentUser } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { AuditLogType } from "@prisma/client";
import { redirect } from "next/navigation";
import { cookies } from "next/headers";

/**
 * Role-based access control (RBAC) utility
 * 
 * This module provides utilities for checking user permissions and
 * enforcing access control throughout the application.
 */

/**
 * Available user roles in the system
 */
export enum UserRole {
  ADMIN = "ADMIN",
  ORGANIZATION_ADMIN = "ORGANIZATION_ADMIN",
  USER = "USER",
}

/**
 * Permission types for different actions
 */
export enum Permission {
  // Organization permissions
  CREATE_ORGANIZATION = "create:organization",
  UPDATE_ORGANIZATION = "update:organization",
  DELETE_ORGANIZATION = "delete:organization",
  
  // User management permissions
  INVITE_USER = "invite:user",
  REMOVE_USER = "remove:user",
  UPDATE_USER_ROLE = "update:user:role",
  
  // Carbon credit permissions
  CREATE_CARBON_CREDIT = "create:carbon_credit",
  UPDATE_CARBON_CREDIT = "update:carbon_credit",
  DELETE_CARBON_CREDIT = "delete:carbon_credit",
  TOKENIZE_CARBON_CREDIT = "tokenize:carbon_credit",
  RETIRE_CARBON_CREDIT = "retire:carbon_credit",
  
  // Wallet permissions
  CREATE_WALLET = "create:wallet",
  TRANSFER_TOKENS = "transfer:tokens",
  
  // Admin permissions
  VIEW_ADMIN_DASHBOARD = "view:admin_dashboard",
  MANAGE_PLATFORM = "manage:platform",
}

/**
 * Permission mapping for different roles
 */
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: Object.values(Permission), // Admins have all permissions
  
  [UserRole.ORGANIZATION_ADMIN]: [
    Permission.UPDATE_ORGANIZATION,
    Permission.INVITE_USER,
    Permission.REMOVE_USER,
    Permission.UPDATE_USER_ROLE,
    Permission.CREATE_CARBON_CREDIT,
    Permission.UPDATE_CARBON_CREDIT,
    Permission.DELETE_CARBON_CREDIT,
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.CREATE_WALLET,
    Permission.TRANSFER_TOKENS,
  ],
  
  [UserRole.USER]: [
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],
};

/**
 * Check if a user has a specific permission
 * @param userRole The user's role
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return rolePermissions[userRole]?.includes(permission) || false;
}

/**
 * Check if the current user has a specific permission
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export async function checkPermission(permission: Permission): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.role) {
    return false;
  }
  
  return hasPermission(session.user.role as UserRole, permission);
}

/**
 * Require a specific permission to access a route or component
 * If the user doesn't have the permission, they will be redirected to the dashboard
 * @param permission The permission to require
 * @param redirectTo The path to redirect to if the user doesn't have the permission
 */
export async function requirePermission(permission: Permission, redirectTo: string = "/dashboard") {
  const hasAccess = await checkPermission(permission);
  
  if (!hasAccess) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Permission denied: ${permission} for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: `Permission denied: ${permission}`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if the current user is an admin of the specified organization
 * @param organizationId The organization ID to check
 * @returns True if the user is an admin of the organization, false otherwise
 */
export async function isOrgAdmin(organizationId?: string): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.role || !session?.user?.organizationId) {
    return false;
  }
  
  // If organizationId is provided, check if the user belongs to that organization
  if (organizationId && session.user.organizationId !== organizationId) {
    return false;
  }
  
  return session.user.role === UserRole.ORGANIZATION_ADMIN || session.user.role === UserRole.ADMIN;
}

/**
 * Require the user to be an admin of the specified organization
 * If the user is not an admin, they will be redirected to the dashboard
 * @param organizationId The organization ID to check
 * @param redirectTo The path to redirect to if the user is not an admin
 */
export async function requireOrgAdmin(organizationId?: string, redirectTo: string = "/dashboard") {
  const isAdmin = await isOrgAdmin(organizationId);
  
  if (!isAdmin) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Organization admin access denied for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: `Organization admin access denied${organizationId ? ` for organization ${organizationId}` : ''}`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if the current user is a platform admin
 * @returns True if the user is a platform admin, false otherwise
 */
export async function isPlatformAdmin(): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.role) {
    return false;
  }
  
  return session.user.role === UserRole.ADMIN;
}

/**
 * Require the user to be a platform admin
 * If the user is not a platform admin, they will be redirected to the dashboard
 * @param redirectTo The path to redirect to if the user is not a platform admin
 */
export async function requirePlatformAdmin(redirectTo: string = "/dashboard") {
  const isAdmin = await isPlatformAdmin();
  
  if (!isAdmin) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Platform admin access denied for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: "Platform admin access denied",
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if the current user can access a specific resource
 * @param resourceOwnerId The ID of the resource owner (user or organization)
 * @param resourceType The type of resource (e.g., 'carbon_credit', 'wallet')
 * @returns True if the user can access the resource, false otherwise
 */
export async function canAccessResource(resourceOwnerId: string, resourceType: string): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.id) {
    return false;
  }
  
  // Platform admins can access all resources
  if (session.user.role === UserRole.ADMIN) {
    return true;
  }
  
  // If the resource belongs to the user, they can access it
  if (resourceOwnerId === session.user.id) {
    return true;
  }
  
  // If the resource belongs to the user's organization, check their role
  if (session.user.organizationId === resourceOwnerId) {
    // Organization admins can access all organization resources
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      return true;
    }
    
    // Regular users can access some organization resources based on resource type
    if (session.user.role === UserRole.USER) {
      // Define which resource types regular users can access
      const accessibleResourceTypes = ['carbon_credit', 'wallet'];
      return accessibleResourceTypes.includes(resourceType);
    }
  }
  
  return false;
}

/**
 * Require the user to be able to access a specific resource
 * If the user cannot access the resource, they will be redirected to the dashboard
 * @param resourceOwnerId The ID of the resource owner (user or organization)
 * @param resourceType The type of resource (e.g., 'carbon_credit', 'wallet')
 * @param redirectTo The path to redirect to if the user cannot access the resource
 */
export async function requireResourceAccess(
  resourceOwnerId: string, 
  resourceType: string, 
  redirectTo: string = "/dashboard"
) {
  const hasAccess = await canAccessResource(resourceOwnerId, resourceType);
  
  if (!hasAccess) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Resource access denied: ${resourceType} (${resourceOwnerId}) for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: `Resource access denied: ${resourceType} (${resourceOwnerId})`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}
