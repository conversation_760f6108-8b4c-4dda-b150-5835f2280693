'use client';

import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle2, AlertCircle, ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useProfileCompletion } from '@/hooks/use-profile-completion';

export interface ProfileField {
  name: string;
  label: string;
  completed: boolean;
  required: boolean;
}

interface ProfileCompletionIndicatorProps {
  organizationId: string;
  fields: ProfileField[];
  className?: string;
  showDetails?: boolean;
  compact?: boolean;
}

export function ProfileCompletionIndicator({
  organizationId,
  fields: propFields,
  className,
  showDetails = true,
  compact = false,
}: Readonly<ProfileCompletionIndicatorProps>) {
  const router = useRouter();
  const { fields, completionPercentage, loading, error } = useProfileCompletion({ organizationId });

  // Use provided fields if available, otherwise use fields from hook
  const displayFields = propFields || fields;

  // If there's an error and no fields are provided, show a fallback UI
  if (error && !propFields && displayFields.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Profile Completion</CardTitle>
          <CardDescription>
            Unable to load profile completion data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            {error}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => router.push(`/settings/organization?organizationId=${organizationId}`)}
          >
            Go to Organization Settings
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Navigate to organization settings
  const handleCompleteProfile = () => {
    router.push(`/settings/organization?organizationId=${organizationId}`);
  };

  if (loading) {
    return (
      <div className={cn("flex flex-col space-y-2", className)}>
        <div className="h-2 w-full bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={cn("flex items-center space-x-4", className)}>
        <Progress value={completionPercentage} className="flex-1 h-2" />
        <span className="text-sm font-medium">{completionPercentage}%</span>
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Profile Completion</CardTitle>
          <span className="text-2xl font-bold">{completionPercentage}%</span>
        </div>
        <CardDescription>
          {completionPercentage < 100
            ? "Complete your organization profile to unlock all features"
            : "Your organization profile is complete!"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Progress value={completionPercentage} className="h-2 mb-4" />

        {showDetails && (
          <div className="space-y-2 mt-4">
            {displayFields
              .filter(field => !field.completed)
              .slice(0, 3) // Show only first 3 incomplete fields
              .map((field) => (
                <div key={field.name} className="flex items-center text-sm">
                  <AlertCircle className="h-4 w-4 text-amber-500 mr-2 flex-shrink-0" />
                  <span>{field.label} {field.required && <span className="text-muted-foreground">(Required)</span>}</span>
                </div>
              ))}

            {displayFields.filter(field => !field.completed).length > 3 && (
              <div className="text-sm text-muted-foreground">
                And {displayFields.filter(field => !field.completed).length - 3} more fields to complete
              </div>
            )}

            {displayFields.filter(field => !field.completed).length === 0 && (
              <div className="flex items-center text-sm">
                <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                <span>All profile information completed</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
      {completionPercentage < 100 && (
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            onClick={handleCompleteProfile}
          >
            Complete Profile
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
