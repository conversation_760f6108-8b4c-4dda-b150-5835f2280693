'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AlertCircle, X, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { cn } from '@/lib/utils';

export interface ContextNudgeProps {
  organizationId: string;
  title: string;
  description: string;
  fields: string[];
  nudgeId: string;
  className?: string;
  variant?: 'default' | 'warning' | 'info';
}

export function ContextNudge({
  organizationId,
  title,
  description,
  fields,
  nudgeId,
  className,
  variant = 'default',
}: ContextNudgeProps) {
  const router = useRouter();
  const [dismissed, setDismissed] = useLocalStorage<boolean>(`context-nudge-dismissed-${nudgeId}-${organizationId}`, false);

  if (dismissed) {
    return null;
  }

  // Navigate to organization settings with specific section highlighted
  const handleCompleteProfile = () => {
    router.push(`/settings/organization?organizationId=${organizationId}&highlight=${fields[0]}`);
  };

  // Dismiss the nudge
  const handleDismiss = () => {
    setDismissed(true);
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'warning':
        return 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50';
      case 'info':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/50';
      default:
        return 'border-primary/20 bg-primary/5';
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'warning':
        return 'text-amber-600 dark:text-amber-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-primary';
    }
  };

  const getButtonStyles = () => {
    switch (variant) {
      case 'warning':
        return 'bg-white dark:bg-amber-900/30 border-amber-200 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/50';
      case 'info':
        return 'bg-white dark:bg-blue-900/30 border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/50';
      default:
        return 'bg-white dark:bg-primary/10 border-primary/20 hover:bg-primary/10';
    }
  };

  return (
    <Alert 
      className={cn(
        'relative mb-6',
        getVariantStyles(),
        className
      )}
    >
      <AlertCircle className={cn('h-4 w-4', getIconColor())} />
      <div className="flex-1">
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="flex-1">
            {description}
          </div>
          <div className="flex-shrink-0">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleCompleteProfile}
              className={getButtonStyles()}
            >
              Complete Profile
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </AlertDescription>
      </div>
      <Button 
        variant="ghost" 
        size="icon" 
        className={cn(
          'absolute top-1 right-1 h-6 w-6 hover:bg-muted',
          getIconColor()
        )}
        onClick={handleDismiss}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Dismiss</span>
      </Button>
    </Alert>
  );
}
