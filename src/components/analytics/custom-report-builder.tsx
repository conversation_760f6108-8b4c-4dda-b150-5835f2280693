"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { HelpCard } from "@/components/ui/contextual-help";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  Di<PERSON><PERSON>it<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  BarChart3,
  LineChart,
  PieChart,
  Download,
  Calendar,
  RefreshCw,
  Loader2,
  ArrowLeft,
  Leaf,
  DollarSign,
  TrendingUp,
  Activity,
  Filter,
  Plus,
  Settings,
  Save,
  Share,
  Eye,
  FileText,
  Trash2,
  Undo2,
  Redo2,
  MoveHorizontal,
  MoveVertical,
  Grid2X2,
  Grid3X3,
  Maximize2,
  Minimize2,
  Copy,
  Palette,
  Table,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  ChevronLeft,
  Layers,
  LayoutGrid,
  LayoutList
} from "lucide-react";

// Available metrics for the report builder
const availableMetrics = [
  { id: "emissions_reduced", name: "Emissions Reduced", category: "carbon_impact", unit: "tCO2e" },
  { id: "credits_retired", name: "Credits Retired", category: "carbon_impact", unit: "credits" },
  { id: "credits_by_standard", name: "Credits by Standard", category: "carbon_impact", unit: "credits" },
  { id: "credits_by_vintage", name: "Credits by Vintage", category: "carbon_impact", unit: "credits" },
  { id: "transaction_volume", name: "Transaction Volume", category: "financial", unit: "$" },
  { id: "revenue", name: "Revenue", category: "financial", unit: "$" },
  { id: "expenses", name: "Expenses", category: "financial", unit: "$" },
  { id: "wallet_balances", name: "Wallet Balances", category: "financial", unit: "$" },
  { id: "carbon_asset_value", name: "Carbon Asset Value", category: "financial", unit: "$" },
  { id: "revenue_by_source", name: "Revenue by Source", category: "financial", unit: "$" },
  { id: "orders", name: "Orders", category: "trading", unit: "count" },
  { id: "price_history", name: "Price History", category: "trading", unit: "$" },
  { id: "market_trends", name: "Market Trends", category: "trading", unit: "various" }
];

// Available chart types
const chartTypes = [
  { id: "bar", name: "Bar Chart", icon: <BarChart3 className="h-4 w-4" /> },
  { id: "line", name: "Line Chart", icon: <LineChart className="h-4 w-4" /> },
  { id: "pie", name: "Pie Chart", icon: <PieChart className="h-4 w-4" /> },
  { id: "area", name: "Area Chart", icon: <Activity className="h-4 w-4" /> },
  { id: "table", name: "Table", icon: <Table className="h-4 w-4" /> }
];

// Available layouts
const layouts = [
  { id: "1x1", name: "1 Column", icon: <LayoutList className="h-4 w-4" /> },
  { id: "2x1", name: "2 Columns", icon: <Grid2X2 className="h-4 w-4" /> },
  { id: "2x2", name: "4 Grid", icon: <LayoutGrid className="h-4 w-4" /> },
  { id: "3x2", name: "6 Grid", icon: <Grid3X3 className="h-4 w-4" /> }
];

// Sample saved reports
const savedReports = [
  { id: "1", name: "Carbon Impact Q3 2023", description: "Quarterly carbon impact report", createdAt: "2023-10-01" },
  { id: "2", name: "Financial Performance 2023", description: "Annual financial performance", createdAt: "2023-12-15" },
  { id: "3", name: "Trading Activity Summary", description: "Monthly trading activity summary", createdAt: "2023-11-30" },
  { id: "4", name: "Executive Dashboard", description: "Executive overview of key metrics", createdAt: "2023-12-01" }
];

interface CustomReportBuilderProps {
  userId?: string;
  organizationId?: string;
}

export function CustomReportBuilder({
  userId,
  organizationId,
}: CustomReportBuilderProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("builder");
  const [reportName, setReportName] = useState("");
  const [reportDescription, setReportDescription] = useState("");
  const [selectedLayout, setSelectedLayout] = useState(layouts[0].id);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [filterCategory, setFilterCategory] = useState("all");
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)); // 90 days ago
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportFormat, setExportFormat] = useState("pdf");
  const [selectedReport, setSelectedReport] = useState<string | null>(null);

  // Handle adding a metric to the report
  const handleAddMetric = (metricId: string) => {
    if (!selectedMetrics.includes(metricId)) {
      setSelectedMetrics([...selectedMetrics, metricId]);
    }
  };

  // Handle removing a metric from the report
  const handleRemoveMetric = (metricId: string) => {
    setSelectedMetrics(selectedMetrics.filter(id => id !== metricId));
  };

  // Handle saving the report
  const handleSaveReport = () => {
    if (!reportName) {
      toast({
        title: "Error",
        description: "Please provide a name for your report.",
        variant: "destructive"
      });
      return;
    }

    if (selectedMetrics.length === 0) {
      toast({
        title: "Error",
        description: "Please add at least one metric to your report.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setShowSaveDialog(false);
      toast({
        title: "Report Saved",
        description: `Report "${reportName}" has been saved successfully.`,
      });
    }, 1000);
  };

  // Handle generating the report
  const handleGenerateReport = () => {
    if (selectedMetrics.length === 0) {
      toast({
        title: "Error",
        description: "Please add at least one metric to your report.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setShowPreview(true);
      toast({
        title: "Report Generated",
        description: "Your custom report has been generated successfully.",
      });
    }, 1500);
  };

  // Handle exporting the report
  const handleExportReport = () => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setShowExportDialog(false);
      toast({
        title: "Report Exported",
        description: `Report has been exported as ${exportFormat.toUpperCase()} successfully.`,
      });
    }, 1500);
  };

  // Handle loading a saved report
  const handleLoadReport = (reportId: string) => {
    setIsLoading(true);
    setSelectedReport(reportId);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      const report = savedReports.find(r => r.id === reportId);
      if (report) {
        setReportName(report.name);
        setReportDescription(report.description);
        // In a real implementation, we would load the actual metrics and configuration
        setSelectedMetrics(["emissions_reduced", "credits_retired"]);
        toast({
          title: "Report Loaded",
          description: `Report "${report.name}" has been loaded successfully.`,
        });
      }
    }, 1000);
  };

  // Render available metrics panel
  const renderAvailableMetrics = () => {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Available Metrics</CardTitle>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-[150px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="carbon_impact">Carbon Impact</SelectItem>
                <SelectItem value="financial">Financial</SelectItem>
                <SelectItem value="trading">Trading</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <CardDescription>Drag metrics to add to your report</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px] pr-4">
            {availableMetrics
              .filter(metric => filterCategory === "all" || metric.category === filterCategory)
              .map(metric => (
                <div
                  key={metric.id}
                  className="flex items-center justify-between p-3 mb-2 border rounded-md cursor-move hover:bg-muted/50"
                  onClick={() => handleAddMetric(metric.id)}
                >
                  <div className="flex items-center">
                    {metric.category === "carbon_impact" ? (
                      <Leaf className="h-4 w-4 mr-2 text-green-500" />
                    ) : metric.category === "financial" ? (
                      <DollarSign className="h-4 w-4 mr-2 text-blue-500" />
                    ) : (
                      <TrendingUp className="h-4 w-4 mr-2 text-purple-500" />
                    )}
                    <span>{metric.name}</span>
                  </div>
                  <Badge variant="outline">{metric.unit}</Badge>
                </div>
              ))}
          </ScrollArea>
        </CardContent>
      </Card>
    );
  };

  // Render selected metrics panel
  const renderSelectedMetrics = () => {
    if (selectedMetrics.length === 0) {
      return (
        <div className="border-2 border-dashed rounded-md h-[400px] flex items-center justify-center p-4">
          <div className="text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Metrics Selected</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Click on metrics from the left panel to add them to your report
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {selectedMetrics.map(metricId => {
          const metric = availableMetrics.find(m => m.id === metricId);
          if (!metric) return null;

          return (
            <Card key={metric.id} className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 h-6 w-6"
                onClick={() => handleRemoveMetric(metric.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <CardHeader className="pb-2">
                <div className="flex items-center">
                  {metric.category === "carbon_impact" ? (
                    <Leaf className="h-4 w-4 mr-2 text-green-500" />
                  ) : metric.category === "financial" ? (
                    <DollarSign className="h-4 w-4 mr-2 text-blue-500" />
                  ) : (
                    <TrendingUp className="h-4 w-4 mr-2 text-purple-500" />
                  )}
                  <CardTitle className="text-base">{metric.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="flex items-center justify-between mb-2">
                  <Label>Chart Type</Label>
                  <Select defaultValue="bar">
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Chart Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {chartTypes.map(chartType => (
                        <SelectItem key={chartType.id} value={chartType.id}>
                          <div className="flex items-center">
                            {chartType.icon}
                            <span className="ml-2">{chartType.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Display Options</Label>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <Palette className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <Maximize2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-2 flex justify-between">
                <Badge variant="outline">{metric.unit}</Badge>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          );
        })}
      </div>
    );
  };

  // Render report configuration panel
  const renderReportConfiguration = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Report Configuration</CardTitle>
          <CardDescription>Configure your report settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="report-name">Report Name</Label>
            <Input
              id="report-name"
              placeholder="Enter report name"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="report-description">Description (Optional)</Label>
            <Input
              id="report-description"
              placeholder="Enter report description"
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
            />
          </div>
          <Separator />
          <div className="space-y-2">
            <Label>Date Range</Label>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="space-y-2 flex-1">
                <Label className="text-xs">Start Date</Label>
                <DatePicker date={startDate} setDate={setStartDate} />
              </div>
              <div className="space-y-2 flex-1">
                <Label className="text-xs">End Date</Label>
                <DatePicker date={endDate} setDate={setEndDate} />
              </div>
            </div>
          </div>
          <Separator />
          <div className="space-y-2">
            <Label>Layout</Label>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
              {layouts.map(layout => (
                <Button
                  key={layout.id}
                  type="button"
                  variant={selectedLayout === layout.id ? "default" : "outline"}
                  className="flex flex-col items-center justify-center h-20"
                  onClick={() => setSelectedLayout(layout.id)}
                >
                  {layout.icon}
                  <span className="mt-2 text-xs">{layout.name}</span>
                </Button>
              ))}
            </div>
          </div>
          <Separator />
          <div className="space-y-2">
            <Label>Additional Options</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="include-summary" />
                <Label htmlFor="include-summary">Include executive summary</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="include-insights" />
                <Label htmlFor="include-insights">Include AI-powered insights</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="include-metadata" />
                <Label htmlFor="include-metadata">Include metadata</Label>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => setShowSaveDialog(true)}>
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
          <Button onClick={handleGenerateReport} disabled={isLoading || selectedMetrics.length === 0}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Generate Report
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Render saved reports panel
  const renderSavedReports = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Saved Reports</CardTitle>
          <CardDescription>Your previously saved reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {savedReports.map(report => (
              <div
                key={report.id}
                className={`p-3 border rounded-md cursor-pointer hover:bg-muted/50 ${
                  selectedReport === report.id ? 'bg-muted/50 border-primary' : ''
                }`}
                onClick={() => handleLoadReport(report.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="font-medium">{report.name}</div>
                  <Badge variant="outline">{new Date(report.createdAt).toLocaleDateString()}</Badge>
                </div>
                <p className="text-sm text-muted-foreground mt-1">{report.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full" onClick={() => setActiveTab("builder")}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Report
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Render report preview
  const renderReportPreview = () => {
    if (!showPreview) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => setShowPreview(false)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Editor
          </Button>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setShowExportDialog(true)}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{reportName || "Untitled Report"}</CardTitle>
            {reportDescription && (
              <CardDescription>{reportDescription}</CardDescription>
            )}
            <div className="flex items-center text-sm text-muted-foreground mt-2">
              <Calendar className="h-4 w-4 mr-1" />
              <span>
                {startDate?.toLocaleDateString()} - {endDate?.toLocaleDateString()}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className={`grid gap-6 ${
              selectedLayout === "1x1" ? "grid-cols-1" :
              selectedLayout === "2x1" ? "grid-cols-1 md:grid-cols-2" :
              selectedLayout === "2x2" ? "grid-cols-1 md:grid-cols-2" :
              "grid-cols-1 md:grid-cols-3"
            }`}>
              {selectedMetrics.map(metricId => {
                const metric = availableMetrics.find(m => m.id === metricId);
                if (!metric) return null;

                return (
                  <Card key={metric.id}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{metric.name}</CardTitle>
                    </CardHeader>
                    <CardContent className="h-[250px] flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        {metric.category === "carbon_impact" ? (
                          <LineChart className="h-16 w-16 mx-auto mb-4" />
                        ) : metric.category === "financial" ? (
                          <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                        ) : (
                          <PieChart className="h-16 w-16 mx-auto mb-4" />
                        )}
                        <p>Chart visualization would appear here</p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Custom Report Builder</h2>
          <p className="text-muted-foreground">
            Create custom reports with the metrics that matter to you
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="builder" className="flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              Builder
            </TabsTrigger>
            <TabsTrigger value="saved" className="flex items-center">
              <Layers className="h-4 w-4 mr-2" />
              Saved Reports
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Separator />

      {showPreview ? (
        renderReportPreview()
      ) : (
        <TabsContent value={activeTab} className="mt-0">
          {activeTab === "builder" ? (
            <div className="grid gap-6 md:grid-cols-3">
              <div className="md:col-span-1">
                {renderAvailableMetrics()}
              </div>
              <div className="md:col-span-2">
                <div className="grid gap-6 grid-cols-1">
                  <Card>
                    <CardHeader>
                      <CardTitle>Report Canvas</CardTitle>
                      <CardDescription>Selected metrics for your report</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {renderSelectedMetrics()}
                    </CardContent>
                  </Card>
                  {renderReportConfiguration()}
                </div>
              </div>
            </div>
          ) : (
            renderSavedReports()
          )}
        </TabsContent>
      )}

      {/* Save Report Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Report</DialogTitle>
            <DialogDescription>
              Save your custom report for future use.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="save-report-name">Report Name</Label>
              <Input
                id="save-report-name"
                placeholder="Enter report name"
                value={reportName}
                onChange={(e) => setReportName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="save-report-description">Description (Optional)</Label>
              <Input
                id="save-report-description"
                placeholder="Enter report description"
                value={reportDescription}
                onChange={(e) => setReportDescription(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="schedule-report" />
              <Label htmlFor="schedule-report">Schedule recurring report</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>Cancel</Button>
            <Button onClick={handleSaveReport} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Report
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Report Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Report</DialogTitle>
            <DialogDescription>
              Export your report in various formats.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <Button
                variant={exportFormat === "pdf" ? "default" : "outline"}
                className="flex flex-col items-center justify-center h-24"
                onClick={() => setExportFormat("pdf")}
              >
                <FileText className="h-8 w-8 mb-2" />
                PDF
              </Button>
              <Button
                variant={exportFormat === "xlsx" ? "default" : "outline"}
                className="flex flex-col items-center justify-center h-24"
                onClick={() => setExportFormat("xlsx")}
              >
                <Table className="h-8 w-8 mb-2" />
                Excel
              </Button>
              <Button
                variant={exportFormat === "csv" ? "default" : "outline"}
                className="flex flex-col items-center justify-center h-24"
                onClick={() => setExportFormat("csv")}
              >
                <FileText className="h-8 w-8 mb-2" />
                CSV
              </Button>
              <Button
                variant={exportFormat === "png" ? "default" : "outline"}
                className="flex flex-col items-center justify-center h-24"
                onClick={() => setExportFormat("png")}
              >
                <FileText className="h-8 w-8 mb-2" />
                Image
              </Button>
            </div>
            <Separator />
            <div className="space-y-2">
              <Label>Export Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="export-include-metadata" defaultChecked />
                  <Label htmlFor="export-include-metadata">Include metadata</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="export-high-quality" defaultChecked />
                  <Label htmlFor="export-high-quality">High quality</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExportDialog(false)}>Cancel</Button>
            <Button onClick={handleExportReport} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
