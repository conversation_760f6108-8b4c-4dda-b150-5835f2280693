"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { HelpCard } from "@/components/ui/contextual-help";
import { DatePicker } from "@/components/ui/date-picker";
import {
  BarChart3,
  LineChart as LineChartIcon,
  Pie<PERSON>hart as PieChartIcon,
  Download,
  Calendar,
  RefreshCw,
  Loader2,
  ArrowLeft,
  Leaf,
  // DollarSign,
  TrendingUp,
  Activity,
  Filter,
  Plus,
  Settings,
  Save,
  Share,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  ArrowUpRight,
  ArrowDownRight,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Wallet,
  FileText,
  LayoutDashboard,
  Sliders,
  ChevronDown,
  ChevronRight,
  Undo2,
  Redo2,
  Trash2
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AnimatedCard } from "@/components/ui/animated";
import { RupeeIcon } from "@/components/ui/icons/rupee-icon";

// Mock data for charts
const mockCarbonImpactData = {
  totalEmissionsReduced: 12500,
  totalCreditsRetired: 8750,
  emissionsReductionByMonth: [
    { month: "Jan", amount: 850 },
    { month: "Feb", amount: 920 },
    { month: "Mar", amount: 1050 },
    { month: "Apr", amount: 980 },
    { month: "May", amount: 1120 },
    { month: "Jun", amount: 1250 },
    { month: "Jul", amount: 1180 },
    { month: "Aug", amount: 1320 },
    { month: "Sep", amount: 1400 },
    { month: "Oct", amount: 1280 },
    { month: "Nov", amount: 1150 },
    { month: "Dec", amount: 1000 }
  ],
  creditsByStandard: [
    { standard: "Verra", count: 4200 },
    { standard: "Gold Standard", count: 2800 },
    { standard: "ACR", count: 1500 },
    { standard: "CAR", count: 950 },
    { standard: "Other", count: 550 }
  ],
  creditsByVintage: [
    { vintage: 2018, count: 950 },
    { vintage: 2019, count: 1200 },
    { vintage: 2020, count: 1850 },
    { vintage: 2021, count: 2300 },
    { vintage: 2022, count: 2950 },
    { vintage: 2023, count: 3500 }
  ]
};

const mockFinancialData = {
  totalTransactionVolume: 2850000,
  totalRevenue: 425000,
  totalExpenses: 285000,
  walletBalances: [
    { wallet: "Main Wallet", balance: 185000 },
    { wallet: "Reserve Wallet", balance: 320000 },
    { wallet: "Trading Wallet", balance: 95000 }
  ],
  carbonAssetValue: 1250000,
  transactionsByMonth: [
    { month: "Jan", amount: 180000 },
    { month: "Feb", amount: 195000 },
    { month: "Mar", amount: 220000 },
    { month: "Apr", amount: 210000 },
    { month: "May", amount: 240000 },
    { month: "Jun", amount: 265000 },
    { month: "Jul", amount: 250000 },
    { month: "Aug", amount: 280000 },
    { month: "Sep", amount: 295000 },
    { month: "Oct", amount: 275000 },
    { month: "Nov", amount: 245000 },
    { month: "Dec", amount: 215000 }
  ],
  revenueBySource: [
    { source: "Credit Sales", amount: 285000 },
    { source: "Transaction Fees", amount: 85000 },
    { source: "Subscription Fees", amount: 45000 },
    { source: "Other", amount: 10000 }
  ]
};

const mockTradingData = {
  totalOrders: 1850,
  activeOrders: 320,
  completedOrders: 1530,
  ordersByType: [
    { type: "Buy", count: 980 },
    { type: "Sell", count: 870 }
  ],
  priceHistory: [
    { date: "Jan", price: 12.5 },
    { date: "Feb", price: 13.2 },
    { date: "Mar", price: 14.8 },
    { date: "Apr", price: 14.2 },
    { date: "May", price: 15.5 },
    { date: "Jun", price: 16.8 },
    { date: "Jul", price: 16.2 },
    { date: "Aug", price: 17.5 },
    { date: "Sep", price: 18.2 },
    { date: "Oct", price: 17.8 },
    { date: "Nov", price: 16.5 },
    { date: "Dec", price: 15.8 }
  ],
  marketTrends: [
    { metric: "Average Price", value: 15.8, change: 2.5 },
    { metric: "Volume", value: 2850000, change: 12.5 },
    { metric: "Active Listings", value: 450, change: -5.2 },
    { metric: "Market Depth", value: 12500000, change: 8.7 }
  ]
};

// Mock saved dashboard layouts
const mockSavedDashboards = [
  { id: "1", name: "Carbon Impact Overview", description: "Key carbon impact metrics and trends", isDefault: true },
  { id: "2", name: "Financial Performance", description: "Revenue, expenses, and transaction metrics", isDefault: false },
  { id: "3", name: "Trading Activity", description: "Market trends and trading performance", isDefault: false },
  { id: "4", name: "Executive Summary", description: "High-level overview for executives", isDefault: false }
];

// Mock insights
const mockInsights = [
  {
    id: "1",
    type: "trend",
    severity: "positive",
    title: "Carbon Credit Retirement Increasing",
    description: "Carbon credit retirement has increased by 15% over the last quarter, indicating growing commitment to emissions reduction.",
    metric: "+15%",
    date: "2023-11-15"
  },
  {
    id: "2",
    type: "anomaly",
    severity: "warning",
    title: "Unusual Trading Volume Detected",
    description: "Trading volume spiked 35% above normal levels on November 10. This may indicate unusual market activity.",
    metric: "+35%",
    date: "2023-11-10"
  },
  {
    id: "3",
    type: "forecast",
    severity: "neutral",
    title: "Revenue Forecast Updated",
    description: "Based on current trends, Q4 revenue is projected to be 8% higher than previously forecasted.",
    metric: "+8%",
    date: "2023-11-12"
  },
  {
    id: "4",
    type: "trend",
    severity: "negative",
    title: "Expense Growth Outpacing Revenue",
    description: "Expenses have grown 12% while revenue has only increased 8% over the past 3 months.",
    metric: "-4%",
    date: "2023-11-08"
  }
];

// Available chart types for the dashboard
const chartTypes = [
  { id: "bar", name: "Bar Chart", icon: <BarChart3 className="h-4 w-4" /> },
  { id: "line", name: "Line Chart", icon: <LineChartIcon className="h-4 w-4" /> },
  { id: "pie", name: "Pie Chart", icon: <PieChartIcon className="h-4 w-4" /> },
  { id: "area", name: "Area Chart", icon: <Activity className="h-4 w-4" /> }
];

// Available metrics for the dashboard
const availableMetrics = [
  { id: "emissions_reduced", name: "Emissions Reduced", category: "carbon_impact", unit: "tCO2e" },
  { id: "credits_retired", name: "Credits Retired", category: "carbon_impact", unit: "credits" },
  { id: "credits_by_standard", name: "Credits by Standard", category: "carbon_impact", unit: "credits" },
  { id: "credits_by_vintage", name: "Credits by Vintage", category: "carbon_impact", unit: "credits" },
  { id: "transaction_volume", name: "Transaction Volume", category: "financial", unit: "₹" },
  { id: "revenue", name: "Revenue", category: "financial", unit: "₹" },
  { id: "expenses", name: "Expenses", category: "financial", unit: "₹" },
  { id: "wallet_balances", name: "Wallet Balances", category: "financial", unit: "₹" },
  { id: "carbon_asset_value", name: "Carbon Asset Value", category: "financial", unit: "₹" },
  { id: "revenue_by_source", name: "Revenue by Source", category: "financial", unit: "₹" },
  { id: "orders", name: "Orders", category: "trading", unit: "count" },
  { id: "price_history", name: "Price History", category: "trading", unit: "₹" },
  { id: "market_trends", name: "Market Trends", category: "trading", unit: "various" }
];

interface EnhancedDashboardProps {
  userId?: string;
  organizationId?: string;
}

export function EnhancedDashboard({
  userId,
  organizationId,
}: EnhancedDashboardProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [timeRange, setTimeRange] = useState("30d");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [showInsights, setShowInsights] = useState(true);
  const [activeDashboard, setActiveDashboard] = useState(mockSavedDashboards[0]);
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newDashboardName, setNewDashboardName] = useState("");
  const [newDashboardDescription, setNewDashboardDescription] = useState("");
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportFormat, setExportFormat] = useState("pdf");
  const [exportIncludeInsights, setExportIncludeInsights] = useState(true);
  const [showAddWidgetDialog, setShowAddWidgetDialog] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState(availableMetrics[0].id);
  const [selectedChartType, setSelectedChartType] = useState(chartTypes[0].id);
  const [widgetTitle, setWidgetTitle] = useState("");
  const [filterCategory, setFilterCategory] = useState("all");

  // Simulated data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Handle data refresh
  const handleRefresh = () => {
    setIsRefreshing(true);

    // Simulate API call
    setTimeout(() => {
      setIsRefreshing(false);
      toast({
        title: "Dashboard Refreshed",
        description: "The dashboard data has been updated with the latest information.",
      });
    }, 1000);
  };

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    setIsLoading(true);

    // Calculate new date range based on selected time range
    const now = new Date();
    let newStartDate;

    switch (value) {
      case "7d":
        newStartDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        newStartDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        newStartDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case "1y":
        newStartDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        newStartDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    setStartDate(newStartDate);
    setEndDate(now);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Handle dashboard save
  const handleSaveDashboard = () => {
    if (!newDashboardName) {
      toast({
        title: "Error",
        description: "Please provide a name for your dashboard.",
        variant: "destructive"
      });
      return;
    }

    // Simulate API call
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setShowSaveDialog(false);
      toast({
        title: "Dashboard Saved",
        description: `Dashboard "${newDashboardName}" has been saved successfully.`,
      });

      // Reset form
      setNewDashboardName("");
      setNewDashboardDescription("");
    }, 1000);
  };

  // Handle dashboard export
  const handleExportDashboard = () => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setShowExportDialog(false);
      toast({
        title: "Dashboard Exported",
        description: `Dashboard has been exported as ${exportFormat.toUpperCase()} successfully.`,
      });
    }, 1500);
  };

  // Handle add widget
  const handleAddWidget = () => {
    if (!widgetTitle) {
      toast({
        title: "Error",
        description: "Please provide a title for your widget.",
        variant: "destructive"
      });
      return;
    }

    // Simulate API call
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setShowAddWidgetDialog(false);
      toast({
        title: "Widget Added",
        description: `Widget "${widgetTitle}" has been added to your dashboard.`,
      });

      // Reset form
      setWidgetTitle("");
    }, 1000);
  };

  // Format number with commas and abbreviate large numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    } else {
      return num.toString();
    }
  };

  // Format currency
  const formatCurrency = (amount: number): string => {
    return '₹' + formatNumber(amount);
  };

  // Render insights section
  const renderInsights = () => {
    return (
      <Card className="col-span-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Insights & Recommendations</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => setShowInsights(!showInsights)}>
              {showInsights ? (
                <>
                  <EyeOff className="h-4 w-4 mr-1" />
                  Hide
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-1" />
                  Show
                </>
              )}
            </Button>
          </div>
          <CardDescription>
            AI-powered insights based on your data patterns and trends
          </CardDescription>
        </CardHeader>
        {showInsights && (
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {mockInsights.map((insight) => (
                <Card key={insight.id} className={`border-l-4 ${
                  insight.severity === 'positive' ? 'border-l-green-500' :
                  insight.severity === 'negative' ? 'border-l-red-500' :
                  insight.severity === 'warning' ? 'border-l-yellow-500' :
                  'border-l-blue-500'
                }`}>
                  <CardHeader className="p-4 pb-2">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-sm font-medium">
                        {insight.title}
                      </CardTitle>
                      <Badge variant={
                        insight.severity === 'positive' ? 'success' :
                        insight.severity === 'negative' ? 'destructive' :
                        insight.severity === 'warning' ? 'warning' :
                        'secondary'
                      }>
                        {insight.metric}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-xs text-muted-foreground">
                      {insight.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">
                        {insight.type === 'trend' ? 'Trend Analysis' :
                         insight.type === 'anomaly' ? 'Anomaly Detection' :
                         'Forecast'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {new Date(insight.date).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        )}
      </Card>
    );
  };

  // Render carbon impact metrics
  const renderCarbonImpactMetrics = () => {
    return (
      <>
        <Card className="col-span-1 md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Emissions Reduction</CardTitle>
            <CardDescription>Total emissions reduced over time</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {isLoading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <div className="text-center text-muted-foreground">
                <LineChartIcon className="h-16 w-16 mx-auto mb-4" />
                <p>Line chart visualization would appear here</p>
                <p className="text-sm mt-2">Showing monthly emissions reduction data</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Total Reduced</p>
              <p className="text-2xl font-bold">{formatNumber(mockCarbonImpactData.totalEmissionsReduced)} tCO2e</p>
            </div>
            <Button variant="outline" size="sm">
              <Maximize2 className="h-4 w-4 mr-1" />
              Expand
            </Button>
          </CardFooter>
        </Card>

        <Card className="col-span-1 md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Credits by Standard</CardTitle>
            <CardDescription>Distribution of credits by verification standard</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {isLoading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <div className="text-center text-muted-foreground">
                <PieChartIcon className="h-16 w-16 mx-auto mb-4" />
                <p>Pie chart visualization would appear here</p>
                <p className="text-sm mt-2">Showing distribution by verification standard</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Total Credits</p>
              <p className="text-2xl font-bold">{formatNumber(mockCarbonImpactData.creditsByStandard.reduce((sum, item) => sum + item.count, 0))}</p>
            </div>
            <Button variant="outline" size="sm">
              <Maximize2 className="h-4 w-4 mr-1" />
              Expand
            </Button>
          </CardFooter>
        </Card>
      </>
    );
  };

  // Render financial metrics
  const renderFinancialMetrics = () => {
    return (
      <>
        <Card className="col-span-1 md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Transaction Volume</CardTitle>
            <CardDescription>Monthly transaction volume</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {isLoading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <div className="text-center text-muted-foreground">
                <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                <p>Bar chart visualization would appear here</p>
                <p className="text-sm mt-2">Showing monthly transaction volume data</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Total Volume</p>
              <p className="text-2xl font-bold">{formatCurrency(mockFinancialData.totalTransactionVolume)}</p>
            </div>
            <Button variant="outline" size="sm">
              <Maximize2 className="h-4 w-4 mr-1" />
              Expand
            </Button>
          </CardFooter>
        </Card>

        <Card className="col-span-1 md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Revenue by Source</CardTitle>
            <CardDescription>Distribution of revenue by source</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {isLoading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <div className="text-center text-muted-foreground">
                <PieChartIcon className="h-16 w-16 mx-auto mb-4" />
                <p>Pie chart visualization would appear here</p>
                <p className="text-sm mt-2">Showing distribution of revenue by source</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Total Revenue</p>
              <p className="text-2xl font-bold">{formatCurrency(mockFinancialData.totalRevenue)}</p>
            </div>
            <Button variant="outline" size="sm">
              <Maximize2 className="h-4 w-4 mr-1" />
              Expand
            </Button>
          </CardFooter>
        </Card>
      </>
    );
  };

  // Render trading metrics
  const renderTradingMetrics = () => {
    return (
      <>
        <Card className="col-span-1 md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Price History</CardTitle>
            <CardDescription>Historical price trends</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {isLoading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <div className="text-center text-muted-foreground">
                <LineChartIcon className="h-16 w-16 mx-auto mb-4" />
                <p>Line chart visualization would appear here</p>
                <p className="text-sm mt-2">Showing historical price data</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Current Price</p>
              <p className="text-2xl font-bold">${mockTradingData.priceHistory[mockTradingData.priceHistory.length - 1].price.toFixed(2)}</p>
            </div>
            <Button variant="outline" size="sm">
              <Maximize2 className="h-4 w-4 mr-1" />
              Expand
            </Button>
          </CardFooter>
        </Card>

        <Card className="col-span-1 md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Orders</CardTitle>
            <CardDescription>Order statistics</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            {isLoading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <div className="text-center text-muted-foreground">
                <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                <p>Bar chart visualization would appear here</p>
                <p className="text-sm mt-2">Showing order statistics</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Total Orders</p>
              <p className="text-2xl font-bold">{formatNumber(mockTradingData.totalOrders)}</p>
            </div>
            <Button variant="outline" size="sm">
              <Maximize2 className="h-4 w-4 mr-1" />
              Expand
            </Button>
          </CardFooter>
        </Card>
      </>
    );
  };

  // Render custom report builder
  const renderCustomReportBuilder = () => {
    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Custom Report Builder",
            description: "Create custom reports by selecting metrics, time periods, and visualization types.",
            type: "info",
            tips: [
              "Drag and drop metrics to add them to your report",
              "Choose from different visualization types for each metric",
              "Set custom time periods for more detailed analysis",
              "Save your reports for future use or schedule automated exports"
            ]
          }}
          className="mb-6"
        />

        <div className="grid gap-6 md:grid-cols-3">
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>Available Metrics</CardTitle>
              <CardDescription>Drag metrics to add to your report</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between mb-2">
                  <Label>Filter by Category</Label>
                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="carbon_impact">Carbon Impact</SelectItem>
                      <SelectItem value="financial">Financial</SelectItem>
                      <SelectItem value="trading">Trading</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <ScrollArea className="h-[400px] pr-4">
                  {availableMetrics
                    .filter(metric => filterCategory === "all" || metric.category === filterCategory)
                    .map(metric => (
                      <div
                        key={metric.id}
                        className="flex items-center justify-between p-3 mb-2 border rounded-md cursor-move hover:bg-muted/50"
                      >
                        <div className="flex items-center">
                          {metric.category === "carbon_impact" ? (
                            <Leaf className="h-4 w-4 mr-2 text-green-500" />
                          ) : metric.category === "financial" ? (
                            <DollarSign className="h-4 w-4 mr-2 text-blue-500" />
                          ) : (
                            <TrendingUp className="h-4 w-4 mr-2 text-purple-500" />
                          )}
                          <span>{metric.name}</span>
                        </div>
                        <Badge variant="outline">{metric.unit}</Badge>
                      </div>
                    ))}
                </ScrollArea>
              </div>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Report Canvas</CardTitle>
                  <CardDescription>Build your custom report</CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Undo2 className="h-4 w-4 mr-1" />
                    Undo
                  </Button>
                  <Button variant="outline" size="sm">
                    <Redo2 className="h-4 w-4 mr-1" />
                    Redo
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed rounded-md h-[400px] flex items-center justify-center p-4">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">Drop Metrics Here</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Drag and drop metrics from the left panel to add them to your report
                  </p>
                  <Button onClick={() => setShowAddWidgetDialog(true)}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Widget
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="flex space-x-2">
                <Button variant="outline">
                  <Save className="h-4 w-4 mr-1" />
                  Save Draft
                </Button>
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-1" />
                  Preview
                </Button>
              </div>
              <Button>
                <FileText className="h-4 w-4 mr-1" />
                Generate Report
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  };

  // Render dashboard content based on active tab
  const renderDashboardContent = () => {
    switch (activeTab) {
      case "carbon-impact":
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {renderInsights()}
            {renderCarbonImpactMetrics()}
          </div>
        );
      case "financial":
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {renderInsights()}
            {renderFinancialMetrics()}
          </div>
        );
      case "trading":
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {renderInsights()}
            {renderTradingMetrics()}
          </div>
        );
      case "custom-report":
        return renderCustomReportBuilder();
      default:
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {renderInsights()}
            {renderCarbonImpactMetrics()}
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Visualize and analyze your carbon impact, financial performance, and trading activity
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
              <SelectItem value="90d">Last 90 Days</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
            {isRefreshing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </>
            )}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Options
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Dashboard Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowSaveDialog(true)}>
                <Save className="h-4 w-4 mr-2" />
                Save Dashboard
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowExportDialog(true)}>
                <Download className="h-4 w-4 mr-2" />
                Export Dashboard
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setIsCustomizing(!isCustomizing)}>
                <Sliders className="h-4 w-4 mr-2" />
                {isCustomizing ? "Exit Customize Mode" : "Customize Dashboard"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowInsights(!showInsights)}>
                {showInsights ? (
                  <>
                    <EyeOff className="h-4 w-4 mr-2" />
                    Hide Insights
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Show Insights
                  </>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Separator />

      {timeRange === "custom" && (
        <Card>
          <CardHeader>
            <CardTitle>Custom Date Range</CardTitle>
            <CardDescription>Select a custom date range for your analytics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="space-y-2 flex-1">
                <Label>Start Date</Label>
                <DatePicker date={startDate} setDate={setStartDate} />
              </div>
              <div className="space-y-2 flex-1">
                <Label>End Date</Label>
                <DatePicker date={endDate} setDate={setEndDate} />
              </div>
              <div className="flex items-end">
                <Button onClick={() => handleTimeRangeChange("custom")}>Apply Range</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex flex-col md:flex-row gap-4">
        <Card className="md:w-64 flex-shrink-0">
          <CardHeader>
            <CardTitle>Saved Dashboards</CardTitle>
            <CardDescription>Your custom dashboards</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="space-y-1">
              {mockSavedDashboards.map((dashboard) => (
                <Button
                  key={dashboard.id}
                  variant={activeDashboard.id === dashboard.id ? "secondary" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => setActiveDashboard(dashboard)}
                >
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  {dashboard.name}
                  {dashboard.isDefault && (
                    <Badge variant="outline" className="ml-2">Default</Badge>
                  )}
                </Button>
              ))}
            </div>
          </CardContent>
          <CardFooter className="pt-2">
            <Button variant="outline" className="w-full" onClick={() => setShowSaveDialog(true)}>
              <Plus className="h-4 w-4 mr-1" />
              New Dashboard
            </Button>
          </CardFooter>
        </Card>

        <div className="flex-1 space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="carbon-impact" className="flex items-center">
                <Leaf className="h-4 w-4 mr-2" />
                Carbon Impact
              </TabsTrigger>
              <TabsTrigger value="financial" className="flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Financial
              </TabsTrigger>
              <TabsTrigger value="trading" className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2" />
                Trading
              </TabsTrigger>
              <TabsTrigger value="custom-report" className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Custom Report
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <Loader2 className="h-12 w-12 animate-spin text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Loading dashboard data...</p>
                </div>
              ) : (
                renderDashboardContent()
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Save Dashboard Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Dashboard</DialogTitle>
            <DialogDescription>
              Save your current dashboard configuration for future use.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="dashboard-name">Dashboard Name</Label>
              <Input
                id="dashboard-name"
                placeholder="Enter dashboard name"
                value={newDashboardName}
                onChange={(e) => setNewDashboardName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dashboard-description">Description (Optional)</Label>
              <Input
                id="dashboard-description"
                placeholder="Enter dashboard description"
                value={newDashboardDescription}
                onChange={(e) => setNewDashboardDescription(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="make-default" />
              <Label htmlFor="make-default">Make this my default dashboard</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>Cancel</Button>
            <Button onClick={handleSaveDashboard} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Dashboard
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dashboard Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Dashboard</DialogTitle>
            <DialogDescription>
              Export your dashboard in various formats for sharing or reporting.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Export Format</Label>
              <RadioGroup value={exportFormat} onValueChange={setExportFormat} className="flex space-x-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pdf" id="pdf" />
                  <Label htmlFor="pdf">PDF</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="csv" id="csv" />
                  <Label htmlFor="csv">CSV</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="xlsx" id="xlsx" />
                  <Label htmlFor="xlsx">Excel</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="png" id="png" />
                  <Label htmlFor="png">Image</Label>
                </div>
              </RadioGroup>
            </div>
            <div className="space-y-2">
              <Label>Export Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-insights" checked={exportIncludeInsights} onCheckedChange={(checked) => setExportIncludeInsights(!!checked)} />
                  <Label htmlFor="include-insights">Include insights and recommendations</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-metadata" />
                  <Label htmlFor="include-metadata">Include metadata (date range, filters, etc.)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="white-label" />
                  <Label htmlFor="white-label">White-label export (remove branding)</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExportDialog(false)}>Cancel</Button>
            <Button onClick={handleExportDashboard} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Widget Dialog */}
      <Dialog open={showAddWidgetDialog} onOpenChange={setShowAddWidgetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Widget</DialogTitle>
            <DialogDescription>
              Add a new widget to your dashboard.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="widget-title">Widget Title</Label>
              <Input
                id="widget-title"
                placeholder="Enter widget title"
                value={widgetTitle}
                onChange={(e) => setWidgetTitle(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Metric</Label>
              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger>
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  {availableMetrics.map((metric) => (
                    <SelectItem key={metric.id} value={metric.id}>
                      {metric.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Chart Type</Label>
              <div className="grid grid-cols-2 gap-2">
                {chartTypes.map((chartType) => (
                  <Button
                    key={chartType.id}
                    type="button"
                    variant={selectedChartType === chartType.id ? "default" : "outline"}
                    className="justify-start"
                    onClick={() => setSelectedChartType(chartType.id)}
                  >
                    {chartType.icon}
                    <span className="ml-2">{chartType.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddWidgetDialog(false)}>Cancel</Button>
            <Button onClick={handleAddWidget} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Widget
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
