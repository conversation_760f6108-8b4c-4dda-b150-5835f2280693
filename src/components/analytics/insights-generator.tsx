"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { HelpCard } from "@/components/ui/contextual-help";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Lightbulb,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Info,
  BarChart3,
  LineChart,
  PieChart,
  RefreshCw,
  Loader2,
  Download,
  Share,
  Bookmark,
  BookmarkCheck,
  Filter,
  Search,
  Zap,
  Brain,
  Sparkles,
  ArrowUpRight,
  ArrowDownRight,
  Leaf,
  DollarSign,
  Wallet,
  Clock,
  Calendar,
  ChevronRight,
  ChevronDown,
  Eye,
  EyeOff,
  Flag
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { AnimatedCard } from "@/components/ui/animated";

// Sample insights data
const SAMPLE_INSIGHTS = [
  {
    id: "1",
    title: "Carbon Credit Retirement Increasing",
    description: "Carbon credit retirement has increased by 15% over the last quarter, indicating growing commitment to emissions reduction.",
    category: "carbon_impact",
    type: "trend",
    severity: "positive",
    metric: "+15%",
    date: "2023-11-15",
    confidence: 0.92,
    isBookmarked: false
  },
  {
    id: "2",
    title: "Unusual Trading Volume Detected",
    description: "Trading volume spiked 35% above normal levels on November 10. This may indicate unusual market activity.",
    category: "trading",
    type: "anomaly",
    severity: "warning",
    metric: "+35%",
    date: "2023-11-10",
    confidence: 0.85,
    isBookmarked: true
  },
  {
    id: "3",
    title: "Revenue Forecast Updated",
    description: "Based on current trends, Q4 revenue is projected to be 8% higher than previously forecasted.",
    category: "financial",
    type: "forecast",
    severity: "neutral",
    metric: "+8%",
    date: "2023-11-12",
    confidence: 0.78,
    isBookmarked: false
  },
  {
    id: "4",
    title: "Expense Growth Outpacing Revenue",
    description: "Expenses have grown 12% while revenue has only increased 8% over the past 3 months.",
    category: "financial",
    type: "trend",
    severity: "negative",
    metric: "-4%",
    date: "2023-11-08",
    confidence: 0.88,
    isBookmarked: false
  },
  {
    id: "5",
    title: "Carbon Credit Price Trend",
    description: "Carbon credit prices have increased by 7% in the last month, outperforming market expectations.",
    category: "trading",
    type: "trend",
    severity: "positive",
    metric: "+7%",
    date: "2023-11-05",
    confidence: 0.82,
    isBookmarked: false
  },
  {
    id: "6",
    title: "Wallet Balance Optimization",
    description: "Your reserve wallet has excess funds that could be allocated to trading for better returns.",
    category: "financial",
    type: "recommendation",
    severity: "neutral",
    metric: "$45K",
    date: "2023-11-14",
    confidence: 0.75,
    isBookmarked: true
  },
  {
    id: "7",
    title: "Seasonal Trading Pattern Detected",
    description: "Your trading activity shows a consistent pattern of increased volume at month-end.",
    category: "trading",
    type: "pattern",
    severity: "neutral",
    metric: "Monthly",
    date: "2023-11-01",
    confidence: 0.79,
    isBookmarked: false
  },
  {
    id: "8",
    title: "Carbon Impact Milestone Reached",
    description: "Your organization has surpassed 10,000 tCO2e in total emissions reduction.",
    category: "carbon_impact",
    type: "milestone",
    severity: "positive",
    metric: "10K tCO2e",
    date: "2023-11-11",
    confidence: 0.95,
    isBookmarked: true
  }
];

// Insight type icons
const TYPE_ICONS: Record<string, React.ReactNode> = {
  trend: <TrendingUp className="h-4 w-4" />,
  anomaly: <AlertTriangle className="h-4 w-4" />,
  forecast: <Zap className="h-4 w-4" />,
  recommendation: <Lightbulb className="h-4 w-4" />,
  pattern: <LineChart className="h-4 w-4" />,
  milestone: <Flag className="h-4 w-4" />
};

// Category icons
const CATEGORY_ICONS: Record<string, React.ReactNode> = {
  carbon_impact: <Leaf className="h-4 w-4" />,
  financial: <DollarSign className="h-4 w-4" />,
  trading: <TrendingUp className="h-4 w-4" />
};

// Severity badges
const SEVERITY_BADGES: Record<string, React.ReactNode> = {
  positive: <Badge variant="success" className="flex items-center"><ArrowUpRight className="h-3 w-3 mr-1" />Positive</Badge>,
  negative: <Badge variant="destructive" className="flex items-center"><ArrowDownRight className="h-3 w-3 mr-1" />Negative</Badge>,
  warning: <Badge variant="warning" className="flex items-center"><AlertTriangle className="h-3 w-3 mr-1" />Warning</Badge>,
  neutral: <Badge variant="secondary" className="flex items-center"><Info className="h-3 w-3 mr-1" />Neutral</Badge>
};

interface InsightsGeneratorProps {
  userId?: string;
  organizationId?: string;
}

export function InsightsGenerator({
  userId,
  organizationId,
}: InsightsGeneratorProps) {
  const [insights, setInsights] = useState(SAMPLE_INSIGHTS);
  const [activeTab, setActiveTab] = useState("all");
  const [isGenerating, setIsGenerating] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [severityFilter, setSeverityFilter] = useState("all");
  const [expandedInsight, setExpandedInsight] = useState<string | null>(null);

  // Filter insights based on active tab, search query, and filters
  const filteredInsights = insights.filter(insight => {
    // Filter by tab
    if (activeTab !== "all" && activeTab !== "bookmarked") {
      if (insight.category !== activeTab) return false;
    }

    // Filter bookmarked insights
    if (activeTab === "bookmarked" && !insight.isBookmarked) {
      return false;
    }

    // Filter by search query
    if (searchQuery && !insight.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !insight.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Filter by category
    if (categoryFilter !== "all" && insight.category !== categoryFilter) {
      return false;
    }

    // Filter by type
    if (typeFilter !== "all" && insight.type !== typeFilter) {
      return false;
    }

    // Filter by severity
    if (severityFilter !== "all" && insight.severity !== severityFilter) {
      return false;
    }

    return true;
  });

  // Toggle bookmark status
  const toggleBookmark = (insightId: string) => {
    setInsights(insights.map(insight =>
      insight.id === insightId
        ? { ...insight, isBookmarked: !insight.isBookmarked }
        : insight
    ));

    const insight = insights.find(i => i.id === insightId);
    if (insight) {
      toast({
        title: insight.isBookmarked ? "Bookmark Removed" : "Bookmark Added",
        description: `"${insight.title}" has been ${insight.isBookmarked ? "removed from" : "added to"} your bookmarks.`,
      });
    }
  };

  // Generate new insights
  const generateInsights = () => {
    setIsGenerating(true);

    // Simulate API call
    setTimeout(() => {
      setIsGenerating(false);
      toast({
        title: "Insights Generated",
        description: "New insights have been generated based on your latest data.",
      });
    }, 2000);
  };

  // Toggle expanded insight
  const toggleExpandInsight = (insightId: string) => {
    if (expandedInsight === insightId) {
      setExpandedInsight(null);
    } else {
      setExpandedInsight(insightId);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Insights Generator</h2>
          <p className="text-muted-foreground">
            AI-powered insights and recommendations based on your data
          </p>
        </div>

        <Button onClick={generateInsights} disabled={isGenerating}>
          {isGenerating ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Generate Insights
            </>
          )}
        </Button>
      </div>

      <Separator />

      <HelpCard
        content={{
          title: "About Insights Generator",
          description: "The Insights Generator uses AI to analyze your data and provide valuable insights and recommendations.",
          type: "info",
          tips: [
            "Insights are generated based on patterns and anomalies in your data",
            "Bookmark important insights to reference them later",
            "Filter insights by category, type, or severity to focus on what matters most",
            "Generate new insights anytime to get the latest analysis"
          ]
        }}
        className="mb-6"
      />

      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-64 space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full">
              <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
              <TabsTrigger value="bookmarked" className="flex-1">Bookmarked</TabsTrigger>
            </TabsList>
          </Tabs>

          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="carbon_impact">Carbon Impact</SelectItem>
                    <SelectItem value="financial">Financial</SelectItem>
                    <SelectItem value="trading">Trading</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Type</label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="trend">Trend</SelectItem>
                    <SelectItem value="anomaly">Anomaly</SelectItem>
                    <SelectItem value="forecast">Forecast</SelectItem>
                    <SelectItem value="recommendation">Recommendation</SelectItem>
                    <SelectItem value="pattern">Pattern</SelectItem>
                    <SelectItem value="milestone">Milestone</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Severity</label>
                <Select value={severityFilter} onValueChange={setSeverityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="positive">Positive</SelectItem>
                    <SelectItem value="negative">Negative</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="neutral">Neutral</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="carbon_impact">
            <TabsList className="w-full">
              <TabsTrigger value="carbon_impact" className="flex items-center">
                <Leaf className="h-4 w-4 mr-2" />
                Carbon
              </TabsTrigger>
              <TabsTrigger value="financial" className="flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Financial
              </TabsTrigger>
              <TabsTrigger value="trading" className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2" />
                Trading
              </TabsTrigger>
            </TabsList>

            <TabsContent value="carbon_impact" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Carbon Impact Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Reduced</span>
                      <span className="font-medium">12,500 tCO2e</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Growth Rate</span>
                      <span className="font-medium text-green-600">+15%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Insights</span>
                      <span className="font-medium">2</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="financial" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Financial Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Revenue</span>
                      <span className="font-medium">$425,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Growth Rate</span>
                      <span className="font-medium text-green-600">+8%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Insights</span>
                      <span className="font-medium">3</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="trading" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Trading Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Volume</span>
                      <span className="font-medium">$2.85M</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Growth Rate</span>
                      <span className="font-medium text-green-600">+12%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Insights</span>
                      <span className="font-medium">3</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex-1 space-y-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search insights..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {filteredInsights.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Brain className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Insights Found</h3>
                <p className="text-sm text-muted-foreground text-center max-w-md mb-6">
                  {searchQuery || categoryFilter !== "all" || typeFilter !== "all" || severityFilter !== "all"
                    ? "No insights match your current filters. Try adjusting your search criteria."
                    : "No insights available. Generate new insights to analyze your data."}
                </p>
                <Button onClick={generateInsights} disabled={isGenerating}>
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Insights
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ) : (
            <ScrollArea className="h-[calc(100vh-300px)]">
              <div className="space-y-4 pr-4">
                {filteredInsights.map(insight => (
                  <AnimatedCard key={insight.id} className={`border-l-4 ${
                    insight.severity === 'positive' ? 'border-l-green-500' :
                    insight.severity === 'negative' ? 'border-l-red-500' :
                    insight.severity === 'warning' ? 'border-l-yellow-500' :
                    'border-l-blue-500'
                  }`}>
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          {CATEGORY_ICONS[insight.category]}
                          <CardTitle className="ml-2 text-base">{insight.title}</CardTitle>
                        </div>
                        <div className="flex items-center space-x-1">
                          {SEVERITY_BADGES[insight.severity]}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => toggleBookmark(insight.id)}
                          >
                            {insight.isBookmarked ? (
                              <BookmarkCheck className="h-4 w-4 text-primary" />
                            ) : (
                              <Bookmark className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground mt-1">
                        <Badge variant="outline" className="mr-2 flex items-center">
                          {TYPE_ICONS[insight.type]}
                          <span className="ml-1 capitalize">{insight.type}</span>
                        </Badge>
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>{new Date(insight.date).toLocaleDateString()}</span>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm">{insight.description}</p>

                      {expandedInsight === insight.id && (
                        <div className="mt-4 pt-4 border-t">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium mb-2">Confidence Score</h4>
                              <div className="flex items-center">
                                <div className="w-full bg-muted rounded-full h-2 mr-2">
                                  <div
                                    className={`h-2 rounded-full ${
                                      insight.confidence > 0.8 ? 'bg-green-500' :
                                      insight.confidence > 0.6 ? 'bg-yellow-500' :
                                      'bg-red-500'
                                    }`}
                                    style={{ width: `${insight.confidence * 100}%` }}
                                  />
                                </div>
                                <span className="text-sm font-medium">{Math.round(insight.confidence * 100)}%</span>
                              </div>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium mb-2">Related Metrics</h4>
                              <div className="flex flex-wrap gap-2">
                                <Badge variant="outline">
                                  {insight.category === "carbon_impact" ? "Emissions" :
                                   insight.category === "financial" ? "Revenue" :
                                   "Trading Volume"}
                                </Badge>
                                <Badge variant="outline">
                                  {insight.category === "carbon_impact" ? "Credits" :
                                   insight.category === "financial" ? "Expenses" :
                                   "Orders"}
                                </Badge>
                              </div>
                            </div>
                          </div>

                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-2">Recommended Actions</h4>
                            <ul className="text-sm space-y-1 list-disc pl-5">
                              <li>
                                {insight.severity === "positive" ? "Continue current strategy to maintain positive trend" :
                                 insight.severity === "negative" ? "Investigate root causes and develop mitigation plan" :
                                 insight.severity === "warning" ? "Monitor closely and prepare contingency plans" :
                                 "Review data for potential opportunities"}
                              </li>
                              <li>
                                {insight.type === "trend" ? "Analyze contributing factors to understand drivers" :
                                 insight.type === "anomaly" ? "Investigate unusual pattern for potential issues" :
                                 insight.type === "forecast" ? "Adjust planning based on projected outcomes" :
                                 insight.type === "recommendation" ? "Implement suggested changes to improve results" :
                                 insight.type === "pattern" ? "Leverage pattern for strategic planning" :
                                 "Celebrate achievement and set new targets"}
                              </li>
                            </ul>
                          </div>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="pt-0 flex justify-between">
                      <div className="flex items-center">
                        <Badge variant="outline" className="mr-2">
                          {insight.metric}
                        </Badge>
                        <Badge variant="outline" className="flex items-center">
                          <Brain className="h-3 w-3 mr-1" />
                          AI Generated
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpandInsight(insight.id)}
                      >
                        {expandedInsight === insight.id ? (
                          <>
                            Less Details
                            <ChevronDown className="h-4 w-4 ml-1" />
                          </>
                        ) : (
                          <>
                            More Details
                            <ChevronRight className="h-4 w-4 ml-1" />
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </AnimatedCard>
                ))}
              </div>
            </ScrollArea>
          )}

          <div className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {filteredInsights.length} of {insights.length} insights
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Share className="h-4 w-4 mr-1" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}