"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  Users,
  Building2,
  <PERSON>Card,
  Leaf,
  BarChart3,
  DollarSign,
  TrendingUp,
  RefreshCw
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";

type PlatformStats = {
  users: {
    total: number;
    organizations: {
      total: number;
      active: number;
    };
  };
  carbonCredits: {
    total: number;
    listed: number;
    sold: number;
    retired: number;
  };
  transactions: {
    total: number;
    completed: number;
    volume: number;
    fees: number;
  };
  orders: {
    total: number;
    completed: number;
    pending: number;
  };
  subscriptions: Record<string, number>;
};

type OrganizationStats = {
  organization: {
    id: string;
    name: string;
    status: string;
    subscription: string;
    transactionFeeRate: number;
    listingFeeRate: number;
  };
  users: {
    total: number;
  };
  carbonCredits: {
    total: number;
    listed: number;
    sold: number;
    retired: number;
    volume: number;
  };
  transactions: {
    buyVolume: number;
    sellVolume: number;
    totalVolume: number;
    fees: number;
  };
};

export function DashboardOverview() {
  const { data: session } = useSession();
  const [platformStats, setPlatformStats] = useState<PlatformStats | null>(null);
  const [orgStats, setOrgStats] = useState<OrganizationStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const { toast } = useToast();

  const fetchPlatformStats = async () => {
    if (!session?.user) return;

    try {
      setIsLoading(true);
      const response = await fetch("/api/analytics/platform");

      if (!response.ok) {
        throw new Error("Failed to fetch platform stats");
      }

      const data = await response.json();
      setPlatformStats(data);
    } catch (error) {
      console.error("Error fetching platform stats:", error);
      toast({
        title: "Error",
        description: "Failed to load platform statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchOrganizationStats = async () => {
    if (!session?.user?.organizationId) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/analytics/organization/${session.user.organizationId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch organization stats");
      }

      const data = await response.json();
      setOrgStats(data);
    } catch (error) {
      console.error("Error fetching organization stats:", error);
      toast({
        title: "Error",
        description: "Failed to load organization statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      if (session.user.role === "ADMIN") {
        fetchPlatformStats();
      } else {
        fetchOrganizationStats();
      }
    }
  }, [session]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("en-US").format(value);
  };

  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: 2,
    }).format(value);
  };

  const refreshStats = () => {
    if (session?.user?.role === "ADMIN") {
      fetchPlatformStats();
    } else {
      fetchOrganizationStats();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Dashboard</h2>
        <Button variant="outline" size="sm" onClick={refreshStats}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {session?.user?.role === "ADMIN" ? (
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="users">Users & Organizations</TabsTrigger>
            <TabsTrigger value="credits">Carbon Credits</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {isLoading ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Card key={i}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        <Skeleton className="h-4 w-24" />
                      </CardTitle>
                      <Skeleton className="h-4 w-4" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-7 w-full" />
                      <Skeleton className="h-4 w-1/2 mt-1" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : platformStats ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(platformStats.users.total)}</div>
                    <p className="text-xs text-muted-foreground">
                      Across {formatNumber(platformStats.users.organizations.total)} organizations
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Carbon Credits</CardTitle>
                    <Leaf className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(platformStats.carbonCredits.total)}</div>
                    <p className="text-xs text-muted-foreground">
                      {formatNumber(platformStats.carbonCredits.listed)} listed, {formatNumber(platformStats.carbonCredits.sold)} sold
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(platformStats.transactions.volume)}</div>
                    <p className="text-xs text-muted-foreground">
                      {formatNumber(platformStats.transactions.completed)} completed transactions
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Platform Fees</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(platformStats.transactions.fees)}</div>
                    <p className="text-xs text-muted-foreground">
                      From {formatNumber(platformStats.transactions.total)} transactions
                    </p>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center p-6">
                  <p className="text-muted-foreground">No data available</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="users" className="space-y-4">
            {isLoading ? (
              <div className="grid gap-4 md:grid-cols-2">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Card key={i}>
                    <CardHeader>
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-48" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-40 w-full" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : platformStats ? (
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>User Statistics</CardTitle>
                    <CardDescription>Overview of platform users</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Users</span>
                        <span className="font-bold">{formatNumber(platformStats.users.total)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Organizations</span>
                        <span className="font-bold">{formatNumber(platformStats.users.organizations.total)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Active Organizations</span>
                        <span className="font-bold">{formatNumber(platformStats.users.organizations.active)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Activation Rate</span>
                        <span className="font-bold">
                          {formatPercentage(
                            platformStats.users.organizations.active / platformStats.users.organizations.total
                          )}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Distribution</CardTitle>
                    <CardDescription>Breakdown by subscription plan</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(platformStats.subscriptions).map(([plan, count]) => (
                        <div key={plan} className="flex items-center justify-between">
                          <span className="text-sm font-medium capitalize">{plan}</span>
                          <span className="font-bold">{formatNumber(count)}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center p-6">
                  <p className="text-muted-foreground">No data available</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="credits" className="space-y-4">
            {isLoading ? (
              <Card>
                <CardHeader>
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-48" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-40 w-full" />
                </CardContent>
              </Card>
            ) : platformStats ? (
              <Card>
                <CardHeader>
                  <CardTitle>Carbon Credit Statistics</CardTitle>
                  <CardDescription>Overview of carbon credits on the platform</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Total Carbon Credits</span>
                      <span className="font-bold">{formatNumber(platformStats.carbonCredits.total)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Listed Credits</span>
                      <span className="font-bold">{formatNumber(platformStats.carbonCredits.listed)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Sold Credits</span>
                      <span className="font-bold">{formatNumber(platformStats.carbonCredits.sold)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Retired Credits</span>
                      <span className="font-bold">{formatNumber(platformStats.carbonCredits.retired)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Listing Rate</span>
                      <span className="font-bold">
                        {formatPercentage(platformStats.carbonCredits.listed / platformStats.carbonCredits.total)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Sale Conversion Rate</span>
                      <span className="font-bold">
                        {formatPercentage(platformStats.carbonCredits.sold / platformStats.carbonCredits.listed)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center p-6">
                  <p className="text-muted-foreground">No data available</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="transactions" className="space-y-4">
            {isLoading ? (
              <div className="grid gap-4 md:grid-cols-2">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Card key={i}>
                    <CardHeader>
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-48" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-40 w-full" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : platformStats ? (
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Transaction Statistics</CardTitle>
                    <CardDescription>Overview of platform transactions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Transactions</span>
                        <span className="font-bold">{formatNumber(platformStats.transactions.total)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completed Transactions</span>
                        <span className="font-bold">{formatNumber(platformStats.transactions.completed)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completion Rate</span>
                        <span className="font-bold">
                          {formatPercentage(platformStats.transactions.completed / platformStats.transactions.total)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Volume</span>
                        <span className="font-bold">{formatCurrency(platformStats.transactions.volume)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Average Transaction Value</span>
                        <span className="font-bold">
                          {formatCurrency(
                            platformStats.transactions.volume / platformStats.transactions.completed
                          )}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Order Statistics</CardTitle>
                    <CardDescription>Overview of platform orders</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Orders</span>
                        <span className="font-bold">{formatNumber(platformStats.orders.total)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completed Orders</span>
                        <span className="font-bold">{formatNumber(platformStats.orders.completed)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Pending Orders</span>
                        <span className="font-bold">{formatNumber(platformStats.orders.pending)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completion Rate</span>
                        <span className="font-bold">
                          {formatPercentage(platformStats.orders.completed / platformStats.orders.total)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Fees Collected</span>
                        <span className="font-bold">{formatCurrency(platformStats.transactions.fees)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center p-6">
                  <p className="text-muted-foreground">No data available</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      ) : (
        // Organization dashboard
        <div className="space-y-6">
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      <Skeleton className="h-4 w-24" />
                    </CardTitle>
                    <Skeleton className="h-4 w-4" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-7 w-full" />
                    <Skeleton className="h-4 w-1/2 mt-1" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : orgStats ? (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Carbon Credits</CardTitle>
                    <Leaf className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(orgStats.carbonCredits.total)}</div>
                    <p className="text-xs text-muted-foreground">
                      {formatNumber(orgStats.carbonCredits.listed)} listed, {formatNumber(orgStats.carbonCredits.sold)} sold
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(orgStats.transactions.totalVolume)}</div>
                    <p className="text-xs text-muted-foreground">
                      Buy: {formatCurrency(orgStats.transactions.buyVolume)}, Sell: {formatCurrency(orgStats.transactions.sellVolume)}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Fees Paid</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(orgStats.transactions.fees)}</div>
                    <p className="text-xs text-muted-foreground">
                      Transaction fee rate: {formatPercentage(orgStats.organization.transactionFeeRate)}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Team Members</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(orgStats.users.total)}</div>
                    <p className="text-xs text-muted-foreground">
                      Subscription: {orgStats.organization.subscription}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Carbon Credit Statistics</CardTitle>
                    <CardDescription>Overview of your carbon credits</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Carbon Credits</span>
                        <span className="font-bold">{formatNumber(orgStats.carbonCredits.total)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Listed Credits</span>
                        <span className="font-bold">{formatNumber(orgStats.carbonCredits.listed)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Sold Credits</span>
                        <span className="font-bold">{formatNumber(orgStats.carbonCredits.sold)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Retired Credits</span>
                        <span className="font-bold">{formatNumber(orgStats.carbonCredits.retired)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Volume</span>
                        <span className="font-bold">{formatNumber(orgStats.carbonCredits.volume)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Listing Fee Rate</span>
                        <span className="font-bold">{formatPercentage(orgStats.organization.listingFeeRate)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Transaction Statistics</CardTitle>
                    <CardDescription>Overview of your transactions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Volume</span>
                        <span className="font-bold">{formatCurrency(orgStats.transactions.totalVolume)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Buy Volume</span>
                        <span className="font-bold">{formatCurrency(orgStats.transactions.buyVolume)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Sell Volume</span>
                        <span className="font-bold">{formatCurrency(orgStats.transactions.sellVolume)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Total Fees Paid</span>
                        <span className="font-bold">{formatCurrency(orgStats.transactions.fees)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Transaction Fee Rate</span>
                        <span className="font-bold">{formatPercentage(orgStats.organization.transactionFeeRate)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center p-6">
                <p className="text-muted-foreground">No data available</p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
