"use client";

import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { AnimatedFormMessage } from "@/components/ui/animated";
import { simpleEmailSchema } from "@/lib/validation/schemas";

const invitationSchema = z.object({
  email: simpleEmailSchema,
  role: z.enum(["USER", "ADMIN", "FINANCE", "COMPLIANCE"]),
});

type InvitationFormValues = z.infer<typeof invitationSchema>;

interface InvitedTeamMember {
  id: string;
  email: string;
  role: string;
  status: "PENDING";
}

interface TeamInvitationFormProps {
  organizationId: string;
  onSuccess?: () => void;
}

export function TeamInvitationForm({ organizationId, onSuccess }: TeamInvitationFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitedMembers, setInvitedMembers] = useState<InvitedTeamMember[]>([]);

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<InvitationFormValues>({
    resolver: zodResolver(invitationSchema),
    defaultValues: {
      role: "USER",
    },
  });

  async function onSubmit(data: InvitationFormValues) {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/organizations/${organizationId}/invitations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          role: data.role,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || "An error occurred while sending the invitation");
        setIsLoading(false);
        return;
      }

      // Add the newly invited member to the list
      setInvitedMembers([
        ...invitedMembers,
        {
          id: result.invitation.id,
          email: data.email,
          role: data.role,
          status: "PENDING",
        },
      ]);

      // Reset the form
      reset();
      setIsLoading(false);

      toast({
        title: "Invitation sent",
        description: `An invitation has been sent to ${data.email}`,
      });
    } catch {
      setError("An error occurred. Please try again.");
      setIsLoading(false);
    }
  }

  function handleContinue() {
    if (onSuccess) {
      onSuccess();
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Invite Team Members</h3>
        <p className="text-sm text-muted-foreground">
          Invite colleagues to join your organization. You can set their role and permissions.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
          <div className="grid gap-2 md:col-span-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email Address
            </label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              disabled={isLoading}
              {...register("email")}
            />
            {errors.email && (
              <AnimatedFormMessage className="text-red-500">{errors.email.message}</AnimatedFormMessage>
            )}
          </div>

          <div className="grid gap-2">
            <label htmlFor="role" className="text-sm font-medium">
              Role
            </label>
            <Select
              onValueChange={(value) => setValue("role", value as "USER" | "ADMIN" | "FINANCE" | "COMPLIANCE")}
              defaultValue="USER"
            >
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USER">Team Member</SelectItem>
                <SelectItem value="ADMIN">Administrator</SelectItem>
                <SelectItem value="FINANCE">Finance</SelectItem>
                <SelectItem value="COMPLIANCE">Compliance</SelectItem>
              </SelectContent>
            </Select>
            {errors.role && (
              <AnimatedFormMessage className="text-red-500">{errors.role.message}</AnimatedFormMessage>
            )}
          </div>
        </div>

        {error && <AnimatedFormMessage className="text-red-500">{error}</AnimatedFormMessage>}

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Sending..." : "Send Invitation"}
          </Button>
        </div>
      </form>

      {invitedMembers.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium mb-2">Invited Members</h4>
          <div className="border rounded-md">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="px-4 py-2 text-left text-sm">Email</th>
                  <th className="px-4 py-2 text-left text-sm">Role</th>
                  <th className="px-4 py-2 text-left text-sm">Status</th>
                </tr>
              </thead>
              <tbody>
                {invitedMembers.map((member) => (
                  <tr key={member.id} className="border-b last:border-0">
                    <td className="px-4 py-2 text-sm">{member.email}</td>
                    <td className="px-4 py-2 text-sm">
                      {member.role === "USER"
                        ? "Team Member"
                        : member.role === "ADMIN"
                        ? "Administrator"
                        : member.role === "FINANCE"
                        ? "Finance"
                        : "Compliance"}
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <span className="inline-flex items-center rounded-full bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800">
                        {member.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div className="flex justify-end mt-6">
        <Button type="button" onClick={handleContinue} variant="default">
          Continue to Next Step
        </Button>
      </div>
    </div>
  );
}
