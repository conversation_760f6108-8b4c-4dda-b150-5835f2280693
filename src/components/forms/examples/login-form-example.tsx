"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ValidatedForm } from "@/components/forms/validated-form";
import { loginSchema } from "@/lib/validation/schemas";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { signIn } from "next-auth/react";
import Link from "next/link";

/**
 * Login Form Example
 *
 * This component demonstrates how to use the ValidatedForm component with the loginSchema
 * to create a login form with consistent validation and error handling.
 */
export function LoginFormExample() {
  const router = useRouter();
  const [authError, setAuthError] = useState<string | null>(null);

  const handleLogin = async (data: { email: string; password: string }) => {
    // Clear any previous errors
    setAuthError(null);

    // Sign in with credentials
    const result = await signIn("credentials", {
      redirect: false,
      email: data.email,
      password: data.password,
    });

    // Handle authentication error
    if (!result?.ok) {
      setAuthError(result?.error || "An error occurred during login");
      return;
    }

    // Redirect to dashboard on success
    router.push("/dashboard");
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Login</CardTitle>
        <CardDescription>Enter your credentials to access your account</CardDescription>
      </CardHeader>
      <CardContent>
        <ValidatedForm
          schema={loginSchema}
          defaultValues={{ email: "", password: "" }}
          onSubmit={handleLogin}
          formOptions={{
            // Don't show toast for validation errors, we'll display them inline
            showToast: false,
          }}
        >
          {({ control, isSubmitting, formError }) => (
            <>
              {/* Display authentication error if any */}
              {(authError || formError) && (
                <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md mb-4">
                  {authError || formError?.message}
                </div>
              )}

              {/* Email field */}
              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Password field */}
              <FormField
                control={control}
                name="password"
                render={({ field }) => (
                  <FormItem className="mb-6">
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Enter your password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Submit button */}
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Logging in..." : "Login"}
              </Button>
            </>
          )}
        </ValidatedForm>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <div className="text-sm text-center">
          <Link href="/forgot-password" className="text-primary hover:underline">
            Forgot your password?
          </Link>
        </div>
        <div className="text-sm text-center">
          Don&apos;t have an account?{" "}
          <Link href="/register" className="text-primary hover:underline">
            Register
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
