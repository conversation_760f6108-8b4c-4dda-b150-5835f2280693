"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ValidatedForm } from "@/components/forms/validated-form";
import { registrationSchema } from "@/lib/validation/schemas";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { toast } from "@/components/ui/use-toast";

/**
 * Registration Form Example
 *
 * This component demonstrates how to use the ValidatedForm component with the registrationSchema
 * to create a registration form with consistent validation and error handling.
 */
export function RegistrationFormExample() {
  const router = useRouter();
  const [registrationError, setRegistrationError] = useState<string | null>(null);

  const handleRegistration = async (data: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) => {
    // Clear any previous errors
    setRegistrationError(null);

    try {
      // Send registration request to API
      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.name,
          email: data.email,
          password: data.password,
        }),
      });

      // Parse response
      const result = await response.json();

      // Handle error response
      if (!response.ok) {
        setRegistrationError(result.error || "An error occurred during registration");
        return;
      }

      // Show success message
      toast({
        title: "Registration Successful",
        description: "Your account has been created. You can now log in.",
        variant: "default",
      });

      // Redirect to login page
      router.push("/login?registered=true");
    } catch (error) {
      // Handle unexpected errors
      setRegistrationError("An unexpected error occurred. Please try again.");
      console.error("Registration error:", error);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Create an Account</CardTitle>
        <CardDescription>Enter your details to create a new account</CardDescription>
      </CardHeader>
      <CardContent>
        <ValidatedForm
          schema={registrationSchema}
          defaultValues={{ name: "", email: "", password: "", confirmPassword: "" }}
          onSubmit={handleRegistration}
          formOptions={{
            // Don't show toast for validation errors, we'll display them inline
            showToast: false,
          }}
        >
          {({ control, isSubmitting, formError }) => (
            <>
              {/* Display registration error if any */}
              {(registrationError || formError) && (
                <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md mb-4">
                  {registrationError || formError?.message}
                </div>
              )}

              {/* Name field */}
              <FormField
                control={control}
                name="name"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email field */}
              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Password field */}
              <FormField
                control={control}
                name="password"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Create a password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Confirm Password field */}
              <FormField
                control={control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="mb-6">
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Confirm your password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Submit button */}
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Creating Account..." : "Create Account"}
              </Button>
            </>
          )}
        </ValidatedForm>
      </CardContent>
      <CardFooter className="flex justify-center">
        <div className="text-sm text-center">
          Already have an account?{" "}
          <Link href="/login" className="text-primary hover:underline">
            Login
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
