"use client";

import React from "react";
import { z } from "zod";
import { FieldValues } from "react-hook-form";
import { AnimatedForm } from "@/components/ui/animated/form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useValidatedForm, UseValidatedFormOptions } from "@/lib/validation/hooks";
import { VARIANTS } from "@/lib/animations";

interface ValidatedFormProps<T extends FieldValues> {
  schema: z.Schema<T>;
  defaultValues?: Partial<T>;
  onSubmit: (data: T) => Promise<void> | void;
  children: React.ReactNode | ((form: ReturnType<typeof useValidatedForm<T>>) => React.ReactNode);
  className?: string;
  animationVariant?: keyof typeof VARIANTS;
  showErrorSummary?: boolean;
  formOptions?: UseValidatedFormOptions<T>;
}

/**
 * ValidatedForm component
 *
 * A form component that uses Zod for validation and provides standardized error handling.
 *
 * @example
 * ```tsx
 * <ValidatedForm
 *   schema={loginSchema}
 *   defaultValues={{ email: "", password: "" }}
 *   onSubmit={handleLogin}
 * >
 *   {({ register, formState }) => (
 *     <>
 *       <FormField
 *         control={control}
 *         name="email"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Email</FormLabel>
 *             <FormControl>
 *               <Input placeholder="Email" {...field} />
 *             </FormControl>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit" disabled={isSubmitting}>
 *         {isSubmitting ? "Logging in..." : "Login"}
 *       </Button>
 *     </>
 *   )}
 * </ValidatedForm>
 * ```
 */
export function ValidatedForm<T extends FieldValues>({
  schema,
  defaultValues,
  onSubmit,
  children,
  className,
  animationVariant = "fadeIn",
  showErrorSummary = true,
  formOptions,
}: ValidatedFormProps<T>) {
  // Initialize form with validation
  const form = useValidatedForm<T>(schema, {
    ...formOptions,
    // Only include defaultValues if provided
    ...(defaultValues ? { defaultValues: defaultValues as any } : {}),
  });

  const {
    handleSubmitWithValidation,
    formError,
  } = form;

  // Memoize the submit handler to prevent re-renders
  const submitHandler = React.useMemo(
    () => handleSubmitWithValidation(onSubmit),
    [handleSubmitWithValidation, onSubmit]
  );

  return (
    <AnimatedForm
      onSubmit={submitHandler}
      className={className}
      animationVariant={animationVariant}
    >
      {/* Error summary */}
      {showErrorSummary && formError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{formError.message}</AlertDescription>
        </Alert>
      )}

      {/* Form content */}
      {typeof children === "function" ? children(form) : children}
    </AnimatedForm>
  );
}

export default ValidatedForm;
