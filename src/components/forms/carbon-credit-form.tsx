"use client";


import { useRouter } from "next/navigation";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { RequiredLabel } from "@/components/ui/required-label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ValidatedForm } from "@/components/forms/validated-form";
import { carbonCreditSchema } from "@/lib/validation/schemas";

type CarbonCreditFormValues = {
  name: string;
  description: string;
  quantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location?: string;
};

const standards = [
  "Verra",
  "Gold Standard",
  "American Carbon Registry",
  "Climate Action Reserve",
  "Plan Vivo",
  "Clean Development Mechanism",
  "Other",
];

const methodologies = [
  "Renewable Energy",
  "Energy Efficiency",
  "Forestry and Land Use",
  "Waste Management",
  "Transportation",
  "Industrial Processes",
  "Agriculture",
  "Other",
];

interface CarbonCreditFormProps {
  onSuccess?: () => void;
}

export function CarbonCreditForm({ onSuccess }: CarbonCreditFormProps) {
  const router = useRouter();

  // Default form values
  const defaultValues: CarbonCreditFormValues = {
    name: "",
    description: "",
    quantity: 0,
    price: 0,
    vintage: new Date().getFullYear(),
    standard: "",
    methodology: "",
    location: "",
  };

  // Form submission handler
  async function onSubmit(data: CarbonCreditFormValues): Promise<void> {
    try {
      const response = await fetch("/api/carbon-credits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create carbon credit");
      }

      toast({
        title: "Carbon credit created",
        description: "Your carbon credit has been created successfully.",
      });

      if (onSuccess) {
        onSuccess();
      } else {
        router.push("/dashboard/carbon-credits");
        router.refresh();
      }
    } catch (error) {
      console.error("Error creating carbon credit:", error);
      throw new Error(error instanceof Error ? error.message : "Failed to create carbon credit");
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>List a Carbon Credit</CardTitle>
        <CardDescription>
          Provide details about your carbon credit to list it on the marketplace.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ValidatedForm
          schema={carbonCreditSchema}
          defaultValues={defaultValues}
          onSubmit={onSubmit}
          className="space-y-8"
        >
          {({ control, isSubmitting, formError }) => (
            <>
            {formError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{formError.message}</AlertDescription>
              </Alert>
            )}
            {/* Essential Project Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Essential Project Information</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Basic details required for your carbon credit listing
              </p>

              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredLabel>Project Name</RequiredLabel>
                      <FormControl>
                        <Input placeholder="e.g., Wind Farm Project" {...field} />
                      </FormControl>
                      <FormDescription>
                        The name of your carbon credit project.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Location</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Texas, USA" {...field} />
                      </FormControl>
                      <FormDescription>
                        The geographic location of the project.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Description</RequiredLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your carbon credit project in detail..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a detailed description of your carbon credit project.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Certification Details */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Certification Details</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Information about the verification and methodology
              </p>

              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={control}
                  name="standard"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredLabel>Standard</RequiredLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a standard" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {standards.map((standard) => (
                            <SelectItem key={standard} value={standard}>
                              {standard}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The verification standard used for the carbon credit.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="methodology"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredLabel>Methodology</RequiredLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a methodology" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {methodologies.map((methodology) => (
                            <SelectItem key={methodology} value={methodology}>
                              {methodology}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The methodology used to generate the carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="vintage"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredLabel>Vintage Year</RequiredLabel>
                      <FormControl>
                        <Input type="number" min={2000} {...field} />
                      </FormControl>
                      <FormDescription>
                        The year the carbon credits were generated.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Listing Details */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Listing Details</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Quantity and pricing information for your carbon credits
              </p>

              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredLabel>Quantity (tons)</RequiredLabel>
                      <FormControl>
                        <Input type="number" min={0} step={0.01} {...field} />
                      </FormControl>
                      <FormDescription>
                        The amount of carbon credits in metric tons of CO2e.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <RequiredLabel>Price per Ton (INR)</RequiredLabel>
                      <FormControl>
                        <Input type="number" min={0} step={0.01} {...field} />
                      </FormControl>
                      <FormDescription>
                        The price per ton of carbon credits in INR.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Carbon Credit"
                )}
              </Button>
            </div>
            </>
          )}
        </ValidatedForm>
      </CardContent>
    </Card>
  );
}
