"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  Animated<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  AnimatedCard<PERSON><PERSON><PERSON>, 
  AnimatedCardT<PERSON>le,
  AnimatedButton
} from "@/components/ui/animated";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

/**
 * Animation settings component for user preferences
 */
export function AnimationSettings() {
  // Get animation preferences from localStorage
  const [enableAnimations, setEnableAnimations] = useState(true);
  const [animationIntensity, setAnimationIntensity] = useState(100);
  const [enableReducedMotion, setEnableReducedMotion] = useState(false);
  
  // Load preferences from localStorage on mount
  useEffect(() => {
    const storedEnableAnimations = localStorage.getItem("enableAnimations");
    const storedAnimationIntensity = localStorage.getItem("animationIntensity");
    const storedEnableReducedMotion = localStorage.getItem("enableReducedMotion");
    
    if (storedEnableAnimations !== null) {
      setEnableAnimations(storedEnableAnimations === "true");
    }
    
    if (storedAnimationIntensity !== null) {
      setAnimationIntensity(parseInt(storedAnimationIntensity));
    }
    
    if (storedEnableReducedMotion !== null) {
      setEnableReducedMotion(storedEnableReducedMotion === "true");
    } else {
      // Check if user has prefers-reduced-motion set in their OS
      const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;
      setEnableReducedMotion(prefersReducedMotion);
    }
  }, []);
  
  // Save preferences to localStorage when changed
  useEffect(() => {
    localStorage.setItem("enableAnimations", enableAnimations.toString());
    localStorage.setItem("animationIntensity", animationIntensity.toString());
    localStorage.setItem("enableReducedMotion", enableReducedMotion.toString());
    
    // Apply preferences to document
    document.documentElement.style.setProperty(
      "--animation-multiplier", 
      enableAnimations ? (animationIntensity / 100).toString() : "0"
    );
    
    // Add or remove reduced-motion class to body
    if (enableReducedMotion || !enableAnimations) {
      document.body.classList.add("reduce-motion");
    } else {
      document.body.classList.remove("reduce-motion");
    }
  }, [enableAnimations, animationIntensity, enableReducedMotion]);
  
  // Reset to defaults
  const resetToDefaults = () => {
    setEnableAnimations(true);
    setAnimationIntensity(100);
    setEnableReducedMotion(false);
  };
  
  return (
    <AnimatedCard animationVariant="fadeIn">
      <AnimatedCardHeader>
        <AnimatedCardTitle>Animation Settings</AnimatedCardTitle>
      </AnimatedCardHeader>
      <AnimatedCardContent>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enable-animations" className="text-sm font-medium">Enable Animations</Label>
              <p className="text-xs text-muted-foreground">
                Turn on/off all animations and transitions
              </p>
            </div>
            <Switch
              id="enable-animations"
              checked={enableAnimations}
              onCheckedChange={setEnableAnimations}
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="animation-intensity" className="text-sm font-medium">Animation Intensity</Label>
              <span className="text-sm">{animationIntensity}%</span>
            </div>
            <Slider
              id="animation-intensity"
              min={0}
              max={100}
              step={10}
              value={[animationIntensity]}
              onValueChange={(value) => setAnimationIntensity(value[0])}
              disabled={!enableAnimations}
            />
            <p className="text-xs text-muted-foreground">
              Adjust the speed and intensity of animations
            </p>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="reduced-motion" className="text-sm font-medium">Reduced Motion</Label>
              <p className="text-xs text-muted-foreground">
                Simplify animations for accessibility
              </p>
            </div>
            <Switch
              id="reduced-motion"
              checked={enableReducedMotion}
              onCheckedChange={setEnableReducedMotion}
              disabled={!enableAnimations}
            />
          </div>
          
          <AnimatedButton 
            variant="outline" 
            size="sm" 
            onClick={resetToDefaults}
            animationVariant="buttonTap"
          >
            Reset to Defaults
          </AnimatedButton>
        </div>
      </AnimatedCardContent>
    </AnimatedCard>
  );
}
