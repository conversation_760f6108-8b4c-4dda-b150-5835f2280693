"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedLoadingDotsProps extends React.HTMLAttributes<HTMLDivElement> {
  animationVariant?: keyof typeof VARIANTS;
  size?: "sm" | "md" | "lg";
  color?: string;
  count?: number;
}

/**
 * Animated loading dots component
 */
export function AnimatedLoadingDots({
  className,
  animationVariant = "pulse",
  size = "md",
  color = "primary",
  count = 3,
  ...props
}: AnimatedLoadingDotsProps) {
  const dotAnimation = useAnimationVariant(animationVariant);
  
  const sizeClasses = {
    sm: "w-1 h-1",
    md: "w-2 h-2",
    lg: "w-3 h-3",
  };
  
  return (
    <div 
      className={cn("flex items-center space-x-1", className)}
      {...props}
    >
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className={cn(
            "rounded-full",
            sizeClasses[size],
            `bg-${color}`
          )}
          initial="initial"
          animate="animate"
          exit="exit"
          {...dotAnimation}
          transition={{
            ...dotAnimation.transition,
            delay: i * 0.15,
          }}
        />
      ))}
    </div>
  );
}
