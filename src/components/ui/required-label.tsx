"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { FormLabel } from "@/components/ui/form";

interface RequiredLabelProps extends React.ComponentPropsWithoutRef<typeof FormLabel> {
  required?: boolean;
}

/**
 * A form label component that automatically adds an asterisk for required fields
 */
export function RequiredLabel({
  children,
  required = true,
  className,
  ...props
}: RequiredLabelProps) {
  return (
    <FormLabel className={cn(className)} {...props}>
      {children}
      {required && <span className="text-destructive ml-1">*</span>}
    </FormLabel>
  );
}
