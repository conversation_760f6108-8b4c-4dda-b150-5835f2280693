"use client";

import * as React from "react";
import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface BreadcrumbItem {
  label: string;
  href: string;
  isCurrent?: boolean;
}

interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
  children?: React.ReactNode;
}

export function PageHeader({
  title,
  description,
  className,
  children,
}: PageHeaderProps) {
  return (
    <div className={cn("flex flex-col space-y-2", className)}>
      <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
      {description && (
        <p className="text-muted-foreground">{description}</p>
      )}
      {children}
    </div>
  );
}

interface PageHeaderWithActionsProps extends PageHeaderProps {
  actions?: React.ReactNode;
}

export function PageHeaderWithActions({
  title,
  description,
  className,
  children,
  actions,
}: PageHeaderWithActionsProps) {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      <PageHeader title={title} description={description} className="mb-0">
        {children}
      </PageHeader>
      {actions && (
        <div className="flex items-center space-x-2">
          {actions}
        </div>
      )}
    </div>
  );
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

export function Breadcrumb({
  items,
  className,
  showHome = true,
}: BreadcrumbProps) {
  return (
    <nav className={cn("flex items-center text-sm text-muted-foreground", className)}>
      {showHome && (
        <>
          <Link
            href="/"
            className="flex items-center hover:text-foreground transition-colors"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">Home</span>
          </Link>
          <ChevronRight className="h-4 w-4 mx-1" />
        </>
      )}
      {items.map((item, index) => (
        <React.Fragment key={item.href}>
          {index > 0 && <ChevronRight className="h-4 w-4 mx-1" />}
          {item.isCurrent ? (
            <span className="font-medium text-foreground">{item.label}</span>
          ) : (
            <Link
              href={item.href}
              className="hover:text-foreground transition-colors"
            >
              {item.label}
            </Link>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}

interface PageHeaderWithBreadcrumbProps extends PageHeaderProps {
  breadcrumbItems: BreadcrumbItem[];
  actions?: React.ReactNode;
  showHome?: boolean;
}

export function PageHeaderWithBreadcrumb({
  title,
  description,
  className,
  children,
  breadcrumbItems,
  actions,
  showHome = true,
}: PageHeaderWithBreadcrumbProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <Breadcrumb items={breadcrumbItems} showHome={showHome} />
      <PageHeaderWithActions
        title={title}
        description={description}
        actions={actions}
      >
        {children}
      </PageHeaderWithActions>
    </div>
  );
}
