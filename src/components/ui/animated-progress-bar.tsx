"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedProgressBarProps extends React.HTMLAttributes<HTMLDivElement> {
  animationVariant?: keyof typeof VARIANTS;
  value: number;
  max?: number;
  color?: string;
  showValue?: boolean;
  height?: string;
  animated?: boolean;
}

/**
 * Animated progress bar component
 */
export function AnimatedProgressBar({
  className,
  animationVariant = "fadeIn",
  value,
  max = 100,
  color = "primary",
  showValue = false,
  height = "h-2",
  animated = true,
  ...props
}: AnimatedProgressBarProps) {
  const progressAnimation = useAnimationVariant(animationVariant);
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));
  
  return (
    <div 
      className={cn("w-full bg-muted rounded-full overflow-hidden", height, className)}
      {...props}
    >
      <motion.div
        className={cn(`bg-${color}`, height)}
        style={{ 
          width: animated ? 0 : `${percentage}%`,
        }}
        initial={animated ? { width: 0 } : false}
        animate={animated ? { width: `${percentage}%` } : false}
        transition={{ duration: 0.5, ease: "easeOut" }}
        {...progressAnimation}
      >
        {showValue && (
          <span className="sr-only">{percentage}%</span>
        )}
      </motion.div>
      {showValue && (
        <div className="mt-1 text-xs text-right">
          {percentage.toFixed(0)}%
        </div>
      )}
    </div>
  );
}
