import React from 'react';
import { LucideProps } from 'lucide-react';

export const RupeeIcon = (props: LucideProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={props.size || 24}
      height={props.size || 24}
      viewBox="0 0 24 24"
      fill="none"
      stroke={props.color || 'currentColor'}
      strokeWidth={props.strokeWidth || 2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M6 3h12M6 8h12M6 13l8.5 8M15 8c0 2.5-2.5 4-5.5 4H6" />
    </svg>
  );
};

export default RupeeIcon;
