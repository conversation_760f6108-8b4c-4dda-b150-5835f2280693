"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

/**
 * Animated success feedback component
 */
function AnimatedSuccess({
  className,
  size = "md",
}: {
  className?: string;
  size?: "sm" | "md" | "lg";
}) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  };
  
  return (
    <motion.div
      className={cn("text-green-500", className)}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ 
        scale: [0, 1.2, 1],
        opacity: 1,
      }}
      transition={{
        duration: 0.5,
        ease: "easeOut",
      }}
    >
      <CheckCircle className={sizeClasses[size]} />
    </motion.div>
  );
}

/**
 * Animated error feedback component
 */
function AnimatedError({
  className,
  size = "md",
}: {
  className?: string;
  size?: "sm" | "md" | "lg";
}) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  };
  
  return (
    <motion.div
      className={cn("text-red-500", className)}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ 
        scale: [0, 1.2, 1],
        opacity: 1,
        rotate: [0, -10, 10, -10, 10, 0],
      }}
      transition={{
        duration: 0.5,
        ease: "easeOut",
      }}
    >
      <XCircle className={sizeClasses[size]} />
    </motion.div>
  );
}

/**
 * Animated toast notifications with enhanced animations
 */
export function showAnimatedToast(
  type: "success" | "error" | "warning" | "info",
  title: string,
  description?: string,
  duration = 5000
) {
  const icons = {
    success: <AnimatedSuccess />,
    error: <AnimatedError />,
    warning: <AlertCircle className="text-yellow-500 w-6 h-6" />,
    info: <Info className="text-blue-500 w-6 h-6" />,
  };
  
  toast[type](title, {
    description,
    icon: icons[type],
    duration,
    className: "group transform-gpu animate-in slide-in-from-top-5 fade-in",
  });
}

/**
 * Animated feedback message component
 */
function AnimatedFeedback({
  type = "info",
  message,
  visible = true,
  className,
  onClose,
}: {
  type?: "success" | "error" | "warning" | "info";
  message: string;
  visible?: boolean;
  className?: string;
  onClose?: () => void;
}) {
  const feedbackAnimation = useAnimationVariant("slideInFromTop");
  
  const bgColors = {
    success: "bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800",
    error: "bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800",
    warning: "bg-yellow-50 dark:bg-yellow-950/30 border-yellow-200 dark:border-yellow-800",
    info: "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800",
  };
  
  const textColors = {
    success: "text-green-700 dark:text-green-300",
    error: "text-red-700 dark:text-red-300",
    warning: "text-yellow-700 dark:text-yellow-300",
    info: "text-blue-700 dark:text-blue-300",
  };
  
  const icons = {
    success: <CheckCircle className="w-5 h-5" />,
    error: <XCircle className="w-5 h-5" />,
    warning: <AlertCircle className="w-5 h-5" />,
    info: <Info className="w-5 h-5" />,
  };
  
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className={cn(
            "flex items-center gap-3 rounded-md border px-4 py-3",
            bgColors[type],
            textColors[type],
            className
          )}
          initial="initial"
          animate="animate"
          exit="exit"
          {...feedbackAnimation}
        >
          {icons[type]}
          <span>{message}</span>
          {onClose && (
            <button
              onClick={onClose}
              className="ml-auto rounded-full p-1 hover:bg-black/5 dark:hover:bg-white/5"
            >
              <XCircle className="w-4 h-4" />
            </button>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export { AnimatedSuccess, AnimatedError, AnimatedFeedback };
