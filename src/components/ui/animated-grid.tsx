"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedGridProps {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  keyExtractor: (item: any, index: number) => string;
  className?: string;
  itemClassName?: string;
  animationVariant?: keyof typeof VARIANTS;
  staggerDelay?: number;
  initialDelay?: number;
  gridCols?: string;
  gap?: string;
  onItemClick?: (item: any, index: number) => void;
}

/**
 * Animated grid component that renders a grid of items with animations
 */
export function AnimatedGrid({
  items,
  renderItem,
  keyExtractor,
  className,
  itemClassName,
  animationVariant = "gridItem",
  staggerDelay = 0.05,
  initialDelay = 0.1,
  gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  gap = "gap-4",
  onItemClick,
}: AnimatedGridProps) {
  const itemAnimation = useAnimationVariant(animationVariant);

  // Get the staggerContainer variant and customize it based on our specific delays
  const baseContainerAnimation = useAnimationVariant("staggerContainer");
  const containerAnimation = {
    ...baseContainerAnimation,
    animate: {
      ...baseContainerAnimation.animate,
      transition: {
        ...baseContainerAnimation.animate?.transition,
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
    exit: {
      ...baseContainerAnimation.exit,
      transition: {
        ...baseContainerAnimation.exit?.transition,
        staggerChildren: 0.03,
        staggerDirection: -1,
      },
    },
  };

  return (
    <motion.div
      className={cn("grid", gridCols, gap, className)}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={containerAnimation}
    >
      {items.map((item, index) => (
        <motion.div
          key={keyExtractor(item, index)}
          className={cn(itemClassName)}
          variants={itemAnimation}
          onClick={onItemClick ? () => onItemClick(item, index) : undefined}
          whileHover={onItemClick ? { scale: 1.02 } : undefined}
          whileTap={onItemClick ? { scale: 0.98 } : undefined}
        >
          {renderItem(item, index)}
        </motion.div>
      ))}
    </motion.div>
  );
}
