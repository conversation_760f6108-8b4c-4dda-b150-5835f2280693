"use client";

import * as React from "react";
import { motion } from "framer-motion";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedTabsProps extends React.ComponentProps<typeof Tabs> {
  tabAnimation?: keyof typeof VARIANTS;
  contentAnimation?: keyof typeof VARIANTS;
  disableAnimation?: boolean;
}

/**
 * Animated tabs component with smooth transitions between tab content
 */
const AnimatedTabs = React.forwardRef<HTMLDivElement, AnimatedTabsProps>(
  ({ 
    className, 
    tabAnimation = "buttonTap", 
    contentAnimation = "fadeIn", 
    disableAnimation = false,
    children,
    ...props 
  }, ref) => {
    // If animations are disabled, render the regular tabs
    if (disableAnimation) {
      return <Tabs ref={ref} className={className} {...props}>{children}</Tabs>;
    }
    
    // Process children to add animations
    const processedChildren = React.Children.map(children, (child) => {
      if (!React.isValidElement(child)) return child;
      
      // Handle TabsList
      if (child.type === TabsList) {
        const tabsListChildren = React.Children.map(child.props.children, (tabChild) => {
          if (!React.isValidElement(tabChild) || tabChild.type !== TabsTrigger) return tabChild;
          
          // Create animated tab trigger
          const tabTriggerAnimation = useAnimationVariant(tabAnimation);
          
          return React.cloneElement(tabChild, {
            asChild: true,
            children: (
              <motion.button
                whileTap="whileTap"
                whileHover="whileHover"
                {...tabTriggerAnimation}
              >
                {tabChild.props.children}
              </motion.button>
            ),
          });
        });
        
        return React.cloneElement(child, {
          children: tabsListChildren,
        });
      }
      
      // Handle TabsContent
      if (child.type === TabsContent) {
        const contentAnimationVariant = useAnimationVariant(contentAnimation);
        
        return React.cloneElement(child, {
          asChild: true,
          children: (
            <motion.div
              initial="initial"
              animate="animate"
              exit="exit"
              {...contentAnimationVariant}
            >
              {child.props.children}
            </motion.div>
          ),
        });
      }
      
      return child;
    });
    
    return (
      <Tabs ref={ref} className={className} {...props}>
        {processedChildren}
      </Tabs>
    );
  }
);

AnimatedTabs.displayName = "AnimatedTabs";

/**
 * Animated tab indicator component for custom tab implementations
 */
function AnimatedTabIndicator({
  activeIndex,
  itemCount,
  className,
}: {
  activeIndex: number;
  itemCount: number;
  className?: string;
}) {
  return (
    <div className={cn("relative", className)}>
      <motion.div
        className="absolute bottom-0 h-0.5 bg-primary rounded-full"
        initial={false}
        animate={{
          left: `${(activeIndex / itemCount) * 100}%`,
          width: `${(1 / itemCount) * 100}%`,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30,
        }}
      />
    </div>
  );
}

export { AnimatedTabs, AnimatedTabIndicator };
