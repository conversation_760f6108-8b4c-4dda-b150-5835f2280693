"use client";

import { toast } from "@/components/ui/use-toast";
import { CheckCircle, AlertCircle, Info } from "lucide-react";

type ToastType = "success" | "error" | "info" | "warning";

interface AnimatedToastOptions {
  title?: string;
  description: string;
  type?: ToastType;
  duration?: number;
}

export function showAnimatedToast({
  title,
  description,
  type = "info",
  duration = 5000,
}: AnimatedToastOptions) {
  const icons = {
    success: <CheckCircle className="h-5 w-5 text-green-500" />,
    error: <AlertCircle className="h-5 w-5 text-red-500" />,
    warning: <AlertCircle className="h-5 w-5 text-yellow-500" />,
    info: <Info className="h-5 w-5 text-blue-500" />,
  };

  const defaultTitles = {
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Information",
  };

  toast({
    title: title || defaultTitles[type],
    description,
    duration,
    variant: "default",
    icon: icons[type],
  });
}
