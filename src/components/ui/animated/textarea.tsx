"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { Textarea } from "@/components/ui/textarea";
import { VARIANTS } from "@/lib/animations";

interface AnimatedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedTextarea({
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedTextareaProps) {
  const textareaAnimation = useAnimationVariant(animationVariant);
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  return (
    <motion.div
      className="w-full"
      initial="initial"
      animate="animate"
      exit="exit"
      whileFocus="whileFocus"
      {...textareaAnimation}
    >
      <Textarea className={cn("", className)} ref={textareaRef} {...props} />
    </motion.div>
  );
}

// Export the original textarea component for convenience
export { Textarea } from "@/components/ui/textarea";
