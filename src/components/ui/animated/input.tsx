"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { useFocusPreservation } from "@/hooks/use-focus-preservation";
import { getFormAnimationProps } from "@/lib/animation-optimizations";
import { Input } from "@/components/ui/input";
import { VARIANTS } from "@/lib/animations";

interface AnimatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  animationVariant?: keyof typeof VARIANTS;
}

/**
 * A simplified version of AnimatedInput that strictly follows React's rules of hooks
 * by ensuring all hooks are called in the same order on every render.
 */
export const AnimatedInput = React.forwardRef<
  HTMLInputElement,
  AnimatedInputProps
>(({ className, animationVariant = "fadeIn", onFocus, onBlur, ...props }, forwardedRef) => {
  // State hooks - all state hooks must be called first and unconditionally
  const [hasValue, setHasValue] = React.useState(Boolean(props.value));
  const [isTyping, setIsTyping] = React.useState(false);

  // Custom hooks
  const inputAnimation = useAnimationVariant(animationVariant);
  const { focusProps, isFocused, updateSelectionState, restoreFocus } = useFocusPreservation<HTMLInputElement>();

  // Effect hooks
  React.useEffect(() => {
    setHasValue(Boolean(props.value));
    if (isFocused) {
      // Update selection state after value changes
      setTimeout(updateSelectionState, 0);
    }
  }, [props.value, isFocused, updateSelectionState]);

  // Effect to restore focus after re-renders
  React.useEffect(() => {
    if (isFocused) {
      // Restore focus after any re-render when the input should be focused
      requestAnimationFrame(() => {
        restoreFocus();
      });
    }
  }, [isFocused, restoreFocus, isTyping]); // Add isTyping as a dependency to restore focus when typing state changes

  // Effect for tracking typing activity
  React.useEffect(() => {
    let typingTimer: NodeJS.Timeout;
    if (isFocused && hasValue) {
      setIsTyping(true);
      typingTimer = setTimeout(() => {
        setIsTyping(false);
      }, 1000);
    } else if (!isFocused) {
      setIsTyping(false);
    }
    return () => {
      if (typingTimer) clearTimeout(typingTimer);
    };
  }, [isFocused, hasValue, props.value]);

  // Callback hooks
  const handleRef = React.useCallback(
    (element: HTMLInputElement | null) => {
      if (focusProps.ref.current !== element) {
        focusProps.ref.current = element;
      }
      if (typeof forwardedRef === 'function') {
        forwardedRef(element);
      } else if (forwardedRef) {
        forwardedRef.current = element;
      }
    },
    [forwardedRef, focusProps.ref]
  );

  const handleFocus = React.useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      focusProps.onFocus(e);
      if (onFocus) onFocus(e);
    },
    [focusProps.onFocus, onFocus]
  );

  const handleBlur = React.useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      focusProps.onBlur();
      if (onBlur) onBlur(e);
    },
    [focusProps.onBlur, onBlur]
  );

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setIsTyping(true);
      if (props.onChange) props.onChange(e);
      // Update selection state after change
      setTimeout(updateSelectionState, 0);
    },
    [props.onChange, updateSelectionState]
  );

  // Common input props
  const inputProps = {
    className: cn("", className),
    ref: handleRef,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onChange: handleChange,
    "data-focused": isFocused,
    "data-typing": isTyping,
    ...props
  };

  // Form animation props - calculate these regardless of typing state
  const formAnimationProps = getFormAnimationProps(isFocused);

  // Combined animation props - calculate these regardless of typing state
  const animationProps = {
    ...inputAnimation,
    ...formAnimationProps,
    transition: {
      ...inputAnimation.transition,
      ...formAnimationProps.animate.transition,
    }
  };

  // Always use the same container structure to maintain focus
  // Just disable animations when typing
  return (
    <motion.div
      className="w-full"
      initial="initial"
      animate={isFocused ? { opacity: 1 } : "animate"}
      exit="exit"
      // Only apply animation props when not typing
      {...(isTyping ? {} : animationProps)}
      // Disable layout animations when focused or typing
      layout={(isFocused || isTyping) ? false : "position"}
      // Disable all animations when typing
      transition={isTyping ? { duration: 0 } : undefined}
    >
      <Input {...inputProps} />
    </motion.div>
  );
});

AnimatedInput.displayName = "AnimatedInput";
