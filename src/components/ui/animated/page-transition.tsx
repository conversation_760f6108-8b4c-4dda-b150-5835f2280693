"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  animationVariant?: keyof typeof VARIANTS;
}

export function PageTransition({
  children,
  className,
  animationVariant = "fadeIn",
}: PageTransitionProps) {
  const pageAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("w-full", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...pageAnimation}
    >
      {children}
    </motion.div>
  );
}

interface StaggeredListProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  initialDelay?: number;
}

export function StaggeredList({
  children,
  className,
  staggerDelay = 0.05,
  initialDelay = 0.1,
}: StaggeredListProps) {
  const containerAnimation = {
    animate: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
  };

  return (
    <motion.div
      className={cn("w-full", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...containerAnimation}
    >
      {children}
    </motion.div>
  );
}
