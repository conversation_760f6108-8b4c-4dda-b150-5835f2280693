"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

import { VARIANTS } from "@/lib/animations";

interface AnimatedTabsProps extends React.ComponentProps<typeof Tabs> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedTabsListProps extends React.ComponentProps<typeof TabsList> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedTabsTriggerProps extends React.ComponentProps<typeof TabsTrigger> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedTabsContentProps extends React.ComponentProps<typeof TabsContent> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedTabs({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedTabsProps) {
  const tabsAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...tabsAnimation}
    >
      <Tabs className={className} {...props}>
        {children}
      </Tabs>
    </motion.div>
  );
}

export function AnimatedTabsList({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedTabsListProps) {
  const tabsListAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...tabsListAnimation}
    >
      <TabsList className={className} {...props}>
        {children}
      </TabsList>
    </motion.div>
  );
}

export function AnimatedTabsTrigger({
  children,
  className,
  animationVariant = "buttonTap",
  ...props
}: AnimatedTabsTriggerProps) {
  const tabsTriggerAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      whileTap="whileTap"
      {...tabsTriggerAnimation}
    >
      <TabsTrigger className={className} {...props}>
        {children}
      </TabsTrigger>
    </motion.div>
  );
}

export function AnimatedTabsContent({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedTabsContentProps) {
  const tabsContentAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...tabsContentAnimation}
    >
      <TabsContent className={className} {...props}>
        {children}
      </TabsContent>
    </motion.div>
  );
}

// Export the original tabs components for convenience
export {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
}
