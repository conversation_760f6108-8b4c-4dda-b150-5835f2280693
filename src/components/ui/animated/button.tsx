"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { Button } from "@/components/ui/button";
import { VARIANTS } from "@/lib/animations";

interface AnimatedButtonProps extends React.ComponentProps<typeof Button> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedButton({
  children,
  className,
  animationVariant = "buttonTap",
  ...props
}: AnimatedButtonProps) {
  const buttonAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      whileTap="whileTap"
      {...buttonAnimation}
    >
      <Button className={className} {...props}>
        {children}
      </Button>
    </motion.div>
  );
}
