"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { VARIANTS } from "@/lib/animations";

interface AnimatedCardProps extends React.ComponentProps<typeof Card> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedCardHeaderProps extends React.ComponentProps<typeof CardHeader> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedCardFooterProps extends React.ComponentProps<typeof CardFooter> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedCardTitleProps extends React.ComponentProps<typeof CardTitle> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedCardDescriptionProps extends React.ComponentProps<typeof CardDescription> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedCardContentProps extends React.ComponentProps<typeof CardContent> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedCard({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedCardProps) {
  const cardAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...cardAnimation}
    >
      <Card className={className} {...props}>
        {children}
      </Card>
    </motion.div>
  );
}

export function AnimatedCardHeader({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedCardHeaderProps) {
  const headerAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...headerAnimation}
    >
      <CardHeader className={className} {...props}>
        {children}
      </CardHeader>
    </motion.div>
  );
}

export function AnimatedCardFooter({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedCardFooterProps) {
  const footerAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...footerAnimation}
    >
      <CardFooter className={className} {...props}>
        {children}
      </CardFooter>
    </motion.div>
  );
}

export function AnimatedCardTitle({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedCardTitleProps) {
  const titleAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...titleAnimation}
    >
      <CardTitle className={className} {...props}>
        {children}
      </CardTitle>
    </motion.div>
  );
}

export function AnimatedCardDescription({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedCardDescriptionProps) {
  const descriptionAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...descriptionAnimation}
    >
      <CardDescription className={className} {...props}>
        {children}
      </CardDescription>
    </motion.div>
  );
}

export function AnimatedCardContent({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedCardContentProps) {
  const contentAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...contentAnimation}
    >
      <CardContent className={className} {...props}>
        {children}
      </CardContent>
    </motion.div>
  );
}
