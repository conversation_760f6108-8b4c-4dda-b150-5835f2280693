"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";
import { cn } from "@/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarInset,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar";

// Animated Sidebar Provider
interface AnimatedSidebarProviderProps extends React.ComponentProps<typeof SidebarProvider> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebarProvider({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSidebarProviderProps) {
  return (
    <SidebarProvider className={className} {...props}>
      {children}
    </SidebarProvider>
  );
}

// Animated Sidebar
interface AnimatedSidebarProps extends React.ComponentProps<typeof Sidebar> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebar({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSidebarProps) {
  const sidebarAnimation = useAnimationVariant(animationVariant);

  return (
    <Sidebar className={className} {...props}>
      <motion.div
        className="w-full h-full"
        initial="initial"
        animate="animate"
        exit="exit"
        {...sidebarAnimation}
      >
        {children}
      </motion.div>
    </Sidebar>
  );
}

// Animated Sidebar Header
interface AnimatedSidebarHeaderProps extends React.ComponentProps<typeof SidebarHeader> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebarHeader({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSidebarHeaderProps) {
  const headerAnimation = useAnimationVariant(animationVariant);

  return (
    <SidebarHeader className={className} {...props}>
      <motion.div
        className="w-full"
        initial="initial"
        animate="animate"
        exit="exit"
        {...headerAnimation}
      >
        {children}
      </motion.div>
    </SidebarHeader>
  );
}

// Animated Sidebar Content
interface AnimatedSidebarContentProps extends React.ComponentProps<typeof SidebarContent> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebarContent({
  children,
  className,
  animationVariant = "staggerContainer",
  ...props
}: AnimatedSidebarContentProps) {
  const contentAnimation = useAnimationVariant(animationVariant);

  return (
    <SidebarContent className={className} {...props}>
      <motion.div
        className="w-full"
        initial="initial"
        animate="animate"
        exit="exit"
        {...contentAnimation}
      >
        {children}
      </motion.div>
    </SidebarContent>
  );
}

// Animated Sidebar Menu
interface AnimatedSidebarMenuProps extends React.ComponentProps<typeof SidebarMenu> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebarMenu({
  children,
  className,
  animationVariant = "staggerContainer",
  ...props
}: AnimatedSidebarMenuProps) {
  const menuAnimation = useAnimationVariant(animationVariant);

  return (
    <SidebarMenu className={className} {...props}>
      <motion.div
        className="w-full"
        initial="initial"
        animate="animate"
        exit="exit"
        {...menuAnimation}
      >
        {children}
      </motion.div>
    </SidebarMenu>
  );
}

// Animated Sidebar Menu Item
interface AnimatedSidebarMenuItemProps extends React.ComponentProps<typeof SidebarMenuItem> {
  animationVariant?: keyof typeof VARIANTS;
  active?: boolean;
}

export function AnimatedSidebarMenuItem({
  children,
  className,
  animationVariant = "listItem",
  active = false,
  ...props
}: AnimatedSidebarMenuItemProps) {
  const itemAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className="w-full"
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      whileTap="whileTap"
      {...itemAnimation}
    >
      <SidebarMenuItem
        className={cn(
          active && "bg-accent text-accent-foreground",
          className
        )}
        {...props}
      >
        {children}
      </SidebarMenuItem>
    </motion.div>
  );
}

// Animated Sidebar Menu Button
interface AnimatedSidebarMenuButtonProps extends React.ComponentProps<typeof SidebarMenuButton> {
  animationVariant?: keyof typeof VARIANTS;
  active?: boolean;
}

export function AnimatedSidebarMenuButton({
  children,
  className,
  animationVariant = "buttonTap",
  active = false,
  ...props
}: AnimatedSidebarMenuButtonProps) {
  const buttonAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className="w-full"
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      whileTap="whileTap"
      {...buttonAnimation}
    >
      <SidebarMenuButton
        className={cn(
          active && "bg-accent text-accent-foreground",
          className
        )}
        {...props}
      >
        {children}
      </SidebarMenuButton>
    </motion.div>
  );
}

// Animated Sidebar Footer
interface AnimatedSidebarFooterProps extends React.ComponentProps<typeof SidebarFooter> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebarFooter({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSidebarFooterProps) {
  const footerAnimation = useAnimationVariant(animationVariant);

  return (
    <SidebarFooter className={className} {...props}>
      <motion.div
        className="w-full"
        initial="initial"
        animate="animate"
        exit="exit"
        {...footerAnimation}
      >
        {children}
      </motion.div>
    </SidebarFooter>
  );
}

// Animated Sidebar Trigger
interface AnimatedSidebarTriggerProps extends React.ComponentProps<typeof SidebarTrigger> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSidebarTrigger({
  className,
  animationVariant = "buttonTap",
  ...props
}: AnimatedSidebarTriggerProps) {
  const triggerAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className="inline-flex"
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      whileTap="whileTap"
      {...triggerAnimation}
    >
      <SidebarTrigger className={className} {...props} />
    </motion.div>
  );
}

// Export all components
export {
  useSidebar,
  SidebarRail,
  SidebarSeparator,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarGroupAction,
  SidebarInput,
  SidebarInset,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
};
