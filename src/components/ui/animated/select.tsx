"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { VARIANTS } from "@/lib/animations";

interface AnimatedSelectProps extends React.ComponentProps<typeof Select> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedSelectTriggerProps extends React.ComponentProps<typeof SelectTrigger> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedSelectContentProps extends React.ComponentProps<typeof SelectContent> {
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedSelectItemProps extends React.ComponentProps<typeof SelectItem> {
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedSelect({
  children,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSelectProps) {
  const selectAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className="w-full"
      initial="initial"
      animate="animate"
      exit="exit"
      {...selectAnimation}
    >
      <Select {...props}>{children}</Select>
    </motion.div>
  );
}

export function AnimatedSelectTrigger({
  children,
  className,
  animationVariant = "buttonTap",
  ...props
}: AnimatedSelectTriggerProps) {
  const triggerAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      whileTap="whileTap"
      {...triggerAnimation}
    >
      <SelectTrigger className={className} {...props}>
        {children}
      </SelectTrigger>
    </motion.div>
  );
}

export function AnimatedSelectContent({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSelectContentProps) {
  const contentAnimation = useAnimationVariant(animationVariant);

  return (
    <SelectContent
      className={className}
      {...props}
      // Apply animation through CSS classes instead of motion.div
      // because SelectContent is a portal component
      style={{
        animation: "fadeIn 0.2s ease-out",
      }}
    >
      {children}
    </SelectContent>
  );
}

export function AnimatedSelectItem({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedSelectItemProps) {
  const itemAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="whileHover"
      {...itemAnimation}
    >
      <SelectItem className={className} {...props}>
        {children}
      </SelectItem>
    </motion.div>
  );
}

// Export the original select components for convenience
export {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
};
