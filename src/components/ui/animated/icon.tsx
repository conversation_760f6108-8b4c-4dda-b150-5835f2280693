"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedIconProps {
  icon: React.ReactNode;
  className?: string;
  animationVariant?: keyof typeof VARIANTS;
  interactive?: boolean;
  onClick?: () => void;
  size?: "sm" | "md" | "lg" | "xl";
  color?: string;
}

/**
 * Animated icon component that adds animations to any icon
 */
export function AnimatedIcon({
  icon,
  className,
  animationVariant = "buttonTap",
  interactive = true,
  onClick,
  size = "md",
  color,
}: AnimatedIconProps) {
  const iconAnimation = useAnimationVariant(animationVariant);
  
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-10 h-10",
  };
  
  return (
    <motion.div
      className={cn(
        sizeClasses[size],
        interactive && "cursor-pointer",
        color && `text-${color}`,
        className
      )}
      initial="initial"
      whileHover={interactive ? "whileHover" : undefined}
      whileTap={interactive ? "whileTap" : undefined}
      animate="animate"
      exit="exit"
      onClick={onClick}
      {...iconAnimation}
    >
      {icon}
    </motion.div>
  );
}

/**
 * Animated notification bell with a bounce effect
 */
export function AnimatedNotificationBell({
  icon,
  hasNotifications = false,
  onClick,
  className,
}: {
  icon: React.ReactNode;
  hasNotifications?: boolean;
  onClick?: () => void;
  className?: string;
}) {
  const bellAnimation = {
    initial: { rotate: 0 },
    animate: hasNotifications ? {
      rotate: [0, 15, -15, 10, -10, 5, -5, 0],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatDelay: 5,
      }
    } : {},
    whileHover: {
      scale: 1.1,
      transition: { duration: 0.2 }
    },
    whileTap: {
      scale: 0.9,
      transition: { duration: 0.1 }
    },
  };
  
  return (
    <motion.div
      className={cn("relative cursor-pointer", className)}
      initial="initial"
      animate="animate"
      whileHover="whileHover"
      whileTap="whileTap"
      onClick={onClick}
      {...bellAnimation}
    >
      {icon}
      {hasNotifications && (
        <motion.div
          className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
        />
      )}
    </motion.div>
  );
}
