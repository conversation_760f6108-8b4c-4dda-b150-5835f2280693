"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
  useFormField,
} from "@/components/ui/form";
import { FormProvider } from "react-hook-form";
import { VARIANTS } from "@/lib/animations";

interface AnimatedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedFormItemProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedFormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  children: React.ReactNode;
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedFormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  animationVariant?: keyof typeof VARIANTS;
}

interface AnimatedFormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  animationVariant?: keyof typeof VARIANTS;
}

export function AnimatedForm({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedFormProps) {
  const formAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...formAnimation}
    >
      <form className={cn("space-y-4", className)} {...props}>
        {children}
      </form>
    </motion.div>
  );
}

export function AnimatedFormItem({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedFormItemProps) {
  const itemAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...itemAnimation}
    >
      <FormItem className={className} {...props}>
        {children}
      </FormItem>
    </motion.div>
  );
}

export function AnimatedFormLabel({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedFormLabelProps) {
  const labelAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...labelAnimation}
    >
      <FormLabel className={className} {...props}>
        {children}
      </FormLabel>
    </motion.div>
  );
}

export function AnimatedFormDescription({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedFormDescriptionProps) {
  const descriptionAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...descriptionAnimation}
    >
      <FormDescription className={className} {...props}>
        {children}
      </FormDescription>
    </motion.div>
  );
}

export function AnimatedFormMessage({
  children,
  className,
  animationVariant = "fadeIn",
  ...props
}: AnimatedFormMessageProps) {
  const messageAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...messageAnimation}
    >
      <FormMessage className={className} {...props}>
        {children}
      </FormMessage>
    </motion.div>
  );
}

// Export the original form components for convenience
export {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
  useFormField,
};
