"use client";

// Import components from sidebar.tsx
import {
  SidebarInset,
  useSidebar,
} from "@/components/ui/sidebar";

// Import components from icon.tsx
import { AnimatedIcon } from "./icon";

// Import Form component
import { Form } from "@/components/ui/form";

// Import Button component
import { Button } from "@/components/ui/button";

// Create a PageTransition component with animations
import React from "react";
import { motion } from "framer-motion";

const PageTransition = ({ children }) => {
  const variants = {
    hidden: { opacity: 0, x: 0, y: 20 },
    enter: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, x: 0, y: 20 }
  };

  return (
    <motion.div
      initial="hidden"
      animate="enter"
      exit="exit"
      variants={variants}
      transition={{ duration: 0.3, type: 'easeInOut' }}
      className="w-full"
    >
      {children}
    </motion.div>
  );
};

// Create a simple AnimatedButton component
const AnimatedButton = (props) => {
  return <Button {...props} />;
};

// Create placeholder components for sidebar
const AnimatedSidebarProvider = ({ children }) => {
  return <>{children}</>;
};

const AnimatedSidebar = ({ children }) => {
  return <div>{children}</div>;
};

const AnimatedSidebarHeader = ({ children, className }) => {
  return <div className={className}>{children}</div>;
};

const AnimatedSidebarContent = ({ children, className }) => {
  return <div className={className}>{children}</div>;
};

const AnimatedSidebarMenu = ({ children, className }) => {
  return <div className={className}>{children}</div>;
};

const AnimatedSidebarMenuItem = ({ children, className, active }) => {
  return <div className={`${className} ${active ? "bg-accent" : ""}`}>{children}</div>;
};

const AnimatedSidebarFooter = ({ children, className }) => {
  return <div className={className}>{children}</div>;
};

const AnimatedSidebarTrigger = ({ className }) => {
  return <button className={className}>Toggle</button>;
};

// Export all components
export {
  // Sidebar components
  AnimatedSidebarProvider,
  AnimatedSidebar,
  AnimatedSidebarHeader,
  AnimatedSidebarContent,
  AnimatedSidebarMenu,
  AnimatedSidebarMenuItem,
  AnimatedSidebarFooter,
  AnimatedSidebarTrigger,
  useSidebar,
  SidebarInset,

  // Icon components
  AnimatedIcon,

  // Page transition components
  PageTransition,

  // Button components
  AnimatedButton,

  // Form components
  Form,
};
