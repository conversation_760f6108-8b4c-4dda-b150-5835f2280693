"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";

interface AnimatedSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg";
  animationVariant?: string;
}

export function AnimatedSpinner({
  className,
  size = "md",
  animationVariant = "spin",
  ...props
}: AnimatedSpinnerProps) {
  const spinnerAnimation = useAnimationVariant(animationVariant);
  
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
  };

  return (
    <motion.div
      className={cn(
        "animate-spin rounded-full border-2 border-primary border-t-transparent",
        sizeClasses[size],
        className
      )}
      initial="initial"
      animate="animate"
      exit="exit"
      {...spinnerAnimation}
      {...props}
    />
  );
}
