"use client";

import React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";
import { cn } from "@/lib/utils";

interface PageHeaderWithBreadcrumbProps {
  title: string;
  description?: string;
  breadcrumbItems?: {
    label: string;
    href: string;
    isCurrent?: boolean;
  }[];
  children?: React.ReactNode;
  className?: string;
}

export function PageHeaderWithBreadcrumb({
  title,
  description,
  breadcrumbItems,
  children,
  className,
}: PageHeaderWithBreadcrumbProps) {
  const pathname = usePathname();

  // Generate breadcrumb items if not provided
  const items = breadcrumbItems || generateBreadcrumbItems(pathname);

  return (
    <div className={cn("space-y-2 pt-4", className)}>
      <Breadcrumb className="mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />

          {items.map((item, index) => (
            <React.Fragment key={item.href}>
              <BreadcrumbItem>
                {item.isCurrent ? (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href={item.href}>{item.label}</BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {index < items.length - 1 && <BreadcrumbSeparator />}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col space-y-1.5">
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
        {children}
      </div>
    </div>
  );
}

// Helper function to generate breadcrumb items from pathname
function generateBreadcrumbItems(pathname: string) {
  const segments = pathname.split('/').filter(Boolean);

  // Remove 'dashboard' from segments as it's already included in the base
  if (segments[0] === 'dashboard') {
    segments.shift();
  }

  if (segments.length === 0) {
    return [];
  }

  return segments.map((segment, index) => {
    const href = `/dashboard/${segments.slice(0, index + 1).join('/')}`;
    const label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
    const isCurrent = index === segments.length - 1;

    return { label, href, isCurrent };
  });
}
