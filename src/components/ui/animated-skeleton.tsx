"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedSkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  animationVariant?: keyof typeof VARIANTS;
  width?: string | number;
  height?: string | number;
}

/**
 * Animated skeleton component for loading states
 */
export function AnimatedSkeleton({
  className,
  animationVariant = "pulse",
  width,
  height,
  ...props
}: AnimatedSkeletonProps) {
  const skeletonAnimation = useAnimationVariant(animationVariant);
  
  const style: React.CSSProperties = {
    width: width,
    height: height,
  };
  
  return (
    <motion.div
      className={cn("", className)}
      initial="initial"
      animate="animate"
      exit="exit"
      {...skeletonAnimation}
      style={style}
    >
      <Skeleton className={cn("w-full h-full", className)} {...props} />
    </motion.div>
  );
}

// Export the original skeleton component for convenience
export { Skeleton } from "@/components/ui/skeleton";
