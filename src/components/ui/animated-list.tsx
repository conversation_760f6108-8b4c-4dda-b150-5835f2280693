"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedListProps {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  keyExtractor: (item: any, index: number) => string;
  className?: string;
  itemClassName?: string;
  animationVariant?: keyof typeof VARIANTS;
  staggerDelay?: number;
  initialDelay?: number;
  onItemClick?: (item: any, index: number) => void;
}

/**
 * Animated list component that renders a list of items with animations
 */
export function AnimatedList({
  items,
  renderItem,
  keyExtractor,
  className,
  itemClassName,
  animationVariant = "listItem",
  staggerDelay = 0.05,
  initialDelay = 0.1,
  onItemClick,
}: AnimatedListProps) {
  const itemAnimation = useAnimationVariant(animationVariant);

  // Get the staggerContainer variant and customize it based on our specific delays
  const baseContainerAnimation = useAnimationVariant("staggerContainer");
  const containerAnimation = {
    ...baseContainerAnimation,
    animate: {
      ...baseContainerAnimation.animate,
      transition: {
        ...baseContainerAnimation.animate?.transition,
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
    exit: {
      ...baseContainerAnimation.exit,
      transition: {
        ...baseContainerAnimation.exit?.transition,
        staggerChildren: 0.03,
        staggerDirection: -1,
      },
    },
  };

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      exit="exit"
      {...containerAnimation}
    >
      <AnimatePresence>
        {items.map((item, index) => (
          <motion.div
            key={keyExtractor(item, index)}
            className={cn(itemClassName, onItemClick && "cursor-pointer")}
            initial="initial"
            animate="animate"
            exit="exit"
            whileHover={onItemClick ? "whileHover" : undefined}
            whileTap={onItemClick ? "whileTap" : undefined}
            onClick={onItemClick ? () => onItemClick(item, index) : undefined}
            layout
            {...itemAnimation}
          >
            {renderItem(item, index)}
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
}

/**
 * Animated grid component that renders a grid of items with animations
 */
export function AnimatedGrid({
  items,
  renderItem,
  keyExtractor,
  className,
  itemClassName,
  animationVariant = "listItem",
  staggerDelay = 0.05,
  initialDelay = 0.1,
  onItemClick,
  gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
}: AnimatedListProps & {
  gridCols?: string;
}) {
  return (
    <AnimatedList
      items={items}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      className={cn("grid gap-4", gridCols, className)}
      itemClassName={itemClassName}
      animationVariant={animationVariant}
      staggerDelay={staggerDelay}
      initialDelay={initialDelay}
      onItemClick={onItemClick}
    />
  );
}
