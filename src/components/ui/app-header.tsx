import { ReactNode } from "react";

interface AppHeaderProps {
  title?: string;
  description?: string;
  children?: ReactNode;
}

export function AppHeader({
  title = "Carbonix",
  description = "Accelerate sustainability goals with secure, transparent carbon credit trading",
  children
}: AppHeaderProps) {
  return (
    <div className="w-full flex flex-col gap-2">
      <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {title}
      </h2>
      {description && (
        <p className="mt-2 text-center text-sm text-gray-600">
          {description}
        </p>
      )}
      {children}
    </div>
  );
}
