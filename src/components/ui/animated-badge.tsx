"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";
import { VariantProps } from "class-variance-authority";

// Define custom badge variants
const customBadgeVariants = {
  success: "bg-green-100 text-green-800 hover:bg-green-200 border-green-200",
  warning: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-200",
};

interface AnimatedBadgeProps extends React.ComponentProps<typeof Badge> {
  animationVariant?: keyof typeof VARIANTS;
  disableAnimation?: boolean;
  pulse?: boolean;
  variant?: "success" | "warning" | VariantProps<typeof badgeVariants>["variant"];
}

/**
 * Animated badge component with optional pulse effect
 */
const AnimatedBadge = React.forwardRef<HTMLDivElement, AnimatedBadgeProps>(
  ({ className, animationVariant = "fadeIn", disableAnimation = false, pulse = false, ...props }, ref) => {
    const badgeAnimation = useAnimationVariant(animationVariant);
    // Get pulse animation upfront to avoid conditional hook calls
    const pulseAnimationProps = useAnimationVariant("pulse");

    // Handle custom variants
    const { variant, ...otherProps } = props;

    // If animations are disabled, render the regular badge
    if (disableAnimation) {
      if (variant === "success" || variant === "warning") {
        const customClassName = cn(customBadgeVariants[variant], className);
        return <Badge ref={ref} className={customClassName} variant="outline" {...otherProps} />;
      }
      return <Badge ref={ref} className={className} variant={variant} {...otherProps} />;
    }

    // Create a motion badge with the specified animation
    const MotionBadge = motion.create(Badge);

    // Handle custom variants
    if (variant === "success" || variant === "warning") {
      const customClassName = cn(customBadgeVariants[variant], className);

      return (
        <MotionBadge
          ref={ref}
          className={customClassName}
          variant="outline"
          initial="initial"
          animate="animate"
          exit="exit"
          {...badgeAnimation}
          {...(pulse ? pulseAnimationProps : {})}
          {...otherProps}
        />
      );
    }

    // Standard variant
    return (
      <MotionBadge
        ref={ref}
        className={className}
        variant={variant}
        initial="initial"
        animate="animate"
        exit="exit"
        {...badgeAnimation}
        {...(pulse ? pulseAnimationProps : {})}
        {...otherProps}
      />
    );
  }
);

AnimatedBadge.displayName = "AnimatedBadge";

/**
 * Animated status indicator component
 */
function AnimatedStatusIndicator({
  status = "idle",
  size = "md",
  className,
  pulseEffect = true,
}: Readonly<{
  status?: "idle" | "loading" | "success" | "error" | "warning";
  size?: "sm" | "md" | "lg";
  className?: string;
  pulseEffect?: boolean;
}>) {
  const statusColors = {
    idle: "bg-gray-400",
    loading: "bg-blue-500",
    success: "bg-green-500",
    error: "bg-red-500",
    warning: "bg-yellow-500",
  };

  const sizeClasses = {
    sm: "w-2 h-2",
    md: "w-3 h-3",
    lg: "w-4 h-4",
  };

  // Get animation variants upfront to avoid conditional hook calls
  const loadingPulseAnimation = useAnimationVariant("loadingPulse");
  const regularPulseAnimation = useAnimationVariant("pulse");

  // Determine which animation to use
  const animationProps = pulseEffect && status !== "idle"
    ? status === "loading"
      ? loadingPulseAnimation
      : regularPulseAnimation
    : {};

  return (
    <motion.div
      className={cn(
        "rounded-full",
        statusColors[status],
        sizeClasses[size],
        className
      )}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      exit={{ scale: 0 }}
      {...animationProps}
    />
  );
}

export { AnimatedBadge, AnimatedStatusIndicator };
