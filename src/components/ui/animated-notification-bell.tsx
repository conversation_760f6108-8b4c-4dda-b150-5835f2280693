"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedNotificationBellProps extends React.HTMLAttributes<HTMLDivElement> {
  icon: React.ReactNode;
  hasNotifications?: boolean;
  count?: number;
  animationVariant?: keyof typeof VARIANTS;
  size?: "sm" | "md" | "lg" | "xl";
  onClick?: () => void;
}

/**
 * Animated notification bell component with optional notification indicator
 */
export function AnimatedNotificationBell({
  icon,
  hasNotifications = false,
  count,
  className,
  animationVariant = "notificationBell",
  size = "md",
  onClick,
  ...props
}: AnimatedNotificationBellProps) {
  const bellAnimation = useAnimationVariant(animationVariant);

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-10 h-10",
  };

  const dotSizeClasses = {
    sm: "w-2 h-2",
    md: "w-2.5 h-2.5",
    lg: "w-3 h-3",
    xl: "w-3.5 h-3.5",
  };

  const countSizeClasses = {
    sm: "w-4 h-4 text-[10px]",
    md: "w-5 h-5 text-xs",
    lg: "w-6 h-6 text-xs",
    xl: "w-7 h-7 text-sm",
  };

  return (
    <motion.div
      className={cn("relative cursor-pointer", className)}
      initial="initial"
      whileHover="whileHover"
      whileTap="whileTap"
      animate="animate"
      exit="exit"
      onClick={onClick}
      {...bellAnimation}
      {...props}
    >
      <div className={cn(sizeClasses[size])}>
        {icon}
      </div>

      {hasNotifications && (
        count ? (
          <div className={cn(
            "absolute -top-1 -right-1 flex items-center justify-center rounded-full bg-destructive text-destructive-foreground font-medium",
            countSizeClasses[size]
          )}>
            {count > 99 ? "99+" : count}
          </div>
        ) : (
          <motion.div
            className={cn(
              "absolute -top-1 -right-1 rounded-full bg-destructive",
              dotSizeClasses[size]
            )}
            initial={{ scale: 0.8 }}
            animate={{ scale: [0.8, 1.2, 0.8] }}
            transition={{
              repeat: Infinity,
              duration: 2,
              repeatType: "loop"
            }}
          />
        )
      )}
    </motion.div>
  );
}
