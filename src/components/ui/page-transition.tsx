"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useAnimationVariant, useAnimations } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface PageTransitionProps {
  children: React.ReactNode;
  animationVariant?: keyof typeof VARIANTS;
  className?: string;
}

/**
 * Page transition component that animates page transitions
 * Uses lazy loading for better performance
 */
export function PageTransition({
  children,
  animationVariant = "pageTransition",
  className
}: PageTransitionProps) {
  const pageAnimation = useAnimationVariant(animationVariant);
  const { useAnimationProperties, useLazyAnimation } = useAnimations();
  const { ref, inView } = useLazyAnimation(0.1);
  const animationProps = useAnimationProperties();

  return (
    <AnimatePresence mode="wait">
      <motion.div
        ref={ref}
        key={React.useId()}
        initial="initial"
        animate={inView ? "animate" : "initial"}
        exit="exit"
        className={className}
        style={animationProps.style}
        {...pageAnimation}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}

/**
 * Staggered list component that animates list items with a staggered effect
 * Optimized for performance with lazy loading and reduced animations on low-performance devices
 */
export function StaggeredList({
  children,
  className,
  itemClassName,
  staggerDelay = 0.05,
  initialDelay = 0.1,
}: {
  children: React.ReactNode;
  className?: string;
  itemClassName?: string;
  staggerDelay?: number;
  initialDelay?: number;
}) {
  const { useAnimationProperties, useLazyAnimation } = useAnimations();
  const { ref, inView } = useLazyAnimation(0.1);
  const { enableStaggering, shouldSimplifyAnimations } = useAnimations();
  const animationProps = useAnimationProperties();

  // Get the staggerContainer variant and customize it based on performance settings
  const baseContainerAnimation = useAnimationVariant("staggerContainer");

  // Adjust staggering based on device performance
  const effectiveStaggerDelay = enableStaggering ? staggerDelay : 0;
  const effectiveInitialDelay = enableStaggering ? initialDelay : 0;

  // Customize the staggerContainer animation with our specific delays
  const containerAnimation = {
    ...baseContainerAnimation,
    animate: {
      ...baseContainerAnimation.animate,
      transition: {
        ...baseContainerAnimation.animate?.transition,
        staggerChildren: effectiveStaggerDelay,
        delayChildren: effectiveInitialDelay,
      },
    },
    exit: {
      ...baseContainerAnimation.exit,
      transition: {
        ...baseContainerAnimation.exit?.transition,
        staggerChildren: enableStaggering ? 0.03 : 0,
        staggerDirection: -1,
      },
    },
  };

  const itemAnimation = useAnimationVariant("listItem");

  // Clone children and wrap them in motion.div
  const animatedChildren = React.Children.map(children, (child, index) => {
    if (!React.isValidElement(child)) return child;

    // For simplified animations, only animate the first few items to improve performance
    const shouldAnimate = !shouldSimplifyAnimations || index < 5;

    return (
      <motion.div
        className={itemClassName}
        initial="initial"
        animate={inView && shouldAnimate ? "animate" : "initial"}
        exit="exit"
        style={animationProps.style}
        {...itemAnimation}
      >
        {child}
      </motion.div>
    );
  });

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="initial"
      animate={inView ? "animate" : "initial"}
      exit="exit"
      style={animationProps.style}
      {...containerAnimation}
    >
      {animatedChildren}
    </motion.div>
  );
}
