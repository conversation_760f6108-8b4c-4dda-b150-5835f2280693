"use client";

// Export all animated components from a single file for easier imports

// Animation utilities
export * from "@/lib/animations";
export * from "@/hooks/use-animations";

// Animated components - using specific imports to avoid conflicts
export * from "@/components/ui/animated-button";
export * from "@/components/ui/animated-card";
export * from "@/components/ui/animated-form";
export * from "@/components/ui/animated-list";
// Export icon components but exclude the conflicting NotificationBell
export { AnimatedIcon } from "@/components/ui/animated-icon";
export * from "@/components/ui/animated-tooltip";
// Export badge component but exclude the conflicting StatusIndicator
export { AnimatedBadge } from "@/components/ui/animated-badge";
export * from "@/components/ui/animated-tabs";
// Explicitly rename the conflicting export to resolve ambiguity
export { AnimatedSpinner as LoadingSpinner } from "@/components/ui/animated-loading";
// Export feedback components but exclude the conflicting Success/Error
export { AnimatedFeedback, showAnimatedToast } from "@/components/ui/animated-feedback";
// Export page transition but exclude the conflicting StaggeredList
export { PageTransition } from "@/components/ui/page-transition";
// Export select components
export * from "@/components/ui/animated/select";
// Export skeleton component
export * from "@/components/ui/animated-skeleton";
// Export loading dots component
export * from "@/components/ui/animated-loading-dots";
// Export progress bar component
export * from "@/components/ui/animated-progress-bar";
// Export notification bell component with a renamed export
export { AnimatedNotificationBell as NotificationBell } from "@/components/ui/animated-notification-bell";
// Export status indicator component with a renamed export
export { AnimatedStatusIndicator as StatusIndicator } from "@/components/ui/animated-status-indicator";
// Export success and error components with renamed exports
export { AnimatedSuccess as SuccessIcon, AnimatedError as ErrorIcon } from "@/components/ui/animated-success-error";
// Export grid component with a renamed export
export { AnimatedGrid as Grid } from "@/components/ui/animated-grid";
// Export staggered list component with a renamed export
export { StaggeredList as StaggeredListComponent } from "@/components/ui/staggered-list";
// Export form components
export { Form } from "@/components/ui/form";
