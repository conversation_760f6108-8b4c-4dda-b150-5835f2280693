/**
 * Permission Gate Component
 * 
 * This component conditionally renders its children based on permission checks.
 */

import React from 'react';
import { usePermission } from '@/hooks/use-permissions';
import { Skeleton } from '@/components/ui/skeleton';

interface PermissionGateProps {
  permission: string;
  resourceType?: string;
  resourceId?: string;
  teamId?: string;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * PermissionGate component
 * 
 * Renders its children only if the user has the specified permission.
 * Otherwise, renders the fallback component or nothing.
 */
export function PermissionGate({
  permission,
  resourceType,
  resourceId,
  teamId,
  fallback = null,
  loading,
  children,
}: PermissionGateProps) {
  const { hasPermission, isLoading, error } = usePermission(permission, {
    resourceType,
    resourceId,
    teamId,
  });
  
  // Show loading state
  if (isLoading) {
    return loading || (
      <div className="animate-pulse">
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }
  
  // Show error state
  if (error) {
    console.error('Permission check error:', error);
    return fallback;
  }
  
  // Show content or fallback based on permission
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

/**
 * PermissionGateGroup component
 * 
 * Renders its children if the user has any of the specified permissions.
 */
export function PermissionGateGroup({
  permissions,
  resourceType,
  resourceId,
  teamId,
  fallback = null,
  loading,
  children,
}: Omit<PermissionGateProps, 'permission'> & { permissions: string[] }) {
  const permissionChecks = permissions.map(permission => 
    usePermission(permission, { resourceType, resourceId, teamId })
  );
  
  const isLoading = permissionChecks.some(check => check.isLoading);
  const hasAnyPermission = permissionChecks.some(check => check.hasPermission);
  const hasError = permissionChecks.some(check => check.error);
  
  // Show loading state
  if (isLoading) {
    return loading || (
      <div className="animate-pulse">
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }
  
  // Show error state
  if (hasError) {
    console.error('Permission check error in group');
    return fallback;
  }
  
  // Show content or fallback based on permissions
  return hasAnyPermission ? <>{children}</> : <>{fallback}</>;
}
