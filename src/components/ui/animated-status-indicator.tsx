"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedStatusIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  status: "idle" | "loading" | "success" | "error" | "warning";
  size?: "sm" | "md" | "lg";
  animationVariant?: keyof typeof VARIANTS;
  showLabel?: boolean;
}

/**
 * Animated status indicator component
 */
export function AnimatedStatusIndicator({
  status,
  className,
  size = "md",
  animationVariant = "pulse",
  showLabel = false,
  ...props
}: AnimatedStatusIndicatorProps) {
  const statusAnimation = useAnimationVariant(animationVariant);
  
  const sizeClasses = {
    sm: "w-2 h-2",
    md: "w-3 h-3",
    lg: "w-4 h-4",
  };
  
  const statusColors = {
    idle: "bg-muted",
    loading: "bg-blue-500",
    success: "bg-green-500",
    error: "bg-red-500",
    warning: "bg-yellow-500",
  };
  
  const statusLabels = {
    idle: "Idle",
    loading: "Loading",
    success: "Success",
    error: "Error",
    warning: "Warning",
  };
  
  const pulseAnimation = status === "loading" || status === "warning";
  
  return (
    <div className="flex items-center gap-2">
      <motion.div
        className={cn(
          "rounded-full",
          sizeClasses[size],
          statusColors[status],
          className
        )}
        initial="initial"
        animate={pulseAnimation ? {
          scale: [1, 1.2, 1],
          opacity: [0.7, 1, 0.7],
        } : "animate"}
        transition={pulseAnimation ? {
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut",
        } : undefined}
        exit="exit"
        {...statusAnimation}
        {...props}
      />
      {showLabel && (
        <span className="text-sm">{statusLabels[status]}</span>
      )}
    </div>
  );
}
