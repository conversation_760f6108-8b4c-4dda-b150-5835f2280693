"use client";

import { useEffect, useState } from 'react';

interface ConfettiProps {
  duration?: number;
  particleCount?: number;
  spread?: number;
  origin?: { x: number; y: number };
  colors?: string[];
  onComplete?: () => void;
}

export function Confetti({
  duration = 3000,
  particleCount = 100,
  spread = 70,
  origin = { x: 0.5, y: 0.5 },
  colors,
  onComplete,
}: ConfettiProps) {
  const [confettiInstance, setConfettiInstance] = useState<any>(null);

  useEffect(() => {
    // Dynamically import canvas-confetti to avoid SSR issues
    const loadConfetti = async () => {
      try {
        const confettiModule = await import('canvas-confetti');
        const confetti = confettiModule.default;
        setConfettiInstance(confetti);
      } catch (error) {
        console.error('Failed to load confetti:', error);
      }
    };

    loadConfetti();
  }, []);

  useEffect(() => {
    if (!confettiInstance) return;

    const timeoutId = setTimeout(() => {
      confettiInstance({
        particleCount,
        spread,
        origin,
        colors,
        disableForReducedMotion: true,
      });

      // Call onComplete callback after duration
      if (onComplete) {
        setTimeout(onComplete, duration);
      }
    }, 100); // Small delay to ensure component is mounted

    return () => clearTimeout(timeoutId);
  }, [confettiInstance, particleCount, spread, origin, colors, duration, onComplete]);

  // This component doesn't render anything visible
  return null;
}
