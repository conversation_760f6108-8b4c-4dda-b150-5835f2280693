import { ReactNode } from "react";
import { cn } from "@/lib/utils";
import { PageTransition } from "@/components/ui/animated";

interface PageContainerProps {
  children: ReactNode;
  className?: string;
  withTransition?: boolean;
  transitionVariant?: "fadeIn" | "slideInFromBottom" | "slideInFromRight";
}

export function PageContainer({ 
  children, 
  className,
  withTransition = true,
  transitionVariant = "fadeIn"
}: PageContainerProps) {
  const content = (
    <div className={cn("w-full flex-grow flex flex-col", className)}>
      {children}
    </div>
  );

  if (withTransition) {
    return (
      <PageTransition animationVariant={transitionVariant}>
        {content}
      </PageTransition>
    );
  }

  return content;
}
