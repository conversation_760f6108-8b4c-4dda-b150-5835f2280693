"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";
import { CheckIcon, XIcon } from "lucide-react";

interface AnimatedSuccessProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl";
  animationVariant?: keyof typeof VARIANTS;
  color?: string;
}

interface AnimatedErrorProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl";
  animationVariant?: keyof typeof VARIANTS;
  color?: string;
}

/**
 * Animated success icon component
 */
export function AnimatedSuccess({
  className,
  size = "md",
  animationVariant = "fadeIn",
  color = "green-500",
  ...props
}: AnimatedSuccessProps) {
  const successAnimation = useAnimationVariant(animationVariant);

  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-10 h-10",
    xl: "w-12 h-12",
  };

  const iconSizeClasses = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
    xl: "w-6 h-6",
  };

  // Get the checkmark animation from centralized variants
  const checkmarkAnimation = useAnimationVariant("successCheckmark");

  return (
    <motion.div
      className={cn(
        "flex items-center justify-center rounded-full",
        `bg-${color}/20`,
        sizeClasses[size],
        className
      )}
      initial="initial"
      animate="animate"
      exit="exit"
      {...successAnimation}
      {...props}
    >
      <motion.div
        className={cn(
          `text-${color}`,
          iconSizeClasses[size]
        )}
        initial="initial"
        animate="animate"
        {...checkmarkAnimation}
      >
        <CheckIcon />
      </motion.div>
    </motion.div>
  );
}

/**
 * Animated error icon component
 */
export function AnimatedError({
  className,
  size = "md",
  animationVariant = "fadeIn",
  color = "red-500",
  ...props
}: AnimatedErrorProps) {
  const errorAnimation = useAnimationVariant(animationVariant);

  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-10 h-10",
    xl: "w-12 h-12",
  };

  const iconSizeClasses = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
    xl: "w-6 h-6",
  };

  return (
    <motion.div
      className={cn(
        "flex items-center justify-center rounded-full",
        `bg-${color}/20`,
        sizeClasses[size],
        className
      )}
      initial="initial"
      animate="animate"
      exit="exit"
      {...errorAnimation}
      {...props}
    >
      <motion.div
        className={cn(
          `text-${color}`,
          iconSizeClasses[size]
        )}
        initial="initial"
        animate="animate"
        {...useAnimationVariant("errorXmark")}
      >
        <XIcon />
      </motion.div>
    </motion.div>
  );
}
