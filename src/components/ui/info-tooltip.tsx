"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";

interface InfoTooltipProps {
  content: React.ReactNode;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
  className?: string;
  iconClassName?: string;
}

/**
 * A reusable information tooltip component
 * 
 * @param content The content to display in the tooltip
 * @param side The side of the trigger to show the tooltip
 * @param align The alignment of the tooltip relative to the trigger
 * @param className Additional classes for the tooltip content
 * @param iconClassName Additional classes for the info icon
 */
export function InfoTooltip({ 
  content, 
  side = "top", 
  align = "center",
  className = "",
  iconClassName = ""
}: InfoTooltipProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            className={`h-5 w-5 text-muted-foreground hover:text-foreground p-0 ${iconClassName}`}
          >
            <Info className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent 
          side={side} 
          align={align} 
          className={`max-w-xs ${className}`}
        >
          {content}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
