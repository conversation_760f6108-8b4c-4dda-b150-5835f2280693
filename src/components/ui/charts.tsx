"use client";

import { useMemo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  CartesianGrid,
  Cell,
  Legend,
} from "recharts";

interface ChartProps {
  data: any[];
  xAxisKey: string;
  yAxisKey: string;
  height?: number;
  colors?: string[];
  valueFormatter?: (value: number) => string;
}

interface PieChartProps {
  data: any[];
  nameKey: string;
  dataKey: string;
  height?: number;
  colors?: string[];
  valueFormatter?: (value: number) => string;
  showLegend?: boolean;
}

export function BarChart({
  data,
  xAxisKey,
  yAxisKey,
  height = 300,
  colors = ["#2563eb"],
  valueFormatter = (value: number) => `${value}`,
}: ChartProps) {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm">
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                {label}
              </span>
              <span className="font-bold text-muted-foreground">
                {valueFormatter(payload[0].value)}
              </span>
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsBarChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} className="stroke-muted" />
        <XAxis
          dataKey={xAxisKey}
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12 }}
          tickMargin={8}
          className="text-muted-foreground"
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12 }}
          tickMargin={8}
          className="text-muted-foreground"
          tickFormatter={valueFormatter}
        />
        <Tooltip content={<CustomTooltip />} cursor={{ fill: "hsl(var(--muted))", opacity: 0.15 }} />
        <Bar
          dataKey={yAxisKey}
          fill={colors[0]}
          radius={[4, 4, 0, 0]}
          className="fill-primary"
        />
      </RechartsBarChart>
    </ResponsiveContainer>
  );
}

export function LineChart({
  data,
  xAxisKey,
  yAxisKey,
  height = 300,
  colors = ["#2563eb"],
  valueFormatter = (value: number) => `${value}`,
}: ChartProps) {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm">
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                {label}
              </span>
              <span className="font-bold text-muted-foreground">
                {valueFormatter(payload[0].value)}
              </span>
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsLineChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} className="stroke-muted" />
        <XAxis
          dataKey={xAxisKey}
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12 }}
          tickMargin={8}
          className="text-muted-foreground"
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12 }}
          tickMargin={8}
          className="text-muted-foreground"
          tickFormatter={valueFormatter}
        />
        <Tooltip content={<CustomTooltip />} cursor={{ stroke: "hsl(var(--muted))", strokeWidth: 1 }} />
        <Line
          type="monotone"
          dataKey={yAxisKey}
          stroke={colors[0]}
          strokeWidth={2}
          dot={{ r: 4, strokeWidth: 2 }}
          activeDot={{ r: 6, strokeWidth: 2 }}
        />
      </RechartsLineChart>
    </ResponsiveContainer>
  );
}

export function PieChart({
  data,
  nameKey,
  dataKey,
  height = 300,
  colors = [
    "#2563eb",
    "#16a34a",
    "#ea580c",
    "#8b5cf6",
    "#db2777",
    "#14b8a6",
    "#f59e0b",
    "#6366f1"
  ],
  valueFormatter = (value: number) => `${value}`,
  showLegend = true,
}: PieChartProps) {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm">
          <div className="flex flex-col">
            <span className="font-medium">{payload[0].name}</span>
            <span className="text-muted-foreground">
              {valueFormatter(payload[0].value)}
            </span>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsPieChart margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
        <Pie
          data={data}
          nameKey={nameKey}
          dataKey={dataKey}
          cx="50%"
          cy="50%"
          outerRadius={80}
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={colors[index % colors.length]} 
            />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
        {showLegend && <Legend layout="vertical" align="right" verticalAlign="middle" />}
      </RechartsPieChart>
    </ResponsiveContainer>
  );
}
