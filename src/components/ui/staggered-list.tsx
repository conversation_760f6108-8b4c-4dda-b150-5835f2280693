"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface StaggeredListProps {
  children: React.ReactNode;
  className?: string;
  itemClassName?: string;
  animationVariant?: keyof typeof VARIANTS;
  staggerDelay?: number;
  initialDelay?: number;
  as?: React.ElementType;
}

/**
 * StaggeredList component that animates its children with a staggered effect
 */
export function StaggeredList({
  children,
  className,
  itemClassName,
  animationVariant = "fadeIn",
  staggerDelay = 0.05,
  initialDelay = 0.1,
  as: Component = "ul",
}: StaggeredListProps) {
  const itemAnimation = useAnimationVariant(animationVariant);

  // Get the staggerContainer variant and customize it based on our specific delays
  const baseContainerAnimation = useAnimationVariant("staggerContainer");
  const containerAnimation = {
    ...baseContainerAnimation,
    animate: {
      ...baseContainerAnimation.animate,
      transition: {
        ...baseContainerAnimation.animate?.transition,
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
    exit: {
      ...baseContainerAnimation.exit,
      transition: {
        ...baseContainerAnimation.exit?.transition,
        staggerChildren: 0.03,
        staggerDirection: -1,
      },
    },
  };

  // Wrap each child in a motion.li element
  const wrappedChildren = React.Children.map(children, (child, index) => {
    if (!React.isValidElement(child)) return null;

    return (
      <motion.li
        className={cn(itemClassName)}
        variants={itemAnimation}
        key={index}
      >
        {child}
      </motion.li>
    );
  });

  return (
    <Component
      as={motion[Component]}
      className={cn(className)}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={containerAnimation}
    >
      {wrappedChildren}
    </Component>
  );
}
