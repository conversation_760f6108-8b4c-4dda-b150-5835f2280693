"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

/**
 * Animated loading skeleton component with wave effect
 */
function AnimatedSkeleton({
  className,
  ...props
}: React.ComponentProps<typeof Skeleton>) {
  return (
    <Skeleton
      className={cn("relative overflow-hidden", className)}
      {...props}
    >
      <motion.div
        className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
        animate={{
          translateX: ["100%", "-100%"],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "linear",
        }}
      />
    </Skeleton>
  );
}

/**
 * Animated loading dots component
 */
function AnimatedLoadingDots({
  className,
  dotClassName,
  count = 3,
  size = "md",
}: {
  className?: string;
  dotClassName?: string;
  count?: number;
  size?: "sm" | "md" | "lg";
}) {
  const sizeClasses = {
    sm: "w-1 h-1",
    md: "w-2 h-2",
    lg: "w-3 h-3",
  };
  
  return (
    <div className={cn("flex items-center space-x-1", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className={cn(
            "rounded-full bg-current",
            sizeClasses[size],
            dotClassName
          )}
          initial={{ opacity: 0.4, scale: 0.8 }}
          animate={{
            opacity: [0.4, 1, 0.4],
            scale: [0.8, 1, 0.8],
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  );
}

/**
 * Animated progress bar component
 */
function AnimatedProgressBar({
  value = 0,
  className,
  barClassName,
  indeterminate = false,
}: {
  value?: number;
  className?: string;
  barClassName?: string;
  indeterminate?: boolean;
}) {
  return (
    <div
      className={cn(
        "relative h-2 w-full overflow-hidden rounded-full bg-primary/20",
        className
      )}
    >
      {indeterminate ? (
        <motion.div
          className={cn("h-full bg-primary", barClassName)}
          initial={{ width: "30%", x: "-100%" }}
          animate={{
            x: ["100%", "-100%"],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ) : (
        <motion.div
          className={cn("h-full bg-primary", barClassName)}
          initial={{ width: 0 }}
          animate={{ width: `${value}%` }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
          }}
        />
      )}
    </div>
  );
}

/**
 * Animated spinner component
 */
function AnimatedSpinner({
  className,
  size = "md",
  color = "primary",
}: {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  color?: "primary" | "secondary" | "accent" | "white";
}) {
  const sizeClasses = {
    sm: "w-4 h-4 border-2",
    md: "w-6 h-6 border-2",
    lg: "w-8 h-8 border-3",
    xl: "w-12 h-12 border-4",
  };
  
  const colorClasses = {
    primary: "border-primary",
    secondary: "border-secondary",
    accent: "border-accent",
    white: "border-white",
  };
  
  return (
    <motion.div
      className={cn(
        "rounded-full border-solid border-t-transparent",
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        ease: "linear",
        repeat: Infinity,
      }}
    />
  );
}

export { AnimatedSkeleton, AnimatedLoadingDots, AnimatedProgressBar, AnimatedSpinner };
