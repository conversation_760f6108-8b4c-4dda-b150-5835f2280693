"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

// Animated tooltip component
interface AnimatedTooltipProps {
  trigger: React.ReactNode;
  content: React.ReactNode;
  className?: string;
  contentClassName?: string;
  animationVariant?: keyof typeof VARIANTS;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
  delayDuration?: number;
}

export function AnimatedTooltip({
  trigger,
  content,
  className,
  contentClassName,
  animationVariant = "scaleIn",
  side = "top",
  align = "center",
  delayDuration = 200,
}: AnimatedTooltipProps) {
  const tooltipAnimation = useAnimationVariant(animationVariant);
  const [isOpen, setIsOpen] = React.useState(false);
  
  // Custom motion component for tooltip content
  const MotionTooltipContent = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<typeof TooltipContent>
  >(({ className, ...props }, ref) => {
    return (
      <AnimatePresence>
        {isOpen && (
          <TooltipContent
            ref={ref}
            asChild
            className={className}
            {...props}
          >
            <motion.div
              initial="initial"
              animate="animate"
              exit="exit"
              {...tooltipAnimation}
            >
              {props.children}
            </motion.div>
          </TooltipContent>
        )}
      </AnimatePresence>
    );
  });
  MotionTooltipContent.displayName = "MotionTooltipContent";
  
  return (
    <TooltipProvider delayDuration={delayDuration}>
      <Tooltip 
        open={isOpen} 
        onOpenChange={setIsOpen}
      >
        <TooltipTrigger asChild className={className}>
          {trigger}
        </TooltipTrigger>
        <MotionTooltipContent 
          side={side} 
          align={align} 
          className={contentClassName}
        >
          {content}
        </MotionTooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Animated popover component
interface AnimatedPopoverProps {
  trigger: React.ReactNode;
  content: React.ReactNode;
  className?: string;
  contentClassName?: string;
  animationVariant?: keyof typeof VARIANTS;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
}

export function AnimatedPopover({
  trigger,
  content,
  className,
  contentClassName,
  animationVariant = "scaleIn",
  side = "bottom",
  align = "center",
}: AnimatedPopoverProps) {
  const popoverAnimation = useAnimationVariant(animationVariant);
  const [isOpen, setIsOpen] = React.useState(false);
  
  // Custom motion component for popover content
  const MotionPopoverContent = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<typeof PopoverContent>
  >(({ className, ...props }, ref) => {
    return (
      <AnimatePresence>
        {isOpen && (
          <PopoverContent
            ref={ref}
            asChild
            className={className}
            {...props}
          >
            <motion.div
              initial="initial"
              animate="animate"
              exit="exit"
              {...popoverAnimation}
            >
              {props.children}
            </motion.div>
          </PopoverContent>
        )}
      </AnimatePresence>
    );
  });
  MotionPopoverContent.displayName = "MotionPopoverContent";
  
  return (
    <Popover 
      open={isOpen} 
      onOpenChange={setIsOpen}
    >
      <PopoverTrigger asChild className={className}>
        {trigger}
      </PopoverTrigger>
      <MotionPopoverContent 
        side={side} 
        align={align} 
        className={contentClassName}
      >
        {content}
      </MotionPopoverContent>
    </Popover>
  );
}
