"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Filter,
  ArrowUpDown,
  Link,
  Link2Off,
  FileText,
  Calendar,
  CheckCircle,
  Clock,
  ShoppingCart,
  Archive,
  AlertCircle,
  Info,
  Plus,
  Trash,
  RotateCcw,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

// Project interface
interface Project {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  verificationStatus: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  country?: string;
  standard?: string;
  methodology?: string;
  estimatedReductions?: number;
  actualReductions?: number;
  createdAt: string;
  updatedAt: string;
  carbonCredits: CarbonCredit[];
  organization: {
    id: string;
    name: string;
  };
}

// Carbon credit interface
interface CarbonCredit {
  id: string;
  name: string;
  quantity: number;
  availableQuantity: number;
  retiredQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  projectId?: string;
}

// Project-Credit Linking props
interface ProjectCreditLinkingProps {
  project: Project;
  availableCredits: CarbonCredit[];
  onSave: (linkedCredits: string[], unlinkedCredits: string[]) => void;
  onCancel: () => void;
}

export function ProjectCreditLinking({
  project,
  availableCredits,
  onSave,
  onCancel
}: ProjectCreditLinkingProps) {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [vintageFilter, setVintageFilter] = useState<string>("");
  const [standardFilter, setStandardFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [selectedCredits, setSelectedCredits] = useState<string[]>([]);
  const [expandedCredits, setExpandedCredits] = useState<Record<string, boolean>>({});
  const [activeTab, setActiveTab] = useState<string>("available");

  // Initialize selected credits with already linked credits
  useEffect(() => {
    setSelectedCredits(project.carbonCredits.map(credit => credit.id));
  }, [project]);

  // Get unique vintages
  const vintages = [...new Set(availableCredits.map(credit => credit.vintage.toString()))];

  // Get unique standards
  const standards = [...new Set(availableCredits.map(credit => credit.standard))];

  // Get unique statuses
  const statuses = [...new Set(availableCredits.map(credit => credit.status))];

  // Filter and sort credits
  const filteredAvailableCredits = availableCredits.filter(credit => {
    // Search term filter
    if (searchTerm && !credit.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Vintage filter
    if (vintageFilter && credit.vintage.toString() !== vintageFilter) {
      return false;
    }

    // Standard filter
    if (standardFilter && credit.standard !== standardFilter) {
      return false;
    }

    // Status filter
    if (statusFilter && credit.status !== statusFilter) {
      return false;
    }

    // Only show credits that are not already linked to this project
    return !credit.projectId || credit.projectId === project.id;
  }).sort((a, b) => {
    // Sort by selected field
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "vintage":
        comparison = a.vintage - b.vintage;
        break;
      case "quantity":
        comparison = a.quantity - b.quantity;
        break;
      case "price":
        comparison = a.price - b.price;
        break;
      case "date":
        comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        break;
      default:
        comparison = a.name.localeCompare(b.name);
    }

    // Apply sort order
    return sortOrder === "asc" ? comparison : -comparison;
  });

  // Get currently linked credits
  const linkedCredits = availableCredits.filter(credit =>
    credit.projectId === project.id
  );

  // Get selected credits details
  const selectedCreditsDetails = availableCredits.filter(credit =>
    selectedCredits.includes(credit.id)
  );

  // Toggle credit selection
  const toggleCreditSelection = (creditId: string) => {
    setSelectedCredits(prev => {
      if (prev.includes(creditId)) {
        return prev.filter(id => id !== creditId);
      } else {
        return [...prev, creditId];
      }
    });
  };

  // Toggle credit expansion
  const toggleCreditExpansion = (creditId: string) => {
    setExpandedCredits(prev => ({
      ...prev,
      [creditId]: !prev[creditId]
    }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("");
    setVintageFilter("");
    setStandardFilter("");
    setStatusFilter("");
    setSortBy("name");
    setSortOrder("asc");
  };

  // Select all credits
  const selectAllCredits = () => {
    setSelectedCredits(filteredAvailableCredits.map(credit => credit.id));
  };

  // Deselect all credits
  const deselectAllCredits = () => {
    setSelectedCredits([]);
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "VERIFIED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "LISTED":
        return <ShoppingCart className="h-4 w-4 text-blue-500" />;
      case "RETIRED":
        return <Archive className="h-4 w-4 text-purple-500" />;
      default:
        return null;
    }
  };

  // Calculate changes
  const calculateChanges = () => {
    const originalLinkedIds = project.carbonCredits.map(credit => credit.id);
    const toLink = selectedCredits.filter(id => !originalLinkedIds.includes(id));
    const toUnlink = originalLinkedIds.filter(id => !selectedCredits.includes(id));

    return {
      toLink,
      toUnlink,
      hasChanges: toLink.length > 0 || toUnlink.length > 0
    };
  };

  const { toLink, toUnlink, hasChanges } = calculateChanges();

  // Render credit card
  const renderCreditCard = (credit: CarbonCredit, isLinked: boolean = false) => {
    const isSelected = selectedCredits.includes(credit.id);
    const isExpanded = expandedCredits[credit.id];

    return (
      <Card
        key={credit.id}
        className={`border ${isSelected ? "border-primary" : ""}`}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => toggleCreditSelection(credit.id)}
                className="mr-2"
              />
              <div>
                <CardTitle className="text-base">{credit.name}</CardTitle>
                <CardDescription className="line-clamp-1">
                  {credit.vintage} • {credit.standard} • {credit.methodology}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{credit.status}</Badge>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => toggleCreditExpansion(credit.id)}
                >
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">Quantity</div>
              <div className="font-medium">{credit.quantity.toLocaleString()} tons</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Available</div>
              <div className="font-medium">{credit.availableQuantity.toLocaleString()} tons</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Price</div>
              <div className="font-medium">${credit.price.toFixed(2)}/ton</div>
            </div>
          </div>

          <CollapsibleContent className="pt-4">
            <Separator className="mb-4" />

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">Standard</div>
                  <div className="font-medium">{credit.standard}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Methodology</div>
                  <div className="font-medium">{credit.methodology}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Created</div>
                  <div className="font-medium">{formatDate(credit.createdAt)}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Updated</div>
                  <div className="font-medium">{formatDate(credit.updatedAt)}</div>
                </div>
              </div>

              <div className="flex justify-between pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`/carbon-credits/${credit.id}`, '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Details
                </Button>
                <Button
                  variant={isSelected ? "destructive" : "outline"}
                  size="sm"
                  onClick={() => toggleCreditSelection(credit.id)}
                >
                  {isSelected ? (
                    <>
                      <Link2Off className="h-4 w-4 mr-2" />
                      {isLinked ? "Unlink" : "Remove"}
                    </>
                  ) : (
                    <>
                      <Link className="h-4 w-4 mr-2" />
                      {isLinked ? "Re-link" : "Link"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CollapsibleContent>
        </CardContent>
      </Card>
    );
  };

  // Render available credits tab
  const renderAvailableCreditsTab = () => {
    return (
      <div className="space-y-6">
        {/* Search and filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search credits..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={vintageFilter} onValueChange={setVintageFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Vintage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">All Vintages</SelectItem>
                {vintages.map(vintage => (
                  <SelectItem key={vintage} value={vintage}>{vintage}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={standardFilter} onValueChange={setStandardFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Standard" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">All Standards</SelectItem>
                {standards.map(standard => (
                  <SelectItem key={standard} value={standard}>{standard}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">All Statuses</SelectItem>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Sort options */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSortOrder(sortOrder === "asc" ? "desc" : "asc");
              }}
            >
              <ArrowUpDown className="h-4 w-4 mr-2" />
              {sortOrder === "asc" ? "Ascending" : "Descending"}
            </Button>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="vintage">Vintage</SelectItem>
                <SelectItem value="quantity">Quantity</SelectItem>
                <SelectItem value="price">Price</SelectItem>
                <SelectItem value="date">Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllCredits}
            >
              Select All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={deselectAllCredits}
            >
              Deselect All
            </Button>
          </div>
        </div>

        {/* Results count */}
        <div className="text-sm text-muted-foreground">
          Showing {filteredAvailableCredits.length} of {availableCredits.length} credits
          {(searchTerm || vintageFilter || standardFilter || statusFilter) && (
            <Button
              variant="link"
              size="sm"
              className="ml-2 h-auto p-0"
              onClick={resetFilters}
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset Filters
            </Button>
          )}
        </div>

        {/* Credits list */}
        {filteredAvailableCredits.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <FileText className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Credits Found</h3>
            <p className="text-muted-foreground text-center max-w-md mb-6">
              {searchTerm || vintageFilter || standardFilter || statusFilter ?
                "No credits match your current filters. Try adjusting your search criteria." :
                "There are no available carbon credits to link to this project."}
            </p>
            {(searchTerm || vintageFilter || standardFilter || statusFilter) && (
              <Button
                variant="outline"
                onClick={resetFilters}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAvailableCredits.map(credit => (
              <Collapsible
                key={credit.id}
                open={expandedCredits[credit.id]}
                onOpenChange={() => toggleCreditExpansion(credit.id)}
              >
                {renderCreditCard(credit)}
              </Collapsible>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render selected credits tab
  const renderSelectedCreditsTab = () => {
    return (
      <div className="space-y-6">
        {selectedCreditsDetails.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Link className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Credits Selected</h3>
            <p className="text-muted-foreground text-center max-w-md mb-6">
              You haven't selected any carbon credits to link to this project yet.
            </p>
            <Button
              variant="outline"
              onClick={() => setActiveTab("available")}
            >
              Browse Available Credits
            </Button>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {selectedCreditsDetails.length} {selectedCreditsDetails.length === 1 ? "credit" : "credits"} selected
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={deselectAllCredits}
              >
                <Trash className="h-4 w-4 mr-2" />
                Clear Selection
              </Button>
            </div>

            <div className="space-y-4">
              {selectedCreditsDetails.map(credit => (
                <Collapsible
                  key={credit.id}
                  open={expandedCredits[credit.id]}
                  onOpenChange={() => toggleCreditExpansion(credit.id)}
                >
                  {renderCreditCard(credit, credit.projectId === project.id)}
                </Collapsible>
              ))}
            </div>
          </>
        )}
      </div>
    );
  };

  // Render changes summary
  const renderChangesSummary = () => {
    if (!hasChanges) {
      return (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            No changes have been made to the linked credits.
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className="space-y-4">
        {toLink.length > 0 && (
          <div>
            <h3 className="text-sm font-medium mb-2">Credits to Link ({toLink.length})</h3>
            <div className="space-y-2">
              {toLink.map(id => {
                const credit = availableCredits.find(c => c.id === id);
                if (!credit) return null;

                return (
                  <div key={id} className="flex items-center justify-between p-2 border rounded-md">
                    <div className="flex items-center">
                      <Plus className="h-4 w-4 text-green-500 mr-2" />
                      <div>
                        <div className="font-medium">{credit.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {credit.vintage} • {credit.standard} • {credit.quantity.toLocaleString()} tons
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline">{credit.status}</Badge>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {toUnlink.length > 0 && (
          <div>
            <h3 className="text-sm font-medium mb-2">Credits to Unlink ({toUnlink.length})</h3>
            <div className="space-y-2">
              {toUnlink.map(id => {
                const credit = availableCredits.find(c => c.id === id);
                if (!credit) return null;

                return (
                  <div key={id} className="flex items-center justify-between p-2 border rounded-md">
                    <div className="flex items-center">
                      <Trash className="h-4 w-4 text-red-500 mr-2" />
                      <div>
                        <div className="font-medium">{credit.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {credit.vintage} • {credit.standard} • {credit.quantity.toLocaleString()} tons
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline">{credit.status}</Badge>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <CardTitle>Link Carbon Credits to Project</CardTitle>
        <CardDescription>
          Manage the carbon credits associated with {project.name}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="available" className="flex-1">
              <FileText className="h-4 w-4 mr-2" />
              Available Credits
            </TabsTrigger>
            <TabsTrigger value="selected" className="flex-1">
              <Link className="h-4 w-4 mr-2" />
              Selected Credits ({selectedCredits.length})
            </TabsTrigger>
            <TabsTrigger value="changes" className="flex-1">
              <Info className="h-4 w-4 mr-2" />
              Changes Summary
            </TabsTrigger>
          </TabsList>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <TabsContent value="available" className="mt-0">
                {renderAvailableCreditsTab()}
              </TabsContent>

              <TabsContent value="selected" className="mt-0">
                {renderSelectedCreditsTab()}
              </TabsContent>

              <TabsContent value="changes" className="mt-0">
                {renderChangesSummary()}
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          onClick={() => onSave(toLink, toUnlink)}
          disabled={!hasChanges}
        >
          Save Changes
        </Button>
      </CardFooter>
    </AnimatedCard>
  );
}
