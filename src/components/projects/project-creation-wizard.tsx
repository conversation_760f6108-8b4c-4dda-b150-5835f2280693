"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";

// Import wizard step components
import { ProjectTemplatesStep } from "./wizard-steps/project-templates-step";
import { ProjectTypeStep } from "./wizard-steps/project-type-step";
import { BasicInfoStep } from "./wizard-steps/basic-info-step";
import { LocationStep } from "./wizard-steps/location-step";
import { MethodologyStep } from "./wizard-steps/methodology-step";
import { TimelineStep } from "./wizard-steps/timeline-step";
import { FinancialsStep } from "./wizard-steps/financials-step";
import { ReviewStep } from "./wizard-steps/review-step";
import { PageTransition } from "@/components/ui/page-transition";
import { AnimatedButton } from "@/components/ui/animated-button";
import {
  Leaf,
  Wind,
  Droplets,
  Factory,
  Recycle,
  Trees,
  Building,
  Info,
  Check,
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Save,
  FileCheck,
  MapPin,
  Calendar,
  BarChart,
  FileText,
  HelpCircle,
  Lightbulb
} from "lucide-react";

// Define project types with icons and descriptions
const PROJECT_TYPES = [
  {
    id: "RENEWABLE_ENERGY",
    name: "Renewable Energy",
    icon: <Wind className="h-5 w-5" />,
    description: "Projects that generate electricity from renewable sources like solar, wind, or hydro.",
    examples: "Solar farms, wind turbines, hydroelectric plants",
    methodologies: ["Gold Standard", "Verra VCS", "CDM"],
    requirements: ["Grid connection details", "Baseline energy mix", "Emission reduction calculations"]
  },
  {
    id: "FORESTRY",
    name: "Forestry & Conservation",
    icon: <Trees className="h-5 w-5" />,
    description: "Projects that protect existing forests or reforest degraded land.",
    examples: "REDD+ projects, reforestation, avoided deforestation",
    methodologies: ["Verra VCS", "Gold Standard", "Plan Vivo"],
    requirements: ["Land ownership documentation", "Forest inventory data", "Satellite imagery"]
  },
  {
    id: "WASTE_MANAGEMENT",
    name: "Waste Management",
    icon: <Recycle className="h-5 w-5" />,
    description: "Projects that reduce emissions from waste through recycling, composting, or methane capture.",
    examples: "Landfill gas capture, waste-to-energy, composting",
    methodologies: ["CDM", "Verra VCS", "Gold Standard"],
    requirements: ["Waste composition data", "Methane generation potential", "Energy generation details"]
  },
  {
    id: "INDUSTRIAL",
    name: "Industrial Processes",
    icon: <Factory className="h-5 w-5" />,
    description: "Projects that reduce emissions from industrial processes through efficiency or fuel switching.",
    examples: "Cement production, refrigerant management, fuel switching",
    methodologies: ["CDM", "Verra VCS", "Climate Action Reserve"],
    requirements: ["Process flow diagrams", "Energy consumption data", "Emission reduction calculations"]
  },
  {
    id: "WATER",
    name: "Water Management",
    icon: <Droplets className="h-5 w-5" />,
    description: "Projects that improve water quality, access, or efficiency.",
    examples: "Water purification, irrigation efficiency, watershed management",
    methodologies: ["Gold Standard", "Verra VCS", "CDM"],
    requirements: ["Water quality data", "Flow measurements", "Community impact assessment"]
  },
  {
    id: "AGRICULTURE",
    name: "Sustainable Agriculture",
    icon: <Leaf className="h-5 w-5" />,
    description: "Projects that reduce emissions from agriculture through improved practices.",
    examples: "Improved rice cultivation, livestock management, soil carbon sequestration",
    methodologies: ["Verra VCS", "Gold Standard", "Plan Vivo"],
    requirements: ["Land use history", "Soil carbon measurements", "Agricultural practice documentation"]
  },
  {
    id: "COMMUNITY",
    name: "Community Development",
    icon: <Building className="h-5 w-5" />,
    description: "Projects that reduce emissions while providing community benefits.",
    examples: "Clean cookstoves, water purification, rural electrification",
    methodologies: ["Gold Standard", "Verra VCS", "CDM"],
    requirements: ["Community engagement plan", "Social impact assessment", "Stakeholder consultation records"]
  }
];

// Define project standards with descriptions
const PROJECT_STANDARDS = [
  {
    id: "VERRA",
    name: "Verra VCS",
    description: "Verified Carbon Standard by Verra, one of the most widely used GHG crediting programs.",
    website: "https://verra.org/programs/verified-carbon-standard/"
  },
  {
    id: "GOLD_STANDARD",
    name: "Gold Standard",
    description: "Certification for projects that reduce carbon emissions while contributing to sustainable development.",
    website: "https://www.goldstandard.org/"
  },
  {
    id: "CDM",
    name: "Clean Development Mechanism",
    description: "UN-run carbon offset scheme under the Kyoto Protocol.",
    website: "https://cdm.unfccc.int/"
  },
  {
    id: "AMERICAN_CARBON_REGISTRY",
    name: "American Carbon Registry",
    description: "The first private voluntary GHG registry in the United States.",
    website: "https://americancarbonregistry.org/"
  },
  {
    id: "CLIMATE_ACTION_RESERVE",
    name: "Climate Action Reserve",
    description: "Program for the North American carbon market.",
    website: "https://www.climateactionreserve.org/"
  },
  {
    id: "PLAN_VIVO",
    name: "Plan Vivo",
    description: "Standard for community land use projects with a focus on livelihoods.",
    website: "https://www.planvivo.org/"
  },
  {
    id: "OTHER",
    name: "Other",
    description: "Other recognized carbon standards or methodologies.",
    website: ""
  }
];

// Define wizard steps
enum WizardStep {
  TEMPLATE_SELECTION = 1,
  PROJECT_TYPE = 2,
  BASIC_INFO = 3,
  LOCATION = 4,
  METHODOLOGY = 5,
  TIMELINE = 6,
  FINANCIALS = 7,
  REVIEW = 8
}

// Define form schema
const projectSchema = z.object({
  // Step 1: Project Type
  projectType: z.string().min(1, "Project type is required"),

  // Step 2: Basic Info
  name: z.string().min(3, "Project name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  summary: z.string().min(10, "Summary must be at least 10 characters"),

  // Step 3: Location
  country: z.string().min(1, "Country is required"),
  region: z.string().optional(),
  coordinates: z.object({
    latitude: z.string().optional(),
    longitude: z.string().optional(),
  }),

  // Step 4: Methodology
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  validationBody: z.string().optional(),

  // Step 5: Timeline
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  creditingPeriod: z.number().positive("Crediting period must be positive"),

  // Step 6: Financials
  estimatedCredits: z.number().positive("Estimated credits must be positive"),
  estimatedPrice: z.number().positive("Estimated price must be positive"),
  investmentRequired: z.number().positive("Investment required must be positive"),
  roi: z.number().optional(),

  // Additional fields
  documents: z.array(z.any()).optional(),
  additionalInfo: z.string().optional(),
});

type ProjectFormValues = z.infer<typeof projectSchema>;

export default function ProjectCreationWizard() {
  const router = useRouter();
  const { data: session } = useSession();
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.TEMPLATE_SELECTION);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProjectType, setSelectedProjectType] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>("BLANK");
  const [draftSaved, setDraftSaved] = useState(false);
  const [draftSaving, setDraftSaving] = useState(false);
  const [methodologies, setMethodologies] = useState<string[]>([]);

  // Initialize form
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      projectType: "",
      name: "",
      description: "",
      summary: "",
      country: "",
      region: "",
      coordinates: {
        latitude: "",
        longitude: "",
      },
      standard: "",
      methodology: "",
      validationBody: "",
      startDate: "",
      endDate: "",
      creditingPeriod: 10,
      estimatedCredits: 0,
      estimatedPrice: 0,
      investmentRequired: 0,
      roi: 0,
      documents: [],
      additionalInfo: "",
    },
    mode: "onChange",
  });

  // Watch form values for auto-save
  const formValues = form.watch();

  // Load methodologies based on selected standard
  useEffect(() => {
    const standard = form.watch("standard");
    if (standard) {
      // In a real implementation, this would fetch methodologies from an API
      // For now, we'll use mock data
      const mockMethodologies = [
        "VM0006 - Carbon Accounting for Mosaic Deforestation",
        "VM0007 - REDD+ Methodology Framework",
        "VM0009 - Avoided Ecosystem Conversion",
        "VM0015 - Avoided Unplanned Deforestation",
        "VM0017 - Adoption of Sustainable Agricultural Land Management",
        "VM0025 - Campus Clean Energy and Energy Efficiency",
        "VM0026 - Sustainable Grassland Management",
        "VM0032 - Adoption of Sustainable Grasslands through Adjustment of Fire and Grazing",
        "VM0033 - Methodology for Tidal Wetland and Seagrass Restoration",
        "VM0036 - Methodology for Rewetting Drained Temperate Peatlands",
      ];
      setMethodologies(mockMethodologies);
    }
  }, [form.watch("standard")]);

  // Load project type details when selected
  useEffect(() => {
    const projectType = form.watch("projectType");
    if (projectType) {
      setSelectedProjectType(projectType);
    }
  }, [form.watch("projectType")]);

  // Auto-save draft
  useEffect(() => {
    const saveTimeout = setTimeout(() => {
      if (Object.keys(form.formState.dirtyFields).length > 0) {
        saveDraft();
      }
    }, 5000);

    return () => clearTimeout(saveTimeout);
  }, [formValues]);

  // Save draft function
  const saveDraft = async () => {
    try {
      setDraftSaving(true);

      // In a real implementation, this would save to an API
      // For now, we'll just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));

      setDraftSaved(true);
      setTimeout(() => setDraftSaved(false), 3000);
    } catch (error) {
      console.error("Error saving draft:", error);
    } finally {
      setDraftSaving(false);
    }
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string, defaultValues: any) => {
    setSelectedTemplate(templateId);

    // Apply template default values to form
    if (defaultValues) {
      Object.entries(defaultValues).forEach(([key, value]) => {
        form.setValue(key as any, value, { shouldValidate: true });
      });
    }

    // Move to next step
    setCurrentStep(WizardStep.PROJECT_TYPE);
  };

  // Handle next step
  const handleNext = async () => {
    let isValid = false;

    switch (currentStep) {
      case WizardStep.TEMPLATE_SELECTION:
        // Template selection is always valid
        isValid = true;
        break;
      case WizardStep.PROJECT_TYPE:
        isValid = await form.trigger("projectType");
        break;
      case WizardStep.BASIC_INFO:
        isValid = await form.trigger(["name", "description", "summary"]);
        break;
      case WizardStep.LOCATION:
        isValid = await form.trigger(["country"]);
        break;
      case WizardStep.METHODOLOGY:
        isValid = await form.trigger(["standard", "methodology"]);
        break;
      case WizardStep.TIMELINE:
        isValid = await form.trigger(["startDate", "endDate", "creditingPeriod"]);
        break;
      case WizardStep.FINANCIALS:
        isValid = await form.trigger(["estimatedCredits", "estimatedPrice", "investmentRequired"]);
        break;
      default:
        isValid = true;
    }

    if (isValid) {
      setCurrentStep(prev => (prev < WizardStep.REVIEW ? prev + 1 : prev));
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    setCurrentStep(prev => (prev > WizardStep.TEMPLATE_SELECTION ? prev - 1 : prev));
  };

  // Handle form submission
  const handleSubmit = async (data: ProjectFormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Transform data to match server expectations
      const serverData = {
        // Map projectType to type as expected by the server
        type: data.projectType,
        name: data.name,
        description: data.description,
        // Convert coordinates object to string format
        coordinates: data.coordinates.latitude && data.coordinates.longitude
          ? `${data.coordinates.latitude},${data.coordinates.longitude}`
          : undefined,
        country: data.country,
        location: data.region,
        standard: data.standard,
        methodology: data.methodology,
        validator: data.validationBody,
        // Format dates as ISO strings for the server
        startDate: data.startDate ? new Date(data.startDate).toISOString() : undefined,
        endDate: data.endDate ? new Date(data.endDate).toISOString() : undefined,
        estimatedReductions: data.estimatedCredits,
        budget: data.investmentRequired,
        roi: data.roi,
        // Include any additional fields that might be needed
        summary: data.summary,
      };

      console.log("Sending project data to server:", serverData);

      const response = await fetch("/api/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(serverData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || "Failed to create project");
      }

      toast({
        title: "Project Created",
        description: "Your project has been created successfully.",
      });

      router.push(`/projects/${responseData.project.id}`);
    } catch (error) {
      console.error("Error creating project:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create project",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate progress percentage
  const progressPercentage = ((currentStep - 1) / (WizardStep.REVIEW - 1)) * 100;

  // Get current project type details
  const currentProjectType = PROJECT_TYPES.find(type => type.id === selectedProjectType);

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Create New Project</h1>
            <p className="text-muted-foreground">
              Create a new carbon reduction or removal project
            </p>
          </div>
          <div className="flex items-center gap-2">
            {draftSaving && (
              <span className="text-sm text-muted-foreground flex items-center">
                <Skeleton className="h-4 w-4 rounded-full mr-2 animate-pulse" />
                Saving...
              </span>
            )}
            {draftSaved && (
              <span className="text-sm text-green-600 flex items-center">
                <Check className="h-4 w-4 mr-2" />
                Draft saved
              </span>
            )}
            <Button variant="outline" size="sm" onClick={saveDraft} disabled={draftSaving}>
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
          </div>
        </div>

        <div className="relative">
          <Progress value={progressPercentage} className="h-2" />
          <div className="absolute top-4 w-full">
            <div className="flex justify-between">
              {Object.values(WizardStep).filter(v => typeof v === "number").map((step) => (
                <div
                  key={step}
                  className={`flex flex-col items-center ${
                    currentStep >= step ? "text-primary" : "text-muted-foreground"
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      currentStep > step
                        ? "bg-primary text-primary-foreground"
                        : currentStep === step
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {currentStep > step ? <Check className="h-4 w-4" /> : step}
                  </div>
                  <span className="text-xs mt-1 hidden md:block">
                    {WizardStep[step]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <Card className="mt-20">
          <CardContent className="pt-6">
            <FormProvider {...form}>
              {currentStep === WizardStep.TEMPLATE_SELECTION && (
                <ProjectTemplatesStep onSelectTemplate={handleTemplateSelect} />
              )}

              {currentStep === WizardStep.PROJECT_TYPE && (
                <ProjectTypeStep />
              )}

              {currentStep === WizardStep.BASIC_INFO && (
                <BasicInfoStep />
              )}

              {currentStep === WizardStep.LOCATION && (
                <LocationStep />
              )}

              {currentStep === WizardStep.METHODOLOGY && (
                <MethodologyStep />
              )}

              {currentStep === WizardStep.TIMELINE && (
                <TimelineStep />
              )}

              {currentStep === WizardStep.FINANCIALS && (
                <FinancialsStep />
              )}

              {currentStep === WizardStep.REVIEW && (
                <ReviewStep
                  formValues={formValues}
                  onEdit={(step) => setCurrentStep(step)}
                  onSubmit={() => handleSubmit(formValues)}
                  isSubmitting={isSubmitting}
                  error={error}
                />
              )}
            </FormProvider>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === WizardStep.TEMPLATE_SELECTION}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            {currentStep < WizardStep.REVIEW ? (
              <Button onClick={handleNext}>
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={() => handleSubmit(formValues)}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Skeleton className="h-4 w-4 rounded-full mr-2 animate-pulse" />
                    Creating...
                  </>
                ) : (
                  <>
                    <FileCheck className="mr-2 h-4 w-4" />
                    Create Project
                  </>
                )}
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </PageTransition>
  );
}
