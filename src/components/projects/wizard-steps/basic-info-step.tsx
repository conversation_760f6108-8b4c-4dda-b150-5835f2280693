"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  FileText, 
  Info, 
  HelpCircle, 
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

export function BasicInfoStep() {
  const { control, watch, formState } = useFormContext();
  const projectName = watch("name");
  const description = watch("description");
  const summary = watch("summary");
  
  // Calculate character counts and limits
  const nameLength = projectName?.length || 0;
  const descriptionLength = description?.length || 0;
  const summaryLength = summary?.length || 0;
  
  const nameLimit = 100;
  const descriptionLimit = 2000;
  const summaryLimit = 500;
  
  // Calculate validation status
  const isNameValid = nameLength >= 3 && nameLength <= nameLimit;
  const isDescriptionValid = descriptionLength >= 10 && descriptionLength <= descriptionLimit;
  const isSummaryValid = summaryLength >= 10 && summaryLength <= summaryLimit;
  
  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Project Basic Information",
          description: "Provide essential details about your project. A clear name and description will help buyers understand your project's value.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/projects/creating-projects",
          learnMoreText: "Learn more about creating effective project descriptions",
        }}
        className="mb-6"
      />
      
      <motion.div 
        className="space-y-6"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Project Name
                  <InlineHelp
                    content={{
                      title: "Project Name",
                      description: "Choose a clear, descriptive name that identifies your project's location, type, and purpose.",
                      examples: [
                        "Amazonas Forest Conservation Project",
                        "Tamil Nadu Wind Power Generation",
                        "Sustainable Rice Cultivation in Cambodia"
                      ],
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      placeholder="e.g., Amazonas Forest Conservation Project"
                      className="pr-16"
                      maxLength={nameLimit}
                    />
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                      {nameLength}/{nameLimit}
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  A clear, concise name for your carbon project.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
        
        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Project Description
                  <InlineHelp
                    content={{
                      title: "Project Description",
                      description: "Provide a comprehensive description of your project, including its goals, activities, and expected impacts.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Textarea
                      {...field}
                      placeholder="Describe your project in detail..."
                      className="min-h-32 resize-y"
                      maxLength={descriptionLimit}
                    />
                    <div className="absolute right-3 bottom-3 text-xs text-muted-foreground">
                      {descriptionLength}/{descriptionLimit}
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  Detailed description of your project's activities, goals, and impact.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
        
        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="summary"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Project Summary
                  <InlineHelp
                    content={{
                      title: "Project Summary",
                      description: "A brief summary that will be displayed in search results and listings. Make it compelling and concise.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Textarea
                      {...field}
                      placeholder="Provide a brief summary of your project..."
                      className="min-h-20 resize-y"
                      maxLength={summaryLimit}
                    />
                    <div className="absolute right-3 bottom-3 text-xs text-muted-foreground">
                      {summaryLength}/{summaryLimit}
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  A concise summary that will appear in marketplace listings.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
      </motion.div>
      
      <Card className="mt-6 bg-muted">
        <CardContent className="pt-6">
          <h3 className="text-sm font-semibold mb-2">Validation Status</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              {isNameValid ? (
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              ) : (
                <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
              )}
              <span className="text-sm">
                Project Name: {isNameValid ? 'Valid' : 'Needs attention'}
              </span>
            </div>
            <div className="flex items-center">
              {isDescriptionValid ? (
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              ) : (
                <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
              )}
              <span className="text-sm">
                Description: {isDescriptionValid ? 'Valid' : 'Needs attention'}
              </span>
            </div>
            <div className="flex items-center">
              {isSummaryValid ? (
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              ) : (
                <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
              )}
              <span className="text-sm">
                Summary: {isSummaryValid ? 'Valid' : 'Needs attention'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="mt-4 text-sm text-muted-foreground">
        <p className="flex items-center">
          <Info className="h-4 w-4 mr-2" />
          All fields on this page are required and will be visible to potential buyers.
        </p>
      </div>
    </div>
  );
}
