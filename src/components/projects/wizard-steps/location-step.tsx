"use client";

import { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import { AnimatedInput } from "@/components/ui/animated";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import {
  MapPin,
  Globe,
  Info,
  HelpCircle,
  AlertCircle,
  CheckCircle,
  Map,
  Search,
  X,
  Plus,
  Minus,
  Locate
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { countries } from "@/lib/countries";

// Mock regions for demonstration
const REGIONS_BY_COUNTRY: Record<string, string[]> = {
  "US": [
    "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia",
    "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland",
    "Massachusetts", "Michigan", "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey",
    "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina",
    "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"
  ],
  "BR": [
    "Acre", "Alagoas", "Amapá", "Amazonas", "Bahia", "Ceará", "Distrito Federal", "Espírito Santo", "Goiás", "Maranhão",
    "Mato Grosso", "Mato Grosso do Sul", "Minas Gerais", "Pará", "Paraíba", "Paraná", "Pernambuco", "Piauí", "Rio de Janeiro", "Rio Grande do Norte",
    "Rio Grande do Sul", "Rondônia", "Roraima", "Santa Catarina", "São Paulo", "Sergipe", "Tocantins"
  ],
  "IN": [
    "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand",
    "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab",
    "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal"
  ],
  // Add more countries as needed
};

export function LocationStep() {
  const { control, watch, setValue } = useFormContext();
  const selectedCountry = watch("country");
  const selectedRegion = watch("region");
  const coordinates = watch("coordinates");
  const [mapDialogOpen, setMapDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [tempCoordinates, setTempCoordinates] = useState<{latitude: string, longitude: string} | null>(null);

  // Get regions for selected country
  const regions = selectedCountry ? REGIONS_BY_COUNTRY[selectedCountry] || [] : [];

  // Handle map dialog open
  const handleOpenMapDialog = () => {
    setTempCoordinates(coordinates || null);
    setMapDialogOpen(true);
  };

  // Handle map click (simulated)
  const handleMapClick = (event: React.MouseEvent<HTMLDivElement>) => {
    // In a real implementation, this would get coordinates from the map click
    // For now, we'll simulate it with random coordinates near the center
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Convert pixel position to simulated lat/long (just for demonstration)
    const lat = (40.7128 + (y - rect.height/2) / 100).toFixed(4);
    const lng = (-74.0060 + (x - rect.width/2) / 100).toFixed(4);

    setTempCoordinates({
      latitude: lat,
      longitude: lng
    });
  };

  // Handle search location (simulated)
  const handleSearchLocation = () => {
    if (!searchQuery) return;

    // In a real implementation, this would geocode the search query
    // For now, we'll just set some example coordinates based on the query
    const hash = searchQuery.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const lat = (40 + (hash % 10) / 10).toFixed(4);
    const lng = (-74 - (hash % 20) / 10).toFixed(4);

    setTempCoordinates({
      latitude: lat,
      longitude: lng
    });

    setSearchQuery("");
  };

  // Handle save coordinates
  const handleSaveCoordinates = () => {
    if (tempCoordinates) {
      setValue("coordinates", tempCoordinates, { shouldValidate: true });
    }
    setMapDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Project Location",
          description: "Specify where your project is located. Accurate location information helps with verification and allows buyers to find projects in specific regions. Precise coordinates are essential for many project types, especially forestry and land use projects.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/projects/location-requirements",
          learnMoreText: "Learn more about location requirements",
          tips: [
            "Use the interactive map to pinpoint your project's exact location",
            "For large projects spanning multiple areas, select the primary or central location",
            "Coordinates should be in decimal degrees format (e.g., 40.7128, -74.0060)",
            "Some project types require boundary polygons which can be added later"
          ]
        }}
        className="mb-6"
      />

      <motion.div
        className="space-y-6"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="country"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Country
                  <InlineHelp
                    content={{
                      title: "Project Country",
                      description: "Select the country where your project is primarily located. For projects spanning multiple countries, select the primary country and note others in the description.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.code} value={country.code}>
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  The country where your project is located.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="region"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Region/State/Province
                  <InlineHelp
                    content={{
                      title: "Project Region",
                      description: "Select the specific region, state, or province where your project is located.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={!selectedCountry || regions.length === 0}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={
                        !selectedCountry
                          ? "Select a country first"
                          : regions.length === 0
                          ? "No regions available"
                          : "Select a region"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {regions.map((region) => (
                        <SelectItem key={region} value={region}>
                          {region}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  The specific region where your project is located.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={control}
            name="coordinates.latitude"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Latitude</FormLabel>
                <FormControl>
                  <AnimatedInput
                    {...field}
                    placeholder="e.g., 40.7128"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="coordinates.longitude"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Longitude</FormLabel>
                <FormControl>
                  <AnimatedInput
                    {...field}
                    placeholder="e.g., -74.0060"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
      </motion.div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Globe className="h-5 w-5 mr-2" />
            Map Location
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-muted-foreground">
              {coordinates?.latitude && coordinates?.longitude
                ? "Location has been pinned. Click to adjust."
                : "Click to select your project location on the map."}
            </p>
            <Button variant="outline" size="sm" onClick={handleOpenMapDialog}>
              <MapPin className="h-4 w-4 mr-2" />
              {coordinates?.latitude ? "Edit Location" : "Pin on Map"}
            </Button>
          </div>

          {coordinates?.latitude && coordinates?.longitude ? (
            <div
              className="bg-background rounded-md p-4 text-center relative h-60 flex items-center justify-center cursor-pointer border border-muted-foreground/20 hover:border-primary/50 transition-colors"
              onClick={handleOpenMapDialog}
            >
              <div className="absolute inset-0 opacity-30 bg-[url('/map-placeholder.svg')] bg-cover bg-center rounded-md"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative z-10 bg-background/80 p-4 rounded-lg backdrop-blur-sm">
                  <MapPin className="h-8 w-8 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium">Location Pinned</p>
                  <p className="text-xs text-muted-foreground">
                    Latitude: {coordinates.latitude}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Longitude: {coordinates.longitude}
                  </p>
                  <p className="text-xs mt-2 text-primary">Click to adjust location</p>
                </div>
              </div>
            </div>
          ) : (
            <div
              className="bg-background rounded-md p-4 text-center h-60 flex items-center justify-center cursor-pointer border border-dashed border-muted-foreground/50 hover:border-primary/50 transition-colors"
              onClick={handleOpenMapDialog}
            >
              <div>
                <Map className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-sm text-muted-foreground max-w-xs">
                  No location pinned yet. Click here to open the interactive map and select your project location.
                </p>
                <Button variant="outline" size="sm" className="mt-4">
                  <MapPin className="h-4 w-4 mr-2" />
                  Open Map
                </Button>
              </div>
            </div>
          )}

          <div className="mt-4 text-sm text-muted-foreground">
            <p className="flex items-center">
              <Info className="h-4 w-4 mr-2 flex-shrink-0" />
              Precise location information helps with project verification and visibility to regional buyers. For large projects, you can define detailed boundaries in a later step.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Map Dialog */}
      <Dialog open={mapDialogOpen} onOpenChange={setMapDialogOpen}>
        <DialogContent className="sm:max-w-[700px] h-[600px] flex flex-col">
          <DialogHeader>
            <DialogTitle>Select Project Location</DialogTitle>
            <DialogDescription>
              Click on the map to place a pin at your project's location.
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Input
                placeholder="Search for a location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
                onKeyDown={(e) => e.key === 'Enter' && handleSearchLocation()}
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full"
                onClick={handleSearchLocation}
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
            <Button variant="outline" size="icon" onClick={() => setSearchQuery("")}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="relative flex-1 border rounded-md overflow-hidden">
            {/* Simulated Map */}
            <div
              className="w-full h-full bg-[url('/map-placeholder.svg')] bg-cover bg-center cursor-crosshair"
              onClick={handleMapClick}
            >
              {tempCoordinates && (
                <div
                  className="absolute"
                  style={{
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <MapPin className="h-8 w-8 text-primary -mt-8" />
                </div>
              )}
            </div>

            {/* Map Controls */}
            <div className="absolute top-4 right-4 flex flex-col space-y-2">
              <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
                <Plus className="h-4 w-4" />
              </Button>
              <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
                <Minus className="h-4 w-4" />
              </Button>
              <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
                <Locate className="h-4 w-4" />
              </Button>
            </div>

            {/* Coordinates Display */}
            {tempCoordinates && (
              <div className="absolute bottom-4 left-4 bg-background/80 backdrop-blur-sm p-2 rounded-md shadow-md">
                <p className="text-xs font-medium">Selected Coordinates:</p>
                <p className="text-xs">Lat: {tempCoordinates.latitude}, Long: {tempCoordinates.longitude}</p>
              </div>
            )}
          </div>

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setMapDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveCoordinates} disabled={!tempCoordinates}>
              <MapPin className="h-4 w-4 mr-2" />
              Save Location
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
