"use client";

import { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import { AnimatedInput } from "@/components/ui/animated";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  Info,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  CalendarDays,
  CalendarClock,
  Timer,
  History,
  Hourglass,
  BarChart
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

export function TimelineStep() {
  const { control, watch, setValue } = useFormContext();
  const startDate = watch("startDate");
  const endDate = watch("endDate");
  const creditingPeriod = watch("creditingPeriod");
  const [viewMode, setViewMode] = useState<"timeline" | "calendar">("timeline");
  const [milestones, setMilestones] = useState<Array<{date: string, title: string, type: string}>>([]);

  // Calculate date difference
  const calculateDateDifference = () => {
    if (!startDate || !endDate) return null;

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) return null;

    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffYears = diffDays / 365;

    return {
      days: diffDays,
      years: diffYears.toFixed(1),
      months: Math.ceil(diffDays / 30)
    };
  };

  const dateDiff = calculateDateDifference();

  // Handle crediting period change
  const handleCreditingPeriodChange = (value: number[]) => {
    setValue("creditingPeriod", value[0], { shouldValidate: true });
  };

  // Generate key milestones
  useEffect(() => {
    if (!startDate || !endDate) return;

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) return;

    const newMilestones = [
      {
        date: startDate,
        title: "Project Start",
        type: "start"
      }
    ];

    // Add crediting period end if it's before project end
    if (creditingPeriod) {
      const creditingEnd = new Date(start);
      creditingEnd.setFullYear(creditingEnd.getFullYear() + creditingPeriod);

      if (creditingEnd < end) {
        newMilestones.push({
          date: creditingEnd.toISOString().split('T')[0],
          title: "Crediting Period End",
          type: "crediting"
        });
      }
    }

    // Add verification milestones (example: every 2 years)
    let verificationDate = new Date(start);
    verificationDate.setFullYear(verificationDate.getFullYear() + 2);

    while (verificationDate < end) {
      newMilestones.push({
        date: verificationDate.toISOString().split('T')[0],
        title: "Verification",
        type: "verification"
      });

      verificationDate = new Date(verificationDate);
      verificationDate.setFullYear(verificationDate.getFullYear() + 2);
    }

    // Add project end
    newMilestones.push({
      date: endDate,
      title: "Project End",
      type: "end"
    });

    setMilestones(newMilestones);
  }, [startDate, endDate, creditingPeriod]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Project Timeline",
          description: "Define the timeline for your project, including start and end dates, and the crediting period during which carbon credits will be issued. The timeline is crucial for determining the project's eligibility and potential carbon credit generation.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/projects/timeline-requirements",
          learnMoreText: "Learn more about project timelines",
          tips: [
            "The crediting period is often shorter than the total project duration",
            "Most standards require regular verification (typically every 2-3 years)",
            "Projects with longer durations often have higher total credit generation potential",
            "Some methodologies have specific minimum or maximum crediting period requirements"
          ]
        }}
        className="mb-6"
      />

      <div className="flex justify-end mb-4">
        <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as "timeline" | "calendar")}>
          <TabsList>
            <TabsTrigger value="timeline" className="flex items-center">
              <History className="h-4 w-4 mr-2" />
              Timeline View
            </TabsTrigger>
            <TabsTrigger value="calendar" className="flex items-center">
              <CalendarDays className="h-4 w-4 mr-2" />
              Calendar View
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <motion.div
        className="space-y-6"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={control}
            name="startDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Start Date
                  <InlineHelp
                    content={{
                      title: "Project Start Date",
                      description: "The date when your project begins implementing activities that reduce or remove greenhouse gas emissions.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      type="date"
                      className="pl-10"
                    />
                    <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  </div>
                </FormControl>
                <FormDescription>
                  When your project begins implementation.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="endDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  End Date
                  <InlineHelp
                    content={{
                      title: "Project End Date",
                      description: "The date when your project is expected to complete its emission reduction or removal activities.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      type="date"
                      className="pl-10"
                    />
                    <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  </div>
                </FormControl>
                <FormDescription>
                  When your project is expected to end.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="creditingPeriod"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Crediting Period (Years)
                  <InlineHelp
                    content={{
                      title: "Crediting Period",
                      description: "The number of years during which your project can generate carbon credits. This may differ from the total project duration.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="space-y-4">
                    <Slider
                      defaultValue={[field.value]}
                      min={1}
                      max={30}
                      step={1}
                      onValueChange={handleCreditingPeriodChange}
                    />
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">1 year</span>
                      <span className="text-lg font-medium">{creditingPeriod} years</span>
                      <span className="text-sm text-muted-foreground">30 years</span>
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  The period during which your project can generate carbon credits.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
      </motion.div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <CalendarClock className="h-5 w-5 mr-2" />
            Project Timeline Visualization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="visual" className="mb-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="visual">Visual Timeline</TabsTrigger>
              <TabsTrigger value="milestones">Key Milestones</TabsTrigger>
            </TabsList>

            <TabsContent value="visual" className="pt-4">
              {startDate && endDate && dateDiff ? (
                <div className="space-y-6">
                  <div className="relative h-24">
                    {/* Main timeline bar */}
                    <div className="absolute top-8 left-0 w-full h-3 bg-muted-foreground/20 rounded-full"></div>

                    {/* Crediting period bar */}
                    <div
                      className="absolute top-8 left-0 h-3 bg-primary rounded-full"
                      style={{ width: `${Math.min(100, (creditingPeriod / parseFloat(dateDiff.years)) * 100)}%` }}
                    ></div>

                    {/* Start marker */}
                    <div className="absolute top-7 left-0 h-5 w-5 rounded-full bg-primary border-2 border-background z-10"></div>

                    {/* End marker */}
                    <div className="absolute top-7 right-0 h-5 w-5 rounded-full bg-muted-foreground/70 border-2 border-background z-10"></div>

                    {/* Start date label */}
                    <div className="absolute top-0 left-0 text-xs">
                      <Badge variant="outline" className="bg-primary/10">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(startDate)}
                      </Badge>
                      <p className="text-xs mt-1 text-muted-foreground">Project Start</p>
                    </div>

                    {/* End date label */}
                    <div className="absolute top-0 right-0 text-xs text-right">
                      <Badge variant="outline" className="bg-muted-foreground/10">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(endDate)}
                      </Badge>
                      <p className="text-xs mt-1 text-muted-foreground">Project End</p>
                    </div>

                    {/* Crediting period end marker */}
                    {creditingPeriod < parseFloat(dateDiff.years) && (
                      <div
                        className="absolute text-xs text-center z-10"
                        style={{
                          left: `${(creditingPeriod / parseFloat(dateDiff.years)) * 100}%`,
                          top: '7px',
                          transform: 'translateX(-50%)'
                        }}
                      >
                        <div className="h-5 w-5 rounded-full bg-primary/70 border-2 border-background"></div>
                        <div className="mt-6">
                          <Badge variant="outline" className="bg-primary/10 whitespace-nowrap">
                            <Timer className="h-3 w-3 mr-1" />
                            {formatDate(new Date(new Date(startDate).setFullYear(new Date(startDate).getFullYear() + creditingPeriod)).toISOString().split('T')[0])}
                          </Badge>
                          <p className="text-xs mt-1 text-muted-foreground">Crediting End</p>
                        </div>
                      </div>
                    )}

                    {/* Verification markers */}
                    {milestones
                      .filter(m => m.type === 'verification')
                      .map((milestone, index) => {
                        const milestoneDate = new Date(milestone.date);
                        const startDateObj = new Date(startDate);
                        const endDateObj = new Date(endDate);
                        const totalDuration = endDateObj.getTime() - startDateObj.getTime();
                        const milestonePosition = (milestoneDate.getTime() - startDateObj.getTime()) / totalDuration * 100;

                        return (
                          <div
                            key={index}
                            className="absolute text-xs z-10"
                            style={{
                              left: `${milestonePosition}%`,
                              top: '14px',
                              transform: 'translateX(-50%)'
                            }}
                          >
                            <div className="h-3 w-3 rounded-full bg-yellow-500 border-2 border-background"></div>
                          </div>
                        );
                      })
                    }
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Hourglass className="h-5 w-5 mr-2 text-primary" />
                            <h4 className="text-sm font-medium">Total Duration</h4>
                          </div>
                          <Badge variant="outline" className="ml-2">
                            {dateDiff.years} years
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          {dateDiff.days} days ({dateDiff.months} months)
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Timer className="h-5 w-5 mr-2 text-primary" />
                            <h4 className="text-sm font-medium">Crediting Period</h4>
                          </div>
                          <Badge variant="outline" className="ml-2">
                            {creditingPeriod} years
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          {creditingPeriod * 365} days ({creditingPeriod * 12} months)
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <BarChart className="h-5 w-5 mr-2 text-primary" />
                            <h4 className="text-sm font-medium">Verifications</h4>
                          </div>
                          <Badge variant="outline" className="ml-2">
                            {milestones.filter(m => m.type === 'verification').length}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          Scheduled every 2 years (typical)
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border border-dashed rounded-md">
                  <Clock className="h-10 w-10 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                    Enter start and end dates to visualize your project timeline. The visualization will show key milestones and the crediting period.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="milestones" className="pt-4">
              {startDate && endDate && dateDiff ? (
                <div className="space-y-4">
                  <div className="relative pl-6 border-l-2 border-muted-foreground/20">
                    {milestones.map((milestone, index) => (
                      <div key={index} className="mb-6 relative">
                        <div className={`absolute -left-[25px] h-5 w-5 rounded-full border-2 border-background ${
                          milestone.type === 'start' ? 'bg-primary' :
                          milestone.type === 'end' ? 'bg-muted-foreground/70' :
                          milestone.type === 'crediting' ? 'bg-primary/70' :
                          'bg-yellow-500'
                        }`}></div>
                        <div className="mb-1">
                          <Badge variant="outline" className={`${
                            milestone.type === 'start' ? 'bg-primary/10' :
                            milestone.type === 'end' ? 'bg-muted-foreground/10' :
                            milestone.type === 'crediting' ? 'bg-primary/10' :
                            'bg-yellow-500/10'
                          }`}>
                            {formatDate(milestone.date)}
                          </Badge>
                        </div>
                        <h4 className="text-sm font-medium">{milestone.title}</h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {milestone.type === 'start' && "Project implementation begins"}
                          {milestone.type === 'end' && "Project activities conclude"}
                          {milestone.type === 'crediting' && "End of carbon credit issuance period"}
                          {milestone.type === 'verification' && "Scheduled verification and monitoring report"}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border border-dashed rounded-md">
                  <CalendarClock className="h-10 w-10 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                    Enter start and end dates to see key project milestones. Milestones include project start, verifications, crediting period end, and project end.
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <div className="mt-4 text-sm text-muted-foreground">
        <p className="flex items-center">
          <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
          The crediting period may be limited by the standard you've selected. Check their requirements for details. Most standards require regular verification every 2-3 years.
        </p>
      </div>
    </div>
  );
}
