"use client";

import React, { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Wind,
  Trees,
  Recycle,
  Factory,
  Droplets,
  Leaf,
  Building,
  Info,
  Check,
  AlertCircle,
  FileText,
  Lightbulb,
  Globe,
  TrendingUp
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Define project types with icons and descriptions
const PROJECT_TYPES = [
  {
    id: "RENEWABLE_ENERGY",
    name: "Renewable Energy",
    icon: <Wind className="h-5 w-5" />,
    description: "Projects that generate electricity from renewable sources like solar, wind, or hydro.",
    examples: "Solar farms, wind turbines, hydroelectric plants",
    methodologies: ["Gold Standard", "Verra VCS", "CDM"],
    requirements: ["Grid connection details", "Baseline energy mix", "Emission reduction calculations"],
    popularity: "High",
    complexity: "Medium",
    timeToVerification: "6-12 months",
    marketDemand: "High",
    averagePrice: "$10-20 per credit"
  },
  {
    id: "FORESTRY",
    name: "Forestry & Conservation",
    icon: <Trees className="h-5 w-5" />,
    description: "Projects that protect existing forests or reforest degraded land.",
    examples: "REDD+ projects, reforestation, avoided deforestation",
    methodologies: ["Verra VCS", "Gold Standard", "Plan Vivo"],
    requirements: ["Land ownership documentation", "Forest inventory data", "Satellite imagery"],
    popularity: "Very High",
    complexity: "High",
    timeToVerification: "12-24 months",
    marketDemand: "Very High",
    averagePrice: "$15-25 per credit"
  },
  {
    id: "WASTE_MANAGEMENT",
    name: "Waste Management",
    icon: <Recycle className="h-5 w-5" />,
    description: "Projects that reduce emissions from waste through recycling, composting, or methane capture.",
    examples: "Landfill gas capture, waste-to-energy, composting",
    methodologies: ["CDM", "Verra VCS", "Gold Standard"],
    requirements: ["Waste composition data", "Methane generation potential", "Energy generation details"],
    popularity: "Medium",
    complexity: "Medium",
    timeToVerification: "8-14 months",
    marketDemand: "Medium",
    averagePrice: "$8-15 per credit"
  },
  {
    id: "INDUSTRIAL",
    name: "Industrial Processes",
    icon: <Factory className="h-5 w-5" />,
    description: "Projects that reduce emissions from industrial processes through efficiency or fuel switching.",
    examples: "Cement production, refrigerant management, fuel switching",
    methodologies: ["CDM", "Verra VCS", "Climate Action Reserve"],
    requirements: ["Process flow diagrams", "Energy consumption data", "Emission reduction calculations"],
    popularity: "Medium",
    complexity: "High",
    timeToVerification: "10-18 months",
    marketDemand: "Medium",
    averagePrice: "$12-22 per credit"
  },
  {
    id: "WATER",
    name: "Water Management",
    icon: <Droplets className="h-5 w-5" />,
    description: "Projects that improve water quality, access, or efficiency.",
    examples: "Water purification, irrigation efficiency, watershed management",
    methodologies: ["Gold Standard", "Verra VCS", "CDM"],
    requirements: ["Water quality data", "Flow measurements", "Community impact assessment"],
    popularity: "Low",
    complexity: "Medium",
    timeToVerification: "8-16 months",
    marketDemand: "Low",
    averagePrice: "$10-18 per credit"
  },
  {
    id: "AGRICULTURE",
    name: "Sustainable Agriculture",
    icon: <Leaf className="h-5 w-5" />,
    description: "Projects that reduce emissions from agriculture through improved practices.",
    examples: "Improved rice cultivation, livestock management, soil carbon sequestration",
    methodologies: ["Verra VCS", "Gold Standard", "Plan Vivo"],
    requirements: ["Land use history", "Soil carbon measurements", "Agricultural practice documentation"],
    popularity: "Growing",
    complexity: "Medium",
    timeToVerification: "12-20 months",
    marketDemand: "Growing",
    averagePrice: "$12-20 per credit"
  },
  {
    id: "COMMUNITY",
    name: "Community Development",
    icon: <Building className="h-5 w-5" />,
    description: "Projects that reduce emissions while providing community benefits.",
    examples: "Clean cookstoves, water purification, rural electrification",
    methodologies: ["Gold Standard", "Verra VCS", "CDM"],
    requirements: ["Community engagement plan", "Social impact assessment", "Stakeholder consultation records"],
    popularity: "High",
    complexity: "Medium",
    timeToVerification: "8-14 months",
    marketDemand: "High",
    averagePrice: "$12-25 per credit"
  }
];

export function ProjectTypeStep() {
  const { control, watch, setValue } = useFormContext();
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "details">("grid");

  const projectType = watch("projectType");

  // Set selected type from form value
  useEffect(() => {
    if (projectType && !selectedType) {
      setSelectedType(projectType);
    }
  }, [projectType, selectedType]);

  const handleTypeSelect = (typeId: string) => {
    setSelectedType(typeId);
    setValue("projectType", typeId, { shouldValidate: true });
  };

  const selectedTypeDetails = PROJECT_TYPES.find(type => type.id === selectedType);

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Project Type Selection</h3>
        <p className="text-muted-foreground">
          Choose the type of carbon project you're creating. This will determine the methodologies and requirements for your project. Each project type has different verification processes, market demand, and pricing potential.
        </p>
        <div className="mt-4 p-4 bg-muted/50 rounded-md border">
          <h4 className="text-sm font-medium mb-2">Tips:</h4>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
            <li>Consider market demand and pricing when selecting a project type</li>
            <li>More complex project types typically have longer verification timelines</li>
            <li>Toggle between Grid and Detailed views for more information</li>
            <li>Some project types have specific documentation requirements</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-end mb-4">
        <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as "grid" | "details")}>
          <TabsList>
            <TabsTrigger value="grid">Grid View</TabsTrigger>
            <TabsTrigger value="details">Detailed View</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <FormField
        control={control}
        name="projectType"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="space-y-4"
              >
                {viewMode === "grid" ? (
                  <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                    variants={staggeredListVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    {PROJECT_TYPES.map((type) => (
                      <motion.div
                        key={type.id}
                        variants={itemVariants}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedType === type.id
                              ? 'border-2 border-primary ring-2 ring-primary/20'
                              : 'hover:border-primary/50'
                          }`}
                          onClick={() => handleTypeSelect(type.id)}
                        >
                          <CardHeader className="pb-2 relative">
                            {selectedType === type.id && (
                              <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
                                <Check className="h-4 w-4" />
                              </div>
                            )}
                            <div className="flex items-center space-x-2">
                              <div className={`p-2 rounded-full ${
                                selectedType === type.id ? 'bg-primary text-primary-foreground' : 'bg-muted'
                              }`}>
                                {type.icon}
                              </div>
                              <CardTitle className="text-lg">{type.name}</CardTitle>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-muted-foreground">{type.description}</p>
                            <div className="mt-2 flex flex-wrap gap-1">
                              <Badge variant="outline" className="text-xs">
                                <Globe className="h-3 w-3 mr-1" />
                                {type.popularity}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                <TrendingUp className="h-3 w-3 mr-1" />
                                {type.marketDemand}
                              </Badge>
                            </div>
                          </CardContent>
                          <CardFooter className="pt-0">
                            <RadioGroupItem
                              value={type.id}
                              id={type.id}
                              className="sr-only"
                            />
                          </CardFooter>
                          <div className={`h-1 w-full mt-auto ${
                            selectedType === type.id ? 'bg-primary' : 'bg-transparent'
                          }`}></div>
                        </Card>
                      </motion.div>
                    ))}
                  </motion.div>
                ) : (
                  <div className="space-y-4">
                    {PROJECT_TYPES.map((type) => (
                      <motion.div
                        key={type.id}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <Card
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedType === type.id
                              ? 'border-2 border-primary ring-2 ring-primary/20'
                              : 'hover:border-primary/50'
                          }`}
                          onClick={() => handleTypeSelect(type.id)}
                        >
                          <CardHeader className="pb-2 relative">
                            {selectedType === type.id && (
                              <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
                                <Check className="h-4 w-4" />
                              </div>
                            )}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <div className={`p-2 rounded-full ${
                                  selectedType === type.id ? 'bg-primary text-primary-foreground' : 'bg-muted'
                                }`}>
                                  {type.icon}
                                </div>
                                <CardTitle className="text-lg">{type.name}</CardTitle>
                              </div>
                              <RadioGroupItem
                                value={type.id}
                                id={type.id}
                                className="sr-only"
                              />
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm">{type.description}</p>

                            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Examples</h4>
                                <p className="text-sm text-muted-foreground">{type.examples}</p>
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Common Methodologies</h4>
                                <ul className="text-sm text-muted-foreground list-disc pl-4">
                                  {type.methodologies.map((methodology, index) => (
                                    <li key={index}>{methodology}</li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Market Information</h4>
                                <div className="space-y-1">
                                  <p className="text-sm text-muted-foreground">Popularity: {type.popularity}</p>
                                  <p className="text-sm text-muted-foreground">Market Demand: {type.marketDemand}</p>
                                  <p className="text-sm text-muted-foreground">Average Price: {type.averagePrice}</p>
                                </div>
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Key Requirements</h4>
                                <ul className="text-sm text-muted-foreground list-disc pl-4">
                                  {type.requirements.map((requirement, index) => (
                                    <li key={index}>{requirement}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </CardContent>
                          <div className={`h-1 w-full mt-auto ${
                            selectedType === type.id ? 'bg-primary' : 'bg-transparent'
                          }`}></div>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                )}
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {selectedTypeDetails && (
        <motion.div
          className="mt-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-primary/30 bg-primary/5">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="p-2 rounded-full bg-primary text-primary-foreground mr-2">
                    {selectedTypeDetails.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">Selected: {selectedTypeDetails.name}</CardTitle>
                    <CardDescription>{selectedTypeDetails.description}</CardDescription>
                  </div>
                </div>
                <Check className="h-5 w-5 text-primary" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Verification Time</span>
                  <Badge variant="outline" className="mt-1 w-fit">
                    {selectedTypeDetails.timeToVerification}
                  </Badge>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Complexity</span>
                  <Badge variant="outline" className="mt-1 w-fit">
                    {selectedTypeDetails.complexity}
                  </Badge>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Market Demand</span>
                  <Badge variant="outline" className="mt-1 w-fit">
                    {selectedTypeDetails.marketDemand}
                  </Badge>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Average Price</span>
                  <Badge variant="outline" className="mt-1 w-fit">
                    {selectedTypeDetails.averagePrice}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
