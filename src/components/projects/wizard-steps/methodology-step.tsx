"use client";

import { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import { AnimatedInput } from "@/components/ui/animated";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  FileCheck,
  Globe,
  Info,
  HelpCircle,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Search,
  Filter,
  BookOpen,
  Clock,
  Check
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Define project standards with descriptions
const PROJECT_STANDARDS = [
  {
    id: "VERRA",
    name: "Verra VCS",
    description: "Verified Carbon Standard by Verra, one of the most widely used GHG crediting programs.",
    website: "https://verra.org/programs/verified-carbon-standard/",
    popularity: "Very High",
    rigor: "High",
    acceptanceRate: "Medium",
    costRange: "$$-$$$",
    timeframe: "12-18 months"
  },
  {
    id: "GOLD_STANDARD",
    name: "Gold Standard",
    description: "Certification for projects that reduce carbon emissions while contributing to sustainable development.",
    website: "https://www.goldstandard.org/",
    popularity: "High",
    rigor: "Very High",
    acceptanceRate: "Medium-Low",
    costRange: "$$$",
    timeframe: "12-24 months"
  },
  {
    id: "CDM",
    name: "Clean Development Mechanism",
    description: "UN-run carbon offset scheme under the Kyoto Protocol.",
    website: "https://cdm.unfccc.int/",
    popularity: "Medium",
    rigor: "High",
    acceptanceRate: "Low",
    costRange: "$$-$$$",
    timeframe: "18-36 months"
  },
  {
    id: "AMERICAN_CARBON_REGISTRY",
    name: "American Carbon Registry",
    description: "The first private voluntary GHG registry in the United States.",
    website: "https://americancarbonregistry.org/",
    popularity: "Medium",
    rigor: "High",
    acceptanceRate: "Medium",
    costRange: "$$-$$$",
    timeframe: "12-18 months"
  },
  {
    id: "CLIMATE_ACTION_RESERVE",
    name: "Climate Action Reserve",
    description: "Program for the North American carbon market.",
    website: "https://www.climateactionreserve.org/",
    popularity: "Medium",
    rigor: "High",
    acceptanceRate: "Medium",
    costRange: "$$-$$$",
    timeframe: "12-18 months"
  },
  {
    id: "PLAN_VIVO",
    name: "Plan Vivo",
    description: "Standard for community land use projects with a focus on livelihoods.",
    website: "https://www.planvivo.org/",
    popularity: "Low-Medium",
    rigor: "Medium",
    acceptanceRate: "Medium-High",
    costRange: "$$",
    timeframe: "12-24 months"
  },
  {
    id: "OTHER",
    name: "Other",
    description: "Other recognized carbon standards or methodologies.",
    website: "",
    popularity: "Varies",
    rigor: "Varies",
    acceptanceRate: "Varies",
    costRange: "Varies",
    timeframe: "Varies"
  }
];

// Mock methodologies by standard
const METHODOLOGIES_BY_STANDARD: Record<string, any[]> = {
  "VERRA": [
    { id: "VM0006", name: "VM0006 - Carbon Accounting for Mosaic Deforestation", projectTypes: ["FORESTRY"], complexity: "High" },
    { id: "VM0007", name: "VM0007 - REDD+ Methodology Framework", projectTypes: ["FORESTRY"], complexity: "High" },
    { id: "VM0009", name: "VM0009 - Avoided Ecosystem Conversion", projectTypes: ["FORESTRY"], complexity: "High" },
    { id: "VM0015", name: "VM0015 - Avoided Unplanned Deforestation", projectTypes: ["FORESTRY"], complexity: "High" },
    { id: "VM0017", name: "VM0017 - Adoption of Sustainable Agricultural Land Management", projectTypes: ["AGRICULTURE"], complexity: "Medium" },
    { id: "VM0025", name: "VM0025 - Campus Clean Energy and Energy Efficiency", projectTypes: ["RENEWABLE_ENERGY"], complexity: "Medium" },
    { id: "VM0026", name: "VM0026 - Sustainable Grassland Management", projectTypes: ["AGRICULTURE"], complexity: "Medium" },
    { id: "VM0032", name: "VM0032 - Adoption of Sustainable Grasslands through Adjustment of Fire and Grazing", projectTypes: ["AGRICULTURE"], complexity: "Medium" },
    { id: "VM0033", name: "VM0033 - Methodology for Tidal Wetland and Seagrass Restoration", projectTypes: ["WATER"], complexity: "High" },
    { id: "VM0036", name: "VM0036 - Methodology for Rewetting Drained Temperate Peatlands", projectTypes: ["WATER"], complexity: "High" },
  ],
  "GOLD_STANDARD": [
    { id: "GS_RE", name: "GS Renewable Energy Methodology", projectTypes: ["RENEWABLE_ENERGY"], complexity: "Medium" },
    { id: "GS_EE", name: "GS Energy Efficiency Methodology", projectTypes: ["INDUSTRIAL"], complexity: "Medium" },
    { id: "GS_WM", name: "GS Waste Management Methodology", projectTypes: ["WASTE_MANAGEMENT"], complexity: "Medium" },
    { id: "GS_CS", name: "GS Simplified Methodology for Efficient Cookstoves", projectTypes: ["COMMUNITY"], complexity: "Low" },
    { id: "GS_WS", name: "GS Water Supply and Treatment Methodology", projectTypes: ["WATER"], complexity: "Medium" },
    { id: "GS_LU", name: "GS Land Use and Forests Methodology", projectTypes: ["FORESTRY", "AGRICULTURE"], complexity: "High" },
  ],
  // Add more methodologies for other standards
};

export function MethodologyStep() {
  const { control, watch, setValue } = useFormContext();
  const [methodologies, setMethodologies] = useState<any[]>([]);
  const [filteredMethodologies, setFilteredMethodologies] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"list" | "details">("list");

  const projectType = watch("projectType");
  const standard = watch("standard");

  // Load methodologies based on selected standard
  useEffect(() => {
    if (standard) {
      const standardMethodologies = METHODOLOGIES_BY_STANDARD[standard] || [];
      setMethodologies(standardMethodologies);
      setFilteredMethodologies(standardMethodologies);
    } else {
      setMethodologies([]);
      setFilteredMethodologies([]);
    }
  }, [standard]);

  // Filter methodologies based on search query and project type
  useEffect(() => {
    let filtered = methodologies;

    if (searchQuery) {
      filtered = filtered.filter(m =>
        m.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (projectType) {
      filtered = filtered.filter(m =>
        m.projectTypes.includes(projectType)
      );
    }

    setFilteredMethodologies(filtered);
  }, [searchQuery, projectType, methodologies]);

  // Get selected standard details
  const selectedStandard = PROJECT_STANDARDS.find(s => s.id === standard);

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Methodology Selection",
          description: "Choose the carbon standard and methodology for your project. This determines how your emission reductions will be measured and verified.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/projects/methodologies",
          learnMoreText: "Learn more about carbon standards and methodologies",
        }}
        className="mb-6"
      />

      <motion.div
        className="space-y-6"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="standard"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Carbon Standard
                  <InlineHelp
                    content={{
                      title: "Carbon Standard",
                      description: "The standard organization that will verify and issue credits for your project. Different standards have different requirements, costs, and market recognition.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a carbon standard" />
                    </SelectTrigger>
                    <SelectContent>
                      {PROJECT_STANDARDS.map((standard) => (
                        <SelectItem key={standard.id} value={standard.id}>
                          {standard.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  The carbon standard that will verify and issue credits for your project.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        {selectedStandard && (
          <motion.div
            variants={itemVariants}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-primary/30 bg-primary/5">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg flex items-center">
                      <FileCheck className="h-5 w-5 mr-2 text-primary" />
                      {selectedStandard.name}
                    </CardTitle>
                    <CardDescription>{selectedStandard.description}</CardDescription>
                  </div>
                  <Check className="h-5 w-5 text-primary" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="space-y-1">
                    <p className="font-semibold flex items-center">
                      <Globe className="h-4 w-4 mr-1 text-primary" />
                      Popularity
                    </p>
                    <Badge variant="outline" className="bg-primary/10 border-primary/30">
                      {selectedStandard.popularity}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1 text-primary" />
                      Rigor
                    </p>
                    <Badge variant="outline" className="bg-primary/10 border-primary/30">
                      {selectedStandard.rigor}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1 text-primary" />
                      Acceptance Rate
                    </p>
                    <Badge variant="outline" className="bg-primary/10 border-primary/30">
                      {selectedStandard.acceptanceRate}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-primary" />
                      Timeframe
                    </p>
                    <Badge variant="outline" className="bg-primary/10 border-primary/30">
                      {selectedStandard.timeframe}
                    </Badge>
                  </div>
                </div>
              </CardContent>
              {selectedStandard.website && (
                <CardFooter>
                  <Button variant="outline" size="sm" asChild className="hover:bg-primary/10 hover:text-primary">
                    <a href={selectedStandard.website} target="_blank" rel="noopener noreferrer">
                      Visit Website
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </CardFooter>
              )}
            </Card>
          </motion.div>
        )}

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="methodology"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Methodology
                  <InlineHelp
                    content={{
                      title: "Methodology",
                      description: "The specific methodology that defines how emission reductions will be measured for your project type. Choose one that matches your project activities.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <div className="flex items-center space-x-2 mb-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search methodologies..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as "list" | "details")}>
                    <TabsList>
                      <TabsTrigger value="list">List</TabsTrigger>
                      <TabsTrigger value="details">Details</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={!standard || filteredMethodologies.length === 0}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={
                        !standard
                          ? "Select a standard first"
                          : filteredMethodologies.length === 0
                          ? "No methodologies available"
                          : "Select a methodology"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredMethodologies.map((methodology) => (
                        <SelectItem key={methodology.id} value={methodology.id}>
                          {methodology.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  The specific methodology that defines how emission reductions will be measured.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        {viewMode === "details" && filteredMethodologies.length > 0 && (
          <motion.div variants={itemVariants}>
            <div className="grid grid-cols-1 gap-4 mt-4">
              {filteredMethodologies.map((methodology) => (
                <motion.div
                  key={methodology.id}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <Card
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      watch("methodology") === methodology.id
                        ? 'border-2 border-primary ring-2 ring-primary/20'
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => setValue("methodology", methodology.id, { shouldValidate: true })}
                  >
                    <CardHeader className="pb-2 relative">
                      {watch("methodology") === methodology.id && (
                        <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
                          <Check className="h-4 w-4" />
                        </div>
                      )}
                      <CardTitle className="text-md">{methodology.name}</CardTitle>
                      <div className="flex flex-wrap gap-1 mt-1">
                        <Badge variant="outline" className="text-xs">
                          Complexity: {methodology.complexity}
                        </Badge>
                        {methodology.projectTypes.map((type: string) => (
                          <Badge
                            key={type}
                            variant="outline"
                            className={`text-xs ${type === projectType ? 'bg-primary/10 border-primary/30' : ''}`}
                          >
                            {type.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </CardHeader>
                    <div className={`h-1 w-full mt-auto ${
                      watch("methodology") === methodology.id ? 'bg-primary' : 'bg-transparent'
                    }`}></div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="validationBody"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Validation Body (Optional)
                  <InlineHelp
                    content={{
                      title: "Validation Body",
                      description: "The third-party organization that will validate your project. You can select this later if you haven't decided yet.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <AnimatedInput
                    {...field}
                    placeholder="e.g., DNV GL, SCS Global Services"
                  />
                </FormControl>
                <FormDescription>
                  The third-party organization that will validate your project.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
      </motion.div>

      <div className="mt-4 text-sm text-muted-foreground">
        <p className="flex items-center">
          <BookOpen className="h-4 w-4 mr-2" />
          Need help choosing a methodology? <a href="#" className="ml-1 underline">View our methodology guide</a>
        </p>
      </div>
    </div>
  );
}
