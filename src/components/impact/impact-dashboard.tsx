"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Calendar,
  Download,
  Share2,
  Filter,
  ArrowUpDown,
  Leaf,
  Globe,
  Wind,
  Droplets,
  TreePine,
  Factory,
  Truck,
  Building,
  Users,
  User,
  ShieldCheck,
  Info
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Retirement record interface
interface RetirementRecord {
  id: string;
  creditId: string;
  creditName: string;
  quantity: number;
  purpose: string;
  beneficiary: string;
  retirementDate: string;
  projectId?: string;
  projectName?: string;
  projectType?: string;
  standard?: string;
  methodology?: string;
  vintage?: number;
}

// Impact metrics interface
interface ImpactMetrics {
  totalRetired: number;
  byPurpose: Record<string, number>;
  byProject: Record<string, number>;
  byProjectType: Record<string, number>;
  byMonth: Record<string, number>;
  byBeneficiary: Record<string, number>;
}

// Impact dashboard props
interface ImpactDashboardProps {
  retirements: RetirementRecord[];
  onExport?: () => void;
  onShare?: () => void;
}

// Purpose icons mapping
const PURPOSE_ICONS: Record<string, React.ReactNode> = {
  corporate_offsetting: <Users className="h-4 w-4" />,
  product_offsetting: <ShieldCheck className="h-4 w-4" />,
  event_offsetting: <Calendar className="h-4 w-4" />,
  personal_offsetting: <User className="h-4 w-4" />,
  environmental_contribution: <Leaf className="h-4 w-4" />,
  other: <Globe className="h-4 w-4" />,
};

// Project type icons mapping
const PROJECT_TYPE_ICONS: Record<string, React.ReactNode> = {
  RENEWABLE_ENERGY: <Wind className="h-4 w-4" />,
  FORESTRY: <TreePine className="h-4 w-4" />,
  METHANE_REDUCTION: <Factory className="h-4 w-4" />,
  ENERGY_EFFICIENCY: <Factory className="h-4 w-4" />,
  WASTE_MANAGEMENT: <Factory className="h-4 w-4" />,
  AGRICULTURE: <Leaf className="h-4 w-4" />,
  TRANSPORTATION: <Truck className="h-4 w-4" />,
  INDUSTRIAL: <Factory className="h-4 w-4" />,
  OTHER: <Building className="h-4 w-4" />,
};

export function ImpactDashboard({
  retirements,
  onExport,
  onShare
}: ImpactDashboardProps) {
  const [timeRange, setTimeRange] = useState<string>("all");
  const [groupBy, setGroupBy] = useState<string>("purpose");
  const [activeTab, setActiveTab] = useState<string>("overview");

  // Filter retirements by time range
  const filteredRetirements = retirements.filter(retirement => {
    if (timeRange === "all") return true;

    const retirementDate = new Date(retirement.retirementDate);
    const now = new Date();

    switch (timeRange) {
      case "month":
        const monthAgo = new Date();
        monthAgo.setMonth(now.getMonth() - 1);
        return retirementDate >= monthAgo;
      case "quarter":
        const quarterAgo = new Date();
        quarterAgo.setMonth(now.getMonth() - 3);
        return retirementDate >= quarterAgo;
      case "year":
        const yearAgo = new Date();
        yearAgo.setFullYear(now.getFullYear() - 1);
        return retirementDate >= yearAgo;
      default:
        return true;
    }
  });

  // Calculate impact metrics
  const calculateImpactMetrics = (): ImpactMetrics => {
    const metrics: ImpactMetrics = {
      totalRetired: 0,
      byPurpose: {},
      byProject: {},
      byProjectType: {},
      byMonth: {},
      byBeneficiary: {},
    };

    filteredRetirements.forEach(retirement => {
      // Total retired
      metrics.totalRetired += retirement.quantity;

      // By purpose
      const purpose = retirement.purpose;
      metrics.byPurpose[purpose] = (metrics.byPurpose[purpose] || 0) + retirement.quantity;

      // By project
      if (retirement.projectId) {
        const projectKey = retirement.projectId;
        metrics.byProject[projectKey] = (metrics.byProject[projectKey] || 0) + retirement.quantity;
      }

      // By project type
      if (retirement.projectType) {
        const projectType = retirement.projectType;
        metrics.byProjectType[projectType] = (metrics.byProjectType[projectType] || 0) + retirement.quantity;
      }

      // By month
      const date = new Date(retirement.retirementDate);
      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
      metrics.byMonth[monthKey] = (metrics.byMonth[monthKey] || 0) + retirement.quantity;

      // By beneficiary
      const beneficiary = retirement.beneficiary;
      metrics.byBeneficiary[beneficiary] = (metrics.byBeneficiary[beneficiary] || 0) + retirement.quantity;
    });

    return metrics;
  };

  const impactMetrics = calculateImpactMetrics();

  // Calculate environmental impact
  const calculateEnvironmentalImpact = (totalTons: number) => {
    return {
      trees: Math.round(totalTons * 16.5), // ~16.5 trees per ton of CO2
      carMiles: Math.round(totalTons * 2481), // ~2,481 miles per ton of CO2
      homeEnergy: Math.round(totalTons * 1.21), // ~1.21 months of home energy per ton of CO2
      flightMiles: Math.round(totalTons * 2325), // ~2,325 flight miles per ton of CO2
    };
  };

  const environmentalImpact = calculateEnvironmentalImpact(impactMetrics.totalRetired);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Format month key
  const formatMonthKey = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);
    return date.toLocaleDateString("en-US", { month: "short", year: "numeric" });
  };

  // Get purpose label
  const getPurposeLabel = (purposeKey: string) => {
    switch (purposeKey) {
      case "corporate_offsetting":
        return "Corporate Offsetting";
      case "product_offsetting":
        return "Product Neutrality";
      case "event_offsetting":
        return "Event Offsetting";
      case "personal_offsetting":
        return "Personal Offsetting";
      case "environmental_contribution":
        return "Environmental Contribution";
      case "other":
        return "Other Purpose";
      default:
        return purposeKey;
    }
  };

  // Get purpose icon
  const getPurposeIcon = (purposeKey: string) => {
    return PURPOSE_ICONS[purposeKey] || <Globe className="h-4 w-4" />;
  };

  // Get project type icon
  const getProjectTypeIcon = (typeKey: string) => {
    return PROJECT_TYPE_ICONS[typeKey] || <Building className="h-4 w-4" />;
  };

  // Render overview tab
  const renderOverviewTab = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Total Carbon Retired</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{impactMetrics.totalRetired.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">tons CO₂e</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Equivalent to Trees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{environmentalImpact.trees.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">trees for 1 year</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Car Miles Avoided</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{environmentalImpact.carMiles.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">miles</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Home Energy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{environmentalImpact.homeEnergy.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">months</div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Retirement by Purpose</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(impactMetrics.byPurpose)
                  .sort(([, a], [, b]) => b - a)
                  .map(([purpose, quantity]) => {
                    const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                    return (
                      <div key={purpose} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center">
                            {getPurposeIcon(purpose)}
                            <span className="ml-2">{getPurposeLabel(purpose)}</span>
                          </div>
                          <span className="font-medium">{quantity.toLocaleString()} tons</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Progress value={percentage} className="h-2 flex-1" />
                          <span className="text-xs font-medium">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Retirement by Project Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(impactMetrics.byProjectType)
                  .sort(([, a], [, b]) => b - a)
                  .map(([projectType, quantity]) => {
                    const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                    return (
                      <div key={projectType} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center">
                            {getProjectTypeIcon(projectType)}
                            <span className="ml-2">{projectType.replace(/_/g, " ")}</span>
                          </div>
                          <span className="font-medium">{quantity.toLocaleString()} tons</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Progress value={percentage} className="h-2 flex-1" />
                          <span className="text-xs font-medium">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Retirement Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(impactMetrics.byMonth)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([month, quantity]) => {
                  const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                  return (
                    <div key={month} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>{formatMonthKey(month)}</span>
                        <span className="font-medium">{quantity.toLocaleString()} tons</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Top Beneficiaries</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(impactMetrics.byBeneficiary)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([beneficiary, quantity]) => {
                  const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                  return (
                    <div key={beneficiary} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{beneficiary}</span>
                        <span>{quantity.toLocaleString()} tons</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Render details tab
  const renderDetailsTab = () => {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Recent Retirements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredRetirements
                .sort((a, b) => new Date(b.retirementDate).getTime() - new Date(a.retirementDate).getTime())
                .slice(0, 10)
                .map(retirement => (
                  <div key={retirement.id} className="flex items-center justify-between p-3 border rounded-md">
                    <div className="flex items-center">
                      {getPurposeIcon(retirement.purpose)}
                      <div className="ml-3">
                        <div className="font-medium">{retirement.creditName}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatDate(retirement.retirementDate)} • {getPurposeLabel(retirement.purpose)}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{retirement.quantity.toLocaleString()} tons</div>
                      <div className="text-xs text-muted-foreground">
                        {retirement.beneficiary}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Retirement by Project</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(impactMetrics.byProject)
                .sort(([, a], [, b]) => b - a)
                .map(([projectId, quantity]) => {
                  const project = filteredRetirements.find(r => r.projectId === projectId);
                  const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                  if (!project) return null;

                  return (
                    <div key={projectId} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          {project.projectType && getProjectTypeIcon(project.projectType)}
                          <span className="ml-2">{project.projectName || "Unknown Project"}</span>
                        </div>
                        <span className="font-medium">{quantity.toLocaleString()} tons</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Progress value={percentage} className="h-2 flex-1" />
                        <span className="text-xs font-medium">{percentage}%</span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Retirement by Standard</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredRetirements.reduce((acc, retirement) => {
                  if (retirement.standard) {
                    acc[retirement.standard] = (acc[retirement.standard] || 0) + retirement.quantity;
                  }
                  return acc;
                }, {} as Record<string, number>)
                  .entries()
                  .sort(([, a], [, b]) => b - a)
                  .map(([standard, quantity]) => {
                    const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                    return (
                      <div key={standard} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{standard}</span>
                          <span className="font-medium">{quantity.toLocaleString()} tons</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Retirement by Vintage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredRetirements.reduce((acc, retirement) => {
                  if (retirement.vintage) {
                    acc[retirement.vintage.toString()] = (acc[retirement.vintage.toString()] || 0) + retirement.quantity;
                  }
                  return acc;
                }, {} as Record<string, number>)
                  .entries()
                  .sort(([a], [b]) => parseInt(a) - parseInt(b))
                  .map(([vintage, quantity]) => {
                    const percentage = Math.round((quantity / impactMetrics.totalRetired) * 100);

                    return (
                      <div key={vintage} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{vintage}</span>
                          <span className="font-medium">{quantity.toLocaleString()} tons</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  // Render certificates tab
  const renderCertificatesTab = () => {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Retirement Certificates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredRetirements
                .sort((a, b) => new Date(b.retirementDate).getTime() - new Date(a.retirementDate).getTime())
                .map(retirement => (
                  <div key={retirement.id} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="flex items-center">
                      <div className="bg-muted/50 p-2 rounded-md mr-3">
                        <FileText className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div>
                        <div className="font-medium">
                          {retirement.beneficiary} - {retirement.quantity.toLocaleString()} tons
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatDate(retirement.retirementDate)} • {getPurposeLabel(retirement.purpose)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Impact Dashboard</CardTitle>
            <CardDescription>
              Track the environmental impact of your carbon credit retirements
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={onShare}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">
                <PieChart className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="details">
                <BarChart className="h-4 w-4 mr-2" />
                Details
              </TabsTrigger>
              <TabsTrigger value="certificates">
                <FileText className="h-4 w-4 mr-2" />
                Certificates
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex items-center space-x-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="year">Past Year</SelectItem>
                <SelectItem value="quarter">Past Quarter</SelectItem>
                <SelectItem value="month">Past Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === "overview" && renderOverviewTab()}
            {activeTab === "details" && renderDetailsTab()}
            {activeTab === "certificates" && renderCertificatesTab()}
          </motion.div>
        </AnimatePresence>
      </CardContent>
    </AnimatedCard>
  );
