"use client";

import { useState } from "react";
import { useRout<PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { Send, Loader2 } from "lucide-react";
import { TransactionStatus } from "@/components/blockchain/transaction-status";

// Schema for transfer form
const transferFormSchema = z.object({
  toAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, "Invalid Ethereum address"),
  amount: z.coerce.number().positive("Amount must be positive"),
});

type TransferFormValues = z.infer<typeof transferFormSchema>;

interface EnhancedTransferDialogProps {
  carbonCreditId: string;
  maxAmount: number;
  network: string;
  isTestnet: boolean;
  disabled?: boolean;
  onSuccess?: () => void;
}

export function EnhancedTransferDialog({
  carbonCreditId,
  maxAmount,
  network,
  isTestnet,
  disabled = false,
  onSuccess,
}: EnhancedTransferDialogProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [gasEstimation, setGasEstimation] = useState<any>(null);
  const [isEstimatingGas, setIsEstimatingGas] = useState(false);
  const [currentStep, setCurrentStep] = useState<"form" | "processing" | "complete">("form");
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  // Initialize form
  const form = useForm<TransferFormValues>({
    resolver: zodResolver(transferFormSchema),
    defaultValues: {
      toAddress: "",
      amount: 1,
    },
  });

  // Estimate gas when form values change
  const estimateGas = async (data: TransferFormValues) => {
    if (!data.toAddress || !data.amount) return;

    // Check if amount is greater than max amount
    if (data.amount > maxAmount) return;

    setIsEstimatingGas(true);
    setGasEstimation(null);

    try {
      const response = await fetch(`/api/gas-estimation/transfer`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          carbonCreditId,
          toAddress: data.toAddress,
          amount: data.amount,
          network,
          useTestnet: isTestnet,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to estimate gas");
      }

      const result = await response.json();
      setGasEstimation(result);
    } catch (error) {
      console.error("Error estimating gas:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to estimate gas",
        variant: "destructive",
      });
    } finally {
      setIsEstimatingGas(false);
    }
  };

  // Watch form values and estimate gas when they change
  const watchToAddress = form.watch("toAddress");
  const watchAmount = form.watch("amount");

  // Update gas estimation when form values change
  const updateGasEstimation = async () => {
    const data = {
      toAddress: watchToAddress,
      amount: watchAmount,
    };

    // Only estimate if we have a valid address and amount
    if (data.toAddress && data.toAddress.match(/^0x[a-fA-F0-9]{40}$/) && data.amount > 0 && data.amount <= maxAmount) {
      await estimateGas(data);
    }
  };

  // Form submission handler
  const onSubmit = async (data: TransferFormValues) => {
    setIsSubmitting(true);
    setCurrentStep("processing");

    try {
      const response = await fetch(`/api/carbon-credits/${carbonCreditId}/transfer`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toAddress: data.toAddress,
          amount: data.amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to transfer carbon credit");
      }

      const result = await response.json();

      // Set transaction hash for tracking
      if (result.transactionHash) {
        setTransactionHash(result.transactionHash);
      } else {
        throw new Error("No transaction hash returned");
      }

      // Don't close dialog yet - we'll show the transaction status
    } catch (error) {
      console.error("Error transferring carbon credit:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to transfer carbon credit",
        variant: "destructive",
      });

      // Go back to form step on error
      setCurrentStep("form");
      setIsSubmitting(false);
    }
  };

  // Handle transaction completion
  const handleTransactionComplete = (success: boolean) => {
    setIsSubmitting(false);
    setCurrentStep("complete");

    if (success) {
      toast({
        title: "Carbon Credit Transferred",
        description: `${watchAmount} tons of carbon credit has been successfully transferred to ${watchToAddress}.`,
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh();
      }, 2000);
    } else {
      toast({
        title: "Transfer Failed",
        description: "The transaction failed or was dropped. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle dialog close
  const handleDialogClose = (open: boolean) => {
    // Prevent closing while submitting
    if (isSubmitting && open === false) {
      toast({
        title: "Transaction in Progress",
        description: "Please wait for the transaction to complete before closing.",
      });
      return;
    }

    // Reset state when closing
    if (!open) {
      // Only reset if not in the middle of a transaction
      if (!isSubmitting) {
        setCurrentStep("form");
        setTransactionHash(null);
        form.reset({
          toAddress: "",
          amount: 1,
        });
      }
    }

    setIsOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>
        <Button disabled={disabled || maxAmount <= 0} variant="outline" size="sm">
          <Send className="mr-2 h-4 w-4" />
          Transfer
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        {currentStep === "form" && (
          <>
            <DialogHeader>
              <DialogTitle>Transfer Carbon Credit</DialogTitle>
              <DialogDescription>
                Transfer your tokenized carbon credit to another wallet address.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="toAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient Address</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="0x..."
                          {...field}
                          onChange={(e) => {
                            field.onChange(e);
                            // Estimate gas after a short delay
                            setTimeout(updateGasEstimation, 500);
                          }}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter the Ethereum address of the recipient.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount (tons)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={maxAmount}
                          step={1}
                          {...field}
                          onChange={(e) => {
                            field.onChange(e);
                            // Estimate gas after a short delay
                            setTimeout(updateGasEstimation, 500);
                          }}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter the amount of carbon credits to transfer. Maximum: {maxAmount} tons.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {gasEstimation && (
                  <div className="rounded-lg border p-4 space-y-2">
                    <h4 className="font-medium">Gas Estimation</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Gas Price:</span>
                        <span>{parseFloat(gasEstimation.gasPrice).toFixed(2)} Gwei</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Gas Limit:</span>
                        <span>{parseInt(gasEstimation.gasLimit).toLocaleString()}</span>
                      </div>
                      <Separator className="my-2" />
                      <div className="flex justify-between font-medium">
                        <span>Estimated Cost:</span>
                        <span>
                          {gasEstimation.costInEther.toFixed(6)}{" "}
                          {network === "ethereum" || network === "optimism" || network === "arbitrum" || network === "base"
                            ? "ETH"
                            : network === "polygon"
                            ? "MATIC"
                            : ""}
                        </span>
                      </div>
                      {gasEstimation.usdCost && (
                        <div className="flex justify-between text-muted-foreground">
                          <span>INR Equivalent:</span>
                          <span>${gasEstimation.usdCost.toFixed(2)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || isEstimatingGas}
                    className="gap-1"
                  >
                    {isEstimatingGas ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Estimating Gas...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4" />
                        Transfer
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}

        {currentStep === "processing" && transactionHash && (
          <>
            <DialogHeader>
              <DialogTitle>Transferring Carbon Credit</DialogTitle>
              <DialogDescription>
                Your carbon credit is being transferred on the blockchain. This process may take a few minutes.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <TransactionStatus
                txHash={transactionHash}
                network={network}
                isTestnet={isTestnet}
                onComplete={handleTransactionComplete}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={true}
              >
                Please Wait...
              </Button>
            </DialogFooter>
          </>
        )}

        {currentStep === "complete" && (
          <>
            <DialogHeader>
              <DialogTitle>Transfer Complete</DialogTitle>
              <DialogDescription>
                Your carbon credit has been transferred on the blockchain.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
                <Send className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium">Successfully Transferred</h3>
              <p className="text-sm text-muted-foreground mt-2">
                {watchAmount} tons of carbon credit has been transferred to {watchToAddress.slice(0, 6)}...{watchToAddress.slice(-4)}.
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                onClick={() => setIsOpen(false)}
              >
                Close
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
