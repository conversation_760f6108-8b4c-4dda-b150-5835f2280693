'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

// Schema for batch operations
const batchOperationSchema = z.object({
  operation: z.enum(['verify', 'list', 'retire', 'update-price', 'update-status', 'update-metadata']),
  // Additional fields will be conditionally validated based on the operation
});

// Verification schema
const verificationSchema = z.object({
  operation: z.literal('verify'),
  type: z.enum(['INITIAL', 'ANNUAL', 'METHODOLOGY', 'OWNERSHIP', 'RETIREMENT', 'BATCH']),
  documents: z.array(z.string().url('Invalid document URL')).min(1, 'At least one document is required'),
  notes: z.string().optional(),
});

// Listing schema
const listingSchema = z.object({
  operation: z.literal('list'),
  listingDate: z.string().optional(),
});

// Retirement schema
const retirementSchema = z.object({
  operation: z.literal('retire'),
  reason: z.string().min(1, 'Retirement reason is required'),
  beneficiary: z.string().optional(),
  retirementDate: z.string().optional(),
});

// Price update schema
const priceUpdateSchema = z.object({
  operation: z.literal('update-price'),
  price: z.coerce.number().positive('Price must be positive'),
  reason: z.string().optional(),
});

// Status update schema
const statusUpdateSchema = z.object({
  operation: z.literal('update-status'),
  status: z.enum(['PENDING', 'VERIFIED', 'LISTED', 'RETIRED']),
  reason: z.string().optional(),
});

// Metadata update schema
const metadataUpdateSchema = z.object({
  operation: z.literal('update-metadata'),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  vintage: z.coerce.number().int().min(2000).optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});

// Union of all operation schemas
const formSchema = z.discriminatedUnion('operation', [
  verificationSchema,
  listingSchema,
  retirementSchema,
  priceUpdateSchema,
  statusUpdateSchema,
  metadataUpdateSchema,
]);

type BatchOperationFormValues = z.infer<typeof formSchema>;

interface BatchOperationsProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCreditIds: string[];
  onSuccess?: () => void;
}

export function BatchOperations({
  isOpen,
  onClose,
  selectedCreditIds,
  onSuccess,
}: BatchOperationsProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [documents, setDocuments] = useState<string[]>([]);
  const [documentUrl, setDocumentUrl] = useState('');

  // Initialize form
  const form = useForm<BatchOperationFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      operation: 'verify',
      type: 'INITIAL',
      documents: [],
      notes: '',
      listingDate: '',
      reason: '',
      beneficiary: '',
      retirementDate: '',
      price: 0,
      status: 'VERIFIED',
      standard: '',
      methodology: '',
      vintage: new Date().getFullYear(),
      location: '',
    },
  });

  // Watch the operation field to conditionally render form fields
  const operation = form.watch('operation');

  // Add document URL to the list
  const addDocument = () => {
    if (documentUrl && !documents.includes(documentUrl)) {
      const newDocuments = [...documents, documentUrl];
      setDocuments(newDocuments);
      form.setValue('documents', newDocuments);
      setDocumentUrl('');
    }
  };

  // Remove document from the list
  const removeDocument = (index: number) => {
    const newDocuments = documents.filter((_, i) => i !== index);
    setDocuments(newDocuments);
    form.setValue('documents', newDocuments);
  };

  // Form submission handler
  const onSubmit = async (data: BatchOperationFormValues) => {
    if (selectedCreditIds.length === 0) {
      toast({
        title: 'No Carbon Credits Selected',
        description: 'Please select at least one carbon credit to perform batch operations.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare request data based on operation
      const requestData: any = {
        operation: data.operation,
        carbonCreditIds: selectedCreditIds,
        data: {},
      };

      switch (data.operation) {
        case 'verify':
          requestData.data = {
            type: data.type,
            documents: data.documents,
            notes: data.notes,
          };
          break;
        case 'list':
          requestData.data = {
            listingDate: data.listingDate,
          };
          break;
        case 'retire':
          requestData.data = {
            reason: data.reason,
            beneficiary: data.beneficiary,
            retirementDate: data.retirementDate,
          };
          break;
        case 'update-price':
          requestData.data = {
            price: data.price,
            reason: data.reason,
          };
          break;
        case 'update-status':
          requestData.data = {
            status: data.status,
            reason: data.reason,
          };
          break;
        case 'update-metadata':
          requestData.data = {
            standard: data.standard,
            methodology: data.methodology,
            vintage: data.vintage,
            location: data.location,
            notes: data.notes,
          };
          break;
      }

      // Send request to API
      const response = await fetch('/api/carbon-credits/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to perform batch operation');
      }

      const result = await response.json();

      toast({
        title: 'Batch Operation Successful',
        description: result.message,
      });

      // Close dialog and refresh data
      onClose();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error performing batch operation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to perform batch operation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Batch Operations</DialogTitle>
          <DialogDescription>
            Perform operations on {selectedCreditIds.length} selected carbon credits.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="operation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Operation</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={(value: any) => {
                        field.onChange(value);
                        // Reset form values when operation changes
                        form.reset({ ...form.getValues(), operation: value });
                        setDocuments([]);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select an operation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="verify">Verify</SelectItem>
                        <SelectItem value="list">List on Marketplace</SelectItem>
                        <SelectItem value="retire">Retire</SelectItem>
                        <SelectItem value="update-price">Update Price</SelectItem>
                        <SelectItem value="update-status">Update Status</SelectItem>
                        <SelectItem value="update-metadata">Update Metadata</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Select the operation to perform on the selected carbon credits.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Conditional fields based on operation */}
            {operation === 'verify' && (
              <>
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Type</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select verification type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="INITIAL">Initial</SelectItem>
                            <SelectItem value="ANNUAL">Annual</SelectItem>
                            <SelectItem value="METHODOLOGY">Methodology</SelectItem>
                            <SelectItem value="OWNERSHIP">Ownership</SelectItem>
                            <SelectItem value="RETIREMENT">Retirement</SelectItem>
                            <SelectItem value="BATCH">Batch</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        Select the type of verification to request.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <FormLabel>Documents</FormLabel>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter document URL"
                      value={documentUrl}
                      onChange={(e) => setDocumentUrl(e.target.value)}
                    />
                    <Button type="button" variant="outline" onClick={addDocument}>
                      Add
                    </Button>
                  </div>
                  <FormDescription>
                    Add URLs to supporting documents for verification.
                  </FormDescription>
                  {documents.length > 0 && (
                    <div className="mt-2 space-y-2">
                      {documents.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between rounded border p-2">
                          <span className="truncate text-sm">{doc}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeDocument(index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                  {form.formState.errors.documents && (
                    <p className="text-sm font-medium text-destructive">
                      {form.formState.errors.documents.message}
                    </p>
                  )}
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes for the verification request"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide any additional information for the verification request.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {operation === 'list' && (
              <FormField
                control={form.control}
                name="listingDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Listing Date (Optional)</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormDescription>
                      Set a specific listing date or leave blank to use the current date.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {operation === 'retire' && (
              <>
                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Retirement Reason</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter the reason for retiring these carbon credits"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide a reason for retiring these carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="beneficiary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Beneficiary (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter the beneficiary of the retirement"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Specify the beneficiary of the carbon credit retirement.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="retirementDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Retirement Date (Optional)</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormDescription>
                        Set a specific retirement date or leave blank to use the current date.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {operation === 'update-price' && (
              <>
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Price (INR per ton)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={0}
                          step={0.01}
                          placeholder="Enter new price"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Set the new price for the selected carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason for Price Change (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter the reason for changing the price"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide a reason for the price change.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {operation === 'update-status' && (
              <>
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Status</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select new status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="VERIFIED">Verified</SelectItem>
                            <SelectItem value="LISTED">Listed</SelectItem>
                            <SelectItem value="RETIRED">Retired</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        Set the new status for the selected carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason for Status Change (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter the reason for changing the status"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide a reason for the status change.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {operation === 'update-metadata' && (
              <>
                <FormField
                  control={form.control}
                  name="standard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Standard (Optional)</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select standard" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Verra">Verra</SelectItem>
                            <SelectItem value="Gold Standard">Gold Standard</SelectItem>
                            <SelectItem value="American Carbon Registry">American Carbon Registry</SelectItem>
                            <SelectItem value="Climate Action Reserve">Climate Action Reserve</SelectItem>
                            <SelectItem value="Plan Vivo">Plan Vivo</SelectItem>
                            <SelectItem value="Clean Development Mechanism">Clean Development Mechanism</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        Update the standard for the selected carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="methodology"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Methodology (Optional)</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select methodology" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Renewable Energy">Renewable Energy</SelectItem>
                            <SelectItem value="Energy Efficiency">Energy Efficiency</SelectItem>
                            <SelectItem value="Forestry and Land Use">Forestry and Land Use</SelectItem>
                            <SelectItem value="Waste Management">Waste Management</SelectItem>
                            <SelectItem value="Transportation">Transportation</SelectItem>
                            <SelectItem value="Industrial Processes">Industrial Processes</SelectItem>
                            <SelectItem value="Agriculture">Agriculture</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        Update the methodology for the selected carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vintage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vintage Year (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={2000}
                          max={new Date().getFullYear()}
                          placeholder="Enter vintage year"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Update the vintage year for the selected carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter location"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Update the location for the selected carbon credits.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide any additional information about the metadata update.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
