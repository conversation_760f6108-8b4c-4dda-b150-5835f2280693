"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InfoCircle } from "@/components/ui/icons";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { TrendingUp, TrendingDown } from "lucide-react";
import { RupeeIcon } from "@/components/ui/icons/rupee-icon";

interface PricingStepProps {
  form: UseFormReturn<any>;
}

export function PricingStep({ form }: PricingStepProps) {
  // Get current price value from form
  const price = form.watch("price");

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Pricing Information</CardTitle>
          <CardDescription>
            Update the pricing details of your carbon credit
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center">
                    <FormLabel>Price per Ton (INR)</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <InfoCircle className="h-4 w-4 ml-2 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            The price per ton of carbon credits in INR. This is what buyers will pay.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <FormControl>
                    <div className="relative">
                      <RupeeIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="number"
                        min={0.01}
                        step={0.01}
                        className="pl-8"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    The price per ton of carbon credits in INR
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="minPurchaseQuantity"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center">
                    <FormLabel>Minimum Purchase Quantity</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <InfoCircle className="h-4 w-4 ml-2 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            The minimum quantity of carbon credits that can be purchased in a single transaction.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      step={1}
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    The minimum quantity that can be purchased
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Card className="bg-muted/50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Market Price Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Market Average</span>
                  <span className="font-medium">₹15.00</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">Your Price</span>
                  <span className="font-medium">₹{price?.toFixed(2) ?? "0.00"}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">Comparison</span>
                  <span className={`font-medium flex items-center ${price > 15 ? 'text-amber-600' : price < 15 ? 'text-green-600' : 'text-blue-600'}`}>
                    {price > 15 ? (
                      <>
                        <TrendingUp className="h-4 w-4 mr-1" />
                        {((price / 15 - 1) * 100).toFixed(1)}% above market
                      </>
                    ) : price < 15 ? (
                      <>
                        <TrendingDown className="h-4 w-4 mr-1" />
                        {((1 - price / 15) * 100).toFixed(1)}% below market
                      </>
                    ) : (
                      "At market price"
                    )}
                  </span>
                </div>

                <div className="pt-2">
                  <p className="text-xs text-muted-foreground">
                    Market average is based on similar carbon credits in the past 30 days.
                    {price > 15 && " Consider lowering your price to be more competitive."}
                    {price < 15 && " Your price is competitive compared to the market."}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-muted/50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Price Adjustment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Current Price</span>
                  <span className="font-medium">₹{price?.toFixed(2) ?? "0.00"}</span>
                </div>

                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Slider
                          min={1}
                          max={30}
                          step={0.5}
                          value={[field.value]}
                          onValueChange={(values) => field.onChange(values[0])}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>₹1.00</span>
                  <span>₹15.00</span>
                  <span>₹30.00</span>
                </div>

                <div className="pt-2">
                  <p className="text-xs text-muted-foreground">
                    Adjust the price using the slider or enter a specific value in the price field above.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}
