"use client";

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  ShoppingCart,
  Archive,
  ArrowRight,
  X,
  AlertTriangle,
  Info,
  Eye,
  EyeOff,
  Diff
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

interface ReviewStepProps {
  changes: Record<string, { from: any; to: any }>;
  carbonCredit: any;
}

export function ReviewStep({ changes, carbonCredit }: ReviewStepProps) {
  const [viewMode, setViewMode] = useState<"changes" | "side-by-side" | "unified">("changes");
  const [showUnchanged, setShowUnchanged] = useState(false);

  // Format value based on field type
  const formatValue = (field: string, value: any) => {
    if (field === "price") {
      return `₹${parseFloat(value).toFixed(2)}`;
    }

    if (field === "status") {
      const statusLabels: Record<string, string> = {
        "PENDING": "Pending",
        "VERIFIED": "Verified",
        "LISTED": "Listed",
        "RETIRED": "Retired",
      };

      return statusLabels[value] || value;
    }

    if (value === undefined || value === null) {
      return "—";
    }

    return value.toString();
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "VERIFIED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "LISTED":
        return <ShoppingCart className="h-4 w-4 text-blue-500" />;
      case "RETIRED":
        return <Archive className="h-4 w-4 text-purple-500" />;
      default:
        return null;
    }
  };

  // Field labels for display
  const fieldLabels: Record<string, string> = {
    name: "Name",
    description: "Description",
    vintage: "Vintage Year",
    standard: "Standard",
    methodology: "Methodology",
    location: "Location",
    price: "Price per Ton",
    minPurchaseQuantity: "Minimum Purchase Quantity",
    status: "Status",
  };

  // All fields by category
  const allFields = {
    basicInfo: ["name", "description", "vintage", "standard", "methodology", "location"],
    pricing: ["price", "minPurchaseQuantity"],
    status: ["status"]
  };

  // Group changes by category
  const basicInfoChanges = Object.keys(changes).filter(key =>
    allFields.basicInfo.includes(key)
  );

  const pricingChanges = Object.keys(changes).filter(key =>
    allFields.pricing.includes(key)
  );

  const statusChanges = Object.keys(changes).filter(key =>
    allFields.status.includes(key)
  );

  // Check if there are any changes
  const hasChanges = Object.keys(changes).length > 0;

  // Get all fields to display based on showUnchanged setting
  const getFieldsToDisplay = (category: keyof typeof allFields) => {
    if (showUnchanged) {
      return allFields[category];
    } else {
      return Object.keys(changes).filter(key => allFields[category].includes(key));
    }
  };

  // Check if a field has changes
  const hasFieldChanges = (field: string) => {
    return Object.keys(changes).includes(field);
  };

  // Render field value with appropriate styling
  const renderFieldValue = (field: string, value: any, isNew = false) => {
    const hasChange = hasFieldChanges(field);
    const baseClasses = "p-3 rounded-md text-sm";

    let classes = baseClasses;
    if (isNew && hasChange) {
      classes += " bg-primary/10 font-medium";
    } else if (!isNew && hasChange) {
      classes += " bg-muted line-through text-muted-foreground";
    } else {
      classes += " bg-muted/50";
    }

    if (field === "status") {
      return (
        <div className={classes}>
          <div className="flex items-center">
            {getStatusIcon(value)}
            <span className="ml-2">{formatValue(field, value)}</span>
          </div>
        </div>
      );
    }

    return (
      <div className={classes}>
        {formatValue(field, value)}
      </div>
    );
  };

  // Render change indicator
  const renderChangeIndicator = (field: string) => {
    const hasChange = hasFieldChanges(field);

    if (!hasChange) {
      return <Badge variant="outline" className="text-xs">Unchanged</Badge>;
    }

    return <Badge variant="default" className="text-xs">Changed</Badge>;
  };

  // Render the changes in different view modes
  const renderChangesContent = () => {
    if (!hasChanges) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No changes have been made to the carbon credit.
          </AlertDescription>
        </Alert>
      );
    }

    // Render changes by category
    const renderCategoryChanges = (title: string, fields: string[]) => {
      if (fields.length === 0 && !showUnchanged) return null;

      const fieldsToDisplay = showUnchanged ?
        allFields[title.toLowerCase().replace(' ', '') as keyof typeof allFields] :
        fields;

      if (fieldsToDisplay.length === 0) return null;

      return (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">{title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {fieldsToDisplay.map(field => (
                <div key={field} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{fieldLabels[field]}</span>
                    {renderChangeIndicator(field)}
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 p-2 bg-muted rounded-md text-sm">
                      {field === "status" ? (
                        <div className="flex items-center">
                          {getStatusIcon(changes[field]?.from || carbonCredit[field])}
                          <span className="ml-2">
                            {formatValue(field, changes[field]?.from || carbonCredit[field])}
                          </span>
                        </div>
                      ) : (
                        formatValue(field, changes[field]?.from || carbonCredit[field])
                      )}
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1 p-2 bg-primary/10 rounded-md text-sm font-medium">
                      {field === "status" ? (
                        <div className="flex items-center">
                          {getStatusIcon(changes[field]?.to || carbonCredit[field])}
                          <span className="ml-2">
                            {formatValue(field, changes[field]?.to || carbonCredit[field])}
                          </span>
                        </div>
                      ) : (
                        formatValue(field, changes[field]?.to || carbonCredit[field])
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      );
    };

    return (
      <>
        {renderCategoryChanges("Basic Information", basicInfoChanges)}
        {renderCategoryChanges("Pricing", pricingChanges)}
        {renderCategoryChanges("Status", statusChanges)}

        <Alert className="bg-muted/50 border-muted">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please review the changes carefully before saving. These changes will be recorded in the change history.
          </AlertDescription>
        </Alert>
      </>
    );
  };

  // Render side-by-side view
  const renderSideBySideView = () => {
    // Render a category of fields
    const renderCategory = (title: string, categoryFields: string[]) => {
      const fieldsToDisplay = showUnchanged ?
        allFields[title.toLowerCase().replace(' ', '') as keyof typeof allFields] :
        categoryFields;

      if (fieldsToDisplay.length === 0) return null;

      return (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">{title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-3 gap-4 font-medium text-sm">
                <div>Field</div>
                <div>Current Value</div>
                <div>New Value</div>
              </div>
              <Separator />
              {fieldsToDisplay.map(field => (
                <div key={field} className="grid grid-cols-3 gap-4 items-center">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{fieldLabels[field]}</span>
                    {hasFieldChanges(field) && (
                      <Badge variant="default" className="text-xs">Changed</Badge>
                    )}
                  </div>
                  <div>
                    {field === "status" ? (
                      <div className="flex items-center text-sm">
                        {getStatusIcon(carbonCredit[field])}
                        <span className="ml-2">{formatValue(field, carbonCredit[field])}</span>
                      </div>
                    ) : (
                      <span className="text-sm">{formatValue(field, carbonCredit[field])}</span>
                    )}
                  </div>
                  <div>
                    {field === "status" ? (
                      <div className="flex items-center text-sm font-medium">
                        {getStatusIcon(changes[field]?.to || carbonCredit[field])}
                        <span className="ml-2">
                          {formatValue(field, changes[field]?.to || carbonCredit[field])}
                        </span>
                      </div>
                    ) : (
                      <span className={`text-sm ${hasFieldChanges(field) ? "font-medium" : ""}`}>
                        {formatValue(field, changes[field]?.to || carbonCredit[field])}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      );
    };

    return (
      <>
        {renderCategory("Basic Information", basicInfoChanges)}
        {renderCategory("Pricing", pricingChanges)}
        {renderCategory("Status", statusChanges)}
      </>
    );
  };

  // Render unified view
  const renderUnifiedView = () => {
    // Render a category of fields
    const renderCategory = (title: string, categoryFields: string[]) => {
      const fieldsToDisplay = showUnchanged ?
        allFields[title.toLowerCase().replace(' ', '') as keyof typeof allFields] :
        categoryFields;

      if (fieldsToDisplay.length === 0) return null;

      return (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">{title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {fieldsToDisplay.map(field => (
                <div key={field} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{fieldLabels[field]}</span>
                    {renderChangeIndicator(field)}
                  </div>
                  {hasFieldChanges(field) ? (
                    <>
                      {renderFieldValue(field, carbonCredit[field], false)}
                      <div className="flex items-center">
                        <div className="h-6 border-l-2 border-primary ml-3"></div>
                      </div>
                      {renderFieldValue(field, changes[field].to, true)}
                    </>
                  ) : (
                    renderFieldValue(field, carbonCredit[field], false)
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      );
    };

    return (
      <>
        {renderCategory("Basic Information", basicInfoChanges)}
        {renderCategory("Pricing", pricingChanges)}
        {renderCategory("Status", statusChanges)}
      </>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Review Changes</CardTitle>
              <CardDescription>
                Review the changes you've made to your carbon credit
              </CardDescription>
            </div>
            <Badge variant={hasChanges ? "default" : "outline"}>
              {Object.keys(changes).length} {Object.keys(changes).length === 1 ? "Change" : "Changes"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* View mode selector */}
          <div className="flex items-center justify-between">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="changes" className="text-xs">
                  <Diff className="h-3 w-3 mr-1" />
                  Changes Only
                </TabsTrigger>
                <TabsTrigger value="side-by-side" className="text-xs">
                  <ArrowRight className="h-3 w-3 mr-1" />
                  Side by Side
                </TabsTrigger>
                <TabsTrigger value="unified" className="text-xs">
                  <Eye className="h-3 w-3 mr-1" />
                  Unified View
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <Button
              variant="ghost"
              size="sm"
              className="ml-2"
              onClick={() => setShowUnchanged(!showUnchanged)}
            >
              {showUnchanged ? (
                <>
                  <EyeOff className="h-3 w-3 mr-1" />
                  Hide Unchanged
                </>
              ) : (
                <>
                  <Eye className="h-3 w-3 mr-1" />
                  Show All Fields
                </>
              )}
            </Button>
          </div>

          {/* View content based on selected mode */}
          <AnimatePresence mode="wait">
            <motion.div
              key={viewMode}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              {viewMode === "changes" && renderChangesContent()}
              {viewMode === "side-by-side" && renderSideBySideView()}
              {viewMode === "unified" && renderUnifiedView()}
            </motion.div>
          </AnimatePresence>

          {hasChanges && (
            <Alert variant="warning" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                These changes will be permanently recorded in the change history and may require approval depending on your organization's settings.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
