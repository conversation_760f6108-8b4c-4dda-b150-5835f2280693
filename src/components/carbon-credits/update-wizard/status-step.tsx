"use client";

import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { InfoCircle } from "@/components/ui/icons";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertCircle, CheckCircle, Clock, ShoppingCart, Archive } from "lucide-react";

interface StatusStepProps {
  form: UseFormReturn<any>;
  currentStatus: string;
}

export function StatusStep({ form, currentStatus }: StatusStepProps) {
  // Define status options with descriptions and icons
  const statusOptions = [
    {
      value: "PENDING",
      label: "Pending",
      description: "Credit is pending verification",
      icon: <Clock className="h-5 w-5 text-amber-500" />,
      color: "bg-amber-50 border-amber-200",
    },
    {
      value: "VERIFIED",
      label: "Verified",
      description: "Credit has been verified but not listed for sale",
      icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      color: "bg-green-50 border-green-200",
    },
    {
      value: "LISTED",
      label: "Listed",
      description: "Credit is listed for sale in the marketplace",
      icon: <ShoppingCart className="h-5 w-5 text-blue-500" />,
      color: "bg-blue-50 border-blue-200",
    },
    {
      value: "RETIRED",
      label: "Retired",
      description: "Credit has been retired and cannot be traded",
      icon: <Archive className="h-5 w-5 text-purple-500" />,
      color: "bg-purple-50 border-purple-200",
    },
  ];
  
  // Get current status from form
  const status = form.watch("status");
  
  // Check if status transition is valid
  const isValidTransition = (fromStatus: string, toStatus: string) => {
    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      "PENDING": ["VERIFIED"],
      "VERIFIED": ["LISTED", "RETIRED"],
      "LISTED": ["VERIFIED", "RETIRED"],
      "RETIRED": [],
    };
    
    return validTransitions[fromStatus]?.includes(toStatus);
  };
  
  // Get warning message for invalid transitions
  const getTransitionWarning = (fromStatus: string, toStatus: string) => {
    if (fromStatus === toStatus) return null;
    
    if (!isValidTransition(fromStatus, toStatus)) {
      return `Warning: Transitioning from ${fromStatus.toLowerCase()} to ${toStatus.toLowerCase()} is not a standard flow. This may require additional verification.`;
    }
    
    return null;
  };
  
  const transitionWarning = status && currentStatus 
    ? getTransitionWarning(currentStatus, status)
    : null;
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Status Information</CardTitle>
          <CardDescription>
            Update the status of your carbon credit
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center">
            <FormLabel>Current Status</FormLabel>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <InfoCircle className="h-4 w-4 ml-2 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    The current status of your carbon credit in the lifecycle.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <div className="flex items-center space-x-2">
            {statusOptions.find(option => option.value === currentStatus)?.icon}
            <span className="font-medium">
              {statusOptions.find(option => option.value === currentStatus)?.label || currentStatus}
            </span>
          </div>
          
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <FormLabel>New Status</FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <InfoCircle className="h-4 w-4 ml-2 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          The new status you want to set for your carbon credit.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    onValueChange={field.onChange}
                    className="space-y-3"
                  >
                    {statusOptions.map((option) => (
                      <div
                        key={option.value}
                        className={`flex items-center space-x-2 rounded-md border p-4 ${
                          field.value === option.value ? option.color : ""
                        }`}
                      >
                        <RadioGroupItem value={option.value} id={option.value} />
                        <label
                          htmlFor={option.value}
                          className="flex flex-1 items-center justify-between cursor-pointer"
                        >
                          <div className="flex items-center space-x-2">
                            {option.icon}
                            <div>
                              <p className="font-medium">{option.label}</p>
                              <p className="text-sm text-muted-foreground">
                                {option.description}
                              </p>
                            </div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormDescription>
                  Select the new status for your carbon credit
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {transitionWarning && (
            <Alert variant="warning">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{transitionWarning}</AlertDescription>
            </Alert>
          )}
          
          <Card className="bg-muted/50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Status Transition Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <p>
                  <strong>Pending to Verified:</strong> Requires verification by a recognized standard.
                </p>
                <p>
                  <strong>Verified to Listed:</strong> Makes the credit available for purchase in the marketplace.
                </p>
                <p>
                  <strong>Listed to Verified:</strong> Removes the credit from the marketplace but maintains verification.
                </p>
                <p>
                  <strong>Verified/Listed to Retired:</strong> Permanently retires the credit, making it non-tradable.
                </p>
                <p className="text-muted-foreground mt-2">
                  Note: Once a credit is retired, its status cannot be changed.
                </p>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}
