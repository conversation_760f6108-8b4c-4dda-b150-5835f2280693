"use client";

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { HelpCircle, Info, AlertTriangle, CheckCircle, Clock, ShoppingCart, Archive } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { useState } from "react";

interface ContextualHelpProps {
  step: number;
  currentStatus?: string;
}

export function ContextualHelp({ step, currentStatus }: ContextualHelpProps) {
  const [isOpen, setIsOpen] = useState(true);
  
  // Help content for each step
  const helpContent = {
    1: { // Basic Info
      title: "Basic Information",
      description: "Update the fundamental details of your carbon credit.",
      tips: [
        "The name should be descriptive and unique to help buyers identify your credit",
        "Include key details in the description such as project location, methodology, and impact",
        "The vintage year represents when the emission reduction occurred",
        "Standard and methodology should match your verification documents"
      ],
      warning: "Changing basic information may require re-verification depending on your standard's requirements."
    },
    2: { // Pricing
      title: "Pricing Information",
      description: "Update the pricing details for your carbon credit.",
      tips: [
        "Research current market rates for similar credits before setting your price",
        "Consider the vintage, standard, and methodology when determining price",
        "Setting a minimum purchase quantity can help manage transaction costs",
        "Price changes are immediately reflected in the marketplace"
      ],
      warning: "Frequent price changes may affect buyer confidence. Consider your pricing strategy carefully."
    },
    3: { // Status
      title: "Status Update",
      description: "Change the current status of your carbon credit.",
      tips: [
        "Only certain status transitions are allowed based on the current status",
        "Retiring a credit is permanent and cannot be undone",
        "Listing a credit makes it visible and available for purchase in the marketplace",
        "Status changes are recorded in the audit trail for compliance purposes"
      ],
      warning: currentStatus === "RETIRED" ? 
        "This credit is already retired and cannot be changed to any other status." :
        "Changing status may affect ongoing transactions or visibility in the marketplace."
    },
    4: { // Review
      title: "Review Changes",
      description: "Review all changes before saving.",
      tips: [
        "Carefully review each change to ensure accuracy",
        "All changes will be recorded in the change history",
        "You can go back to previous steps to make adjustments",
        "Changes will be applied immediately upon saving"
      ],
      warning: "Once changes are saved, they will be visible to all users with access to this carbon credit."
    }
  };
  
  // Get status description
  const getStatusDescription = (status: string) => {
    switch (status) {
      case "PENDING":
        return "Credit is pending verification and cannot be traded";
      case "VERIFIED":
        return "Credit has been verified but is not yet listed for sale";
      case "LISTED":
        return "Credit is listed on the marketplace and available for purchase";
      case "RETIRED":
        return "Credit has been permanently retired and cannot be traded";
      default:
        return "";
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "VERIFIED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "LISTED":
        return <ShoppingCart className="h-4 w-4 text-blue-500" />;
      case "RETIRED":
        return <Archive className="h-4 w-4 text-purple-500" />;
      default:
        return null;
    }
  };
  
  // Get valid transitions for current status
  const getValidTransitions = (status: string) => {
    const validTransitions: Record<string, string[]> = {
      "PENDING": ["VERIFIED"],
      "VERIFIED": ["LISTED", "RETIRED"],
      "LISTED": ["VERIFIED", "RETIRED"],
      "RETIRED": [],
    };
    
    return validTransitions[status] || [];
  };
  
  // Render status transition help if on status step
  const renderStatusTransitionHelp = () => {
    if (step !== 3 || !currentStatus) return null;
    
    const validTransitions = getValidTransitions(currentStatus);
    
    return (
      <div className="mt-4 space-y-2">
        <h4 className="text-sm font-medium">Valid Status Transitions</h4>
        <div className="text-sm text-muted-foreground flex items-center">
          {getStatusIcon(currentStatus)}
          <span className="ml-2">{currentStatus}</span>
          <span className="mx-2">→</span>
          {validTransitions.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {validTransitions.map(status => (
                <div key={status} className="flex items-center">
                  {getStatusIcon(status)}
                  <span className="ml-1">{status}</span>
                </div>
              ))}
            </div>
          ) : (
            <span className="text-muted-foreground italic">No valid transitions</span>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Current status: {getStatusDescription(currentStatus)}
        </p>
      </div>
    );
  };
  
  const content = helpContent[step as keyof typeof helpContent];
  
  if (!content) return null;
  
  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Info className="h-4 w-4 text-blue-500 mr-2" />
          <h3 className="text-sm font-medium">Help & Tips</h3>
        </div>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm">
            {isOpen ? "Hide" : "Show"}
          </Button>
        </CollapsibleTrigger>
      </div>
      
      <CollapsibleContent className="mt-2">
        <Card className="bg-muted/50">
          <CardContent className="pt-4 pb-3 px-4">
            <div className="space-y-2">
              <h4 className="font-medium">{content.title}</h4>
              <p className="text-sm text-muted-foreground">{content.description}</p>
              
              {content.tips.length > 0 && (
                <div className="mt-3">
                  <h5 className="text-xs font-medium uppercase text-muted-foreground mb-1">Tips</h5>
                  <ul className="text-sm space-y-1">
                    {content.tips.map((tip, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="h-3 w-3 text-green-500 mt-1 mr-2 flex-shrink-0" />
                        <span>{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {content.warning && (
                <Alert variant="warning" className="mt-3 py-2">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    {content.warning}
                  </AlertDescription>
                </Alert>
              )}
              
              {renderStatusTransitionHelp()}
            </div>
          </CardContent>
        </Card>
      </CollapsibleContent>
    </Collapsible>
  );
}
