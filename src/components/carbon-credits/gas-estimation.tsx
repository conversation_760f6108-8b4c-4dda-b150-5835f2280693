'use client';

import { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, Zap } from 'lucide-react';
import { SupportedNetwork } from '@/lib/blockchain-config';

interface GasEstimationProps {
  carbonCreditId: string;
  type: 'tokenize' | 'transfer' | 'retire';
  amount?: number;
  toAddress?: string;
  network?: SupportedNetwork;
  useTestnet?: boolean;
}

interface GasEstimation {
  gasEstimate: string;
  gasPrice: string;
  totalGasCost: string;
  totalGasCostUsd: string;
  nativeCurrency: string;
  network: string;
  operations: {
    name: string;
    gasEstimate: string;
  }[];
}

export function GasEstimation({
  carbonCreditId,
  type,
  amount = 1,
  toAddress,
  network = SupportedNetwork.POLYGON,
  useTestnet = true,
}: GasEstimationProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [gasEstimation, setGasEstimation] = useState<GasEstimation | null>(null);

  const estimateGas = async () => {
    setIsLoading(true);
    setError(null);

    try {
      let endpoint = '';
      let requestBody: any = {
        carbonCreditId,
        network,
        useTestnet,
      };

      switch (type) {
        case 'tokenize':
          endpoint = '/api/gas-estimation/tokenize';
          break;
        case 'transfer':
          endpoint = '/api/gas-estimation/transfer';
          if (!toAddress) {
            throw new Error('Recipient address is required for transfer gas estimation');
          }
          requestBody = {
            ...requestBody,
            toAddress,
            amount,
          };
          break;
        case 'retire':
          endpoint = '/api/gas-estimation/retire';
          requestBody = {
            ...requestBody,
            amount,
          };
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to estimate gas');
      }

      const data = await response.json();
      setGasEstimation(data.gasEstimation);
    } catch (error) {
      console.error('Error estimating gas:', error);
      setError(error instanceof Error ? error.message : 'Failed to estimate gas');
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to estimate gas',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getOperationTitle = () => {
    switch (type) {
      case 'tokenize':
        return 'Tokenization';
      case 'transfer':
        return 'Transfer';
      case 'retire':
        return 'Retirement';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-yellow-500" />
          Gas Estimation
        </CardTitle>
        <CardDescription>
          Estimate the gas cost for {getOperationTitle().toLowerCase()} on {network}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
          </div>
        ) : error ? (
          <div className="rounded-md bg-destructive/10 p-3 text-sm text-destructive">
            {error}
          </div>
        ) : gasEstimation ? (
          <div className="space-y-4">
            <div className="rounded-md bg-muted p-4">
              <div className="mb-2 text-lg font-semibold">
                Estimated Cost: {gasEstimation.totalGasCost} {gasEstimation.nativeCurrency} (${gasEstimation.totalGasCostUsd})
              </div>
              <div className="text-sm text-muted-foreground">
                <p>Gas Price: {gasEstimation.gasPrice} Gwei</p>
                <p>Total Gas Units: {parseInt(gasEstimation.gasEstimate).toLocaleString()}</p>
              </div>
            </div>
            
            <div>
              <h4 className="mb-2 font-medium">Operation Breakdown</h4>
              <ul className="space-y-1 text-sm">
                {gasEstimation.operations.map((operation, index) => (
                  <li key={index} className="flex justify-between">
                    <span>{operation.name}</span>
                    <span>{parseInt(operation.gasEstimate).toLocaleString()} gas</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="text-xs text-muted-foreground">
              <p>Note: Gas prices fluctuate based on network congestion. This is an estimate and actual costs may vary.</p>
            </div>
          </div>
        ) : (
          <div className="py-6 text-center text-muted-foreground">
            <p>Click the button below to estimate gas costs.</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={estimateGas}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Estimating...
            </>
          ) : gasEstimation ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Estimate
            </>
          ) : (
            <>
              <Zap className="mr-2 h-4 w-4" />
              Estimate Gas
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
