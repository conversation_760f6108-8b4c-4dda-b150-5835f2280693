'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Trash2, Zap } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

// Schema for retirement form
const retirementFormSchema = z.object({
  amount: z.coerce.number().positive('Amount must be positive'),
  reason: z.string().min(1, 'Reason is required'),
  beneficiary: z.string().optional(),
});

type RetirementFormValues = z.infer<typeof retirementFormSchema>;

interface RetireButtonProps {
  carbonCreditId: string;
  maxAmount: number;
  disabled?: boolean;
  onSuccess?: () => void;
}

export function RetireButton({ carbonCreditId, maxAmount, disabled = false, onSuccess }: RetireButtonProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [gasEstimation, setGasEstimation] = useState<any>(null);
  const [isEstimatingGas, setIsEstimatingGas] = useState(false);

  // Initialize form
  const form = useForm<RetirementFormValues>({
    resolver: zodResolver(retirementFormSchema),
    defaultValues: {
      amount: 1,
      reason: '',
      beneficiary: '',
    },
  });

  // Estimate gas when form values change
  const estimateGas = async (data: RetirementFormValues) => {
    if (!data.amount) return;

    // Check if amount is greater than max amount
    if (data.amount > maxAmount) return;

    setIsEstimatingGas(true);
    try {
      const response = await fetch('/api/gas-estimation/retire', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          carbonCreditId,
          amount: data.amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to estimate gas');
      }

      const result = await response.json();
      setGasEstimation(result.gasEstimation);
    } catch (error) {
      console.error('Error estimating gas:', error);
      // Don't show toast for gas estimation errors
    } finally {
      setIsEstimatingGas(false);
    }
  };

  // Watch form values for gas estimation
  useEffect(() => {
    if (isOpen && form.formState.isValid) {
      const data = form.getValues();
      if (data.amount) {
        estimateGas(data);
      }
    }
  }, [isOpen, form.watch('amount')]);

  // Form submission handler
  const onSubmit = async (data: RetirementFormValues) => {
    // Check if amount is greater than max amount
    if (data.amount > maxAmount) {
      form.setError('amount', {
        type: 'manual',
        message: `Amount cannot exceed ${maxAmount} tons`,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/carbon-credits/${carbonCreditId}/retire`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to retire carbon credit');
      }

      const result = await response.json();

      toast({
        title: 'Carbon Credit Retired',
        description: `${data.amount} tons of carbon credit has been successfully retired.`,
      });

      // Close dialog
      setIsOpen(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page
      router.refresh();

      // Open block explorer in a new tab
      if (result.blockExplorerUrl) {
        window.open(result.blockExplorerUrl, '_blank');
      }
    } catch (error) {
      console.error('Error retiring carbon credit:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to retire carbon credit',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button disabled={disabled || maxAmount <= 0} variant="outline" size="sm">
          <Trash2 className="mr-2 h-4 w-4" />
          Retire
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Retire Carbon Credit</DialogTitle>
          <DialogDescription>
            Permanently retire your tokenized carbon credit to offset emissions.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (tons)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={maxAmount}
                      step={1}
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the amount of carbon credits to retire (max: {maxAmount} tons).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Retirement Reason</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter the reason for retiring these carbon credits"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a reason for retiring these carbon credits.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="beneficiary"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Beneficiary (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter the beneficiary of the retirement"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Specify the beneficiary of the carbon credit retirement.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {gasEstimation && (
              <>
                <Separator className="my-4" />
                <div className="space-y-2 rounded-md bg-muted p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">Estimated Gas Cost:</span>
                    </div>
                    <span className="font-medium">
                      {gasEstimation.totalGasCost} {gasEstimation.nativeCurrency} (${gasEstimation.totalGasCostUsd})
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>Gas prices fluctuate based on network congestion. This is an estimate and actual costs may vary.</p>
                  </div>
                </div>
              </>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || isEstimatingGas || maxAmount <= 0}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Retiring...
                  </>
                ) : isEstimatingGas ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Estimating Gas...
                  </>
                ) : (
                  'Retire'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
