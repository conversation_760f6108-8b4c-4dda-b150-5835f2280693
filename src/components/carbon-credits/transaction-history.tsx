'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { ExternalLink, ArrowUpRight, ArrowDownRight, Ban, RefreshCw } from 'lucide-react';

interface Transaction {
  id: string;
  type: 'TOKENIZATION' | 'TRANSFER' | 'RETIREMENT';
  amount: number;
  transactionHash: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  network: string;
  contractAddress: string;
  fromAddress?: string;
  toAddress?: string;
  reason?: string;
  beneficiary?: string;
  createdAt: string;
  updatedAt: string;
}

interface TransactionHistoryProps {
  carbonCreditId: string;
}

export function TransactionHistory({ carbonCreditId }: TransactionHistoryProps) {
  const router = useRouter();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/carbon-credits/${carbonCreditId}/transactions`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch transaction history');
      }

      const data = await response.json();
      setTransactions(data.transactions);
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch transaction history');
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to fetch transaction history',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [carbonCreditId]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'TOKENIZATION':
        return <ArrowUpRight className="h-4 w-4 text-green-500" />;
      case 'TRANSFER':
        return <ArrowDownRight className="h-4 w-4 text-blue-500" />;
      case 'RETIREMENT':
        return <Ban className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
        return 'outline';
      case 'FAILED':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getBlockExplorerUrl = (network: string, transactionHash: string) => {
    switch (network) {
      case 'ethereum':
        return `https://etherscan.io/tx/${transactionHash}`;
      case 'polygon':
        return `https://polygonscan.com/tx/${transactionHash}`;
      case 'arbitrum':
        return `https://arbiscan.io/tx/${transactionHash}`;
      case 'optimism':
        return `https://optimistic.etherscan.io/tx/${transactionHash}`;
      case 'base':
        return `https://basescan.org/tx/${transactionHash}`;
      default:
        return `https://etherscan.io/tx/${transactionHash}`;
    }
  };

  const truncateAddress = (address: string) => {
    if (!address) return '';
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Transaction History</CardTitle>
            <CardDescription>
              View all blockchain transactions for this carbon credit
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={fetchTransactions}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : error ? (
          <div className="py-6 text-center text-muted-foreground">
            <p>{error}</p>
          </div>
        ) : transactions.length === 0 ? (
          <div className="py-6 text-center text-muted-foreground">
            <p>No transactions found for this carbon credit.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTransactionTypeIcon(transaction.type)}
                        {transaction.type}
                      </div>
                    </TableCell>
                    <TableCell>{transaction.amount.toLocaleString()} tons</TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(transaction.status)}>
                        {transaction.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                    <TableCell>
                      {transaction.type === 'TRANSFER' && (
                        <div className="text-xs text-muted-foreground">
                          <div>From: {truncateAddress(transaction.fromAddress || '')}</div>
                          <div>To: {truncateAddress(transaction.toAddress || '')}</div>
                        </div>
                      )}
                      {transaction.type === 'RETIREMENT' && transaction.reason && (
                        <div className="text-xs text-muted-foreground">
                          <div>Reason: {transaction.reason.substring(0, 20)}...</div>
                          {transaction.beneficiary && (
                            <div>Beneficiary: {transaction.beneficiary}</div>
                          )}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(getBlockExplorerUrl(transaction.network, transaction.transactionHash), '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
