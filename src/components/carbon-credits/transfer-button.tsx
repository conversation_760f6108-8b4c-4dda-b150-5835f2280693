'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Send, Zap } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

// Schema for transfer form
const transferFormSchema = z.object({
  toAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid Ethereum address'),
  amount: z.coerce.number().positive('Amount must be positive'),
});

type TransferFormValues = z.infer<typeof transferFormSchema>;

interface TransferButtonProps {
  carbonCreditId: string;
  maxAmount: number;
  disabled?: boolean;
  onSuccess?: () => void;
}

export function TransferButton({ carbonCreditId, maxAmount, disabled = false, onSuccess }: TransferButtonProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [gasEstimation, setGasEstimation] = useState<any>(null);
  const [isEstimatingGas, setIsEstimatingGas] = useState(false);

  // Initialize form
  const form = useForm<TransferFormValues>({
    resolver: zodResolver(transferFormSchema),
    defaultValues: {
      toAddress: '',
      amount: 1,
    },
  });

  // Estimate gas when form values change
  const estimateGas = async (data: TransferFormValues) => {
    if (!data.toAddress || !data.amount) return;

    // Check if amount is greater than max amount
    if (data.amount > maxAmount) return;

    setIsEstimatingGas(true);
    try {
      const response = await fetch('/api/gas-estimation/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          carbonCreditId,
          toAddress: data.toAddress,
          amount: data.amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to estimate gas');
      }

      const result = await response.json();
      setGasEstimation(result.gasEstimation);
    } catch (error) {
      console.error('Error estimating gas:', error);
      // Don't show toast for gas estimation errors
    } finally {
      setIsEstimatingGas(false);
    }
  };

  // Watch form values for gas estimation
  useEffect(() => {
    if (isOpen && form.formState.isValid) {
      const data = form.getValues();
      if (data.toAddress && data.amount) {
        estimateGas(data);
      }
    }
  }, [isOpen, form.watch('toAddress'), form.watch('amount')]);

  // Form submission handler
  const onSubmit = async (data: TransferFormValues) => {
    // Check if amount is greater than max amount
    if (data.amount > maxAmount) {
      form.setError('amount', {
        type: 'manual',
        message: `Amount cannot exceed ${maxAmount} tons`,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/carbon-credits/${carbonCreditId}/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to transfer carbon credit');
      }

      const result = await response.json();

      toast({
        title: 'Carbon Credit Transferred',
        description: `${data.amount} tons of carbon credit has been successfully transferred to ${data.toAddress}.`,
      });

      // Close dialog
      setIsOpen(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page
      router.refresh();

      // Open block explorer in a new tab
      if (result.blockExplorerUrl) {
        window.open(result.blockExplorerUrl, '_blank');
      }
    } catch (error) {
      console.error('Error transferring carbon credit:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to transfer carbon credit',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button disabled={disabled || maxAmount <= 0} variant="outline" size="sm">
          <Send className="mr-2 h-4 w-4" />
          Transfer
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Transfer Carbon Credit</DialogTitle>
          <DialogDescription>
            Transfer your tokenized carbon credit to another wallet address.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="toAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Recipient Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="0x..."
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the Ethereum address of the recipient.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (tons)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={maxAmount}
                      step={1}
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the amount of carbon credits to transfer (max: {maxAmount} tons).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {gasEstimation && (
              <>
                <Separator className="my-4" />
                <div className="space-y-2 rounded-md bg-muted p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">Estimated Gas Cost:</span>
                    </div>
                    <span className="font-medium">
                      {gasEstimation.totalGasCost} {gasEstimation.nativeCurrency} (${gasEstimation.totalGasCostUsd})
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>Gas prices fluctuate based on network congestion. This is an estimate and actual costs may vary.</p>
                  </div>
                </div>
              </>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || isEstimatingGas || maxAmount <= 0}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Transferring...
                  </>
                ) : isEstimatingGas ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Estimating Gas...
                  </>
                ) : (
                  'Transfer'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
