"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Check,
  History,
  Save,
  Info,
  Eye,
  EyeOff,
  Clock,
  Calendar,
  User
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { BasicInfoStep } from "./update-wizard/basic-info-step";
import { PricingStep } from "./update-wizard/pricing-step";
import { StatusStep } from "./update-wizard/status-step";
import { ReviewStep } from "./update-wizard/review-step";
import { ChangeHistoryStep } from "./update-wizard/change-history-step";
import { ContextualHelp } from "./update-wizard/contextual-help";

// Define the wizard steps
enum WizardStep {
  BASIC_INFO = 1,
  PRICING = 2,
  STATUS = 3,
  REVIEW = 4,
}

// Define the form schema
const updateCarbonCreditSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  description: z.string().optional(),
  vintage: z.number().int().min(2000, "Vintage year must be 2000 or later").optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  location: z.string().optional(),
  price: z.number().positive("Price must be positive").optional(),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").optional(),
  status: z.enum(["PENDING", "VERIFIED", "LISTED", "SOLD", "RETIRED"]).optional(),
});

type UpdateCarbonCreditFormValues = z.infer<typeof updateCarbonCreditSchema>;

interface CarbonCredit {
  id: string;
  name: string;
  description: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
  };
}

interface CarbonCreditUpdateWizardProps {
  carbonCredit: CarbonCredit;
  onSuccess?: () => void;
}

export function CarbonCreditUpdateWizard({ carbonCredit, onSuccess }: CarbonCreditUpdateWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.BASIC_INFO);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("update");
  const [changes, setChanges] = useState<Record<string, { from: any; to: any }>>({});
  const [changeHistory, setChangeHistory] = useState<any[]>([]);

  // Calculate progress percentage
  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  // Initialize form with current carbon credit values
  const form = useForm<UpdateCarbonCreditFormValues>({
    resolver: zodResolver(updateCarbonCreditSchema),
    defaultValues: {
      name: carbonCredit.name,
      description: carbonCredit.description,
      vintage: carbonCredit.vintage,
      standard: carbonCredit.standard,
      methodology: carbonCredit.methodology,
      location: carbonCredit.location,
      price: carbonCredit.price,
      minPurchaseQuantity: 1, // Default value, should be replaced with actual value if available
      status: carbonCredit.status as any,
    },
  });

  // Watch form values to track changes
  const formValues = form.watch();

  // Update changes when form values change
  useEffect(() => {
    const newChanges: Record<string, { from: any; to: any }> = {};

    Object.keys(formValues).forEach(key => {
      // Skip if the field is not in the form values
      if (!(key in formValues)) return;

      // Get the typed key
      const typedKey = key as keyof UpdateCarbonCreditFormValues;

      // Compare with original values
      if (formValues[typedKey] !== undefined &&
          formValues[typedKey] !== null &&
          formValues[typedKey] !== (carbonCredit as any)[key]) {
        newChanges[key] = {
          from: (carbonCredit as any)[key],
          to: formValues[typedKey],
        };
      }
    });

    setChanges(newChanges);
  }, [formValues, carbonCredit]);

  // Fetch change history
  useEffect(() => {
    async function fetchChangeHistory() {
      try {
        // This would be replaced with an actual API call
        // For now, we'll simulate some change history
        const mockHistory = [
          {
            id: "1",
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            user: "John Doe",
            changes: {
              price: { from: carbonCredit.price - 5, to: carbonCredit.price },
            },
          },
          {
            id: "2",
            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            user: "Jane Smith",
            changes: {
              status: { from: "PENDING", to: "VERIFIED" },
            },
          },
        ];

        setChangeHistory(mockHistory);
      } catch (error) {
        console.error("Error fetching change history:", error);
      }
    }

    if (activeTab === "history") {
      fetchChangeHistory();
    }
  }, [activeTab, carbonCredit.price]);

  // Handle next step
  const handleNext = () => {
    const currentValues = form.getValues();

    // Validate current step
    let isValid = true;

    if (currentStep === WizardStep.BASIC_INFO) {
      // Validate basic info fields
      isValid = form.trigger(["name", "description", "vintage", "standard", "methodology", "location"]);
    } else if (currentStep === WizardStep.PRICING) {
      // Validate pricing fields
      isValid = form.trigger(["price", "minPurchaseQuantity"]);
    } else if (currentStep === WizardStep.STATUS) {
      // Validate status fields
      isValid = form.trigger(["status"]);
    }

    if (isValid) {
      setCurrentStep(prev => (prev < WizardStep.REVIEW ? prev + 1 : prev));
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    setCurrentStep(prev => (prev > WizardStep.BASIC_INFO ? prev - 1 : prev));
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Only include fields that have changed
      const updateData: Partial<UpdateCarbonCreditFormValues> = {};

      Object.keys(changes).forEach(key => {
        updateData[key as keyof UpdateCarbonCreditFormValues] = changes[key].to;
      });

      // If no changes, show a message and return
      if (Object.keys(updateData).length === 0) {
        toast({
          title: "No changes to save",
          description: "You haven't made any changes to the carbon credit.",
        });
        setIsSubmitting(false);
        return;
      }

      // Send update request
      const response = await fetch(`/api/carbon-credits/${carbonCredit.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update carbon credit");
      }

      toast({
        title: "Carbon credit updated",
        description: "Your carbon credit has been updated successfully.",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error updating carbon credit:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update carbon credit",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case WizardStep.BASIC_INFO:
        return <BasicInfoStep form={form} />;

      case WizardStep.PRICING:
        return <PricingStep form={form} />;

      case WizardStep.STATUS:
        return <StatusStep form={form} currentStatus={carbonCredit.status} />;

      case WizardStep.REVIEW:
        return <ReviewStep changes={changes} carbonCredit={carbonCredit} />;

      default:
        return null;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Show a summary of the carbon credit
  const renderCreditSummary = () => {
    return (
      <div className="mb-6 p-4 border rounded-md bg-muted/20">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium">Carbon Credit Summary</h3>
          <Badge variant="outline" className="text-xs">ID: {carbonCredit.id.substring(0, 8)}</Badge>
        </div>
        <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
          <div>
            <span className="text-muted-foreground">Name:</span> {carbonCredit.name}
          </div>
          <div>
            <span className="text-muted-foreground">Vintage:</span> {carbonCredit.vintage}
          </div>
          <div>
            <span className="text-muted-foreground">Standard:</span> {carbonCredit.standard}
          </div>
          <div>
            <span className="text-muted-foreground">Status:</span> {carbonCredit.status}
          </div>
          <div>
            <span className="text-muted-foreground">Last Updated:</span> {formatDate(carbonCredit.updatedAt)}
          </div>
          <div>
            <span className="text-muted-foreground">Organization:</span> {carbonCredit.organization.name}
          </div>
        </div>
      </div>
    );
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Update Carbon Credit</CardTitle>
            <CardDescription>
              Make changes to your carbon credit information
            </CardDescription>
          </div>
          <Badge
            variant={Object.keys(changes).length > 0 ? "default" : "outline"}
            className="ml-2"
          >
            {Object.keys(changes).length} {Object.keys(changes).length === 1 ? "Change" : "Changes"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full">
            <TabsTrigger value="update" className="flex-1">
              <Eye className="h-4 w-4 mr-2" />
              Update
            </TabsTrigger>
            <TabsTrigger value="history" className="flex-1">
              <History className="h-4 w-4 mr-2" />
              Change History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="update" className="pt-4">
            <AnimatePresence mode="wait">
              <motion.div
                key={`step-${currentStep}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                {/* Credit Summary (collapsible) */}
                {renderCreditSummary()}

                {/* Step Progress */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Step {currentStep} of {totalSteps}</span>
                    <span className="text-sm text-muted-foreground">
                      {currentStep === WizardStep.BASIC_INFO
                        ? "Basic Information"
                        : currentStep === WizardStep.PRICING
                        ? "Pricing"
                        : currentStep === WizardStep.STATUS
                        ? "Status"
                        : "Review Changes"}
                    </span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>

                {/* Contextual Help */}
                <ContextualHelp step={currentStep} currentStatus={carbonCredit.status} />

                {/* Step Content */}
                {renderStepContent()}
              </motion.div>
            </AnimatePresence>
          </TabsContent>

          <TabsContent value="history" className="pt-4">
            <ChangeHistoryStep history={changeHistory} />
          </TabsContent>
        </Tabs>
      </CardContent>

      {activeTab === "update" && (
        <CardFooter className="flex justify-between">
          {currentStep > WizardStep.BASIC_INFO ? (
            <Button variant="outline" onClick={handlePrevious}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
          ) : (
            <div></div>
          )}

          {currentStep < WizardStep.REVIEW ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || Object.keys(changes).length === 0}
              className={Object.keys(changes).length > 0 ? "bg-green-600 hover:bg-green-700" : ""}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save {Object.keys(changes).length} {Object.keys(changes).length === 1 ? "Change" : "Changes"}
                </>
              )}
            </Button>
          )}
        </CardFooter>
      )}
    </AnimatedCard>
  );
}
