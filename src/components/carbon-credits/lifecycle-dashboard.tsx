"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  AlertCircle,
  Clock,
  CheckCircle,
  ShoppingCart,
  Archive,
  ArrowRight,
  Info,
  HelpCircle,
  Calendar,
  BarChart,
  Zap,
  Layers,
  FileCheck,
  Wallet,
  Coins,
  AlertTriangle,
  ChevronRight
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Toolt<PERSON>, TooltipContent, Too<PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";

interface CarbonCredit {
  id: string;
  name: string;
  description: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
  };
  verificationStatus?: string;
  tokenizationStatus?: string;
  retirementStatus?: string;
  certificationDate?: string;
  listingDate?: string;
  retirementDate?: string;
  tokenizationDate?: string;
}

interface LifecycleDashboardProps {
  carbonCredit: CarbonCredit;
  onStatusChange?: (newStatus: string) => void;
}

// Define lifecycle stages
const LIFECYCLE_STAGES = [
  {
    id: "creation",
    name: "Creation",
    description: "Carbon credit is created from a project",
    icon: <Layers className="h-5 w-5" />,
    color: "bg-blue-500",
    requiredStatus: null,
  },
  {
    id: "verification",
    name: "Verification",
    description: "Carbon credit is verified by a third party",
    icon: <FileCheck className="h-5 w-5" />,
    color: "bg-green-500",
    requiredStatus: "PENDING",
  },
  {
    id: "tokenization",
    name: "Tokenization",
    description: "Carbon credit is tokenized on the blockchain",
    icon: <Wallet className="h-5 w-5" />,
    color: "bg-purple-500",
    requiredStatus: "VERIFIED",
  },
  {
    id: "listing",
    name: "Listing",
    description: "Carbon credit is listed for sale on the marketplace",
    icon: <ShoppingCart className="h-5 w-5" />,
    color: "bg-blue-500",
    requiredStatus: "VERIFIED",
  },
  {
    id: "trading",
    name: "Trading",
    description: "Carbon credit is traded between buyers and sellers",
    icon: <Coins className="h-5 w-5" />,
    color: "bg-amber-500",
    requiredStatus: "LISTED",
  },
  {
    id: "retirement",
    name: "Retirement",
    description: "Carbon credit is permanently retired",
    icon: <Archive className="h-5 w-5" />,
    color: "bg-purple-500",
    requiredStatus: "VERIFIED",
  },
];

export function LifecycleDashboard({ carbonCredit, onStatusChange }: LifecycleDashboardProps) {
  const [activeTab, setActiveTab] = useState<string>("timeline");
  const [selectedStage, setSelectedStage] = useState<string | null>(null);

  // Get current lifecycle stage based on status
  const getCurrentStage = () => {
    if (carbonCredit.status === "RETIRED") {
      return "retirement";
    }

    if (carbonCredit.status === "LISTED") {
      return "listing";
    }

    if (carbonCredit.status === "VERIFIED") {
      return "verification";
    }

    return "creation";
  };

  // Calculate progress through lifecycle
  const calculateProgress = () => {
    const stageIndex = LIFECYCLE_STAGES.findIndex(stage => stage.id === getCurrentStage());
    return ((stageIndex + 1) / LIFECYCLE_STAGES.length) * 100;
  };

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not available";

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  // Get stage status
  const getStageStatus = (stageId: string) => {
    const currentStage = getCurrentStage();
    const currentIndex = LIFECYCLE_STAGES.findIndex(stage => stage.id === currentStage);
    const stageIndex = LIFECYCLE_STAGES.findIndex(stage => stage.id === stageId);

    if (stageIndex < currentIndex) {
      return "completed";
    }

    if (stageIndex === currentIndex) {
      return "current";
    }

    return "upcoming";
  };

  // Get stage date
  const getStageDate = (stageId: string) => {
    switch (stageId) {
      case "creation":
        return formatDate(carbonCredit.createdAt);
      case "verification":
        return formatDate(carbonCredit.certificationDate);
      case "tokenization":
        return formatDate(carbonCredit.tokenizationDate);
      case "listing":
        return formatDate(carbonCredit.listingDate);
      case "retirement":
        return formatDate(carbonCredit.retirementDate);
      default:
        return "Not available";
    }
  };

  // Get next possible stages
  const getNextPossibleStages = () => {
    const currentStatus = carbonCredit.status;

    // Define valid transitions
    const validTransitions: Record<string, string[]> = {
      "PENDING": ["VERIFIED"],
      "VERIFIED": ["LISTED", "RETIRED"],
      "LISTED": ["VERIFIED", "RETIRED"],
      "RETIRED": [],
    };

    return LIFECYCLE_STAGES.filter(stage => {
      if (stage.requiredStatus === null) return false;

      return validTransitions[currentStatus]?.includes(stage.requiredStatus);
    });
  };

  // Render timeline view
  const renderTimeline = () => {
    return (
      <div className="space-y-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Current Stage: {LIFECYCLE_STAGES.find(stage => stage.id === getCurrentStage())?.name}</span>
            <span className="text-sm text-muted-foreground">
              {carbonCredit.status}
            </span>
          </div>
          <Progress value={calculateProgress()} className="h-2" />
        </div>

        <div className="relative space-y-0">
          {/* Timeline line */}
          <div className="absolute left-[19px] top-6 bottom-6 w-[2px] bg-muted-foreground/20"></div>

          {/* Timeline items */}
          {LIFECYCLE_STAGES.map((stage, index) => {
            const stageStatus = getStageStatus(stage.id);
            const stageDate = getStageDate(stage.id);

            return (
              <div key={stage.id} className="relative pl-10 pb-6">
                {/* Timeline dot */}
                <div className="absolute left-0 top-0 h-10 flex items-center">
                  <div className={`h-5 w-5 rounded-full ${
                    stageStatus === "completed" ? stage.color :
                    stageStatus === "current" ? "bg-primary" :
                    "bg-muted"
                  } flex items-center justify-center z-10`}>
                    {stageStatus === "completed" ? (
                      <CheckCircle className="h-3 w-3 text-white" />
                    ) : stageStatus === "current" ? (
                      stage.icon
                    ) : (
                      <div className="h-2 w-2 rounded-full bg-muted-foreground/50"></div>
                    )}
                  </div>
                </div>

                {/* Timeline content */}
                <Card className={`relative ${
                  stageStatus === "current" ? "border-primary" : ""
                }`}>
                  {stageStatus === "current" && (
                    <Badge className="absolute top-4 right-4 bg-primary">
                      Current
                    </Badge>
                  )}
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {stage.icon}
                        <span className="ml-2 font-medium">{stage.name}</span>
                      </div>
                      {stageStatus === "completed" && (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Completed
                        </Badge>
                      )}
                    </div>
                    <CardDescription>{stage.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      {stageStatus === "completed" && (
                        <div className="flex items-center text-muted-foreground">
                          <Calendar className="h-4 w-4 mr-2" />
                          Completed on {stageDate}
                        </div>
                      )}

                      {stageStatus === "current" && (
                        <div className="space-y-2">
                          <div className="flex items-center text-muted-foreground">
                            <Clock className="h-4 w-4 mr-2" />
                            In progress since {stageDate}
                          </div>

                          {getNextPossibleStages().length > 0 && (
                            <div className="pt-2">
                              <span className="text-xs text-muted-foreground">Next possible stages:</span>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {getNextPossibleStages().map(nextStage => (
                                  <Button
                                    key={nextStage.id}
                                    variant="outline"
                                    size="sm"
                                    className="h-8"
                                    onClick={() => setSelectedStage(nextStage.id)}
                                  >
                                    {nextStage.icon}
                                    <span className="ml-2">{nextStage.name}</span>
                                    <ChevronRight className="h-4 w-4 ml-1" />
                                  </Button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {stageStatus === "upcoming" && (
                        <div className="flex items-center text-muted-foreground">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Not started yet
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render analytics view
  const renderAnalytics = () => {
    // Calculate time in each stage
    const calculateTimeInStage = (startDate: string | undefined, endDate: string | undefined) => {
      if (!startDate) return "N/A";

      const start = new Date(startDate);
      const end = endDate ? new Date(endDate) : new Date();

      const diffInDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        return "Less than a day";
      }

      return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'}`;
    };

    // Get stage metrics
    const getStageMetrics = () => {
      return [
        {
          stage: "Creation to Verification",
          time: calculateTimeInStage(carbonCredit.createdAt, carbonCredit.certificationDate),
          status: carbonCredit.certificationDate ? "completed" : "in-progress",
        },
        {
          stage: "Verification to Tokenization",
          time: calculateTimeInStage(carbonCredit.certificationDate, carbonCredit.tokenizationDate),
          status: carbonCredit.tokenizationDate ? "completed" :
                 carbonCredit.certificationDate ? "in-progress" : "not-started",
        },
        {
          stage: "Verification to Listing",
          time: calculateTimeInStage(carbonCredit.certificationDate, carbonCredit.listingDate),
          status: carbonCredit.listingDate ? "completed" :
                 carbonCredit.certificationDate ? "in-progress" : "not-started",
        },
        {
          stage: "Listing to Retirement",
          time: calculateTimeInStage(carbonCredit.listingDate, carbonCredit.retirementDate),
          status: carbonCredit.retirementDate ? "completed" :
                 carbonCredit.listingDate ? "in-progress" : "not-started",
        },
      ];
    };

    const metrics = getStageMetrics();

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Lifecycle Analytics</CardTitle>
            <CardDescription>
              Time spent in each stage of the carbon credit lifecycle
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {metrics.map((metric, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{metric.stage}</span>
                    <Badge
                      variant="outline"
                      className={
                        metric.status === "completed" ? "bg-green-50 text-green-700 border-green-200" :
                        metric.status === "in-progress" ? "bg-blue-50 text-blue-700 border-blue-200" :
                        "bg-gray-50 text-gray-700 border-gray-200"
                      }
                    >
                      {metric.status === "completed" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : metric.status === "in-progress" ? (
                        <Clock className="h-3 w-3 mr-1" />
                      ) : (
                        <AlertCircle className="h-3 w-3 mr-1" />
                      )}
                      {metric.status === "completed" ? "Completed" :
                       metric.status === "in-progress" ? "In Progress" :
                       "Not Started"}
                    </Badge>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full bg-muted rounded-full h-2.5">
                      <div
                        className={`h-2.5 rounded-full ${
                          metric.status === "completed" ? "bg-green-500" :
                          metric.status === "in-progress" ? "bg-blue-500" :
                          "bg-gray-300"
                        }`}
                        style={{
                          width: metric.status === "completed" ? "100%" :
                                 metric.status === "in-progress" ? "50%" : "0%"
                        }}
                      ></div>
                    </div>
                    <span className="ml-4 text-sm">{metric.time}</span>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-6 border-t">
              <h4 className="text-sm font-medium mb-4">Lifecycle Efficiency</h4>
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Lifecycle Time</p>
                        <p className="text-2xl font-bold">
                          {calculateTimeInStage(carbonCredit.createdAt, carbonCredit.retirementDate)}
                        </p>
                      </div>
                      <Calendar className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Current Stage Duration</p>
                        <p className="text-2xl font-bold">
                          {(() => {
                            const currentStage = getCurrentStage();
                            switch (currentStage) {
                              case "creation":
                                return calculateTimeInStage(carbonCredit.createdAt, undefined);
                              case "verification":
                                return calculateTimeInStage(carbonCredit.certificationDate, undefined);
                              case "listing":
                                return calculateTimeInStage(carbonCredit.listingDate, undefined);
                              case "retirement":
                                return calculateTimeInStage(carbonCredit.retirementDate, undefined);
                              default:
                                return "N/A";
                            }
                          })()}
                        </p>
                      </div>
                      <Clock className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Render transition wizard
  const renderTransitionWizard = () => {
    if (!selectedStage) {
      return (
        <div className="flex flex-col items-center justify-center h-64">
          <Info className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Select a Transition</h3>
          <p className="text-muted-foreground text-center max-w-md">
            Select a transition from the timeline view to begin the transition wizard.
          </p>
        </div>
      );
    }

    const stage = LIFECYCLE_STAGES.find(s => s.id === selectedStage);

    if (!stage) {
      return null;
    }

    // Get requirements for the selected stage
    const getRequirements = () => {
      switch (selectedStage) {
        case "verification":
          return [
            { id: "documents", name: "Verification Documents", description: "Upload verification documents", required: true },
            { id: "methodology", name: "Methodology Compliance", description: "Ensure methodology compliance", required: true },
            { id: "third-party", name: "Third-Party Verification", description: "Obtain third-party verification", required: true },
          ];
        case "tokenization":
          return [
            { id: "verified", name: "Verified Status", description: "Carbon credit must be verified", required: true, completed: carbonCredit.status === "VERIFIED" },
            { id: "wallet", name: "Blockchain Wallet", description: "Connect a blockchain wallet", required: true },
            { id: "gas", name: "Gas Fees", description: "Ensure sufficient gas for transaction", required: true },
          ];
        case "listing":
          return [
            { id: "verified", name: "Verified Status", description: "Carbon credit must be verified", required: true, completed: carbonCredit.status === "VERIFIED" },
            { id: "pricing", name: "Pricing Information", description: "Set pricing information", required: true },
            { id: "terms", name: "Terms & Conditions", description: "Accept marketplace terms", required: true },
          ];
        case "retirement":
          return [
            { id: "verified", name: "Verified Status", description: "Carbon credit must be verified", required: true, completed: carbonCredit.status === "VERIFIED" || carbonCredit.status === "LISTED" },
            { id: "reason", name: "Retirement Reason", description: "Provide retirement reason", required: true },
            { id: "beneficiary", name: "Beneficiary", description: "Specify retirement beneficiary", required: false },
          ];
        default:
          return [];
      }
    };

    const requirements = getRequirements();

    // Check if all required requirements are completed
    const canProceed = requirements.filter(r => r.required).every(r => r.completed !== false);

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center">
              {stage.icon}
              <CardTitle className="ml-2">{stage.name} Transition</CardTitle>
            </div>
            <CardDescription>
              Complete the requirements to transition to the {stage.name.toLowerCase()} stage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Requirements</h3>

              {requirements.map((req, index) => (
                <div key={req.id} className="flex items-start p-3 border rounded-md">
                  <div className={`h-5 w-5 rounded-full flex items-center justify-center mr-3 mt-0.5 ${
                    req.completed ? "bg-green-500" : "bg-muted"
                  }`}>
                    {req.completed ? (
                      <CheckCircle className="h-3 w-3 text-white" />
                    ) : (
                      <span className="text-xs text-muted-foreground">{index + 1}</span>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">{req.name}</h4>
                      {req.required && (
                        <Badge variant="outline" className="text-xs">Required</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{req.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <Alert variant={canProceed ? "default" : "warning"} className="mt-4">
              {canProceed ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
              <AlertDescription>
                {canProceed ?
                  "All requirements are met. You can proceed with the transition." :
                  "Some requirements are not met. Please complete all required items before proceeding."}
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setSelectedStage(null)}>
              Cancel
            </Button>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Button
                      disabled={!canProceed}
                      onClick={() => {
                        if (onStatusChange && stage.requiredStatus) {
                          onStatusChange(stage.requiredStatus);
                        }
                        setSelectedStage(null);
                      }}
                    >
                      Proceed to {stage.name}
                    </Button>
                  </div>
                </TooltipTrigger>
                {!canProceed && (
                  <TooltipContent>
                    <p>Complete all required items first</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </CardFooter>
        </Card>
      </div>
    );
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <CardTitle>Carbon Credit Lifecycle</CardTitle>
        <CardDescription>
          Track and manage the lifecycle of your carbon credit
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="timeline" className="flex-1">
              <Layers className="h-4 w-4 mr-2" />
              Timeline
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex-1">
              <BarChart className="h-4 w-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="transition" className="flex-1">
              <Zap className="h-4 w-4 mr-2" />
              Transition
            </TabsTrigger>
          </TabsList>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="pt-4"
            >
              <TabsContent value="timeline" className="mt-0">
                {renderTimeline()}
              </TabsContent>

              <TabsContent value="analytics" className="mt-0">
                {renderAnalytics()}
              </TabsContent>

              <TabsContent value="transition" className="mt-0">
                {renderTransitionWizard()}
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </Tabs>
      </CardContent>
    </AnimatedCard>
  );
