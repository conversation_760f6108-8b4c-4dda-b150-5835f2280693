"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Archive,
  ArrowLeft,
  ArrowRight,
  Calendar,
  Check,
  CheckCircle,
  Clock,
  Download,
  FileText,
  Globe,
  HelpCircle,
  Info,
  Leaf,
  Save,
  Share2,
  ShieldCheck,
  User,
  Users,
  AlertTriangle
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";

// Carbon credit interface
interface CarbonCredit {
  id: string;
  name: string;
  quantity: number;
  availableQuantity: number;
  retiredQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  projectId?: string;
  project?: {
    id: string;
    name: string;
    location?: string;
    type: string;
  };
}

// Retirement wizard props
interface RetirementWizardProps {
  carbonCredit: CarbonCredit;
  onComplete: (retirementData: RetirementFormValues) => void;
  onCancel: () => void;
}

// Retirement purpose options
const RETIREMENT_PURPOSES = [
  {
    id: "corporate_offsetting",
    label: "Corporate Carbon Offsetting",
    description: "Retire credits to offset your company's carbon footprint",
    icon: <Users className="h-4 w-4" />,
  },
  {
    id: "product_offsetting",
    label: "Product Carbon Neutrality",
    description: "Retire credits to make specific products carbon neutral",
    icon: <ShieldCheck className="h-4 w-4" />,
  },
  {
    id: "event_offsetting",
    label: "Event Carbon Neutrality",
    description: "Retire credits to offset emissions from specific events",
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    id: "personal_offsetting",
    label: "Personal Carbon Offsetting",
    description: "Retire credits to offset your personal carbon footprint",
    icon: <User className="h-4 w-4" />,
  },
  {
    id: "environmental_contribution",
    label: "Environmental Contribution",
    description: "Retire credits as a general contribution to climate action",
    icon: <Leaf className="h-4 w-4" />,
  },
  {
    id: "other",
    label: "Other Purpose",
    description: "Retire credits for another specific purpose",
    icon: <Globe className="h-4 w-4" />,
  },
];

// Retirement form schema
const retirementFormSchema = z.object({
  quantity: z.coerce.number()
    .positive("Quantity must be positive")
    .int("Quantity must be a whole number"),
  purpose: z.string().min(1, "Purpose is required"),
  beneficiary: z.string().min(1, "Beneficiary is required"),
  retirementMessage: z.string().optional(),
  certificateOptions: z.object({
    includeProjectDetails: z.boolean().default(true),
    includeImpactMetrics: z.boolean().default(true),
    includeBeneficiaryLogo: z.boolean().default(false),
    generatePDF: z.boolean().default(true),
  }),
  otherPurposeDetails: z.string().optional(),
});

// Retirement form values type
type RetirementFormValues = z.infer<typeof retirementFormSchema>;

// Wizard steps enum
enum WizardStep {
  QUANTITY = 1,
  PURPOSE = 2,
  DETAILS = 3,
  CERTIFICATE = 4,
  REVIEW = 5,
}

export function RetirementWizard({
  carbonCredit,
  onComplete,
  onCancel
}: RetirementWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.QUANTITY);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Total number of steps
  const totalSteps = 5;

  // Calculate progress percentage
  const progress = (currentStep / totalSteps) * 100;

  // Initialize form
  const form = useForm<RetirementFormValues>({
    resolver: zodResolver(retirementFormSchema),
    defaultValues: {
      quantity: 1,
      purpose: "",
      beneficiary: "",
      retirementMessage: "",
      certificateOptions: {
        includeProjectDetails: true,
        includeImpactMetrics: true,
        includeBeneficiaryLogo: false,
        generatePDF: true,
      },
      otherPurposeDetails: "",
    },
  });

  // Watch form values
  const watchPurpose = form.watch("purpose");
  const watchQuantity = form.watch("quantity");

  // Handle next step
  const handleNext = () => {
    switch (currentStep) {
      case WizardStep.QUANTITY:
        form.trigger("quantity").then(isValid => {
          if (isValid) setCurrentStep(WizardStep.PURPOSE);
        });
        break;
      case WizardStep.PURPOSE:
        form.trigger("purpose").then(isValid => {
          if (isValid) setCurrentStep(WizardStep.DETAILS);
        });
        break;
      case WizardStep.DETAILS:
        form.trigger(["beneficiary", "retirementMessage"]).then(isValid => {
          if (isValid) setCurrentStep(WizardStep.CERTIFICATE);
        });
        break;
      case WizardStep.CERTIFICATE:
        setCurrentStep(WizardStep.REVIEW);
        break;
      default:
        break;
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    if (currentStep > WizardStep.QUANTITY) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: RetirementFormValues) => {
    setIsSubmitting(true);

    try {
      // Call the onComplete callback with the form data
      onComplete(data);

      // Show success toast
      toast({
        title: "Retirement Successful",
        description: `Successfully retired ${data.quantity} tons of carbon credits.`,
      });
    } catch (error) {
      // Show error toast
      toast({
        title: "Retirement Failed",
        description: "There was an error retiring the carbon credits. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Calculate estimated impact
  const calculateImpact = (quantity: number) => {
    // These are approximate conversions for educational purposes
    return {
      trees: Math.round(quantity * 16.5), // ~16.5 trees per ton of CO2
      carMiles: Math.round(quantity * 2481), // ~2,481 miles per ton of CO2
      homeEnergy: Math.round(quantity * 1.21), // ~1.21 months of home energy per ton of CO2
      flightMiles: Math.round(quantity * 2325), // ~2,325 flight miles per ton of CO2
    };
  };

  const impact = calculateImpact(watchQuantity || 0);

  // Render quantity step
  const renderQuantityStep = () => {
    return (
      <div className="space-y-6">
        <Alert className="bg-muted/50">
          <Info className="h-4 w-4" />
          <AlertDescription>
            You are about to retire carbon credits from {carbonCredit.name}.
            This action is permanent and cannot be undone.
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="bg-muted/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Credit Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Name</div>
                    <div className="font-medium">{carbonCredit.name}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Vintage</div>
                    <div className="font-medium">{carbonCredit.vintage}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Standard</div>
                    <div className="font-medium">{carbonCredit.standard}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Methodology</div>
                    <div className="font-medium">{carbonCredit.methodology}</div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Available Quantity</span>
                    <span className="font-medium">{carbonCredit.availableQuantity.toLocaleString()} tons</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Already Retired</span>
                    <span className="font-medium">{carbonCredit.retiredQuantity.toLocaleString()} tons</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Quantity</span>
                    <span className="font-medium">{carbonCredit.quantity.toLocaleString()} tons</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-6">
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Retirement Quantity (tons)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={carbonCredit.availableQuantity}
                      {...field}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (isNaN(value)) {
                          field.onChange(1);
                        } else if (value > carbonCredit.availableQuantity) {
                          field.onChange(carbonCredit.availableQuantity);
                        } else if (value < 1) {
                          field.onChange(1);
                        } else {
                          field.onChange(value);
                        }
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the number of tons you want to retire (max: {carbonCredit.availableQuantity.toLocaleString()})
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <div className="text-sm font-medium">Estimated Impact</div>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <Leaf className="h-4 w-4 text-green-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.trees.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Trees planted for one year</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Globe className="h-4 w-4 text-blue-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.carMiles.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Miles driven by car</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <HelpCircle className="h-4 w-4 text-amber-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.homeEnergy.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Months of home energy</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <HelpCircle className="h-4 w-4 text-purple-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.flightMiles.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Miles flown</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render purpose step
  const renderPurposeStep = () => {
    return (
      <div className="space-y-6">
        <FormField
          control={form.control}
          name="purpose"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Retirement Purpose</FormLabel>
              <FormDescription>
                Select the purpose for retiring these carbon credits
              </FormDescription>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-3"
                >
                  {RETIREMENT_PURPOSES.map((purpose) => (
                    <div key={purpose.id}>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value={purpose.id} />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer flex items-center">
                          {purpose.icon}
                          <span className="ml-2 font-medium">{purpose.label}</span>
                        </FormLabel>
                      </FormItem>
                      <div className="pl-7 text-sm text-muted-foreground">
                        {purpose.description}
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {watchPurpose === "other" && (
          <FormField
            control={form.control}
            name="otherPurposeDetails"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Other Purpose Details</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Please describe your specific retirement purpose..."
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </div>
    );
  };

  // Render details step
  const renderDetailsStep = () => {
    return (
      <div className="space-y-6">
        <FormField
          control={form.control}
          name="beneficiary"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Beneficiary Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter the name of the beneficiary organization or individual"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                This will appear on the retirement certificate
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="retirementMessage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Retirement Message (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter a message to include with this retirement..."
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                This message will be permanently recorded with the retirement
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    );
  };

  // Render certificate step
  const renderCertificateStep = () => {
    return (
      <div className="space-y-6">
        <div className="bg-muted/30 p-4 rounded-md">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 mr-2" />
            <h3 className="font-medium">Retirement Certificate Options</h3>
          </div>

          <div className="space-y-4">
            <FormField
              control={form.control}
              name="certificateOptions.includeProjectDetails"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Include Project Details</FormLabel>
                    <FormDescription>
                      Include information about the project that generated these credits
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="certificateOptions.includeImpactMetrics"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Include Impact Metrics</FormLabel>
                    <FormDescription>
                      Include equivalent impact metrics (trees, car miles, etc.)
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="certificateOptions.includeBeneficiaryLogo"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Include Beneficiary Logo</FormLabel>
                    <FormDescription>
                      Upload and include the beneficiary's logo on the certificate
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="certificateOptions.generatePDF"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Generate PDF Certificate</FormLabel>
                    <FormDescription>
                      Generate a downloadable PDF certificate after retirement
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex items-center justify-between p-4 border rounded-md">
          <div className="flex items-center">
            <Download className="h-5 w-5 mr-2 text-muted-foreground" />
            <div>
              <div className="font-medium">Certificate Preview</div>
              <div className="text-sm text-muted-foreground">
                Preview your retirement certificate
              </div>
            </div>
          </div>
          <Button variant="outline" size="sm">
            Preview
          </Button>
        </div>

        <div className="flex items-center justify-between p-4 border rounded-md">
          <div className="flex items-center">
            <Share2 className="h-5 w-5 mr-2 text-muted-foreground" />
            <div>
              <div className="font-medium">Sharing Options</div>
              <div className="text-sm text-muted-foreground">
                Configure how you want to share this certificate
              </div>
            </div>
          </div>
          <Button variant="outline" size="sm">
            Configure
          </Button>
        </div>
      </div>
    );
  };

  // Render review step
  const renderReviewStep = () => {
    const formValues = form.getValues();
    const purposeObj = RETIREMENT_PURPOSES.find(p => p.id === formValues.purpose);

    return (
      <div className="space-y-6">
        <Alert variant="warning">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please review your retirement details carefully. This action cannot be undone once confirmed.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Retirement Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Credit Name</div>
                    <div className="font-medium">{carbonCredit.name}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Quantity</div>
                    <div className="font-medium">{formValues.quantity.toLocaleString()} tons</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Purpose</div>
                    <div className="font-medium">{purposeObj?.label}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Beneficiary</div>
                    <div className="font-medium">{formValues.beneficiary}</div>
                  </div>
                </div>

                {formValues.purpose === "other" && formValues.otherPurposeDetails && (
                  <div>
                    <div className="text-sm text-muted-foreground">Purpose Details</div>
                    <div className="text-sm">{formValues.otherPurposeDetails}</div>
                  </div>
                )}

                {formValues.retirementMessage && (
                  <div>
                    <div className="text-sm text-muted-foreground">Retirement Message</div>
                    <div className="text-sm">{formValues.retirementMessage}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Certificate Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className={`h-4 w-4 mr-2 ${formValues.certificateOptions.includeProjectDetails ? "text-green-500" : "text-muted-foreground"}`} />
                  <span className="text-sm">Include Project Details</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className={`h-4 w-4 mr-2 ${formValues.certificateOptions.includeImpactMetrics ? "text-green-500" : "text-muted-foreground"}`} />
                  <span className="text-sm">Include Impact Metrics</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className={`h-4 w-4 mr-2 ${formValues.certificateOptions.includeBeneficiaryLogo ? "text-green-500" : "text-muted-foreground"}`} />
                  <span className="text-sm">Include Beneficiary Logo</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className={`h-4 w-4 mr-2 ${formValues.certificateOptions.generatePDF ? "text-green-500" : "text-muted-foreground"}`} />
                  <span className="text-sm">Generate PDF Certificate</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Estimated Impact</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <Leaf className="h-4 w-4 text-green-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.trees.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Trees planted for one year</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Globe className="h-4 w-4 text-blue-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.carMiles.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Miles driven by car</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <HelpCircle className="h-4 w-4 text-amber-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.homeEnergy.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Months of home energy</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <HelpCircle className="h-4 w-4 text-purple-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium">{impact.flightMiles.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Miles flown</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case WizardStep.QUANTITY:
        return renderQuantityStep();
      case WizardStep.PURPOSE:
        return renderPurposeStep();
      case WizardStep.DETAILS:
        return renderDetailsStep();
      case WizardStep.CERTIFICATE:
        return renderCertificateStep();
      case WizardStep.REVIEW:
        return renderReviewStep();
      default:
        return null;
    }
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <CardTitle>Retire Carbon Credits</CardTitle>
        <CardDescription>
          Permanently retire carbon credits to offset emissions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Step {currentStep} of {totalSteps}</span>
                <span className="text-sm text-muted-foreground">
                  {currentStep === WizardStep.QUANTITY
                    ? "Quantity"
                    : currentStep === WizardStep.PURPOSE
                    ? "Purpose"
                    : currentStep === WizardStep.DETAILS
                    ? "Details"
                    : currentStep === WizardStep.CERTIFICATE
                    ? "Certificate"
                    : "Review"}
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            <AnimatePresence mode="wait">
              <motion.div
                key={`step-${currentStep}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                {renderStepContent()}
              </motion.div>
            </AnimatePresence>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        {currentStep > WizardStep.QUANTITY ? (
          <Button variant="outline" onClick={handlePrevious}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
        ) : (
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}

        {currentStep < WizardStep.REVIEW ? (
          <Button onClick={handleNext}>
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button
            onClick={form.handleSubmit(handleSubmit)}
            disabled={isSubmitting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isSubmitting ? (
              <>
                <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <Archive className="mr-2 h-4 w-4" />
                Confirm Retirement
              </>
            )}
          </Button>
        )}
      </CardFooter>
    </AnimatedCard>
  );
