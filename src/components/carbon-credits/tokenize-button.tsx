'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Coins, Zap } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

// Schema for tokenization form
const tokenizationFormSchema = z.object({
  network: z.enum(['ethereum', 'polygon', 'arbitrum', 'optimism', 'base']),
  useTestnet: z.boolean(),
});

type TokenizationFormValues = z.infer<typeof tokenizationFormSchema>;

interface TokenizeButtonProps {
  carbonCreditId: string;
  disabled?: boolean;
  onSuccess?: () => void;
}

export function TokenizeButton({ carbonCreditId, disabled = false, onSuccess }: TokenizeButtonProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [gasEstimation, setGasEstimation] = useState<any>(null);
  const [isEstimatingGas, setIsEstimatingGas] = useState(false);

  // Initialize form
  const form = useForm<TokenizationFormValues>({
    resolver: zodResolver(tokenizationFormSchema),
    defaultValues: {
      network: 'polygon',
      useTestnet: true,
    },
  });

  // Estimate gas when form values change
  const estimateGas = async (data: TokenizationFormValues) => {
    setIsEstimatingGas(true);
    try {
      const response = await fetch('/api/gas-estimation/tokenize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          carbonCreditId,
          network: data.network,
          useTestnet: data.useTestnet,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to estimate gas');
      }

      const result = await response.json();
      setGasEstimation(result.gasEstimation);
    } catch (error) {
      console.error('Error estimating gas:', error);
      // Don't show toast for gas estimation errors
    } finally {
      setIsEstimatingGas(false);
    }
  };

  // Watch form values for gas estimation
  useEffect(() => {
    if (isOpen && form.formState.isValid) {
      const data = form.getValues();
      estimateGas(data);
    }
  }, [isOpen, form.watch('network'), form.watch('useTestnet')]);

  // Form submission handler
  const onSubmit = async (data: TokenizationFormValues) => {
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/carbon-credits/${carbonCreditId}/tokenize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to tokenize carbon credit');
      }

      const result = await response.json();

      toast({
        title: 'Carbon Credit Tokenized',
        description: 'Your carbon credit has been successfully tokenized on the blockchain.',
      });

      // Close dialog
      setIsOpen(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page
      router.refresh();

      // Open block explorer in a new tab
      if (result.blockExplorerUrl) {
        window.open(result.blockExplorerUrl, '_blank');
      }
    } catch (error) {
      console.error('Error tokenizing carbon credit:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to tokenize carbon credit',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button disabled={disabled} variant="outline" size="sm">
          <Coins className="mr-2 h-4 w-4" />
          Tokenize
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Tokenize Carbon Credit</DialogTitle>
          <DialogDescription>
            Tokenize your carbon credit on the blockchain to enable trading and retirement.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="network"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Blockchain Network</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a network" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ethereum">Ethereum</SelectItem>
                        <SelectItem value="polygon">Polygon</SelectItem>
                        <SelectItem value="arbitrum">Arbitrum</SelectItem>
                        <SelectItem value="optimism">Optimism</SelectItem>
                        <SelectItem value="base">Base</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Select the blockchain network where you want to tokenize your carbon credit.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="useTestnet"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Use Testnet</FormLabel>
                    <FormDescription>
                      Use testnet for development and testing purposes.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {gasEstimation && (
              <>
                <Separator className="my-4" />
                <div className="space-y-2 rounded-md bg-muted p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">Estimated Gas Cost:</span>
                    </div>
                    <span className="font-medium">
                      {gasEstimation.totalGasCost} {gasEstimation.nativeCurrency} (${gasEstimation.totalGasCostUsd})
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>Gas prices fluctuate based on network congestion. This is an estimate and actual costs may vary.</p>
                  </div>
                </div>
              </>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || isEstimatingGas}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Tokenizing...
                  </>
                ) : isEstimatingGas ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Estimating Gas...
                  </>
                ) : (
                  'Tokenize'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
