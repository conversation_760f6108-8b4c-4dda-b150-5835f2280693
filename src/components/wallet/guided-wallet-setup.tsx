'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Wallet, Shield, Zap, AlertCircle, Info, Check, ChevronRight, ChevronLeft } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ValidatedForm } from '@/components/forms/validated-form';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { z } from 'zod';

// Form schema
const walletSetupSchema = z.object({
  network: z.enum(['ethereum', 'polygon', 'arbitrum', 'optimism', 'base']),
  useTestnet: z.boolean().default(true),
  walletType: z.enum(['smart', 'standard']).default('smart'),
  securityLevel: z.enum(['standard', 'high']).default('standard'),
});

type WalletSetupFormValues = z.infer<typeof walletSetupSchema>;

// Step definitions
enum SetupStep {
  WALLET_TYPE = 1,
  NETWORK = 2,
  SECURITY = 3,
  REVIEW = 4,
}

export function GuidedWalletSetup() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<SetupStep>(SetupStep.WALLET_TYPE);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formValues, setFormValues] = useState<WalletSetupFormValues>({
    network: 'polygon',
    useTestnet: true,
    walletType: 'smart',
    securityLevel: 'standard',
  });

  const totalSteps = 4;

  // Default form values
  const defaultValues: WalletSetupFormValues = {
    network: 'polygon',
    useTestnet: true,
    walletType: 'smart',
    securityLevel: 'standard',
  };

  // Handle form submission
  const onSubmit = async (data: WalletSetupFormValues) => {
    try {
      setIsSubmitting(true);

      const response = await fetch('/api/wallet/smart-wallet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          network: data.network,
          useTestnet: data.useTestnet,
          isSmartWallet: data.walletType === 'smart',
          securityLevel: data.securityLevel,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create wallet');
      }

      const result = await response.json();

      toast({
        title: 'Wallet Created',
        description: `Your ${data.walletType === 'smart' ? 'smart' : 'standard'} wallet has been created successfully.`,
      });

      // Redirect to wallet page
      router.push(`/wallet/${result.wallet.id}`);

      return data;
    } catch (error) {
      console.error('Error creating wallet:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to create wallet');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle step navigation
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      // Submit the form
      handleSubmit();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      const response = await fetch('/api/wallet/smart-wallet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          network: formValues.network,
          useTestnet: formValues.useTestnet,
          isSmartWallet: formValues.walletType === 'smart',
          securityLevel: formValues.securityLevel,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create wallet');
      }

      const result = await response.json();

      toast({
        title: 'Wallet Created',
        description: `Your ${formValues.walletType === 'smart' ? 'smart' : 'standard'} wallet has been created successfully.`,
      });

      // Redirect to wallet page
      router.push(`/wallet/${result.wallet.id}`);
    } catch (error) {
      console.error('Error creating wallet:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create wallet',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update form values
  const updateFormValue = (field: keyof WalletSetupFormValues, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case SetupStep.WALLET_TYPE:
        return (
          <WalletTypeStep
            value={formValues.walletType}
            onChange={(value) => updateFormValue('walletType', value)}
          />
        );
      case SetupStep.NETWORK:
        return (
          <NetworkStep
            network={formValues.network}
            useTestnet={formValues.useTestnet}
            onNetworkChange={(value) => updateFormValue('network', value)}
            onTestnetChange={(value) => updateFormValue('useTestnet', value)}
          />
        );
      case SetupStep.SECURITY:
        return (
          <SecurityStep
            walletType={formValues.walletType}
            securityLevel={formValues.securityLevel}
            onChange={(value) => updateFormValue('securityLevel', value)}
          />
        );
      case SetupStep.REVIEW:
        return <ReviewStep formValues={formValues} />;
      default:
        return null;
    }
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case SetupStep.WALLET_TYPE:
        return 'Choose Wallet Type';
      case SetupStep.NETWORK:
        return 'Select Network';
      case SetupStep.SECURITY:
        return 'Security Settings';
      case SetupStep.REVIEW:
        return 'Review & Create';
      default:
        return '';
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create a New Wallet</CardTitle>
        <CardDescription>
          Set up a wallet to manage your carbon credits and transactions
        </CardDescription>
        <div className="mt-4">
          <div className="flex justify-between text-sm mb-2">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{getStepTitle()}</span>
          </div>
          <Progress value={(currentStep / totalSteps) * 100} className="h-2" />
        </div>
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-[300px]"
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === SetupStep.WALLET_TYPE || isSubmitting}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <Button
          type="button"
          onClick={nextStep}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {currentStep === SetupStep.REVIEW ? "Creating..." : "Next"}
            </>
          ) : (
            <>
              {currentStep === SetupStep.REVIEW ? "Create Wallet" : "Next"}
              <ChevronRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

// Wallet Type Card Component
function WalletTypeCard({
  title,
  description,
  icon,
  features,
  isSelected,
  onClick,
  recommended = false
}: {
  title: string,
  description: string,
  icon: React.ReactNode,
  features: string[],
  isSelected: boolean,
  onClick: () => void,
  recommended?: boolean
}) {
  return (
    <div
      className={`rounded-lg border-2 p-6 cursor-pointer transition-all ${
        isSelected ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50'
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          {icon}
          <div>
            <h4 className="font-medium text-lg">{title}</h4>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </div>
        {recommended && (
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
            Recommended
          </span>
        )}
      </div>

      <div className="mt-4 space-y-2">
        <p className="text-sm font-medium">Features:</p>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start text-sm">
              <Check className="mr-2 h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </div>

      {isSelected && (
        <div className="mt-4 flex justify-end">
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
            Selected
          </span>
        </div>
      )}
    </div>
  );
}

// Step 1: Wallet Type Selection
function WalletTypeStep({ value, onChange }: { value: string, onChange: (value: 'smart' | 'standard') => void }) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Choose Your Wallet Type</h3>
        <p className="text-sm text-muted-foreground">
          Select the type of wallet that best suits your organization's needs
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <WalletTypeCard
          title="Smart Wallet"
          description="Enhanced security with account abstraction"
          icon={<Shield className="h-8 w-8 text-primary" />}
          features={[
            "Multi-signature support for team approvals",
            "Social recovery options if keys are lost",
            "Customizable security policies",
            "Gas optimization for lower transaction fees"
          ]}
          isSelected={value === 'smart'}
          onClick={() => onChange('smart')}
          recommended
        />

        <WalletTypeCard
          title="Standard Wallet"
          description="Traditional blockchain wallet"
          icon={<Wallet className="h-8 w-8 text-primary" />}
          features={[
            "Simple key-based authentication",
            "Direct blockchain interaction",
            "Compatible with all standard wallets",
            "Lower complexity for basic operations"
          ]}
          isSelected={value === 'standard'}
          onClick={() => onChange('standard')}
        />
      </div>
    </div>
  );
}

// Step 2: Network Selection
function NetworkStep({
  network,
  useTestnet,
  onNetworkChange,
  onTestnetChange
}: {
  network: string,
  useTestnet: boolean,
  onNetworkChange: (value: any) => void,
  onTestnetChange: (value: boolean) => void
}) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Select Blockchain Network</h3>
        <p className="text-sm text-muted-foreground">
          Choose the blockchain network for your wallet
        </p>
      </div>

      <Tabs defaultValue={useTestnet ? "testnet" : "mainnet"} onValueChange={(value) => onTestnetChange(value === "testnet")}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="testnet">Testnet (Recommended)</TabsTrigger>
          <TabsTrigger value="mainnet">Mainnet (Production)</TabsTrigger>
        </TabsList>
        <TabsContent value="testnet" className="space-y-4 pt-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Test Environment</AlertTitle>
            <AlertDescription>
              Testnets use test tokens with no real value, perfect for learning and testing.
              You can get free test tokens from network faucets.
            </AlertDescription>
          </Alert>
        </TabsContent>
        <TabsContent value="mainnet" className="space-y-4 pt-4">
          <Alert variant="warning">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Production Environment</AlertTitle>
            <AlertDescription>
              Mainnet transactions use real tokens with actual value. Only use mainnet when
              you're ready to perform real transactions.
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>

      <div className="space-y-2 pt-4">
        <label className="text-sm font-medium">Select Network</label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <NetworkCard
            name="Polygon"
            description={useTestnet ? "Mumbai Testnet" : "Polygon Mainnet"}
            icon="/images/networks/polygon.svg"
            features={[
              "Low transaction fees",
              "Fast confirmation times",
              "Widely used for carbon credits",
              "Strong ecosystem support"
            ]}
            isSelected={network === 'polygon'}
            onClick={() => onNetworkChange('polygon')}
            recommended
          />

          <NetworkCard
            name="Ethereum"
            description={useTestnet ? "Sepolia Testnet" : "Ethereum Mainnet"}
            icon="/images/networks/ethereum.svg"
            features={[
              "Highest security",
              "Most established network",
              "Broad adoption",
              "Strong liquidity"
            ]}
            isSelected={network === 'ethereum'}
            onClick={() => onNetworkChange('ethereum')}
          />

          <NetworkCard
            name="Arbitrum"
            description={useTestnet ? "Arbitrum Goerli" : "Arbitrum One"}
            icon="/images/networks/arbitrum.svg"
            features={[
              "Ethereum Layer 2 scaling",
              "Lower fees than Ethereum",
              "Fast transactions",
              "Full Ethereum compatibility"
            ]}
            isSelected={network === 'arbitrum'}
            onClick={() => onNetworkChange('arbitrum')}
          />

          <NetworkCard
            name="Optimism"
            description={useTestnet ? "Optimism Goerli" : "Optimism Mainnet"}
            icon="/images/networks/optimism.svg"
            features={[
              "Optimistic rollup technology",
              "Fast finality",
              "Low transaction costs",
              "Growing ecosystem"
            ]}
            isSelected={network === 'optimism'}
            onClick={() => onNetworkChange('optimism')}
          />
        </div>
      </div>
    </div>
  );
}

// Network Card Component
function NetworkCard({
  name,
  description,
  icon,
  features,
  isSelected,
  onClick,
  recommended = false
}: {
  name: string,
  description: string,
  icon: string,
  features: string[],
  isSelected: boolean,
  onClick: () => void,
  recommended?: boolean
}) {
  return (
    <div
      className={`rounded-lg border-2 p-4 cursor-pointer transition-all ${
        isSelected ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50'
      }`}
      onClick={onClick}
    >
      <div className="flex items-center space-x-3">
        <div className="h-8 w-8 relative">
          {/* Fallback to a div if image fails to load */}
          <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
            {name.charAt(0)}
          </div>
        </div>
        <div>
          <h4 className="font-medium">{name}</h4>
          <p className="text-xs text-muted-foreground">{description}</p>
        </div>
        {recommended && (
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
            Recommended
          </span>
        )}
      </div>

      {isSelected && (
        <div className="mt-2">
          <p className="text-xs font-medium mb-1">Key features:</p>
          <ul className="space-y-1">
            {features.slice(0, 2).map((feature, index) => (
              <li key={index} className="flex items-start text-xs">
                <Check className="mr-1 h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

// Step 3: Security Settings
function SecurityStep({
  walletType,
  securityLevel,
  onChange
}: {
  walletType: string,
  securityLevel: string,
  onChange: (value: 'standard' | 'high') => void
}) {
  // Only show security options for smart wallets
  if (walletType !== 'smart') {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Security Settings</h3>
          <p className="text-sm text-muted-foreground">
            Configure security options for your wallet
          </p>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Standard Wallet Selected</AlertTitle>
          <AlertDescription>
            Standard wallets use a single private key for security. Make sure to back up your key
            securely as it cannot be recovered if lost.
          </AlertDescription>
        </Alert>

        <div className="rounded-lg border p-4 mt-4">
          <h4 className="font-medium">Security Recommendations</h4>
          <ul className="mt-2 space-y-2">
            <li className="flex items-start text-sm">
              <Check className="mr-2 h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
              <span>Store your private key or seed phrase in a secure password manager</span>
            </li>
            <li className="flex items-start text-sm">
              <Check className="mr-2 h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
              <span>Consider using a hardware wallet for additional security</span>
            </li>
            <li className="flex items-start text-sm">
              <Check className="mr-2 h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
              <span>Never share your private key with anyone</span>
            </li>
            <li className="flex items-start text-sm">
              <Check className="mr-2 h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
              <span>Create offline backups of your recovery information</span>
            </li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Security Settings</h3>
        <p className="text-sm text-muted-foreground">
          Choose the security level for your smart wallet
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <SecurityLevelCard
          title="Standard Security"
          description="Balanced security for most organizations"
          features={[
            "Single owner account",
            "Basic recovery options",
            "Standard transaction limits",
            "Email notifications for transactions"
          ]}
          isSelected={securityLevel === 'standard'}
          onClick={() => onChange('standard')}
          recommended
        />

        <SecurityLevelCard
          title="High Security"
          description="Enhanced protection for high-value wallets"
          features={[
            "Multi-signature requirements (2+ approvers)",
            "Advanced social recovery with guardians",
            "Customizable transaction limits and delays",
            "Comprehensive audit logging"
          ]}
          isSelected={securityLevel === 'high'}
          onClick={() => onChange('high')}
        />
      </div>
    </div>
  );
}

// Security Level Card Component
function SecurityLevelCard({
  title,
  description,
  features,
  isSelected,
  onClick,
  recommended = false
}: {
  title: string,
  description: string,
  features: string[],
  isSelected: boolean,
  onClick: () => void,
  recommended?: boolean
}) {
  return (
    <div
      className={`rounded-lg border-2 p-6 cursor-pointer transition-all ${
        isSelected ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50'
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div>
          <h4 className="font-medium text-lg">{title}</h4>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        {recommended && (
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
            Recommended
          </span>
        )}
      </div>

      <div className="mt-4 space-y-2">
        <p className="text-sm font-medium">Features:</p>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start text-sm">
              <Check className="mr-2 h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </div>

      {isSelected && (
        <div className="mt-4 flex justify-end">
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
            Selected
          </span>
        </div>
      )}
    </div>
  );
}

// Step 4: Review
function ReviewStep({ formValues }: { formValues: WalletSetupFormValues }) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Review Your Wallet Configuration</h3>
        <p className="text-sm text-muted-foreground">
          Please review your wallet settings before creation
        </p>
      </div>

      <div className="rounded-lg border p-4 space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Wallet Type</p>
            <p className="text-sm">
              {formValues.walletType === 'smart' ? 'Smart Wallet' : 'Standard Wallet'}
            </p>
          </div>

          <div>
            <p className="text-sm font-medium">Network</p>
            <p className="text-sm">
              {formValues.network.charAt(0).toUpperCase() + formValues.network.slice(1)}
              {formValues.useTestnet ? ' Testnet' : ' Mainnet'}
            </p>
          </div>

          {formValues.walletType === 'smart' && (
            <div>
              <p className="text-sm font-medium">Security Level</p>
              <p className="text-sm">
                {formValues.securityLevel === 'standard' ? 'Standard Security' : 'High Security'}
              </p>
            </div>
          )}

          <div>
            <p className="text-sm font-medium">Environment</p>
            <p className="text-sm">
              {formValues.useTestnet ? 'Test Environment' : 'Production Environment'}
            </p>
          </div>
        </div>

        <Alert className="mt-4">
          <Info className="h-4 w-4" />
          <AlertTitle>Important Information</AlertTitle>
          <AlertDescription>
            <p className="mb-2">
              {formValues.useTestnet
                ? 'This wallet will be created on a testnet and will require test tokens to operate.'
                : 'This wallet will be created on mainnet and will require real tokens for transactions.'}
            </p>
            <p>
              {formValues.walletType === 'smart'
                ? 'Your smart wallet will be deployed as a smart contract on the blockchain.'
                : 'Your standard wallet will be created with a private key that you must securely back up.'}
            </p>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
}
