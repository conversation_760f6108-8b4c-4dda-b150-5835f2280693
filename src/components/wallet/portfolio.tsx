"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { truncateAddress } from "@/lib/utils";

interface Token {
  contractAddress: string;
  name: string | null;
  symbol: string | null;
  decimals: number | null;
  formattedBalance: string;
}

interface NFT {
  contractAddress: string;
  tokenId: string;
  name: string | null;
  description: string | null;
  tokenType: string | null;
  media: any[];
}

interface Transfer {
  hash: string;
  from: string;
  to: string;
  asset: string;
  value: number;
  direction: "in" | "out";
  metadata: {
    blockTimestamp: string;
  };
}

interface Portfolio {
  nativeBalance: {
    eth: string;
    wei: string;
  };
  tokens: Token[];
  nfts: NFT[];
  transfers?: Transfer[];
}

export default function PortfolioComponent() {
  const { data: session } = useSession();
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("assets");

  useEffect(() => {
    if (session?.user) {
      fetchPortfolio();
    }
  }, [session]);

  const fetchPortfolio = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/wallet/portfolio?includeTransfers=true");
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch portfolio");
      }
      
      const data = await response.json();
      setPortfolio(data.portfolio);
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error fetching portfolio:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={fetchPortfolio}>Retry</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!portfolio) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="mb-4">No portfolio data available.</p>
            <Button onClick={fetchPortfolio}>Refresh</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Portfolio</span>
          <Button variant="outline" size="sm" onClick={fetchPortfolio}>
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <h3 className="text-lg font-medium">Balance</h3>
          <p className="text-3xl font-bold">{portfolio.nativeBalance.eth} ETH</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full mb-4">
            <TabsTrigger value="assets" className="flex-1">Assets</TabsTrigger>
            <TabsTrigger value="nfts" className="flex-1">NFTs</TabsTrigger>
            <TabsTrigger value="activity" className="flex-1">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="assets">
            {portfolio.tokens.length > 0 ? (
              <div className="space-y-4">
                {portfolio.tokens.map((token) => (
                  <div key={token.contractAddress} className="flex justify-between items-center p-3 border rounded-md">
                    <div>
                      <p className="font-medium">{token.name || "Unknown Token"}</p>
                      <p className="text-sm text-muted-foreground">{token.symbol || truncateAddress(token.contractAddress)}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{token.formattedBalance}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center py-4 text-muted-foreground">No tokens found in your wallet.</p>
            )}
          </TabsContent>

          <TabsContent value="nfts">
            {portfolio.nfts.length > 0 ? (
              <div className="grid grid-cols-2 gap-4">
                {portfolio.nfts.map((nft) => (
                  <div key={`${nft.contractAddress}-${nft.tokenId}`} className="border rounded-md overflow-hidden">
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      {nft.media && nft.media.length > 0 ? (
                        <img 
                          src={nft.media[0].gateway || nft.media[0].raw} 
                          alt={nft.name || "NFT"} 
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="text-4xl">🖼️</div>
                      )}
                    </div>
                    <div className="p-3">
                      <p className="font-medium truncate">{nft.name || `NFT #${nft.tokenId}`}</p>
                      <p className="text-sm text-muted-foreground truncate">{nft.tokenType || "NFT"}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center py-4 text-muted-foreground">No NFTs found in your wallet.</p>
            )}
          </TabsContent>

          <TabsContent value="activity">
            {portfolio.transfers && portfolio.transfers.length > 0 ? (
              <div className="space-y-4">
                {portfolio.transfers.map((transfer) => (
                  <div key={transfer.hash} className="flex justify-between items-center p-3 border rounded-md">
                    <div>
                      <p className="font-medium">
                        {transfer.direction === "in" ? "Received" : "Sent"} {transfer.asset}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {transfer.direction === "in" 
                          ? `From: ${truncateAddress(transfer.from)}` 
                          : `To: ${truncateAddress(transfer.to)}`}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(transfer.metadata.blockTimestamp).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${transfer.direction === "in" ? "text-green-600" : "text-red-600"}`}>
                        {transfer.direction === "in" ? "+" : "-"}{transfer.value}
                      </p>
                      <a 
                        href={`https://sepolia.etherscan.io/tx/${transfer.hash}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:underline"
                      >
                        View on Etherscan
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center py-4 text-muted-foreground">No transaction history found.</p>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
