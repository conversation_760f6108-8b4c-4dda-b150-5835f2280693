'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import {
  Loader2,
  ArrowRight,
  ArrowLeft,
  Info,
  AlertCircle,
  Check,
  ChevronDown,
  ChevronUp,
  Wallet,
  CreditCard,
  Zap,
  Clock,
  Shield
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';
import { z } from 'zod';
import { ValidatedForm } from '@/components/forms/validated-form';

// Transaction steps
enum TransactionStep {
  RECIPIENT = 1,
  AMOUNT = 2,
  REVIEW = 3,
  CONFIRMATION = 4,
}

// Transaction form schema
const transactionSchema = z.object({
  recipient: z.string().min(42, "Please enter a valid wallet address"),
  amount: z.string().min(1, "Amount is required"),
  gasOption: z.enum(["standard", "fast", "instant"]).default("standard"),
  showAdvanced: z.boolean().default(false),
  gasLimit: z.string().optional(),
  maxFeePerGas: z.string().optional(),
  maxPriorityFee: z.string().optional(),
  memo: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof transactionSchema>;

interface GuidedTransactionInterfaceProps {
  walletAddress?: string;
  balance?: string;
  network?: string;
  onTransactionComplete?: () => void;
}

export function GuidedTransactionInterface({
  walletAddress = "0x1234...5678",
  balance = "1000",
  network = "Polygon",
  onTransactionComplete
}: GuidedTransactionInterfaceProps) {
  const [currentStep, setCurrentStep] = useState<TransactionStep>(TransactionStep.RECIPIENT);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [formValues, setFormValues] = useState<Partial<TransactionFormValues>>({
    recipient: "",
    amount: "",
    gasOption: "standard",
    showAdvanced: false,
    memo: "",
  });
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  const totalSteps = 4;

  // Update form values
  const updateFormValue = (field: keyof TransactionFormValues, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle step navigation
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle transaction submission
  const handleSubmitTransaction = async () => {
    try {
      setIsSubmitting(true);

      // Simulate transaction processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock transaction hash
      setTransactionHash("******************************************90abcdef1234567890abcdef");

      toast({
        title: "Transaction Submitted",
        description: "Your transaction has been submitted to the network.",
      });

      // Move to confirmation step
      setCurrentStep(TransactionStep.CONFIRMATION);

      if (onTransactionComplete) {
        onTransactionComplete();
      }
    } catch (error) {
      console.error("Error submitting transaction:", error);
      toast({
        variant: "destructive",
        title: "Transaction Failed",
        description: error instanceof Error ? error.message : "Failed to submit transaction",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case TransactionStep.RECIPIENT:
        return "Enter Recipient";
      case TransactionStep.AMOUNT:
        return "Specify Amount";
      case TransactionStep.REVIEW:
        return "Review & Confirm";
      case TransactionStep.CONFIRMATION:
        return "Transaction Submitted";
      default:
        return "";
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case TransactionStep.RECIPIENT:
        return (
          <RecipientStep
            value={formValues.recipient || ""}
            onChange={(value) => updateFormValue("recipient", value)}
          />
        );
      case TransactionStep.AMOUNT:
        return (
          <AmountStep
            value={formValues.amount || ""}
            balance={balance}
            gasOption={formValues.gasOption as "standard" | "fast" | "instant" | undefined}
            showAdvanced={showAdvancedOptions}
            onAmountChange={(value) => updateFormValue("amount", value)}
            onGasOptionChange={(value) => updateFormValue("gasOption", value)}
            onToggleAdvanced={setShowAdvancedOptions}
            onGasLimitChange={(value) => updateFormValue("gasLimit", value)}
            onMaxFeeChange={(value) => updateFormValue("maxFeePerGas", value)}
            onPriorityFeeChange={(value) => updateFormValue("maxPriorityFee", value)}
            onMemoChange={(value) => updateFormValue("memo", value)}
          />
        );
      case TransactionStep.REVIEW:
        return (
          <ReviewStep
            formValues={formValues}
            walletAddress={walletAddress}
            balance={balance}
            network={network}
          />
        );
      case TransactionStep.CONFIRMATION:
        return (
          <ConfirmationStep
            transactionHash={transactionHash || ""}
            network={network}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Send Transaction</CardTitle>
        <CardDescription>
          Transfer tokens from your wallet to another address
        </CardDescription>
        {currentStep !== TransactionStep.CONFIRMATION && (
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-2">
              <span>Step {currentStep} of {totalSteps - 1}</span>
              <span>{getStepTitle()}</span>
            </div>
            <Progress value={(currentStep / (totalSteps - 1)) * 100} className="h-2" />
          </div>
        )}
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-[300px]"
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>
      </CardContent>
      {currentStep !== TransactionStep.CONFIRMATION && (
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === TransactionStep.RECIPIENT || isSubmitting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          {currentStep === TransactionStep.REVIEW ? (
            <Button
              type="button"
              onClick={handleSubmitTransaction}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  Submit Transaction
                  <Zap className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          ) : (
            <Button
              type="button"
              onClick={nextStep}
              disabled={
                (currentStep === TransactionStep.RECIPIENT && !formValues.recipient) ||
                (currentStep === TransactionStep.AMOUNT && !formValues.amount) ||
                isSubmitting
              }
            >
              Continue
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      )}
    </Card>
  );
}

// Step 1: Recipient Address
function RecipientStep({ value, onChange }: { value: string, onChange: (value: string) => void }) {
  const [recentAddresses] = useState([
    { name: "Carbon Project A", address: "******************************************" },
    { name: "Trading Partner B", address: "******************************************" },
    { name: "Retirement Wallet", address: "******************************************" },
  ]);

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Enter Recipient Address</h3>
        <p className="text-sm text-muted-foreground">
          Enter the wallet address where you want to send tokens
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="recipient">Recipient Address</Label>
          <Input
            id="recipient"
            placeholder="0x..."
            value={value}
            onChange={(e) => onChange(e.target.value)}
          />
          <p className="text-xs text-muted-foreground">
            Enter a valid blockchain address starting with 0x
          </p>
        </div>

        {recentAddresses.length > 0 && (
          <div className="space-y-2">
            <Label>Recent Addresses</Label>
            <div className="space-y-2">
              {recentAddresses.map((addr, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-md border hover:bg-accent cursor-pointer"
                  onClick={() => onChange(addr.address)}
                >
                  <div>
                    <p className="font-medium text-sm">{addr.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {addr.address.substring(0, 6)}...{addr.address.substring(addr.address.length - 4)}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => onChange(addr.address)}>
                    Use
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Double-check the address</AlertTitle>
          <AlertDescription>
            Blockchain transactions cannot be reversed. Always verify the recipient address before proceeding.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
}

// Step 2: Amount and Gas Settings
function AmountStep({
  value,
  balance,
  gasOption,
  showAdvanced,
  onAmountChange,
  onGasOptionChange,
  onToggleAdvanced,
  onGasLimitChange,
  onMaxFeeChange,
  onPriorityFeeChange,
  onMemoChange
}: {
  value: string,
  balance: string,
  gasOption?: "standard" | "fast" | "instant",
  showAdvanced: boolean,
  onAmountChange: (value: string) => void,
  onGasOptionChange: (value: "standard" | "fast" | "instant") => void,
  onToggleAdvanced: (value: boolean) => void,
  onGasLimitChange: (value: string) => void,
  onMaxFeeChange: (value: string) => void,
  onPriorityFeeChange: (value: string) => void,
  onMemoChange: (value: string) => void
}) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Specify Amount</h3>
        <p className="text-sm text-muted-foreground">
          Enter the amount you want to send
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between">
            <Label htmlFor="amount">Amount</Label>
            <span className="text-sm text-muted-foreground">Balance: {balance} USDC</span>
          </div>
          <div className="flex space-x-2">
            <Input
              id="amount"
              placeholder="0.00"
              value={value}
              onChange={(e) => onAmountChange(e.target.value)}
              className="flex-1"
            />
            <Button
              variant="outline"
              onClick={() => onAmountChange(balance)}
              type="button"
            >
              Max
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Transaction Speed</Label>
          <div className="grid grid-cols-3 gap-2">
            <div
              className={`border rounded-md p-3 text-center cursor-pointer transition-colors ${
                gasOption === "standard" ? "border-primary bg-primary/5" : "hover:border-primary/50"
              }`}
              onClick={() => onGasOptionChange("standard")}
            >
              <p className="font-medium">Standard</p>
              <p className="text-xs text-muted-foreground">~5 min</p>
            </div>
            <div
              className={`border rounded-md p-3 text-center cursor-pointer transition-colors ${
                gasOption === "fast" ? "border-primary bg-primary/5" : "hover:border-primary/50"
              }`}
              onClick={() => onGasOptionChange("fast")}
            >
              <p className="font-medium">Fast</p>
              <p className="text-xs text-muted-foreground">~1 min</p>
            </div>
            <div
              className={`border rounded-md p-3 text-center cursor-pointer transition-colors ${
                gasOption === "instant" ? "border-primary bg-primary/5" : "hover:border-primary/50"
              }`}
              onClick={() => onGasOptionChange("instant")}
            >
              <p className="font-medium">Instant</p>
              <p className="text-xs text-muted-foreground">~15 sec</p>
            </div>
          </div>
        </div>

        <Collapsible open={showAdvanced} onOpenChange={onToggleAdvanced}>
          <div className="flex items-center justify-between">
            <Label htmlFor="advanced-toggle" className="cursor-pointer">Advanced Options</Label>
            <div className="flex items-center space-x-2">
              <Switch
                id="advanced-toggle"
                checked={showAdvanced}
                onCheckedChange={onToggleAdvanced}
              />
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {showAdvanced ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>

          <CollapsibleContent className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="gas-limit">Gas Limit</Label>
              <Input
                id="gas-limit"
                placeholder="21000"
                onChange={(e) => onGasLimitChange(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                The maximum amount of gas units this transaction can consume
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="max-fee">Max Fee (Gwei)</Label>
              <Input
                id="max-fee"
                placeholder="30"
                onChange={(e) => onMaxFeeChange(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority-fee">Priority Fee (Gwei)</Label>
              <Input
                id="priority-fee"
                placeholder="1.5"
                onChange={(e) => onPriorityFeeChange(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="memo">Memo (Optional)</Label>
              <Input
                id="memo"
                placeholder="Payment for carbon credits"
                onChange={(e) => onMemoChange(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Add a note to this transaction (not stored on blockchain)
              </p>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}

// Step 3: Review Transaction
function ReviewStep({
  formValues,
  walletAddress,
  balance,
  network
}: {
  formValues: Partial<TransactionFormValues>,
  walletAddress: string,
  balance: string,
  network: string
}) {
  // Calculate estimated gas cost based on gas option
  const getGasCost = () => {
    switch (formValues.gasOption) {
      case "standard": return "0.001";
      case "fast": return "0.002";
      case "instant": return "0.003";
      default: return "0.001";
    }
  };

  const gasCost = getGasCost();
  const totalCost = parseFloat(formValues.amount || "0") + parseFloat(gasCost);

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Review Transaction</h3>
        <p className="text-sm text-muted-foreground">
          Please review your transaction details before submitting
        </p>
      </div>

      <div className="rounded-lg border p-4 space-y-4">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium">From</span>
            <span className="text-sm">{walletAddress}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">To</span>
            <span className="text-sm">
              {formValues.recipient?.substring(0, 6)}...{formValues.recipient?.substring((formValues.recipient?.length || 0) - 4)}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Amount</span>
            <span className="text-sm">{formValues.amount} USDC</span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Network</span>
            <span className="text-sm">{network}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Speed</span>
            <span className="text-sm capitalize">{formValues.gasOption}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Estimated Gas</span>
            <span className="text-sm">{gasCost} MATIC</span>
          </div>

          {formValues.memo && (
            <div className="flex justify-between">
              <span className="text-sm font-medium">Memo</span>
              <span className="text-sm">{formValues.memo}</span>
            </div>
          )}

          <Separator />

          <div className="flex justify-between font-medium">
            <span>Total</span>
            <span>{totalCost.toFixed(6)} USDC + Gas</span>
          </div>
        </div>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Important</AlertTitle>
        <AlertDescription>
          This transaction will be submitted to the blockchain and cannot be reversed once confirmed.
        </AlertDescription>
      </Alert>
    </div>
  );
}

// Step 4: Confirmation
function ConfirmationStep({
  transactionHash,
  network
}: {
  transactionHash: string,
  network: string
}) {
  const getExplorerUrl = () => {
    // In a real implementation, this would return the actual block explorer URL
    return `https://explorer.${network.toLowerCase()}.network/tx/${transactionHash}`;
  };

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-green-50 p-6 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <Check className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="mt-3 text-lg font-medium text-green-800">
          Transaction Submitted
        </h3>
        <p className="mt-1 text-sm text-green-700">
          Your transaction has been submitted to the network
        </p>
      </div>

      <div className="rounded-lg border p-4 space-y-4">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium">Transaction Hash</span>
            <span className="text-sm font-mono">
              {transactionHash.substring(0, 10)}...{transactionHash.substring(transactionHash.length - 8)}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Status</span>
            <span className="text-sm flex items-center">
              <Clock className="mr-1 h-4 w-4 text-yellow-500" />
              Pending
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Network</span>
            <span className="text-sm">{network}</span>
          </div>
        </div>
      </div>

      <div className="flex flex-col space-y-2">
        <Button variant="outline" onClick={() => window.open(getExplorerUrl(), '_blank')}>
          View on Block Explorer
        </Button>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Transaction Processing</AlertTitle>
          <AlertDescription>
            Your transaction is being processed by the network. This may take a few minutes depending on network congestion.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
}
}
