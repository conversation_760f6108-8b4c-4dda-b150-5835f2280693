'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { SupportedNetwork } from '@/lib/blockchain-config';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RequiredLabel } from '@/components/ui/required-label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Wallet, Shield, Zap, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ValidatedForm } from '@/components/forms/validated-form';
import { z } from 'zod';

// Form schema - we'll use this locally since it's different from the walletCreationSchema in our validation library
const smartWalletFormSchema = z.object({
  network: z.enum(['ethereum', 'polygon', 'arbitrum', 'optimism', 'base']),
  useTestnet: z.boolean().default(true),
  walletType: z.enum(['smart', 'standard']).default('smart'),
});

type SmartWalletFormValues = {
  network: 'ethereum' | 'polygon' | 'arbitrum' | 'optimism' | 'base';
  useTestnet: boolean;
  walletType: 'smart' | 'standard';
};

export function SmartWalletSetup() {
  const router = useRouter();

  // Default form values
  const defaultValues: SmartWalletFormValues = {
    network: 'ethereum',
    useTestnet: true,
    walletType: 'smart',
  };

  // Handle form submission
  const onSubmit = async (data: SmartWalletFormValues) => {
    try {
      const response = await fetch('/api/wallet/smart-wallet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          network: data.network,
          useTestnet: data.useTestnet,
          isSmartWallet: data.walletType === 'smart',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create wallet');
      }

      const result = await response.json();

      toast({
        title: 'Wallet Created',
        description: `Your ${data.walletType} wallet has been created successfully.`,
      });

      // Redirect to wallet page
      router.push(`/wallet/${result.wallet.id}`);

      return data;
    } catch (error) {
      console.error('Error creating wallet:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to create wallet');
    }
  };

  return (
    <Card className="w-full max-w-lg">
      <CardHeader>
        <CardTitle>Create a New Wallet</CardTitle>
        <CardDescription>
          Set up a wallet to manage your carbon credits and transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ValidatedForm
          schema={smartWalletFormSchema}
          defaultValues={defaultValues}
          onSubmit={onSubmit}
          className="space-y-8"
        >
          {({ control, formState, isSubmitting, formError }) => (
            <>
            {formError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{formError.message}</AlertDescription>
              </Alert>
            )}
            {/* Wallet Configuration */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Wallet Configuration</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Choose the type of wallet that best suits your needs
              </p>

              <FormField
                control={control}
                name="walletType"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <RequiredLabel>Wallet Type</RequiredLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="grid grid-cols-2 gap-4"
                      >
                        <FormItem>
                          <FormLabel className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                            <FormControl>
                              <RadioGroupItem value="smart" className="sr-only" />
                            </FormControl>
                            <Shield className="mb-3 h-6 w-6" />
                            <div className="space-y-1 text-center">
                              <FormLabel className="text-base">Smart Wallet</FormLabel>
                              <FormDescription>
                                Enhanced security with account abstraction
                              </FormDescription>
                            </div>
                          </FormLabel>
                        </FormItem>
                        <FormItem>
                          <FormLabel className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                            <FormControl>
                              <RadioGroupItem value="standard" className="sr-only" />
                            </FormControl>
                            <Wallet className="mb-3 h-6 w-6" />
                            <div className="space-y-1 text-center">
                              <FormLabel className="text-base">Standard Wallet</FormLabel>
                              <FormDescription>
                                Traditional blockchain wallet
                              </FormDescription>
                            </div>
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Network Settings */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Network Settings</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Configure the blockchain network for your wallet
              </p>

              <FormField
                control={control}
                name="network"
                render={({ field }) => (
                  <FormItem>
                    <RequiredLabel>Blockchain Network</RequiredLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a network" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ethereum">Ethereum</SelectItem>
                        <SelectItem value="polygon">Polygon</SelectItem>
                        <SelectItem value="arbitrum">Arbitrum</SelectItem>
                        <SelectItem value="optimism">Optimism</SelectItem>
                        <SelectItem value="base">Base</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the blockchain network for your wallet
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="useTestnet"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Use Testnet</FormLabel>
                      <FormDescription>
                        Use test networks with free tokens for development
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            </>
          )}
        </ValidatedForm>
      </CardContent>
      <CardFooter>
        <Button
          type="submit"
          form="validated-form"
          className="w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Wallet...
            </>
          ) : (
            <>
              <Zap className="mr-2 h-4 w-4" />
              Create Wallet
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
