"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Switch 
} from "@/components/ui/switch";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  Shield, 
  Lock, 
  AlertTriangle, 
  Key, 
  UserPlus, 
  DollarSign, 
  Clock, 
  Save, 
  RefreshCw, 
  Info 
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { truncateAddress } from "@/lib/utils";

interface SecuritySettingsProps {
  walletId?: string;
  onSave?: (settings: any) => void;
}

export default function SecuritySettings({ walletId, onSave }: SecuritySettingsProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("general");
  const [isLoading, setIsLoading] = useState(false);
  const [securitySettings, setSecuritySettings] = useState<any>(null);
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false);
  const [recoveryEmail, setRecoveryEmail] = useState("");
  const [recoveryPhone, setRecoveryPhone] = useState("");
  
  // Security settings state
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [transactionNotifications, setTransactionNotifications] = useState(true);
  const [unusualActivityAlerts, setUnusualActivityAlerts] = useState(true);
  const [dailyLimit, setDailyLimit] = useState("1000");
  const [transactionLimit, setTransactionLimit] = useState("500");
  const [approvalRequired, setApprovalRequired] = useState(false);
  const [approvalThreshold, setApprovalThreshold] = useState("1000");
  const [trustedAddresses, setTrustedAddresses] = useState<string[]>([]);
  const [newTrustedAddress, setNewTrustedAddress] = useState("");
  const [whitelistOnly, setWhitelistOnly] = useState(false);
  const [recoveryEnabled, setRecoveryEnabled] = useState(false);
  const [recoverySetup, setRecoverySetup] = useState(false);
  const [delayedWithdrawals, setDelayedWithdrawals] = useState(false);
  const [withdrawalDelay, setWithdrawalDelay] = useState("24");

  // Fetch security settings
  useEffect(() => {
    if (walletId) {
      const fetchSecuritySettings = async () => {
        try {
          const response = await fetch(`/api/wallet/${walletId}/security`);
          
          if (!response.ok) {
            throw new Error("Failed to fetch security settings");
          }
          
          const data = await response.json();
          setSecuritySettings(data);
          
          // Apply saved settings
          if (data) {
            setTwoFactorEnabled(data.twoFactorEnabled || false);
            setTransactionNotifications(data.transactionNotifications || true);
            setUnusualActivityAlerts(data.unusualActivityAlerts || true);
            setDailyLimit(data.dailyLimit?.toString() || "1000");
            setTransactionLimit(data.transactionLimit?.toString() || "500");
            setApprovalRequired(data.approvalRequired || false);
            setApprovalThreshold(data.approvalThreshold?.toString() || "1000");
            setTrustedAddresses(data.trustedAddresses || []);
            setWhitelistOnly(data.whitelistOnly || false);
            setRecoveryEnabled(data.recoveryEnabled || false);
            setRecoverySetup(data.recoverySetup || false);
            setDelayedWithdrawals(data.delayedWithdrawals || false);
            setWithdrawalDelay(data.withdrawalDelay?.toString() || "24");
            
            if (data.recoveryEmail) {
              setRecoveryEmail(data.recoveryEmail);
            }
            
            if (data.recoveryPhone) {
              setRecoveryPhone(data.recoveryPhone);
            }
          }
        } catch (error) {
          console.error("Error fetching security settings:", error);
        }
      };
      
      fetchSecuritySettings();
    }
  }, [walletId]);

  // Save security settings
  const saveSecuritySettings = async () => {
    if (!walletId) return;
    
    setIsLoading(true);
    
    try {
      const settings = {
        twoFactorEnabled,
        transactionNotifications,
        unusualActivityAlerts,
        dailyLimit: parseFloat(dailyLimit),
        transactionLimit: parseFloat(transactionLimit),
        approvalRequired,
        approvalThreshold: parseFloat(approvalThreshold),
        trustedAddresses,
        whitelistOnly,
        recoveryEnabled,
        recoverySetup,
        recoveryEmail,
        recoveryPhone,
        delayedWithdrawals,
        withdrawalDelay: parseInt(withdrawalDelay),
      };
      
      const response = await fetch(`/api/wallet/${walletId}/security`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save security settings");
      }
      
      toast({
        title: "Security Settings Saved",
        description: "Your wallet security settings have been updated successfully.",
      });
      
      // Update saved settings
      setSecuritySettings(settings);
      
      // Call onSave callback if provided
      if (onSave) {
        onSave(settings);
      }
    } catch (error) {
      console.error("Error saving security settings:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save security settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add trusted address
  const addTrustedAddress = () => {
    if (!newTrustedAddress) return;
    
    // Simple validation for Ethereum address
    if (!/^0x[a-fA-F0-9]{40}$/.test(newTrustedAddress)) {
      toast({
        title: "Invalid Address",
        description: "Please enter a valid Ethereum address.",
        variant: "destructive",
      });
      return;
    }
    
    // Check if address already exists
    if (trustedAddresses.includes(newTrustedAddress)) {
      toast({
        title: "Address Already Added",
        description: "This address is already in your trusted addresses list.",
        variant: "destructive",
      });
      return;
    }
    
    setTrustedAddresses([...trustedAddresses, newTrustedAddress]);
    setNewTrustedAddress("");
  };

  // Remove trusted address
  const removeTrustedAddress = (address: string) => {
    setTrustedAddresses(trustedAddresses.filter(a => a !== address));
  };

  // Setup recovery
  const setupRecovery = () => {
    if (!recoveryEmail && !recoveryPhone) {
      toast({
        title: "Missing Information",
        description: "Please provide at least an email or phone number for recovery.",
        variant: "destructive",
      });
      return;
    }
    
    // Email validation
    if (recoveryEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(recoveryEmail)) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }
    
    // Phone validation (simple)
    if (recoveryPhone && !/^\+?[0-9]{10,15}$/.test(recoveryPhone)) {
      toast({
        title: "Invalid Phone Number",
        description: "Please enter a valid phone number.",
        variant: "destructive",
      });
      return;
    }
    
    setRecoverySetup(true);
    setRecoveryEnabled(true);
    setShowRecoveryDialog(false);
    
    toast({
      title: "Recovery Setup Complete",
      description: "Your wallet recovery options have been set up successfully.",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          Wallet Security
        </CardTitle>
        <CardDescription>
          Configure security settings for your wallet
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full mb-4">
            <TabsTrigger value="general" className="flex items-center">
              <Lock className="mr-2 h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="limits" className="flex items-center">
              <DollarSign className="mr-2 h-4 w-4" />
              Limits
            </TabsTrigger>
            <TabsTrigger value="access" className="flex items-center">
              <UserPlus className="mr-2 h-4 w-4" />
              Access Control
            </TabsTrigger>
            <TabsTrigger value="recovery" className="flex items-center">
              <Key className="mr-2 h-4 w-4" />
              Recovery
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="flex items-center">
                    Two-Factor Authentication
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Require 2FA for all transactions from this wallet.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Switch
                    checked={twoFactorEnabled}
                    onCheckedChange={setTwoFactorEnabled}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Require two-factor authentication for all transactions
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Transaction Notifications</Label>
                  <Switch
                    checked={transactionNotifications}
                    onCheckedChange={setTransactionNotifications}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Receive notifications for all transactions
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Unusual Activity Alerts</Label>
                  <Switch
                    checked={unusualActivityAlerts}
                    onCheckedChange={setUnusualActivityAlerts}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Get alerts for suspicious or unusual activity
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Delayed Withdrawals</Label>
                  <Switch
                    checked={delayedWithdrawals}
                    onCheckedChange={setDelayedWithdrawals}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Add a time delay before withdrawals are processed
                </p>
                
                {delayedWithdrawals && (
                  <div className="pt-2">
                    <Label>Withdrawal Delay (hours)</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Input
                        type="number"
                        value={withdrawalDelay}
                        onChange={(e) => setWithdrawalDelay(e.target.value)}
                        min="1"
                        max="72"
                      />
                      <span className="text-sm">hours</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Enabling security features adds protection but may affect transaction speed. Consider your security needs carefully.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="limits" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Daily Transaction Limit</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    value={dailyLimit}
                    onChange={(e) => setDailyLimit(e.target.value)}
                    min="0"
                  />
                  <span className="text-sm">ETH</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Maximum amount that can be sent in a 24-hour period
                </p>
              </div>
              
              <div className="space-y-2">
                <Label>Single Transaction Limit</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    value={transactionLimit}
                    onChange={(e) => setTransactionLimit(e.target.value)}
                    min="0"
                  />
                  <span className="text-sm">ETH</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Maximum amount that can be sent in a single transaction
                </p>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="flex items-center">
                    Require Approval for Large Transactions
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Transactions above the threshold will require additional approval.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Switch
                    checked={approvalRequired}
                    onCheckedChange={setApprovalRequired}
                  />
                </div>
                
                {approvalRequired && (
                  <div className="pt-2">
                    <Label>Approval Threshold</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Input
                        type="number"
                        value={approvalThreshold}
                        onChange={(e) => setApprovalThreshold(e.target.value)}
                        min="0"
                      />
                      <span className="text-sm">ETH</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Transactions above this amount will require additional approval
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-muted p-4 rounded-md">
              <h3 className="text-sm font-medium mb-2">Current Usage</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Today's Usage</span>
                  <span className="text-sm font-medium">0.5 / {dailyLimit} ETH</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Largest Transaction (24h)</span>
                  <span className="text-sm font-medium">0.2 ETH</span>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="access" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Only Allow Whitelisted Addresses</Label>
                  <Switch
                    checked={whitelistOnly}
                    onCheckedChange={setWhitelistOnly}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Only allow transactions to addresses in your trusted list
                </p>
              </div>
              
              <div className="space-y-2">
                <Label>Trusted Addresses</Label>
                <div className="flex space-x-2">
                  <Input
                    placeholder="0x..."
                    value={newTrustedAddress}
                    onChange={(e) => setNewTrustedAddress(e.target.value)}
                  />
                  <Button
                    onClick={addTrustedAddress}
                    disabled={!newTrustedAddress}
                  >
                    Add
                  </Button>
                </div>
                
                <div className="space-y-2 mt-2">
                  {trustedAddresses.length > 0 ? (
                    trustedAddresses.map((address) => (
                      <div key={address} className="flex justify-between items-center p-2 border rounded-md">
                        <span className="text-sm">{truncateAddress(address)}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTrustedAddress(address)}
                          className="h-8 w-8 p-0"
                        >
                          <span className="sr-only">Remove</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-4 w-4"
                          >
                            <path d="M18 6L6 18M6 6l12 12" />
                          </svg>
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No trusted addresses added yet
                    </p>
                  )}
                </div>
              </div>
              
              {whitelistOnly && trustedAddresses.length === 0 && (
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        Warning: You have enabled whitelist-only mode but haven't added any trusted addresses. You won't be able to send transactions until you add at least one address.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="recovery" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Enable Wallet Recovery</Label>
                  <Switch
                    checked={recoveryEnabled}
                    onCheckedChange={setRecoveryEnabled}
                    disabled={!recoverySetup}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Enable recovery options for your wallet
                </p>
              </div>
              
              {recoverySetup ? (
                <div className="space-y-4">
                  <div className="bg-green-50 border-l-4 border-green-400 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-green-500"
                        >
                          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                          <polyline points="22 4 12 14.01 9 11.01" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-green-700">
                          Recovery options are set up and {recoveryEnabled ? "enabled" : "disabled"}.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Recovery Email</Label>
                    <Input
                      value={recoveryEmail}
                      onChange={(e) => setRecoveryEmail(e.target.value)}
                      disabled={!recoveryEnabled}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Recovery Phone</Label>
                    <Input
                      value={recoveryPhone}
                      onChange={(e) => setRecoveryPhone(e.target.value)}
                      disabled={!recoveryEnabled}
                    />
                  </div>
                  
                  <Button
                    variant="outline"
                    onClick={() => setShowRecoveryDialog(true)}
                    disabled={!recoveryEnabled}
                  >
                    Update Recovery Options
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <AlertTriangle className="h-5 w-5 text-yellow-400" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          Recovery options are not set up. If you lose access to your wallet, you may not be able to recover your funds.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    onClick={() => setShowRecoveryDialog(true)}
                    className="w-full"
                  >
                    Set Up Recovery Options
                  </Button>
                </div>
              )}
            </div>
            
            <Dialog open={showRecoveryDialog} onOpenChange={setShowRecoveryDialog}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Set Up Wallet Recovery</DialogTitle>
                  <DialogDescription>
                    Add recovery options to help you regain access to your wallet if you lose your private key.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Recovery Email</Label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={recoveryEmail}
                      onChange={(e) => setRecoveryEmail(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      We'll send recovery instructions to this email
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Recovery Phone Number</Label>
                    <Input
                      type="tel"
                      placeholder="+1234567890"
                      value={recoveryPhone}
                      onChange={(e) => setRecoveryPhone(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      We'll send recovery codes to this phone number
                    </p>
                  </div>
                  
                  <div className="bg-muted p-4 rounded-md">
                    <p className="text-sm">
                      <strong>Important:</strong> Make sure you have access to the email and phone number you provide. You'll need them to recover your wallet.
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowRecoveryDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={setupRecovery}
                    disabled={!recoveryEmail && !recoveryPhone}
                  >
                    Save Recovery Options
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => {
            // Reset to saved settings
            if (securitySettings) {
              setTwoFactorEnabled(securitySettings.twoFactorEnabled || false);
              setTransactionNotifications(securitySettings.transactionNotifications || true);
              setUnusualActivityAlerts(securitySettings.unusualActivityAlerts || true);
              setDailyLimit(securitySettings.dailyLimit?.toString() || "1000");
              setTransactionLimit(securitySettings.transactionLimit?.toString() || "500");
              setApprovalRequired(securitySettings.approvalRequired || false);
              setApprovalThreshold(securitySettings.approvalThreshold?.toString() || "1000");
              setTrustedAddresses(securitySettings.trustedAddresses || []);
              setWhitelistOnly(securitySettings.whitelistOnly || false);
              setRecoveryEnabled(securitySettings.recoveryEnabled || false);
              setRecoverySetup(securitySettings.recoverySetup || false);
              setDelayedWithdrawals(securitySettings.delayedWithdrawals || false);
              setWithdrawalDelay(securitySettings.withdrawalDelay?.toString() || "24");
              
              if (securitySettings.recoveryEmail) {
                setRecoveryEmail(securitySettings.recoveryEmail);
              }
              
              if (securitySettings.recoveryPhone) {
                setRecoveryPhone(securitySettings.recoveryPhone);
              }
            }
          }}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Reset
        </Button>
        <Button
          onClick={saveSecuritySettings}
          disabled={isLoading}
        >
          <Save className="mr-2 h-4 w-4" />
          {isLoading ? "Saving..." : "Save Settings"}
        </Button>
      </CardFooter>
    </Card>
  );
}
