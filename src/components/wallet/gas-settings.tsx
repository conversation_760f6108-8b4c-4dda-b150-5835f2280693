"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Slider 
} from "@/components/ui/slider";
import { 
  Switch 
} from "@/components/ui/switch";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  Flame, 
  Zap, 
  Settings, 
  Info, 
  Save, 
  RotateCcw, 
  Clock, 
  CheckCircle, 
  AlertTriangle 
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { useToast } from "@/components/ui/use-toast";

interface GasSettingsProps {
  walletId?: string;
  network?: SupportedNetwork;
  onSave?: (settings: any) => void;
}

export default function GasSettings({ walletId, network = SupportedNetwork.ETHEREUM, onSave }: GasSettingsProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("basic");
  const [gasPreset, setGasPreset] = useState("standard");
  const [maxFeePerGas, setMaxFeePerGas] = useState("30");
  const [maxPriorityFeePerGas, setMaxPriorityFeePerGas] = useState("1.5");
  const [gasLimit, setGasLimit] = useState("21000");
  const [useEIP1559, setUseEIP1559] = useState(true);
  const [autoAdjust, setAutoAdjust] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [currentGasPrices, setCurrentGasPrices] = useState<any>(null);
  const [savedSettings, setSavedSettings] = useState<any>(null);

  // Fetch current gas prices
  useEffect(() => {
    const fetchGasPrices = async () => {
      try {
        const response = await fetch(`/api/wallet/gas-prices?network=${network}`);
        
        if (!response.ok) {
          throw new Error("Failed to fetch gas prices");
        }
        
        const data = await response.json();
        setCurrentGasPrices(data);
        
        // Update preset values based on current gas prices
        if (gasPreset === "standard") {
          setMaxFeePerGas(data.standard.maxFeePerGas);
          setMaxPriorityFeePerGas(data.standard.maxPriorityFeePerGas);
        }
      } catch (error) {
        console.error("Error fetching gas prices:", error);
      }
    };
    
    fetchGasPrices();
    
    // Fetch gas prices every 30 seconds
    const interval = setInterval(fetchGasPrices, 30000);
    
    return () => clearInterval(interval);
  }, [network]);

  // Fetch saved gas settings
  useEffect(() => {
    if (walletId) {
      const fetchGasSettings = async () => {
        try {
          const response = await fetch(`/api/wallet/${walletId}/gas-settings`);
          
          if (!response.ok) {
            throw new Error("Failed to fetch gas settings");
          }
          
          const data = await response.json();
          setSavedSettings(data);
          
          // Apply saved settings
          if (data) {
            setUseEIP1559(data.useEIP1559);
            setAutoAdjust(data.autoAdjust);
            setGasPreset(data.preset || "standard");
            setMaxFeePerGas(data.maxFeePerGas || "30");
            setMaxPriorityFeePerGas(data.maxPriorityFeePerGas || "1.5");
            setGasLimit(data.gasLimit || "21000");
          }
        } catch (error) {
          console.error("Error fetching gas settings:", error);
        }
      };
      
      fetchGasSettings();
    }
  }, [walletId]);

  // Update gas settings based on preset
  const updateGasPreset = (preset: string) => {
    setGasPreset(preset);
    
    if (currentGasPrices) {
      switch (preset) {
        case "slow":
          setMaxFeePerGas(currentGasPrices.slow.maxFeePerGas);
          setMaxPriorityFeePerGas(currentGasPrices.slow.maxPriorityFeePerGas);
          break;
        case "standard":
          setMaxFeePerGas(currentGasPrices.standard.maxFeePerGas);
          setMaxPriorityFeePerGas(currentGasPrices.standard.maxPriorityFeePerGas);
          break;
        case "fast":
          setMaxFeePerGas(currentGasPrices.fast.maxFeePerGas);
          setMaxPriorityFeePerGas(currentGasPrices.fast.maxPriorityFeePerGas);
          break;
        case "custom":
          // Keep current values for custom
          break;
      }
    }
  };

  // Save gas settings
  const saveGasSettings = async () => {
    if (!walletId) return;
    
    setIsLoading(true);
    
    try {
      const settings = {
        useEIP1559,
        autoAdjust,
        preset: gasPreset,
        maxFeePerGas,
        maxPriorityFeePerGas,
        gasLimit,
      };
      
      const response = await fetch(`/api/wallet/${walletId}/gas-settings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save gas settings");
      }
      
      toast({
        title: "Gas Settings Saved",
        description: "Your gas settings have been saved successfully.",
      });
      
      // Update saved settings
      setSavedSettings(settings);
      
      // Call onSave callback if provided
      if (onSave) {
        onSave(settings);
      }
    } catch (error) {
      console.error("Error saving gas settings:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save gas settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Reset gas settings
  const resetGasSettings = () => {
    setGasPreset("standard");
    setUseEIP1559(true);
    setAutoAdjust(true);
    
    if (currentGasPrices) {
      setMaxFeePerGas(currentGasPrices.standard.maxFeePerGas);
      setMaxPriorityFeePerGas(currentGasPrices.standard.maxPriorityFeePerGas);
    } else {
      setMaxFeePerGas("30");
      setMaxPriorityFeePerGas("1.5");
    }
    
    setGasLimit("21000");
  };

  // Calculate estimated transaction cost
  const calculateEstimatedCost = () => {
    if (!maxFeePerGas || !gasLimit) return "0.0";
    
    const maxFee = parseFloat(maxFeePerGas);
    const limit = parseFloat(gasLimit);
    
    // Convert from Gwei to ETH
    const costInEth = (maxFee * limit) / 1e9;
    
    return costInEth.toFixed(6);
  };

  // Get network token symbol
  const getNetworkToken = () => {
    switch (network) {
      case SupportedNetwork.POLYGON:
        return "MATIC";
      default:
        return "ETH";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Flame className="mr-2 h-5 w-5" />
          Gas Settings
        </CardTitle>
        <CardDescription>
          Configure gas settings for your transactions on {network}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full mb-4">
            <TabsTrigger value="basic" className="flex items-center">
              <Zap className="mr-2 h-4 w-4" />
              Basic
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              Advanced
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic">
            <div className="space-y-6">
              <div className="space-y-4">
                <Label>Transaction Speed</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant={gasPreset === "slow" ? "default" : "outline"}
                    className="flex flex-col items-center justify-center h-20"
                    onClick={() => updateGasPreset("slow")}
                  >
                    <Clock className="h-6 w-6 mb-1" />
                    <span>Slow</span>
                    <span className="text-xs mt-1">
                      ~{currentGasPrices?.slow?.estimatedSeconds || "30-60"}s
                    </span>
                  </Button>
                  <Button
                    variant={gasPreset === "standard" ? "default" : "outline"}
                    className="flex flex-col items-center justify-center h-20"
                    onClick={() => updateGasPreset("standard")}
                  >
                    <Zap className="h-6 w-6 mb-1" />
                    <span>Standard</span>
                    <span className="text-xs mt-1">
                      ~{currentGasPrices?.standard?.estimatedSeconds || "15-30"}s
                    </span>
                  </Button>
                  <Button
                    variant={gasPreset === "fast" ? "default" : "outline"}
                    className="flex flex-col items-center justify-center h-20"
                    onClick={() => updateGasPreset("fast")}
                  >
                    <Flame className="h-6 w-6 mb-1" />
                    <span>Fast</span>
                    <span className="text-xs mt-1">
                      ~{currentGasPrices?.fast?.estimatedSeconds || "5-15"}s
                    </span>
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Auto-adjust gas settings</Label>
                  <Switch
                    checked={autoAdjust}
                    onCheckedChange={setAutoAdjust}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Automatically adjust gas settings based on network conditions
                </p>
              </div>
              
              <div className="bg-muted p-4 rounded-md">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Estimated Cost</span>
                  <span className="text-sm font-medium">
                    {calculateEstimatedCost()} {getNetworkToken()}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  This is an estimate based on current gas prices and may vary when the transaction is executed.
                </p>
              </div>
              
              {currentGasPrices && (
                <div className="space-y-2">
                  <Label>Current Network Gas Prices</Label>
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="border rounded-md p-2">
                      <p className="text-xs text-muted-foreground">Slow</p>
                      <p className="text-sm font-medium">{currentGasPrices.slow.maxFeePerGas} Gwei</p>
                    </div>
                    <div className="border rounded-md p-2">
                      <p className="text-xs text-muted-foreground">Standard</p>
                      <p className="text-sm font-medium">{currentGasPrices.standard.maxFeePerGas} Gwei</p>
                    </div>
                    <div className="border rounded-md p-2">
                      <p className="text-xs text-muted-foreground">Fast</p>
                      <p className="text-sm font-medium">{currentGasPrices.fast.maxFeePerGas} Gwei</p>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground text-right">
                    Last updated: {currentGasPrices.lastUpdated}
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="advanced">
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="flex items-center">
                    Use EIP-1559
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>EIP-1559 is a more efficient gas fee mechanism that helps reduce gas costs.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Switch
                    checked={useEIP1559}
                    onCheckedChange={setUseEIP1559}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Use the EIP-1559 gas fee mechanism for more predictable transaction fees
                </p>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="flex items-center">
                    Max Fee (Gwei)
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>The maximum amount you're willing to pay per unit of gas.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Slider
                      value={[parseFloat(maxFeePerGas)]}
                      min={1}
                      max={500}
                      step={1}
                      onValueChange={(value) => setMaxFeePerGas(value[0].toString())}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={maxFeePerGas}
                      onChange={(e) => setMaxFeePerGas(e.target.value)}
                      className="w-20"
                    />
                  </div>
                </div>
                
                {useEIP1559 && (
                  <div className="space-y-2">
                    <Label className="flex items-center">
                      Priority Fee (Gwei)
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>The fee paid directly to miners as an incentive to include your transaction.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <div className="flex items-center space-x-2">
                      <Slider
                        value={[parseFloat(maxPriorityFeePerGas)]}
                        min={0.1}
                        max={10}
                        step={0.1}
                        onValueChange={(value) => setMaxPriorityFeePerGas(value[0].toString())}
                        className="flex-1"
                      />
                      <Input
                        type="number"
                        value={maxPriorityFeePerGas}
                        onChange={(e) => setMaxPriorityFeePerGas(e.target.value)}
                        className="w-20"
                      />
                    </div>
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label className="flex items-center">
                    Gas Limit
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 ml-1 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>The maximum amount of gas units you're willing to use for the transaction.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <Input
                    type="number"
                    value={gasLimit}
                    onChange={(e) => setGasLimit(e.target.value)}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Simple Transfer: 21,000</span>
                    <span>ERC-20 Transfer: ~65,000</span>
                    <span>Complex Contract: 100,000+</span>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="bg-muted p-4 rounded-md">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Estimated Cost</span>
                  <span className="text-sm font-medium">
                    {calculateEstimatedCost()} {getNetworkToken()}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  This is an estimate based on your custom gas settings and may vary when the transaction is executed.
                </p>
              </div>
              
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetGasSettings}
                  className="flex items-center"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset to Default
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => {
            // Reset to saved settings or defaults
            if (savedSettings) {
              setUseEIP1559(savedSettings.useEIP1559);
              setAutoAdjust(savedSettings.autoAdjust);
              setGasPreset(savedSettings.preset || "standard");
              setMaxFeePerGas(savedSettings.maxFeePerGas || "30");
              setMaxPriorityFeePerGas(savedSettings.maxPriorityFeePerGas || "1.5");
              setGasLimit(savedSettings.gasLimit || "21000");
            } else {
              resetGasSettings();
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={saveGasSettings}
          disabled={isLoading}
          className="flex items-center"
        >
          <Save className="mr-2 h-4 w-4" />
          {isLoading ? "Saving..." : "Save Settings"}
        </Button>
      </CardFooter>
    </Card>
  );
}
