"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { GasStrategy } from "@/lib/gas-optimizer";
import { truncateAddress } from "@/lib/utils";

interface Wallet {
  id: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  isSmartWallet: boolean;
  balance: number;
  createdAt: string;
}

interface GasPrices {
  eip1559: boolean;
  slow: any;
  average: any;
  fast: any;
  baseFeePerGas?: string;
}

interface GasPriceHistory {
  blockNumber: number;
  timestamp: string;
  baseFeePerGas: string;
}

export default function MultiChainWalletComponent() {
  const { data: session } = useSession();
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeNetwork, setActiveNetwork] = useState<SupportedNetwork>(SupportedNetwork.ETHEREUM);
  
  // Create wallet state
  const [creatingWallet, setCreatingWallet] = useState(false);
  const [isTestnet, setIsTestnet] = useState(true);
  const [isSmartWallet, setIsSmartWallet] = useState(false);
  
  // Send transaction state
  const [sendingTransaction, setSendingTransaction] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);
  const [recipient, setRecipient] = useState("");
  const [amount, setAmount] = useState("");
  const [gasStrategy, setGasStrategy] = useState<GasStrategy>(GasStrategy.AVERAGE);
  const [customGasParams, setCustomGasParams] = useState({
    maxFeePerGas: "",
    maxPriorityFeePerGas: "",
    gasPrice: "",
  });
  const [gasPrices, setGasPrices] = useState<GasPrices | null>(null);
  const [gasPriceHistory, setGasPriceHistory] = useState<GasPriceHistory[]>([]);
  const [transactionError, setTransactionError] = useState<string | null>(null);
  const [transactionSuccess, setTransactionSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user) {
      fetchWallets();
    }
  }, [session]);

  useEffect(() => {
    if (selectedWallet) {
      fetchGasPrices(selectedWallet.network);
    }
  }, [selectedWallet]);

  const fetchWallets = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/wallet/list");
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch wallets");
      }
      
      const data = await response.json();
      setWallets(data.wallets);
      
      // Set the first wallet as selected if available
      if (data.wallets.length > 0) {
        setSelectedWallet(data.wallets[0]);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error fetching wallets:", error);
    } finally {
      setLoading(false);
    }
  };

  const createWallet = async () => {
    setCreatingWallet(true);
    setError(null);

    try {
      const response = await fetch("/api/wallet/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          network: activeNetwork,
          isTestnet,
          isSmartWallet,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create wallet");
      }
      
      const data = await response.json();
      
      // Refresh wallets
      fetchWallets();
      
      // Show success message
      setTransactionSuccess(`${activeNetwork} wallet created successfully`);
      
      // Reset form
      setActiveNetwork(SupportedNetwork.ETHEREUM);
      setIsTestnet(true);
      setIsSmartWallet(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error creating wallet:", error);
    } finally {
      setCreatingWallet(false);
    }
  };

  const fetchGasPrices = async (network: string) => {
    try {
      const response = await fetch(`/api/wallet/send?network=${network}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch gas prices");
      }
      
      const data = await response.json();
      setGasPrices(data.gasPrices);
      setGasPriceHistory(data.gasPriceHistory || []);
    } catch (error) {
      console.error("Error fetching gas prices:", error);
    }
  };

  const sendTransaction = async () => {
    if (!selectedWallet) return;
    
    setSendingTransaction(true);
    setTransactionError(null);
    setTransactionSuccess(null);
    
    try {
      // Prepare gas parameters based on strategy
      const gasParams: any = {
        gasStrategy,
      };
      
      if (gasStrategy === GasStrategy.CUSTOM) {
        if (gasPrices?.eip1559) {
          gasParams.maxFeePerGas = customGasParams.maxFeePerGas;
          gasParams.maxPriorityFeePerGas = customGasParams.maxPriorityFeePerGas;
        } else {
          gasParams.gasPrice = customGasParams.gasPrice;
        }
      }
      
      const response = await fetch("/api/wallet/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          walletId: selectedWallet.id,
          to: recipient,
          amount,
          ...gasParams,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send transaction");
      }
      
      const data = await response.json();
      
      setTransactionSuccess(`Transaction sent successfully: ${data.transaction.hash}`);
      
      // Refresh wallets to update balances
      fetchWallets();
      
      // Reset form
      setRecipient("");
      setAmount("");
    } catch (error) {
      setTransactionError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error sending transaction:", error);
    } finally {
      setSendingTransaction(false);
    }
  };

  const getNetworkColor = (network: string) => {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return "bg-blue-100 text-blue-800";
      case SupportedNetwork.POLYGON:
        return "bg-purple-100 text-purple-800";
      case SupportedNetwork.ARBITRUM:
        return "bg-indigo-100 text-indigo-800";
      case SupportedNetwork.OPTIMISM:
        return "bg-red-100 text-red-800";
      case SupportedNetwork.BASE:
        return "bg-teal-100 text-teal-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getNetworkIcon = (network: string) => {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return "Ξ";
      case SupportedNetwork.POLYGON:
        return "⬡";
      case SupportedNetwork.ARBITRUM:
        return "⬢";
      case SupportedNetwork.OPTIMISM:
        return "○";
      case SupportedNetwork.BASE:
        return "◆";
      default:
        return "●";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Multi-Chain Wallet</CardTitle>
          <CardDescription>Manage your wallets across multiple blockchains</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Multi-Chain Wallet</CardTitle>
        <CardDescription>Manage your wallets across multiple blockchains</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="wallets">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="wallets" className="flex-1">My Wallets</TabsTrigger>
            <TabsTrigger value="create" className="flex-1">Create Wallet</TabsTrigger>
            <TabsTrigger value="send" className="flex-1">Send</TabsTrigger>
          </TabsList>
          
          <TabsContent value="wallets">
            <div className="space-y-4">
              {wallets.length > 0 ? (
                wallets.map((wallet) => (
                  <div
                    key={wallet.id}
                    className={`p-4 border rounded-lg ${
                      selectedWallet?.id === wallet.id ? "border-primary" : ""
                    } cursor-pointer`}
                    onClick={() => setSelectedWallet(wallet)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getNetworkColor(wallet.network)}`}>
                          {getNetworkIcon(wallet.network)}
                        </div>
                        <div>
                          <p className="font-medium">{wallet.network.charAt(0).toUpperCase() + wallet.network.slice(1)}</p>
                          <p className="text-xs text-muted-foreground">
                            {wallet.isTestnet ? "Testnet" : "Mainnet"}
                            {wallet.isSmartWallet ? " • Smart Wallet" : ""}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{wallet.balance.toFixed(6)}</p>
                        <p className="text-xs text-muted-foreground">{truncateAddress(wallet.address)}</p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">You don't have any wallets yet.</p>
                  <Button onClick={() => document.querySelector('[value="create"]')?.click()}>
                    Create Wallet
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="create">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label>Blockchain Network</Label>
                <Select
                  value={activeNetwork}
                  onValueChange={(value) => setActiveNetwork(value as SupportedNetwork)}
                  disabled={creatingWallet}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select network" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={SupportedNetwork.ETHEREUM}>Ethereum</SelectItem>
                    <SelectItem value={SupportedNetwork.POLYGON}>Polygon</SelectItem>
                    <SelectItem value={SupportedNetwork.ARBITRUM}>Arbitrum</SelectItem>
                    <SelectItem value={SupportedNetwork.OPTIMISM}>Optimism</SelectItem>
                    <SelectItem value={SupportedNetwork.BASE}>Base</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Network Type</Label>
                <RadioGroup
                  value={isTestnet ? "testnet" : "mainnet"}
                  onValueChange={(value) => setIsTestnet(value === "testnet")}
                  disabled={creatingWallet}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="testnet" id="testnet" />
                    <Label htmlFor="testnet">Testnet (Recommended for testing)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="mainnet" id="mainnet" />
                    <Label htmlFor="mainnet">Mainnet (Real funds)</Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="space-y-2">
                <Label>Wallet Type</Label>
                <RadioGroup
                  value={isSmartWallet ? "smart" : "eoa"}
                  onValueChange={(value) => setIsSmartWallet(value === "smart")}
                  disabled={creatingWallet}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="eoa" id="eoa" />
                    <Label htmlFor="eoa">Regular Wallet (EOA)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="smart" id="smart" />
                    <Label htmlFor="smart">Smart Wallet (Advanced features)</Label>
                  </div>
                </RadioGroup>
              </div>
              
              {error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}
              
              {transactionSuccess && (
                <div className="rounded-md bg-green-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-green-700">{transactionSuccess}</p>
                    </div>
                  </div>
                </div>
              )}
              
              <Button
                onClick={createWallet}
                disabled={creatingWallet}
                className="w-full"
              >
                {creatingWallet ? "Creating Wallet..." : "Create Wallet"}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="send">
            {selectedWallet ? (
              <div className="space-y-6">
                <div className="p-4 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getNetworkColor(selectedWallet.network)}`}>
                        {getNetworkIcon(selectedWallet.network)}
                      </div>
                      <div>
                        <p className="font-medium">{selectedWallet.network.charAt(0).toUpperCase() + selectedWallet.network.slice(1)}</p>
                        <p className="text-xs text-muted-foreground">
                          {selectedWallet.isTestnet ? "Testnet" : "Mainnet"}
                          {selectedWallet.isSmartWallet ? " • Smart Wallet" : ""}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{selectedWallet.balance.toFixed(6)}</p>
                      <p className="text-xs text-muted-foreground">{truncateAddress(selectedWallet.address)}</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="recipient">Recipient Address</Label>
                    <Input
                      id="recipient"
                      placeholder="0x..."
                      value={recipient}
                      onChange={(e) => setRecipient(e.target.value)}
                      disabled={sendingTransaction}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.000001"
                      min="0"
                      placeholder="0.0"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      disabled={sendingTransaction}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Gas Strategy</Label>
                    <RadioGroup
                      value={gasStrategy}
                      onValueChange={(value) => setGasStrategy(value as GasStrategy)}
                      disabled={sendingTransaction || !gasPrices}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value={GasStrategy.SLOW} id="slow" />
                          <Label htmlFor="slow">Slow</Label>
                        </div>
                        {gasPrices && (
                          <div className="text-sm text-muted-foreground">
                            {gasPrices.eip1559 ? (
                              <span>{gasPrices.slow.maxFeePerGas} Gwei</span>
                            ) : (
                              <span>{gasPrices.slow.gasPrice} Gwei</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value={GasStrategy.AVERAGE} id="average" />
                          <Label htmlFor="average">Average</Label>
                        </div>
                        {gasPrices && (
                          <div className="text-sm text-muted-foreground">
                            {gasPrices.eip1559 ? (
                              <span>{gasPrices.average.maxFeePerGas} Gwei</span>
                            ) : (
                              <span>{gasPrices.average.gasPrice} Gwei</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value={GasStrategy.FAST} id="fast" />
                          <Label htmlFor="fast">Fast</Label>
                        </div>
                        {gasPrices && (
                          <div className="text-sm text-muted-foreground">
                            {gasPrices.eip1559 ? (
                              <span>{gasPrices.fast.maxFeePerGas} Gwei</span>
                            ) : (
                              <span>{gasPrices.fast.gasPrice} Gwei</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value={GasStrategy.CUSTOM} id="custom" />
                        <Label htmlFor="custom">Custom</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  
                  {gasStrategy === GasStrategy.CUSTOM && gasPrices && (
                    <div className="space-y-4 p-4 border rounded-lg">
                      {gasPrices.eip1559 ? (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="maxFeePerGas">Max Fee (Gwei)</Label>
                            <Input
                              id="maxFeePerGas"
                              type="number"
                              step="0.1"
                              min="0"
                              placeholder="Max Fee Per Gas"
                              value={customGasParams.maxFeePerGas}
                              onChange={(e) => setCustomGasParams({
                                ...customGasParams,
                                maxFeePerGas: e.target.value,
                              })}
                              disabled={sendingTransaction}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="maxPriorityFeePerGas">Priority Fee (Gwei)</Label>
                            <Input
                              id="maxPriorityFeePerGas"
                              type="number"
                              step="0.1"
                              min="0"
                              placeholder="Max Priority Fee Per Gas"
                              value={customGasParams.maxPriorityFeePerGas}
                              onChange={(e) => setCustomGasParams({
                                ...customGasParams,
                                maxPriorityFeePerGas: e.target.value,
                              })}
                              disabled={sendingTransaction}
                            />
                          </div>
                        </>
                      ) : (
                        <div className="space-y-2">
                          <Label htmlFor="gasPrice">Gas Price (Gwei)</Label>
                          <Input
                            id="gasPrice"
                            type="number"
                            step="0.1"
                            min="0"
                            placeholder="Gas Price"
                            value={customGasParams.gasPrice}
                            onChange={(e) => setCustomGasParams({
                              ...customGasParams,
                              gasPrice: e.target.value,
                            })}
                            disabled={sendingTransaction}
                          />
                        </div>
                      )}
                    </div>
                  )}
                  
                  {gasPriceHistory.length > 0 && (
                    <div className="space-y-2">
                      <Label>Recent Gas Price Trend</Label>
                      <div className="h-20 flex items-end space-x-1">
                        {gasPriceHistory.map((item, index) => {
                          const height = Math.min(Math.max(parseFloat(item.baseFeePerGas) * 2, 10), 100);
                          return (
                            <div
                              key={index}
                              className="bg-blue-500 w-full rounded-t"
                              style={{ height: `${height}%` }}
                              title={`Block ${item.blockNumber}: ${item.baseFeePerGas} Gwei`}
                            />
                          );
                        })}
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Newest</span>
                        <span>Oldest</span>
                      </div>
                    </div>
                  )}
                  
                  {transactionError && (
                    <div className="rounded-md bg-red-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-red-700">{transactionError}</p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {transactionSuccess && (
                    <div className="rounded-md bg-green-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-green-700">{transactionSuccess}</p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <Button
                    onClick={sendTransaction}
                    disabled={
                      sendingTransaction ||
                      !recipient ||
                      !amount ||
                      parseFloat(amount) <= 0 ||
                      parseFloat(amount) > selectedWallet.balance ||
                      (gasStrategy === GasStrategy.CUSTOM && (
                        (gasPrices?.eip1559 && (!customGasParams.maxFeePerGas || !customGasParams.maxPriorityFeePerGas)) ||
                        (!gasPrices?.eip1559 && !customGasParams.gasPrice)
                      ))
                    }
                    className="w-full"
                  >
                    {sendingTransaction ? "Sending..." : "Send Transaction"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">Please select or create a wallet first.</p>
                <Button onClick={() => document.querySelector('[value="wallets"]')?.click()}>
                  Select Wallet
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
