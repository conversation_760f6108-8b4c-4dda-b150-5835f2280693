"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Shield,
  Users,
  UserPlus,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  Key,
  Lock,
  UserX
} from "lucide-react";
import { truncateAddress } from "@/lib/utils";
import { SmartWalletRecoverySkeleton, GuardianDetailsSkeleton, RecoveryRequestsSkeleton } from "./smart-wallet-recovery-skeleton";

// Schema for adding a guardian
const addGuardianSchema = z.object({
  guardianAddress: z.string()
    .min(42, "Ethereum address must be 42 characters long")
    .max(42, "Ethereum address must be 42 characters long")
    .regex(/^0x[a-fA-F0-9]{40}$/, "Invalid Ethereum address format"),
  guardianName: z.string().min(1, "Guardian name is required"),
  guardianEmail: z.string().email("Invalid email address").optional(),
});

// Schema for initiating recovery
const initiateRecoverySchema = z.object({
  recoveryInitiator: z.string()
    .min(42, "Ethereum address must be 42 characters long")
    .max(42, "Ethereum address must be 42 characters long")
    .regex(/^0x[a-fA-F0-9]{40}$/, "Invalid Ethereum address format"),
  description: z.string().min(1, "Description is required"),
});

// Schema for executing recovery
const executeRecoverySchema = z.object({
  newOwnerAddress: z.string()
    .min(42, "Ethereum address must be 42 characters long")
    .max(42, "Ethereum address must be 42 characters long")
    .regex(/^0x[a-fA-F0-9]{40}$/, "Invalid Ethereum address format"),
});

interface Guardian {
  id: string;
  address: string;
  name: string;
  email?: string;
  status: string;
  addedAt: string;
}

interface RecoveryRequest {
  id: string;
  status: string;
  initiator: string;
  timelock: string;
  createdAt: string;
  executedAt?: string;
  approvals: {
    id: string;
    guardian: {
      id: string;
      address: string;
      name: string;
    };
    approvedAt: string;
  }[];
}

interface SmartWalletRecoveryProps {
  walletId: string;
  walletAddress: string;
  isSmartWallet: boolean;
}

export default function SmartWalletRecovery({
  walletId,
  walletAddress,
  isSmartWallet
}: SmartWalletRecoveryProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("guardians");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [recoveryRequests, setRecoveryRequests] = useState<RecoveryRequest[]>([]);
  const [isAddingGuardian, setIsAddingGuardian] = useState(false);
  const [isInitiatingRecovery, setIsInitiatingRecovery] = useState(false);
  const [isExecutingRecovery, setIsExecutingRecovery] = useState(false);
  const [selectedRecovery, setSelectedRecovery] = useState<RecoveryRequest | null>(null);

  // Add guardian form
  const addGuardianForm = useForm<z.infer<typeof addGuardianSchema>>({
    resolver: zodResolver(addGuardianSchema),
    defaultValues: {
      guardianAddress: "",
      guardianName: "",
      guardianEmail: "",
    },
  });

  // Initiate recovery form
  const initiateRecoveryForm = useForm<z.infer<typeof initiateRecoverySchema>>({
    resolver: zodResolver(initiateRecoverySchema),
    defaultValues: {
      recoveryInitiator: "",
      description: "",
    },
  });

  // Execute recovery form
  const executeRecoveryForm = useForm<z.infer<typeof executeRecoverySchema>>({
    resolver: zodResolver(executeRecoverySchema),
    defaultValues: {
      newOwnerAddress: "",
    },
  });

  // Fetch guardians and recovery requests
  useEffect(() => {
    if (!isSmartWallet) return;

    async function fetchData() {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch guardians
        const guardiansResponse = await fetch(`/api/wallet/smart-wallet/${walletId}/guardians`);

        if (!guardiansResponse.ok) {
          const errorData = await guardiansResponse.json();
          throw new Error(errorData.error || "Failed to fetch guardians");
        }

        const guardiansData = await guardiansResponse.json();
        setGuardians(guardiansData.guardians || []);

        // Fetch recovery requests
        const recoveryResponse = await fetch(`/api/wallet/smart-wallet/${walletId}/recovery`);

        if (!recoveryResponse.ok) {
          const errorData = await recoveryResponse.json();
          throw new Error(errorData.error || "Failed to fetch recovery requests");
        }

        const recoveryData = await recoveryResponse.json();
        setRecoveryRequests(recoveryData.recoveryRequests || []);
      } catch (error) {
        console.error("Error fetching wallet recovery data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [walletId, isSmartWallet]);

  // Handle adding a guardian
  const onAddGuardian = async (data: z.infer<typeof addGuardianSchema>) => {
    setIsAddingGuardian(true);

    try {
      const response = await fetch(`/api/wallet/smart-wallet/${walletId}/guardians`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          address: data.guardianAddress,
          name: data.guardianName,
          email: data.guardianEmail,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add guardian");
      }

      const result = await response.json();

      // Add the new guardian to the list
      setGuardians([...guardians, result.guardian]);

      toast({
        title: "Guardian Added",
        description: "The guardian has been added successfully.",
      });

      // Reset form
      addGuardianForm.reset();
    } catch (error) {
      console.error("Error adding guardian:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add guardian",
        variant: "destructive",
      });
    } finally {
      setIsAddingGuardian(false);
    }
  };

  // Handle initiating recovery
  const onInitiateRecovery = async (data: z.infer<typeof initiateRecoverySchema>) => {
    setIsInitiatingRecovery(true);

    try {
      const response = await fetch(`/api/wallet/smart-wallet/recovery`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          walletId,
          recoveryInitiator: data.recoveryInitiator,
          description: data.description,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to initiate recovery");
      }

      const result = await response.json();

      // Add the new recovery request to the list
      setRecoveryRequests([result.recovery, ...recoveryRequests]);

      toast({
        title: "Recovery Initiated",
        description: "The recovery process has been initiated successfully.",
      });

      // Reset form and close dialog
      initiateRecoveryForm.reset();
      setActiveTab("requests");
    } catch (error) {
      console.error("Error initiating recovery:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to initiate recovery",
        variant: "destructive",
      });
    } finally {
      setIsInitiatingRecovery(false);
    }
  };

  // Handle executing recovery
  const onExecuteRecovery = async (data: z.infer<typeof executeRecoverySchema>) => {
    if (!selectedRecovery) return;

    setIsExecutingRecovery(true);

    try {
      const response = await fetch(`/api/wallet/smart-wallet/recovery`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "execute",
          recoveryId: selectedRecovery.id,
          newOwnerAddress: data.newOwnerAddress,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to execute recovery");
      }

      const result = await response.json();

      // Update the recovery request in the list
      setRecoveryRequests(
        recoveryRequests.map(req =>
          req.id === selectedRecovery.id
            ? { ...req, status: "EXECUTED", executedAt: new Date().toISOString() }
            : req
        )
      );

      toast({
        title: "Recovery Executed",
        description: "The wallet recovery has been executed successfully.",
      });

      // Reset form and close dialog
      executeRecoveryForm.reset();
      setSelectedRecovery(null);

      // Refresh the page to show updated wallet
      router.refresh();
    } catch (error) {
      console.error("Error executing recovery:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to execute recovery",
        variant: "destructive",
      });
    } finally {
      setIsExecutingRecovery(false);
    }
  };

  // If not a smart wallet, show a message
  if (!isSmartWallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Smart Wallet Recovery</CardTitle>
          <CardDescription>
            Social recovery is only available for smart wallets.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Not a Smart Wallet</AlertTitle>
            <AlertDescription>
              This wallet is not a smart wallet. Create a smart wallet to enable social recovery features.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Button onClick={() => router.push("/dashboard/wallet?tab=smart")}>
            Create Smart Wallet
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Smart Wallet Recovery</CardTitle>
        <CardDescription>
          Manage social recovery for your smart wallet
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="animate-in fade-in duration-300">
            {activeTab === "guardians" ? (
              <GuardianDetailsSkeleton />
            ) : (
              <RecoveryRequestsSkeleton />
            )}
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full mb-4">
              <TabsTrigger value="guardians" className="flex-1">
                <Users className="mr-2 h-4 w-4" />
                Guardians
              </TabsTrigger>
              <TabsTrigger value="requests" className="flex-1">
                <Shield className="mr-2 h-4 w-4" />
                Recovery Requests
              </TabsTrigger>
            </TabsList>

            <TabsContent value="guardians">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Wallet Guardians</h3>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Add Guardian
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add Guardian</DialogTitle>
                        <DialogDescription>
                          Add a trusted guardian who can help recover your wallet if you lose access.
                        </DialogDescription>
                      </DialogHeader>

                      <Form {...addGuardianForm}>
                        <form onSubmit={addGuardianForm.handleSubmit(onAddGuardian)} className="space-y-4">
                          <FormField
                            control={addGuardianForm.control}
                            name="guardianAddress"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Guardian Address</FormLabel>
                                <FormControl>
                                  <Input placeholder="0x..." {...field} />
                                </FormControl>
                                <FormDescription>
                                  The Ethereum address of the guardian
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={addGuardianForm.control}
                            name="guardianName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Guardian Name</FormLabel>
                                <FormControl>
                                  <Input placeholder="John Doe" {...field} />
                                </FormControl>
                                <FormDescription>
                                  A name to identify this guardian
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={addGuardianForm.control}
                            name="guardianEmail"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Guardian Email (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="<EMAIL>" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Email for notifications (optional)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <DialogFooter>
                            <Button type="submit" disabled={isAddingGuardian}>
                              {isAddingGuardian ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Adding...
                                </>
                              ) : (
                                <>
                                  <UserPlus className="mr-2 h-4 w-4" />
                                  Add Guardian
                                </>
                              )}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>

                {guardians.length === 0 ? (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>No Guardians</AlertTitle>
                    <AlertDescription>
                      You haven't added any guardians yet. Add guardians to enable social recovery for your wallet.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Address</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Added</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {guardians.map((guardian) => (
                        <TableRow key={guardian.id}>
                          <TableCell className="font-medium">{guardian.name}</TableCell>
                          <TableCell>{truncateAddress(guardian.address)}</TableCell>
                          <TableCell>
                            <Badge variant={guardian.status === "ACTIVE" ? "success" : "secondary"}>
                              {guardian.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{new Date(guardian.addedAt).toLocaleDateString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}

                <Alert className="mt-4">
                  <Shield className="h-4 w-4" />
                  <AlertTitle>Social Recovery</AlertTitle>
                  <AlertDescription>
                    Guardians can help you recover your wallet if you lose access. We recommend adding at least 3 guardians.
                  </AlertDescription>
                </Alert>
              </div>
            </TabsContent>

            <TabsContent value="requests">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Recovery Requests</h3>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <Shield className="mr-2 h-4 w-4" />
                        Initiate Recovery
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Initiate Wallet Recovery</DialogTitle>
                        <DialogDescription>
                          Start the recovery process for your wallet. This will require approval from your guardians.
                        </DialogDescription>
                      </DialogHeader>

                      <Form {...initiateRecoveryForm}>
                        <form onSubmit={initiateRecoveryForm.handleSubmit(onInitiateRecovery)} className="space-y-4">
                          <FormField
                            control={initiateRecoveryForm.control}
                            name="recoveryInitiator"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Recovery Initiator</FormLabel>
                                <FormControl>
                                  <Input placeholder="0x..." {...field} />
                                </FormControl>
                                <FormDescription>
                                  The address of the guardian initiating the recovery
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={initiateRecoveryForm.control}
                            name="description"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Recovery Reason</FormLabel>
                                <FormControl>
                                  <Input placeholder="Lost access to private key" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Reason for initiating the recovery
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <DialogFooter>
                            <Button type="submit" disabled={isInitiatingRecovery}>
                              {isInitiatingRecovery ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Initiating...
                                </>
                              ) : (
                                <>
                                  <Shield className="mr-2 h-4 w-4" />
                                  Initiate Recovery
                                </>
                              )}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>

                {recoveryRequests.length === 0 ? (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>No Recovery Requests</AlertTitle>
                    <AlertDescription>
                      There are no active recovery requests for this wallet.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Initiator</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Approvals</TableHead>
                        <TableHead>Timelock</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recoveryRequests.map((request) => (
                        <TableRow key={request.id}>
                          <TableCell>{truncateAddress(request.initiator)}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                request.status === "APPROVED" ? "success" :
                                request.status === "EXECUTED" ? "default" :
                                request.status === "PENDING" ? "warning" : "secondary"
                              }
                            >
                              {request.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{request.approvals.length} / {guardians.length}</TableCell>
                          <TableCell>{new Date(request.timelock).toLocaleDateString()}</TableCell>
                          <TableCell>{new Date(request.createdAt).toLocaleDateString()}</TableCell>
                          <TableCell>
                            {request.status === "APPROVED" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedRecovery(request)}
                              >
                                <Key className="mr-2 h-4 w-4" />
                                Execute
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>

      {/* Execute Recovery Dialog */}
      {selectedRecovery && (
        <Dialog open={!!selectedRecovery} onOpenChange={(open) => !open && setSelectedRecovery(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Execute Wallet Recovery</DialogTitle>
              <DialogDescription>
                Complete the recovery process by setting a new owner for the wallet.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label>Recovery Status</Label>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      selectedRecovery.status === "APPROVED" ? "success" :
                      selectedRecovery.status === "EXECUTED" ? "default" :
                      selectedRecovery.status === "PENDING" ? "warning" : "secondary"
                    }
                  >
                    {selectedRecovery.status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {selectedRecovery.approvals.length} / {guardians.length} approvals
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Timelock Expires</Label>
                <div className="text-sm">
                  {new Date(selectedRecovery.timelock).toLocaleString()}
                </div>
              </div>

              <Separator />

              <Form {...executeRecoveryForm}>
                <form onSubmit={executeRecoveryForm.handleSubmit(onExecuteRecovery)} className="space-y-4">
                  <FormField
                    control={executeRecoveryForm.control}
                    name="newOwnerAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Owner Address</FormLabel>
                        <FormControl>
                          <Input placeholder="0x..." {...field} />
                        </FormControl>
                        <FormDescription>
                          The new owner address for the wallet
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Alert variant="warning">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Warning</AlertTitle>
                    <AlertDescription>
                      This action will transfer ownership of your wallet to a new address. Make sure you have access to the new address.
                    </AlertDescription>
                  </Alert>

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setSelectedRecovery(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isExecutingRecovery || new Date(selectedRecovery.timelock) > new Date()}
                    >
                      {isExecutingRecovery ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Executing...
                        </>
                      ) : new Date(selectedRecovery.timelock) > new Date() ? (
                        <>
                          <Clock className="mr-2 h-4 w-4" />
                          Timelock Active
                        </>
                      ) : (
                        <>
                          <Key className="mr-2 h-4 w-4" />
                          Execute Recovery
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}
