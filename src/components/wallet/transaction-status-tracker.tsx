'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { 
  Loader2, 
  Check, 
  AlertCircle, 
  Clock, 
  ExternalLink,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Info
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible';

// Transaction status
export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
}

// Transaction type
export enum TransactionType {
  SEND = 'send',
  RECEIVE = 'receive',
  SWAP = 'swap',
  STAKE = 'stake',
  UNSTAKE = 'unstake',
  CLAIM = 'claim',
}

// Transaction details interface
export interface TransactionDetails {
  id: string;
  hash: string;
  status: TransactionStatus;
  type: TransactionType;
  amount: string;
  token: string;
  recipient?: string;
  sender?: string;
  timestamp: Date;
  confirmations?: number;
  requiredConfirmations?: number;
  network: string;
  fee?: string;
  estimatedCompletionTime?: Date;
}

interface TransactionStatusTrackerProps {
  transaction: TransactionDetails;
  onRefresh?: () => void;
  onClose?: () => void;
}

export function TransactionStatusTracker({
  transaction,
  onRefresh,
  onClose,
}: TransactionStatusTrackerProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [progress, setProgress] = useState(0);
  
  // Calculate progress based on confirmations
  useEffect(() => {
    if (transaction.status === TransactionStatus.CONFIRMED) {
      setProgress(100);
    } else if (transaction.status === TransactionStatus.FAILED) {
      setProgress(0);
    } else if (transaction.confirmations && transaction.requiredConfirmations) {
      const calculatedProgress = Math.min(
        Math.round((transaction.confirmations / transaction.requiredConfirmations) * 100),
        99
      );
      setProgress(calculatedProgress);
    } else {
      // If no confirmation data, use a simulated progress for pending transactions
      if (transaction.status === TransactionStatus.PENDING) {
        setProgress(10);
      } else if (transaction.status === TransactionStatus.PROCESSING) {
        setProgress(50);
      }
    }
  }, [transaction]);
  
  // Format time remaining
  const formatTimeRemaining = () => {
    if (!transaction.estimatedCompletionTime) return 'Unknown';
    
    const now = new Date();
    const estimatedTime = new Date(transaction.estimatedCompletionTime);
    const diffMs = estimatedTime.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Any moment now';
    
    const diffMins = Math.round(diffMs / 60000);
    if (diffMins < 1) return 'Less than a minute';
    if (diffMins === 1) return '1 minute';
    if (diffMins < 60) return `${diffMins} minutes`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours === 1) return '1 hour';
    return `${diffHours} hours`;
  };
  
  // Get block explorer URL
  const getExplorerUrl = () => {
    // In a real implementation, this would return the actual block explorer URL
    return `https://explorer.${transaction.network.toLowerCase()}.network/tx/${transaction.hash}`;
  };
  
  // Get status badge
  const getStatusBadge = () => {
    switch (transaction.status) {
      case TransactionStatus.PENDING:
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case TransactionStatus.PROCESSING:
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
            Processing
          </Badge>
        );
      case TransactionStatus.CONFIRMED:
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <Check className="mr-1 h-3 w-3" />
            Confirmed
          </Badge>
        );
      case TransactionStatus.FAILED:
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Transaction Status</CardTitle>
          {getStatusBadge()}
        </div>
        <CardDescription>
          {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} {transaction.amount} {transaction.token}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Transaction Progress</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
        
        <div className="rounded-lg border p-3 space-y-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium">Status</span>
            <span className="text-sm capitalize">{transaction.status}</span>
          </div>
          
          {transaction.confirmations !== undefined && transaction.requiredConfirmations !== undefined && (
            <div className="flex justify-between">
              <span className="text-sm font-medium">Confirmations</span>
              <span className="text-sm">{transaction.confirmations} of {transaction.requiredConfirmations}</span>
            </div>
          )}
          
          {transaction.estimatedCompletionTime && transaction.status !== TransactionStatus.CONFIRMED && transaction.status !== TransactionStatus.FAILED && (
            <div className="flex justify-between">
              <span className="text-sm font-medium">Estimated Time</span>
              <span className="text-sm">{formatTimeRemaining()}</span>
            </div>
          )}
          
          <div className="flex justify-between">
            <span className="text-sm font-medium">Transaction ID</span>
            <span className="text-sm font-mono">
              {transaction.hash.substring(0, 6)}...{transaction.hash.substring(transaction.hash.length - 4)}
            </span>
          </div>
        </div>
        
        <Collapsible open={showDetails} onOpenChange={setShowDetails}>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Transaction Details</span>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm">
                {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
          </div>
          
          <CollapsibleContent className="space-y-4 mt-2">
            <div className="rounded-lg border p-3 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Network</span>
                <span className="text-sm">{transaction.network}</span>
              </div>
              
              {transaction.sender && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium">From</span>
                  <span className="text-sm font-mono">
                    {transaction.sender.substring(0, 6)}...{transaction.sender.substring(transaction.sender.length - 4)}
                  </span>
                </div>
              )}
              
              {transaction.recipient && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium">To</span>
                  <span className="text-sm font-mono">
                    {transaction.recipient.substring(0, 6)}...{transaction.recipient.substring(transaction.recipient.length - 4)}
                  </span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-sm font-medium">Amount</span>
                <span className="text-sm">{transaction.amount} {transaction.token}</span>
              </div>
              
              {transaction.fee && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Fee</span>
                  <span className="text-sm">{transaction.fee}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-sm font-medium">Date</span>
                <span className="text-sm">{transaction.timestamp.toLocaleString()}</span>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
        
        {transaction.status === TransactionStatus.FAILED && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Transaction Failed</AlertTitle>
            <AlertDescription>
              Your transaction could not be processed. This could be due to network congestion, insufficient funds, or other issues.
            </AlertDescription>
          </Alert>
        )}
        
        {transaction.status === TransactionStatus.PENDING && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Transaction Pending</AlertTitle>
            <AlertDescription>
              Your transaction has been submitted to the network and is waiting to be processed. This may take a few minutes.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => window.open(getExplorerUrl(), '_blank')}
        >
          View on Explorer
          <ExternalLink className="ml-2 h-4 w-4" />
        </Button>
        
        <div className="flex space-x-2">
          {onRefresh && transaction.status !== TransactionStatus.CONFIRMED && (
            <Button
              variant="outline"
              onClick={onRefresh}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          )}
          
          {onClose && (
            <Button
              onClick={onClose}
            >
              Close
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
