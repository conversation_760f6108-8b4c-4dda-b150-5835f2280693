"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  ArrowRightLeft, 
  ArrowDown, 
  Wallet, 
  RefreshCw, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { truncateAddress } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

interface CrossChainBridgeProps {
  wallets?: any[];
  onBridgeComplete?: () => void;
}

export default function CrossChainBridge({ wallets = [], onBridgeComplete }: CrossChainBridgeProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("bridge");
  const [sourceNetwork, setSourceNetwork] = useState<SupportedNetwork>(SupportedNetwork.ETHEREUM);
  const [destinationNetwork, setDestinationNetwork] = useState<SupportedNetwork>(SupportedNetwork.POLYGON);
  const [sourceWallet, setSourceWallet] = useState<string>("");
  const [destinationWallet, setDestinationWallet] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [token, setToken] = useState<string>("ETH");
  const [bridgeFee, setBridgeFee] = useState<string>("0.001");
  const [estimatedTime, setEstimatedTime] = useState<string>("10-15 minutes");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [bridgeTransactions, setBridgeTransactions] = useState<any[]>([]);
  const [filteredWallets, setFilteredWallets] = useState<any[]>([]);
  const [destinationWallets, setDestinationWallets] = useState<any[]>([]);

  // Filter wallets based on selected source network
  useEffect(() => {
    if (wallets.length > 0) {
      const filtered = wallets.filter(wallet => wallet.network === sourceNetwork);
      setFilteredWallets(filtered);
      
      // Set first wallet as default if available
      if (filtered.length > 0 && !sourceWallet) {
        setSourceWallet(filtered[0].id);
      }
    }
  }, [sourceNetwork, wallets, sourceWallet]);

  // Filter wallets based on selected destination network
  useEffect(() => {
    if (wallets.length > 0) {
      const filtered = wallets.filter(wallet => wallet.network === destinationNetwork);
      setDestinationWallets(filtered);
      
      // Set first wallet as default if available
      if (filtered.length > 0 && !destinationWallet) {
        setDestinationWallet(filtered[0].id);
      }
    }
  }, [destinationNetwork, wallets, destinationWallet]);

  // Fetch bridge transactions
  useEffect(() => {
    const fetchBridgeTransactions = async () => {
      try {
        const response = await fetch("/api/wallet/bridge/transactions");
        
        if (!response.ok) {
          throw new Error("Failed to fetch bridge transactions");
        }
        
        const data = await response.json();
        setBridgeTransactions(data.transactions);
      } catch (error) {
        console.error("Error fetching bridge transactions:", error);
      }
    };
    
    fetchBridgeTransactions();
  }, []);

  // Swap networks
  const swapNetworks = () => {
    const tempNetwork = sourceNetwork;
    setSourceNetwork(destinationNetwork);
    setDestinationNetwork(tempNetwork);
    
    // Clear selected wallets
    setSourceWallet("");
    setDestinationWallet("");
  };

  // Estimate bridge fee and time
  const estimateBridgeFee = async () => {
    if (!sourceNetwork || !destinationNetwork || !amount || !token) return;
    
    try {
      const response = await fetch(`/api/wallet/bridge/estimate?sourceNetwork=${sourceNetwork}&destinationNetwork=${destinationNetwork}&amount=${amount}&token=${token}`);
      
      if (!response.ok) {
        throw new Error("Failed to estimate bridge fee");
      }
      
      const data = await response.json();
      setBridgeFee(data.fee);
      setEstimatedTime(data.estimatedTime);
    } catch (error) {
      console.error("Error estimating bridge fee:", error);
      toast({
        title: "Error",
        description: "Failed to estimate bridge fee. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Initiate bridge transaction
  const initiateBridge = async () => {
    if (!sourceNetwork || !destinationNetwork || !sourceWallet || !destinationWallet || !amount || !token) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch("/api/wallet/bridge", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sourceWalletId: sourceWallet,
          destinationWalletId: destinationWallet,
          sourceNetwork,
          destinationNetwork,
          amount,
          token,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to initiate bridge transaction");
      }
      
      const data = await response.json();
      
      toast({
        title: "Bridge Initiated",
        description: "Your bridge transaction has been initiated successfully.",
      });
      
      // Add the new transaction to the list
      setBridgeTransactions(prev => [data.transaction, ...prev]);
      
      // Reset form
      setAmount("");
      
      // Call onBridgeComplete callback if provided
      if (onBridgeComplete) {
        onBridgeComplete();
      }
      
      // Switch to history tab
      setActiveTab("history");
    } catch (error) {
      console.error("Error initiating bridge transaction:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to initiate bridge transaction",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      case "INITIATED":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            <RefreshCw className="mr-1 h-3 w-3" />
            Initiated
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            {status}
          </Badge>
        );
    }
  };

  // Get network color
  const getNetworkColor = (network: string) => {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return "bg-blue-100 text-blue-600";
      case SupportedNetwork.POLYGON:
        return "bg-purple-100 text-purple-600";
      case SupportedNetwork.OPTIMISM:
        return "bg-red-100 text-red-600";
      case SupportedNetwork.ARBITRUM:
        return "bg-indigo-100 text-indigo-600";
      case SupportedNetwork.BASE:
        return "bg-blue-100 text-blue-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  // Get network icon (simplified for this example)
  const getNetworkIcon = (network: string) => {
    return <Wallet className="h-4 w-4" />;
  };

  // Get source wallet
  const getWallet = (walletId: string) => {
    return wallets.find(wallet => wallet.id === walletId);
  };

  // Mock tokens for each network
  const getTokensForNetwork = (network: string) => {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return ["ETH", "USDC", "USDT", "DAI"];
      case SupportedNetwork.POLYGON:
        return ["MATIC", "USDC", "USDT", "DAI"];
      case SupportedNetwork.OPTIMISM:
        return ["ETH", "USDC", "USDT", "DAI"];
      case SupportedNetwork.ARBITRUM:
        return ["ETH", "USDC", "USDT", "DAI"];
      case SupportedNetwork.BASE:
        return ["ETH", "USDC", "USDT", "DAI"];
      default:
        return ["ETH"];
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cross-Chain Bridge</CardTitle>
        <CardDescription>
          Transfer assets between different blockchain networks
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full mb-4">
            <TabsTrigger value="bridge" className="flex items-center">
              <ArrowRightLeft className="mr-2 h-4 w-4" />
              Bridge
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              History
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="bridge">
            <div className="space-y-6">
              <div className="flex flex-col space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Source Network</Label>
                    <Select value={sourceNetwork} onValueChange={(value) => setSourceNetwork(value as SupportedNetwork)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select source network" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={SupportedNetwork.ETHEREUM}>Ethereum</SelectItem>
                        <SelectItem value={SupportedNetwork.POLYGON}>Polygon</SelectItem>
                        <SelectItem value={SupportedNetwork.OPTIMISM}>Optimism</SelectItem>
                        <SelectItem value={SupportedNetwork.ARBITRUM}>Arbitrum</SelectItem>
                        <SelectItem value={SupportedNetwork.BASE}>Base</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Source Wallet</Label>
                    <Select value={sourceWallet} onValueChange={setSourceWallet}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select source wallet" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredWallets.length > 0 ? (
                          filteredWallets.map((wallet) => (
                            <SelectItem key={wallet.id} value={wallet.id}>
                              {truncateAddress(wallet.address)} ({wallet.balance} {wallet.network === SupportedNetwork.POLYGON ? "MATIC" : "ETH"})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>
                            No wallets available for this network
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex justify-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={swapNetworks}
                    className="rounded-full"
                  >
                    <ArrowDown className="h-5 w-5" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Destination Network</Label>
                    <Select value={destinationNetwork} onValueChange={(value) => setDestinationNetwork(value as SupportedNetwork)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select destination network" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={SupportedNetwork.ETHEREUM}>Ethereum</SelectItem>
                        <SelectItem value={SupportedNetwork.POLYGON}>Polygon</SelectItem>
                        <SelectItem value={SupportedNetwork.OPTIMISM}>Optimism</SelectItem>
                        <SelectItem value={SupportedNetwork.ARBITRUM}>Arbitrum</SelectItem>
                        <SelectItem value={SupportedNetwork.BASE}>Base</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Destination Wallet</Label>
                    <Select value={destinationWallet} onValueChange={setDestinationWallet}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select destination wallet" />
                      </SelectTrigger>
                      <SelectContent>
                        {destinationWallets.length > 0 ? (
                          destinationWallets.map((wallet) => (
                            <SelectItem key={wallet.id} value={wallet.id}>
                              {truncateAddress(wallet.address)} ({wallet.balance} {wallet.network === SupportedNetwork.POLYGON ? "MATIC" : "ETH"})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>
                            No wallets available for this network
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Token</Label>
                    <Select value={token} onValueChange={setToken}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select token" />
                      </SelectTrigger>
                      <SelectContent>
                        {getTokensForNetwork(sourceNetwork).map((token) => (
                          <SelectItem key={token} value={token}>
                            {token}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Amount</Label>
                    <div className="flex space-x-2">
                      <Input
                        type="number"
                        placeholder="0.0"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        onBlur={estimateBridgeFee}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        className="whitespace-nowrap"
                        onClick={() => {
                          // Set max amount logic would go here
                          const sourceWalletObj = getWallet(sourceWallet);
                          if (sourceWalletObj) {
                            setAmount(sourceWalletObj.balance.toString());
                            estimateBridgeFee();
                          }
                        }}
                      >
                        Max
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Bridge Fee</span>
                    <span className="text-sm font-medium">{bridgeFee} {token}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Estimated Time</span>
                    <span className="text-sm font-medium">{estimatedTime}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">You Will Receive</span>
                    <span className="text-sm font-medium">
                      {amount && bridgeFee ? (parseFloat(amount) - parseFloat(bridgeFee)).toFixed(6) : "0.0"} {token}
                    </span>
                  </div>
                </div>
                
                <div className="bg-muted p-4 rounded-md flex items-start space-x-2">
                  <Info className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-muted-foreground">
                    <p>
                      Bridging assets between networks typically takes {estimatedTime}. The exact time depends on network congestion and the bridge provider.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="history">
            <div className="space-y-4">
              {bridgeTransactions.length > 0 ? (
                bridgeTransactions.map((transaction) => (
                  <div key={transaction.id} className="border rounded-md p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center space-x-2">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getNetworkColor(transaction.sourceNetwork)}`}>
                          {getNetworkIcon(transaction.sourceNetwork)}
                        </div>
                        <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getNetworkColor(transaction.destinationNetwork)}`}>
                          {getNetworkIcon(transaction.destinationNetwork)}
                        </div>
                      </div>
                      {getStatusBadge(transaction.status)}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">From</p>
                        <p className="font-medium">{transaction.sourceNetwork}</p>
                        <p className="text-xs text-muted-foreground">{truncateAddress(transaction.sourceAddress)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">To</p>
                        <p className="font-medium">{transaction.destinationNetwork}</p>
                        <p className="text-xs text-muted-foreground">{truncateAddress(transaction.destinationAddress)}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">Amount</p>
                        <p className="font-medium">{transaction.amount} {transaction.token}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Fee</p>
                        <p className="font-medium">{transaction.fee} {transaction.token}</p>
                      </div>
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      {transaction.createdAt}
                    </div>
                    
                    {transaction.status === "PENDING" && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>Progress</span>
                          <span>Estimated: {transaction.estimatedTimeMinutes} minutes</span>
                        </div>
                        <Progress value={transaction.progress || 30} className="h-1" />
                      </div>
                    )}
                    
                    {transaction.status === "FAILED" && transaction.errorMessage && (
                      <div className="bg-red-50 border-l-4 border-red-400 p-2">
                        <p className="text-xs text-red-700">{transaction.errorMessage}</p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Clock className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-medium">No bridge transactions</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    You haven't made any cross-chain bridge transactions yet.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        {activeTab === "bridge" && (
          <Button 
            className="w-full" 
            onClick={initiateBridge}
            disabled={isLoading || !sourceWallet || !destinationWallet || !amount || parseFloat(amount) <= 0}
          >
            {isLoading ? "Processing..." : "Bridge Assets"}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
