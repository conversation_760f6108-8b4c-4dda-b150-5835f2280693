'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import {
  Loader2,
  ArrowRight,
  ArrowLeft,
  Info,
  AlertCircle,
  Check,
  Shield,
  Key,
  FileText,
  Users,
  Wallet
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';

// Recovery steps
enum RecoveryStep {
  METHOD_SELECTION = 1,
  VERIFICATION = 2,
  RECOVERY_PROCESS = 3,
  CONFIRMATION = 4,
}

// Recovery methods
enum RecoveryMethod {
  SEED_PHRASE = 'seed_phrase',
  SOCIAL_RECOVERY = 'social_recovery',
  ADMIN_RECOVERY = 'admin_recovery',
  BACKUP_KEY = 'backup_key',
}

interface WalletRecoveryGuideProps {
  walletId?: string;
  walletType?: 'smart' | 'standard';
  onRecoveryComplete?: () => void;
}

export function WalletRecoveryGuide({
  walletId = '123456',
  walletType = 'smart',
  onRecoveryComplete
}: WalletRecoveryGuideProps) {
  const [currentStep, setCurrentStep] = useState<RecoveryStep>(RecoveryStep.METHOD_SELECTION);
  const [recoveryMethod, setRecoveryMethod] = useState<RecoveryMethod | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [recoveryProgress, setRecoveryProgress] = useState(0);
  const [recoveryComplete, setRecoveryComplete] = useState(false);

  const totalSteps = 4;

  // Handle step navigation
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);

      // If moving to recovery process step, start the progress simulation
      if (currentStep === RecoveryStep.VERIFICATION) {
        simulateRecoveryProgress();
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Simulate recovery progress
  const simulateRecoveryProgress = () => {
    setRecoveryProgress(0);
    const interval = setInterval(() => {
      setRecoveryProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setCurrentStep(RecoveryStep.CONFIRMATION);
          setRecoveryComplete(true);
          return 100;
        }
        return prev + 5;
      });
    }, 500);
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case RecoveryStep.METHOD_SELECTION:
        return 'Select Recovery Method';
      case RecoveryStep.VERIFICATION:
        return 'Verify Identity';
      case RecoveryStep.RECOVERY_PROCESS:
        return 'Recovering Wallet';
      case RecoveryStep.CONFIRMATION:
        return 'Recovery Complete';
      default:
        return '';
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case RecoveryStep.METHOD_SELECTION:
        return (
          <MethodSelectionStep
            walletType={walletType}
            selectedMethod={recoveryMethod}
            onSelectMethod={setRecoveryMethod}
          />
        );
      case RecoveryStep.VERIFICATION:
        return (
          <VerificationStep
            recoveryMethod={recoveryMethod}
          />
        );
      case RecoveryStep.RECOVERY_PROCESS:
        return (
          <RecoveryProcessStep
            progress={recoveryProgress}
            recoveryMethod={recoveryMethod}
          />
        );
      case RecoveryStep.CONFIRMATION:
        return (
          <ConfirmationStep
            walletId={walletId}
            recoveryMethod={recoveryMethod}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Wallet Recovery</CardTitle>
        <CardDescription>
          Recover access to your {walletType === 'smart' ? 'smart' : 'standard'} wallet
        </CardDescription>
        {currentStep !== RecoveryStep.CONFIRMATION && (
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-2">
              <span>Step {currentStep} of {totalSteps}</span>
              <span>{getStepTitle()}</span>
            </div>
            <Progress value={(currentStep / totalSteps) * 100} className="h-2" />
          </div>
        )}
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-[300px]"
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>
      </CardContent>
      {currentStep !== RecoveryStep.RECOVERY_PROCESS && currentStep !== RecoveryStep.CONFIRMATION && (
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === RecoveryStep.METHOD_SELECTION || isSubmitting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          <Button
            type="button"
            onClick={nextStep}
            disabled={
              (currentStep === RecoveryStep.METHOD_SELECTION && !recoveryMethod) ||
              isSubmitting
            }
          >
            Continue
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      )}
      {currentStep === RecoveryStep.CONFIRMATION && (
        <CardFooter className="flex justify-center">
          <Button
            type="button"
            onClick={() => {
              if (onRecoveryComplete) {
                onRecoveryComplete();
              }
            }}
          >
            Go to Wallet
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}

// Recovery Method Card Component
function RecoveryMethodCard({
  title,
  description,
  icon,
  isSelected,
  onClick,
  recommended = false
}: {
  title: string,
  description: string,
  icon: React.ReactNode,
  isSelected: boolean,
  onClick: () => void,
  recommended?: boolean
}) {
  return (
    <div
      className={`rounded-lg border-2 p-4 cursor-pointer transition-all ${
        isSelected ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50'
      }`}
      onClick={onClick}
    >
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0">
          {icon}
        </div>
        <div>
          <h4 className="font-medium">{title}</h4>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        {recommended && (
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary ml-auto">
            Recommended
          </span>
        )}
      </div>

      {isSelected && (
        <div className="mt-2 flex justify-end">
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
            Selected
          </span>
        </div>
      )}
    </div>
  );
}

// Step 1: Method Selection
function MethodSelectionStep({
  walletType,
  selectedMethod,
  onSelectMethod
}: {
  walletType: 'smart' | 'standard',
  selectedMethod: RecoveryMethod | null,
  onSelectMethod: (method: RecoveryMethod) => void
}) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Select Recovery Method</h3>
        <p className="text-sm text-muted-foreground">
          Choose how you want to recover access to your wallet
        </p>
      </div>

      <div className="space-y-3">
        {walletType === 'smart' && (
          <>
            <RecoveryMethodCard
              title="Social Recovery"
              description="Use trusted guardians to recover your wallet"
              icon={<Users className="h-6 w-6 text-primary" />}
              isSelected={selectedMethod === RecoveryMethod.SOCIAL_RECOVERY}
              onClick={() => onSelectMethod(RecoveryMethod.SOCIAL_RECOVERY)}
              recommended
            />

            <RecoveryMethodCard
              title="Admin Recovery"
              description="Use organization admin privileges to recover"
              icon={<Shield className="h-6 w-6 text-primary" />}
              isSelected={selectedMethod === RecoveryMethod.ADMIN_RECOVERY}
              onClick={() => onSelectMethod(RecoveryMethod.ADMIN_RECOVERY)}
            />
          </>
        )}

        <RecoveryMethodCard
          title="Seed Phrase"
          description="Use your 12 or 24-word recovery phrase"
          icon={<FileText className="h-6 w-6 text-primary" />}
          isSelected={selectedMethod === RecoveryMethod.SEED_PHRASE}
          onClick={() => onSelectMethod(RecoveryMethod.SEED_PHRASE)}
          recommended={walletType === 'standard'}
        />

        <RecoveryMethodCard
          title="Backup Key"
          description="Use your backup private key"
          icon={<Key className="h-6 w-6 text-primary" />}
          isSelected={selectedMethod === RecoveryMethod.BACKUP_KEY}
          onClick={() => onSelectMethod(RecoveryMethod.BACKUP_KEY)}
        />
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Recovery Information</AlertTitle>
        <AlertDescription>
          {selectedMethod === RecoveryMethod.SOCIAL_RECOVERY && (
            "Social recovery requires approval from your designated guardians. Make sure you can contact them."
          )}
          {selectedMethod === RecoveryMethod.ADMIN_RECOVERY && (
            "Admin recovery uses your organization's admin privileges. You'll need admin credentials."
          )}
          {selectedMethod === RecoveryMethod.SEED_PHRASE && (
            "You'll need your complete 12 or 24-word recovery phrase in the correct order."
          )}
          {selectedMethod === RecoveryMethod.BACKUP_KEY && (
            "You'll need your backup private key. This is a long string that starts with 0x."
          )}
          {!selectedMethod && (
            "Select a recovery method to see specific information about the recovery process."
          )}
        </AlertDescription>
      </Alert>
    </div>
  );
}

// Step 2: Verification
function VerificationStep({
  recoveryMethod
}: {
  recoveryMethod: RecoveryMethod | null
}) {
  const [seedPhrase, setSeedPhrase] = useState('');
  const [privateKey, setPrivateKey] = useState('');
  const [guardianEmails, setGuardianEmails] = useState('');
  const [adminCode, setAdminCode] = useState('');

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Verify Your Identity</h3>
        <p className="text-sm text-muted-foreground">
          Provide the necessary information to verify your identity
        </p>
      </div>

      {recoveryMethod === RecoveryMethod.SEED_PHRASE && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="seed-phrase">Recovery Phrase</Label>
            <Input
              id="seed-phrase"
              placeholder="Enter your 12 or 24-word recovery phrase"
              value={seedPhrase}
              onChange={(e) => setSeedPhrase(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              Enter all words separated by spaces
            </p>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Security Warning</AlertTitle>
            <AlertDescription>
              Never share your recovery phrase with anyone. Legitimate services will never ask for your full recovery phrase.
            </AlertDescription>
          </Alert>
        </div>
      )}

      {recoveryMethod === RecoveryMethod.BACKUP_KEY && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="private-key">Backup Private Key</Label>
            <Input
              id="private-key"
              placeholder="Enter your private key"
              value={privateKey}
              onChange={(e) => setPrivateKey(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              Enter your private key starting with 0x
            </p>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Security Warning</AlertTitle>
            <AlertDescription>
              Never share your private key with anyone. Legitimate services will never ask for your private key.
            </AlertDescription>
          </Alert>
        </div>
      )}

      {recoveryMethod === RecoveryMethod.SOCIAL_RECOVERY && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="guardian-emails">Guardian Emails</Label>
            <Input
              id="guardian-emails"
              placeholder="Enter guardian email addresses"
              value={guardianEmails}
              onChange={(e) => setGuardianEmails(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              Enter email addresses separated by commas
            </p>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Guardian Approval</AlertTitle>
            <AlertDescription>
              Recovery requests will be sent to your guardians. At least 2 guardians must approve your request within 24 hours.
            </AlertDescription>
          </Alert>
        </div>
      )}

      {recoveryMethod === RecoveryMethod.ADMIN_RECOVERY && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="admin-code">Admin Recovery Code</Label>
            <Input
              id="admin-code"
              placeholder="Enter admin recovery code"
              value={adminCode}
              onChange={(e) => setAdminCode(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              Enter the recovery code provided by your organization admin
            </p>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Admin Verification</AlertTitle>
            <AlertDescription>
              Your recovery request will be verified by your organization's administrators. This process may take up to 24 hours.
            </AlertDescription>
          </Alert>
        </div>
      )}
    </div>
  );
}

// Step 3: Recovery Process
function RecoveryProcessStep({
  progress,
  recoveryMethod
}: {
  progress: number,
  recoveryMethod: RecoveryMethod | null
}) {
  const getRecoverySteps = () => {
    switch (recoveryMethod) {
      case RecoveryMethod.SEED_PHRASE:
        return [
          "Validating recovery phrase",
          "Deriving wallet keys",
          "Verifying wallet ownership",
          "Restoring wallet access"
        ];
      case RecoveryMethod.BACKUP_KEY:
        return [
          "Validating private key",
          "Verifying wallet ownership",
          "Restoring wallet access"
        ];
      case RecoveryMethod.SOCIAL_RECOVERY:
        return [
          "Sending recovery requests to guardians",
          "Waiting for guardian approvals",
          "Verifying guardian signatures",
          "Restoring wallet access"
        ];
      case RecoveryMethod.ADMIN_RECOVERY:
        return [
          "Validating admin recovery code",
          "Verifying organization credentials",
          "Generating new wallet access",
          "Restoring wallet access"
        ];
      default:
        return ["Processing recovery request"];
    }
  };

  const steps = getRecoverySteps();
  const currentStepIndex = Math.min(Math.floor(progress / (100 / steps.length)), steps.length - 1);

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Recovering Your Wallet</h3>
        <p className="text-sm text-muted-foreground">
          Please wait while we process your recovery request
        </p>
      </div>

      <div className="space-y-4">
        <Progress value={progress} className="h-2" />
        <p className="text-sm text-center">{progress}% Complete</p>

        <div className="space-y-3 mt-6">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center">
              <div className={`flex-shrink-0 h-6 w-6 rounded-full flex items-center justify-center ${
                index < currentStepIndex
                  ? 'bg-primary text-primary-foreground'
                  : index === currentStepIndex
                    ? 'bg-primary/20 text-primary animate-pulse'
                    : 'bg-muted text-muted-foreground'
              }`}>
                {index < currentStepIndex ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <span className="text-xs">{index + 1}</span>
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm ${
                  index < currentStepIndex
                    ? 'text-muted-foreground line-through'
                    : index === currentStepIndex
                      ? 'font-medium'
                      : 'text-muted-foreground'
                }`}>
                  {step}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Recovery in Progress</AlertTitle>
        <AlertDescription>
          Please do not close this window or navigate away during the recovery process.
        </AlertDescription>
      </Alert>
    </div>
  );
}

// Step 4: Confirmation
function ConfirmationStep({
  walletId,
  recoveryMethod
}: {
  walletId: string,
  recoveryMethod: RecoveryMethod | null
}) {
  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-green-50 p-6 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <Check className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="mt-3 text-lg font-medium text-green-800">
          Recovery Complete
        </h3>
        <p className="mt-1 text-sm text-green-700">
          Your wallet has been successfully recovered
        </p>
      </div>

      <div className="rounded-lg border p-4 space-y-4">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium">Wallet ID</span>
            <span className="text-sm">{walletId}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Recovery Method</span>
            <span className="text-sm">
              {recoveryMethod === RecoveryMethod.SEED_PHRASE && "Recovery Phrase"}
              {recoveryMethod === RecoveryMethod.BACKUP_KEY && "Backup Key"}
              {recoveryMethod === RecoveryMethod.SOCIAL_RECOVERY && "Social Recovery"}
              {recoveryMethod === RecoveryMethod.ADMIN_RECOVERY && "Admin Recovery"}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium">Status</span>
            <span className="text-sm flex items-center">
              <Check className="mr-1 h-4 w-4 text-green-500" />
              Recovered
            </span>
          </div>
        </div>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Security Recommendation</AlertTitle>
        <AlertDescription>
          For security reasons, we recommend updating your wallet's security settings and backup methods.
        </AlertDescription>
      </Alert>
    </div>
  );
}
}
