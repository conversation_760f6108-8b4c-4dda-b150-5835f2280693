"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Users, Shield } from "lucide-react";

/**
 * Skeleton loading component for Smart Wallet Recovery
 * Matches the structure of the actual component for a smoother transition
 */
export function SmartWalletRecoverySkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-7 w-48 mb-2" />
        <Skeleton className="h-4 w-64" />
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <Tabs defaultValue="guardians">
            <TabsList className="w-full mb-4">
              <TabsTrigger value="guardians" className="flex-1">
                <Users className="mr-2 h-4 w-4" />
                Guardians
              </TabsTrigger>
              <TabsTrigger value="requests" className="flex-1">
                <Shield className="mr-2 h-4 w-4" />
                Recovery Requests
              </TabsTrigger>
            </TabsList>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-9 w-36" />
              </div>
              
              <div className="space-y-2">
                {/* Guardian table skeleton */}
                <div className="border rounded-md">
                  <div className="grid grid-cols-4 gap-4 p-4 border-b">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="grid grid-cols-4 gap-4 p-4 border-b">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-28" />
                      <Skeleton className="h-6 w-16 rounded-full" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mt-4">
                <Skeleton className="h-24 w-full rounded-md" />
              </div>
            </div>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton for the guardian details section
 */
export function GuardianDetailsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-9 w-36" />
      </div>
      
      <div className="border rounded-md">
        <div className="grid grid-cols-4 gap-4 p-4 border-b">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-20" />
        </div>
        
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="grid grid-cols-4 gap-4 p-4 border-b">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-28" />
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-4 w-20" />
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Skeleton for the recovery requests section
 */
export function RecoveryRequestsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-9 w-36" />
      </div>
      
      <div className="border rounded-md">
        <div className="grid grid-cols-6 gap-4 p-4 border-b">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
        </div>
        
        {Array.from({ length: 2 }).map((_, i) => (
          <div key={i} className="grid grid-cols-6 gap-4 p-4 border-b">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-20 rounded-md" />
          </div>
        ))}
      </div>
    </div>
  );
}
