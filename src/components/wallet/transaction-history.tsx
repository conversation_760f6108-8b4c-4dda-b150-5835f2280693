'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  ExternalLink, 
  RefreshCw,
  CheckCircle2,
  XCircle,
  Clock,
  Filter
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from '@/components/ui/use-toast';
import { SupportedNetwork, getNetworkConfig } from '@/lib/blockchain-config';

interface Transaction {
  id: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'PURCHASE' | 'SALE' | 'FEE';
  amount: number;
  fee: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  transactionHash: string | null;
  blockNumber: number | null;
  network: string;
  chainId: number;
  createdAt: string;
  updatedAt: string;
}

interface TransactionHistoryProps {
  walletId: string;
  network: SupportedNetwork;
  isTestnet: boolean;
}

export function TransactionHistory({ walletId, network, isTestnet }: TransactionHistoryProps) {
  const router = useRouter();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filter, setFilter] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const pageSize = 10;

  const fetchTransactions = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const response = await fetch(
        `/api/wallet/transactions?walletId=${walletId}&page=${refresh ? 1 : page}&limit=${pageSize}${
          filter ? `&type=${filter}` : ''
        }`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch transactions');
      }

      const data = await response.json();

      if (refresh || page === 1) {
        setTransactions(data.transactions);
      } else {
        setTransactions((prev) => [...prev, ...data.transactions]);
      }

      setHasMore(data.pagination.hasMore);

      if (refresh) {
        setPage(1);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load transaction history',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [walletId, page, filter]);

  const handleRefresh = () => {
    fetchTransactions(true);
  };

  const handleLoadMore = () => {
    setPage((prev) => prev + 1);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
        return <ArrowDownRight className="h-4 w-4 text-green-500" />;
      case 'WITHDRAWAL':
        return <ArrowUpRight className="h-4 w-4 text-red-500" />;
      case 'PURCHASE':
        return <ArrowDownRight className="h-4 w-4 text-blue-500" />;
      case 'SALE':
        return <ArrowUpRight className="h-4 w-4 text-purple-500" />;
      case 'FEE':
        return <ArrowUpRight className="h-4 w-4 text-orange-500" />;
      default:
        return <ArrowDownRight className="h-4 w-4" />;
    }
  };

  const getTransactionStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
        return 'Received';
      case 'WITHDRAWAL':
        return 'Sent';
      case 'PURCHASE':
        return 'Purchased';
      case 'SALE':
        return 'Sold';
      case 'FEE':
        return 'Fee';
      default:
        return type;
    }
  };

  const getBlockExplorerUrl = (txHash: string) => {
    const networkConfig = getNetworkConfig(network as SupportedNetwork, isTestnet);
    return `${networkConfig.blockExplorer}/tx/${txHash}`;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Transaction History</CardTitle>
          <CardDescription>Recent transactions for this wallet</CardDescription>
        </div>
        <div className="flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                {filter ? getTransactionTypeLabel(filter) : 'All Types'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filter by type</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={() => setFilter(null)}>
                  All Types
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('DEPOSIT')}>
                  Received
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('WITHDRAWAL')}>
                  Sent
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('PURCHASE')}>
                  Purchased
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('SALE')}>
                  Sold
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('FEE')}>
                  Fees
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span className="sr-only">Refresh</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                </div>
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        ) : transactions.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-muted-foreground">No transactions found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((tx) => (
              <div
                key={tx.id}
                className="flex items-center justify-between border-b border-border py-3 last:border-0"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                    {getTransactionTypeIcon(tx.type)}
                  </div>
                  <div>
                    <div className="flex items-center">
                      <p className="font-medium">{getTransactionTypeLabel(tx.type)}</p>
                      <Badge
                        variant={
                          tx.status === 'COMPLETED'
                            ? 'success'
                            : tx.status === 'PENDING'
                            ? 'outline'
                            : 'destructive'
                        }
                        className="ml-2"
                      >
                        {tx.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(tx.createdAt), 'MMM d, yyyy • h:mm a')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center justify-end space-x-1">
                    <p
                      className={`font-medium ${
                        tx.type === 'DEPOSIT' || tx.type === 'PURCHASE'
                          ? 'text-green-500'
                          : 'text-red-500'
                      }`}
                    >
                      {tx.type === 'DEPOSIT' || tx.type === 'PURCHASE' ? '+' : '-'}
                      {tx.amount.toFixed(6)}
                    </p>
                    {tx.transactionHash && (
                      <a
                        href={getBlockExplorerUrl(tx.transactionHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-2 inline-flex items-center text-blue-500 hover:text-blue-700"
                      >
                        <ExternalLink className="h-3 w-3" />
                        <span className="sr-only">View on block explorer</span>
                      </a>
                    )}
                  </div>
                  {tx.fee > 0 && (
                    <p className="text-xs text-muted-foreground">Fee: {tx.fee.toFixed(6)}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {hasMore && (
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            onClick={handleLoadMore}
            disabled={isLoading}
          >
            Load More
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
