"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { truncateAddress } from "@/lib/utils";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ValidatedForm } from "@/components/forms/validated-form";
import { tokenTransferSchema } from "@/lib/validation/blockchain-schemas";
import { AlertCircle, Shield, Wallet, Zap } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import SmartWalletRecovery from "./smart-wallet-recovery";
import { SmartWalletRecoverySkeleton } from "./smart-wallet-recovery-skeleton";

interface SmartWalletProps {
  wallet?: {
    id: string;
    address: string;
    isSmartWallet: boolean;
    balance: number;
  };
  onWalletCreated?: () => void;
}

export default function SmartWalletComponent({ wallet, onWalletCreated }: SmartWalletProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Send tokens state
  const [sendSuccess, setSendSuccess] = useState<string | null>(null);
  const [gasPrices, setGasPrices] = useState<any>(null);

  // Default form values for token transfer
  const defaultTokenTransferValues = {
    to: "",
    tokenAddress: "ETH",
    amount: "",
    gasOption: "standard",
    memo: "",
  };

  const createSmartWallet = async () => {
    if (!session?.user) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch("/api/wallet/smart-wallet", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create smart wallet");
      }

      const data = await response.json();
      setSuccess("Smart wallet created successfully!");

      if (onWalletCreated) {
        onWalletCreated();
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error creating smart wallet:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendTokens = async (data: any) => {
    if (!session?.user || !wallet) return;

    setSendSuccess(null);

    try {
      const response = await fetch("/api/wallet/send-tokens", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          walletId: wallet.id,
          to: data.to,
          tokenAddress: data.tokenAddress,
          amount: data.amount,
          gasOption: data.gasOption,
          memo: data.memo,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send tokens");
      }

      const result = await response.json();
      setSendSuccess(`Successfully sent ${data.amount} ${data.tokenAddress} to ${truncateAddress(data.to)}`);

      // Refresh wallet data
      if (onWalletCreated) {
        onWalletCreated();
      }

      return data;
    } catch (error) {
      console.error("Error sending tokens:", error);
      throw new Error(error instanceof Error ? error.message : "An error occurred");
    }
  };

  // If user doesn't have a smart wallet yet
  if (!wallet || !wallet.isSmartWallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Smart Wallet</CardTitle>
          <CardDescription>
            Create a smart contract wallet to manage your digital assets with enhanced security and features.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert variant="default" className="bg-blue-50 text-blue-700 border-blue-200">
              <AlertCircle className="h-4 w-4 text-blue-500" />
              <AlertDescription>
                Smart wallets offer enhanced security, social recovery, and advanced features compared to regular wallets.
              </AlertDescription>
            </Alert>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert variant="default" className="bg-green-50 text-green-700 border-green-200">
                <AlertCircle className="h-4 w-4 text-green-500" />
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={createSmartWallet}
            disabled={loading || !!success}
            className="w-full"
          >
            {loading ? "Creating Smart Wallet..." : "Create Smart Wallet"}
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // If user has a smart wallet
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Smart Wallet</CardTitle>
          <CardDescription>
            Manage your smart contract wallet with enhanced security features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Address</span>
              <span className="text-sm font-medium">{truncateAddress(wallet.address)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Balance</span>
              <span className="text-sm font-medium">{wallet.balance} ETH</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Type</span>
              <span className="text-sm font-medium">Smart Contract Wallet</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="mr-2 h-5 w-5" />
            Send Tokens
          </CardTitle>
          <CardDescription>
            Transfer tokens to another address
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sendSuccess && (
              <Alert variant="default" className="bg-green-50 text-green-700 border-green-200">
                <AlertCircle className="h-4 w-4 text-green-500" />
                <AlertDescription>{sendSuccess}</AlertDescription>
              </Alert>
            )}
            
            <Button className="w-full">
              Send Tokens
            </Button>
          </div>
        </CardContent>
      </Card>

      <SmartWalletRecovery
        walletId={wallet.id}
        walletAddress={wallet.address}
        isSmartWallet={wallet.isSmartWallet}
      />
    </div>
  );
}
