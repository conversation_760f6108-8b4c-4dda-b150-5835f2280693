import { ReactNode } from "react";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { UserRole } from "@/lib/authorization";
import { logger } from "@/lib/logger";

interface AuthGuardProps {
  children: ReactNode;
  requiredRole?: UserRole | UserRole[];
  fallback?: ReactNode;
  redirectTo?: string;
}

/**
 * Server component that protects routes based on authentication and role
 * 
 * @example
 * // Basic usage - requires authentication
 * <AuthGuard>
 *   <ProtectedContent />
 * </AuthGuard>
 * 
 * @example
 * // Require specific role
 * <AuthGuard requiredRole={UserRole.ADMIN}>
 *   <AdminDashboard />
 * </AuthGuard>
 * 
 * @example
 * // Require one of multiple roles
 * <AuthGuard requiredRole={[UserRole.ADMIN, UserRole.ORGANIZATION_ADMIN]}>
 *   <AdminContent />
 * </AuthGuard>
 * 
 * @example
 * // Custom fallback component
 * <AuthGuard fallback={<AccessDenied />}>
 *   <ProtectedContent />
 * </AuthGuard>
 */
export async function AuthGuard({
  children,
  requiredRole,
  fallback,
  redirectTo = "/login",
}: AuthGuardProps) {
  const session = await auth();
  
  // Not authenticated
  if (!session?.user) {
    logger.info("Unauthenticated access attempt");
    
    if (redirectTo) {
      redirect(redirectTo);
    }
    
    return fallback || null;
  }
  
  // Check role if required
  if (requiredRole) {
    const userRole = session.user.role as UserRole;
    const hasRequiredRole = Array.isArray(requiredRole)
      ? requiredRole.includes(userRole)
      : userRole === requiredRole;
    
    if (!hasRequiredRole) {
      logger.warn(`Role-based access denied for user ${session.user.email}. Required: ${Array.isArray(requiredRole) ? requiredRole.join(', ') : requiredRole}, Actual: ${userRole}`);
      
      if (redirectTo) {
        redirect(redirectTo);
      }
      
      return fallback || null;
    }
  }
  
  // User is authenticated and has required role
  return <>{children}</>;
}

/**
 * Admin-only guard component
 */
export async function AdminGuard({
  children,
  fallback,
  redirectTo = "/dashboard",
}: Omit<AuthGuardProps, "requiredRole">) {
  return (
    <AuthGuard
      requiredRole={UserRole.ADMIN}
      fallback={fallback}
      redirectTo={redirectTo}
    >
      {children}
    </AuthGuard>
  );
}

/**
 * Organization admin guard component
 */
export async function OrgAdminGuard({
  children,
  fallback,
  redirectTo = "/dashboard",
}: Omit<AuthGuardProps, "requiredRole">) {
  return (
    <AuthGuard
      requiredRole={[UserRole.ADMIN, UserRole.ORGANIZATION_ADMIN]}
      fallback={fallback}
      redirectTo={redirectTo}
    >
      {children}
    </AuthGuard>
  );
}
