"use client";

import { ReactNode, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { UserRole } from "@/lib/client-authorization";
import { Spinner } from "@/components/ui/spinner";

interface ClientAuthGuardProps {
  children: ReactNode;
  requiredRole?: UserRole | UserRole[];
  fallback?: ReactNode;
  redirectTo?: string;
  loadingComponent?: ReactNode;
}

/**
 * Client component that protects routes based on authentication and role
 *
 * @example
 * // Basic usage - requires authentication
 * <ClientAuthGuard>
 *   <ProtectedContent />
 * </ClientAuthGuard>
 *
 * @example
 * // Require specific role
 * <ClientAuthGuard requiredRole={UserRole.ADMIN}>
 *   <AdminDashboard />
 * </ClientAuthGuard>
 *
 * @example
 * // Custom loading component
 * <ClientAuthGuard loadingComponent={<CustomLoader />}>
 *   <ProtectedContent />
 * </ClientAuthGuard>
 */
export function ClientAuthGuard({
  children,
  requiredRole,
  fallback,
  redirectTo = "/login",
  loadingComponent,
}: ClientAuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    // Still loading
    if (status === "loading") {
      return;
    }

    // Not authenticated
    if (status === "unauthenticated") {
      if (redirectTo) {
        router.push(redirectTo);
      }
      setIsAuthorized(false);
      return;
    }

    // Check role if required
    if (requiredRole && session?.user?.role) {
      const userRole = session.user.role as UserRole;
      const hasRequiredRole = Array.isArray(requiredRole)
        ? requiredRole.includes(userRole)
        : userRole === requiredRole;

      if (!hasRequiredRole) {
        if (redirectTo) {
          router.push(redirectTo);
        }
        setIsAuthorized(false);
        return;
      }
    }

    // User is authenticated and has required role
    setIsAuthorized(true);
  }, [session, status, requiredRole, router, redirectTo]);

  // Show loading state
  if (status === "loading" || isAuthorized === null) {
    return loadingComponent || (
      <div className="flex justify-center items-center min-h-[200px]">
        <Spinner size="lg" />
      </div>
    );
  }

  // Show fallback or null if not authorized
  if (!isAuthorized) {
    return fallback || null;
  }

  // Show children if authorized
  return <>{children}</>;
}

/**
 * Admin-only guard client component
 */
export function ClientAdminGuard({
  children,
  fallback,
  redirectTo = "/dashboard",
  loadingComponent,
}: Omit<ClientAuthGuardProps, "requiredRole">) {
  return (
    <ClientAuthGuard
      requiredRole={UserRole.ADMIN}
      fallback={fallback}
      redirectTo={redirectTo}
      loadingComponent={loadingComponent}
    >
      {children}
    </ClientAuthGuard>
  );
}

/**
 * Organization admin guard client component
 */
export function ClientOrgAdminGuard({
  children,
  fallback,
  redirectTo = "/dashboard",
  loadingComponent,
}: Omit<ClientAuthGuardProps, "requiredRole">) {
  return (
    <ClientAuthGuard
      requiredRole={[UserRole.ADMIN, UserRole.ORGANIZATION_ADMIN]}
      fallback={fallback}
      redirectTo={redirectTo}
      loadingComponent={loadingComponent}
    >
      {children}
    </ClientAuthGuard>
  );
}
