"use client";

import { ReactNode } from "react";
import { ClientAuthGuard } from "@/components/auth/client-auth-guard";
import { UserRole } from "@/lib/client-authorization";

interface ProtectedPageProps {
  children: ReactNode;
  requiredRole?: UserRole | UserRole[];
  fallback?: ReactNode;
  redirectTo?: string;
  loadingComponent?: ReactNode;
}

/**
 * A wrapper component for protected pages
 * This component can be used to protect client-side pages that require authentication
 */
export function ProtectedPage({
  children,
  requiredRole,
  fallback,
  redirectTo = "/login",
  loadingComponent,
}: ProtectedPageProps) {
  return (
    <ClientAuthGuard
      requiredRole={requiredRole}
      fallback={fallback}
      redirectTo={redirectTo}
      loadingComponent={loadingComponent}
    >
      {children}
    </ClientAuthGuard>
  );
}

/**
 * Admin-only protected page
 */
export function AdminProtectedPage({
  children,
  fallback,
  redirectTo = "/dashboard",
  loadingComponent,
}: Omit<ProtectedPageProps, "requiredRole">) {
  return (
    <ProtectedPage
      requiredRole={UserRole.ADMIN}
      fallback={fallback}
      redirectTo={redirectTo}
      loadingComponent={loadingComponent}
    >
      {children}
    </ProtectedPage>
  );
}

/**
 * Organization admin protected page
 */
export function OrgAdminProtectedPage({
  children,
  fallback,
  redirectTo = "/dashboard",
  loadingComponent,
}: Omit<ProtectedPageProps, "requiredRole">) {
  return (
    <ProtectedPage
      requiredRole={[UserRole.ADMIN, UserRole.ORGANIZATION_ADMIN]}
      fallback={fallback}
      redirectTo={redirectTo}
      loadingComponent={loadingComponent}
    >
      {children}
    </ProtectedPage>
  );
}
