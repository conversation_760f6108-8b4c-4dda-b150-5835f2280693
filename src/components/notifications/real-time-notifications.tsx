'use client';

import { useEffect, useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import { Bell, X } from 'lucide-react';
import { NotificationType, NotificationPriority } from '@prisma/client';
import { useRouter } from 'next/navigation';

// Mock WebSocket for demonstration purposes
// In a real implementation, you would use a real WebSocket connection
class MockWebSocket {
  private callbacks: Record<string, Function[]> = {};
  private interval: NodeJS.Timeout | null = null;

  constructor(url: string) {
    console.log(`Connecting to ${url}`);
    
    // Simulate connection delay
    setTimeout(() => {
      this.trigger('open', {});
      
      // Simulate receiving notifications periodically
      this.interval = setInterval(() => {
        // Only send a notification occasionally
        if (Math.random() > 0.8) {
          this.simulateNotification();
        }
      }, 30000);
    }, 1000);
  }

  addEventListener(event: string, callback: Function) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
  }

  removeEventListener(event: string, callback: Function) {
    if (this.callbacks[event]) {
      this.callbacks[event] = this.callbacks[event].filter(cb => cb !== callback);
    }
  }

  close() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    this.trigger('close', {});
  }

  private trigger(event: string, data: any) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }

  private simulateNotification() {
    const types: NotificationType[] = [
      'SYSTEM', 'ORDER', 'TRANSACTION', 'CREDIT', 
      'VERIFICATION', 'SUBSCRIPTION', 'PAYMENT', 'TEAM',
      'SECURITY', 'MARKETPLACE', 'WALLET'
    ];
    
    const priorities: NotificationPriority[] = [
      'LOW', 'NORMAL', 'HIGH', 'URGENT'
    ];
    
    const type = types[Math.floor(Math.random() * types.length)];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];
    
    const notifications = {
      SYSTEM: {
        title: 'System Update',
        message: 'The system will be undergoing maintenance in 24 hours.',
      },
      ORDER: {
        title: 'New Order',
        message: 'You have received a new order for your carbon credits.',
      },
      TRANSACTION: {
        title: 'Transaction Completed',
        message: 'Your transaction has been successfully processed.',
      },
      CREDIT: {
        title: 'Carbon Credit Update',
        message: 'Your carbon credit listing has been verified.',
      },
      VERIFICATION: {
        title: 'Verification Update',
        message: 'Your organization verification is in progress.',
      },
      SUBSCRIPTION: {
        title: 'Subscription Reminder',
        message: 'Your subscription will renew in 7 days.',
      },
      PAYMENT: {
        title: 'Payment Received',
        message: 'We have received your payment for the recent invoice.',
      },
      TEAM: {
        title: 'Team Update',
        message: 'A new member has joined your team.',
      },
      SECURITY: {
        title: 'Security Alert',
        message: 'A new device has logged into your account.',
      },
      MARKETPLACE: {
        title: 'Marketplace Update',
        message: 'New carbon credits are available on the marketplace.',
      },
      WALLET: {
        title: 'Wallet Update',
        message: 'Your wallet balance has been updated.',
      },
    };
    
    const notification = {
      id: `mock-${Date.now()}`,
      ...notifications[type],
      type,
      priority,
      read: false,
      createdAt: new Date().toISOString(),
      actionUrl: '/dashboard',
    };
    
    this.trigger('message', { data: JSON.stringify(notification) });
  }
}

export function RealTimeNotifications() {
  const router = useRouter();
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // In a real implementation, you would use a real WebSocket connection
    // const socket = new WebSocket('wss://your-api.com/notifications');
    const socket = new MockWebSocket('wss://example.com/notifications');
    
    const handleOpen = () => {
      console.log('WebSocket connected');
      setConnected(true);
    };
    
    const handleMessage = (event: any) => {
      try {
        const notification = JSON.parse(event.data);
        
        // Show toast notification
        toast({
          title: notification.title,
          description: notification.message,
          action: notification.actionUrl ? {
            label: 'View',
            onClick: () => router.push(notification.actionUrl),
          } : undefined,
        });
        
        // Play sound for high priority notifications
        if (notification.priority === 'HIGH' || notification.priority === 'URGENT') {
          const audio = new Audio('/sounds/notification.mp3');
          audio.play().catch(err => console.error('Failed to play notification sound:', err));
        }
      } catch (error) {
        console.error('Error processing notification:', error);
      }
    };
    
    const handleClose = () => {
      console.log('WebSocket disconnected');
      setConnected(false);
    };
    
    const handleError = (error: any) => {
      console.error('WebSocket error:', error);
      setConnected(false);
    };
    
    socket.addEventListener('open', handleOpen);
    socket.addEventListener('message', handleMessage);
    socket.addEventListener('close', handleClose);
    socket.addEventListener('error', handleError);
    
    // Clean up on unmount
    return () => {
      socket.removeEventListener('open', handleOpen);
      socket.removeEventListener('message', handleMessage);
      socket.removeEventListener('close', handleClose);
      socket.removeEventListener('error', handleError);
      socket.close();
    };
  }, [router]);

  return null; // This component doesn't render anything visible
}
