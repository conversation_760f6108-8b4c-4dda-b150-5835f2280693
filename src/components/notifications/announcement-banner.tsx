'use client';

import { useState, useEffect } from 'react';
import { Megaphone, X, ChevronDown, ChevronUp, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { cn } from '@/lib/utils';

interface Announcement {
  id: string;
  title: string;
  message: string;
  type: 'INFO' | 'WARNING' | 'CRITICAL' | 'SUCCESS';
  startDate: string;
  endDate: string;
  actionUrl?: string;
  actionLabel?: string;
  dismissible: boolean;
}

interface AnnouncementBannerProps {
  className?: string;
  maxHeight?: number;
}

export function AnnouncementBanner({ className, maxHeight = 400 }: AnnouncementBannerProps) {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [dismissedAnnouncements, setDismissedAnnouncements] = useLocalStorage<string[]>(
    'dismissed-announcements',
    []
  );

  // Fetch active announcements
  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/notifications/announcements');
        
        if (!response.ok) {
          throw new Error('Failed to fetch announcements');
        }
        
        const data = await response.json();
        setAnnouncements(data.announcements);
      } catch (error) {
        console.error('Error fetching announcements:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAnnouncements();
    
    // Refresh announcements every 5 minutes
    const interval = setInterval(fetchAnnouncements, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Filter out dismissed announcements
  const activeAnnouncements = announcements.filter(
    (announcement) => !dismissedAnnouncements.includes(announcement.id)
  );

  // Handle announcement dismissal
  const dismissAnnouncement = (id: string) => {
    setDismissedAnnouncements([...dismissedAnnouncements, id]);
  };

  // Get background color based on announcement type
  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'INFO':
        return 'bg-blue-50 dark:bg-blue-950';
      case 'WARNING':
        return 'bg-amber-50 dark:bg-amber-950';
      case 'CRITICAL':
        return 'bg-red-50 dark:bg-red-950';
      case 'SUCCESS':
        return 'bg-green-50 dark:bg-green-950';
      default:
        return 'bg-muted';
    }
  };

  // Get badge variant based on announcement type
  const getBadgeVariant = (type: string): 'default' | 'destructive' | 'outline' | 'secondary' | 'success' => {
    switch (type) {
      case 'INFO':
        return 'default';
      case 'WARNING':
        return 'secondary';
      case 'CRITICAL':
        return 'destructive';
      case 'SUCCESS':
        return 'success';
      default:
        return 'outline';
    }
  };

  if (isLoading || activeAnnouncements.length === 0) {
    return null;
  }

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={cn('w-full', className)}
    >
      <Card className={cn(
        'border-0 rounded-none shadow-none',
        getBackgroundColor(activeAnnouncements[0].type)
      )}>
        <div className="container py-2 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Megaphone className="h-5 w-5" />
              <span className="font-medium">{activeAnnouncements[0].title}</span>
              <Badge variant={getBadgeVariant(activeAnnouncements[0].type)}>
                {activeAnnouncements[0].type}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  {isOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                  <span className="sr-only">Toggle announcement</span>
                </Button>
              </CollapsibleTrigger>
              {activeAnnouncements[0].dismissible && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => dismissAnnouncement(activeAnnouncements[0].id)}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Dismiss</span>
                </Button>
              )}
            </div>
          </div>
          <CollapsibleContent className="pt-2">
            <div 
              className="prose prose-sm dark:prose-invert max-w-none"
              style={{ maxHeight: `${maxHeight}px`, overflowY: 'auto' }}
              dangerouslySetInnerHTML={{ __html: activeAnnouncements[0].message }}
            />
            {activeAnnouncements[0].actionUrl && (
              <div className="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                >
                  <a 
                    href={activeAnnouncements[0].actionUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center"
                  >
                    {activeAnnouncements[0].actionLabel || 'Learn More'}
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </Button>
              </div>
            )}
            {activeAnnouncements.length > 1 && (
              <div className="mt-2 pt-2 border-t">
                <p className="text-sm font-medium">
                  +{activeAnnouncements.length - 1} more announcement{activeAnnouncements.length > 2 ? 's' : ''}
                </p>
              </div>
            )}
          </CollapsibleContent>
        </div>
      </Card>
    </Collapsible>
  );
}
