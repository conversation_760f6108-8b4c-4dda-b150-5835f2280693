'use client';

import { useState, useEffect } from 'react';
import { NotificationType } from '@prisma/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

interface NotificationPreference {
  id: string;
  email: boolean;
  push: boolean;
  inApp: boolean;
  types: Record<NotificationType, boolean>;
  userId: string;
}

const notificationTypeLabels: Record<NotificationType, string> = {
  SYSTEM: 'System notifications',
  ORDER: 'Order updates',
  TRANSACTION: 'Transaction updates',
  CREDIT: 'Carbon credit updates',
  VERIFICATION: 'Verification updates',
  SUBSCRIPTION: 'Subscription updates',
  PAYMENT: 'Payment notifications',
  TEAM: 'Team updates',
  SECURITY: 'Security alerts',
  MARKETPLACE: 'Marketplace updates',
  WALLET: 'Wallet updates',
};

const notificationTypeDescriptions: Record<NotificationType, string> = {
  SYSTEM: 'Important system announcements and updates',
  ORDER: 'Updates about your buy and sell orders',
  TRANSACTION: 'Updates about your transactions',
  CREDIT: 'Updates about your carbon credits',
  VERIFICATION: 'Updates about verification processes',
  SUBSCRIPTION: 'Updates about your subscription',
  PAYMENT: 'Billing and payment notifications',
  TEAM: 'Updates about your team members',
  SECURITY: 'Security alerts and notifications',
  MARKETPLACE: 'Updates about marketplace activity',
  WALLET: 'Updates about your wallet',
};

export function EmailPreferences() {
  const [preferences, setPreferences] = useState<NotificationPreference | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Fetch notification preferences
  const fetchPreferences = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications/preferences');
      if (!response.ok) throw new Error('Failed to fetch notification preferences');
      
      const data = await response.json();
      setPreferences(data);
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      toast({
        title: 'Error',
        description: 'Failed to load notification preferences',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Save notification preferences
  const savePreferences = async () => {
    if (!preferences) return;
    
    try {
      setSaving(true);
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });
      
      if (!response.ok) throw new Error('Failed to save notification preferences');
      
      toast({
        title: 'Success',
        description: 'Notification preferences saved successfully',
      });
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast({
        title: 'Error',
        description: 'Failed to save notification preferences',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Toggle global preference
  const toggleGlobalPreference = (key: 'email' | 'push' | 'inApp') => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      [key]: !preferences[key],
    });
  };

  // Toggle notification type preference
  const toggleTypePreference = (type: NotificationType) => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      types: {
        ...preferences.types,
        [type]: !preferences.types?.[type],
      },
    });
  };

  // Load preferences on mount
  useEffect(() => {
    fetchPreferences();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Notification Preferences</CardTitle>
          <CardDescription>Manage how you receive notifications</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </CardContent>
      </Card>
    );
  }

  if (!preferences) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Notification Preferences</CardTitle>
          <CardDescription>Manage how you receive notifications</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-gray-500">Failed to load notification preferences</p>
          <Button
            className="mt-4 mx-auto block"
            onClick={fetchPreferences}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>Manage how you receive notifications</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Global notification settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Notification Channels</h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications" className="font-medium">
                    Email Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Receive notifications via email
                  </p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={preferences.email}
                  onCheckedChange={() => toggleGlobalPreference('email')}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-notifications" className="font-medium">
                    Push Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Receive push notifications in your browser
                  </p>
                </div>
                <Switch
                  id="push-notifications"
                  checked={preferences.push}
                  onCheckedChange={() => toggleGlobalPreference('push')}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="in-app-notifications" className="font-medium">
                    In-App Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Receive notifications within the application
                  </p>
                </div>
                <Switch
                  id="in-app-notifications"
                  checked={preferences.inApp}
                  onCheckedChange={() => toggleGlobalPreference('inApp')}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Notification types */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Notification Types</h3>
            <div className="grid gap-4">
              {Object.entries(notificationTypeLabels).map(([type, label]) => (
                <div key={type} className="flex items-center justify-between">
                  <div>
                    <Label htmlFor={`notification-${type}`} className="font-medium">
                      {label}
                    </Label>
                    <p className="text-sm text-gray-500">
                      {notificationTypeDescriptions[type as NotificationType]}
                    </p>
                  </div>
                  <Switch
                    id={`notification-${type}`}
                    checked={preferences.types?.[type as NotificationType] ?? true}
                    onCheckedChange={() => toggleTypePreference(type as NotificationType)}
                  />
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={savePreferences}
              disabled={saving}
            >
              {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Preferences
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
