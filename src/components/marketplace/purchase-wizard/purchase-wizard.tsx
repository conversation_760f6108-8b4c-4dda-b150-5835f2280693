"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  ArrowRight,
  Loader2,
  CheckCircle,
  AlertCircle,
  ShoppingCart,
  Wallet,
  Receipt,
  FileCheck
} from "lucide-react";
import { CarbonCredit } from "../enhanced-credit-card";
import { CreditInfoStep } from "./credit-info-step";
import { QuantityStep } from "./quantity-step";
import { ReviewStep } from "./review-step";
import { ConfirmationStep } from "./confirmation-step";

// Wizard steps
enum PurchaseStep {
  CREDIT_INFO = 1,
  QUANTITY = 2,
  REVIEW = 3,
  CONFIRMATION = 4,
}

export interface PurchaseWizardProps {
  credit: CarbonCredit;
  isOpen: boolean;
  onClose: () => void;
  onPurchase: (creditId: string, quantity: number) => Promise<boolean>;
}

export function PurchaseWizard({
  credit,
  isOpen,
  onClose,
  onPurchase,
}: PurchaseWizardProps) {
  const [step, setStep] = useState<PurchaseStep>(PurchaseStep.CREDIT_INFO);
  const [quantity, setQuantity] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Calculate total price
  const totalPrice = credit.price * quantity;

  // Calculate fees (simplified example)
  const platformFee = totalPrice * 0.01; // 1% platform fee
  const gasFee = 0.05; // Fixed gas fee in INR
  const totalFees = platformFee + gasFee;

  // Calculate final total
  const finalTotal = totalPrice + totalFees;

  // Calculate progress
  const totalSteps = 4;
  const progress = (step / totalSteps) * 100;

  // Get step title
  const getStepTitle = (currentStep: PurchaseStep): string => {
    switch (currentStep) {
      case PurchaseStep.CREDIT_INFO:
        return "Credit Information";
      case PurchaseStep.QUANTITY:
        return "Select Quantity";
      case PurchaseStep.REVIEW:
        return "Review Order";
      case PurchaseStep.CONFIRMATION:
        return "Confirm Purchase";
      default:
        return "";
    }
  };

  // Get step icon
  const getStepIcon = (currentStep: PurchaseStep) => {
    switch (currentStep) {
      case PurchaseStep.CREDIT_INFO:
        return <ShoppingCart className="h-5 w-5" />;
      case PurchaseStep.QUANTITY:
        return <Receipt className="h-5 w-5" />;
      case PurchaseStep.REVIEW:
        return <FileCheck className="h-5 w-5" />;
      case PurchaseStep.CONFIRMATION:
        return <Wallet className="h-5 w-5" />;
      default:
        return null;
    }
  };

  // Handle next step
  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  // Handle previous step
  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  // Handle quantity change
  const handleQuantityChange = (newQuantity: number) => {
    setQuantity(newQuantity);
  };

  // Handle purchase
  const handlePurchase = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const result = await onPurchase(credit.id, quantity);

      if (result) {
        setSuccess(true);
        setStep(PurchaseStep.CONFIRMATION);
      } else {
        throw new Error("Purchase failed. Please try again.");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    // Reset state when dialog is closed
    setStep(PurchaseStep.CREDIT_INFO);
    setQuantity(1);
    setError(null);
    setSuccess(false);
    onClose();
  };

  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case PurchaseStep.CREDIT_INFO:
        return <CreditInfoStep credit={credit} />;

      case PurchaseStep.QUANTITY:
        return (
          <QuantityStep
            credit={credit}
            quantity={quantity}
            onChange={handleQuantityChange}
          />
        );

      case PurchaseStep.REVIEW:
        return (
          <ReviewStep
            credit={credit}
            quantity={quantity}
            totalPrice={totalPrice}
            platformFee={platformFee}
            gasFee={gasFee}
            totalFees={totalFees}
            finalTotal={finalTotal}
          />
        );

      case PurchaseStep.CONFIRMATION:
        return (
          <ConfirmationStep
            credit={credit}
            quantity={quantity}
            totalPrice={finalTotal}
            success={success}
            error={error}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            {getStepIcon(step)}
            <DialogTitle>{getStepTitle(step)}</DialogTitle>
          </div>
          <DialogDescription>
            {step === PurchaseStep.CREDIT_INFO && "Review the carbon credit details"}
            {step === PurchaseStep.QUANTITY && "Select the quantity you want to purchase"}
            {step === PurchaseStep.REVIEW && "Review your order before confirming"}
            {step === PurchaseStep.CONFIRMATION && (success ? "Your purchase was successful" : "Confirming your purchase")}
          </DialogDescription>
        </DialogHeader>

        {step !== PurchaseStep.CONFIRMATION && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Step {step} of {totalSteps}</span>
              <span className="text-sm text-muted-foreground">
                {getStepTitle(step)}
              </span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        <div className="py-2">
          {renderStepContent()}
        </div>

        {error && step !== PurchaseStep.CONFIRMATION && (
          <div className="mt-2 p-3 rounded-md bg-red-50 text-red-800 dark:bg-red-950/50 dark:text-red-300">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{error}</p>
            </div>
          </div>
        )}

        <DialogFooter>
          {step === PurchaseStep.CONFIRMATION ? (
            <Button onClick={handleClose}>
              {success ? "Done" : "Close"}
            </Button>
          ) : (
            <div className="flex justify-between w-full">
              <Button
                variant="outline"
                onClick={step === PurchaseStep.CREDIT_INFO ? handleClose : prevStep}
              >
                {step === PurchaseStep.CREDIT_INFO ? (
                  "Cancel"
                ) : (
                  <>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </>
                )}
              </Button>

              <Button
                onClick={step === PurchaseStep.REVIEW ? handlePurchase : nextStep}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : step === PurchaseStep.REVIEW ? (
                  "Confirm Purchase"
                ) : (
                  <>
                    Continue
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
