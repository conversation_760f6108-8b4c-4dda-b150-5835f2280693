"use client";

import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import { 
  Globe, 
  Calendar, 
  Tag, 
  Trees, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Shield
} from "lucide-react";
import { CarbonCredit } from "../enhanced-credit-card";

interface CreditInfoStepProps {
  credit: CarbonCredit;
}

export function CreditInfoStep({ credit }: CreditInfoStepProps) {
  // Get verification status badge
  const getVerificationBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return (
          <Badge variant="success" className="flex items-center">
            <CheckCircle className="mr-1 h-3 w-3" />
            Verified
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="warning" className="flex items-center">
            <AlertCircle className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "UNVERIFIED":
        return (
          <Badge variant="outline" className="flex items-center">
            <Info className="mr-1 h-3 w-3" />
            Unverified
          </Badge>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">{credit.name}</h3>
        {getVerificationBadge(credit.verificationStatus)}
      </div>
      
      <div className="aspect-[4/3] relative bg-muted rounded-md overflow-hidden">
        {credit.image ? (
          <Image
            src={credit.image}
            alt={credit.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <Trees className="h-12 w-12 text-muted-foreground" />
          </div>
        )}
      </div>
      
      <p className="text-sm">{credit.description}</p>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-1">
          <div className="flex items-center text-sm text-muted-foreground">
            <Tag className="h-4 w-4 mr-1" />
            Standard
            <InfoTooltip 
              content="The verification standard that certified this carbon credit"
              className="ml-1"
            />
          </div>
          <p className="font-medium">{credit.standard}</p>
        </div>
        
        <div className="space-y-1">
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="h-4 w-4 mr-1" />
            Vintage
            <InfoTooltip 
              content="The year when the carbon reduction or removal occurred"
              className="ml-1"
            />
          </div>
          <p className="font-medium">{credit.vintage}</p>
        </div>
        
        <div className="space-y-1">
          <div className="flex items-center text-sm text-muted-foreground">
            <Globe className="h-4 w-4 mr-1" />
            Location
          </div>
          <p className="font-medium">{credit.location}</p>
        </div>
        
        <div className="space-y-1">
          <div className="flex items-center text-sm text-muted-foreground">
            <Trees className="h-4 w-4 mr-1" />
            Project Type
          </div>
          <p className="font-medium">{credit.projectType}</p>
        </div>
      </div>
      
      <Separator />
      
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-green-500" />
            <span className="font-medium">Verification Status</span>
          </div>
          {getVerificationBadge(credit.verificationStatus)}
        </div>
        
        <p className="text-sm text-muted-foreground">
          {credit.verificationStatus === "VERIFIED" 
            ? "This carbon credit has been verified by an accredited third-party verifier and meets all quality standards."
            : credit.verificationStatus === "PENDING"
            ? "This carbon credit is currently undergoing verification. The process typically takes 2-4 weeks."
            : "This carbon credit has not been verified yet. Unverified credits carry additional risk."}
        </p>
      </div>
      
      <div className="flex justify-between items-center p-3 bg-muted rounded-md">
        <div>
          <p className="text-sm text-muted-foreground">Price per ton</p>
          <p className="text-xl font-bold">${credit.price.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Available</p>
          <p className="text-xl font-bold">{credit.available.toLocaleString()} tons</p>
        </div>
      </div>
    </div>
  );
}
