"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { 
  ChevronDown, 
  ChevronUp, 
  Info, 
  Wallet, 
  CreditCard, 
  Landmark, 
  Zap,
  AlertTriangle,
  Receipt,
  FileText,
  CheckCircle
} from "lucide-react";
import { CarbonCredit } from "../enhanced-credit-card";

interface ReviewStepProps {
  credit: CarbonCredit;
  quantity: number;
  totalPrice: number;
  platformFee: number;
  gasFee: number;
  totalFees: number;
  finalTotal: number;
}

export function ReviewStep({
  credit,
  quantity,
  totalPrice,
  platformFee,
  gasFee,
  totalFees,
  finalTotal,
}: ReviewStepProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium">{credit.name}</h3>
                <p className="text-sm text-muted-foreground">{credit.standard} • {credit.vintage}</p>
              </div>
              <Badge variant="outline">{credit.verificationStatus}</Badge>
            </div>
            
            <Separator />
            
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="text-sm text-muted-foreground">Price per ton</dt>
                <dd className="text-sm font-medium">${credit.price.toFixed(2)}</dd>
              </div>
              
              <div className="flex justify-between">
                <dt className="text-sm text-muted-foreground">Quantity</dt>
                <dd className="text-sm font-medium">{quantity} tons</dd>
              </div>
              
              <div className="flex justify-between">
                <dt className="text-sm text-muted-foreground">Subtotal</dt>
                <dd className="text-sm font-medium">${totalPrice.toFixed(2)}</dd>
              </div>
              
              <Separator />
              
              <div className="flex justify-between">
                <dt className="text-sm text-muted-foreground flex items-center">
                  Platform Fee
                  <InfoTooltip 
                    content="A 1% fee to support the platform's operations and development"
                    className="ml-1"
                  />
                </dt>
                <dd className="text-sm font-medium">${platformFee.toFixed(2)}</dd>
              </div>
              
              <div className="flex justify-between">
                <dt className="text-sm text-muted-foreground flex items-center">
                  Gas Fee
                  <InfoTooltip 
                    content="Network fee for processing the blockchain transaction"
                    className="ml-1"
                  />
                </dt>
                <dd className="text-sm font-medium">${gasFee.toFixed(2)}</dd>
              </div>
              
              <div className="flex justify-between">
                <dt className="text-sm text-muted-foreground">Total Fees</dt>
                <dd className="text-sm font-medium">${totalFees.toFixed(2)}</dd>
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-medium">
                <dt>Total</dt>
                <dd>${finalTotal.toFixed(2)}</dd>
              </div>
            </dl>
          </div>
        </CardContent>
      </Card>
      
      <Collapsible
        open={showDetails}
        onOpenChange={setShowDetails}
        className="border rounded-md"
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4">
          <div className="flex items-center">
            <Receipt className="h-5 w-5 mr-2" />
            <span className="font-medium">Purchase Details</span>
          </div>
          {showDetails ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </CollapsibleTrigger>
        <CollapsibleContent className="p-4 pt-0 border-t">
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Payment Method</h4>
              <div className="flex items-center space-x-2 p-3 bg-muted rounded-md">
                <Wallet className="h-5 w-5 text-primary" />
                <span>Connected Wallet (0x1234...5678)</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Transaction Details</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Transaction Type</span>
                  <span>Carbon Credit Purchase</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Network</span>
                  <span>Ethereum</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Gas Optimization</span>
                  <span>Standard</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Delivery</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Delivery Method</span>
                  <span>Wallet Transfer</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Estimated Delivery</span>
                  <span>Immediate after confirmation</span>
                </div>
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
      
      <div className="space-y-4">
        <div className="flex items-start space-x-2">
          <Checkbox 
            id="terms" 
            checked={termsAccepted}
            onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
          />
          <div className="space-y-1">
            <Label
              htmlFor="terms"
              className="text-sm font-medium"
            >
              I agree to the Terms of Purchase
            </Label>
            <p className="text-xs text-muted-foreground">
              By checking this box, you agree to our <a href="#" className="text-primary hover:underline">Terms of Service</a> and acknowledge that carbon credit purchases are final and non-refundable.
            </p>
          </div>
        </div>
        
        <div className="flex items-start p-3 bg-blue-50 text-blue-800 dark:bg-blue-950/50 dark:text-blue-300 rounded-md">
          <Info className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <p className="font-medium">Purchase Information</p>
            <p>After purchase, your carbon credits will be transferred to your wallet. You'll receive a certificate and can view your credits in your portfolio.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
