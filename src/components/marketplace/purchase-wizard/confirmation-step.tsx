"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  ExternalLink, 
  Copy, 
  Download,
  Share2,
  ArrowRight
} from "lucide-react";
import { CarbonCredit } from "../enhanced-credit-card";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";

interface ConfirmationStepProps {
  credit: CarbonCredit;
  quantity: number;
  totalPrice: number;
  success: boolean;
  error: string | null;
}

export function ConfirmationStep({
  credit,
  quantity,
  totalPrice,
  success,
  error,
}: ConfirmationStepProps) {
  const router = useRouter();
  
  // Mock transaction hash
  const transactionHash = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
  
  // Handle copy transaction hash
  const copyTransactionHash = () => {
    navigator.clipboard.writeText(transactionHash);
    // Would add toast notification in a real implementation
  };
  
  // Handle view portfolio
  const handleViewPortfolio = () => {
    router.push("/portfolio");
  };
  
  // Handle view transaction
  const handleViewTransaction = () => {
    window.open(`https://etherscan.io/tx/${transactionHash}`, "_blank");
  };
  
  // Handle download certificate
  const handleDownloadCertificate = () => {
    // Would implement certificate download in a real implementation
  };
  
  return (
    <div className="space-y-6">
      {success ? (
        <div className="text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="flex justify-center mb-4"
          >
            <div className="p-4 bg-green-100 dark:bg-green-900/20 rounded-full">
              <CheckCircle className="h-12 w-12 text-green-500" />
            </div>
          </motion.div>
          
          <h3 className="text-xl font-medium">Purchase Successful!</h3>
          <p className="text-muted-foreground mt-1">
            You have successfully purchased {quantity} tons of carbon credits.
          </p>
        </div>
      ) : (
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-red-100 dark:bg-red-900/20 rounded-full">
              <XCircle className="h-12 w-12 text-red-500" />
            </div>
          </div>
          
          <h3 className="text-xl font-medium">Purchase Failed</h3>
          <p className="text-muted-foreground mt-1">
            {error || "There was an error processing your purchase. Please try again."}
          </p>
        </div>
      )}
      
      {success && (
        <>
          <div className="p-4 border rounded-md space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Credit</span>
              <span className="text-sm font-medium">{credit.name}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Quantity</span>
              <span className="text-sm font-medium">{quantity} tons</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Total Price</span>
              <span className="text-sm font-medium">${totalPrice.toFixed(2)}</span>
            </div>
            
            <Separator />
            
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Transaction Hash</span>
              <div className="flex items-center">
                <span className="text-sm font-mono mr-2">
                  {`${transactionHash.substring(0, 6)}...${transactionHash.substring(transactionHash.length - 4)}`}
                </span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6"
                  onClick={copyTransactionHash}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Status</span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-green-500 mr-1">Confirmed</span>
                <CheckCircle className="h-3 w-3 text-green-500" />
              </div>
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button onClick={handleViewPortfolio}>
              View in Portfolio
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            
            <Button variant="outline" onClick={handleViewTransaction}>
              View Transaction
              <ExternalLink className="ml-2 h-4 w-4" />
            </Button>
            
            <Button variant="outline" onClick={handleDownloadCertificate}>
              Download Certificate
              <Download className="ml-2 h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-start p-3 bg-blue-50 text-blue-800 dark:bg-blue-950/50 dark:text-blue-300 rounded-md">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium">Next Steps</p>
              <p>Your carbon credits are now in your portfolio. You can retire them to offset your emissions or hold them as an investment.</p>
            </div>
          </div>
        </>
      )}
      
      {!success && (
        <div className="space-y-4">
          <div className="p-4 border rounded-md space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Error Type</span>
              <span className="text-sm font-medium">Transaction Failed</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Attempted Purchase</span>
              <span className="text-sm font-medium">{quantity} tons of {credit.name}</span>
            </div>
            
            <Separator />
            
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Status</span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-red-500 mr-1">Failed</span>
                <XCircle className="h-3 w-3 text-red-500" />
              </div>
            </div>
          </div>
          
          <div className="flex items-start p-3 bg-yellow-50 text-yellow-800 dark:bg-yellow-950/50 dark:text-yellow-300 rounded-md">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium">Troubleshooting</p>
              <p>Please check your wallet connection and ensure you have sufficient funds. If the problem persists, contact our support team.</p>
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
            
            <Button variant="outline" onClick={() => router.push("/support")}>
              Contact Support
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
