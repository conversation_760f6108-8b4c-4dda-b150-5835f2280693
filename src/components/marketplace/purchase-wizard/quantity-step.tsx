"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>lider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import { 
  Plus, 
  Minus, 
  Info, 
  AlertTriangle, 
  Leaf, 
  TreePine,
  Car,
  Plane,
  Home
} from "lucide-react";
import { CarbonCredit } from "../enhanced-credit-card";

interface QuantityStepProps {
  credit: CarbonCredit;
  quantity: number;
  onChange: (quantity: number) => void;
}

export function QuantityStep({ credit, quantity, onChange }: QuantityStepProps) {
  const [inputValue, setInputValue] = useState(quantity.toString());
  
  // Calculate total price
  const totalPrice = credit.price * quantity;
  
  // Calculate carbon offset equivalents (simplified examples)
  const flightEquivalent = quantity * 0.5; // 1 ton offsets 0.5 long-haul flights
  const carEquivalent = quantity * 4; // 1 ton offsets 4 months of driving
  const homeEquivalent = quantity * 0.25; // 1 ton offsets 0.25 homes' monthly energy
  
  // Handle quantity change
  const handleQuantityChange = (newQuantity: number) => {
    // Ensure quantity is within valid range
    const validQuantity = Math.max(1, Math.min(newQuantity, credit.available));
    onChange(validQuantity);
    setInputValue(validQuantity.toString());
  };
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    
    const newValue = parseInt(e.target.value);
    if (!isNaN(newValue) && newValue > 0) {
      handleQuantityChange(newValue);
    }
  };
  
  // Handle slider change
  const handleSliderChange = (value: number[]) => {
    handleQuantityChange(value[0]);
  };
  
  // Handle increment/decrement
  const increment = () => handleQuantityChange(quantity + 1);
  const decrement = () => handleQuantityChange(quantity - 1);
  
  // Get recommended quantities
  const getRecommendedQuantities = () => {
    const max = Math.min(credit.available, 1000);
    
    return [
      { value: 1, label: "Minimum" },
      { value: 5, label: "Small" },
      { value: 10, label: "Medium" },
      { value: 50, label: "Large" },
      { value: 100, label: "Extra Large" },
      { value: max, label: "Maximum" },
    ].filter(q => q.value <= max);
  };
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-medium flex items-center">
            Select Quantity
            <InfoTooltip 
              content="Each carbon credit represents one ton of CO2 equivalent that has been reduced or removed from the atmosphere."
              className="ml-1"
            />
          </h3>
          <Badge variant="outline">
            {credit.available.toLocaleString()} available
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={decrement}
            disabled={quantity <= 1}
          >
            <Minus className="h-4 w-4" />
          </Button>
          
          <Input
            type="number"
            value={inputValue}
            onChange={handleInputChange}
            min={1}
            max={credit.available}
            className="text-center"
          />
          
          <Button
            variant="outline"
            size="icon"
            onClick={increment}
            disabled={quantity >= credit.available}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="pt-2">
          <Slider
            value={[quantity]}
            min={1}
            max={Math.min(credit.available, 100)}
            step={1}
            onValueChange={handleSliderChange}
          />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>1 ton</span>
            <span>{Math.min(credit.available, 100)} tons</span>
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Recommended Quantities</h4>
        <div className="flex flex-wrap gap-2">
          {getRecommendedQuantities().map((rec) => (
            <Button
              key={rec.value}
              variant={quantity === rec.value ? "default" : "outline"}
              size="sm"
              onClick={() => handleQuantityChange(rec.value)}
            >
              {rec.value} ({rec.label})
            </Button>
          ))}
        </div>
      </div>
      
      <div className="p-4 bg-muted rounded-md space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-muted-foreground">Total Price</p>
            <p className="text-xl font-bold">${totalPrice.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Quantity</p>
            <p className="text-xl font-bold">{quantity} tons</p>
          </div>
        </div>
        
        <div className="space-y-2">
          <h4 className="text-sm font-medium flex items-center">
            <Leaf className="mr-1 h-4 w-4 text-green-500" />
            Environmental Impact
          </h4>
          <div className="grid grid-cols-3 gap-2">
            <div className="p-2 bg-background rounded-md text-center">
              <div className="flex justify-center mb-1">
                <Plane className="h-5 w-5 text-blue-500" />
              </div>
              <p className="text-xs text-muted-foreground">Flights Offset</p>
              <p className="font-medium">{flightEquivalent.toFixed(1)}</p>
            </div>
            
            <div className="p-2 bg-background rounded-md text-center">
              <div className="flex justify-center mb-1">
                <Car className="h-5 w-5 text-orange-500" />
              </div>
              <p className="text-xs text-muted-foreground">Driving (months)</p>
              <p className="font-medium">{carEquivalent.toFixed(1)}</p>
            </div>
            
            <div className="p-2 bg-background rounded-md text-center">
              <div className="flex justify-center mb-1">
                <Home className="h-5 w-5 text-purple-500" />
              </div>
              <p className="text-xs text-muted-foreground">Homes (monthly)</p>
              <p className="font-medium">{homeEquivalent.toFixed(1)}</p>
            </div>
          </div>
        </div>
      </div>
      
      {quantity > 50 && (
        <div className="flex items-start p-3 bg-yellow-50 text-yellow-800 dark:bg-yellow-950/50 dark:text-yellow-300 rounded-md">
          <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <p className="font-medium">Large Purchase</p>
            <p>You're purchasing a significant amount of carbon credits. For purchases over 100 tons, consider contacting our sales team for potential volume discounts.</p>
          </div>
        </div>
      )}
    </div>
  );
}
