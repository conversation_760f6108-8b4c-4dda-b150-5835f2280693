"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronDown,
  ChevronUp,
  X,
  Filter,
  Search,
  Sliders,
  Tag,
  Calendar,
  Globe,
  Trees,
  Leaf,
  Check,
} from "lucide-react";
import { AnimatedButton } from "@/components/ui/animated";

// Filter types
export interface FilterState {
  search?: string;
  standard?: string[];
  priceRange?: [number, number];
  vintage?: string[];
  location?: string[];
  methodology?: string[];
  verified?: boolean;
  available?: boolean;
}

interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

interface ProgressiveFiltersProps {
  initialFilters?: FilterState;
  standards: FilterOption[];
  vintages: FilterOption[];
  locations: FilterOption[];
  methodologies: FilterOption[];
  onFilterChange: (filters: FilterState) => void;
}

export function ProgressiveFilters({
  initialFilters = {},
  standards,
  vintages,
  locations,
  methodologies,
  onFilterChange,
}: ProgressiveFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [showAllFilters, setShowAllFilters] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>(
    initialFilters.priceRange || [0, 100]
  );
  
  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search && filters.search.trim()) count++;
    if (filters.standard && filters.standard.length > 0) count++;
    if (filters.priceRange && (filters.priceRange[0] > 0 || filters.priceRange[1] < 100)) count++;
    if (filters.vintage && filters.vintage.length > 0) count++;
    if (filters.location && filters.location.length > 0) count++;
    if (filters.methodology && filters.methodology.length > 0) count++;
    if (filters.verified) count++;
    if (filters.available) count++;
    return count;
  };
  
  // Apply filter
  const applyFilter = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };
  
  // Clear filter
  const clearFilter = (key: string) => {
    const newFilters = { ...filters };
    delete newFilters[key as keyof FilterState];
    setFilters(newFilters);
    onFilterChange(newFilters);
  };
  
  // Clear all filters
  const clearAllFilters = () => {
    setFilters({});
    onFilterChange({});
  };
  
  // Handle search
  const handleSearch = (value: string) => {
    applyFilter("search", value);
  };
  
  // Handle price range change
  const handlePriceRangeChange = (value: [number, number]) => {
    setPriceRange(value);
  };
  
  // Apply price range
  const applyPriceRange = () => {
    applyFilter("priceRange", priceRange);
    setActiveFilter(null);
  };
  
  // Toggle filter in array
  const toggleArrayFilter = (key: string, value: string) => {
    const currentValues = filters[key as keyof FilterState] as string[] || [];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    applyFilter(key, newValues.length > 0 ? newValues : undefined);
  };
  
  // Toggle boolean filter
  const toggleBooleanFilter = (key: string) => {
    applyFilter(key, !filters[key as keyof FilterState]);
  };
  
  // Get filter chip label
  const getFilterChipLabel = (key: string): string | undefined => {
    switch (key) {
      case "search":
        return filters.search;
      case "standard":
        return filters.standard && filters.standard.length > 0
          ? `${filters.standard.length} selected`
          : undefined;
      case "priceRange":
        return filters.priceRange
          ? `$${filters.priceRange[0]}-$${filters.priceRange[1]}`
          : undefined;
      case "vintage":
        return filters.vintage && filters.vintage.length > 0
          ? `${filters.vintage.length} selected`
          : undefined;
      case "location":
        return filters.location && filters.location.length > 0
          ? `${filters.location.length} selected`
          : undefined;
      case "methodology":
        return filters.methodology && filters.methodology.length > 0
          ? `${filters.methodology.length} selected`
          : undefined;
      case "verified":
        return filters.verified ? "Verified" : undefined;
      case "available":
        return filters.available ? "Available" : undefined;
      default:
        return undefined;
    }
  };
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center">
          <Filter className="mr-2 h-4 w-4" />
          Filters
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary" className="ml-2">
              {getActiveFilterCount()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search carbon credits..."
            className="pl-8"
            value={filters.search || ""}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        
        {/* Quick Filter Chips */}
        <div className="flex flex-wrap gap-2">
          <FilterChip
            label="Standard"
            value={getFilterChipLabel("standard")}
            onClick={() => setActiveFilter(activeFilter === "standard" ? null : "standard")}
            active={activeFilter === "standard"}
            onClear={
              filters.standard ? () => clearFilter("standard") : undefined
            }
          />
          
          <FilterChip
            label="Price"
            value={getFilterChipLabel("priceRange")}
            onClick={() => setActiveFilter(activeFilter === "price" ? null : "price")}
            active={activeFilter === "price"}
            onClear={
              filters.priceRange ? () => clearFilter("priceRange") : undefined
            }
          />
          
          <FilterChip
            label="Vintage"
            value={getFilterChipLabel("vintage")}
            onClick={() => setActiveFilter(activeFilter === "vintage" ? null : "vintage")}
            active={activeFilter === "vintage"}
            onClear={
              filters.vintage ? () => clearFilter("vintage") : undefined
            }
          />
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAllFilters(!showAllFilters)}
            className="h-8 gap-1 rounded-full"
          >
            <Sliders className="h-3.5 w-3.5" />
            {showAllFilters ? "Less filters" : "More filters"}
            <ChevronDown
              className={`h-3.5 w-3.5 transition-transform ${
                showAllFilters ? "rotate-180" : ""
              }`}
            />
          </Button>
          
          {getActiveFilterCount() > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="h-8 gap-1 rounded-full text-muted-foreground hover:text-foreground"
            >
              <X className="h-3.5 w-3.5" />
              Clear all
            </Button>
          )}
        </div>
        
        {/* Active Filter Panel */}
        {activeFilter && (
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm flex items-center">
                  {activeFilter === "standard" && (
                    <Tag className="mr-2 h-4 w-4" />
                  )}
                  {activeFilter === "price" && (
                    <Sliders className="mr-2 h-4 w-4" />
                  )}
                  {activeFilter === "vintage" && (
                    <Calendar className="mr-2 h-4 w-4" />
                  )}
                  {activeFilter === "location" && (
                    <Globe className="mr-2 h-4 w-4" />
                  )}
                  {activeFilter === "methodology" && (
                    <Trees className="mr-2 h-4 w-4" />
                  )}
                  {activeFilter.charAt(0).toUpperCase() + activeFilter.slice(1)} Filter
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={() => setActiveFilter(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {activeFilter === "standard" && (
                <div className="space-y-3">
                  {standards.map((standard) => (
                    <div key={standard.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`standard-${standard.value}`}
                        checked={(filters.standard || []).includes(standard.value)}
                        onCheckedChange={() => toggleArrayFilter("standard", standard.value)}
                      />
                      <Label
                        htmlFor={`standard-${standard.value}`}
                        className="flex items-center justify-between flex-1 text-sm"
                      >
                        <span>{standard.label}</span>
                        {standard.count !== undefined && (
                          <Badge variant="outline" className="ml-auto">
                            {standard.count}
                          </Badge>
                        )}
                      </Label>
                    </div>
                  ))}
                </div>
              )}
              
              {activeFilter === "price" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Price Range</span>
                      <span className="text-sm">
                        ${priceRange[0]} - ${priceRange[1]}
                      </span>
                    </div>
                    <Slider
                      value={priceRange}
                      min={0}
                      max={100}
                      step={1}
                      onValueChange={(value) => handlePriceRangeChange(value as [number, number])}
                      className="py-2"
                    />
                  </div>
                  <Button onClick={applyPriceRange} className="w-full">
                    Apply Price Range
                  </Button>
                </div>
              )}
              
              {activeFilter === "vintage" && (
                <div className="space-y-3">
                  {vintages.map((vintage) => (
                    <div key={vintage.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`vintage-${vintage.value}`}
                        checked={(filters.vintage || []).includes(vintage.value)}
                        onCheckedChange={() => toggleArrayFilter("vintage", vintage.value)}
                      />
                      <Label
                        htmlFor={`vintage-${vintage.value}`}
                        className="flex items-center justify-between flex-1 text-sm"
                      >
                        <span>{vintage.label}</span>
                        {vintage.count !== undefined && (
                          <Badge variant="outline" className="ml-auto">
                            {vintage.count}
                          </Badge>
                        )}
                      </Label>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
        
        {/* Additional Filters */}
        {showAllFilters && (
          <Collapsible
            open={showAllFilters}
            className="space-y-4 pt-2"
          >
            <CollapsibleContent className="space-y-4">
              <Separator />
              
              {/* Location Filter */}
              <div className="space-y-2">
                <div
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => setActiveFilter(activeFilter === "location" ? null : "location")}
                >
                  <Label className="text-sm font-medium flex items-center">
                    <Globe className="mr-2 h-4 w-4" />
                    Location
                  </Label>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform ${
                      activeFilter === "location" ? "rotate-180" : ""
                    }`}
                  />
                </div>
                
                {activeFilter === "location" && (
                  <div className="space-y-3 pt-2">
                    {locations.map((location) => (
                      <div key={location.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`location-${location.value}`}
                          checked={(filters.location || []).includes(location.value)}
                          onCheckedChange={() => toggleArrayFilter("location", location.value)}
                        />
                        <Label
                          htmlFor={`location-${location.value}`}
                          className="flex items-center justify-between flex-1 text-sm"
                        >
                          <span>{location.label}</span>
                          {location.count !== undefined && (
                            <Badge variant="outline" className="ml-auto">
                              {location.count}
                            </Badge>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Methodology Filter */}
              <div className="space-y-2">
                <div
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => setActiveFilter(activeFilter === "methodology" ? null : "methodology")}
                >
                  <Label className="text-sm font-medium flex items-center">
                    <Trees className="mr-2 h-4 w-4" />
                    Methodology
                  </Label>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform ${
                      activeFilter === "methodology" ? "rotate-180" : ""
                    }`}
                  />
                </div>
                
                {activeFilter === "methodology" && (
                  <div className="space-y-3 pt-2">
                    {methodologies.map((methodology) => (
                      <div key={methodology.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`methodology-${methodology.value}`}
                          checked={(filters.methodology || []).includes(methodology.value)}
                          onCheckedChange={() => toggleArrayFilter("methodology", methodology.value)}
                        />
                        <Label
                          htmlFor={`methodology-${methodology.value}`}
                          className="flex items-center justify-between flex-1 text-sm"
                        >
                          <span>{methodology.label}</span>
                          {methodology.count !== undefined && (
                            <Badge variant="outline" className="ml-auto">
                              {methodology.count}
                            </Badge>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Boolean Filters */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="verified"
                    checked={filters.verified || false}
                    onCheckedChange={() => toggleBooleanFilter("verified")}
                  />
                  <Label
                    htmlFor="verified"
                    className="flex items-center text-sm"
                  >
                    <Leaf className="mr-2 h-4 w-4 text-green-500" />
                    Verified Projects Only
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="available"
                    checked={filters.available || false}
                    onCheckedChange={() => toggleBooleanFilter("available")}
                  />
                  <Label
                    htmlFor="available"
                    className="flex items-center text-sm"
                  >
                    <Check className="mr-2 h-4 w-4 text-blue-500" />
                    Available for Purchase
                  </Label>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </CardContent>
    </Card>
  );
}

interface FilterChipProps {
  label: string;
  value?: string;
  onClick: () => void;
  active?: boolean;
  onClear?: () => void;
}

function FilterChip({ label, value, onClick, active, onClear }: FilterChipProps) {
  return (
    <Button
      variant={active ? "default" : value ? "secondary" : "outline"}
      size="sm"
      onClick={onClick}
      className="h-8 gap-1 rounded-full"
    >
      {label}
      {value && (
        <>
          <Separator orientation="vertical" className="h-4 mx-0.5" />
          <span className="max-w-24 truncate text-xs">{value}</span>
          {onClear && (
            <X
              className="h-3 w-3 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onClear();
              }}
            />
          )}
        </>
      )}
    </Button>
  );
}
