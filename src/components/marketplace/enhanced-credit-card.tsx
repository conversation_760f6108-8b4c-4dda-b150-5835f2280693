"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Leaf, 
  Globe, 
  Calendar, 
  Tag, 
  Trees, 
  Info, 
  ShoppingCart, 
  ExternalLink,
  Star,
  StarHalf,
  Shield,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { AnimatedCard, AnimatedCardContent, AnimatedCardHeader } from "@/components/ui/animated";
import { motion } from "framer-motion";

// Carbon credit type
export interface CarbonCredit {
  id: string;
  name: string;
  description: string;
  price: number;
  available: number;
  vintage: string;
  standard: string;
  methodology: string;
  location: string;
  projectType: string;
  verificationStatus: "VERIFIED" | "PENDING" | "UNVERIFIED";
  rating?: number;
  image?: string;
  addedDate: string;
  isNew?: boolean;
  isFeatured?: boolean;
  isPopular?: boolean;
}

interface EnhancedCreditCardProps {
  credit: CarbonCredit;
  onAddToCart?: (creditId: string, quantity: number) => void;
}

export function EnhancedCreditCard({ credit, onAddToCart }: EnhancedCreditCardProps) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);
  
  // Get methodology color
  const getMethodologyColor = (methodology: string): string => {
    const methodologyMap: Record<string, string> = {
      "RENEWABLE_ENERGY": "from-blue-500 to-cyan-500",
      "FORESTRY": "from-green-500 to-emerald-500",
      "AGRICULTURE": "from-yellow-500 to-amber-500",
      "WASTE_MANAGEMENT": "from-purple-500 to-pink-500",
      "ENERGY_EFFICIENCY": "from-orange-500 to-red-500",
      "TRANSPORTATION": "from-indigo-500 to-violet-500",
      "INDUSTRIAL": "from-gray-500 to-slate-500",
    };
    
    return methodologyMap[methodology] || "from-gray-500 to-slate-500";
  };
  
  // Get verification status badge
  const getVerificationBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return (
          <Badge variant="success" className="flex items-center">
            <CheckCircle className="mr-1 h-3 w-3" />
            Verified
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="warning" className="flex items-center">
            <AlertCircle className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "UNVERIFIED":
        return (
          <Badge variant="outline" className="flex items-center">
            <Info className="mr-1 h-3 w-3" />
            Unverified
          </Badge>
        );
      default:
        return null;
    }
  };
  
  // Get rating stars
  const getRatingStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={`full-${i}`} className="h-4 w-4 fill-yellow-400 text-yellow-400" />);
    }
    
    if (hasHalfStar) {
      stars.push(<StarHalf key="half" className="h-4 w-4 fill-yellow-400 text-yellow-400" />);
    }
    
    const emptyStars = 5 - stars.length;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="h-4 w-4 text-muted-foreground" />);
    }
    
    return stars;
  };
  
  // Handle card click
  const handleCardClick = () => {
    router.push(`/marketplace/${credit.id}`);
  };
  
  // Handle add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAddToCart) {
      onAddToCart(credit.id, 1);
    }
  };
  
  return (
    <AnimatedCard 
      className="overflow-hidden cursor-pointer group"
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      animationVariant="hoverLift"
    >
      <div className="relative">
        {/* Methodology color bar */}
        <div 
          className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${getMethodologyColor(credit.methodology)}`} 
        />
        
        {/* Credit image */}
        <div className="aspect-[4/3] relative bg-muted">
          {credit.image ? (
            <Image
              src={credit.image}
              alt={credit.name}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <Trees className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
          
          {/* Badges */}
          <div className="absolute top-2 right-2 flex flex-col gap-1">
            {credit.vintage && (
              <Badge variant="secondary" className="text-xs">
                {credit.vintage}
              </Badge>
            )}
            {credit.standard && (
              <Badge variant="outline" className="text-xs bg-white/80 dark:bg-black/80">
                {credit.standard}
              </Badge>
            )}
          </div>
          
          {/* Featured/New badges */}
          {(credit.isNew || credit.isFeatured || credit.isPopular) && (
            <div className="absolute top-2 left-2">
              {credit.isNew && (
                <Badge className="bg-blue-500 text-white">New</Badge>
              )}
              {credit.isFeatured && (
                <Badge className="bg-purple-500 text-white ml-1">Featured</Badge>
              )}
              {credit.isPopular && (
                <Badge className="bg-orange-500 text-white ml-1">Popular</Badge>
              )}
            </div>
          )}
        </div>
      </div>
      
      <AnimatedCardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base line-clamp-1">{credit.name}</CardTitle>
          {getVerificationBadge(credit.verificationStatus)}
        </div>
        <CardDescription className="line-clamp-2 mt-1">
          {credit.description}
        </CardDescription>
      </AnimatedCardHeader>
      
      <AnimatedCardContent className="pb-3">
        <div className="grid grid-cols-2 gap-y-2 mb-3">
          <div>
            <p className="text-xs text-muted-foreground">Price</p>
            <p className="font-medium">${credit.price.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Available</p>
            <p className="font-medium">{credit.available.toLocaleString()} tons</p>
          </div>
          
          <div className="col-span-2">
            <div className="flex items-center mt-1">
              {credit.rating && getRatingStars(credit.rating)}
            </div>
          </div>
        </div>
        
        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="p-1.5 rounded-full bg-muted">
                  <Globe className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">Location: {credit.location}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="p-1.5 rounded-full bg-muted">
                  <Trees className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">Project Type: {credit.projectType}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="p-1.5 rounded-full bg-muted">
                  <Tag className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">Standard: {credit.standard}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="ml-auto">
            <Button
              size="sm"
              className="h-8 px-3"
              onClick={handleAddToCart}
            >
              <ShoppingCart className="h-3.5 w-3.5 mr-1" />
              Add
            </Button>
          </div>
        </div>
      </AnimatedCardContent>
      
      {/* Hover overlay with quick actions */}
      <motion.div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center opacity-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        transition={{ duration: 0.2 }}
        style={{ pointerEvents: isHovered ? 'auto' : 'none' }}
      >
        <div className="space-y-3 text-center p-4">
          <h3 className="font-medium">{credit.name}</h3>
          <div className="flex justify-center gap-2">
            <Button onClick={handleCardClick}>
              View Details
            </Button>
            <Button variant="outline" onClick={handleAddToCart}>
              <ShoppingCart className="h-4 w-4 mr-1" />
              Add to Cart
            </Button>
          </div>
        </div>
      </motion.div>
    </AnimatedCard>
  );
}
