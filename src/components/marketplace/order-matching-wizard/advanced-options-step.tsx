"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Info, Zap, Lightbulb, Sparkles } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Schema for advanced options
const advancedOptionsSchema = z.object({
  adaptiveMatching: z.boolean().default(true),
  prioritizeOwnOrders: z.boolean().default(false),
  minimumMatchSize: z.number().min(0).default(0),
  notifyOnMatch: z.boolean().default(true),
  gasOptimization: z.enum(["STANDARD", "AGGRESSIVE", "MINIMAL"]).default("STANDARD"),
});

type AdvancedOptionsValues = z.infer<typeof advancedOptionsSchema>;

interface AdvancedOptionsStepProps {
  defaultValues?: Partial<AdvancedOptionsValues>;
  onBack: () => void;
  onComplete: (data: AdvancedOptionsValues) => void;
}

export default function AdvancedOptionsStep({ 
  defaultValues, 
  onBack, 
  onComplete 
}: AdvancedOptionsStepProps) {
  const form = useForm<AdvancedOptionsValues>({
    resolver: zodResolver(advancedOptionsSchema),
    defaultValues: {
      adaptiveMatching: defaultValues?.adaptiveMatching ?? true,
      prioritizeOwnOrders: defaultValues?.prioritizeOwnOrders ?? false,
      minimumMatchSize: defaultValues?.minimumMatchSize ?? 0,
      notifyOnMatch: defaultValues?.notifyOnMatch ?? true,
      gasOptimization: defaultValues?.gasOptimization ?? "STANDARD",
    },
  });
  
  const handleSubmit = (data: AdvancedOptionsValues) => {
    onComplete(data);
  };
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Advanced Options</h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="text-sm">
                  These advanced settings give you more control over how your orders
                  are matched and executed in the marketplace.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <p className="text-sm text-muted-foreground">
          Fine-tune your order matching preferences with these advanced settings.
        </p>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="adaptiveMatching"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Adaptive Matching</FormLabel>
                  <FormDescription>
                    Automatically select the best matching strategy based on market conditions
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="prioritizeOwnOrders"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Prioritize Own Orders</FormLabel>
                  <FormDescription>
                    Give priority to matching with your own organization's orders when possible
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="minimumMatchSize"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Match Size</FormLabel>
                <FormControl>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">0 tons</span>
                      <span className="text-sm text-muted-foreground">100 tons</span>
                    </div>
                    <Slider
                      value={[field.value]}
                      min={0}
                      max={100}
                      step={1}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                    <div className="text-center">
                      <Badge variant="outline">{field.value} tons</Badge>
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  Minimum size for a match to be executed (0 = no minimum)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notifyOnMatch"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Notify on Match</FormLabel>
                  <FormDescription>
                    Receive notifications when your orders are matched
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gasOptimization"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Gas Optimization</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="grid grid-cols-3 gap-4"
                  >
                    <FormItem>
                      <FormLabel className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                        <FormControl>
                          <RadioGroupItem value="STANDARD" className="sr-only" />
                        </FormControl>
                        <Sparkles className="mb-3 h-6 w-6" />
                        <div className="space-y-1 text-center">
                          <FormLabel className="text-base">Standard</FormLabel>
                          <FormDescription>
                            Balanced approach
                          </FormDescription>
                        </div>
                      </FormLabel>
                    </FormItem>
                    <FormItem>
                      <FormLabel className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                        <FormControl>
                          <RadioGroupItem value="AGGRESSIVE" className="sr-only" />
                        </FormControl>
                        <Zap className="mb-3 h-6 w-6" />
                        <div className="space-y-1 text-center">
                          <FormLabel className="text-base">Aggressive</FormLabel>
                          <FormDescription>
                            Faster execution
                          </FormDescription>
                        </div>
                      </FormLabel>
                    </FormItem>
                    <FormItem>
                      <FormLabel className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                        <FormControl>
                          <RadioGroupItem value="MINIMAL" className="sr-only" />
                        </FormControl>
                        <Lightbulb className="mb-3 h-6 w-6" />
                        <div className="space-y-1 text-center">
                          <FormLabel className="text-base">Minimal</FormLabel>
                          <FormDescription>
                            Lowest gas cost
                          </FormDescription>
                        </div>
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormDescription>
                  Choose how to optimize gas costs for your transactions
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Time Settings
            </Button>
            
            <Button type="submit">
              Save Preferences
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
