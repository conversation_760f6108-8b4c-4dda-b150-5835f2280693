"use client";

import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Settings } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import StrategySelectionStep from "./strategy-selection-step";
import TimeSettingsStep from "./time-settings-step";
import AdvancedOptionsStep from "./advanced-options-step";

// Define the steps in the wizard
enum WizardStep {
  STRATEGY = 1,
  TIME_SETTINGS = 2,
  ADVANCED_OPTIONS = 3,
}

// Combined type for all form values
interface OrderMatchingPreferencesValues {
  defaultStrategy: "PRICE_TIME" | "PRO_RATA" | "FIFO" | "VOLUME_WEIGHTED";
  timeInForce: "GTC" | "IOC" | "FOK" | "GTD";
  expirationDays: number;
  autoRenew: boolean;
  adaptiveMatching: boolean;
  prioritizeOwnOrders: boolean;
  minimumMatchSize: number;
  notifyOnMatch: boolean;
  gasOptimization: "STANDARD" | "AGGRESSIVE" | "MINIMAL";
}

interface OrderMatchingWizardProps {
  organizationId?: string;
  defaultValues?: Partial<OrderMatchingPreferencesValues>;
  onComplete: (values: OrderMatchingPreferencesValues) => void;
}

export default function OrderMatchingWizard({
  organizationId,
  defaultValues,
  onComplete,
}: OrderMatchingWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.STRATEGY);
  const [formValues, setFormValues] = useState<Partial<OrderMatchingPreferencesValues>>(
    defaultValues || {
      defaultStrategy: "PRICE_TIME",
      timeInForce: "GTC",
      expirationDays: 30,
      autoRenew: false,
      adaptiveMatching: true,
      prioritizeOwnOrders: false,
      minimumMatchSize: 0,
      notifyOnMatch: true,
      gasOptimization: "STANDARD",
    }
  );
  
  // Calculate progress percentage
  const progress = (currentStep / Object.keys(WizardStep).length * 2) * 100;
  
  // Handle strategy selection
  const handleStrategyNext = (data: { defaultStrategy: any }) => {
    setFormValues({ ...formValues, ...data });
    setCurrentStep(WizardStep.TIME_SETTINGS);
  };
  
  // Handle time settings
  const handleTimeSettingsNext = (data: { timeInForce: any; expirationDays: number; autoRenew: boolean }) => {
    setFormValues({ ...formValues, ...data });
    setCurrentStep(WizardStep.ADVANCED_OPTIONS);
  };
  
  // Handle advanced options and complete the wizard
  const handleAdvancedOptionsComplete = (data: {
    adaptiveMatching: boolean;
    prioritizeOwnOrders: boolean;
    minimumMatchSize: number;
    notifyOnMatch: boolean;
    gasOptimization: any;
  }) => {
    const completeValues = { ...formValues, ...data } as OrderMatchingPreferencesValues;
    
    // Call the onComplete callback with all form values
    onComplete(completeValues);
    
    // Show success toast
    toast({
      title: "Preferences Saved",
      description: "Your order matching preferences have been updated successfully.",
    });
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="mr-2 h-5 w-5" />
          Order Matching Preferences
        </CardTitle>
        <CardDescription>
          Configure how your orders are matched in the marketplace
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Step {currentStep} of 3</span>
            <span className="text-sm text-muted-foreground">
              {currentStep === WizardStep.STRATEGY 
                ? "Strategy Selection" 
                : currentStep === WizardStep.TIME_SETTINGS 
                ? "Time Settings" 
                : "Advanced Options"}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
        
        {currentStep === WizardStep.STRATEGY && (
          <StrategySelectionStep 
            defaultValue={formValues.defaultStrategy}
            onNext={handleStrategyNext} 
          />
        )}
        
        {currentStep === WizardStep.TIME_SETTINGS && (
          <TimeSettingsStep 
            defaultValues={{
              timeInForce: formValues.timeInForce,
              expirationDays: formValues.expirationDays,
              autoRenew: formValues.autoRenew,
            }}
            onBack={() => setCurrentStep(WizardStep.STRATEGY)}
            onNext={handleTimeSettingsNext}
          />
        )}
        
        {currentStep === WizardStep.ADVANCED_OPTIONS && (
          <AdvancedOptionsStep 
            defaultValues={{
              adaptiveMatching: formValues.adaptiveMatching,
              prioritizeOwnOrders: formValues.prioritizeOwnOrders,
              minimumMatchSize: formValues.minimumMatchSize,
              notifyOnMatch: formValues.notifyOnMatch,
              gasOptimization: formValues.gasOptimization,
            }}
            onBack={() => setCurrentStep(WizardStep.TIME_SETTINGS)}
            onComplete={handleAdvancedOptionsComplete}
          />
        )}
      </CardContent>
    </Card>
  );
}
