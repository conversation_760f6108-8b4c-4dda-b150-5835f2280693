"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowRight, Info, ArrowDownUp, Scale, Clock, BarChart } from "lucide-react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { motion } from "framer-motion";

// Schema for strategy selection
const strategySelectionSchema = z.object({
  defaultStrategy: z.enum(["PRICE_TIME", "PRO_RATA", "FIFO", "VOLUME_WEIGHTED"]),
});

type StrategySelectionValues = z.infer<typeof strategySelectionSchema>;

interface StrategySelectionStepProps {
  defaultValue?: string;
  onNext: (data: StrategySelectionValues) => void;
}

export default function StrategySelectionStep({ 
  defaultValue = "PRICE_TIME", 
  onNext 
}: StrategySelectionStepProps) {
  const [selectedStrategy, setSelectedStrategy] = useState<string>(defaultValue);
  
  const form = useForm<StrategySelectionValues>({
    resolver: zodResolver(strategySelectionSchema),
    defaultValues: {
      defaultStrategy: defaultValue as any,
    },
  });
  
  const handleSubmit = () => {
    onNext({ defaultStrategy: selectedStrategy as any });
  };
  
  const strategies = [
    {
      id: "PRICE_TIME",
      name: "Price-Time Priority",
      description: "Best price, then earliest order",
      icon: ArrowDownUp,
      color: "bg-blue-100 text-blue-700",
    },
    {
      id: "PRO_RATA",
      name: "Pro-Rata",
      description: "Proportional allocation by size",
      icon: Scale,
      color: "bg-purple-100 text-purple-700",
    },
    {
      id: "FIFO",
      name: "First-In-First-Out",
      description: "Strict time priority",
      icon: Clock,
      color: "bg-green-100 text-green-700",
    },
    {
      id: "VOLUME_WEIGHTED",
      name: "Volume-Weighted",
      description: "Prioritizes larger orders",
      icon: BarChart,
      color: "bg-amber-100 text-amber-700",
    },
  ];
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Choose Your Matching Strategy</h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="text-sm">
                  The matching strategy determines how your orders are matched with others in the marketplace.
                  Different strategies are optimal for different market conditions.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <p className="text-sm text-muted-foreground">
          Select how you want your orders to be matched in the marketplace.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {strategies.map((strategy) => (
          <Card
            key={strategy.id}
            className={`relative overflow-hidden cursor-pointer transition-all duration-200 ${
              selectedStrategy === strategy.id 
                ? "border-primary ring-2 ring-primary/20" 
                : "hover:border-primary/50"
            }`}
            onClick={() => setSelectedStrategy(strategy.id)}
          >
            <div className="p-4">
              <div className="flex items-start space-x-4">
                <div className={`p-2 rounded-full ${strategy.color}`}>
                  <strategy.icon className="h-5 w-5" />
                </div>
                <div className="space-y-1">
                  <h4 className="font-medium">{strategy.name}</h4>
                  <p className="text-sm text-muted-foreground">{strategy.description}</p>
                </div>
              </div>
              
              {selectedStrategy === strategy.id && (
                <motion.div 
                  className="absolute inset-0 border-2 border-primary rounded-lg pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                />
              )}
            </div>
          </Card>
        ))}
      </div>
      
      <div className="pt-4">
        <Button 
          onClick={handleSubmit}
          className="w-full sm:w-auto"
        >
          Continue to Time Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
