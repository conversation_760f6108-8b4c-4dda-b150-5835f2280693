"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, ArrowRight, Info, Timer, Zap, Layers, Clock } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Schema for time settings
const timeSettingsSchema = z.object({
  timeInForce: z.enum(["GTC", "IOC", "FOK", "GTD"]).default("GTC"),
  expirationDays: z.number().min(1).max(90).default(30),
  autoRenew: z.boolean().default(false),
});

type TimeSettingsValues = z.infer<typeof timeSettingsSchema>;

interface TimeSettingsStepProps {
  defaultValues?: Partial<TimeSettingsValues>;
  onBack: () => void;
  onNext: (data: TimeSettingsValues) => void;
}

export default function TimeSettingsStep({ 
  defaultValues, 
  onBack, 
  onNext 
}: TimeSettingsStepProps) {
  const form = useForm<TimeSettingsValues>({
    resolver: zodResolver(timeSettingsSchema),
    defaultValues: {
      timeInForce: defaultValues?.timeInForce || "GTC",
      expirationDays: defaultValues?.expirationDays || 30,
      autoRenew: defaultValues?.autoRenew || false,
    },
  });
  
  const timeInForce = form.watch("timeInForce");
  
  const handleSubmit = (data: TimeSettingsValues) => {
    onNext(data);
  };
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Order Time Settings</h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="text-sm">
                  Time settings determine how long your orders remain active in the marketplace
                  and what happens when they can't be filled immediately.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <p className="text-sm text-muted-foreground">
          Configure how long your orders remain active and when they expire.
        </p>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="timeInForce"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Time in Force</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select time in force" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="GTC">
                      <div className="flex items-center">
                        <Timer className="mr-2 h-4 w-4" />
                        <div>
                          <span>Good Till Cancelled (GTC)</span>
                          <p className="text-xs text-muted-foreground">Order remains active until cancelled</p>
                        </div>
                      </div>
                    </SelectItem>
                    <SelectItem value="IOC">
                      <div className="flex items-center">
                        <Zap className="mr-2 h-4 w-4" />
                        <div>
                          <span>Immediate or Cancel (IOC)</span>
                          <p className="text-xs text-muted-foreground">Fill immediately or cancel remaining</p>
                        </div>
                      </div>
                    </SelectItem>
                    <SelectItem value="FOK">
                      <div className="flex items-center">
                        <Layers className="mr-2 h-4 w-4" />
                        <div>
                          <span>Fill or Kill (FOK)</span>
                          <p className="text-xs text-muted-foreground">Fill completely or cancel entirely</p>
                        </div>
                      </div>
                    </SelectItem>
                    <SelectItem value="GTD">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4" />
                        <div>
                          <span>Good Till Date (GTD)</span>
                          <p className="text-xs text-muted-foreground">Active until specified date</p>
                        </div>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Specify how long your orders remain active
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {timeInForce === "GTD" && (
            <FormField
              control={form.control}
              name="expirationDays"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiration Period (Days)</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">1 day</span>
                        <span className="text-sm text-muted-foreground">90 days</span>
                      </div>
                      <Slider
                        value={[field.value]}
                        min={1}
                        max={90}
                        step={1}
                        onValueChange={(value) => field.onChange(value[0])}
                      />
                      <div className="text-center">
                        <Badge variant="outline">{field.value} days</Badge>
                      </div>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Number of days before your orders expire
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          
          {(timeInForce === "GTC" || timeInForce === "GTD") && (
            <FormField
              control={form.control}
              name="autoRenew"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Auto-Renew Orders</FormLabel>
                    <FormDescription>
                      Automatically renew orders when they expire
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
          
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Strategy
            </Button>
            
            <Button type="submit">
              Continue to Advanced Options
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
