"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Al<PERSON><PERSON>riangle } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import OrderMatchingWizard from "./order-matching-wizard/order-matching-wizard";

// Define the type for order matching preferences
interface OrderMatchingPreferencesValues {
  defaultStrategy: "PRICE_TIME" | "PRO_RATA" | "FIFO" | "VOLUME_WEIGHTED";
  adaptiveMatching: boolean;
  prioritizeOwnOrders: boolean;
  minimumMatchSize: number;
  timeInForce: "GTC" | "IOC" | "FOK" | "GTD";
  expirationDays: number;
  autoRenew: boolean;
  notifyOnMatch: boolean;
  gasOptimization: "STANDARD" | "AGGRESSIVE" | "MINIMAL";
}

interface OrderMatchingPreferencesProps {
  organizationId?: string;
}

export default function OrderMatchingPreferences({ organizationId }: OrderMatchingPreferencesProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [preferences, setPreferences] = useState<OrderMatchingPreferencesValues | null>(null);

  // Fetch preferences
  useEffect(() => {
    async function fetchPreferences() {
      if (!session?.user) return;

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/marketplace/preferences");

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch preferences");
        }

        const data = await response.json();
        setPreferences(data.preferences);
      } catch (error) {
        console.error("Error fetching preferences:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchPreferences();
  }, [session]);

  // Handle saving preferences
  const handleSavePreferences = async (data: OrderMatchingPreferencesValues) => {
    setIsSaving(true);
    setError(null);

    try {
      const response = await fetch("/api/marketplace/preferences", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          preferences: data,
          organizationId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save preferences");
      }

      const result = await response.json();
      setPreferences(result.preferences);
    } catch (error) {
      console.error("Error saving preferences:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save preferences",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      {isLoading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading preferences...</span>
          </CardContent>
        </Card>
      ) : error ? (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <OrderMatchingWizard
          organizationId={organizationId}
          defaultValues={preferences || undefined}
          onComplete={handleSavePreferences}
        />
      )}
    </>
  );
}
