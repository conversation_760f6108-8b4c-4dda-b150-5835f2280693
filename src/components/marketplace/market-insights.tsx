"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import {
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Clock,
  ArrowRight,
  BarChart4,
  LineChart,
  PieChart,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Market data types
export interface MarketTrend {
  period: "1d" | "7d" | "30d" | "90d" | "1y";
  percentage: number;
  direction: "up" | "down" | "neutral";
}

export interface MarketDepth {
  buy: number;
  sell: number;
}

export interface RecentTrade {
  id: string;
  quantity: number;
  price: number;
  timestamp: string;
  side: "BUY" | "SELL";
}

export interface PricePoint {
  date: string;
  price: number;
}

export interface MarketInsightsProps {
  trends: MarketTrend[];
  marketDepth: MarketDepth;
  recentTrades: RecentTrade[];
  priceHistory: PricePoint[];
  averagePrice: number;
  volumeToday: number;
  projectType?: string;
  standard?: string;
  vintage?: string;
}

export function MarketInsights({
  trends,
  marketDepth,
  recentTrades,
  priceHistory,
  averagePrice,
  volumeToday,
  projectType,
  standard,
  vintage,
}: MarketInsightsProps) {
  const [activePeriod, setActivePeriod] = useState<"1d" | "7d" | "30d" | "90d" | "1y">("30d");
  
  // Get current trend
  const currentTrend = trends.find(trend => trend.period === activePeriod) || trends[0];
  
  // Calculate market depth percentage
  const totalDepth = marketDepth.buy + marketDepth.sell;
  const buyPercentage = totalDepth > 0 ? (marketDepth.buy / totalDepth) * 100 : 50;
  const sellPercentage = totalDepth > 0 ? (marketDepth.sell / totalDepth) * 100 : 50;
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center">
            <BarChart className="mr-2 h-4 w-4" />
            Market Insights
          </CardTitle>
          <InfoTooltip 
            content={
              <div className="space-y-2">
                <p>Market insights provide real-time data about trading activity and market conditions.</p>
                <p>Use this information to make more informed trading decisions.</p>
              </div>
            }
          />
        </div>
        <CardDescription>
          {projectType || standard || vintage ? (
            <span>
              Market data for {projectType && <Badge variant="outline" className="mr-1">{projectType}</Badge>}
              {standard && <Badge variant="outline" className="mr-1">{standard}</Badge>}
              {vintage && <Badge variant="outline">{vintage}</Badge>}
            </span>
          ) : (
            "Current market conditions and recent trading activity"
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Price Trend */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">Price Trend</p>
              <Tabs 
                value={activePeriod} 
                onValueChange={(value) => setActivePeriod(value as any)}
                className="h-7"
              >
                <TabsList className="h-7 p-0">
                  <TabsTrigger value="1d" className="text-xs px-1.5 h-6">1D</TabsTrigger>
                  <TabsTrigger value="7d" className="text-xs px-1.5 h-6">1W</TabsTrigger>
                  <TabsTrigger value="30d" className="text-xs px-1.5 h-6">1M</TabsTrigger>
                  <TabsTrigger value="90d" className="text-xs px-1.5 h-6">3M</TabsTrigger>
                  <TabsTrigger value="1y" className="text-xs px-1.5 h-6">1Y</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            
            <div className="flex items-center">
              {currentTrend.direction === "up" ? (
                <TrendingUp className="text-green-500 mr-1 h-4 w-4" />
              ) : currentTrend.direction === "down" ? (
                <TrendingDown className="text-red-500 mr-1 h-4 w-4" />
              ) : (
                <TrendingDown className="text-gray-500 mr-1 h-4 w-4" />
              )}
              <span className={
                currentTrend.direction === "up" 
                  ? "text-green-500" 
                  : currentTrend.direction === "down" 
                  ? "text-red-500" 
                  : "text-gray-500"
              }>
                {currentTrend.direction === "up" ? "+" : ""}
                {currentTrend.percentage.toFixed(2)}%
              </span>
            </div>
            
            <div className="h-20 w-full bg-muted rounded-md flex items-end p-1">
              {/* Placeholder for price chart - would be replaced with actual chart component */}
              <div className="flex items-end w-full h-full space-x-0.5">
                {priceHistory.slice(-20).map((point, i) => {
                  const height = `${Math.max(30, Math.min(100, (point.price / averagePrice) * 70))}%`;
                  const isPositive = i > 0 ? point.price >= priceHistory[i-1].price : true;
                  
                  return (
                    <div 
                      key={i} 
                      className={`flex-1 rounded-sm ${
                        isPositive ? "bg-green-500" : "bg-red-500"
                      }`}
                      style={{ height }}
                    />
                  );
                })}
              </div>
            </div>
            
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Avg: ${averagePrice.toFixed(2)}</span>
              <span>Vol: {volumeToday.toLocaleString()} tons</span>
            </div>
          </div>
          
          {/* Market Depth */}
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Market Depth</p>
            <div className="flex items-center justify-between">
              <span className="font-medium">{marketDepth.buy.toLocaleString()} tons</span>
              <span className="font-medium">{marketDepth.sell.toLocaleString()} tons</span>
            </div>
            <div className="flex h-4 overflow-hidden rounded-full">
              <div 
                className="bg-green-500" 
                style={{ width: `${buyPercentage}%` }} 
              />
              <div 
                className="bg-red-500" 
                style={{ width: `${sellPercentage}%` }} 
              />
            </div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Buy Orders</span>
              <span>Sell Orders</span>
            </div>
            
            <div className="flex justify-between mt-2">
              <div className="text-center">
                <p className="text-xs text-muted-foreground">Buy Pressure</p>
                <p className={`text-sm font-medium ${buyPercentage > 60 ? "text-green-500" : ""}`}>
                  {buyPercentage > sellPercentage ? "High" : buyPercentage === sellPercentage ? "Neutral" : "Low"}
                </p>
              </div>
              <div className="text-center">
                <p className="text-xs text-muted-foreground">Market Balance</p>
                <p className="text-sm font-medium">
                  {Math.abs(buyPercentage - sellPercentage) < 10 
                    ? "Balanced" 
                    : buyPercentage > sellPercentage 
                    ? "Buyers Market" 
                    : "Sellers Market"}
                </p>
              </div>
            </div>
          </div>
          
          {/* Recent Trades */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">Recent Trades</p>
              <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                View All
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {recentTrades.slice(0, 5).map((trade) => (
                <div key={trade.id} className="flex justify-between text-xs">
                  <span>{trade.quantity.toLocaleString()} tons</span>
                  <span className={trade.side === "BUY" ? "text-green-500" : "text-red-500"}>
                    ${trade.price.toFixed(2)}
                  </span>
                  <span className="text-muted-foreground">
                    {formatDistanceToNow(new Date(trade.timestamp), { addSuffix: true })}
                  </span>
                </div>
              ))}
            </div>
            
            <div className="flex justify-between mt-2 pt-2 border-t text-xs">
              <div>
                <span className="text-muted-foreground">24h Volume:</span>
                <span className="ml-1 font-medium">{volumeToday.toLocaleString()} tons</span>
              </div>
              <div>
                <span className="text-muted-foreground">Last Trade:</span>
                <span className="ml-1 font-medium">
                  {recentTrades.length > 0 
                    ? formatDistanceToNow(new Date(recentTrades[0].timestamp), { addSuffix: true }) 
                    : "N/A"}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-between mt-6 pt-4 border-t">
          <div className="flex space-x-4">
            <Button variant="outline" size="sm" className="h-8">
              <BarChart4 className="mr-2 h-4 w-4" />
              Market Analysis
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <LineChart className="mr-2 h-4 w-4" />
              Price History
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <PieChart className="mr-2 h-4 w-4" />
              Market Distribution
            </Button>
          </div>
          <Button variant="ghost" size="sm" className="h-8">
            <Clock className="mr-2 h-4 w-4" />
            Last updated: {formatDistanceToNow(new Date(), { addSuffix: true })}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
