"use client";

import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, AlertCircle, Leaf, BarChart3, Calendar, Globe } from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Pagination } from "@/components/ui/pagination";
import {
  AnimatedGrid,
  Animated<PERSON>ard,
  AnimatedCardHeader,
  Animated<PERSON>ard<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>ooter,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedBadge,
  PageTransition,
  AnimatedInput,
  showAnimatedToast
} from "@/components/ui/animated";

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  availableQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  country: string | null;
  projectId: string | null;
  projectType: string | null;
  verificationStatus: string;
  tokenId: string | null;
  organization: {
    id: string;
    name: string;
    logo: string | null;
    verificationStatus: string;
  };
  _count: {
    buyOrders: number;
    sellOrders: number;
  };
}

interface CarbonCreditGridProps {
  carbonCredits: CarbonCredit[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

export default function CarbonCreditGrid({
  carbonCredits,
  totalCount,
  currentPage,
  pageSize,
}: CarbonCreditGridProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [selectedCredit, setSelectedCredit] = useState<CarbonCredit | null>(null);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [isPurchasing, setIsPurchasing] = useState(false);

  const handlePurchase = async () => {
    if (!selectedCredit || !session?.user) {
      showAnimatedToast(
        "error",
        "Error",
        "You must be logged in to purchase carbon credits"
      );
      return;
    }

    setIsPurchasing(true);

    try {
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "BUY",
          quantity: purchaseQuantity,
          price: selectedCredit.price,
          carbonCreditId: selectedCredit.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create purchase order");
      }

      showAnimatedToast(
        "success",
        "Purchase order created",
        `Your order for ${purchaseQuantity} tons of carbon credits has been placed.`
      );

      // Refresh the page to update the available credits
      router.refresh();
      setSelectedCredit(null);
    } catch (error) {
      console.error("Error purchasing carbon credits:", error);
      showAnimatedToast(
        "error",
        "Error",
        error instanceof Error ? error.message : "Failed to purchase carbon credits"
      );
    } finally {
      setIsPurchasing(false);
    }
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  if (carbonCredits.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
        <div className="mb-4 rounded-full bg-primary/10 p-3">
          <AlertCircle className="h-6 w-6 text-primary" />
        </div>
        <h3 className="mb-1 text-lg font-medium">No carbon credits found</h3>
        <p className="mb-4 text-center text-sm text-muted-foreground">
          No carbon credits match your search criteria. Try adjusting your filters.
        </p>
        <Button variant="outline" onClick={() => router.push("/marketplace")}>
          Reset Filters
        </Button>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="space-y-6">
        <AnimatedGrid
          items={carbonCredits}
          keyExtractor={(credit) => credit.id}
          gridCols="sm:grid-cols-2 lg:grid-cols-3"
          renderItem={(credit) => (
            <AnimatedCard className="overflow-hidden flex flex-col" animationVariant="hoverLift">
              <AnimatedCardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <AnimatedCardTitle className="line-clamp-1">{credit.name}</AnimatedCardTitle>
                    <div className="flex items-center text-sm text-muted-foreground">
                      {credit.organization.logo ? (
                        <Image
                          src={credit.organization.logo}
                          alt={credit.organization.name}
                          width={20}
                          height={20}
                          className="mr-1 rounded-full"
                        />
                      ) : (
                        <div className="mr-1 h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center">
                          <Leaf className="h-3 w-3 text-primary" />
                        </div>
                      )}
                      <span>{credit.organization.name}</span>
                      {credit.organization.verificationStatus === "VERIFIED" && (
                        <AnimatedBadge variant="outline" className="ml-2 text-xs" pulse>
                          Verified
                        </AnimatedBadge>
                      )}
                    </div>
                  </div>
                  <AnimatedBadge variant="secondary">
                    {credit.tokenId ? "Tokenized" : "Traditional"}
                  </AnimatedBadge>
                </div>
              </AnimatedCardHeader>
              <AnimatedCardContent className="flex-grow">
                <p className="line-clamp-2 text-sm mb-4">
                  {credit.description || "No description provided."}
                </p>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <div className="flex items-center">
                    <BarChart3 className="mr-1.5 h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Available</p>
                      <p>{credit.availableQuantity.toLocaleString()} tons</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="mr-1.5 h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Vintage</p>
                      <p>{credit.vintage}</p>
                    </div>
                  </div>
                  <div className="col-span-2 flex items-center">
                    <Globe className="mr-1.5 h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Location</p>
                      <p className="truncate">{credit.location || credit.country || "N/A"}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4 flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Price</p>
                    <p className="text-lg font-bold">${credit.price.toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">per ton</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">Activity</p>
                    <p className="text-sm">
                      {credit._count.buyOrders} buy / {credit._count.sellOrders} sell
                    </p>
                  </div>
                </div>
              </AnimatedCardContent>
              <AnimatedCardFooter className="flex justify-between pt-3">
                <AnimatedButton
                  variant="outline"
                  onClick={() => router.push(`/marketplace/${credit.id}`)}
                >
                  View Details
                </AnimatedButton>
                <Dialog>
                  <DialogTrigger asChild>
                    <AnimatedButton
                      animationVariant="buttonTap"
                      onClick={() => {
                        setSelectedCredit(credit);
                        setPurchaseQuantity(1);
                      }}
                    >
                      Purchase
                    </AnimatedButton>
                  </DialogTrigger>
                <DialogContent>
                  {selectedCredit && (
                    <>
                      <DialogHeader>
                        <DialogTitle>Purchase Carbon Credits</DialogTitle>
                        <DialogDescription>
                          You are about to purchase carbon credits from {selectedCredit.organization.name}.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="mt-4 space-y-4">
                        <div className="rounded-md bg-muted p-4">
                          <h3 className="font-medium">{selectedCredit.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {selectedCredit.vintage} • {selectedCredit.standard} • {selectedCredit.methodology}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <label htmlFor="quantity" className="text-sm font-medium">
                            Quantity (tons)
                          </label>
                          <AnimatedInput
                            id="quantity"
                            type="number"
                            min={1}
                            max={selectedCredit.availableQuantity}
                            value={purchaseQuantity}
                            onChange={(e) => setPurchaseQuantity(parseInt(e.target.value))}
                          />
                          <p className="text-xs text-muted-foreground">
                            Available: {selectedCredit.availableQuantity.toLocaleString()} tons
                          </p>
                        </div>
                        <div className="rounded-md bg-muted p-4">
                          <div className="flex justify-between">
                            <span>Price per ton:</span>
                            <span>${selectedCredit.price.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between font-medium">
                            <span>Total price:</span>
                            <span>${(selectedCredit.price * purchaseQuantity).toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <AnimatedButton
                          variant="outline"
                          onClick={() => setSelectedCredit(null)}
                        >
                          Cancel
                        </AnimatedButton>
                        <AnimatedButton
                          onClick={handlePurchase}
                          disabled={isPurchasing || !session?.user}
                        >
                          {isPurchasing ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            "Confirm Purchase"
                          )}
                        </AnimatedButton>
                      </DialogFooter>
                    </>
                  )}
                </DialogContent>
              </Dialog>
            </AnimatedCardFooter>
          </AnimatedCard>
        )}
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page: number) => {
            const params = new URLSearchParams(window.location.search);
            params.set("page", page.toString());
            router.push(`/marketplace?${params.toString()}`);
          }}
        />
      )}
    </div>
    </PageTransition>
}
