'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Loader2, Upload, Download, FileText, Check, X } from 'lucide-react';

// Form schema for CSV upload
const csvUploadSchema = z.object({
  file: z.instanceof(File),
  operation: z.enum(['list', 'update', 'retire']),
  confirmOverwrite: z.boolean().default(false),
});

// Form schema for batch listing
const batchListingSchema = z.object({
  carbonCreditIds: z.array(z.string()).min(1, 'Select at least one carbon credit'),
  price: z.number().positive('Price must be positive'),
  minPurchaseQuantity: z.number().positive('Minimum purchase quantity must be positive').optional(),
});

// Form schema for batch updating
const batchUpdateSchema = z.object({
  carbonCreditIds: z.array(z.string()).min(1, 'Select at least one carbon credit'),
  price: z.number().positive('Price must be positive').optional(),
  minPurchaseQuantity: z.number().positive('Minimum purchase quantity must be positive').optional(),
  description: z.string().optional(),
});

type CsvUploadFormValues = z.infer<typeof csvUploadSchema>;
type BatchListingFormValues = z.infer<typeof batchListingSchema>;
type BatchUpdateFormValues = z.infer<typeof batchUpdateSchema>;

interface CarbonCredit {
  id: string;
  name: string;
  vintage: number;
  standard: string;
  methodology: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  status: string;
  verificationStatus: string;
}

interface BatchOperationsProps {
  carbonCredits: CarbonCredit[];
  onSuccess?: () => void;
}

export function BatchOperations({ carbonCredits, onSuccess }: BatchOperationsProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'csv' | 'listing' | 'update'>('listing');
  const [selectedCredits, setSelectedCredits] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [csvPreview, setCsvPreview] = useState<any[] | null>(null);
  const [csvErrors, setCsvErrors] = useState<string[]>([]);

  // CSV upload form
  const csvForm = useForm<CsvUploadFormValues>({
    resolver: zodResolver(csvUploadSchema),
    defaultValues: {
      operation: 'list',
      confirmOverwrite: false,
    },
  });

  // Batch listing form
  const listingForm = useForm<BatchListingFormValues>({
    resolver: zodResolver(batchListingSchema),
    defaultValues: {
      carbonCreditIds: [],
      price: 0,
      minPurchaseQuantity: 1,
    },
  });

  // Batch update form
  const updateForm = useForm<BatchUpdateFormValues>({
    resolver: zodResolver(batchUpdateSchema),
    defaultValues: {
      carbonCreditIds: [],
      price: undefined,
      minPurchaseQuantity: undefined,
      description: '',
    },
  });

  // Handle CSV file upload
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    csvForm.setValue('file', file);

    // Parse CSV file
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\\n');
        const headers = lines[0].split(',');
        
        const data = [];
        const errors = [];

        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue;
          
          const values = lines[i].split(',');
          const row: Record<string, string> = {};
          
          for (let j = 0; j < headers.length; j++) {
            row[headers[j].trim()] = values[j]?.trim() || '';
          }
          
          // Validate required fields
          if (!row.id && !row.name) {
            errors.push(`Row ${i}: Missing ID or name`);
          }
          
          if (csvForm.getValues('operation') === 'list' && !row.price) {
            errors.push(`Row ${i}: Missing price for listing operation`);
          }
          
          data.push(row);
        }

        setCsvPreview(data);
        setCsvErrors(errors);
      } catch (error) {
        console.error('Error parsing CSV:', error);
        toast({
          title: 'Error',
          description: 'Failed to parse CSV file. Please check the format.',
          variant: 'destructive',
        });
      }
    };
    reader.readAsText(file);
  };

  // Handle CSV form submission
  const onCsvSubmit = async (data: CsvUploadFormValues) => {
    if (csvErrors.length > 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the CSV file before submitting.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('operation', data.operation);
      formData.append('confirmOverwrite', data.confirmOverwrite.toString());

      const response = await fetch('/api/carbon-credits/batch', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process batch operation');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: `Batch ${data.operation} operation completed successfully. ${result.processed} items processed.`,
      });

      if (onSuccess) {
        onSuccess();
      }

      // Reset form
      csvForm.reset();
      setCsvPreview(null);
      setCsvErrors([]);
    } catch (error) {
      console.error('Error submitting batch operation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to process batch operation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle batch listing form submission
  const onListingSubmit = async (data: BatchListingFormValues) => {
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/carbon-credits/batch/list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to list carbon credits');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: `${result.processed} carbon credits listed successfully.`,
      });

      if (onSuccess) {
        onSuccess();
      }

      // Reset form
      listingForm.reset();
      setSelectedCredits([]);
    } catch (error) {
      console.error('Error listing carbon credits:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to list carbon credits',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle batch update form submission
  const onUpdateSubmit = async (data: BatchUpdateFormValues) => {
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/carbon-credits/batch/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update carbon credits');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: `${result.processed} carbon credits updated successfully.`,
      });

      if (onSuccess) {
        onSuccess();
      }

      // Reset form
      updateForm.reset();
      setSelectedCredits([]);
    } catch (error) {
      console.error('Error updating carbon credits:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update carbon credits',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle credit selection
  const handleCreditSelection = (creditId: string, checked: boolean) => {
    if (checked) {
      setSelectedCredits([...selectedCredits, creditId]);
      listingForm.setValue('carbonCreditIds', [...selectedCredits, creditId]);
      updateForm.setValue('carbonCreditIds', [...selectedCredits, creditId]);
    } else {
      const filtered = selectedCredits.filter(id => id !== creditId);
      setSelectedCredits(filtered);
      listingForm.setValue('carbonCreditIds', filtered);
      updateForm.setValue('carbonCreditIds', filtered);
    }
  };

  // Download CSV template
  const downloadCsvTemplate = () => {
    const headers = ['id', 'name', 'price', 'minPurchaseQuantity', 'description'];
    const csvContent = [
      headers.join(','),
      'credit-id-1,Carbon Credit 1,10,1,Description for credit 1',
      'credit-id-2,Carbon Credit 2,15,2,Description for credit 2',
    ].join('\\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'carbon-credits-template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Batch Operations</CardTitle>
        <CardDescription>
          Manage multiple carbon credits at once
        </CardDescription>
        <div className="flex space-x-2 pt-2">
          <Button
            variant={activeTab === 'listing' ? 'default' : 'outline'}
            onClick={() => setActiveTab('listing')}
          >
            Batch Listing
          </Button>
          <Button
            variant={activeTab === 'update' ? 'default' : 'outline'}
            onClick={() => setActiveTab('update')}
          >
            Batch Update
          </Button>
          <Button
            variant={activeTab === 'csv' ? 'default' : 'outline'}
            onClick={() => setActiveTab('csv')}
          >
            CSV Upload
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {activeTab === 'csv' && (
          <Form {...csvForm}>
            <form onSubmit={csvForm.handleSubmit(onCsvSubmit)} className="space-y-6">
              <FormField
                control={csvForm.control}
                name="operation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Operation Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select operation" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="list">List Carbon Credits</SelectItem>
                        <SelectItem value="update">Update Carbon Credits</SelectItem>
                        <SelectItem value="retire">Retire Carbon Credits</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the operation to perform on the carbon credits
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={csvForm.control}
                name="file"
                render={({ field: { value, onChange, ...fieldProps } }) => (
                  <FormItem>
                    <FormLabel>CSV File</FormLabel>
                    <FormControl>
                      <div className="grid w-full items-center gap-1.5">
                        <Input
                          type="file"
                          accept=".csv"
                          onChange={(e) => {
                            handleFileChange(e);
                          }}
                          {...fieldProps}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Upload a CSV file with carbon credit data.{' '}
                      <Button
                        variant="link"
                        className="h-auto p-0"
                        onClick={downloadCsvTemplate}
                      >
                        Download template
                      </Button>
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {csvPreview && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">CSV Preview</h3>
                  {csvErrors.length > 0 && (
                    <div className="rounded-md bg-destructive/10 p-3 text-destructive">
                      <h4 className="font-medium">Errors:</h4>
                      <ul className="list-inside list-disc">
                        {csvErrors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {Object.keys(csvPreview[0]).map((header) => (
                            <TableHead key={header}>{header}</TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {csvPreview.slice(0, 5).map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {Object.values(row).map((value, colIndex) => (
                              <TableCell key={colIndex}>{value}</TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    {csvPreview.length > 5 && (
                      <div className="p-2 text-center text-sm text-muted-foreground">
                        Showing 5 of {csvPreview.length} rows
                      </div>
                    )}
                  </div>
                </div>
              )}

              <FormField
                control={csvForm.control}
                name="confirmOverwrite"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Confirm Overwrite</FormLabel>
                      <FormDescription>
                        Check this box to confirm that you want to overwrite existing data
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isSubmitting || csvErrors.length > 0}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload and Process
                  </>
                )}
              </Button>
            </form>
          </Form>
        )}

        {activeTab === 'listing' && (
          <Form {...listingForm}>
            <form onSubmit={listingForm.handleSubmit(onListingSubmit)} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Select Carbon Credits</h3>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={
                              selectedCredits.length === carbonCredits.filter(c => c.verificationStatus === 'VERIFIED' && c.status !== 'LISTED').length
                            }
                            onCheckedChange={(checked) => {
                              if (checked) {
                                const eligibleCredits = carbonCredits
                                  .filter(c => c.verificationStatus === 'VERIFIED' && c.status !== 'LISTED')
                                  .map(c => c.id);
                                setSelectedCredits(eligibleCredits);
                                listingForm.setValue('carbonCreditIds', eligibleCredits);
                                updateForm.setValue('carbonCreditIds', eligibleCredits);
                              } else {
                                setSelectedCredits([]);
                                listingForm.setValue('carbonCreditIds', []);
                                updateForm.setValue('carbonCreditIds', []);
                              }
                            }}
                          />
                        </TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Vintage</TableHead>
                        <TableHead>Standard</TableHead>
                        <TableHead>Available Quantity</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {carbonCredits.map((credit) => (
                        <TableRow key={credit.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedCredits.includes(credit.id)}
                              onCheckedChange={(checked) => handleCreditSelection(credit.id, !!checked)}
                              disabled={credit.verificationStatus !== 'VERIFIED' || credit.status === 'LISTED'}
                            />
                          </TableCell>
                          <TableCell>{credit.name}</TableCell>
                          <TableCell>{credit.vintage}</TableCell>
                          <TableCell>{credit.standard}</TableCell>
                          <TableCell>{credit.availableQuantity}</TableCell>
                          <TableCell>
                            {credit.status === 'LISTED' ? (
                              <span className="flex items-center text-green-500">
                                <Check className="mr-1 h-4 w-4" />
                                Listed
                              </span>
                            ) : credit.verificationStatus !== 'VERIFIED' ? (
                              <span className="flex items-center text-yellow-500">
                                <X className="mr-1 h-4 w-4" />
                                Not Verified
                              </span>
                            ) : (
                              <span className="flex items-center text-blue-500">
                                <FileText className="mr-1 h-4 w-4" />
                                Ready to List
                              </span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <FormField
                control={listingForm.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price per Ton</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="10.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Set the price per ton for all selected carbon credits
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={listingForm.control}
                name="minPurchaseQuantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Purchase Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        step="1"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Set the minimum purchase quantity for all selected carbon credits
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isSubmitting || selectedCredits.length === 0}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Listing...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    List {selectedCredits.length} Carbon Credits
                  </>
                )}
              </Button>
            </form>
          </Form>
        )}

        {activeTab === 'update' && (
          <Form {...updateForm}>
            <form onSubmit={updateForm.handleSubmit(onUpdateSubmit)} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Select Carbon Credits</h3>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedCredits.length === carbonCredits.length}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                const allCredits = carbonCredits.map(c => c.id);
                                setSelectedCredits(allCredits);
                                listingForm.setValue('carbonCreditIds', allCredits);
                                updateForm.setValue('carbonCreditIds', allCredits);
                              } else {
                                setSelectedCredits([]);
                                listingForm.setValue('carbonCreditIds', []);
                                updateForm.setValue('carbonCreditIds', []);
                              }
                            }}
                          />
                        </TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Vintage</TableHead>
                        <TableHead>Standard</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {carbonCredits.map((credit) => (
                        <TableRow key={credit.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedCredits.includes(credit.id)}
                              onCheckedChange={(checked) => handleCreditSelection(credit.id, !!checked)}
                            />
                          </TableCell>
                          <TableCell>{credit.name}</TableCell>
                          <TableCell>{credit.vintage}</TableCell>
                          <TableCell>{credit.standard}</TableCell>
                          <TableCell>{credit.price}</TableCell>
                          <TableCell>{credit.status}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <FormField
                control={updateForm.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price per Ton (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="Leave blank to keep current prices"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Update the price per ton for all selected carbon credits
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={updateForm.control}
                name="minPurchaseQuantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Purchase Quantity (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        step="1"
                        placeholder="Leave blank to keep current values"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Update the minimum purchase quantity for all selected carbon credits
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={updateForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Leave blank to keep current descriptions"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Update the description for all selected carbon credits
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isSubmitting || selectedCredits.length === 0}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Update {selectedCredits.length} Carbon Credits
                  </>
                )}
              </Button>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
