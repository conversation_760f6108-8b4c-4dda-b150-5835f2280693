"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  AnimatedButton,
  AnimatedInput,
  PageTransition
} from "@/components/ui/animated";

interface MarketplaceHeaderProps {
  totalCount: number;
  search: string;
}

export default function MarketplaceHeader({ totalCount, search }: MarketplaceHeaderProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(search);
  const [sortOption, setSortOption] = useState(searchParams.get("sort") || "newest");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    const params = new URLSearchParams(searchParams.toString());

    if (searchTerm) {
      params.set("q", searchTerm);
    } else {
      params.delete("q");
    }

    // Reset to page 1 when searching
    params.delete("page");

    router.push(`/marketplace?${params.toString()}`);
  };

  const handleSortChange = (value: string) => {
    setSortOption(value);

    const params = new URLSearchParams(searchParams.toString());
    params.set("sort", value);

    router.push(`/marketplace?${params.toString()}`);
  };

  return (
    <PageTransition>
    <div className="space-y-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Carbon Credit Marketplace</h1>
          <p className="text-muted-foreground">
            Browse and purchase verified carbon credits from trusted providers
          </p>
        </div>
        <div className="flex items-center gap-2">
          <p className="text-sm text-muted-foreground whitespace-nowrap">
            {totalCount} {totalCount === 1 ? "credit" : "credits"} available
          </p>
          <Select value={sortOption} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="vintage-new">Vintage: Newest</SelectItem>
              <SelectItem value="vintage-old">Vintage: Oldest</SelectItem>
              <SelectItem value="quantity">Quantity: Most Available</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <form onSubmit={handleSearch} className="flex w-full items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <AnimatedInput
            type="search"
            placeholder="Search by name, standard, methodology, or location..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <AnimatedButton type="submit" animationVariant="buttonTap">Search</AnimatedButton>
      </form>
    </div>
    </PageTransition>
  );
}
