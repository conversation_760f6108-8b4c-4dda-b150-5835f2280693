"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  ShoppingCart,
  ListFilter,
  ClipboardList,
  Heart,
  Settings,
  BarChart4,
  History,
  BookOpen,
} from "lucide-react";

interface MarketplaceNavigationProps {
  className?: string;
}

export default function MarketplaceNavigation({ className }: MarketplaceNavigationProps) {
  const pathname = usePathname();
  
  const navigation = [
    {
      name: "Browse",
      href: "/marketplace",
      icon: ShoppingCart,
      current: pathname === "/marketplace",
    },
    {
      name: "My Orders",
      href: "/marketplace/my-orders",
      icon: ClipboardList,
      current: pathname === "/marketplace/my-orders" || pathname.startsWith("/marketplace/my-orders/"),
    },
    {
      name: "Watchlist",
      href: "/marketplace/watchlist",
      icon: Heart,
      current: pathname === "/marketplace/watchlist",
    },
    {
      name: "Order Book",
      href: "/marketplace/order-book",
      icon: BookO<PERSON>,
      current: pathname === "/marketplace/order-book",
    },
    {
      name: "History",
      href: "/marketplace/history",
      icon: History,
      current: pathname === "/marketplace/history",
    },
    {
      name: "Analytics",
      href: "/marketplace/analytics",
      icon: BarChart4,
      current: pathname === "/marketplace/analytics",
    },
    {
      name: "Preferences",
      href: "/marketplace/preferences",
      icon: Settings,
      current: pathname === "/marketplace/preferences",
    },
  ];
  
  return (
    <nav className={cn("flex space-x-2 lg:space-x-4 overflow-x-auto pb-2", className)}>
      {navigation.map((item) => (
        <Link
          key={item.name}
          href={item.href}
          className={cn(
            "flex items-center px-3 py-2 text-sm font-medium rounded-md whitespace-nowrap",
            item.current
              ? "bg-primary text-primary-foreground"
              : "text-muted-foreground hover:bg-muted hover:text-foreground"
          )}
        >
          <item.icon className="mr-2 h-4 w-4" />
          {item.name}
        </Link>
      ))}
    </nav>
  );
}
