"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Filter, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";

interface MarketplaceFiltersProps {
  standards: string[];
  methodologies: string[];
  vintages: number[];
  countries: string[];
  selectedStandard?: string;
  selectedMethodology?: string;
  selectedVintage?: number;
  selectedCountry?: string;
  minPrice?: number;
  maxPrice?: number;
  sort?: string;
}

export default function MarketplaceFilters({
  standards,
  methodologies,
  vintages,
  countries,
  selectedStandard,
  selectedMethodology,
  selectedVintage,
  selectedCountry,
  minPrice,
  maxPrice,
  sort,
}: MarketplaceFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [priceRange, setPriceRange] = useState<[number, number]>([
    minPrice || 0,
    maxPrice || 100,
  ]);
  
  const [activeFilters, setActiveFilters] = useState<number>(0);
  
  // Calculate active filters count
  useEffect(() => {
    let count = 0;
    if (selectedStandard) count++;
    if (selectedMethodology) count++;
    if (selectedVintage) count++;
    if (selectedCountry) count++;
    if (minPrice || maxPrice) count++;
    setActiveFilters(count);
  }, [selectedStandard, selectedMethodology, selectedVintage, selectedCountry, minPrice, maxPrice]);
  
  const applyFilter = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    
    // Reset to page 1 when filtering
    params.delete("page");
    
    router.push(`/marketplace?${params.toString()}`);
  };
  
  const applyPriceFilter = () => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (priceRange[0] > 0) {
      params.set("minPrice", priceRange[0].toString());
    } else {
      params.delete("minPrice");
    }
    
    if (priceRange[1] < 100) {
      params.set("maxPrice", priceRange[1].toString());
    } else {
      params.delete("maxPrice");
    }
    
    // Reset to page 1 when filtering
    params.delete("page");
    
    router.push(`/marketplace?${params.toString()}`);
  };
  
  const resetFilters = () => {
    router.push("/marketplace");
  };
  
  const removeFilter = (key: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete(key);
    router.push(`/marketplace?${params.toString()}`);
  };
  
  // Desktop filter view
  const DesktopFilters = () => (
    <Card className="sticky top-20 hidden lg:block">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Filters</CardTitle>
        {activeFilters > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            Reset
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {activeFilters > 0 && (
          <div className="flex flex-wrap gap-2">
            {selectedStandard && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {selectedStandard}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeFilter("standard")}
                />
              </Badge>
            )}
            {selectedMethodology && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {selectedMethodology}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeFilter("methodology")}
                />
              </Badge>
            )}
            {selectedVintage && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Vintage: {selectedVintage}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeFilter("vintage")}
                />
              </Badge>
            )}
            {selectedCountry && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {selectedCountry}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeFilter("country")}
                />
              </Badge>
            )}
            {(minPrice || maxPrice) && (
              <Badge variant="secondary" className="flex items-center gap-1">
                ${minPrice || 0} - ${maxPrice || 100}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => {
                    removeFilter("minPrice");
                    removeFilter("maxPrice");
                  }}
                />
              </Badge>
            )}
          </div>
        )}
        
        <Accordion type="multiple" defaultValue={["standard", "methodology", "price", "vintage", "country"]}>
          <AccordionItem value="standard">
            <AccordionTrigger>Standard</AccordionTrigger>
            <AccordionContent>
              <RadioGroup
                value={selectedStandard || ""}
                onValueChange={(value) => applyFilter("standard", value || null)}
              >
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="standard-all" />
                    <Label htmlFor="standard-all">All Standards</Label>
                  </div>
                  {standards.map((standard) => (
                    <div key={standard} className="flex items-center space-x-2">
                      <RadioGroupItem value={standard} id={`standard-${standard}`} />
                      <Label htmlFor={`standard-${standard}`}>{standard}</Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="methodology">
            <AccordionTrigger>Methodology</AccordionTrigger>
            <AccordionContent>
              <RadioGroup
                value={selectedMethodology || ""}
                onValueChange={(value) => applyFilter("methodology", value || null)}
              >
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="methodology-all" />
                    <Label htmlFor="methodology-all">All Methodologies</Label>
                  </div>
                  {methodologies.map((methodology) => (
                    <div key={methodology} className="flex items-center space-x-2">
                      <RadioGroupItem value={methodology} id={`methodology-${methodology}`} />
                      <Label htmlFor={`methodology-${methodology}`}>{methodology}</Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="price">
            <AccordionTrigger>Price Range</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    ${priceRange[0]} - ${priceRange[1]}
                  </span>
                  <Button size="sm" variant="outline" onClick={applyPriceFilter}>
                    Apply
                  </Button>
                </div>
                <Slider
                  value={priceRange}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={(value) => setPriceRange(value as [number, number])}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="vintage">
            <AccordionTrigger>Vintage Year</AccordionTrigger>
            <AccordionContent>
              <RadioGroup
                value={selectedVintage?.toString() || ""}
                onValueChange={(value) => applyFilter("vintage", value || null)}
              >
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="vintage-all" />
                    <Label htmlFor="vintage-all">All Years</Label>
                  </div>
                  {vintages.map((vintage) => (
                    <div key={vintage} className="flex items-center space-x-2">
                      <RadioGroupItem value={vintage.toString()} id={`vintage-${vintage}`} />
                      <Label htmlFor={`vintage-${vintage}`}>{vintage}</Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="country">
            <AccordionTrigger>Country</AccordionTrigger>
            <AccordionContent>
              <RadioGroup
                value={selectedCountry || ""}
                onValueChange={(value) => applyFilter("country", value || null)}
              >
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="" id="country-all" />
                    <Label htmlFor="country-all">All Countries</Label>
                  </div>
                  {countries.map((country) => (
                    <div key={country} className="flex items-center space-x-2">
                      <RadioGroupItem value={country} id={`country-${country}`} />
                      <Label htmlFor={`country-${country}`}>{country}</Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  );
  
  // Mobile filter view
  const MobileFilters = () => (
    <div className="lg:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" className="w-full flex items-center justify-between">
            <span className="flex items-center">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </span>
            {activeFilters > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilters}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-[300px] sm:w-[400px]">
          <SheetHeader>
            <SheetTitle>Filters</SheetTitle>
            <SheetDescription>
              Refine your search with these filters
            </SheetDescription>
          </SheetHeader>
          <div className="py-4">
            {activeFilters > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium">Active Filters</h3>
                  <Button variant="ghost" size="sm" onClick={resetFilters}>
                    Reset All
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedStandard && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      {selectedStandard}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeFilter("standard")}
                      />
                    </Badge>
                  )}
                  {selectedMethodology && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      {selectedMethodology}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeFilter("methodology")}
                      />
                    </Badge>
                  )}
                  {selectedVintage && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Vintage: {selectedVintage}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeFilter("vintage")}
                      />
                    </Badge>
                  )}
                  {selectedCountry && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      {selectedCountry}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeFilter("country")}
                      />
                    </Badge>
                  )}
                  {(minPrice || maxPrice) && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      ${minPrice || 0} - ${maxPrice || 100}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => {
                          removeFilter("minPrice");
                          removeFilter("maxPrice");
                        }}
                      />
                    </Badge>
                  )}
                </div>
              </div>
            )}
            
            <Separator className="my-4" />
            
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Standard</h3>
                <RadioGroup
                  value={selectedStandard || ""}
                  onValueChange={(value) => applyFilter("standard", value || null)}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="" id="mobile-standard-all" />
                      <Label htmlFor="mobile-standard-all">All Standards</Label>
                    </div>
                    {standards.map((standard) => (
                      <div key={standard} className="flex items-center space-x-2">
                        <RadioGroupItem value={standard} id={`mobile-standard-${standard}`} />
                        <Label htmlFor={`mobile-standard-${standard}`}>{standard}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Methodology</h3>
                <RadioGroup
                  value={selectedMethodology || ""}
                  onValueChange={(value) => applyFilter("methodology", value || null)}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="" id="mobile-methodology-all" />
                      <Label htmlFor="mobile-methodology-all">All Methodologies</Label>
                    </div>
                    {methodologies.map((methodology) => (
                      <div key={methodology} className="flex items-center space-x-2">
                        <RadioGroupItem value={methodology} id={`mobile-methodology-${methodology}`} />
                        <Label htmlFor={`mobile-methodology-${methodology}`}>{methodology}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Price Range</h3>
                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    ${priceRange[0]} - ${priceRange[1]}
                  </span>
                  <Button size="sm" variant="outline" onClick={applyPriceFilter}>
                    Apply
                  </Button>
                </div>
                <Slider
                  value={priceRange}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={(value) => setPriceRange(value as [number, number])}
                />
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Vintage Year</h3>
                <RadioGroup
                  value={selectedVintage?.toString() || ""}
                  onValueChange={(value) => applyFilter("vintage", value || null)}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="" id="mobile-vintage-all" />
                      <Label htmlFor="mobile-vintage-all">All Years</Label>
                    </div>
                    {vintages.map((vintage) => (
                      <div key={vintage} className="flex items-center space-x-2">
                        <RadioGroupItem value={vintage.toString()} id={`mobile-vintage-${vintage}`} />
                        <Label htmlFor={`mobile-vintage-${vintage}`}>{vintage}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Country</h3>
                <RadioGroup
                  value={selectedCountry || ""}
                  onValueChange={(value) => applyFilter("country", value || null)}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="" id="mobile-country-all" />
                      <Label htmlFor="mobile-country-all">All Countries</Label>
                    </div>
                    {countries.map((country) => (
                      <div key={country} className="flex items-center space-x-2">
                        <RadioGroupItem value={country} id={`mobile-country-${country}`} />
                        <Label htmlFor={`mobile-country-${country}`}>{country}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
            </div>
            
            <div className="mt-6 space-x-2">
              <SheetClose asChild>
                <Button className="w-full">Apply Filters</Button>
              </SheetClose>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
  
  return (
    <>
      <DesktopFilters />
      <MobileFilters />
    </>
  );
}
