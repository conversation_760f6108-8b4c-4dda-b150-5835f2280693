"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/charts";
import { Leaf, TrendingUp, Users, BarChart2, ArrowUpRight, ArrowDownRight } from "lucide-react";

interface MarketplaceStatsProps {
  stats: {
    totalListed: number;
    totalVolume: number;
    averagePrice: number;
    totalTransactions: number;
    transactionVolume: {
      quantity: number;
      amount: number;
    };
    activeBuyOrders: number;
    activeSellOrders: number;
    organizationsWithCredits: number;
    recentTransactions: any[];
    priceHistory: any[];
  };
}

export default function MarketplaceStats({ stats }: MarketplaceStatsProps) {
  const [chartView, setChartView] = useState<"volume" | "price">("volume");
  
  // Format price history data for chart
  const priceHistoryData = stats.priceHistory.map((item) => ({
    name: `${item.month}/${item.year}`,
    value: item._avg.price,
  }));
  
  // Create volume data (this would ideally come from the backend)
  const volumeData = stats.priceHistory.map((item, index) => ({
    name: `${item.month}/${item.year}`,
    value: Math.round(stats.totalVolume / stats.priceHistory.length * (0.8 + Math.random() * 0.4)),
  }));
  
  return (
    <div className="mt-8">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Credits Listed</CardTitle>
            <Leaf className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalListed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalVolume.toLocaleString()} tons available
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Price</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.averagePrice.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Per ton of carbon credits
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Orders</CardTitle>
            <BarChart2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(stats.activeBuyOrders + stats.activeSellOrders).toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span className="flex items-center">
                <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                {stats.activeBuyOrders} buy
              </span>
              <span>•</span>
              <span className="flex items-center">
                <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                {stats.activeSellOrders} sell
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Organizations</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.organizationsWithCredits}</div>
            <p className="text-xs text-muted-foreground">
              Verified sellers on the marketplace
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div className="mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Market Activity</CardTitle>
            <CardDescription>
              Historical data for the carbon credit marketplace
            </CardDescription>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="volume" onClick={() => setChartView("volume")}>
                Trading Volume
              </TabsTrigger>
              <TabsTrigger value="price" onClick={() => setChartView("price")}>
                Price Trends
              </TabsTrigger>
            </TabsList>
          </CardHeader>
          <CardContent className="pl-2">
            {chartView === "volume" ? (
              <BarChart
                data={volumeData}
                xAxisKey="name"
                yAxisKey="value"
                height={300}
                colors={["#0ea5e9"]}
                valueFormatter={(value: number) => `${value.toLocaleString()} tons`}
              />
            ) : (
              <LineChart
                data={priceHistoryData}
                xAxisKey="name"
                yAxisKey="value"
                height={300}
                colors={["#10b981"]}
                valueFormatter={(value: number) => `$${value.toFixed(2)}`}
              />
            )}
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>
              Latest completed trades on the marketplace
            </CardDescription>
          </CardHeader>
          <CardContent>
            {stats.recentTransactions.length > 0 ? (
              <div className="space-y-4">
                {stats.recentTransactions.map((tx) => (
                  <div key={tx.id} className="flex items-center">
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {tx.carbonCredit.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(tx.createdAt).toLocaleDateString()} • {tx.quantity} tons at ${tx.price}/ton
                      </p>
                    </div>
                    <div className="ml-auto font-medium">
                      ${tx.amount.toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-sm text-muted-foreground py-8">
                No recent transactions to display
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
