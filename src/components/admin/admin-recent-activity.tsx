"use client";

import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Building2, User, CreditCard, DollarSign } from "lucide-react";

interface AdminRecentActivityProps {
  items: any[];
  type: "organizations" | "transactions" | "users";
}

export default function AdminRecentActivity({ items, type }: AdminRecentActivityProps) {
  const router = useRouter();

  if (!items || items.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        No recent {type} to display
      </div>
    );
  }

  const renderOrganizationItem = (item: any) => (
    <div 
      key={item.id} 
      className="flex items-center space-x-4 rounded-md p-2 hover:bg-muted cursor-pointer"
      onClick={() => router.push(`/admin/organizations/${item.id}`)}
    >
      <Avatar className="h-8 w-8">
        {item.logo ? (
          <AvatarImage src={item.logo} alt={item.name} />
        ) : (
          <AvatarFallback>
            <Building2 className="h-4 w-4" />
          </AvatarFallback>
        )}
      </Avatar>
      <div className="flex-1 space-y-1">
        <p className="text-sm font-medium leading-none">{item.name}</p>
        <p className="text-xs text-muted-foreground">
          {new Date(item.createdAt).toLocaleDateString()} • {item._count?.users || 0} users
        </p>
      </div>
      <Badge variant={
        item.verificationStatus === "VERIFIED" ? "default" : 
        item.verificationStatus === "IN_REVIEW" ? "secondary" : 
        "outline"
      }>
        {item.verificationStatus}
      </Badge>
    </div>
  );

  const renderTransactionItem = (item: any) => (
    <div 
      key={item.id} 
      className="flex items-center space-x-4 rounded-md p-2 hover:bg-muted cursor-pointer"
      onClick={() => router.push(`/admin/transactions/${item.id}`)}
    >
      <Avatar className="h-8 w-8">
        <AvatarFallback>
          <DollarSign className="h-4 w-4" />
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 space-y-1">
        <p className="text-sm font-medium leading-none">
          {item.carbonCredit?.name || "Carbon Credit Transaction"}
        </p>
        <p className="text-xs text-muted-foreground">
          {new Date(item.createdAt).toLocaleDateString()} • ${item.amount?.toLocaleString() || "0"}
        </p>
      </div>
      <Badge>
        {item.status}
      </Badge>
    </div>
  );

  const renderUserItem = (item: any) => (
    <div 
      key={item.id} 
      className="flex items-center space-x-4 rounded-md p-2 hover:bg-muted cursor-pointer"
      onClick={() => router.push(`/admin/users/${item.id}`)}
    >
      <Avatar className="h-8 w-8">
        {item.image ? (
          <AvatarImage src={item.image} alt={item.name} />
        ) : (
          <AvatarFallback>
            <User className="h-4 w-4" />
          </AvatarFallback>
        )}
      </Avatar>
      <div className="flex-1 space-y-1">
        <p className="text-sm font-medium leading-none">{item.name || item.email}</p>
        <p className="text-xs text-muted-foreground">
          {new Date(item.createdAt).toLocaleDateString()} • {item.organization?.name || "No organization"}
        </p>
      </div>
      <Badge variant={
        item.role === "ADMIN" ? "destructive" : 
        item.role === "ORGANIZATION_ADMIN" ? "default" : 
        "secondary"
      }>
        {item.role}
      </Badge>
    </div>
  );

  return (
    <div className="space-y-2">
      {items.map((item) => {
        if (type === "organizations") return renderOrganizationItem(item);
        if (type === "transactions") return renderTransactionItem(item);
        if (type === "users") return renderUserItem(item);
        return null;
      })}
    </div>
  );
}
