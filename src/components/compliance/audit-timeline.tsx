"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { AnimatedCard } from "@/components/ui/animated";
import { HelpCard } from "@/components/ui/contextual-help";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DatePicker } from "@/components/ui/date-picker";
import {
  FileText,
  Download,
  Share2,
  Printer,
  Eye,
  Clock,
  Calendar,
  User,
  Shield,
  AlertCircle,
  FileCheck,
  Search,
  Filter,
  ChevronDown,
  ArrowUpDown,
  RefreshCw,
  History,
  Activity,
  Layers,
  ZoomIn,
  ZoomOut,
  ChevronsLeft,
  ChevronsRight
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Sample audit events data
const SAMPLE_AUDIT_EVENTS = [
  {
    id: "evt-001",
    timestamp: new Date("2023-10-15T09:30:00"),
    type: "KYC_VERIFICATION",
    user: "John Doe",
    action: "Completed KYC verification for user ID: USR-123",
    status: "SUCCESS",
    details: {
      userId: "USR-123",
      documentType: "Passport",
      verificationMethod: "Manual Review"
    }
  },
  {
    id: "evt-002",
    timestamp: new Date("2023-10-15T10:15:00"),
    type: "AML_SCREENING",
    user: "Jane Smith",
    action: "Performed AML screening for organization ID: ORG-456",
    status: "WARNING",
    details: {
      organizationId: "ORG-456",
      riskScore: 65,
      flaggedItems: ["PEP Match", "Adverse Media"]
    }
  },
  {
    id: "evt-003",
    timestamp: new Date("2023-10-14T14:45:00"),
    type: "CARBON_VERIFICATION",
    user: "John Doe",
    action: "Verified carbon credit project ID: PRJ-789",
    status: "SUCCESS",
    details: {
      projectId: "PRJ-789",
      creditAmount: 1000,
      methodology: "VCS VM0015"
    }
  },
  {
    id: "evt-004",
    timestamp: new Date("2023-10-14T11:20:00"),
    type: "DOCUMENT_UPLOAD",
    user: "Jane Smith",
    action: "Uploaded compliance document for organization ID: ORG-456",
    status: "SUCCESS",
    details: {
      organizationId: "ORG-456",
      documentType: "Annual Report",
      fileSize: "2.4 MB"
    }
  },
  {
    id: "evt-005",
    timestamp: new Date("2023-10-13T16:30:00"),
    type: "REPORT_GENERATION",
    user: "John Doe",
    action: "Generated compliance report ID: REP-321",
    status: "SUCCESS",
    details: {
      reportId: "REP-321",
      reportType: "Quarterly Compliance",
      format: "PDF"
    }
  },
  {
    id: "evt-006",
    timestamp: new Date("2023-10-13T09:15:00"),
    type: "KYC_VERIFICATION",
    user: "Jane Smith",
    action: "Rejected KYC verification for user ID: USR-456",
    status: "FAILURE",
    details: {
      userId: "USR-456",
      documentType: "Driver's License",
      rejectionReason: "Document expired"
    }
  },
  {
    id: "evt-007",
    timestamp: new Date("2023-10-12T13:45:00"),
    type: "AML_SCREENING",
    user: "John Doe",
    action: "Performed AML screening for organization ID: ORG-789",
    status: "SUCCESS",
    details: {
      organizationId: "ORG-789",
      riskScore: 25,
      flaggedItems: []
    }
  },
  {
    id: "evt-008",
    timestamp: new Date("2023-10-12T10:30:00"),
    type: "CARBON_VERIFICATION",
    user: "Jane Smith",
    action: "Requested additional information for project ID: PRJ-456",
    status: "PENDING",
    details: {
      projectId: "PRJ-456",
      requestedInfo: ["Monitoring Report", "Verification Statement"]
    }
  }
];

// Event type icons
const EVENT_TYPE_ICONS: Record<string, React.ReactNode> = {
  KYC_VERIFICATION: <Shield className="h-4 w-4" />,
  AML_SCREENING: <AlertCircle className="h-4 w-4" />,
  CARBON_VERIFICATION: <FileCheck className="h-4 w-4" />,
  DOCUMENT_UPLOAD: <FileText className="h-4 w-4" />,
  REPORT_GENERATION: <FileText className="h-4 w-4" />,
};

// Event status badges
const EVENT_STATUS_BADGES: Record<string, { variant: "default" | "outline" | "secondary" | "destructive", label: string }> = {
  SUCCESS: { variant: "default", label: "Success" },
  WARNING: { variant: "secondary", label: "Warning" },
  FAILURE: { variant: "destructive", label: "Failed" },
  PENDING: { variant: "outline", label: "Pending" },
};

interface AuditTimelineProps {
  userId?: string;
  organizationId?: string;
  startDate?: Date;
  endDate?: Date;
  eventTypes?: string[];
}

export function AuditTimeline({
  userId,
  organizationId,
  startDate,
  endDate,
  eventTypes
}: AuditTimelineProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedEventTypes, setSelectedEventTypes] = useState<string[]>(eventTypes || []);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{
    from?: Date;
    to?: Date;
  }>({
    from: startDate,
    to: endDate
  });
  const [zoomLevel, setZoomLevel] = useState(1);
  const [activeTab, setActiveTab] = useState("timeline");
  const [groupBy, setGroupBy] = useState<"none" | "day" | "type" | "user">("day");

  // Fetch audit events
  useEffect(() => {
    const fetchAuditEvents = async () => {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setIsLoading(false);
    };
    
    fetchAuditEvents();
  }, [userId, organizationId]);

  // Filter audit events
  const filteredEvents = SAMPLE_AUDIT_EVENTS.filter(event => {
    // Match search query
    const matchesSearch = 
      event.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.type.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Match event types
    const matchesEventType = 
      selectedEventTypes.length === 0 || 
      selectedEventTypes.includes(event.type);
    
    // Match users
    const matchesUser = 
      selectedUsers.length === 0 || 
      selectedUsers.includes(event.user);
    
    // Match statuses
    const matchesStatus = 
      selectedStatuses.length === 0 || 
      selectedStatuses.includes(event.status);
    
    // Match date range
    const matchesDateRange = 
      (!dateRange.from || event.timestamp >= dateRange.from) &&
      (!dateRange.to || event.timestamp <= dateRange.to);
    
    return matchesSearch && matchesEventType && matchesUser && matchesStatus && matchesDateRange;
  });

  // Group events by selected grouping
  const groupedEvents = () => {
    if (groupBy === "none") {
      return { "All Events": filteredEvents };
    } else if (groupBy === "day") {
      return filteredEvents.reduce((groups: Record<string, typeof SAMPLE_AUDIT_EVENTS>, event) => {
        const day = format(event.timestamp, "yyyy-MM-dd");
        if (!groups[day]) {
          groups[day] = [];
        }
        groups[day].push(event);
        return groups;
      }, {});
    } else if (groupBy === "type") {
      return filteredEvents.reduce((groups: Record<string, typeof SAMPLE_AUDIT_EVENTS>, event) => {
        if (!groups[event.type]) {
          groups[event.type] = [];
        }
        groups[event.type].push(event);
        return groups;
      }, {});
    } else if (groupBy === "user") {
      return filteredEvents.reduce((groups: Record<string, typeof SAMPLE_AUDIT_EVENTS>, event) => {
        if (!groups[event.user]) {
          groups[event.user] = [];
        }
        groups[event.user].push(event);
        return groups;
      }, {});
    }
    return { "All Events": filteredEvents };
  };

  // Extract unique users for filtering
  const uniqueUsers = Array.from(new Set(SAMPLE_AUDIT_EVENTS.map(event => event.user)));
  
  // Extract unique event types for filtering
  const uniqueEventTypes = Array.from(new Set(SAMPLE_AUDIT_EVENTS.map(event => event.type)));
  
  // Extract unique statuses for filtering
  const uniqueStatuses = Array.from(new Set(SAMPLE_AUDIT_EVENTS.map(event => event.status)));

  // Render timeline view
  const renderTimeline = () => {
    const groups = groupedEvents();
    
    return (
      <div className="space-y-8">
        {Object.entries(groups).map(([groupName, events]) => (
          <div key={groupName} className="space-y-4">
            <div className="sticky top-0 z-10 bg-background py-2">
              <h3 className="text-lg font-semibold">
                {groupBy === "day" ? format(new Date(groupName), "MMMM d, yyyy") : groupName}
                <Badge variant="outline" className="ml-2">
                  {events.length} {events.length === 1 ? "event" : "events"}
                </Badge>
              </h3>
              <Separator className="mt-2" />
            </div>
            
            <motion.div
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
              className="space-y-4 pl-6 border-l-2 border-muted"
            >
              {events.map((event) => (
                <motion.div
                  key={event.id}
                  variants={itemVariants}
                  className="relative"
                >
                  <div className="absolute -left-[25px] mt-1 h-4 w-4 rounded-full bg-primary" />
                  <AnimatedCard className="ml-2">
                    <CardHeader className="p-4 pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {EVENT_TYPE_ICONS[event.type]}
                          <span className="ml-2 font-medium">{event.type.replace(/_/g, ' ')}</span>
                          <Badge 
                            variant={EVENT_STATUS_BADGES[event.status].variant} 
                            className="ml-2"
                          >
                            {EVENT_STATUS_BADGES[event.status].label}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {format(event.timestamp, "h:mm a")}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <p className="text-sm">{event.action}</p>
                      <div className="mt-2 text-xs text-muted-foreground flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        {event.user}
                      </div>
                    </CardContent>
                  </AnimatedCard>
                </motion.div>
              ))}
            </motion.div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Audit Timeline</h2>
          <p className="text-muted-foreground">
            Visualize and track compliance activities over time
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      <Separator />

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <Tabs
          defaultValue="timeline"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full md:w-auto"
        >
          <TabsList className="grid grid-cols-2 w-full md:w-auto">
            <TabsTrigger value="timeline" className="flex items-center">
              <History className="h-4 w-4 mr-2" />
              Timeline
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center">
              <Layers className="h-4 w-4 mr-2" />
              List View
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search audit events..."
              className="w-full md:w-[250px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                Filter
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[300px] p-4" align="end">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Events</h4>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium">Date Range</div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">From</div>
                      <DatePicker
                        selected={dateRange.from}
                        onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">To</div>
                      <DatePicker
                        selected={dateRange.to}
                        onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium">Group By</div>
                  <Select
                    value={groupBy}
                    onValueChange={(value) => setGroupBy(value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Group by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Grouping</SelectItem>
                      <SelectItem value="day">Day</SelectItem>
                      <SelectItem value="type">Event Type</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setDateRange({});
                      setSelectedEventTypes([]);
                      setSelectedUsers([]);
                      setSelectedStatuses([]);
                      setGroupBy("day");
                    }}
                  >
                    Reset
                  </Button>
                  <Button size="sm">
                    Apply Filters
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          <div className="hidden md:flex items-center space-x-1 border rounded-md">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-none"
              onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.25))}
              disabled={zoomLevel <= 0.5}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-none"
              onClick={() => setZoomLevel(Math.min(2, zoomLevel + 0.25))}
              disabled={zoomLevel >= 2}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-[200px] w-full" />
          <Skeleton className="h-[200px] w-full" />
        </div>
      ) : filteredEvents.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="py-8 text-center">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No audit events found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Try adjusting your filters or search query
            </p>
          </CardContent>
        </Card>
      ) : (
        <div style={{ transform: `scale(${zoomLevel})`, transformOrigin: "top left" }}>
          {renderTimeline()}
        </div>
      )}
    </div>
  );
}
