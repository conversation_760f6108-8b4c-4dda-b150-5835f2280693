"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Loader2,
  AlertTriangle,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  AlertCircle,
  User,
  Building,
  FileText,
  ArrowRight,
  BarChart4,
  TrendingUp,
  TrendingDown,
  Info,
  Search,
  Filter,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { ComplianceRiskLevel } from "@/lib/compliance/types";

interface RiskAssessmentDashboardProps {
  userId?: string;
  organizationId?: string;
}

interface RiskAssessmentData {
  riskLevel: ComplianceRiskLevel;
  riskScore: number;
  riskFactors: string[];
  recommendations: string[];
  lastUpdated: string;
}

interface SanctionsCheckData {
  passed: boolean;
  matchCount: number;
  matches: Array<{
    listType: "SANCTIONS" | "PEP" | "ADVERSE_MEDIA";
    listName: string;
    matchScore: number;
    details: Record<string, any>;
  }>;
  lastChecked: string;
}

export default function RiskAssessmentDashboard({
  userId,
  organizationId,
}: RiskAssessmentDashboardProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [riskAssessment, setRiskAssessment] = useState<RiskAssessmentData | null>(null);
  const [sanctionsCheck, setSanctionsCheck] = useState<SanctionsCheckData | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch risk assessment data
  useEffect(() => {
    async function fetchRiskData() {
      if (!session?.user) return;

      setIsLoading(true);
      setError(null);

      try {
        // Construct query parameters
        const params = new URLSearchParams();
        if (userId) params.set("userId", userId);
        if (organizationId) params.set("organizationId", organizationId);

        // Fetch risk assessment data
        const response = await fetch(`/api/compliance/risk-assessment?${params.toString()}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch risk assessment data");
        }

        const data = await response.json();

        if (data.riskAssessment) {
          setRiskAssessment({
            riskLevel: data.riskAssessment.riskLevel,
            riskScore: data.riskAssessment.details.riskScore || 0,
            riskFactors: data.riskAssessment.details.riskFactors || [],
            recommendations: data.riskAssessment.details.recommendations || [],
            lastUpdated: data.riskAssessment.createdAt,
          });
        }

        if (data.sanctionsCheck) {
          setSanctionsCheck({
            passed: data.sanctionsCheck.result === "PASS",
            matchCount: data.sanctionsCheck.details.matches?.length || 0,
            matches: data.sanctionsCheck.details.matches || [],
            lastChecked: data.sanctionsCheck.createdAt,
          });
        }
      } catch (error) {
        console.error("Error fetching risk assessment data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchRiskData();
  }, [session, userId, organizationId]);

  // Refresh risk assessment
  const handleRefreshRiskAssessment = async () => {
    if (!session?.user) return;

    setIsRefreshing(true);
    setError(null);

    try {
      // Construct request body
      const body: any = {};
      if (userId) body.userId = userId;
      if (organizationId) body.organizationId = organizationId;

      // Perform risk assessment
      const response = await fetch("/api/compliance/risk-assessment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to perform risk assessment");
      }

      const data = await response.json();

      setRiskAssessment({
        riskLevel: data.result.riskLevel,
        riskScore: data.result.riskScore,
        riskFactors: data.result.riskFactors,
        recommendations: data.result.recommendations,
        lastUpdated: new Date().toISOString(),
      });

      toast({
        title: "Risk Assessment Updated",
        description: `Risk assessment completed with ${data.result.riskLevel} risk level`,
      });
    } catch (error) {
      console.error("Error refreshing risk assessment:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform risk assessment",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Perform sanctions check
  const handleSanctionsCheck = async () => {
    if (!session?.user) return;

    setIsRefreshing(true);
    setError(null);

    try {
      // Construct request body
      const body: any = { action: "sanctions" };
      if (userId) body.userId = userId;
      if (organizationId) body.organizationId = organizationId;

      // Perform sanctions check
      const response = await fetch("/api/compliance/risk-assessment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to perform sanctions check");
      }

      const data = await response.json();

      setSanctionsCheck({
        passed: data.result.passed,
        matchCount: data.result.matches.length,
        matches: data.result.matches,
        lastChecked: new Date().toISOString(),
      });

      toast({
        title: "Sanctions Check Completed",
        description: data.message,
      });
    } catch (error) {
      console.error("Error performing sanctions check:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform sanctions check",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Get risk level color
  const getRiskLevelColor = (level: ComplianceRiskLevel) => {
    switch (level) {
      case ComplianceRiskLevel.LOW:
        return "bg-green-500";
      case ComplianceRiskLevel.MEDIUM:
        return "bg-yellow-500";
      case ComplianceRiskLevel.HIGH:
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get risk level badge variant
  const getRiskLevelBadge = (level: ComplianceRiskLevel) => {
    switch (level) {
      case ComplianceRiskLevel.LOW:
        return "success";
      case ComplianceRiskLevel.MEDIUM:
        return "warning";
      case ComplianceRiskLevel.HIGH:
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      {isLoading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading risk assessment data...</span>
          </CardContent>
        </Card>
      ) : error ? (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">
              <BarChart4 className="mr-2 h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="risk-factors">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Risk Factors
            </TabsTrigger>
            <TabsTrigger value="sanctions">
              <Shield className="mr-2 h-4 w-4" />
              Sanctions & PEP
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Risk Assessment Summary</CardTitle>
                    <CardDescription>
                      Overall compliance risk assessment for {userId ? "this user" : "your organization"}
                    </CardDescription>
                  </div>
                  <InfoTooltip
                    content={
                      <div className="space-y-2">
                        <p>This dashboard shows your current compliance risk profile based on various factors.</p>
                        <p>A lower risk score indicates better compliance standing.</p>
                      </div>
                    }
                  />
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Card className="relative overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
                        <InfoTooltip
                          content={
                            <div>
                              <p>Your overall risk classification:</p>
                              <ul className="mt-1">
                                <li><span className="font-medium text-green-500">Low</span>: 0-30 points</li>
                                <li><span className="font-medium text-yellow-500">Medium</span>: 31-70 points</li>
                                <li><span className="font-medium text-red-500">High</span>: 71-100 points</li>
                              </ul>
                            </div>
                          }
                          side="right"
                        />
                      </div>
                    </CardHeader>
                    <CardContent>
                      {riskAssessment ? (
                        <div className="flex items-center">
                          <Badge variant={getRiskLevelBadge(riskAssessment.riskLevel)} className="mr-2">
                            {riskAssessment.riskLevel}
                          </Badge>
                          {riskAssessment.riskLevel === ComplianceRiskLevel.LOW && (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                          {riskAssessment.riskLevel === ComplianceRiskLevel.MEDIUM && (
                            <AlertCircle className="h-4 w-4 text-yellow-500" />
                          )}
                          {riskAssessment.riskLevel === ComplianceRiskLevel.HIGH && (
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          )}
                        </div>
                      ) : (
                        <Badge variant="outline">Not Assessed</Badge>
                      )}
                    </CardContent>
                    {riskAssessment && riskAssessment.riskLevel === ComplianceRiskLevel.HIGH && (
                      <div className="absolute -right-12 top-2 rotate-45 bg-red-500 text-white text-xs py-1 px-12">
                        Attention Needed
                      </div>
                    )}
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
                        <InfoTooltip
                          content={
                            <div>
                              <p>Your numerical risk score on a scale of 0-100.</p>
                              <p>This score is calculated based on multiple risk factors including transaction patterns, documentation, and jurisdictional considerations.</p>
                            </div>
                          }
                          side="right"
                        />
                      </div>
                    </CardHeader>
                    <CardContent>
                      {riskAssessment ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-2xl font-bold">{riskAssessment.riskScore}</span>
                            <span className="text-xs text-muted-foreground">/100</span>
                          </div>
                          <Progress
                            value={riskAssessment.riskScore}
                            max={100}
                            className={`h-2 ${
                              riskAssessment.riskScore < 30
                                ? "bg-green-100"
                                : riskAssessment.riskScore < 70
                                ? "bg-yellow-100"
                                : "bg-red-100"
                            }`}
                            indicatorClassName={
                              riskAssessment.riskScore < 30
                                ? "bg-green-500"
                                : riskAssessment.riskScore < 70
                                ? "bg-yellow-500"
                                : "bg-red-500"
                            }
                          />
                        </div>
                      ) : (
                        <span className="text-muted-foreground">No data</span>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm font-medium">Sanctions Check</CardTitle>
                        <InfoTooltip
                          content={
                            <div>
                              <p>Verification against global sanctions and PEP (Politically Exposed Persons) lists.</p>
                              <p>This check is critical for regulatory compliance and preventing financial crimes.</p>
                            </div>
                          }
                          side="right"
                        />
                      </div>
                    </CardHeader>
                    <CardContent>
                      {sanctionsCheck ? (
                        <div className="flex items-center">
                          <Badge
                            variant={sanctionsCheck.passed ? "success" : "destructive"}
                            className="mr-2"
                          >
                            {sanctionsCheck.passed ? "PASSED" : "FAILED"}
                          </Badge>
                          {sanctionsCheck.passed ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                        </div>
                      ) : (
                        <Badge variant="outline">Not Checked</Badge>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {riskAssessment ? (
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>
                            {new Date(riskAssessment.lastUpdated).toLocaleDateString()}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Never</span>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {riskAssessment && riskAssessment.recommendations.length > 0 && (
                  <Collapsible className="mt-6 border rounded-md p-4">
                    <CollapsibleTrigger className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <Info className="h-5 w-5 text-blue-500 mr-2" />
                        <h3 className="text-lg font-medium">Recommendations</h3>
                      </div>
                      <ChevronDown className="h-4 w-4 text-muted-foreground transition-transform ui-expanded:rotate-180" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="pt-4">
                      <ul className="space-y-4">
                        {riskAssessment.recommendations.map((recommendation, index) => (
                          <li key={index} className="flex items-start bg-muted/30 p-3 rounded-md">
                            <div className="p-1 bg-blue-100 rounded-full mr-3 mt-0.5">
                              <Info className="h-4 w-4 text-blue-500 flex-shrink-0" />
                            </div>
                            <div className="space-y-2">
                              <p className="font-medium">{recommendation}</p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => router.push("/compliance/actions")}
                              >
                                Take Action
                                <ArrowRight className="ml-2 h-3 w-3" />
                              </Button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </CollapsibleContent>
                  </Collapsible>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="text-sm text-muted-foreground">
                  {riskAssessment ? (
                    <span>
                      Last assessment: {new Date(riskAssessment.lastUpdated).toLocaleString()}
                    </span>
                  ) : (
                    <span>No previous assessment</span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSanctionsCheck}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Shield className="mr-2 h-4 w-4" />
                    )}
                    Check Sanctions
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleRefreshRiskAssessment}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-4 w-4" />
                    )}
                    Refresh Assessment
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="risk-factors" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Risk Factors</CardTitle>
                    <CardDescription>
                      Factors contributing to the overall risk assessment
                    </CardDescription>
                  </div>
                  <InfoTooltip
                    content={
                      <div className="space-y-2">
                        <p>Risk factors are specific elements that contribute to your overall risk score.</p>
                        <p>Addressing these factors can help reduce your compliance risk level.</p>
                      </div>
                    }
                  />
                </div>
              </CardHeader>
              <CardContent>
                {riskAssessment && riskAssessment.riskFactors.length > 0 ? (
                  <div className="space-y-4">
                    {riskAssessment.riskFactors.map((factor, index) => {
                      // Group risk factors by category
                      const isTransactionRisk = factor.toLowerCase().includes("transaction");
                      const isDocumentRisk = factor.toLowerCase().includes("document") || factor.toLowerCase().includes("verification");
                      const isJurisdictionRisk = factor.toLowerCase().includes("jurisdiction") || factor.toLowerCase().includes("country");

                      let riskCategory = "General Risk";
                      let riskIcon = <AlertTriangle className="h-5 w-5 text-yellow-500" />;

                      if (isTransactionRisk) {
                        riskCategory = "Transaction Risk";
                        riskIcon = <BarChart4 className="h-5 w-5 text-amber-500" />;
                      } else if (isDocumentRisk) {
                        riskCategory = "Documentation Risk";
                        riskIcon = <FileText className="h-5 w-5 text-blue-500" />;
                      } else if (isJurisdictionRisk) {
                        riskCategory = "Jurisdictional Risk";
                        riskIcon = <Building className="h-5 w-5 text-purple-500" />;
                      }

                      return (
                        <Collapsible key={index} className="border rounded-md">
                          <CollapsibleTrigger className="flex items-center justify-between w-full p-3 text-left">
                            <div className="flex items-center space-x-3">
                              <div className="p-1.5 rounded-full bg-muted">
                                {riskIcon}
                              </div>
                              <div>
                                <div className="font-medium">{riskCategory}</div>
                                <div className="text-sm text-muted-foreground line-clamp-1">{factor}</div>
                              </div>
                            </div>
                            <ChevronDown className="h-4 w-4 text-muted-foreground transition-transform ui-expanded:rotate-180" />
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            <div className="p-3 pt-0 border-t">
                              <div className="space-y-3">
                                <p>{factor}</p>

                                {/* Remediation suggestions based on risk type */}
                                <div className="mt-2">
                                  <h4 className="text-sm font-medium mb-1">Remediation Steps:</h4>
                                  <ul className="text-sm space-y-1 list-disc pl-5">
                                    {isTransactionRisk && (
                                      <>
                                        <li>Review recent transaction patterns</li>
                                        <li>Implement transaction limits if needed</li>
                                        <li>Document source of funds for large transactions</li>
                                      </>
                                    )}
                                    {isDocumentRisk && (
                                      <>
                                        <li>Submit missing or updated documentation</li>
                                        <li>Ensure all verification documents are current</li>
                                        <li>Complete any pending verification steps</li>
                                      </>
                                    )}
                                    {isJurisdictionRisk && (
                                      <>
                                        <li>Provide additional documentation for high-risk jurisdictions</li>
                                        <li>Implement enhanced due diligence measures</li>
                                        <li>Consider restructuring operations if possible</li>
                                      </>
                                    )}
                                    {!isTransactionRisk && !isDocumentRisk && !isJurisdictionRisk && (
                                      <>
                                        <li>Review compliance policies and procedures</li>
                                        <li>Implement additional controls as needed</li>
                                        <li>Contact compliance support for guidance</li>
                                      </>
                                    )}
                                  </ul>
                                </div>

                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => router.push("/compliance/remediation")}
                                >
                                  View Detailed Remediation Plan
                                  <ArrowRight className="ml-2 h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      );
                    })}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                    <h3 className="text-lg font-medium">No Risk Factors Identified</h3>
                    <p className="text-muted-foreground mt-2">
                      {riskAssessment
                        ? "No significant risk factors were identified in the assessment."
                        : "No risk assessment has been performed yet."}
                    </p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleRefreshRiskAssessment}
                  disabled={isRefreshing}
                >
                  {isRefreshing ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  Refresh Assessment
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="sanctions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sanctions & PEP Screening</CardTitle>
                <CardDescription>
                  Results from screening against sanctions and politically exposed persons lists
                </CardDescription>
              </CardHeader>
              <CardContent>
                {sanctionsCheck ? (
                  <div className="space-y-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-full ${sanctionsCheck.passed ? 'bg-green-100' : 'bg-red-100'}`}>
                        {sanctionsCheck.passed ? (
                          <CheckCircle className="h-6 w-6 text-green-500" />
                        ) : (
                          <XCircle className="h-6 w-6 text-red-500" />
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium">
                          {sanctionsCheck.passed ? "No Significant Matches" : "Potential Matches Found"}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {sanctionsCheck.passed
                            ? "No significant matches found in sanctions or PEP lists."
                            : `Found ${sanctionsCheck.matchCount} potential matches that require review.`}
                        </p>
                      </div>
                    </div>

                    <Separator />

                    {sanctionsCheck.matches.length > 0 ? (
                      <div className="space-y-4">
                        <h3 className="text-md font-medium">Match Details</h3>
                        {sanctionsCheck.matches.map((match, index) => (
                          <div key={index} className="border rounded-md p-4 space-y-2">
                            <div className="flex justify-between">
                              <div className="flex items-center">
                                <Badge variant={match.listType === "SANCTIONS" ? "destructive" : "warning"} className="mr-2">
                                  {match.listType}
                                </Badge>
                                <span className="font-medium">{match.listName}</span>
                              </div>
                              <Badge variant="outline">
                                Match Score: {(match.matchScore * 100).toFixed(0)}%
                              </Badge>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              <p>Details: {match.details.description || "No additional details available"}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <Shield className="h-12 w-12 text-green-500 mx-auto mb-4" />
                        <h3 className="text-lg font-medium">No Matches Found</h3>
                        <p className="text-muted-foreground mt-2">
                          No matches were found in sanctions or PEP lists.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No Screening Performed</h3>
                    <p className="text-muted-foreground mt-2">
                      No sanctions or PEP screening has been performed yet.
                    </p>
                    <Button
                      onClick={handleSanctionsCheck}
                      disabled={isRefreshing}
                      className="mt-4"
                    >
                      {isRefreshing ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Shield className="mr-2 h-4 w-4" />
                      )}
                      Perform Screening
                    </Button>
                  </div>
                )}
              </CardContent>
              {sanctionsCheck && (
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">
                    Last checked: {new Date(sanctionsCheck.lastChecked).toLocaleString()}
                  </div>
                  <Button
                    onClick={handleSanctionsCheck}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-4 w-4" />
                    )}
                    Refresh Screening
                  </Button>
                </CardFooter>
              )}
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
