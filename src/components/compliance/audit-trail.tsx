"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { HelpCard } from "@/components/ui/contextual-help";
import { AnimatedCard } from "@/components/ui/animated";
import {
  Clock,
  Search,
  Filter,
  Download,
  FileText,
  ChevronRight,
  ChevronDown,
  Users,
  Building,
  Wallet,
  FileCheck,
  Shield,
  AlertCircle,
  Landmark,
  RefreshCw,
  Calendar,
  ArrowDownUp,
  Eye,
  Info
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Sample audit log data
const AUDIT_LOGS = [
  {
    id: "log-001",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    action: "USER_LOGIN",
    actionDescription: "User login successful",
    entity: "John Smith",
    entityType: "user",
    entityId: "user-123",
    component: "AUTH",
    severity: "INFO",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    metadata: {
      method: "password",
      location: "New York, USA"
    }
  },
  {
    id: "log-002",
    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    action: "KYC_VERIFICATION_APPROVED",
    actionDescription: "KYC verification approved",
    entity: "Jane Doe",
    entityType: "user",
    entityId: "user-456",
    component: "KYC",
    severity: "INFO",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    metadata: {
      documentType: "PASSPORT",
      verifiedBy: "admin-789"
    }
  },
  {
    id: "log-003",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    action: "TRANSACTION_CREATED",
    actionDescription: "Carbon credit transaction created",
    entity: "Acme Corp",
    entityType: "organization",
    entityId: "org-123",
    component: "MARKETPLACE",
    severity: "INFO",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    metadata: {
      transactionId: "tx-456",
      amount: "100 tCO2e",
      price: "$2500"
    }
  },
  {
    id: "log-004",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
    action: "AML_RISK_ALERT",
    actionDescription: "AML risk alert triggered",
    entity: "XYZ Ltd",
    entityType: "organization",
    entityId: "org-789",
    component: "AML",
    severity: "WARNING",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    metadata: {
      riskLevel: "MEDIUM",
      reason: "Unusual transaction pattern",
      alertId: "alert-123"
    }
  },
  {
    id: "log-005",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    action: "PROJECT_VERIFICATION_COMPLETED",
    actionDescription: "Carbon project verification completed",
    entity: "Green Energy Project",
    entityType: "project",
    entityId: "project-456",
    component: "VERIFICATION",
    severity: "INFO",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    metadata: {
      verifier: "Carbon Verify Inc.",
      standard: "VERRA",
      creditsIssued: "5000 tCO2e"
    }
  },
  {
    id: "log-006",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 36), // 1.5 days ago
    action: "ADMIN_SETTINGS_CHANGED",
    actionDescription: "System settings modified",
    entity: "Admin User",
    entityType: "user",
    entityId: "admin-789",
    component: "ADMIN",
    severity: "WARNING",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    metadata: {
      setting: "AML_RISK_THRESHOLDS",
      oldValue: "{ low: 0.2, medium: 0.5, high: 0.8 }",
      newValue: "{ low: 0.3, medium: 0.6, high: 0.9 }"
    }
  },
  {
    id: "log-007",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
    action: "FAILED_LOGIN_ATTEMPT",
    actionDescription: "Failed login attempt",
    entity: "Unknown",
    entityType: "user",
    entityId: "unknown",
    component: "AUTH",
    severity: "ERROR",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    metadata: {
      username: "<EMAIL>",
      reason: "Invalid password",
      attempts: "3"
    }
  }
];

// Component icons
const COMPONENT_ICONS: Record<string, React.ReactNode> = {
  AUTH: <Users className="h-4 w-4" />,
  KYC: <Shield className="h-4 w-4" />,
  AML: <AlertCircle className="h-4 w-4" />,
  MARKETPLACE: <Wallet className="h-4 w-4" />,
  VERIFICATION: <FileCheck className="h-4 w-4" />,
  ADMIN: <Landmark className="h-4 w-4" />
};

// Severity badges
const SEVERITY_BADGES: Record<string, React.ReactNode> = {
  INFO: <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Info</Badge>,
  WARNING: <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Warning</Badge>,
  ERROR: <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Error</Badge>,
  CRITICAL: <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">Critical</Badge>
};

// Entity type icons
const ENTITY_TYPE_ICONS: Record<string, React.ReactNode> = {
  user: <Users className="h-4 w-4" />,
  organization: <Building className="h-4 w-4" />,
  project: <FileText className="h-4 w-4" />,
  system: <Landmark className="h-4 w-4" />,
  wallet: <Wallet className="h-4 w-4" />
};

interface AuditTrailProps {
  userId?: string;
  organizationId?: string;
  selectedComponent?: string;
}

export function AuditTrail({
  userId,
  organizationId,
  selectedComponent: initialComponent,
}: AuditTrailProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedComponent, setSelectedComponent] = useState<string>(initialComponent || "ALL");
  const [selectedSeverity, setSelectedSeverity] = useState<string>("ALL");
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)); // 7 days ago
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [expandedLog, setExpandedLog] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Filter logs based on search query, component, severity, and date range
  const filteredLogs = AUDIT_LOGS.filter((log) => {
    const matchesSearch =
      log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.actionDescription.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.entity.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesComponent = selectedComponent === "ALL" || log.component === selectedComponent;
    const matchesSeverity = selectedSeverity === "ALL" || log.severity === selectedSeverity;

    const matchesDateRange =
      (!startDate || log.timestamp >= startDate) &&
      (!endDate || log.timestamp <= endDate);

    return matchesSearch && matchesComponent && matchesSeverity && matchesDateRange;
  }).sort((a, b) => {
    if (sortOrder === "asc") {
      return a.timestamp.getTime() - b.timestamp.getTime();
    } else {
      return b.timestamp.getTime() - a.timestamp.getTime();
    }
  });

  // Handle log export
  const handleExportLogs = () => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Logs Exported",
        description: "Audit logs have been exported successfully.",
      });
      setIsLoading(false);
    }, 1000);
  };

  // Toggle log details
  const toggleLogDetails = (logId: string) => {
    if (expandedLog === logId) {
      setExpandedLog(null);
    } else {
      setExpandedLog(logId);
    }
  };

  // Render filter controls
  const renderFilterControls = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="text-sm font-medium mb-1 block">Component</label>
          <Select value={selectedComponent} onValueChange={setSelectedComponent}>
            <SelectTrigger>
              <SelectValue placeholder="Select component" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Components</SelectItem>
              <SelectItem value="AUTH">Authentication</SelectItem>
              <SelectItem value="KYC">KYC Verification</SelectItem>
              <SelectItem value="AML">AML Monitoring</SelectItem>
              <SelectItem value="MARKETPLACE">Marketplace</SelectItem>
              <SelectItem value="VERIFICATION">Verification</SelectItem>
              <SelectItem value="ADMIN">Administration</SelectItem>
            </SelectContent>
          </Select>
          {initialComponent && initialComponent !== "ALL" && (
            <p className="text-xs text-muted-foreground mt-1">
              Filtered to show only {initialComponent} logs
            </p>
          )}
        </div>

        <div>
          <label className="text-sm font-medium mb-1 block">Severity</label>
          <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
            <SelectTrigger>
              <SelectValue placeholder="Select severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Severities</SelectItem>
              <SelectItem value="INFO">Info</SelectItem>
              <SelectItem value="WARNING">Warning</SelectItem>
              <SelectItem value="ERROR">Error</SelectItem>
              <SelectItem value="CRITICAL">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium mb-1 block">Start Date</label>
          <DatePicker date={startDate} setDate={setStartDate} />
        </div>

        <div>
          <label className="text-sm font-medium mb-1 block">End Date</label>
          <DatePicker date={endDate} setDate={setEndDate} />
        </div>
      </div>
    );
  };

  // Render audit log table
  const renderAuditLogTable = () => {
    return (
      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            <tr className="border-b bg-muted/50">
              <th className="py-3 px-4 text-left font-medium">
                <div className="flex items-center cursor-pointer" onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}>
                  Timestamp
                  <ArrowDownUp className="h-4 w-4 ml-1" />
                </div>
              </th>
              <th className="py-3 px-4 text-left font-medium">Action</th>
              <th className="py-3 px-4 text-left font-medium">Entity</th>
              <th className="py-3 px-4 text-left font-medium">Component</th>
              <th className="py-3 px-4 text-left font-medium">Severity</th>
              <th className="py-3 px-4 text-right font-medium">Details</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={6} className="py-8 text-center">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">Loading audit logs...</p>
                </td>
              </tr>
            ) : filteredLogs.length === 0 ? (
              <tr>
                <td colSpan={6} className="py-8 text-center">
                  <Clock className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No audit logs found</p>
                  <p className="text-sm text-muted-foreground mt-1">Try adjusting your filters or search query</p>
                </td>
              </tr>
            ) : (
              filteredLogs.map((log) => (
                <>
                  <tr key={log.id} className={`border-b hover:bg-muted/50 ${expandedLog === log.id ? 'bg-muted/50' : ''}`}>
                    <td className="py-3 px-4">
                      <div className="font-medium">{format(log.timestamp, "MMM d, yyyy")}</div>
                      <div className="text-xs text-muted-foreground">{format(log.timestamp, "h:mm:ss a")}</div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="font-medium">{log.action.replace(/_/g, ' ')}</div>
                      <div className="text-xs text-muted-foreground">{log.actionDescription}</div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        {ENTITY_TYPE_ICONS[log.entityType]}
                        <span className="ml-1">{log.entity}</span>
                      </div>
                      <div className="text-xs text-muted-foreground">ID: {log.entityId}</div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant="outline" className="flex items-center w-fit">
                        {COMPONENT_ICONS[log.component]}
                        <span className="ml-1">{log.component}</span>
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      {SEVERITY_BADGES[log.severity]}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleLogDetails(log.id)}
                        className="flex items-center"
                      >
                        {expandedLog === log.id ? (
                          <>
                            Hide
                            <ChevronDown className="h-4 w-4 ml-1" />
                          </>
                        ) : (
                          <>
                            View
                            <ChevronRight className="h-4 w-4 ml-1" />
                          </>
                        )}
                      </Button>
                    </td>
                  </tr>
                  {expandedLog === log.id && (
                    <tr className="bg-muted/30">
                      <td colSpan={6} className="py-3 px-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium mb-2">Request Details</h4>
                            <div className="space-y-2 text-sm">
                              <div className="grid grid-cols-3 gap-2">
                                <div className="text-muted-foreground">IP Address:</div>
                                <div className="col-span-2">{log.ipAddress}</div>
                              </div>
                              <div className="grid grid-cols-3 gap-2">
                                <div className="text-muted-foreground">User Agent:</div>
                                <div className="col-span-2 text-xs">{log.userAgent}</div>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium mb-2">Additional Information</h4>
                            <div className="space-y-2 text-sm">
                              {Object.entries(log.metadata).map(([key, value]) => (
                                <div key={key} className="grid grid-cols-3 gap-2">
                                  <div className="text-muted-foreground">{key.replace(/([A-Z])/g, ' $1').trim()}:</div>
                                  <div className="col-span-2">{value}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-end mt-4">
                          <Button variant="outline" size="sm" className="mr-2">
                            <Eye className="h-4 w-4 mr-1" />
                            View Full Log
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-1" />
                            Export
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Audit Trail</h2>
          <p className="text-muted-foreground">
            Comprehensive record of all compliance and system activities
          </p>
        </div>

        <Button onClick={handleExportLogs} disabled={isLoading}>
          {isLoading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Export Logs
            </>
          )}
        </Button>
      </div>

      <Separator />

      <HelpCard
        content={{
          title: "Audit Trail",
          description: "The audit trail provides a comprehensive record of all activities and events in the system.",
          type: "info",
          tips: [
            "Use filters to narrow down the logs by component, severity, or date range",
            "Click on a log entry to view detailed information",
            "Export logs for compliance reporting or record-keeping",
            "Audit logs are retained for 7 years in accordance with regulatory requirements"
          ]
        }}
        className="mb-6"
      />

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Refine audit logs by component, severity, or date range</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search audit logs..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {renderFilterControls()}
          </div>
        </CardContent>
      </Card>

      <AnimatedCard>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Audit Logs</CardTitle>
            <Badge variant="outline" className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {filteredLogs.length} {filteredLogs.length === 1 ? 'entry' : 'entries'}
            </Badge>
          </div>
          <CardDescription>
            Showing {filteredLogs.length} of {AUDIT_LOGS.length} audit log entries
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          {renderAuditLogTable()}
        </CardContent>
        <CardFooter className="flex justify-between py-4">
          <div className="text-sm text-muted-foreground">
            <Info className="h-4 w-4 inline mr-1" />
            Audit logs are retained for 7 years
          </div>
          <Button variant="outline" size="sm" disabled>
            Load More
          </Button>
        </CardFooter>
      </AnimatedCard>
    </div>
  );
