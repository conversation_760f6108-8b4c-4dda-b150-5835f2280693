"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { AnimatedCard } from "@/components/ui/animated";
import { HelpCard } from "@/components/ui/contextual-help";
import {
  FileText,
  Download,
  Share2,
  Printer,
  Eye,
  BarChart3,
  PieChart,
  LineChart,
  Table,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

interface ReportPreviewProps {
  reportId: string;
  reportType: string;
  reportName: string;
  startDate: Date;
  endDate: Date;
  format: string;
  filters?: Record<string, any>;
}

export function ReportPreview({
  reportId,
  reportType,
  reportName,
  startDate,
  endDate,
  format,
  filters
}: ReportPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [previewData, setPreviewData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("summary");
  const [error, setError] = useState<string | null>(null);

  // Fetch preview data
  useEffect(() => {
    const fetchPreviewData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Generate mock preview data based on report type
        let mockData: any = {
          summary: {
            title: reportName,
            period: `${format(startDate, "MMM d, yyyy")} - ${format(endDate, "MMM d, yyyy")}`,
            generatedAt: new Date().toISOString(),
            totalRecords: Math.floor(Math.random() * 1000) + 100,
          }
        };

        // Add type-specific data
        switch (reportType) {
          case "KYC":
            mockData.summary.verificationStats = {
              verified: Math.floor(Math.random() * 80) + 20,
              pending: Math.floor(Math.random() * 30),
              rejected: Math.floor(Math.random() * 10),
              expired: Math.floor(Math.random() * 5),
            };
            mockData.charts = {
              verificationStatus: [
                { name: "Verified", value: mockData.summary.verificationStats.verified },
                { name: "Pending", value: mockData.summary.verificationStats.pending },
                { name: "Rejected", value: mockData.summary.verificationStats.rejected },
                { name: "Expired", value: mockData.summary.verificationStats.expired },
              ],
              verificationTrend: Array.from({ length: 6 }, (_, i) => ({
                month: format(new Date(new Date().setMonth(new Date().getMonth() - 5 + i)), "MMM"),
                verified: Math.floor(Math.random() * 30) + 10,
                pending: Math.floor(Math.random() * 15),
                rejected: Math.floor(Math.random() * 5),
              })),
            };
            break;
          
          case "AML":
            mockData.summary.riskStats = {
              lowRisk: Math.floor(Math.random() * 70) + 30,
              mediumRisk: Math.floor(Math.random() * 40),
              highRisk: Math.floor(Math.random() * 20),
              criticalRisk: Math.floor(Math.random() * 5),
            };
            mockData.charts = {
              riskDistribution: [
                { name: "Low Risk", value: mockData.summary.riskStats.lowRisk },
                { name: "Medium Risk", value: mockData.summary.riskStats.mediumRisk },
                { name: "High Risk", value: mockData.summary.riskStats.highRisk },
                { name: "Critical Risk", value: mockData.summary.riskStats.criticalRisk },
              ],
              alertTrend: Array.from({ length: 6 }, (_, i) => ({
                month: format(new Date(new Date().setMonth(new Date().getMonth() - 5 + i)), "MMM"),
                alerts: Math.floor(Math.random() * 30) + 5,
              })),
            };
            break;
          
          default:
            // Generic data for other report types
            mockData.summary.stats = {
              completed: Math.floor(Math.random() * 80) + 20,
              inProgress: Math.floor(Math.random() * 30),
              pending: Math.floor(Math.random() * 20),
            };
            mockData.charts = {
              statusDistribution: [
                { name: "Completed", value: mockData.summary.stats.completed },
                { name: "In Progress", value: mockData.summary.stats.inProgress },
                { name: "Pending", value: mockData.summary.stats.pending },
              ],
              trend: Array.from({ length: 6 }, (_, i) => ({
                month: format(new Date(new Date().setMonth(new Date().getMonth() - 5 + i)), "MMM"),
                value: Math.floor(Math.random() * 50) + 20,
              })),
            };
        }

        setPreviewData(mockData);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching report preview:", error);
        setError("Failed to load report preview");
        setIsLoading(false);
      }
    };

    fetchPreviewData();
  }, [reportId, reportType, reportName, startDate, endDate, format, filters]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <Separator className="my-4" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-[200px]" />
          <Skeleton className="h-[200px]" />
        </div>
        <Skeleton className="h-[300px] mt-4" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className="border-destructive/50">
        <CardHeader>
          <CardTitle className="text-destructive">Preview Error</CardTitle>
          <CardDescription>
            We couldn't generate a preview for this report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <AlertTriangle className="h-12 w-12 text-destructive" />
          </div>
          <p className="text-center text-destructive">{error}</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full" onClick={() => setIsLoading(true)}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Report Preview",
          description: "This is a preview of how your report will look when generated.",
          type: "info",
          tips: [
            "The preview shows sample data to represent the report structure",
            "Actual report content will reflect real data from your selected date range",
            "You can adjust report settings and regenerate the preview"
          ]
        }}
        className="mb-6"
      />

      <AnimatedCard>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{previewData.summary.title}</CardTitle>
              <CardDescription>
                {previewData.summary.period}
              </CardDescription>
            </div>
            <Badge variant="outline" className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              Preview
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full justify-start px-6 pt-2">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="charts">Charts</TabsTrigger>
              <TabsTrigger value="data">Data</TabsTrigger>
            </TabsList>
            <Separator />
            <div className="p-6">
              <TabsContent value="summary" className="mt-0">
                {renderSummaryTab()}
              </TabsContent>
              <TabsContent value="charts" className="mt-0">
                {renderChartsTab()}
              </TabsContent>
              <TabsContent value="data" className="mt-0">
                {renderDataTab()}
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between border-t bg-muted/50 px-6 py-3">
          <div className="text-sm text-muted-foreground">
            <FileText className="h-3 w-3 inline mr-1" />
            Sample preview with mock data
          </div>
          <Button variant="outline" size="sm">
            <Eye className="h-3 w-3 mr-1" />
            Full Preview
          </Button>
        </CardFooter>
      </AnimatedCard>
    </div>
  );

  // Render summary tab content
  function renderSummaryTab() {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {renderSummaryCards()}
        </div>
      </div>
    );
  }

  // Render charts tab content
  function renderChartsTab() {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Distribution</CardTitle>
            </CardHeader>
            <CardContent className="h-[200px] flex items-center justify-center">
              <div className="text-center">
                <PieChart className="h-16 w-16 mx-auto mb-2 text-primary/60" />
                <p className="text-sm text-muted-foreground">Chart visualization</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Trend Analysis</CardTitle>
            </CardHeader>
            <CardContent className="h-[200px] flex items-center justify-center">
              <div className="text-center">
                <LineChart className="h-16 w-16 mx-auto mb-2 text-primary/60" />
                <p className="text-sm text-muted-foreground">Chart visualization</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Render data tab content
  function renderDataTab() {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Data Table</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            <div className="text-center">
              <Table className="h-16 w-16 mx-auto mb-2 text-primary/60" />
              <p className="text-sm text-muted-foreground">Data table visualization</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render summary cards based on report type
  function renderSummaryCards() {
    if (reportType === "KYC") {
      const { verified, pending, rejected, expired } = previewData.summary.verificationStats;
      const total = verified + pending + rejected + expired;
      
      return (
        <>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Verified</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{verified}</div>
              <p className="text-xs text-muted-foreground">{Math.round((verified / total) * 100)}% of total</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{pending}</div>
              <p className="text-xs text-muted-foreground">{Math.round((pending / total) * 100)}% of total</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Issues</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{rejected + expired}</div>
              <p className="text-xs text-muted-foreground">{Math.round(((rejected + expired) / total) * 100)}% of total</p>
            </CardContent>
          </Card>
        </>
      );
    } else if (reportType === "AML") {
      const { lowRisk, mediumRisk, highRisk, criticalRisk } = previewData.summary.riskStats;
      const total = lowRisk + mediumRisk + highRisk + criticalRisk;
      
      return (
        <>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Low Risk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{lowRisk}</div>
              <p className="text-xs text-muted-foreground">{Math.round((lowRisk / total) * 100)}% of total</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Medium Risk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{mediumRisk}</div>
              <p className="text-xs text-muted-foreground">{Math.round((mediumRisk / total) * 100)}% of total</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">High/Critical Risk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{highRisk + criticalRisk}</div>
              <p className="text-xs text-muted-foreground">{Math.round(((highRisk + criticalRisk) / total) * 100)}% of total</p>
            </CardContent>
          </Card>
        </>
      );
    } else {
      // Generic cards for other report types
      const { completed, inProgress, pending } = previewData.summary.stats;
      const total = completed + inProgress + pending;
      
      return (
        <>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{completed}</div>
              <p className="text-xs text-muted-foreground">{Math.round((completed / total) * 100)}% of total</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">In Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{inProgress}</div>
              <p className="text-xs text-muted-foreground">{Math.round((inProgress / total) * 100)}% of total</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{pending}</div>
              <p className="text-xs text-muted-foreground">{Math.round((pending / total) * 100)}% of total</p>
            </CardContent>
          </Card>
        </>
      );
    }
  }
}
