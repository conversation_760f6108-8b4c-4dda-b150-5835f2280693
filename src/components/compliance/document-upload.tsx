"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
  Upload, 
  X, 
  AlertTriangle, 
  Camera, 
  FileText, 
  RefreshCw,
  Check,
  Trash2
} from "lucide-react";
import { ComplianceDocumentType } from "@/lib/compliance";
import { AnimatedCard } from "@/components/ui/animated";
import { motion } from "framer-motion";

interface DocumentUploadProps {
  documentType: ComplianceDocumentType;
  documentTitle: string;
  onUpload: (file: File, name: string, notes: string) => Promise<string>;
  onCancel: () => void;
}

export function DocumentUpload({ 
  documentType, 
  documentTitle, 
  onUpload, 
  onCancel 
}: DocumentUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [documentName, setDocumentName] = useState("");
  const [notes, setNotes] = useState("");
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [qualityIssues, setQualityIssues] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    setFile(selectedFile);
    
    // Create preview URL
    const objectUrl = URL.createObjectURL(selectedFile);
    setPreviewUrl(objectUrl);
    
    // Set default document name from file name
    if (!documentName) {
      const fileName = selectedFile.name.split('.')[0];
      setDocumentName(fileName);
    }
    
    // Simulate quality check
    checkDocumentQuality(selectedFile);
  };

  // Simulate document quality check
  const checkDocumentQuality = (file: File) => {
    const issues: string[] = [];
    
    // Size check (less than 200KB might be too small for a document)
    if (file.size < 200 * 1024) {
      issues.push("Image resolution may be too low for verification");
    }
    
    // File type check
    if (!['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)) {
      issues.push("File format not recommended. Please use JPG, PNG, or PDF");
    }
    
    // Simulate other checks
    if (Math.random() > 0.7) {
      issues.push("Document edges may not be fully visible");
    }
    
    setQualityIssues(issues);
  };

  // Handle camera capture
  const handleCameraCapture = () => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = "image/*";
      fileInputRef.current.capture = "environment";
      fileInputRef.current.click();
    }
  };

  // Handle file upload
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = ".jpg,.jpeg,.png,.pdf";
      fileInputRef.current.removeAttribute("capture");
      fileInputRef.current.click();
    }
  };

  // Handle document upload
  const handleUpload = async () => {
    if (!file || !documentName) return;
    
    setUploading(true);
    
    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        const newProgress = prev + 10;
        if (newProgress >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return newProgress;
      });
    }, 300);
    
    try {
      await onUpload(file, documentName, notes);
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      clearInterval(progressInterval);
      setUploading(false);
    }
  };

  // Handle retake/reupload
  const handleRetake = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    
    setFile(null);
    setPreviewUrl(null);
    setQualityIssues([]);
    setUploadProgress(0);
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium flex items-center">
        <FileText className="mr-2 h-5 w-5" />
        Upload {documentTitle}
      </h3>
      
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept=".jpg,.jpeg,.png,.pdf"
      />
      
      {!previewUrl ? (
        <Card className="border-dashed">
          <CardContent className="pt-6 pb-6 flex flex-col items-center justify-center text-center space-y-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Upload your {documentTitle}</h4>
              <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                Drag and drop or select a file from your device
              </p>
            </div>
            <div className="flex flex-wrap gap-2 justify-center">
              <Button 
                variant="outline" 
                onClick={handleFileUpload}
                className="flex items-center"
              >
                <FileText className="mr-2 h-4 w-4" />
                Select File
              </Button>
              <Button 
                variant="outline" 
                onClick={handleCameraCapture}
                className="flex items-center"
              >
                <Camera className="mr-2 h-4 w-4" />
                Take Photo
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <AnimatedCard>
          <CardContent className="pt-6 space-y-4">
            <div className="relative border rounded-md overflow-hidden">
              {file?.type.startsWith('image/') ? (
                <div className="aspect-[4/3] relative">
                  <Image 
                    src={previewUrl} 
                    alt="Document preview" 
                    fill
                    className="object-contain"
                  />
                </div>
              ) : (
                <div className="aspect-[4/3] flex items-center justify-center bg-muted">
                  <FileText className="h-12 w-12 text-muted-foreground" />
                  <span className="ml-2 font-medium">{file?.name}</span>
                </div>
              )}
              
              {qualityIssues.length > 0 && (
                <div className="absolute top-2 right-2">
                  <Badge variant="destructive" className="flex items-center">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    Quality issues
                  </Badge>
                </div>
              )}
              
              <Button 
                size="icon" 
                variant="destructive" 
                className="absolute top-2 left-2 h-8 w-8"
                onClick={handleRetake}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {qualityIssues.length > 0 && (
              <Alert variant="warning">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Document quality issues</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    {qualityIssues.map((issue, i) => (
                      <li key={i}>{issue}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="documentName">Document Name</Label>
                <Input
                  id="documentName"
                  value={documentName}
                  onChange={(e) => setDocumentName(e.target.value)}
                  placeholder={`My ${documentTitle}`}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add any additional information about this document"
                  rows={3}
                />
              </div>
            </div>
            
            {uploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} />
              </div>
            )}
            
            <div className="flex justify-between">
              <Button 
                variant="outline" 
                onClick={onCancel}
                disabled={uploading}
              >
                Cancel
              </Button>
              
              <div className="space-x-2">
                <Button 
                  variant="outline" 
                  onClick={handleRetake}
                  disabled={uploading}
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retake
                </Button>
                
                <Button 
                  onClick={handleUpload}
                  disabled={uploading || !documentName || qualityIssues.length > 0}
                >
                  {uploading ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Upload
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </AnimatedCard>
      )}
    </div>
  );
}
