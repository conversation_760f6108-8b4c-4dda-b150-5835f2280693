"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Upload, Shield, CheckCircle2 } from "lucide-react";
import { KycLevel, ComplianceDocumentType, ComplianceStatus } from "@/lib/compliance";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

// Schema for KYC verification form
const kycVerificationSchema = z.object({
  level: z.enum(["BASIC", "INTERMEDIATE", "ADVANCED"]),
  documents: z.array(
    z.object({
      type: z.string(),
      name: z.string().min(1, "Document name is required"),
      file: z.instanceof(File).refine(file => file.size > 0, "File is required"),
    })
  ).min(1, "At least one document is required"),
});

type KycVerificationFormValues = z.infer<typeof kycVerificationSchema>;

interface KycVerificationFormProps {
  userId: string;
  organizationId: string;
  currentStatus?: {
    status: ComplianceStatus;
    level: KycLevel;
    documents: {
      id: string;
      type: ComplianceDocumentType;
      name: string;
      url: string;
      status: ComplianceStatus;
    }[];
  };
}

export function KycVerificationForm({
  userId,
  organizationId,
  currentStatus,
}: KycVerificationFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [activeTab, setActiveTab] = useState<string>(currentStatus?.status === ComplianceStatus.APPROVED ? "status" : "verify");

  // Get required documents for each level
  const requiredDocuments = {
    [KycLevel.BASIC]: [
      ComplianceDocumentType.PASSPORT,
      ComplianceDocumentType.SELFIE,
    ],
    [KycLevel.INTERMEDIATE]: [
      ComplianceDocumentType.PASSPORT,
      ComplianceDocumentType.SELFIE,
      ComplianceDocumentType.PROOF_OF_ADDRESS,
    ],
    [KycLevel.ADVANCED]: [
      ComplianceDocumentType.PASSPORT,
      ComplianceDocumentType.SELFIE,
      ComplianceDocumentType.PROOF_OF_ADDRESS,
      ComplianceDocumentType.BANK_STATEMENT,
    ],
  };

  // Initialize form
  const form = useForm<KycVerificationFormValues>({
    resolver: zodResolver(kycVerificationSchema),
    defaultValues: {
      level: currentStatus?.level || KycLevel.BASIC,
      documents: [],
    },
  });

  // Watch the selected level to update required documents
  const watchLevel = form.watch("level");
  const watchDocuments = form.watch("documents");

  // Get document type label
  const getDocumentTypeLabel = (type: string): string => {
    switch (type) {
      case ComplianceDocumentType.PASSPORT:
        return "Passport";
      case ComplianceDocumentType.DRIVERS_LICENSE:
        return "Driver's License";
      case ComplianceDocumentType.NATIONAL_ID:
        return "National ID";
      case ComplianceDocumentType.UTILITY_BILL:
        return "Utility Bill";
      case ComplianceDocumentType.BANK_STATEMENT:
        return "Bank Statement";
      case ComplianceDocumentType.PROOF_OF_ADDRESS:
        return "Proof of Address";
      case ComplianceDocumentType.SELFIE:
        return "Selfie with ID";
      default:
        return type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: ComplianceStatus) => {
    switch (status) {
      case ComplianceStatus.APPROVED:
        return "success";
      case ComplianceStatus.REJECTED:
        return "destructive";
      case ComplianceStatus.IN_REVIEW:
        return "secondary";
      case ComplianceStatus.PENDING:
      case ComplianceStatus.EXPIRED:
      default:
        return "outline";
    }
  };

  // Handle file upload
  const handleFileUpload = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Create a unique ID for this upload
      const uploadId = `upload-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      
      // Initialize progress
      setUploadProgress(prev => ({ ...prev, [uploadId]: 0 }));
      
      // Create form data
      const formData = new FormData();
      formData.append("file", file);
      
      // Create XMLHttpRequest
      const xhr = new XMLHttpRequest();
      
      // Track progress
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(prev => ({ ...prev, [uploadId]: progress }));
        }
      });
      
      // Handle completion
      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response.url);
          } catch (error) {
            reject(new Error("Invalid response from server"));
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });
      
      // Handle error
      xhr.addEventListener("error", () => {
        reject(new Error("Upload failed"));
      });
      
      // Open and send request
      xhr.open("POST", "/api/upload");
      xhr.send(formData);
    });
  };

  // Form submission handler
  const onSubmit = async (data: KycVerificationFormValues) => {
    setIsSubmitting(true);

    try {
      // Upload all documents
      const uploadedDocuments = await Promise.all(
        data.documents.map(async (doc) => {
          const url = await handleFileUpload(doc.file);
          return {
            type: doc.type,
            name: doc.name,
            url,
          };
        })
      );

      // Submit KYC verification request
      const response = await fetch("/api/compliance/kyc/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          organizationId,
          level: data.level,
          documents: uploadedDocuments,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit KYC verification");
      }

      const result = await response.json();

      toast({
        title: "KYC Verification Submitted",
        description: result.message || "Your KYC verification request has been submitted successfully.",
      });

      // Switch to status tab
      setActiveTab("status");
      
      // Refresh the page to update status
      router.refresh();
    } catch (error) {
      console.error("Error submitting KYC verification:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit KYC verification",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add document field
  const addDocumentField = (type: string) => {
    const documents = form.getValues("documents") || [];
    
    // Check if document type already exists
    const existingDoc = documents.find(doc => doc.type === type);
    
    if (!existingDoc) {
      form.setValue("documents", [
        ...documents,
        {
          type,
          name: getDocumentTypeLabel(type),
          file: new File([], ""),
        },
      ]);
    }
  };

  // Remove document field
  const removeDocumentField = (index: number) => {
    const documents = form.getValues("documents") || [];
    form.setValue("documents", documents.filter((_, i) => i !== index));
  };

  // Update required documents when level changes
  const updateRequiredDocuments = () => {
    const level = form.getValues("level") as KycLevel;
    const required = requiredDocuments[level];
    
    // Add required document fields
    required.forEach(type => {
      addDocumentField(type);
    });
    
    // Remove documents that are not required for this level
    const documents = form.getValues("documents") || [];
    const updatedDocuments = documents.filter(doc => required.includes(doc.type as ComplianceDocumentType));
    form.setValue("documents", updatedDocuments);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>KYC Verification</CardTitle>
        <CardDescription>
          Complete your Know Your Customer (KYC) verification to enable trading on the platform.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="verify">Verify</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
          </TabsList>
          
          <TabsContent value="verify" className="pt-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Level</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                            // Update required documents after a short delay
                            setTimeout(updateRequiredDocuments, 100);
                          }}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a verification level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={KycLevel.BASIC}>Basic</SelectItem>
                            <SelectItem value={KycLevel.INTERMEDIATE}>Intermediate</SelectItem>
                            <SelectItem value={KycLevel.ADVANCED}>Advanced</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        {watchLevel === KycLevel.BASIC && "Basic verification allows limited trading."}
                        {watchLevel === KycLevel.INTERMEDIATE && "Intermediate verification increases your trading limits."}
                        {watchLevel === KycLevel.ADVANCED && "Advanced verification provides the highest trading limits."}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium">Required Documents</h3>
                  </div>
                  
                  {requiredDocuments[watchLevel as KycLevel].map((docType) => {
                    const docIndex = watchDocuments?.findIndex(d => d.type === docType);
                    const doc = docIndex !== -1 ? watchDocuments[docIndex] : null;
                    
                    return (
                      <div key={docType} className="rounded-lg border p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">{getDocumentTypeLabel(docType)}</h4>
                            <p className="text-sm text-muted-foreground">
                              {docType === ComplianceDocumentType.PASSPORT && "Upload a clear scan of your passport."}
                              {docType === ComplianceDocumentType.DRIVERS_LICENSE && "Upload a clear scan of your driver's license."}
                              {docType === ComplianceDocumentType.NATIONAL_ID && "Upload a clear scan of your national ID."}
                              {docType === ComplianceDocumentType.UTILITY_BILL && "Upload a recent utility bill (less than 3 months old)."}
                              {docType === ComplianceDocumentType.BANK_STATEMENT && "Upload a recent bank statement (less than 3 months old)."}
                              {docType === ComplianceDocumentType.PROOF_OF_ADDRESS && "Upload a document proving your address (less than 3 months old)."}
                              {docType === ComplianceDocumentType.SELFIE && "Upload a selfie of yourself holding your ID document."}
                            </p>
                          </div>
                          {doc && doc.file.size > 0 && (
                            <Badge variant="outline" className="ml-2">
                              {doc.file.name}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Input
                            type="file"
                            accept=".jpg,.jpeg,.png,.pdf"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                if (docIndex !== -1) {
                                  const updatedDocs = [...watchDocuments];
                                  updatedDocs[docIndex] = {
                                    ...updatedDocs[docIndex],
                                    file,
                                  };
                                  form.setValue("documents", updatedDocs);
                                } else {
                                  addDocumentField(docType);
                                  setTimeout(() => {
                                    const newDocIndex = form.getValues("documents").findIndex(d => d.type === docType);
                                    if (newDocIndex !== -1) {
                                      const updatedDocs = [...form.getValues("documents")];
                                      updatedDocs[newDocIndex] = {
                                        ...updatedDocs[newDocIndex],
                                        file,
                                      };
                                      form.setValue("documents", updatedDocs);
                                    }
                                  }, 100);
                                }
                              }
                            }}
                            disabled={isSubmitting}
                            className="flex-1"
                          />
                          
                          {doc && uploadProgress[doc.file.name] > 0 && uploadProgress[doc.file.name] < 100 && (
                            <div className="w-full mt-2">
                              <Progress value={uploadProgress[doc.file.name]} className="h-2" />
                              <p className="text-xs text-right mt-1">{uploadProgress[doc.file.name]}%</p>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Submit Verification
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </TabsContent>
          
          <TabsContent value="status" className="pt-4">
            {currentStatus ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium">Verification Status</h3>
                    <p className="text-sm text-muted-foreground">
                      Your current KYC verification status
                    </p>
                  </div>
                  <Badge variant={getStatusBadgeVariant(currentStatus.status)}>
                    {currentStatus.status}
                  </Badge>
                </div>
                
                <div className="rounded-lg border p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="font-medium">Verification Level</h4>
                      <p className="text-sm text-muted-foreground">
                        Your current verification level
                      </p>
                    </div>
                    <Badge variant="secondary">
                      {currentStatus.level}
                    </Badge>
                  </div>
                  
                  {currentStatus.status === ComplianceStatus.APPROVED && (
                    <div className="flex items-center justify-center py-6">
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
                          <CheckCircle2 className="h-8 w-8 text-green-600" />
                        </div>
                        <h3 className="text-lg font-medium">Verification Approved</h3>
                        <p className="text-sm text-muted-foreground mt-2">
                          Your KYC verification has been approved. You can now trade on the platform.
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {currentStatus.status === ComplianceStatus.IN_REVIEW && (
                    <div className="flex items-center justify-center py-6">
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 mb-4">
                          <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                        </div>
                        <h3 className="text-lg font-medium">Verification In Progress</h3>
                        <p className="text-sm text-muted-foreground mt-2">
                          Your KYC verification is being reviewed. This process may take 1-2 business days.
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {currentStatus.status === ComplianceStatus.REJECTED && (
                    <div className="flex items-center justify-center py-6">
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
                          <Shield className="h-8 w-8 text-red-600" />
                        </div>
                        <h3 className="text-lg font-medium">Verification Rejected</h3>
                        <p className="text-sm text-muted-foreground mt-2">
                          Your KYC verification was rejected. Please submit new documents.
                        </p>
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => setActiveTab("verify")}
                        >
                          Try Again
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                
                {currentStatus.documents.length > 0 && (
                  <div className="rounded-lg border p-4">
                    <h4 className="font-medium mb-4">Submitted Documents</h4>
                    <div className="space-y-4">
                      {currentStatus.documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{getDocumentTypeLabel(doc.type)}</p>
                            <p className="text-sm text-muted-foreground">{doc.name}</p>
                          </div>
                          <Badge variant={getStatusBadgeVariant(doc.status)}>
                            {doc.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No Verification Found</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  You haven't submitted any KYC verification yet. Please go to the Verify tab to get started.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => setActiveTab("verify")}
                >
                  Start Verification
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          Your information is securely stored and processed in compliance with data protection regulations.
        </p>
      </CardFooter>
    </Card>
  );
}
