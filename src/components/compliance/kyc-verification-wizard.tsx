"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import { DocumentRequirements } from "./document-requirements";
import { DocumentUpload } from "./document-upload";
import { VerificationStatusTimeline } from "./verification-status-timeline";
import { 
  KycVerificationSkeleton, 
  DocumentRequirementsSkeleton,
  DocumentUploadSkeleton,
  VerificationStatusSkeleton
} from "./kyc-verification-skeleton";
import {
  Loader2,
  Alert<PERSON>riangle,
  CheckCircle,
  FileText,
  User,
  Shield,
  ArrowLeft,
  ArrowRight,
  Info,
} from "lucide-react";
import {
  ComplianceDocumentType,
  KycLevel,
  VerificationStatus,
  VerificationStep,
} from "@/lib/compliance";

// Wizard steps
enum WizardStep {
  LEVEL_SELECTION = 1,
  DOCUMENT_REQUIREMENTS = 2,
  DOCUMENT_UPLOAD = 3,
  REVIEW_SUBMIT = 4,
}

interface KycVerificationWizardProps {
  userId?: string;
  organizationId?: string;
}

export function KycVerificationWizard({
  userId,
  organizationId,
}: KycVerificationWizardProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("verify");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<WizardStep>(WizardStep.LEVEL_SELECTION);
  const [selectedLevel, setSelectedLevel] = useState<KycLevel>(KycLevel.BASIC);
  const [currentDocumentType, setCurrentDocumentType] = useState<ComplianceDocumentType | null>(null);
  const [uploadedDocuments, setUploadedDocuments] = useState<string[]>([]);
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatus>("NOT_STARTED");
  const [verificationSteps, setVerificationSteps] = useState<VerificationStep[]>([]);

  // Calculate progress
  const totalSteps = 4;
  const progress = (step / totalSteps) * 100;

  // Get document title
  const getDocumentTitle = (type: ComplianceDocumentType) => {
    switch (type) {
      case ComplianceDocumentType.PASSPORT:
        return "Government ID";
      case ComplianceDocumentType.SELFIE:
        return "Selfie with ID";
      case ComplianceDocumentType.PROOF_OF_ADDRESS:
        return "Proof of Address";
      case ComplianceDocumentType.BANK_STATEMENT:
        return "Bank Statement";
      default:
        return "Document";
    }
  };

  // Get required documents for selected level
  const getRequiredDocuments = (level: KycLevel): ComplianceDocumentType[] => {
    switch (level) {
      case KycLevel.BASIC:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
        ];
      case KycLevel.INTERMEDIATE:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
          ComplianceDocumentType.PROOF_OF_ADDRESS,
        ];
      case KycLevel.ADVANCED:
        return [
          ComplianceDocumentType.PASSPORT,
          ComplianceDocumentType.SELFIE,
          ComplianceDocumentType.PROOF_OF_ADDRESS,
          ComplianceDocumentType.BANK_STATEMENT,
        ];
      default:
        return [];
    }
  };

  // Handle level selection
  const handleLevelSelect = (level: KycLevel) => {
    setSelectedLevel(level);
    setStep(WizardStep.DOCUMENT_REQUIREMENTS);
  };

  // Handle document upload selection
  const handleDocumentSelect = (type: ComplianceDocumentType) => {
    setCurrentDocumentType(type);
    setStep(WizardStep.DOCUMENT_UPLOAD);
  };

  // Handle document upload
  const handleDocumentUpload = async (file: File, name: string, notes: string) => {
    if (!currentDocumentType) return "";
    
    // Simulate document upload
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Add to uploaded documents
    if (!uploadedDocuments.includes(currentDocumentType)) {
      setUploadedDocuments([...uploadedDocuments, currentDocumentType]);
    }
    
    // Return to document requirements
    setCurrentDocumentType(null);
    setStep(WizardStep.DOCUMENT_REQUIREMENTS);
    
    // Check if all required documents are uploaded
    const requiredDocs = getRequiredDocuments(selectedLevel);
    if (requiredDocs.every(doc => uploadedDocuments.includes(doc) || doc === currentDocumentType)) {
      setStep(WizardStep.REVIEW_SUBMIT);
    }
    
    toast({
      title: "Document Uploaded",
      description: `Your ${getDocumentTitle(currentDocumentType)} has been uploaded successfully.`,
    });
    
    return "document-id-123"; // Simulated document ID
  };

  // Handle verification submission
  const handleSubmitVerification = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update verification status
      setVerificationStatus("PENDING");
      
      // Create verification steps
      const steps: VerificationStep[] = [
        {
          type: "DOCUMENT_UPLOAD",
          title: "Documents Submitted",
          description: "Your verification documents have been submitted successfully.",
          status: "COMPLETED",
          timestamp: new Date().toISOString(),
        },
        {
          type: "IDENTITY_VERIFICATION",
          title: "Identity Verification",
          description: "We are verifying your identity documents.",
          status: "IN_PROGRESS",
          timestamp: new Date().toISOString(),
          estimatedCompletion: "1 business day",
        },
        {
          type: "COMPLIANCE_CHECK",
          title: "Compliance Check",
          description: "We will check your information against compliance databases.",
          status: "PENDING",
          estimatedCompletion: "1-2 business days",
        },
      ];
      
      setVerificationSteps(steps);
      
      // Switch to status tab
      setActiveTab("status");
      
      toast({
        title: "Verification Submitted",
        description: "Your verification request has been submitted successfully.",
      });
    } catch (err) {
      console.error("Error submitting verification:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to submit verification",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle step navigation
  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  // Handle verification step action
  const handleStepAction = (step: VerificationStep) => {
    if (step.type === "DOCUMENT_UPLOAD") {
      setActiveTab("verify");
      setStep(WizardStep.DOCUMENT_REQUIREMENTS);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case WizardStep.LEVEL_SELECTION:
        return (
          <div className="space-y-6">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Choose Verification Level</AlertTitle>
              <AlertDescription>
                Select the verification level that best suits your needs. Higher levels provide access to more features but require additional documentation.
              </AlertDescription>
            </Alert>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card 
                className={`cursor-pointer transition-all ${
                  selectedLevel === KycLevel.BASIC 
                    ? "border-primary ring-2 ring-primary/20" 
                    : "hover:border-primary/50"
                }`}
                onClick={() => handleLevelSelect(KycLevel.BASIC)}
              >
                <CardHeader>
                  <CardTitle className="text-base">Basic</CardTitle>
                  <CardDescription>
                    For individual users with basic trading needs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>Trade up to 10,000 carbon credits</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>Basic portfolio management</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>2 documents required</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card 
                className={`cursor-pointer transition-all ${
                  selectedLevel === KycLevel.INTERMEDIATE 
                    ? "border-primary ring-2 ring-primary/20" 
                    : "hover:border-primary/50"
                }`}
                onClick={() => handleLevelSelect(KycLevel.INTERMEDIATE)}
              >
                <CardHeader>
                  <CardTitle className="text-base">Intermediate</CardTitle>
                  <CardDescription>
                    For businesses with moderate trading volume
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>Trade up to 100,000 carbon credits</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>Advanced portfolio analytics</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>3 documents required</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card 
                className={`cursor-pointer transition-all ${
                  selectedLevel === KycLevel.ADVANCED 
                    ? "border-primary ring-2 ring-primary/20" 
                    : "hover:border-primary/50"
                }`}
                onClick={() => handleLevelSelect(KycLevel.ADVANCED)}
              >
                <CardHeader>
                  <CardTitle className="text-base">Advanced</CardTitle>
                  <CardDescription>
                    For enterprises with high trading volume
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>Unlimited trading volume</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>Enterprise-grade features</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
                      <span>4 documents required</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        );
      
      case WizardStep.DOCUMENT_REQUIREMENTS:
        return (
          <div className="space-y-6">
            <DocumentRequirements 
              level={selectedLevel} 
              submittedDocuments={uploadedDocuments} 
            />
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Upload Documents</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getRequiredDocuments(selectedLevel).map((docType) => {
                  const isUploaded = uploadedDocuments.includes(docType);
                  
                  return (
                    <Button
                      key={docType}
                      variant={isUploaded ? "outline" : "default"}
                      className={`justify-start h-auto py-3 ${
                        isUploaded ? "border-green-200 bg-green-50/50 dark:bg-green-950/10" : ""
                      }`}
                      onClick={() => handleDocumentSelect(docType)}
                    >
                      <div className="flex items-center">
                        <div className="p-2 rounded-full bg-muted mr-2">
                          <FileText className="h-4 w-4" />
                        </div>
                        <div className="text-left">
                          <div className="font-medium">{getDocumentTitle(docType)}</div>
                          <div className="text-xs text-muted-foreground">
                            {isUploaded ? "Uploaded - Click to replace" : "Click to upload"}
                          </div>
                        </div>
                      </div>
                      {isUploaded && (
                        <CheckCircle className="ml-auto h-4 w-4 text-green-500" />
                      )}
                    </Button>
                  );
                })}
              </div>
            </div>
            
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={prevStep}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              
              <Button
                onClick={nextStep}
                disabled={uploadedDocuments.length === 0}
              >
                Review & Submit
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        );
      
      case WizardStep.DOCUMENT_UPLOAD:
        return currentDocumentType ? (
          <DocumentUpload
            documentType={currentDocumentType}
            documentTitle={getDocumentTitle(currentDocumentType)}
            onUpload={handleDocumentUpload}
            onCancel={() => {
              setCurrentDocumentType(null);
              setStep(WizardStep.DOCUMENT_REQUIREMENTS);
            }}
          />
        ) : (
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={() => setStep(WizardStep.DOCUMENT_REQUIREMENTS)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Documents
            </Button>
          </div>
        );
      
      case WizardStep.REVIEW_SUBMIT:
        return (
          <div className="space-y-6">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Review Your Submission</AlertTitle>
              <AlertDescription>
                Please review your documents before submitting. Once submitted, your verification request will be processed within 1-2 business days.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Verification Level</h3>
              <div className="p-4 border rounded-md">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  <span className="font-medium">{selectedLevel} Verification</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Uploaded Documents</h3>
              <div className="space-y-2">
                {getRequiredDocuments(selectedLevel).map((docType) => {
                  const isUploaded = uploadedDocuments.includes(docType);
                  
                  return (
                    <div 
                      key={docType} 
                      className="flex items-center justify-between p-3 border rounded-md"
                    >
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 mr-2" />
                        <span>{getDocumentTitle(docType)}</span>
                      </div>
                      {isUploaded ? (
                        <Badge variant="success" className="flex items-center">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Uploaded
                        </Badge>
                      ) : (
                        <Badge variant="destructive" className="flex items-center">
                          <AlertTriangle className="mr-1 h-3 w-3" />
                          Missing
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setStep(WizardStep.DOCUMENT_REQUIREMENTS)}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Documents
              </Button>
              
              <Button
                onClick={handleSubmitVerification}
                disabled={
                  isLoading || 
                  !getRequiredDocuments(selectedLevel).every(doc => uploadedDocuments.includes(doc))
                }
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    Submit Verification
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>KYC Verification</CardTitle>
          <InfoTooltip 
            content={
              <div className="space-y-2">
                <p>KYC (Know Your Customer) verification is required to comply with regulatory requirements.</p>
                <p>Higher verification levels allow for higher trading limits and additional features.</p>
              </div>
            }
          />
        </div>
        <CardDescription>
          Complete your identity verification to access all platform features
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {isLoading && step === WizardStep.LEVEL_SELECTION ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading verification options...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full grid grid-cols-2">
              <TabsTrigger value="verify">Verify</TabsTrigger>
              <TabsTrigger value="status">Status</TabsTrigger>
            </TabsList>
            
            <TabsContent value="verify" className="pt-4">
              {activeTab === "verify" && (
                <>
                  {step !== WizardStep.DOCUMENT_UPLOAD && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Step {step} of {totalSteps}</span>
                        <span className="text-sm text-muted-foreground">
                          {step === WizardStep.LEVEL_SELECTION 
                            ? "Select Verification Level" 
                            : step === WizardStep.DOCUMENT_REQUIREMENTS 
                            ? "Document Requirements" 
                            : "Review & Submit"}
                        </span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>
                  )}
                  
                  <div className="animate-in fade-in duration-300">
                    {renderStepContent()}
                  </div>
                </>
              )}
            </TabsContent>
            
            <TabsContent value="status" className="pt-4">
              {activeTab === "status" && (
                <div className="animate-in fade-in duration-300">
                  {verificationStatus === "NOT_STARTED" ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <div className="p-3 bg-muted rounded-full mb-4">
                        <FileText className="h-6 w-6" />
                      </div>
                      <h3 className="text-lg font-medium">No Verification Submitted</h3>
                      <p className="text-muted-foreground mt-2 max-w-md">
                        You haven't submitted any verification documents yet. Complete the verification process to access all platform features.
                      </p>
                      <Button 
                        className="mt-4"
                        onClick={() => setActiveTab("verify")}
                      >
                        Start Verification
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <VerificationStatusTimeline 
                      status={verificationStatus} 
                      steps={verificationSteps}
                      onAction={handleStepAction}
                    />
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}
