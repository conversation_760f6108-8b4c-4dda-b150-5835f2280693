"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { HelpCard } from "@/components/ui/contextual-help";
import { AnimatedCard } from "@/components/ui/animated";
import {
  FileText,
  Shield,
  AlertCircle,
  FileCheck,
  Clock,
  ChevronRight,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  PieChart,
  Users,
  Building,
  Wallet,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Landmark,
  FileWarning,
  Bell,
  Calendar,
  Hourglass
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Sample compliance data
const COMPLIANCE_SUMMARY = {
  kycStatus: {
    verified: 78,
    pending: 12,
    rejected: 5,
    expired: 5,
    total: 100,
    change: 8,
    changeType: "increase"
  },
  amlStatus: {
    lowRisk: 65,
    mediumRisk: 25,
    highRisk: 8,
    criticalRisk: 2,
    total: 100,
    change: -5,
    changeType: "decrease"
  },
  verificationStatus: {
    verified: 42,
    pending: 15,
    rejected: 3,
    total: 60,
    change: 12,
    changeType: "increase"
  },
  auditStatus: {
    compliant: 92,
    nonCompliant: 8,
    total: 100,
    change: 3,
    changeType: "increase"
  }
};

// Recent compliance activities
const RECENT_ACTIVITIES = [
  {
    id: "act-001",
    type: "KYC",
    action: "Verification Approved",
    entity: "Acme Corp",
    entityType: "organization",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    status: "APPROVED"
  },
  {
    id: "act-002",
    type: "AML",
    action: "Risk Assessment Updated",
    entity: "John Smith",
    entityType: "user",
    timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
    status: "MEDIUM_RISK"
  },
  {
    id: "act-003",
    type: "VERIFICATION",
    action: "Carbon Credit Verification",
    entity: "Green Energy Project",
    entityType: "project",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
    status: "PENDING"
  },
  {
    id: "act-004",
    type: "AUDIT",
    action: "Quarterly Audit Completed",
    entity: "System",
    entityType: "system",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    status: "COMPLETED"
  },
  {
    id: "act-005",
    type: "KYC",
    action: "Document Expired",
    entity: "Jane Doe",
    entityType: "user",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 36), // 1.5 days ago
    status: "EXPIRED"
  }
];

// Upcoming compliance tasks
const UPCOMING_TASKS = [
  {
    id: "task-001",
    type: "KYC",
    title: "Verify New Users",
    dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // 2 days from now
    count: 5,
    priority: "HIGH"
  },
  {
    id: "task-002",
    type: "AML",
    title: "Review Flagged Transactions",
    dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 day from now
    count: 3,
    priority: "CRITICAL"
  },
  {
    id: "task-003",
    type: "VERIFICATION",
    title: "Verify Carbon Credit Issuance",
    dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // 5 days from now
    count: 2,
    priority: "MEDIUM"
  },
  {
    id: "task-004",
    type: "AUDIT",
    title: "Prepare Monthly Compliance Report",
    dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days from now
    count: 1,
    priority: "LOW"
  }
];

// Status icons
const STATUS_ICONS: Record<string, React.ReactNode> = {
  APPROVED: <CheckCircle className="h-4 w-4 text-green-500" />,
  PENDING: <Hourglass className="h-4 w-4 text-amber-500" />,
  REJECTED: <XCircle className="h-4 w-4 text-red-500" />,
  EXPIRED: <AlertTriangle className="h-4 w-4 text-orange-500" />,
  COMPLETED: <CheckCircle className="h-4 w-4 text-green-500" />,
  LOW_RISK: <Shield className="h-4 w-4 text-green-500" />,
  MEDIUM_RISK: <Shield className="h-4 w-4 text-amber-500" />,
  HIGH_RISK: <Shield className="h-4 w-4 text-red-500" />,
  CRITICAL_RISK: <AlertCircle className="h-4 w-4 text-red-500" />,
  VERIFIED: <CheckCircle className="h-4 w-4 text-green-500" />,
  NON_COMPLIANT: <XCircle className="h-4 w-4 text-red-500" />,
  COMPLIANT: <CheckCircle className="h-4 w-4 text-green-500" />
};

// Type icons
const TYPE_ICONS: Record<string, React.ReactNode> = {
  KYC: <Shield className="h-4 w-4" />,
  AML: <AlertCircle className="h-4 w-4" />,
  VERIFICATION: <FileCheck className="h-4 w-4" />,
  AUDIT: <Clock className="h-4 w-4" />
};

// Entity type icons
const ENTITY_TYPE_ICONS: Record<string, React.ReactNode> = {
  user: <Users className="h-4 w-4" />,
  organization: <Building className="h-4 w-4" />,
  project: <FileText className="h-4 w-4" />,
  system: <Landmark className="h-4 w-4" />,
  wallet: <Wallet className="h-4 w-4" />
};

// Priority badges
const PRIORITY_BADGES: Record<string, React.ReactNode> = {
  LOW: <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Low</Badge>,
  MEDIUM: <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Medium</Badge>,
  HIGH: <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">High</Badge>,
  CRITICAL: <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Critical</Badge>
};

interface ComplianceDashboardProps {
  userId?: string;
  organizationId?: string;
}

export function ComplianceDashboard({
  userId,
  organizationId,
}: ComplianceDashboardProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Calculate percentages for progress bars
  const kycVerifiedPercentage = Math.round((COMPLIANCE_SUMMARY.kycStatus.verified / COMPLIANCE_SUMMARY.kycStatus.total) * 100);
  const amlLowRiskPercentage = Math.round((COMPLIANCE_SUMMARY.amlStatus.lowRisk / COMPLIANCE_SUMMARY.amlStatus.total) * 100);
  const verificationCompletedPercentage = Math.round((COMPLIANCE_SUMMARY.verificationStatus.verified / COMPLIANCE_SUMMARY.verificationStatus.total) * 100);
  const auditCompliancePercentage = Math.round((COMPLIANCE_SUMMARY.auditStatus.compliant / COMPLIANCE_SUMMARY.auditStatus.total) * 100);

  // Render KYC status card
  const renderKycStatusCard = () => {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center">
            <Shield className="h-5 w-5 text-muted-foreground mr-2" />
            <CardTitle className="text-sm font-medium">KYC Verification Status</CardTitle>
          </div>
          <Badge
            variant={COMPLIANCE_SUMMARY.kycStatus.changeType === "increase" ? "default" : "destructive"}
            className="flex items-center"
          >
            {COMPLIANCE_SUMMARY.kycStatus.changeType === "increase" ? (
              <ArrowUpRight className="h-3 w-3 mr-1" />
            ) : (
              <ArrowDownRight className="h-3 w-3 mr-1" />
            )}
            {Math.abs(COMPLIANCE_SUMMARY.kycStatus.change)}%
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{kycVerifiedPercentage}%</div>
          <p className="text-xs text-muted-foreground">
            {COMPLIANCE_SUMMARY.kycStatus.verified} of {COMPLIANCE_SUMMARY.kycStatus.total} users verified
          </p>
          <div className="mt-4">
            <Progress value={kycVerifiedPercentage} className="h-2" />
          </div>
          <div className="mt-3 grid grid-cols-4 gap-1 text-xs">
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                <span>Verified</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.kycStatus.verified}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-amber-500 mr-1"></div>
                <span>Pending</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.kycStatus.pending}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                <span>Rejected</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.kycStatus.rejected}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-orange-500 mr-1"></div>
                <span>Expired</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.kycStatus.expired}%</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto" onClick={() => router.push("/compliance/kyc")}>
            View Details
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Render AML status card
  const renderAmlStatusCard = () => {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-muted-foreground mr-2" />
            <CardTitle className="text-sm font-medium">AML Risk Assessment</CardTitle>
          </div>
          <Badge
            variant={COMPLIANCE_SUMMARY.amlStatus.changeType === "decrease" ? "default" : "destructive"}
            className="flex items-center"
          >
            {COMPLIANCE_SUMMARY.amlStatus.changeType === "decrease" ? (
              <ArrowDownRight className="h-3 w-3 mr-1" />
            ) : (
              <ArrowUpRight className="h-3 w-3 mr-1" />
            )}
            {Math.abs(COMPLIANCE_SUMMARY.amlStatus.change)}%
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{amlLowRiskPercentage}%</div>
          <p className="text-xs text-muted-foreground">
            {COMPLIANCE_SUMMARY.amlStatus.lowRisk} of {COMPLIANCE_SUMMARY.amlStatus.total} entities low risk
          </p>
          <div className="mt-4">
            <Progress value={amlLowRiskPercentage} className="h-2" />
          </div>
          <div className="mt-3 grid grid-cols-4 gap-1 text-xs">
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                <span>Low</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.amlStatus.lowRisk}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-amber-500 mr-1"></div>
                <span>Medium</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.amlStatus.mediumRisk}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-orange-500 mr-1"></div>
                <span>High</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.amlStatus.highRisk}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                <span>Critical</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.amlStatus.criticalRisk}%</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto" onClick={() => router.push("/compliance/aml")}>
            View Details
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Render verification status card
  const renderVerificationStatusCard = () => {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center">
            <FileCheck className="h-5 w-5 text-muted-foreground mr-2" />
            <CardTitle className="text-sm font-medium">Carbon Verification</CardTitle>
          </div>
          <Badge
            variant={COMPLIANCE_SUMMARY.verificationStatus.changeType === "increase" ? "default" : "destructive"}
            className="flex items-center"
          >
            {COMPLIANCE_SUMMARY.verificationStatus.changeType === "increase" ? (
              <ArrowUpRight className="h-3 w-3 mr-1" />
            ) : (
              <ArrowDownRight className="h-3 w-3 mr-1" />
            )}
            {Math.abs(COMPLIANCE_SUMMARY.verificationStatus.change)}%
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{verificationCompletedPercentage}%</div>
          <p className="text-xs text-muted-foreground">
            {COMPLIANCE_SUMMARY.verificationStatus.verified} of {COMPLIANCE_SUMMARY.verificationStatus.total} projects verified
          </p>
          <div className="mt-4">
            <Progress value={verificationCompletedPercentage} className="h-2" />
          </div>
          <div className="mt-3 grid grid-cols-3 gap-1 text-xs">
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                <span>Verified</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.verificationStatus.verified}</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-amber-500 mr-1"></div>
                <span>Pending</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.verificationStatus.pending}</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                <span>Rejected</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.verificationStatus.rejected}</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto" onClick={() => router.push("/compliance/verification")}>
            View Details
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Render audit status card
  const renderAuditStatusCard = () => {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center">
            <Clock className="h-5 w-5 text-muted-foreground mr-2" />
            <CardTitle className="text-sm font-medium">Audit Compliance</CardTitle>
          </div>
          <Badge
            variant={COMPLIANCE_SUMMARY.auditStatus.changeType === "increase" ? "default" : "destructive"}
            className="flex items-center"
          >
            {COMPLIANCE_SUMMARY.auditStatus.changeType === "increase" ? (
              <ArrowUpRight className="h-3 w-3 mr-1" />
            ) : (
              <ArrowDownRight className="h-3 w-3 mr-1" />
            )}
            {Math.abs(COMPLIANCE_SUMMARY.auditStatus.change)}%
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{auditCompliancePercentage}%</div>
          <p className="text-xs text-muted-foreground">
            {COMPLIANCE_SUMMARY.auditStatus.compliant} of {COMPLIANCE_SUMMARY.auditStatus.total} requirements compliant
          </p>
          <div className="mt-4">
            <Progress value={auditCompliancePercentage} className="h-2" />
          </div>
          <div className="mt-3 grid grid-cols-2 gap-1 text-xs">
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                <span>Compliant</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.auditStatus.compliant}%</span>
            </div>
            <div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                <span>Non-Compliant</span>
              </div>
              <span className="font-medium">{COMPLIANCE_SUMMARY.auditStatus.nonCompliant}%</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto" onClick={() => router.push("/compliance/audit")}>
            View Details
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Render recent activities
  const renderRecentActivities = () => {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Recent Activities</CardTitle>
            <Button variant="ghost" size="sm" className="h-8 px-2">
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
          <CardDescription>Latest compliance activities and events</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {RECENT_ACTIVITIES.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-full ${
                  activity.type === "KYC" ? "bg-blue-100" :
                  activity.type === "AML" ? "bg-red-100" :
                  activity.type === "VERIFICATION" ? "bg-green-100" :
                  "bg-purple-100"
                }`}>
                  {TYPE_ICONS[activity.type]}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="font-medium text-sm">{activity.action}</p>
                    <Badge variant="outline" className="flex items-center">
                      {STATUS_ICONS[activity.status.replace(/_/g, "_")]}
                      <span className="ml-1">{activity.status.replace(/_/g, " ")}</span>
                    </Badge>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-xs text-muted-foreground">
                      {ENTITY_TYPE_ICONS[activity.entityType]}
                      <span className="ml-1">{activity.entity}</span>
                    </div>
                    <span className="mx-2 text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground">
                      {format(activity.timestamp, "MMM d, h:mm a")}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render upcoming tasks
  const renderUpcomingTasks = () => {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Upcoming Tasks</CardTitle>
            <Button variant="ghost" size="sm" className="h-8 px-2">
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
          <CardDescription>Compliance tasks requiring attention</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {UPCOMING_TASKS.map((task) => (
              <div key={task.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-full ${
                  task.type === "KYC" ? "bg-blue-100" :
                  task.type === "AML" ? "bg-red-100" :
                  task.type === "VERIFICATION" ? "bg-green-100" :
                  "bg-purple-100"
                }`}>
                  {TYPE_ICONS[task.type]}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="font-medium text-sm">{task.title}</p>
                    {PRIORITY_BADGES[task.priority]}
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>Due: {format(task.dueDate, "MMM d, yyyy")}</span>
                    </div>
                    <span className="mx-2 text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground flex items-center">
                      <FileWarning className="h-3 w-3 mr-1" />
                      {task.count} {task.count === 1 ? 'item' : 'items'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Compliance Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor and manage compliance activities across your organization
          </p>
        </div>

        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full md:w-auto"
        >
          <TabsList className="grid grid-cols-4 w-full md:w-auto">
            <TabsTrigger value="overview" className="flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="kyc" className="flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              KYC
            </TabsTrigger>
            <TabsTrigger value="aml" className="flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              AML
            </TabsTrigger>
            <TabsTrigger value="verification" className="flex items-center">
              <FileCheck className="h-4 w-4 mr-2" />
              Verification
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Separator />

      <TabsContent value="overview" className="mt-0">
        <HelpCard
          content={{
            title: "Compliance Overview",
            description: "Monitor your organization's compliance status across all regulatory requirements.",
            type: "info",
            tips: [
              "Review KYC verification status for all users and organizations",
              "Monitor AML risk levels and address high-risk entities",
              "Track carbon credit verification progress",
              "Ensure audit compliance with regulatory requirements"
            ]
          }}
          className="mb-6"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {renderKycStatusCard()}
          {renderAmlStatusCard()}
          {renderVerificationStatusCard()}
          {renderAuditStatusCard()}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          {renderRecentActivities()}
          {renderUpcomingTasks()}
        </div>
      </TabsContent>

      <TabsContent value="kyc" className="mt-0">
        <HelpCard
          content={{
            title: "KYC Verification",
            description: "Manage Know Your Customer verification processes for users and organizations.",
            type: "info",
            tips: [
              "Review pending verification requests",
              "Monitor expired documents that need renewal",
              "Track verification completion rates",
              "Address rejected verifications"
            ]
          }}
          className="mb-6"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {renderKycStatusCard()}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>KYC Verification Trends</CardTitle>
              <CardDescription>Monthly verification activity</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                <p>Chart visualization would appear here</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Pending Verifications</CardTitle>
              <CardDescription>Users awaiting verification</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4" />
                <p>User verification list would appear here</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Document Expiry</CardTitle>
              <CardDescription>Documents requiring renewal</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <FileWarning className="h-12 w-12 mx-auto mb-4" />
                <p>Document expiry list would appear here</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="aml" className="mt-0">
        <HelpCard
          content={{
            title: "AML Monitoring",
            description: "Monitor Anti-Money Laundering compliance and risk assessment.",
            type: "info",
            tips: [
              "Review flagged transactions",
              "Monitor high-risk entities",
              "Track risk assessment trends",
              "Address compliance alerts"
            ]
          }}
          className="mb-6"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {renderAmlStatusCard()}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Risk Assessment Distribution</CardTitle>
              <CardDescription>Entity risk level breakdown</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <PieChart className="h-16 w-16 mx-auto mb-4" />
                <p>Chart visualization would appear here</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>High Risk Entities</CardTitle>
              <CardDescription>Entities requiring attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <AlertCircle className="h-12 w-12 mx-auto mb-4" />
                <p>High risk entity list would appear here</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Recent Alerts</CardTitle>
              <CardDescription>AML monitoring alerts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Bell className="h-12 w-12 mx-auto mb-4" />
                <p>Alert list would appear here</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="verification" className="mt-0">
        <HelpCard
          content={{
            title: "Carbon Credit Verification",
            description: "Track verification status of carbon credit projects and issuances.",
            type: "info",
            tips: [
              "Monitor verification progress",
              "Review pending verifications",
              "Track verification success rates",
              "Address rejected verifications"
            ]
          }}
          className="mb-6"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {renderVerificationStatusCard()}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Verification Timeline</CardTitle>
              <CardDescription>Project verification progress</CardDescription>
            </CardHeader>
            <CardContent className="h-[300px] flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                <p>Timeline visualization would appear here</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Pending Verifications</CardTitle>
              <CardDescription>Projects awaiting verification</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <FileCheck className="h-12 w-12 mx-auto mb-4" />
                <p>Project verification list would appear here</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Verification History</CardTitle>
              <CardDescription>Recent verification activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4" />
                <p>Verification history would appear here</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </div>
  );
