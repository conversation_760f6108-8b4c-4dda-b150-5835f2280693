"use client";

import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, FileText, Camera, Home, CreditCard, AlertCircle } from "lucide-react";
import { ComplianceDocumentType, KycLevel } from "@/lib/compliance";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface DocumentRequirementsProps {
  level: KycLevel;
  submittedDocuments?: string[];
}

interface DocumentRequirement {
  type: ComplianceDocumentType;
  title: string;
  description: string;
  requirements: string[];
  icon: React.ReactNode;
  sampleImage: string;
  requiredForLevels: KycLevel[];
}

export function DocumentRequirements({ level, submittedDocuments = [] }: DocumentRequirementsProps) {
  // Define document requirements for each document type
  const documentRequirements: DocumentRequirement[] = [
    {
      type: ComplianceDocumentType.PASSPORT,
      title: "Government ID",
      description: "Passport, driver's license, or national ID card",
      requirements: [
        "Government-issued photo ID",
        "Full name clearly visible",
        "Not expired",
        "All four corners visible"
      ],
      icon: <FileText className="h-5 w-5" />,
      sampleImage: "/images/sample-passport.jpg",
      requiredForLevels: [KycLevel.BASIC, KycLevel.INTERMEDIATE, KycLevel.ADVANCED]
    },
    {
      type: ComplianceDocumentType.SELFIE,
      title: "Selfie with ID",
      description: "A photo of yourself holding your ID",
      requirements: [
        "Hold your ID next to your face",
        "Your face and the ID must be clearly visible",
        "Good lighting conditions",
        "Neutral background"
      ],
      icon: <Camera className="h-5 w-5" />,
      sampleImage: "/images/sample-selfie.jpg",
      requiredForLevels: [KycLevel.BASIC, KycLevel.INTERMEDIATE, KycLevel.ADVANCED]
    },
    {
      type: ComplianceDocumentType.PROOF_OF_ADDRESS,
      title: "Proof of Address",
      description: "Utility bill, bank statement, or official letter",
      requirements: [
        "Less than 3 months old",
        "Full name and address clearly visible",
        "Issued by a recognized organization",
        "Complete document visible"
      ],
      icon: <Home className="h-5 w-5" />,
      sampleImage: "/images/sample-address-proof.jpg",
      requiredForLevels: [KycLevel.INTERMEDIATE, KycLevel.ADVANCED]
    },
    {
      type: ComplianceDocumentType.BANK_STATEMENT,
      title: "Bank Statement",
      description: "Recent bank statement or financial record",
      requirements: [
        "Less than 3 months old",
        "Full name and account details visible",
        "Issued by a recognized financial institution",
        "Transaction history visible"
      ],
      icon: <CreditCard className="h-5 w-5" />,
      sampleImage: "/images/sample-bank-statement.jpg",
      requiredForLevels: [KycLevel.ADVANCED]
    }
  ];

  // Filter requirements based on selected level
  const requiredDocuments = documentRequirements.filter(doc => 
    doc.requiredForLevels.includes(level as KycLevel)
  );

  return (
    <div className="space-y-6">
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Document Requirements</AlertTitle>
        <AlertDescription>
          Please ensure all documents meet the requirements below. Unclear or incomplete documents will delay your verification.
        </AlertDescription>
      </Alert>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {requiredDocuments.map((doc) => {
          const isSubmitted = submittedDocuments.includes(doc.type);
          
          return (
            <Card 
              key={doc.type} 
              className={`overflow-hidden transition-all ${
                isSubmitted ? "border-green-200 bg-green-50/50 dark:bg-green-950/10" : ""
              }`}
            >
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center text-base">
                    <div className="p-2 rounded-full bg-muted mr-2">
                      {doc.icon}
                    </div>
                    {doc.title}
                  </CardTitle>
                  {isSubmitted ? (
                    <Badge variant="success" className="flex items-center">
                      <Check className="mr-1 h-3 w-3" />
                      Submitted
                    </Badge>
                  ) : (
                    <Badge variant="outline">Required</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {doc.description}
                    </p>
                    <h4 className="text-sm font-medium mb-1">Requirements:</h4>
                    <ul className="list-disc pl-5 space-y-1 text-sm">
                      {doc.requirements.map((req, i) => (
                        <li key={i}>{req}</li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="relative h-24 w-32 border rounded-md overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center bg-muted text-xs text-muted-foreground">
                        Sample Image
                      </div>
                      {/* Uncomment when sample images are available */}
                      {/* <Image 
                        src={doc.sampleImage} 
                        alt={`Sample ${doc.title}`} 
                        fill
                        className="object-cover"
                      /> */}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
