"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import { HelpCard } from "@/components/ui/contextual-help";
import { AnimatedCard } from "@/components/ui/animated";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import {
  FileText,
  FileSpreadsheet,
  FileJson,
  Calendar,
  Filter,
  CheckCircle,
  AlertCircle,
  Shield,
  FileCheck,
  Clock,
  Download,
  Trash2,
  MoreHorizontal,
  Eye,
  Share2,
  Copy,
  Search,
  Plus,
  RefreshCw,
  SlidersHorizontal,
  CalendarIcon,
  User,
  Tag,
  X,
  ChevronDown,
  ArrowUpDown,
  History,
  FileSymlink
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { ReportGenerationWizard } from "./report-generation-wizard";
import { ReportSharingDialog } from "./report-sharing-dialog";

// Sample report data
const SAMPLE_REPORTS = [
  {
    id: "rep-001",
    name: "KYC Verification Status Report",
    type: "KYC",
    format: "PDF",
    createdAt: new Date("2023-10-15"),
    createdBy: "John Doe",
    status: "COMPLETED",
    size: "1.2 MB",
  },
  {
    id: "rep-002",
    name: "AML Risk Assessment Report",
    type: "AML",
    format: "CSV",
    createdAt: new Date("2023-10-10"),
    createdBy: "Jane Smith",
    status: "COMPLETED",
    size: "3.5 MB",
  },
  {
    id: "rep-003",
    name: "Carbon Credit Verification Report",
    type: "VERIFICATION",
    format: "PDF",
    createdAt: new Date("2023-10-05"),
    createdBy: "John Doe",
    status: "COMPLETED",
    size: "2.8 MB",
  },
  {
    id: "rep-004",
    name: "Quarterly Compliance Audit",
    type: "AUDIT",
    format: "JSON",
    createdAt: new Date("2023-09-30"),
    createdBy: "Jane Smith",
    status: "COMPLETED",
    size: "4.1 MB",
  },
  {
    id: "rep-005",
    name: "Custom Compliance Report",
    type: "CUSTOM",
    format: "PDF",
    createdAt: new Date("2023-09-25"),
    createdBy: "John Doe",
    status: "COMPLETED",
    size: "1.9 MB",
  },
];

// Report type icons
const REPORT_TYPE_ICONS: Record<string, React.ReactNode> = {
  KYC: <Shield className="h-4 w-4" />,
  AML: <AlertCircle className="h-4 w-4" />,
  VERIFICATION: <FileCheck className="h-4 w-4" />,
  AUDIT: <Clock className="h-4 w-4" />,
  CUSTOM: <FileText className="h-4 w-4" />,
};

// Report format icons
const REPORT_FORMAT_ICONS: Record<string, React.ReactNode> = {
  PDF: <FileText className="h-4 w-4" />,
  CSV: <FileSpreadsheet className="h-4 w-4" />,
  JSON: <FileJson className="h-4 w-4" />,
};

interface ReportManagementProps {
  userId?: string;
  organizationId?: string;
}

export function ReportManagement({
  userId,
  organizationId,
}: ReportManagementProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [isSharingDialogOpen, setIsSharingDialogOpen] = useState<boolean>(false);
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [selectedReportData, setSelectedReportData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<{
    from?: Date;
    to?: Date;
  }>({});
  const [selectedFormats, setSelectedFormats] = useState<string[]>([]);
  const [selectedCreators, setSelectedCreators] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Filter reports based on all criteria
  const filteredReports = SAMPLE_REPORTS.filter((report) => {
    // Match search query
    const matchesSearch =
      report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.createdBy.toLowerCase().includes(searchQuery.toLowerCase());

    // Match tab selection
    const matchesTab = activeTab === "all" || report.type === activeTab;

    // Match date range
    const matchesDateRange =
      (!dateRange.from || report.createdAt >= dateRange.from) &&
      (!dateRange.to || report.createdAt <= dateRange.to);

    // Match selected formats
    const matchesFormat =
      selectedFormats.length === 0 ||
      selectedFormats.includes(report.format);

    // Match selected creators
    const matchesCreator =
      selectedCreators.length === 0 ||
      selectedCreators.includes(report.createdBy);

    return matchesSearch && matchesTab && matchesDateRange && matchesFormat && matchesCreator;
  });

  // Sort the filtered reports
  const sortedReports = [...filteredReports].sort((a, b) => {
    if (sortBy === "date") {
      return sortOrder === "asc"
        ? a.createdAt.getTime() - b.createdAt.getTime()
        : b.createdAt.getTime() - a.createdAt.getTime();
    } else if (sortBy === "name") {
      return sortOrder === "asc"
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortBy === "type") {
      return sortOrder === "asc"
        ? a.type.localeCompare(b.type)
        : b.type.localeCompare(a.type);
    } else if (sortBy === "size") {
      const sizeA = parseFloat(a.size.split(" ")[0]);
      const sizeB = parseFloat(b.size.split(" ")[0]);
      return sortOrder === "asc" ? sizeA - sizeB : sizeB - sizeA;
    }
    return 0;
  });

  // Handle report creation
  const handleReportCreated = (reportId: string) => {
    setIsCreateDialogOpen(false);
    toast({
      title: "Report Generated",
      description: "Your report has been generated successfully.",
    });
    // Refresh the list
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Handle report deletion
  const handleDeleteReport = (reportId: string) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Report Deleted",
        description: "The report has been deleted successfully.",
      });
      setIsDeleteDialogOpen(false);
      setSelectedReport(null);
      setIsLoading(false);
    }, 1000);
  };

  // Handle report download
  const handleDownloadReport = (reportId: string) => {
    const report = SAMPLE_REPORTS.find((r) => r.id === reportId);

    if (report) {
      toast({
        title: "Downloading Report",
        description: `${report.name} is being downloaded.`,
      });
    }
  };

  // Handle report view
  const handleViewReport = (reportId: string) => {
    router.push(`/compliance/reports/${reportId}`);
  };

  // Handle report sharing
  const handleShareReport = (reportId: string) => {
    const report = SAMPLE_REPORTS.find((r) => r.id === reportId);
    if (report) {
      setSelectedReport(reportId);
      setSelectedReportData(report);
      setIsSharingDialogOpen(true);
    }
  };

  // Extract unique creators for filtering
  const uniqueCreators = Array.from(new Set(SAMPLE_REPORTS.map(report => report.createdBy)));

  // Extract unique formats for filtering
  const uniqueFormats = Array.from(new Set(SAMPLE_REPORTS.map(report => report.format)));

  // Render report table
  const renderReportTable = () => {
    return (
      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            <tr className="border-b bg-muted/50">
              <th className="py-3 px-4 text-left font-medium">Report Name</th>
              <th className="py-3 px-4 text-left font-medium">Type</th>
              <th className="py-3 px-4 text-left font-medium">Format</th>
              <th className="py-3 px-4 text-left font-medium">Created</th>
              <th className="py-3 px-4 text-left font-medium">Size</th>
              <th className="py-3 px-4 text-right font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={6} className="py-8 text-center">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">Loading reports...</p>
                </td>
              </tr>
            ) : sortedReports.length === 0 ? (
              <tr>
                <td colSpan={6} className="py-8 text-center">
                  <FileText className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No reports found</p>
                  <p className="text-sm text-muted-foreground mt-1">Try adjusting your filters or create a new report</p>
                </td>
              </tr>
            ) : (
              sortedReports.map((report) => (
                <tr key={report.id} className="border-b hover:bg-muted/50">
                  <td className="py-3 px-4">
                    <div className="font-medium">{report.name}</div>
                    <div className="text-xs text-muted-foreground">
                      Created by {report.createdBy}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <Badge className="flex items-center w-fit">
                      {REPORT_TYPE_ICONS[report.type]}
                      <span className="ml-1">{report.type}</span>
                    </Badge>
                  </td>
                  <td className="py-3 px-4">
                    <Badge variant="outline" className="flex items-center w-fit">
                      {REPORT_FORMAT_ICONS[report.format]}
                      <span className="ml-1">{report.format}</span>
                    </Badge>
                  </td>
                  <td className="py-3 px-4">
                    <div>{format(report.createdAt, "MMM d, yyyy")}</div>
                    <div className="text-xs text-muted-foreground">
                      {format(report.createdAt, "h:mm a")}
                    </div>
                  </td>
                  <td className="py-3 px-4">{report.size}</td>
                  <td className="py-3 px-4 text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleViewReport(report.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Report
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDownloadReport(report.id)}>
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShareReport(report.id)}>
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => {
                            setSelectedReport(report.id);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Compliance Reports</h2>
          <p className="text-muted-foreground">
            Manage and generate compliance reports for your organization
          </p>
        </div>

        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Report
        </Button>
      </div>

      <Separator />

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full md:w-auto"
        >
          <TabsList className="grid grid-cols-5 w-full md:w-auto">
            <TabsTrigger value="all" className="flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              All
            </TabsTrigger>
            <TabsTrigger value="KYC" className="flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              KYC
            </TabsTrigger>
            <TabsTrigger value="AML" className="flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              AML
            </TabsTrigger>
            <TabsTrigger value="VERIFICATION" className="flex items-center">
              <FileCheck className="h-4 w-4 mr-2" />
              Verification
            </TabsTrigger>
            <TabsTrigger value="AUDIT" className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Audit
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search reports..."
              className="w-full md:w-[250px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon" className="relative">
                <SlidersHorizontal className="h-4 w-4" />
                {(selectedFormats.length > 0 || selectedCreators.length > 0 || dateRange.from || dateRange.to) && (
                  <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-primary" />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[300px] p-4" align="end">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Reports</h4>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Date Range</div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">From</div>
                      <DatePicker
                        selected={dateRange.from}
                        onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">To</div>
                      <DatePicker
                        selected={dateRange.to}
                        onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Format</div>
                  <div className="space-y-1">
                    {uniqueFormats.map(format => (
                      <div key={format} className="flex items-center space-x-2">
                        <Checkbox
                          id={`format-${format}`}
                          checked={selectedFormats.includes(format)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedFormats(prev => [...prev, format]);
                            } else {
                              setSelectedFormats(prev => prev.filter(f => f !== format));
                            }
                          }}
                        />
                        <label
                          htmlFor={`format-${format}`}
                          className="text-sm flex items-center"
                        >
                          {REPORT_FORMAT_ICONS[format]}
                          <span className="ml-2">{format}</span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Created By</div>
                  <div className="space-y-1">
                    {uniqueCreators.map(creator => (
                      <div key={creator} className="flex items-center space-x-2">
                        <Checkbox
                          id={`creator-${creator}`}
                          checked={selectedCreators.includes(creator)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedCreators(prev => [...prev, creator]);
                            } else {
                              setSelectedCreators(prev => prev.filter(c => c !== creator));
                            }
                          }}
                        />
                        <label
                          htmlFor={`creator-${creator}`}
                          className="text-sm flex items-center"
                        >
                          <User className="h-3 w-3 mr-2" />
                          {creator}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Sort By</div>
                  <div className="grid grid-cols-2 gap-2">
                    <Select
                      value={sortBy}
                      onValueChange={setSortBy}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="date">Date</SelectItem>
                        <SelectItem value="name">Name</SelectItem>
                        <SelectItem value="type">Type</SelectItem>
                        <SelectItem value="size">Size</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select
                      value={sortOrder}
                      onValueChange={(value) => setSortOrder(value as "asc" | "desc")}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Order" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">Ascending</SelectItem>
                        <SelectItem value="desc">Descending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setDateRange({});
                      setSelectedFormats([]);
                      setSelectedCreators([]);
                      setSortBy("date");
                      setSortOrder("desc");
                    }}
                  >
                    Reset
                  </Button>
                  <Button size="sm" onClick={() => setIsFilterOpen(false)}>
                    Apply Filters
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSortBy("date");
              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
            }}
            className="hidden md:flex items-center"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Date
            <ArrowUpDown className="h-3 w-3 ml-1" />
          </Button>
        </div>
      </div>

      {/* Active filters display */}
      {(selectedFormats.length > 0 || selectedCreators.length > 0 || dateRange.from || dateRange.to) && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>

          {dateRange.from && (
            <Badge variant="outline" className="flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              From: {format(dateRange.from, "MMM d, yyyy")}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => setDateRange(prev => ({ ...prev, from: undefined }))}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {dateRange.to && (
            <Badge variant="outline" className="flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              To: {format(dateRange.to, "MMM d, yyyy")}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => setDateRange(prev => ({ ...prev, to: undefined }))}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {selectedFormats.map(format => (
            <Badge key={format} variant="outline" className="flex items-center">
              {REPORT_FORMAT_ICONS[format]}
              <span className="mx-1">{format}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0"
                onClick={() => setSelectedFormats(prev => prev.filter(f => f !== format))}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {selectedCreators.map(creator => (
            <Badge key={creator} variant="outline" className="flex items-center">
              <User className="h-3 w-3 mr-1" />
              {creator}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => setSelectedCreators(prev => prev.filter(c => c !== creator))}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          <Button
            variant="ghost"
            size="sm"
            className="h-7 text-xs"
            onClick={() => {
              setDateRange({});
              setSelectedFormats([]);
              setSelectedCreators([]);
            }}
          >
            Clear All
          </Button>
        </div>
      )}

      <AnimatedCard>
        <CardContent className="p-0">
          {renderReportTable()}
        </CardContent>
      </AnimatedCard>

      {/* Create Report Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Generate New Report</DialogTitle>
            <DialogDescription>
              Create a new compliance report by following the steps below.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <ReportGenerationWizard
              userId={userId}
              organizationId={organizationId}
              onReportGenerated={handleReportCreated}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Report</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this report? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedReport && handleDeleteReport(selectedReport)}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Report Sharing Dialog */}
      {selectedReportData && (
        <ReportSharingDialog
          open={isSharingDialogOpen}
          onOpenChange={setIsSharingDialogOpen}
          reportId={selectedReport || ""}
          reportName={selectedReportData.name}
          reportType={selectedReportData.type}
        />
      )}
    </div>
  );
