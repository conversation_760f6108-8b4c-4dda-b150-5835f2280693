"use client";

import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, Dialog<PERSON>it<PERSON> } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Users,
  Mail,
  Link,
  Copy,
  Check,
  Clock,
  Calendar,
  Shield,
  Eye,
  EyeOff,
  Trash2,
  Plus,
  RefreshCw,
  Send
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Define the form schema
const sharingFormSchema = z.object({
  accessType: z.enum(["private", "organization", "specific_users", "public_link"]),
  expiryEnabled: z.boolean().default(false),
  expiryDate: z.date().optional(),
  recipients: z.array(
    z.object({
      email: z.string().email("Invalid email address"),
      permission: z.enum(["view", "edit", "admin"]),
    })
  ).optional(),
  message: z.string().optional(),
  password: z.string().optional(),
  passwordEnabled: z.boolean().default(false),
  notifyOnAccess: z.boolean().default(false),
});

type SharingFormValues = z.infer<typeof sharingFormSchema>;

interface ReportSharingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  reportId: string;
  reportName: string;
  reportType: string;
}

export function ReportSharingDialog({
  open,
  onOpenChange,
  reportId,
  reportName,
  reportType
}: ReportSharingDialogProps) {
  const [isSharing, setIsSharing] = useState(false);
  const [activeTab, setActiveTab] = useState("users");
  const [shareLink, setShareLink] = useState<string | null>(null);
  const [linkCopied, setLinkCopied] = useState(false);

  // Initialize form with default values
  const form = useForm<SharingFormValues>({
    resolver: zodResolver(sharingFormSchema),
    defaultValues: {
      accessType: "private",
      expiryEnabled: false,
      recipients: [],
      passwordEnabled: false,
      notifyOnAccess: false,
    },
  });

  // Handle form submission
  const onSubmit = async (data: SharingFormValues) => {
    setIsSharing(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate a mock share link
      if (data.accessType === "public_link") {
        const mockLink = `https://carbonix.io/shared/reports/${reportId}?token=${Math.random().toString(36).substring(2, 15)}`;
        setShareLink(mockLink);
      }

      toast({
        title: "Report Shared",
        description: data.accessType === "public_link" 
          ? "Public link has been generated successfully." 
          : "Report has been shared successfully.",
      });

      setIsSharing(false);
    } catch (error) {
      console.error("Error sharing report:", error);
      toast({
        title: "Error",
        description: "Failed to share report. Please try again.",
        variant: "destructive",
      });
      setIsSharing(false);
    }
  };

  // Handle adding a new recipient
  const handleAddRecipient = () => {
    const currentRecipients = form.getValues("recipients") || [];
    form.setValue("recipients", [
      ...currentRecipients,
      { email: "", permission: "view" }
    ]);
  };

  // Handle removing a recipient
  const handleRemoveRecipient = (index: number) => {
    const currentRecipients = form.getValues("recipients") || [];
    currentRecipients.splice(index, 1);
    form.setValue("recipients", currentRecipients);
  };

  // Handle copying link to clipboard
  const handleCopyLink = () => {
    if (shareLink) {
      navigator.clipboard.writeText(shareLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Share Report</DialogTitle>
          <DialogDescription>
            Control how this report is shared with others
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {reportType}
                </Badge>
                <span className="font-medium">{reportName}</span>
              </div>

              <Tabs defaultValue="users" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="users" className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Share with Users
                  </TabsTrigger>
                  <TabsTrigger value="link" className="flex items-center">
                    <Link className="h-4 w-4 mr-2" />
                    Get Shareable Link
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="users" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="accessType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Access Level</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(value) => {
                              field.onChange(value);
                              if (value === "specific_users" && (!form.getValues("recipients") || form.getValues("recipients").length === 0)) {
                                handleAddRecipient();
                              }
                            }}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="private" id="private" />
                              <Label htmlFor="private" className="flex items-center">
                                <Shield className="h-4 w-4 mr-2" />
                                Private (Only you)
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="organization" id="organization" />
                              <Label htmlFor="organization" className="flex items-center">
                                <Users className="h-4 w-4 mr-2" />
                                Organization (All members)
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="specific_users" id="specific_users" />
                              <Label htmlFor="specific_users" className="flex items-center">
                                <Mail className="h-4 w-4 mr-2" />
                                Specific Users
                              </Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch("accessType") === "specific_users" && (
                    <div className="space-y-3 border rounded-md p-3">
                      <div className="text-sm font-medium">Recipients</div>
                      {form.watch("recipients")?.map((_, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <div className="flex-1">
                            <FormField
                              control={form.control}
                              name={`recipients.${index}.email`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      placeholder="Email address"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="w-24">
                            <FormField
                              control={form.control}
                              name={`recipients.${index}.permission`}
                              render={({ field }) => (
                                <FormItem>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Permission" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="view">View</SelectItem>
                                      <SelectItem value="edit">Edit</SelectItem>
                                      <SelectItem value="admin">Admin</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveRecipient(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleAddRecipient}
                        className="mt-2"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Recipient
                      </Button>
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Message (Optional)</FormLabel>
                        <FormControl>
                          <textarea
                            className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            placeholder="Add a message to include with your invitation"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This message will be included in the email sent to recipients
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="link" className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="accessType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Link Access</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="public_link" id="public_link" />
                              <Label htmlFor="public_link" className="flex items-center">
                                <Link className="h-4 w-4 mr-2" />
                                Anyone with the link
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="organization" id="org_link" />
                              <Label htmlFor="org_link" className="flex items-center">
                                <Users className="h-4 w-4 mr-2" />
                                Organization members only
                              </Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4 border rounded-md p-3">
                    <FormField
                      control={form.control}
                      name="expiryEnabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Link Expiration
                            </FormLabel>
                            <FormDescription>
                              Set an expiration date for this link
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {form.watch("expiryEnabled") && (
                      <FormField
                        control={form.control}
                        name="expiryDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Expiry Date</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                value={field.value ? field.value.toISOString().split('T')[0] : ''}
                                onChange={(e) => {
                                  field.onChange(e.target.value ? new Date(e.target.value) : undefined);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="passwordEnabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Password Protection
                            </FormLabel>
                            <FormDescription>
                              Require a password to access this report
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {form.watch("passwordEnabled") && (
                      <FormField
                        control={form.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Password</FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder="Enter password"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="notifyOnAccess"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Notify me when someone accesses this report
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  {shareLink && (
                    <div className="mt-4 p-3 border rounded-md bg-muted/50">
                      <div className="text-sm font-medium mb-2">Shareable Link</div>
                      <div className="flex items-center space-x-2">
                        <Input
                          value={shareLink}
                          readOnly
                          className="font-mono text-xs"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={handleCopyLink}
                        >
                          {linkCopied ? (
                            <Check className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSharing}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSharing}
              >
                {isSharing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Sharing...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Share Report
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
