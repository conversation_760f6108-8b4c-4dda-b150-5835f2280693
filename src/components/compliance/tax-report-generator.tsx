"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { FileText, Download, Loader2, Calendar } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";

interface TaxReport {
  id: string;
  year: number;
  quarter?: number;
  format: string;
  url: string;
  createdAt: Date;
}

interface TaxReportGeneratorProps {
  userId?: string;
  organizationId?: string;
}

export function TaxReportGenerator({
  userId,
  organizationId,
}: TaxReportGeneratorProps) {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(false);
  const [reports, setReports] = useState<TaxReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedQuarter, setSelectedQuarter] = useState<number | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<string>("pdf");

  // Get available years (current year and 5 years back)
  const availableYears = Array.from({ length: 6 }, (_, i) => new Date().getFullYear() - i);

  // Load existing reports
  useEffect(() => {
    const fetchReports = async () => {
      try {
        const response = await fetch("/api/tax-reports");

        if (!response.ok) {
          throw new Error("Failed to fetch tax reports");
        }

        const data = await response.json();
        setReports(data.reports.map((report: any) => ({
          ...report,
          createdAt: new Date(report.createdAt),
        })));
      } catch (error) {
        console.error("Error fetching tax reports:", error);
        toast({
          title: "Error",
          description: "Failed to fetch tax reports",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchReports();
  }, []);

  // Generate tax report
  const generateReport = async () => {
    setIsGenerating(true);

    try {
      const response = await fetch("/api/tax-reports/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          year: selectedYear,
          quarter: selectedQuarter,
          format: selectedFormat,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to generate tax report");
      }

      const result = await response.json();

      toast({
        title: "Tax Report Generated",
        description: "Your tax report has been generated successfully.",
      });

      // Add the new report to the list
      setReports(prev => [
        {
          id: result.reportId,
          year: selectedYear,
          quarter: selectedQuarter || undefined,
          format: selectedFormat,
          url: result.reportUrl,
          createdAt: new Date(),
        },
        ...prev,
      ]);

      // Open the report in a new tab
      window.open(result.reportUrl, "_blank");
    } catch (error) {
      console.error("Error generating tax report:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate tax report",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Format report period
  const formatReportPeriod = (year: number, quarter?: number) => {
    if (quarter) {
      return `Q${quarter} ${year}`;
    }
    return `${year}`;
  };

  // Get file format icon
  const getFormatIcon = (format: string) => {
    switch (format.toLowerCase()) {
      case "pdf":
        return <FileText className="h-4 w-4" />;
      case "csv":
        return <FileText className="h-4 w-4" />;
      case "xlsx":
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tax Reports</CardTitle>
        <CardDescription>
          Generate and download tax reports for your carbon credit transactions.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="generate">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generate">Generate Report</TabsTrigger>
            <TabsTrigger value="history">Report History</TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="pt-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Year</label>
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={(value) => setSelectedYear(parseInt(value))}
                      disabled={isGenerating}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableYears.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Quarter (Optional)</label>
                    <Select
                      value={selectedQuarter?.toString() || ""}
                      onValueChange={(value) => setSelectedQuarter(value ? parseInt(value) : null)}
                      disabled={isGenerating}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All quarters" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">All quarters</SelectItem>
                        <SelectItem value="1">Q1 (Jan-Mar)</SelectItem>
                        <SelectItem value="2">Q2 (Apr-Jun)</SelectItem>
                        <SelectItem value="3">Q3 (Jul-Sep)</SelectItem>
                        <SelectItem value="4">Q4 (Oct-Dec)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Format</label>
                  <Select
                    value={selectedFormat}
                    onValueChange={setSelectedFormat}
                    disabled={isGenerating}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="rounded-lg border p-4">
                <h3 className="font-medium mb-2">Report Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Period:</span>
                    <span>{formatReportPeriod(selectedYear, selectedQuarter || undefined)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Format:</span>
                    <span className="flex items-center">
                      {getFormatIcon(selectedFormat)}
                      <span className="ml-1">{selectedFormat.toUpperCase()}</span>
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Includes:</span>
                    <span>All carbon credit transactions</span>
                  </div>
                </div>
              </div>

              <Button
                onClick={generateReport}
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="history" className="pt-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : reports.length > 0 ? (
              <div className="space-y-4">
                {reports.map((report) => (
                  <div key={report.id} className="rounded-lg border p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {getFormatIcon(report.format)}
                        <div className="ml-3">
                          <h4 className="font-medium">
                            {formatReportPeriod(report.year, report.quarter)} Tax Report
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Generated on {format(new Date(report.createdAt), "MMM d, yyyy")}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(report.url, "_blank")}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No Reports Found</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  You haven't generated any tax reports yet. Go to the Generate Report tab to create your first report.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        <p className="text-sm text-muted-foreground">
          Tax reports include all carbon credit transactions for the selected period. These reports can be used for tax filing purposes.
        </p>
      </CardFooter>
    </Card>
  );
}
