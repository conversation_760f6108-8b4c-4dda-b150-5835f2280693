"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { format } from "date-fns";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { DatePicker } from "@/components/ui/date-picker";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { HelpCard } from "@/components/ui/contextual-help";
import { AnimatedCard } from "@/components/ui/animated";
import { Switch } from "@/components/ui/switch";
import {
  FileText,
  FileSpreadsheet,
  FileJson,
  Calendar,
  Filter,
  CheckCircle,
  AlertCircle,
  Shield,
  FileCheck,
  Clock,
  ArrowRight,
  Download,
  Save,
  Loader2,
  Edit,
  Plus,
  X
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { ReportPreview } from "./report-preview";

// Define the form schema
const reportFormSchema = z.object({
  reportType: z.enum(["KYC", "AML", "VERIFICATION", "AUDIT", "CUSTOM"], {
    required_error: "Please select a report type",
  }),
  name: z.string().min(1, "Report name is required"),
  description: z.string().optional(),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }),
  format: z.enum(["PDF", "CSV", "JSON"], {
    required_error: "Please select a format",
  }),
  filters: z.record(z.any()).optional(),
  schedule: z.object({
    enabled: z.boolean().default(false),
    frequency: z.enum(["DAILY", "WEEKLY", "MONTHLY", "QUARTERLY"]).optional(),
    dayOfWeek: z.number().min(0).max(6).optional(),
    dayOfMonth: z.number().min(1).max(31).optional(),
    time: z.string().optional(),
    recipients: z.array(z.string().email("Invalid email address")).optional(),
  }).optional(),
});

type ReportFormValues = z.infer<typeof reportFormSchema>;

// Report templates
const REPORT_TEMPLATES = [
  {
    id: "kyc-summary",
    name: "KYC Summary Report",
    description: "Overview of KYC verification status for all users and organizations",
    type: "KYC",
    icon: <Shield className="h-5 w-5" />,
  },
  {
    id: "aml-activity",
    name: "AML Activity Report",
    description: "Summary of AML monitoring activities and risk assessments",
    type: "AML",
    icon: <AlertCircle className="h-5 w-5" />,
  },
  {
    id: "verification-status",
    name: "Carbon Verification Report",
    description: "Status of carbon credit verification processes",
    type: "VERIFICATION",
    icon: <FileCheck className="h-5 w-5" />,
  },
  {
    id: "audit-log",
    name: "Audit Log Report",
    description: "Comprehensive record of compliance activities",
    type: "AUDIT",
    icon: <Clock className="h-5 w-5" />,
  },
  {
    id: "custom-report",
    name: "Custom Compliance Report",
    description: "Configurable custom report with advanced filters",
    type: "CUSTOM",
    icon: <FileText className="h-5 w-5" />,
  },
];

// Format options
const FORMAT_OPTIONS = [
  {
    id: "PDF",
    name: "PDF Document",
    description: "Portable Document Format for easy sharing and printing",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    id: "CSV",
    name: "CSV Spreadsheet",
    description: "Comma-separated values for data analysis",
    icon: <FileSpreadsheet className="h-5 w-5" />,
  },
  {
    id: "JSON",
    name: "JSON Data",
    description: "Structured data format for system integration",
    icon: <FileJson className="h-5 w-5" />,
  },
];

interface ReportGenerationWizardProps {
  userId?: string;
  organizationId?: string;
  onReportGenerated?: (reportId: string) => void;
}

export function ReportGenerationWizard({
  userId,
  organizationId,
  onReportGenerated,
}: ReportGenerationWizardProps) {
  const router = useRouter();
  const [step, setStep] = useState<number>(1);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Initialize form with default values
  const form = useForm<ReportFormValues>({
    resolver: zodResolver(reportFormSchema),
    defaultValues: {
      reportType: "KYC",
      name: "",
      description: "",
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
      endDate: new Date(),
      format: "PDF",
      filters: {},
      schedule: {
        enabled: false,
        frequency: "MONTHLY",
        dayOfMonth: 1,
        time: "09:00",
        recipients: [],
      },
    },
  });

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = REPORT_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      form.setValue("reportType", template.type as any);
      form.setValue("name", template.name);
      form.setValue("description", template.description);
    }
  };

  // Handle form submission
  const onSubmit = async (data: ReportFormValues) => {
    setIsGenerating(true);

    try {
      // Prepare schedule data if enabled
      let scheduleData = null;
      if (data.schedule?.enabled) {
        scheduleData = {
          frequency: data.schedule.frequency,
          ...(data.schedule.frequency === "WEEKLY" && { dayOfWeek: data.schedule.dayOfWeek }),
          ...(["MONTHLY", "QUARTERLY"].includes(data.schedule.frequency || "") && { dayOfMonth: data.schedule.dayOfMonth }),
          time: data.schedule.time,
          recipients: data.schedule.recipients?.filter(email => email.trim() !== "") || [],
        };
      }

      const response = await fetch("/api/compliance/reporting", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          startDate: data.startDate.toISOString(),
          endDate: data.endDate.toISOString(),
          schedule: data.schedule?.enabled ? scheduleData : undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to generate report");
      }

      const result = await response.json();

      // Show appropriate toast message based on whether scheduling is enabled
      if (data.schedule?.enabled) {
        toast({
          title: "Report Scheduled",
          description: "Your compliance report has been generated and scheduled for recurring delivery.",
        });
      } else {
        toast({
          title: "Report Generated",
          description: "Your compliance report has been generated successfully.",
        });
      }

      if (onReportGenerated) {
        onReportGenerated(result.id);
      }

      // Redirect to the report view page
      router.push(`/compliance/reports/${result.id}`);
    } catch (error) {
      console.error("Error generating report:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate report",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return renderTemplateSelection();
      case 2:
        return renderReportDetails();
      case 3:
        return renderDateAndFormat();
      case 4:
        return renderFiltersAndOptions();
      case 5:
        return renderSchedulingOptions();
      case 6:
        return renderReviewAndGenerate();
      default:
        return null;
    }
  };

  // Render template selection step
  const renderTemplateSelection = () => {
    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Select Report Template",
            description: "Choose a template to pre-fill common report settings or create a custom report from scratch.",
            type: "info",
            tips: [
              "Templates save time by pre-filling report settings",
              "You can modify any pre-filled values in later steps",
              "Custom reports allow for more detailed configuration"
            ]
          }}
          className="mb-6"
        />

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-4"
          variants={staggeredListVariants}
          initial="hidden"
          animate="visible"
        >
          {REPORT_TEMPLATES.map((template) => (
            <motion.div
              key={template.id}
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedTemplate === template.id
                    ? 'border-2 border-primary ring-2 ring-primary/20'
                    : 'hover:border-primary/50'
                }`}
                onClick={() => handleTemplateSelect(template.id)}
              >
                <CardHeader className="pb-2 relative">
                  {selectedTemplate === template.id && (
                    <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
                      <CheckCircle className="h-4 w-4" />
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <div className={`p-2 rounded-full ${
                      selectedTemplate === template.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}>
                      {template.icon}
                    </div>
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                </CardContent>
                <div className={`h-1 w-full mt-auto ${
                  selectedTemplate === template.id ? 'bg-primary' : 'bg-transparent'
                }`}></div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    );
  };

  // Render report details step
  const renderReportDetails = () => {
    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Report Details",
            description: "Provide basic information about your report.",
            type: "info",
            tips: [
              "Give your report a clear, descriptive name",
              "Add a detailed description to help others understand the report's purpose",
              "The report type determines what data will be included"
            ]
          }}
          className="mb-6"
        />

        <Form {...form}>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="reportType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Report Type</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-1 md:grid-cols-2 gap-4"
                    >
                      {REPORT_TEMPLATES.map((template) => (
                        <div key={template.id}>
                          <RadioGroupItem
                            value={template.type}
                            id={template.type}
                            className="sr-only"
                          />
                          <Label
                            htmlFor={template.type}
                            className={`flex items-center space-x-2 rounded-md border-2 p-4 cursor-pointer ${
                              field.value === template.type
                                ? 'border-primary bg-primary/5'
                                : 'hover:bg-muted/50'
                            }`}
                          >
                            <div className={`p-2 rounded-full ${
                              field.value === template.type
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted'
                            }`}>
                              {template.icon}
                            </div>
                            <div>
                              <div className="font-medium">{template.type}</div>
                              <div className="text-sm text-muted-foreground">
                                {template.description.split('.')[0]}
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Report Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter report name" {...field} />
                  </FormControl>
                  <FormDescription>
                    A descriptive name for your report
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter report description"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A detailed description of the report's purpose and contents
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </Form>
      </div>
    );
  };

  // Render date and format step
  const renderDateAndFormat = () => {
    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Date Range & Format",
            description: "Select the time period for your report and the output format.",
            type: "info",
            tips: [
              "Choose a date range that covers all relevant activities",
              "Different formats are suitable for different purposes",
              "PDF is best for sharing, CSV for data analysis, and JSON for system integration"
            ]
          }}
          className="mb-6"
        />

        <Form {...form}>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <DatePicker
                      date={field.value}
                      setDate={field.onChange}
                    />
                    <FormDescription>
                      The beginning of the report period
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <DatePicker
                      date={field.value}
                      setDate={field.onChange}
                    />
                    <FormDescription>
                      The end of the report period
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="format"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Report Format</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-1 md:grid-cols-3 gap-4"
                    >
                      {FORMAT_OPTIONS.map((format) => (
                        <div key={format.id}>
                          <RadioGroupItem
                            value={format.id}
                            id={format.id}
                            className="sr-only"
                          />
                          <Label
                            htmlFor={format.id}
                            className={`flex flex-col items-center space-y-2 rounded-md border-2 p-4 cursor-pointer ${
                              field.value === format.id
                                ? 'border-primary bg-primary/5'
                                : 'hover:bg-muted/50'
                            }`}
                          >
                            <div className={`p-2 rounded-full ${
                              field.value === format.id
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted'
                            }`}>
                              {format.icon}
                            </div>
                            <div className="font-medium">{format.name}</div>
                            <div className="text-xs text-center text-muted-foreground">
                              {format.description}
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </Form>
      </div>
    );
  };

  // Render filters and options step
  const renderFiltersAndOptions = () => {
    const reportType = form.watch("reportType");

    // Define filter options based on report type
    const filterOptions = {
      KYC: [
        { id: "status", name: "Verification Status", options: ["APPROVED", "PENDING", "REJECTED", "EXPIRED"] },
        { id: "level", name: "KYC Level", options: ["BASIC", "INTERMEDIATE", "ADVANCED"] },
        { id: "documentTypes", name: "Document Types", options: ["PASSPORT", "ID_CARD", "DRIVERS_LICENSE", "UTILITY_BILL", "BANK_STATEMENT"] },
      ],
      AML: [
        { id: "riskLevel", name: "Risk Level", options: ["LOW", "MEDIUM", "HIGH", "CRITICAL"] },
        { id: "checkType", name: "Check Type", options: ["INITIAL", "PERIODIC", "TRIGGERED"] },
        { id: "alertStatus", name: "Alert Status", options: ["OPEN", "CLOSED", "INVESTIGATING"] },
      ],
      VERIFICATION: [
        { id: "status", name: "Verification Status", options: ["VERIFIED", "PENDING", "REJECTED"] },
        { id: "projectType", name: "Project Type", options: ["RENEWABLE_ENERGY", "FORESTRY", "WASTE_MANAGEMENT", "INDUSTRIAL", "WATER", "AGRICULTURE", "COMMUNITY"] },
        { id: "standard", name: "Carbon Standard", options: ["VERRA", "GOLD_STANDARD", "CDM", "AMERICAN_CARBON_REGISTRY", "CLIMATE_ACTION_RESERVE", "PLAN_VIVO"] },
      ],
      AUDIT: [
        { id: "actionType", name: "Action Type", options: ["USER_ACTION", "SYSTEM_ACTION", "ADMIN_ACTION"] },
        { id: "severity", name: "Severity", options: ["INFO", "WARNING", "ERROR", "CRITICAL"] },
        { id: "component", name: "Component", options: ["KYC", "AML", "WALLET", "MARKETPLACE", "PROJECT", "ADMIN"] },
      ],
      CUSTOM: [
        { id: "status", name: "Status", options: ["ACTIVE", "INACTIVE", "PENDING", "COMPLETED"] },
        { id: "type", name: "Type", options: ["USER", "ORGANIZATION", "PROJECT", "CREDIT", "TRANSACTION"] },
        { id: "category", name: "Category", options: ["COMPLIANCE", "FINANCIAL", "OPERATIONAL", "TECHNICAL"] },
      ],
    };

    const currentFilters = filterOptions[reportType as keyof typeof filterOptions] || [];

    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Filters & Options",
            description: "Refine your report with specific filters and options.",
            type: "info",
            tips: [
              "Add filters to focus on specific data points",
              "More filters will result in a more targeted report",
              "You can leave filters empty to include all data"
            ]
          }}
          className="mb-6"
        />

        <Form {...form}>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Report Filters</h3>
              <Badge variant="outline" className="flex items-center">
                <Filter className="h-3 w-3 mr-1" />
                {reportType} Filters
              </Badge>
            </div>

            <Separator />

            <div className="space-y-4">
              {currentFilters.map((filter) => (
                <div key={filter.id} className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                  <div>
                    <h4 className="text-sm font-medium">{filter.name}</h4>
                    <p className="text-xs text-muted-foreground">
                      Filter report by {filter.name.toLowerCase()}
                    </p>
                  </div>
                  <Select
                    onValueChange={(value) => {
                      const currentFilters = form.getValues("filters") || {};
                      form.setValue("filters", {
                        ...currentFilters,
                        [filter.id]: value,
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${filter.name}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">All {filter.name}</SelectItem>
                      {filter.options.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option.replace(/_/g, ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}

              {currentFilters.length === 0 && (
                <div className="text-center py-6">
                  <Filter className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">No Filters Available</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    This report type does not have any specific filters.
                  </p>
                </div>
              )}
            </div>

            <Separator />

            <div className="rounded-lg border p-4 bg-muted/50">
              <h4 className="font-medium mb-2">Additional Options</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeCharts"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      const currentFilters = form.getValues("filters") || {};
                      form.setValue("filters", {
                        ...currentFilters,
                        includeCharts: e.target.checked,
                      });
                    }}
                  />
                  <label htmlFor="includeCharts" className="text-sm">
                    Include charts and visualizations
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeSummary"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      const currentFilters = form.getValues("filters") || {};
                      form.setValue("filters", {
                        ...currentFilters,
                        includeSummary: e.target.checked,
                      });
                    }}
                    defaultChecked
                  />
                  <label htmlFor="includeSummary" className="text-sm">
                    Include executive summary
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeRawData"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      const currentFilters = form.getValues("filters") || {};
                      form.setValue("filters", {
                        ...currentFilters,
                        includeRawData: e.target.checked,
                      });
                    }}
                  />
                  <label htmlFor="includeRawData" className="text-sm">
                    Include raw data appendix
                  </label>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </div>
    );
  };

  // Render review and generate step
  const renderReviewAndGenerate = () => {
    const formValues = form.getValues();

    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Review & Generate",
            description: "Review your report configuration before generating.",
            type: "info",
            tips: [
              "Verify all settings are correct before generating",
              "Report generation may take a few moments to complete",
              "You can edit any section by clicking the edit button"
            ]
          }}
          className="mb-6"
        />

        <AnimatedCard>
          <CardHeader>
            <CardTitle>Report Summary</CardTitle>
            <CardDescription>
              Review your report configuration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Report Type</h3>
                  <p className="font-medium flex items-center">
                    {REPORT_TEMPLATES.find(t => t.type === formValues.reportType)?.icon}
                    <span className="ml-2">{formValues.reportType.replace(/_/g, ' ')}</span>
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Report Name</h3>
                  <p className="font-medium">{formValues.name}</p>
                </div>

                {formValues.description && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                    <p className="text-sm">{formValues.description}</p>
                  </div>
                )}

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Date Range</h3>
                  <p className="font-medium">
                    {format(formValues.startDate, "MMM d, yyyy")} - {format(formValues.endDate, "MMM d, yyyy")}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Format</h3>
                  <p className="font-medium flex items-center">
                    {FORMAT_OPTIONS.find(f => f.id === formValues.format)?.icon}
                    <span className="ml-2">{formValues.format}</span>
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Filters</h3>
                  {formValues.filters && Object.keys(formValues.filters).length > 0 ? (
                    <div className="flex flex-wrap gap-2 mt-1">
                      {Object.entries(formValues.filters).map(([key, value]) => (
                        value && (
                          <Badge key={key} variant="outline" className="text-xs">
                            {key.replace(/([A-Z])/g, ' $1').trim()}: {String(value).replace(/_/g, ' ')}
                          </Badge>
                        )
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No filters applied</p>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Scheduling</h3>
                  {formValues.schedule?.enabled ? (
                    <div className="space-y-1">
                      <p className="text-sm">
                        <Badge variant="outline" className="mr-2">Enabled</Badge>
                        {formValues.schedule.frequency} at {formValues.schedule.time || "00:00"}
                      </p>
                      {formValues.schedule.frequency === "WEEKLY" && formValues.schedule.dayOfWeek !== undefined && (
                        <p className="text-xs text-muted-foreground">
                          Every {["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"][formValues.schedule.dayOfWeek]}
                        </p>
                      )}
                      {(formValues.schedule.frequency === "MONTHLY" || formValues.schedule.frequency === "QUARTERLY") &&
                       formValues.schedule.dayOfMonth !== undefined && (
                        <p className="text-xs text-muted-foreground">
                          Day {formValues.schedule.dayOfMonth} of the {formValues.schedule.frequency.toLowerCase()}
                        </p>
                      )}
                      {formValues.schedule.recipients && formValues.schedule.recipients.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs font-medium">Recipients:</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {formValues.schedule.recipients.map((email, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {email}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Not scheduled</p>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div className="rounded-lg border p-4 bg-muted/20">
              <h3 className="font-medium mb-2">Report Preview</h3>
              <div className="border rounded-md bg-background p-4">
                <ReportPreview
                  reportId="preview"
                  reportType={formValues.reportType}
                  reportName={formValues.name}
                  startDate={formValues.startDate}
                  endDate={formValues.endDate}
                  format={formValues.format}
                  filters={formValues.filters}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep(2)}
              >
                <Edit className="h-3 w-3 mr-1" />
                Edit Details
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep(3)}
              >
                <Calendar className="h-3 w-3 mr-1" />
                Edit Date Range
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep(4)}
              >
                <Filter className="h-3 w-3 mr-1" />
                Edit Filters
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep(5)}
              >
                <Clock className="h-3 w-3 mr-1" />
                Edit Schedule
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  // Save as template
                  toast({
                    title: "Report Template Saved",
                    description: "This report configuration has been saved as a template.",
                  });
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                Save as Template
              </Button>
              <Button
                onClick={() => form.handleSubmit(onSubmit)()}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </CardFooter>
        </AnimatedCard>
      </div>
    );
  };

  // Render scheduling options step
  const renderSchedulingOptions = () => {
    return (
      <div className="space-y-6">
        <HelpCard
          content={{
            title: "Report Scheduling",
            description: "Set up automated report generation and distribution.",
            type: "info",
            tips: [
              "Schedule reports to run automatically at regular intervals",
              "Reports can be sent to multiple recipients via email",
              "Scheduled reports use the same filters and settings you've configured",
              "You can edit or cancel scheduled reports at any time"
            ]
          }}
          className="mb-6"
        />

        <AnimatedCard>
          <CardHeader>
            <CardTitle>Scheduling Options</CardTitle>
            <CardDescription>
              Configure automated report generation and distribution
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="schedule.enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Enable Scheduled Reports
                    </FormLabel>
                    <FormDescription>
                      Automatically generate this report on a recurring schedule
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {form.watch("schedule.enabled") && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="schedule.frequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Frequency</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select frequency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="DAILY">Daily</SelectItem>
                          <SelectItem value="WEEKLY">Weekly</SelectItem>
                          <SelectItem value="MONTHLY">Monthly</SelectItem>
                          <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How often the report should be generated
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch("schedule.frequency") === "WEEKLY" && (
                  <FormField
                    control={form.control}
                    name="schedule.dayOfWeek"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Day of Week</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select day of week" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="0">Sunday</SelectItem>
                            <SelectItem value="1">Monday</SelectItem>
                            <SelectItem value="2">Tuesday</SelectItem>
                            <SelectItem value="3">Wednesday</SelectItem>
                            <SelectItem value="4">Thursday</SelectItem>
                            <SelectItem value="5">Friday</SelectItem>
                            <SelectItem value="6">Saturday</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Which day of the week the report should be generated
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {(form.watch("schedule.frequency") === "MONTHLY" ||
                  form.watch("schedule.frequency") === "QUARTERLY") && (
                  <FormField
                    control={form.control}
                    name="schedule.dayOfMonth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Day of Month</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select day of month" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Array.from({ length: 31 }, (_, i) => (
                              <SelectItem key={i + 1} value={(i + 1).toString()}>
                                {i + 1}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Which day of the month the report should be generated
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="schedule.time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Time</FormLabel>
                      <FormControl>
                        <Input
                          type="time"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        What time the report should be generated (24-hour format)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <FormLabel>Recipients</FormLabel>
                  <div className="mt-2 space-y-2">
                    {form.watch("schedule.recipients")?.map((email, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          value={email}
                          onChange={(e) => {
                            const recipients = [...form.getValues("schedule.recipients") || []];
                            recipients[index] = e.target.value;
                            form.setValue("schedule.recipients", recipients);
                          }}
                          placeholder="Email address"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            const recipients = [...form.getValues("schedule.recipients") || []];
                            recipients.splice(index, 1);
                            form.setValue("schedule.recipients", recipients);
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => {
                        const recipients = [...form.getValues("schedule.recipients") || []];
                        recipients.push("");
                        form.setValue("schedule.recipients", recipients);
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Recipient
                    </Button>
                  </div>
                  <FormDescription className="mt-2">
                    Email addresses to receive the generated report
                  </FormDescription>
                </div>
              </motion.div>
            )}
          </CardContent>
        </AnimatedCard>
      </div>
    );
  };

  // Render step navigation
  const renderStepNavigation = () => {
    return (
      <div className="mt-8 flex justify-between">
        <Button
          variant="outline"
          onClick={() => setStep(step - 1)}
          disabled={step === 1 || isGenerating}
        >
          Back
        </Button>

        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4, 5, 6].map((stepNumber) => (
            <Button
              key={stepNumber}
              variant="ghost"
              size="icon"
              className={`w-8 h-8 rounded-full ${
                stepNumber === step
                  ? 'bg-primary text-primary-foreground'
                  : stepNumber < step
                  ? 'bg-primary/20 text-primary'
                  : 'bg-muted text-muted-foreground'
              }`}
              onClick={() => {
                if (stepNumber < step) {
                  setStep(stepNumber);
                }
              }}
              disabled={stepNumber > step || isGenerating}
            >
              {stepNumber < step ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                stepNumber
              )}
            </Button>
          ))}
        </div>

        {step < 6 ? (
          <Button
            onClick={() => {
              // Validate current step before proceeding
              if (step === 1) {
                if (!selectedTemplate) {
                  toast({
                    title: "Template Required",
                    description: "Please select a report template to continue.",
                    variant: "destructive",
                  });
                  return;
                }
                setStep(2);
              } else if (step === 2) {
                const { reportType, name } = form.getValues();
                if (!reportType || !name) {
                  toast({
                    title: "Required Fields Missing",
                    description: "Please fill in all required fields to continue.",
                    variant: "destructive",
                  });
                  return;
                }
                setStep(3);
              } else if (step === 3) {
                const { startDate, endDate, format } = form.getValues();
                if (!startDate || !endDate || !format) {
                  toast({
                    title: "Required Fields Missing",
                    description: "Please fill in all required fields to continue.",
                    variant: "destructive",
                  });
                  return;
                }
                if (startDate > endDate) {
                  toast({
                    title: "Invalid Date Range",
                    description: "Start date must be before end date.",
                    variant: "destructive",
                  });
                  return;
                }
                setStep(4);
              } else {
                setStep(step + 1);
              }
            }}
            disabled={isGenerating}
          >
            {step === 5 ? (
              <>
                Review
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              <>
                Continue
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        ) : (
          <Button
            onClick={() => form.handleSubmit(onSubmit)()}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Generate Report
              </>
            )}
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {renderStepContent()}
      {renderStepNavigation()}
    </div>
  );
