"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  XCircle, 
  FileText, 
  User, 
  Shield, 
  ArrowRight 
} from "lucide-react";
import { 
  VerificationStatus, 
  VerificationStep, 
  VerificationStepStatus 
} from "@/lib/compliance";
import { formatDistanceToNow } from "date-fns";

interface VerificationStatusTimelineProps {
  status: VerificationStatus;
  steps: VerificationStep[];
  onAction?: (step: VerificationStep) => void;
}

export function VerificationStatusTimeline({ 
  status, 
  steps, 
  onAction 
}: VerificationStatusTimelineProps) {
  // Get status badge variant
  const getStatusBadge = (status: VerificationStatus) => {
    switch (status) {
      case "PENDING":
        return "warning";
      case "APPROVED":
        return "success";
      case "REJECTED":
        return "destructive";
      default:
        return "outline";
    }
  };

  // Get status icon
  const getStatusIcon = (status: VerificationStatus) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4" />;
      case "APPROVED":
        return <CheckCircle className="h-4 w-4" />;
      case "REJECTED":
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Get step icon
  const getStepIcon = (step: VerificationStep) => {
    switch (step.type) {
      case "DOCUMENT_UPLOAD":
        return <FileText className="h-5 w-5" />;
      case "IDENTITY_VERIFICATION":
        return <User className="h-5 w-5" />;
      case "COMPLIANCE_CHECK":
        return <Shield className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  // Get step status icon
  const getStepStatusIcon = (status: VerificationStepStatus) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "IN_PROGRESS":
        return <Clock className="h-5 w-5 text-blue-500" />;
      case "PENDING":
        return <Clock className="h-5 w-5 text-gray-400" />;
      case "FAILED":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Status</p>
          <div className="flex items-center mt-1">
            <Badge variant={getStatusBadge(status)} className="mr-2">
              {status}
            </Badge>
            {getStatusIcon(status)}
          </div>
        </div>
        
        <div>
          <p className="text-sm text-muted-foreground">Submitted</p>
          <p className="font-medium mt-1">
            {steps[0]?.timestamp 
              ? formatDistanceToNow(new Date(steps[0].timestamp), { addSuffix: true }) 
              : "Not submitted"}
          </p>
        </div>
        
        <div>
          <p className="text-sm text-muted-foreground">Last Updated</p>
          <p className="font-medium mt-1">
            {steps.length > 0 
              ? formatDistanceToNow(
                  new Date(
                    steps
                      .filter(step => step.timestamp)
                      .sort((a, b) => 
                        new Date(b.timestamp!).getTime() - new Date(a.timestamp!).getTime()
                      )[0]?.timestamp || new Date()
                  ), 
                  { addSuffix: true }
                ) 
              : "Never"}
          </p>
        </div>
        
        <div>
          <p className="text-sm text-muted-foreground">Estimated Completion</p>
          <p className="font-medium mt-1">
            {status === "PENDING" ? "1-2 business days" : "Completed"}
          </p>
        </div>
      </div>
      
      <div className="h-px bg-border" />
      
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Verification Timeline</h3>
        
        <div className="relative pl-8 border-l-2 border-muted space-y-8">
          {steps.map((step, index) => {
            const isCompleted = step.status === "COMPLETED";
            const isInProgress = step.status === "IN_PROGRESS";
            const isFailed = step.status === "FAILED";
            const isPending = step.status === "PENDING";
            
            return (
              <div key={index} className="relative">
                <div className={`absolute -left-[25px] w-12 h-12 rounded-full flex items-center justify-center ${
                  isCompleted 
                    ? "bg-green-100 dark:bg-green-900/20" 
                    : isInProgress 
                    ? "bg-blue-100 dark:bg-blue-900/20" 
                    : isFailed 
                    ? "bg-red-100 dark:bg-red-900/20" 
                    : "bg-gray-100 dark:bg-gray-800"
                }`}>
                  {getStepStatusIcon(step.status)}
                </div>
                
                <div className="pt-1">
                  <div className="flex items-center">
                    <div className="mr-2">
                      {getStepIcon(step)}
                    </div>
                    <h4 className="font-medium">{step.title}</h4>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mt-1">
                    {step.description}
                  </p>
                  
                  {step.timestamp && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatDistanceToNow(new Date(step.timestamp), { addSuffix: true })}
                    </p>
                  )}
                  
                  {step.message && (
                    <div className={`mt-2 p-3 rounded-md ${
                      isFailed 
                        ? "bg-red-50 text-red-800 dark:bg-red-950/50 dark:text-red-300" 
                        : "bg-muted"
                    }`}>
                      <p className="text-sm">{step.message}</p>
                    </div>
                  )}
                  
                  {isInProgress && step.action && onAction && (
                    <Button 
                      size="sm" 
                      className="mt-2" 
                      onClick={() => onAction(step)}
                    >
                      {step.action}
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Button>
                  )}
                  
                  {isFailed && step.action && onAction && (
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      className="mt-2" 
                      onClick={() => onAction(step)}
                    >
                      {step.action}
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
