"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Zap,
  Clock,
  AlertTriangle,
  <PERSON>fo,
  CheckCircle,
  ArrowRight,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Save
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";

// Chain interface
interface Chain {
  id: number;
  name: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  blockExplorerUrl: string;
}

// Gas price recommendation interface
interface GasPriceRecommendation {
  slow: number;
  standard: number;
  fast: number;
  rapid: number;
}

// Transaction interface
interface Transaction {
  id: string;
  hash: string;
  type: string;
  status: string;
  timestamp: string;
  from: string;
  to: string;
  value: number;
  gasUsed: number;
  gasPrice: number;
  chainId: number;
  blockNumber?: number;
  nonce: number;
  data?: string;
  error?: string;
}

// Gas optimization props
interface GasOptimizationProps {
  transaction?: Transaction;
  chains: Chain[];
  gasPriceRecommendations: Record<number, GasPriceRecommendation>;
  onSave: (data: GasOptimizationFormValues) => void;
  onCancel: () => void;
}

// Gas optimization form schema
const gasOptimizationFormSchema = z.object({
  chainId: z.coerce.number().positive("Chain is required"),
  gasPrice: z.coerce.number().positive("Gas price is required"),
  priorityFee: z.coerce.number().min(0, "Priority fee must be non-negative"),
  gasLimit: z.coerce.number().positive("Gas limit is required"),
  maxFeePerGas: z.coerce.number().positive("Max fee per gas is required"),
  nonce: z.coerce.number().min(0, "Nonce must be non-negative"),
  speedPreset: z.enum(["slow", "standard", "fast", "rapid", "custom"]),
  enableEIP1559: z.boolean().default(true),
});

// Gas optimization form values type
type GasOptimizationFormValues = z.infer<typeof gasOptimizationFormSchema>;

export function GasOptimization({
  transaction,
  chains,
  gasPriceRecommendations,
  onSave,
  onCancel
}: GasOptimizationProps) {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Initialize form
  const form = useForm<GasOptimizationFormValues>({
    resolver: zodResolver(gasOptimizationFormSchema),
    defaultValues: {
      chainId: transaction?.chainId || chains[0]?.id || 1,
      gasPrice: transaction?.gasPrice || 0,
      priorityFee: 1,
      gasLimit: transaction?.gasUsed || 21000,
      maxFeePerGas: transaction?.gasPrice || 0,
      nonce: transaction?.nonce || 0,
      speedPreset: "standard",
      enableEIP1559: true,
    },
  });

  // Watch form values
  const watchChainId = form.watch("chainId");
  const watchGasPrice = form.watch("gasPrice");
  const watchGasLimit = form.watch("gasLimit");
  const watchSpeedPreset = form.watch("speedPreset");
  const watchEnableEIP1559 = form.watch("enableEIP1559");
  const watchMaxFeePerGas = form.watch("maxFeePerGas");
  const watchPriorityFee = form.watch("priorityFee");

  // Get current chain
  const currentChain = chains.find(chain => chain.id === watchChainId);

  // Get gas price recommendations for current chain
  const currentGasPriceRecommendations = gasPriceRecommendations[watchChainId] || {
    slow: 30,
    standard: 50,
    fast: 80,
    rapid: 120,
  };

  // Update gas price based on speed preset
  const updateGasPriceFromPreset = (preset: string) => {
    if (preset === "custom") return;

    const presetGasPrice = currentGasPriceRecommendations[preset as keyof GasPriceRecommendation];

    if (watchEnableEIP1559) {
      // For EIP-1559 transactions
      const baseFee = presetGasPrice * 0.8; // Estimate base fee as 80% of the recommended gas price
      const priorityFee = presetGasPrice * 0.2; // Estimate priority fee as 20% of the recommended gas price
      const maxFeePerGas = baseFee + priorityFee;

      form.setValue("maxFeePerGas", maxFeePerGas);
      form.setValue("priorityFee", priorityFee);
    } else {
      // For legacy transactions
      form.setValue("gasPrice", presetGasPrice);
    }
  };

  // Effect to update gas price when speed preset changes
  useState(() => {
    updateGasPriceFromPreset(watchSpeedPreset);
  });

  // Calculate transaction cost
  const calculateTransactionCost = () => {
    const gasLimit = watchGasLimit;
    const gasPrice = watchEnableEIP1559 ? watchMaxFeePerGas : watchGasPrice;
    const cost = (gasLimit * gasPrice) / 10 ** 9; // Convert from Gwei to ETH
    return cost;
  };

  const transactionCost = calculateTransactionCost();

  // Calculate estimated confirmation time
  const calculateEstimatedConfirmationTime = () => {
    const gasPrice = watchEnableEIP1559 ? watchMaxFeePerGas : watchGasPrice;

    if (gasPrice >= currentGasPriceRecommendations.rapid) {
      return "< 15 seconds";
    } else if (gasPrice >= currentGasPriceRecommendations.fast) {
      return "~ 30 seconds";
    } else if (gasPrice >= currentGasPriceRecommendations.standard) {
      return "~ 1 minute";
    } else {
      return "> 2 minutes";
    }
  };

  const estimatedConfirmationTime = calculateEstimatedConfirmationTime();

  // Handle form submission
  const handleSubmit = async (data: GasOptimizationFormValues) => {
    setIsSubmitting(true);

    try {
      // Call the onSave callback with the form data
      onSave(data);

      // Show success toast
      toast({
        title: "Gas Settings Saved",
        description: "Your gas optimization settings have been applied.",
      });
    } catch (error) {
      // Show error toast
      toast({
        title: "Error",
        description: "There was an error saving your gas settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format gas price
  const formatGasPrice = (gasPrice: number) => {
    return `${gasPrice} Gwei`;
  };

  // Format ETH value
  const formatEthValue = (value: number) => {
    return `${value.toFixed(6)} ${currentChain?.nativeCurrency.symbol || "ETH"}`;
  };

  // Get speed preset color
  const getSpeedPresetColor = (preset: string) => {
    switch (preset) {
      case "slow":
        return "text-amber-500";
      case "standard":
        return "text-blue-500";
      case "fast":
        return "text-green-500";
      case "rapid":
        return "text-purple-500";
      default:
        return "text-muted-foreground";
    }
  };

  // Get speed preset icon
  const getSpeedPresetIcon = (preset: string) => {
    switch (preset) {
      case "slow":
        return <Clock className="h-4 w-4" />;
      case "standard":
        return <CheckCircle className="h-4 w-4" />;
      case "fast":
        return <Zap className="h-4 w-4" />;
      case "rapid":
        return <Zap className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <CardTitle>Gas Optimization</CardTitle>
        <CardDescription>
          {transaction
            ? "Optimize gas settings for your transaction"
            : "Configure gas settings for new transactions"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="chainId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Network</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select network" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {chains.map((chain) => (
                          <SelectItem key={chain.id} value={chain.id.toString()}>
                            {chain.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="speedPreset"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Transaction Speed</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          field.onChange(value);
                          updateGasPriceFromPreset(value);
                        }}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                          {["slow", "standard", "fast", "rapid"].map((preset) => (
                            <FormItem key={preset} className="flex flex-row items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value={preset} />
                              </FormControl>
                              <FormLabel className={`font-normal cursor-pointer flex items-center ${getSpeedPresetColor(preset)}`}>
                                {getSpeedPresetIcon(preset)}
                                <span className="ml-2 capitalize">{preset}</span>
                              </FormLabel>
                            </FormItem>
                          ))}
                        </div>

                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="custom" />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer flex items-center">
                            <Settings className="h-4 w-4 mr-2" />
                            Custom
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormDescription>
                      {watchSpeedPreset === "slow" && "Slower but cheaper. May take longer to confirm."}
                      {watchSpeedPreset === "standard" && "Balanced speed and cost. Recommended for most transactions."}
                      {watchSpeedPreset === "fast" && "Faster confirmation with higher cost."}
                      {watchSpeedPreset === "rapid" && "Fastest confirmation with premium cost."}
                      {watchSpeedPreset === "custom" && "Manually configure gas settings."}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="enableEIP1559"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">EIP-1559 Transaction</FormLabel>
                      <FormDescription>
                        Use EIP-1559 gas model for more predictable fees
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <Separator />

              {watchEnableEIP1559 ? (
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="maxFeePerGas"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Max Fee (Gwei)</FormLabel>
                        <div className="flex items-center space-x-2">
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <Slider
                            value={[field.value]}
                            min={currentGasPriceRecommendations.slow / 2}
                            max={currentGasPriceRecommendations.rapid * 1.5}
                            step={1}
                            onValueChange={(value) => field.onChange(value[0])}
                            className="w-full max-w-xs"
                          />
                        </div>
                        <FormDescription>
                          Maximum amount you're willing to pay per gas unit
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priorityFee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority Fee (Gwei)</FormLabel>
                        <div className="flex items-center space-x-2">
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <Slider
                            value={[field.value]}
                            min={0}
                            max={currentGasPriceRecommendations.standard}
                            step={0.1}
                            onValueChange={(value) => field.onChange(value[0])}
                            className="w-full max-w-xs"
                          />
                        </div>
                        <FormDescription>
                          Tip to incentivize miners to include your transaction
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              ) : (
                <FormField
                  control={form.control}
                  name="gasPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gas Price (Gwei)</FormLabel>
                      <div className="flex items-center space-x-2">
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <Slider
                          value={[field.value]}
                          min={currentGasPriceRecommendations.slow / 2}
                          max={currentGasPriceRecommendations.rapid * 1.5}
                          step={1}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="w-full max-w-xs"
                        />
                      </div>
                      <FormDescription>
                        Amount you're willing to pay per gas unit
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="gasLimit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gas Limit</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormDescription>
                      Maximum gas units for this transaction
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {transaction && (
                <FormField
                  control={form.control}
                  name="nonce"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nonce</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormDescription>
                        Transaction sequence number
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <Separator />

              <div className="rounded-lg border p-4 space-y-3">
                <h3 className="font-medium">Transaction Cost Estimate</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Gas Price</div>
                    <div className="font-medium">
                      {watchEnableEIP1559
                        ? `${formatGasPrice(watchMaxFeePerGas)} (max)`
                        : formatGasPrice(watchGasPrice)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Gas Limit</div>
                    <div className="font-medium">{watchGasLimit.toLocaleString()} units</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Estimated Cost</div>
                    <div className="font-medium">{formatEthValue(transactionCost)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Estimated Time</div>
                    <div className="font-medium">{estimatedConfirmationTime}</div>
                  </div>
                </div>

                {watchSpeedPreset === "slow" && (
                  <Alert variant="warning" className="mt-2">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      This transaction may take longer to confirm during periods of network congestion.
                    </AlertDescription>
                  </Alert>
                )}

                {watchSpeedPreset === "rapid" && (
                  <Alert variant="warning" className="mt-2">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      You're paying a premium for faster confirmation. This is significantly higher than the current network average.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          onClick={form.handleSubmit(handleSubmit)}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
              Processing...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Gas Settings
            </>
          )}
        </Button>
      </CardFooter>
    </AnimatedCard>
  );
