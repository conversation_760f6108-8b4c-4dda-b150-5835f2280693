"use client";

import { useState, useEffect } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>eader, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Loader2, CheckCircle2, XCircle, AlertCircle, ExternalLink } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

export type TransactionStatus = "pending" | "mining" | "confirmed" | "failed" | "dropped";

export interface TransactionStatusProps {
  txHash: string;
  network: string;
  isTestnet?: boolean;
  onComplete?: (success: boolean) => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export function TransactionStatus({
  txHash,
  network,
  isTestnet = true,
  onComplete,
  autoClose = false,
  autoCloseDelay = 5000,
}: TransactionStatusProps) {
  const [status, setStatus] = useState<TransactionStatus>("pending");
  const [progress, setProgress] = useState(0);
  const [confirmations, setConfirmations] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [blockExplorerUrl, setBlockExplorerUrl] = useState<string>("");
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Get block explorer URL based on network
  useEffect(() => {
    const getBlockExplorerUrl = () => {
      const baseUrl = isTestnet
        ? network === "ethereum"
          ? "https://sepolia.etherscan.io"
          : network === "polygon"
          ? "https://mumbai.polygonscan.com"
          : network === "arbitrum"
          ? "https://sepolia.arbiscan.io"
          : network === "optimism"
          ? "https://sepolia-optimism.etherscan.io"
          : "https://sepolia.basescan.org"
        : network === "ethereum"
        ? "https://etherscan.io"
        : network === "polygon"
        ? "https://polygonscan.com"
        : network === "arbitrum"
        ? "https://arbiscan.io"
        : network === "optimism"
        ? "https://optimistic.etherscan.io"
        : "https://basescan.org";

      return `${baseUrl}/tx/${txHash}`;
    };

    setBlockExplorerUrl(getBlockExplorerUrl());
  }, [txHash, network, isTestnet]);

  // Poll for transaction status
  useEffect(() => {
    const pollTransactionStatus = async () => {
      try {
        const response = await fetch(`/api/blockchain/transaction-status?txHash=${txHash}&network=${network}&isTestnet=${isTestnet}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to get transaction status");
        }

        const data = await response.json();
        
        setStatus(data.status);
        setConfirmations(data.confirmations || 0);
        setError(data.error || null);
        
        // Update progress based on status
        if (data.status === "pending") {
          setProgress(10);
          setEstimatedTimeRemaining(data.estimatedTime || 60);
        } else if (data.status === "mining") {
          setProgress(50);
          setEstimatedTimeRemaining(data.estimatedTime || 30);
        } else if (data.status === "confirmed") {
          setProgress(100);
          setEstimatedTimeRemaining(null);
          
          // Call onComplete callback
          if (onComplete) {
            onComplete(true);
          }
          
          // Auto close after delay if enabled
          if (autoClose) {
            setTimeout(() => {
              if (onComplete) {
                onComplete(true);
              }
            }, autoCloseDelay);
          }
          
          // Clear polling interval
          if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
          }
        } else if (data.status === "failed" || data.status === "dropped") {
          setProgress(100);
          setEstimatedTimeRemaining(null);
          
          // Call onComplete callback
          if (onComplete) {
            onComplete(false);
          }
          
          // Clear polling interval
          if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
          }
        }
      } catch (error) {
        console.error("Error polling transaction status:", error);
        setError(error instanceof Error ? error.message : "Failed to get transaction status");
      }
    };

    // Initial poll
    pollTransactionStatus();
    
    // Set up polling interval (every 5 seconds)
    const interval = setInterval(pollTransactionStatus, 5000);
    setPollingInterval(interval);
    
    // Clean up interval on unmount
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [txHash, network, isTestnet, onComplete, autoClose, autoCloseDelay]);

  // Format estimated time remaining
  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds} seconds`;
    } else {
      return `${Math.floor(seconds / 60)} minutes ${seconds % 60} seconds`;
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: TransactionStatus) => {
    switch (status) {
      case "pending":
        return "outline";
      case "mining":
        return "secondary";
      case "confirmed":
        return "success";
      case "failed":
      case "dropped":
        return "destructive";
      default:
        return "outline";
    }
  };

  // Get status icon
  const getStatusIcon = (status: TransactionStatus) => {
    switch (status) {
      case "pending":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case "mining":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case "confirmed":
        return <CheckCircle2 className="h-4 w-4" />;
      case "failed":
        return <XCircle className="h-4 w-4" />;
      case "dropped":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Loader2 className="h-4 w-4 animate-spin" />;
    }
  };

  // Get status message
  const getStatusMessage = (status: TransactionStatus) => {
    switch (status) {
      case "pending":
        return "Transaction is pending. Waiting for it to be included in a block...";
      case "mining":
        return "Transaction is being mined. This may take a few minutes...";
      case "confirmed":
        return `Transaction confirmed with ${confirmations} confirmations.`;
      case "failed":
        return `Transaction failed: ${error || "Unknown error"}`;
      case "dropped":
        return "Transaction was dropped from the network. Please try again.";
      default:
        return "Checking transaction status...";
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Transaction Status</CardTitle>
            <CardDescription>
              Tracking your blockchain transaction
            </CardDescription>
          </div>
          <Badge variant={getStatusBadgeVariant(status)}>
            <span className="flex items-center gap-1">
              {getStatusIcon(status)}
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} />
        </div>
        
        <div className="rounded-lg bg-muted p-4">
          <p className="text-sm">{getStatusMessage(status)}</p>
          
          {estimatedTimeRemaining !== null && (
            <p className="mt-2 text-xs text-muted-foreground">
              Estimated time remaining: {formatTimeRemaining(estimatedTimeRemaining)}
            </p>
          )}
          
          <div className="mt-3 text-xs">
            <span className="font-medium">Transaction Hash: </span>
            <span className="font-mono">{txHash.slice(0, 10)}...{txHash.slice(-8)}</span>
          </div>
          
          <div className="mt-1 text-xs">
            <span className="font-medium">Network: </span>
            <span>{network.charAt(0).toUpperCase() + network.slice(1)} {isTestnet ? "(Testnet)" : "(Mainnet)"}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            navigator.clipboard.writeText(txHash);
            toast({
              title: "Transaction hash copied",
              description: "The transaction hash has been copied to your clipboard.",
            });
          }}
        >
          Copy Tx Hash
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open(blockExplorerUrl, "_blank")}
        >
          <ExternalLink className="mr-2 h-4 w-4" />
          View on Explorer
        </Button>
      </CardFooter>
    </Card>
  );
}
