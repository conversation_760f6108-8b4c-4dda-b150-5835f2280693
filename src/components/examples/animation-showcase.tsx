"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardT<PERSON>le,
  AnimatedCardDescription,
  AnimatedCardContent,
  AnimatedCardFooter,
  AnimatedInput,
  AnimatedForm,
  AnimatedFormItem,
  AnimatedFormLabel,
  AnimatedFormDescription,
  AnimatedList,
  Grid as AnimatedG<PERSON>,
  AnimatedIcon,
  NotificationBell as AnimatedNotificationBell,
  AnimatedTooltip,
  AnimatedPopover,
  AnimatedBadge,
  StatusIndicator as AnimatedStatusIndicator,
  AnimatedTabs,
  AnimatedSkeleton,
  AnimatedLoadingDots,
  AnimatedProgressBar,
  AnimatedSpinner,
  SuccessIcon as AnimatedSuccess,
  ErrorIcon as AnimatedError,
  AnimatedFeedback,
  showAnimatedToast,
  PageTransition,
  StaggeredListComponent as StaggeredList
} from "@/components/ui/animated";
import {
  Bell,
  Info,
  Settings,
  User,
  CreditCard,
  BarChart,
  Calendar,
  Wallet
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

/**
 * Animation showcase component that demonstrates various micro-animations and micro-interactions
 */
export function AnimationShowcase() {
  const [activeTab, setActiveTab] = useState("buttons");
  const [feedbackVisible, setFeedbackVisible] = useState(false);
  const [feedbackType, setFeedbackType] = useState<"success" | "error" | "warning" | "info">("success");
  const [progress, setProgress] = useState(30);

  // Sample data for lists
  const items = [
    { id: "1", name: "Item 1", description: "Description for item 1" },
    { id: "2", name: "Item 2", description: "Description for item 2" },
    { id: "3", name: "Item 3", description: "Description for item 3" },
    { id: "4", name: "Item 4", description: "Description for item 4" },
    { id: "5", name: "Item 5", description: "Description for item 5" },
  ];

  // Show toast notification
  const handleShowToast = (type: "success" | "error" | "warning" | "info") => {
    showAnimatedToast(
      type,
      `${type.charAt(0).toUpperCase() + type.slice(1)} Toast`,
      `This is a ${type} toast notification with animation.`
    );
  };

  // Show feedback message
  const handleShowFeedback = (type: "success" | "error" | "warning" | "info") => {
    setFeedbackType(type);
    setFeedbackVisible(true);
  };

  // Increment progress bar
  const handleIncrementProgress = () => {
    setProgress((prev) => (prev >= 100 ? 0 : prev + 10));
  };

  return (
    <PageTransition>
      <div className="container py-8 space-y-8">
        <h1 className="text-3xl font-bold">Animation Showcase</h1>
        <p className="text-muted-foreground">
          This page demonstrates various micro-animations and micro-interactions available in the Carbonix platform.
        </p>

        {/* Feedback message */}
        {feedbackVisible && (
          <AnimatedFeedback
            type={feedbackType}
            message={`This is a ${feedbackType} message with animation.`}
            onClose={() => setFeedbackVisible(false)}
          />
        )}

        {/* Tabs */}
        <AnimatedTabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 md:grid-cols-8 w-full">
            <TabsTrigger value="buttons">Buttons</TabsTrigger>
            <TabsTrigger value="cards">Cards</TabsTrigger>
            <TabsTrigger value="forms">Forms</TabsTrigger>
            <TabsTrigger value="lists">Lists</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
            <TabsTrigger value="loading">Loading</TabsTrigger>
            <TabsTrigger value="icons">Icons</TabsTrigger>
            <TabsTrigger value="tooltips">Tooltips</TabsTrigger>
          </TabsList>

          {/* Buttons Tab */}
          <TabsContent value="buttons" className="space-y-6">
            <h2 className="text-2xl font-semibold">Animated Buttons</h2>
            <div className="flex flex-wrap gap-4">
              <AnimatedButton>Default Button</AnimatedButton>
              <AnimatedButton variant="secondary">Secondary Button</AnimatedButton>
              <AnimatedButton variant="destructive">Destructive Button</AnimatedButton>
              <AnimatedButton variant="outline">Outline Button</AnimatedButton>
              <AnimatedButton variant="ghost">Ghost Button</AnimatedButton>
              <AnimatedButton variant="link">Link Button</AnimatedButton>
              <AnimatedButton animationVariant="hoverScale">Scale Button</AnimatedButton>
            </div>
          </TabsContent>

          {/* Cards Tab */}
          <TabsContent value="cards" className="space-y-6">
            <h2 className="text-2xl font-semibold">Animated Cards</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Card Title</AnimatedCardTitle>
                  <AnimatedCardDescription>Card Description</AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <p>This is a basic card with hover lift animation.</p>
                </AnimatedCardContent>
                <AnimatedCardFooter>
                  <AnimatedButton size="sm">Action</AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>

              <AnimatedCard animationVariant="hoverScale">
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Scale Card</AnimatedCardTitle>
                  <AnimatedCardDescription>With scale animation</AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <p>This card scales up slightly on hover.</p>
                </AnimatedCardContent>
                <AnimatedCardFooter>
                  <AnimatedButton size="sm" variant="outline">Action</AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>

              <AnimatedCard className="border-primary">
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Featured Card</AnimatedCardTitle>
                  <AnimatedCardDescription>With primary border</AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <p>This card has a primary border to highlight it.</p>
                </AnimatedCardContent>
                <AnimatedCardFooter>
                  <AnimatedButton size="sm" variant="secondary">Action</AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Forms Tab */}
          <TabsContent value="forms" className="space-y-6">
            <h2 className="text-2xl font-semibold">Animated Forms</h2>
            <AnimatedForm className="space-y-6 max-w-md">
              <AnimatedFormItem>
                <AnimatedFormLabel>Name</AnimatedFormLabel>
                <AnimatedInput placeholder="Enter your name" />
                <AnimatedFormDescription>Your full name as it appears on your ID.</AnimatedFormDescription>
              </AnimatedFormItem>

              <AnimatedFormItem>
                <AnimatedFormLabel>Email</AnimatedFormLabel>
                <AnimatedInput type="email" placeholder="Enter your email" />
              </AnimatedFormItem>

              <AnimatedFormItem>
                <AnimatedFormLabel>Password</AnimatedFormLabel>
                <AnimatedInput type="password" placeholder="Enter your password" />
              </AnimatedFormItem>

              <AnimatedButton type="submit">Submit</AnimatedButton>
            </AnimatedForm>
          </TabsContent>

          {/* Lists Tab */}
          <TabsContent value="lists" className="space-y-6">
            <h2 className="text-2xl font-semibold">Animated Lists</h2>

            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-medium mb-4">Staggered List</h3>
                <AnimatedList
                  items={items}
                  keyExtractor={(item) => item.id}
                  className="space-y-2"
                  renderItem={(item) => (
                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">{item.description}</p>
                    </div>
                  )}
                  onItemClick={(item) => alert(`Clicked: ${item.name}`)}
                />
              </div>

              <div>
                <h3 className="text-xl font-medium mb-4">Animated Grid</h3>
                <AnimatedGrid
                  items={items}
                  keyExtractor={(item) => item.id}
                  gridCols="grid-cols-1 md:grid-cols-3"
                  renderItem={(item) => (
                    <AnimatedCard className="h-full">
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>{item.name}</AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <p>{item.description}</p>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  )}
                />
              </div>
            </div>
          </TabsContent>

          {/* Feedback Tab */}
          <TabsContent value="feedback" className="space-y-6">
            <h2 className="text-2xl font-semibold">Animated Feedback</h2>

            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-xl font-medium">Toast Notifications</h3>
                <div className="flex flex-wrap gap-2">
                  <AnimatedButton onClick={() => handleShowToast("success")}>Success Toast</AnimatedButton>
                  <AnimatedButton onClick={() => handleShowToast("error")} variant="destructive">Error Toast</AnimatedButton>
                  <AnimatedButton onClick={() => handleShowToast("warning")} variant="outline">Warning Toast</AnimatedButton>
                  <AnimatedButton onClick={() => handleShowToast("info")} variant="secondary">Info Toast</AnimatedButton>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Feedback Messages</h3>
                <div className="flex flex-wrap gap-2">
                  <AnimatedButton onClick={() => handleShowFeedback("success")}>Success Message</AnimatedButton>
                  <AnimatedButton onClick={() => handleShowFeedback("error")} variant="destructive">Error Message</AnimatedButton>
                  <AnimatedButton onClick={() => handleShowFeedback("warning")} variant="outline">Warning Message</AnimatedButton>
                  <AnimatedButton onClick={() => handleShowFeedback("info")} variant="secondary">Info Message</AnimatedButton>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Status Indicators</h3>
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <AnimatedStatusIndicator status="idle" />
                    <span>Idle</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AnimatedStatusIndicator status="loading" />
                    <span>Loading</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AnimatedStatusIndicator status="success" />
                    <span>Success</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AnimatedStatusIndicator status="error" />
                    <span>Error</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AnimatedStatusIndicator status="warning" />
                    <span>Warning</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Badges</h3>
                <div className="flex flex-wrap gap-2">
                  <AnimatedBadge>Default</AnimatedBadge>
                  <AnimatedBadge variant="secondary">Secondary</AnimatedBadge>
                  <AnimatedBadge variant="destructive">Destructive</AnimatedBadge>
                  <AnimatedBadge variant="outline">Outline</AnimatedBadge>
                  <AnimatedBadge pulse>Pulse</AnimatedBadge>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Loading Tab */}
          <TabsContent value="loading" className="space-y-6">
            <h2 className="text-2xl font-semibold">Loading Animations</h2>

            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-xl font-medium">Skeletons</h3>
                <div className="space-y-2 max-w-md">
                  <AnimatedSkeleton className="h-8 w-full" />
                  <AnimatedSkeleton className="h-4 w-3/4" />
                  <AnimatedSkeleton className="h-4 w-1/2" />
                  <AnimatedSkeleton className="h-32 w-full" />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Spinners</h3>
                <div className="flex items-center gap-6">
                  <AnimatedSpinner size="sm" />
                  <AnimatedSpinner size="md" />
                  <AnimatedSpinner size="lg" />
                  <AnimatedSpinner size="xl" />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Loading Dots</h3>
                <div className="flex items-center gap-6">
                  <AnimatedLoadingDots size="sm" />
                  <AnimatedLoadingDots size="md" />
                  <AnimatedLoadingDots size="lg" />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Progress Bars</h3>
                <div className="space-y-4 max-w-md">
                  <AnimatedProgressBar value={progress} />
                  <AnimatedProgressBar indeterminate />
                  <div className="flex items-center gap-2">
                    <AnimatedButton size="sm" onClick={handleIncrementProgress}>
                      Increment Progress
                    </AnimatedButton>
                    <span>{progress}%</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Icons Tab */}
          <TabsContent value="icons" className="space-y-6">
            <h2 className="text-2xl font-semibold">Animated Icons</h2>

            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-xl font-medium">Interactive Icons</h3>
                <div className="flex items-center gap-6">
                  <AnimatedIcon icon={<Settings />} />
                  <AnimatedIcon icon={<User />} animationVariant="hoverScale" />
                  <AnimatedIcon icon={<CreditCard />} size="lg" />
                  <AnimatedIcon icon={<BarChart />} size="xl" />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Notification Bell</h3>
                <div className="flex items-center gap-6">
                  <AnimatedNotificationBell icon={<Bell />} hasNotifications={false} />
                  <AnimatedNotificationBell icon={<Bell />} hasNotifications={true} />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Success/Error Icons</h3>
                <div className="flex items-center gap-6">
                  <AnimatedSuccess />
                  <AnimatedSuccess size="lg" />
                  <AnimatedError />
                  <AnimatedError size="lg" />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Tooltips Tab */}
          <TabsContent value="tooltips" className="space-y-6">
            <h2 className="text-2xl font-semibold">Tooltips & Popovers</h2>

            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-xl font-medium">Animated Tooltips</h3>
                <div className="flex items-center gap-6">
                  <AnimatedTooltip
                    trigger={<AnimatedButton variant="outline">Hover Me</AnimatedButton>}
                    content="This is a tooltip with animation"
                  />

                  <AnimatedTooltip
                    trigger={<AnimatedIcon icon={<Info />} />}
                    content="Information tooltip"
                    side="right"
                  />

                  <AnimatedTooltip
                    trigger={<AnimatedBadge>New</AnimatedBadge>}
                    content="This feature is new"
                    side="bottom"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-medium">Animated Popovers</h3>
                <div className="flex items-center gap-6">
                  <AnimatedPopover
                    trigger={<AnimatedButton>Click Me</AnimatedButton>}
                    content={
                      <div className="space-y-2">
                        <h4 className="font-medium">Popover Title</h4>
                        <p className="text-sm text-muted-foreground">This is a popover with animation.</p>
                        <AnimatedButton size="sm">Action</AnimatedButton>
                      </div>
                    }
                  />

                  <AnimatedPopover
                    trigger={<AnimatedIcon icon={<Calendar />} size="lg" />}
                    content={
                      <div className="space-y-2">
                        <h4 className="font-medium">Calendar</h4>
                        <p className="text-sm text-muted-foreground">Select a date from the calendar.</p>
                      </div>
                    }
                    side="right"
                  />

                  <AnimatedPopover
                    trigger={<AnimatedIcon icon={<Wallet />} size="lg" />}
                    content={
                      <div className="space-y-2">
                        <h4 className="font-medium">Wallet</h4>
                        <p className="text-sm text-muted-foreground">Your wallet balance: $1,234.56</p>
                        <AnimatedButton size="sm" variant="outline">View Details</AnimatedButton>
                      </div>
                    }
                    side="top"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </AnimatedTabs>
      </div>
    </PageTransition>
  );
}
