'use client';

import { ContextNudge } from '@/components/profile/context-nudge';
import { useContextNudges } from '@/hooks/use-context-nudges';

interface VerificationDocumentNudgesProps {
  organizationId: string;
}

export function VerificationDocumentNudges({ organizationId }: VerificationDocumentNudgesProps) {
  const { nudges, loading } = useContextNudges({
    organizationId,
    context: 'verification'
  });

  if (loading || nudges.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {nudges.map(nudge => (
        <ContextNudge
          key={nudge.id}
          organizationId={organizationId}
          title={nudge.title}
          description={nudge.description}
          fields={nudge.fields}
          nudgeId={nudge.id}
          variant={nudge.variant}
        />
      ))}
    </div>
  );
}
