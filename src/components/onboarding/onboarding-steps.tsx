'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { CheckCircle, Circle } from 'lucide-react';

interface OnboardingStep {
  id: number;
  name: string;
  path: string;
  completed: boolean;
  current: boolean;
}

interface OnboardingStepsProps {
  organizationId?: string | null;
  currentStep: number;
}

export function OnboardingSteps({ organizationId, currentStep }: OnboardingStepsProps) {
  const pathname = usePathname();
  
  const [steps, setSteps] = useState<OnboardingStep[]>([
    {
      id: 1,
      name: 'Organization',
      path: '/onboarding',
      completed: currentStep > 1,
      current: currentStep === 1,
    },
    {
      id: 2,
      name: 'Team',
      path: '/onboarding',
      completed: currentStep > 2,
      current: currentStep === 2,
    },
    {
      id: 3,
      name: 'Subscription',
      path: '/onboarding',
      completed: currentStep > 3,
      current: currentStep === 3,
    },
    {
      id: 4,
      name: 'Wallet',
      path: organizationId ? `/onboarding/wallet?organizationId=${organizationId}` : '/onboarding/wallet',
      completed: pathname === '/onboarding/wallet' && currentStep > 4,
      current: pathname === '/onboarding/wallet',
    },
    {
      id: 5,
      name: 'Verification',
      path: organizationId ? `/onboarding/verification?organizationId=${organizationId}` : '/onboarding/verification',
      completed: pathname === '/onboarding/verification' && currentStep > 5,
      current: pathname === '/onboarding/verification',
    },
  ]);

  useEffect(() => {
    // Update steps when organizationId or currentStep changes
    setSteps(prevSteps => 
      prevSteps.map(step => {
        if (step.id === 4) {
          return {
            ...step,
            path: organizationId ? `/onboarding/wallet?organizationId=${organizationId}` : '/onboarding/wallet',
            completed: currentStep > 4 || (pathname === '/onboarding/wallet' && step.completed),
            current: pathname === '/onboarding/wallet' || currentStep === 4,
          };
        }
        if (step.id === 5) {
          return {
            ...step,
            path: organizationId ? `/onboarding/verification?organizationId=${organizationId}` : '/onboarding/verification',
            completed: currentStep > 5 || (pathname === '/onboarding/verification' && step.completed),
            current: pathname === '/onboarding/verification' || currentStep === 5,
          };
        }
        if (step.id < 4) {
          return {
            ...step,
            completed: currentStep > step.id,
            current: currentStep === step.id,
          };
        }
        return step;
      })
    );
  }, [organizationId, currentStep, pathname]);

  return (
    <nav aria-label="Progress" className="w-full max-w-4xl mx-auto mb-8">
      <ol role="list" className="flex items-center">
        {steps.map((step, stepIdx) => (
          <li
            key={step.name}
            className={cn(
              stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : '',
              'relative flex-1'
            )}
          >
            {step.completed ? (
              <div className="flex flex-col items-start group">
                <span className="flex items-center">
                  <span className="relative flex h-5 w-5 items-center justify-center">
                    <CheckCircle
                      className="h-5 w-5 text-primary"
                      aria-hidden="true"
                    />
                  </span>
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    {step.name}
                  </span>
                </span>
                {stepIdx !== steps.length - 1 ? (
                  <span
                    className="absolute top-2.5 left-2.5 h-0.5 w-full bg-primary"
                    aria-hidden="true"
                  />
                ) : null}
              </div>
            ) : step.current ? (
              <div className="flex flex-col items-start" aria-current="step">
                <span className="flex items-center">
                  <span className="relative flex h-5 w-5 items-center justify-center">
                    <span
                      className="absolute h-4 w-4 rounded-full bg-primary"
                      aria-hidden="true"
                    />
                    <span className="relative block h-2 w-2 rounded-full bg-white" />
                  </span>
                  <span className="ml-3 text-sm font-medium text-primary">
                    {step.name}
                  </span>
                </span>
                {stepIdx !== steps.length - 1 ? (
                  <span
                    className="absolute top-2.5 left-2.5 h-0.5 w-full bg-gray-200"
                    aria-hidden="true"
                  />
                ) : null}
              </div>
            ) : (
              <div className="flex flex-col items-start">
                <span className="flex items-center">
                  <span className="relative flex h-5 w-5 items-center justify-center">
                    <Circle
                      className="h-5 w-5 text-gray-300"
                      aria-hidden="true"
                    />
                  </span>
                  <span className="ml-3 text-sm font-medium text-gray-500">
                    {step.name}
                  </span>
                </span>
                {stepIdx !== steps.length - 1 ? (
                  <span
                    className="absolute top-2.5 left-2.5 h-0.5 w-full bg-gray-200"
                    aria-hidden="true"
                  />
                ) : null}
              </div>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
