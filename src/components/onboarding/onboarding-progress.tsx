'use client';

import { useEffect, useState } from 'react';
import { CheckCircle2, Circle, HelpCircle } from 'lucide-react';
import { OnboardingStep } from '@/lib/onboarding-state';
import { cn } from '@/lib/utils';

interface OnboardingProgressProps {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  skippedSteps: OnboardingStep[];
}

interface StepItem {
  id: OnboardingStep;
  name: string;
  description: string;
}

export function OnboardingProgress({
  currentStep,
  completedSteps,
  skippedSteps,
}: OnboardingProgressProps) {
  const steps: StepItem[] = [
    {
      id: OnboardingStep.ORGANIZATION_DETAILS,
      name: 'Organization',
      description: 'Create your organization profile'
    },
    {
      id: OnboardingStep.TEAM_INVITATIONS,
      name: 'Team',
      description: 'Invite team members'
    },
    {
      id: OnboardingStep.SUBSCRIPTION,
      name: 'Subscription',
      description: 'Select a subscription plan'
    },
    {
      id: OnboardingStep.WALLET_SETUP,
      name: 'Wallet',
      description: 'Set up your carbon wallet'
    },
    {
      id: OnboardingStep.VERIFICATION,
      name: 'Verification',
      description: 'Submit verification documents'
    }
  ];

  return (
    <div className="py-6">
      <ol className="flex items-center w-full">
        {steps.map((step, index) => {
          const isCompleted = completedSteps?.includes(step.id);
          const isSkipped = skippedSteps?.includes(step.id);
          const isCurrent = currentStep === step.id;

          return (
            <li
              key={step.id}
              className={cn(
                "flex items-center",
                index < steps.length - 1 ? "w-full" : "",
                isSkipped ? "text-gray-400" : ""
              )}
            >
              <div className="flex flex-col items-center">
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full",
                  isCompleted ? "bg-green-100" :
                  isCurrent ? "bg-blue-100" :
                  "bg-gray-100"
                )}>
                  {isCompleted ? (
                    <CheckCircle2 className="w-5 h-5 text-green-500" />
                  ) : isSkipped ? (
                    <HelpCircle className="w-5 h-5 text-gray-400" />
                  ) : (
                    <Circle
                      className={cn(
                        "w-5 h-5",
                        isCurrent ? "text-blue-500" : "text-gray-500"
                      )}
                    />
                  )}
                </div>
                <span className={cn(
                  "text-xs mt-1",
                  isCurrent ? "font-medium" : "font-normal",
                  isSkipped ? "text-gray-400" : ""
                )}>
                  {step.name}
                </span>
              </div>

              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "w-full h-0.5 mx-2",
                    isCompleted ? "bg-green-500" : "bg-gray-200"
                  )}
                />
              )}
            </li>
          );
        })}
      </ol>

      <div className="mt-2 text-center text-sm text-gray-500">
        {steps.find(s => s.id === currentStep)?.description || 'Complete your onboarding'}
      </div>
    </div>
  );
}
