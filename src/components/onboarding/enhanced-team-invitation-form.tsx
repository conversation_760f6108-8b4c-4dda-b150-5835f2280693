'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { toast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Loader2, 
  X, 
  Plus, 
  UserPlus, 
  Mail, 
  Upload, 
  AlertCircle, 
  Info, 
  CheckCircle, 
  User, 
  Shield, 
  FileText, 
  Trash2 
} from 'lucide-react';
import { HelpCard, ContextualHelp } from '@/components/ui/contextual-help';
import { OnboardingTour } from './onboarding-tour';
import { simpleEmailSchema } from '@/lib/validation/schemas';

// Schema for single invitation
const inviteSchema = z.object({
  email: simpleEmailSchema,
  name: z.string().optional(),
  role: z.enum(['USER', 'ADMIN', 'FINANCE', 'COMPLIANCE']),
  message: z.string().optional(),
});

// Schema for bulk invitations
const bulkInviteSchema = z.object({
  emails: z.string().min(1, 'Please enter at least one email address'),
  role: z.enum(['USER', 'ADMIN', 'FINANCE', 'COMPLIANCE']),
  message: z.string().optional(),
});

// Schema for CSV upload
const csvUploadSchema = z.object({
  file: z.instanceof(File).refine(file => file.size > 0, 'Please select a file'),
  role: z.enum(['USER', 'ADMIN', 'FINANCE', 'COMPLIANCE']),
  message: z.string().optional(),
});

type InviteFormValues = z.infer<typeof inviteSchema>;
type BulkInviteFormValues = z.infer<typeof bulkInviteSchema>;
type CsvUploadFormValues = z.infer<typeof csvUploadSchema>;

interface InvitedTeamMember {
  id: string;
  email: string;
  name?: string;
  role: string;
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'FAILED';
  createdAt: Date;
}

interface EnhancedTeamInvitationFormProps {
  organizationId: string;
  onSuccess?: () => void;
  onSkip?: () => void;
}

export function EnhancedTeamInvitationForm({ 
  organizationId, 
  onSuccess, 
  onSkip 
}: EnhancedTeamInvitationFormProps) {
  const [activeTab, setActiveTab] = useState<string>('single');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitedMembers, setInvitedMembers] = useState<InvitedTeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [csvData, setCsvData] = useState<string | null>(null);
  const [csvPreview, setCsvPreview] = useState<string[][]>([]);
  const [showTour, setShowTour] = useState(false);

  // Form for single invitation
  const singleForm = useForm<InviteFormValues>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      name: '',
      role: 'USER',
      message: '',
    },
  });

  // Form for bulk invitations
  const bulkForm = useForm<BulkInviteFormValues>({
    resolver: zodResolver(bulkInviteSchema),
    defaultValues: {
      emails: '',
      role: 'USER',
      message: '',
    },
  });

  // Form for CSV upload
  const csvForm = useForm<CsvUploadFormValues>({
    resolver: zodResolver(csvUploadSchema),
    defaultValues: {
      role: 'USER',
      message: '',
    },
  });

  // Load existing invitations
  useEffect(() => {
    const loadInvitations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/organizations/${organizationId}/invitations`);
        
        if (response.ok) {
          const data = await response.json();
          setInvitedMembers(data.invitations || []);
        }
      } catch (err) {
        console.error('Error loading invitations:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (organizationId) {
      loadInvitations();
    }
  }, [organizationId]);

  // Handle single invitation submission
  const handleSingleInvite = async (data: InviteFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`/api/organizations/${organizationId}/invitations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send invitation');
      }

      // Add the newly invited member to the list
      setInvitedMembers([
        ...invitedMembers,
        {
          id: result.invitation.id,
          email: data.email,
          name: data.name,
          role: data.role,
          status: 'PENDING',
          createdAt: new Date(),
        },
      ]);

      toast({
        title: 'Invitation sent',
        description: `An invitation has been sent to ${data.email}`,
      });

      // Reset the form
      singleForm.reset({
        email: '',
        name: '',
        role: 'USER',
        message: '',
      });

      if (invitedMembers.length === 0) {
        // First invitation sent
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to send invitation',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle bulk invitation submission
  const handleBulkInvite = async (data: BulkInviteFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Parse emails (comma or newline separated)
      const emails = data.emails
        .split(/[,\n]/)
        .map(email => email.trim())
        .filter(email => email.length > 0);

      if (emails.length === 0) {
        throw new Error('Please enter at least one valid email address');
      }

      const response = await fetch(`/api/organizations/${organizationId}/bulk-invitations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emails,
          role: data.role,
          message: data.message,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send invitations');
      }

      // Add the newly invited members to the list
      const newInvitations = result.invitations.map((invitation: any) => ({
        id: invitation.id,
        email: invitation.email,
        role: data.role,
        status: 'PENDING',
        createdAt: new Date(),
      }));

      setInvitedMembers([...invitedMembers, ...newInvitations]);

      toast({
        title: 'Invitations sent',
        description: `${result.successful} invitations sent successfully${result.failed > 0 ? `, ${result.failed} failed` : ''}`,
      });

      // Reset the form
      bulkForm.reset({
        emails: '',
        role: 'USER',
        message: '',
      });

      if (invitedMembers.length === 0 && result.successful > 0) {
        // First invitation sent
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to send invitations',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle CSV file upload
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvData(content);
      
      // Simple CSV parsing for preview
      const rows = content.split('\n').map(row => row.split(',').map(cell => cell.trim()));
      setCsvPreview(rows.slice(0, 5)); // Show first 5 rows
    };
    reader.readAsText(file);
  };

  // Render role badge
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <Shield className="mr-1 h-3 w-3" />
            Admin
          </Badge>
        );
      case 'FINANCE':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <FileText className="mr-1 h-3 w-3" />
            Finance
          </Badge>
        );
      case 'COMPLIANCE':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <FileText className="mr-1 h-3 w-3" />
            Compliance
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <User className="mr-1 h-3 w-3" />
            User
          </Badge>
        );
    }
  };

  return (
    <div className="space-y-6 team-invitation-form">
      {/* Help card */}
      <HelpCard
        content={{
          title: "Team Invitation Guide",
          description: "Invite team members to collaborate on your carbon trading platform. You can invite individuals or multiple people at once.",
          type: "info",
        }}
        className="mb-4"
      />

      {/* Invitation tabs */}
      <Tabs defaultValue="single" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="single" className="role-selection">
            <UserPlus className="mr-2 h-4 w-4" />
            Single Invite
          </TabsTrigger>
          <TabsTrigger value="bulk" className="bulk-invite">
            <Mail className="mr-2 h-4 w-4" />
            Bulk Invite
          </TabsTrigger>
          <TabsTrigger value="csv" disabled>
            <Upload className="mr-2 h-4 w-4" />
            CSV Upload
          </TabsTrigger>
        </TabsList>

        {/* Single invitation form */}
        <TabsContent value="single" className="space-y-4 mt-4">
          <Form {...singleForm}>
            <form onSubmit={singleForm.handleSubmit(handleSingleInvite)} className="space-y-4">
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                <FormField
                  control={singleForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input placeholder="<EMAIL>" {...field} />
                          <div className="absolute right-3 top-1/2 -translate-y-1/2">
                            <ContextualHelp
                              content={{
                                title: "Email Address",
                                description: "Enter the email address of the person you want to invite. They'll receive an invitation email with instructions to join.",
                                type: "info",
                              }}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={singleForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormDescription>
                        Adding a name personalizes the invitation email
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={singleForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem className="role-selection">
                    <FormLabel>Role</FormLabel>
                    <div className="flex items-center space-x-2">
                      <FormControl>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USER">Regular User</SelectItem>
                            <SelectItem value="ADMIN">Administrator</SelectItem>
                            <SelectItem value="FINANCE">Finance Manager</SelectItem>
                            <SelectItem value="COMPLIANCE">Compliance Officer</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <ContextualHelp
                        content={{
                          title: "User Roles",
                          description: "Different roles have different permissions: Admins can manage the organization, Finance managers handle financial operations, Compliance officers manage regulatory compliance, and regular users have basic access.",
                          type: "info",
                        }}
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={singleForm.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Personal Message (Optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="I'm inviting you to join our carbon trading platform..." 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Add a personal message to the invitation email
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Send Invitation
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        {/* Bulk invitation form */}
        <TabsContent value="bulk" className="space-y-4 mt-4">
          <Form {...bulkForm}>
            <form onSubmit={bulkForm.handleSubmit(handleBulkInvite)} className="space-y-4">
              <FormField
                control={bulkForm.control}
                name="emails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Addresses</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Textarea 
                          placeholder="Enter multiple email addresses, separated by commas or new lines" 
                          className="min-h-[120px]"
                          {...field} 
                        />
                        <div className="absolute right-3 top-3">
                          <ContextualHelp
                            content={{
                              title: "Bulk Email Format",
                              description: "Enter multiple email addresses separated by commas or new lines. For example: <EMAIL>, <EMAIL>",
                              type: "info",
                            }}
                          />
                        </div>
                      </div>
                    </FormControl>
                    <FormDescription>
                      Enter multiple email addresses, separated by commas or new lines
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={bulkForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role for All Invitees</FormLabel>
                    <div className="flex items-center space-x-2">
                      <FormControl>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USER">Regular User</SelectItem>
                            <SelectItem value="ADMIN">Administrator</SelectItem>
                            <SelectItem value="FINANCE">Finance Manager</SelectItem>
                            <SelectItem value="COMPLIANCE">Compliance Officer</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <ContextualHelp
                        content={{
                          title: "Bulk Role Assignment",
                          description: "All invitees will be assigned this role. You can change individual roles later from the team management page.",
                          type: "info",
                        }}
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={bulkForm.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Personal Message (Optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="I'm inviting you to join our carbon trading platform..." 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Add a personal message to all invitation emails
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Send Invitations
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        {/* CSV upload form - disabled for now */}
        <TabsContent value="csv" className="space-y-4 mt-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Coming Soon</AlertTitle>
            <AlertDescription>
              CSV upload functionality will be available in a future update.
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>

      {/* Invited members list */}
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">Invited Team Members</h3>
        
        {isLoading ? (
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded-md animate-pulse">
                <div className="space-y-2">
                  <div className="h-4 w-48 bg-gray-200 rounded"></div>
                  <div className="h-3 w-24 bg-gray-200 rounded"></div>
                </div>
                <div className="h-6 w-16 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        ) : invitedMembers.length > 0 ? (
          <div className="space-y-2 max-h-[300px] overflow-y-auto">
            <AnimatePresence>
              {invitedMembers.map((member) => (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center justify-between p-3 border rounded-md"
                >
                  <div>
                    <div className="font-medium">{member.email}</div>
                    <div className="text-sm text-muted-foreground">
                      Invited {new Date(member.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getRoleBadge(member.role)}
                    <Badge variant={member.status === 'PENDING' ? 'outline' : 'secondary'}>
                      {member.status}
                    </Badge>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <div className="text-center p-6 border border-dashed rounded-md">
            <UserPlus className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <h3 className="font-medium">No team members invited yet</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Invite team members to collaborate on your carbon trading platform
            </p>
            <Button variant="outline" onClick={() => setActiveTab('single')}>
              <UserPlus className="mr-2 h-4 w-4" />
              Invite Team Members
            </Button>
          </div>
        )}
      </div>

      {/* Skip button */}
      {onSkip && (
        <div className="flex justify-between mt-6">
          <Button variant="outline" onClick={() => setShowTour(true)}>
            <Info className="mr-2 h-4 w-4" />
            Show Guide
          </Button>
          <Button variant="outline" onClick={onSkip}>
            Skip & Continue
          </Button>
        </div>
      )}

      {/* Guided tour */}
      <OnboardingTour page="team" autoStart={showTour} />
    </div>
  );
}
