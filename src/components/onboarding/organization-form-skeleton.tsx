"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

// Animated skeleton component with fade-in/out transitions
const AnimatedSkeleton = ({ className, ...props }: React.ComponentProps<typeof Skeleton>) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.3 }}
  >
    <Skeleton className={cn(className)} {...props} />
  </motion.div>
);

export function OrganizationFormSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="w-full max-w-4xl">
        <CardHeader className="space-y-2">
          <div className="flex items-center justify-between">
            <AnimatedSkeleton className="h-7 w-48" />
            <AnimatedSkeleton className="h-5 w-5 rounded-full" />
          </div>
          <AnimatedSkeleton className="h-4 w-64" />
          <AnimatedSkeleton className="h-2 w-full" />
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <AnimatedSkeleton className="h-4 w-32" />
              <AnimatedSkeleton className="h-10 w-full" />
              <AnimatedSkeleton className="h-3 w-3/4" />
            </div>

            <div className="space-y-2">
              <AnimatedSkeleton className="h-4 w-24" />
              <AnimatedSkeleton className="h-10 w-full" />
              <AnimatedSkeleton className="h-3 w-2/3" />
            </div>

            <div className="space-y-2">
              <AnimatedSkeleton className="h-4 w-36" />
              <AnimatedSkeleton className="h-10 w-full" />
              <AnimatedSkeleton className="h-3 w-1/2" />
            </div>

            <div className="space-y-2">
              <AnimatedSkeleton className="h-4 w-40" />
              <AnimatedSkeleton className="h-24 w-full" />
              <AnimatedSkeleton className="h-3 w-3/5" />
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <div className="flex justify-between items-center w-full">
            <AnimatedSkeleton className="h-10 w-24" />
            <div className="flex space-x-2">
              <AnimatedSkeleton className="h-10 w-40" />
              <AnimatedSkeleton className="h-10 w-32" />
            </div>
          </div>
          <AnimatedSkeleton className="h-4 w-64 mx-auto" />
        </CardFooter>
      </Card>
    </motion.div>
  );
}

export function BasicInfoStepSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, staggerChildren: 0.1 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-40" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-3/4" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-24" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-2/3" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-36" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-1/2" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-40" />
          <AnimatedSkeleton className="h-24 w-full" />
          <AnimatedSkeleton className="h-3 w-3/5" />
        </div>
      </div>
    </motion.div>
  );
}

export function ContactInfoStepSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, staggerChildren: 0.1 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-32" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-3/4" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-36" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-2/3" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-28" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-4/5" />
        </div>
      </div>
    </motion.div>
  );
}

export function LegalInfoStepSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, staggerChildren: 0.1 }}
      className="space-y-6"
    >
      <AnimatedSkeleton className="h-16 w-full rounded-md" />

      <div className="space-y-4">
        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-28" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-3/4" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-44" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-2/3" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-24" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-1/2" />
        </div>

        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-32" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-3/5" />
        </div>
      </div>
    </motion.div>
  );
}

export function AddressInfoStepSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, staggerChildren: 0.1 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <AnimatedSkeleton className="h-4 w-32" />
          <AnimatedSkeleton className="h-10 w-full" />
          <AnimatedSkeleton className="h-3 w-3/4" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <AnimatedSkeleton className="h-4 w-16" />
            <AnimatedSkeleton className="h-10 w-full" />
          </div>

          <div className="space-y-2">
            <AnimatedSkeleton className="h-4 w-36" />
            <AnimatedSkeleton className="h-10 w-full" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <AnimatedSkeleton className="h-4 w-32" />
            <AnimatedSkeleton className="h-10 w-full" />
          </div>

          <div className="space-y-2">
            <AnimatedSkeleton className="h-4 w-24" />
            <AnimatedSkeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    </motion.div>
  );
}
