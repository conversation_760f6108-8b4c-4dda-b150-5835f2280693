'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  ChevronRight, 
  ChevronLeft, 
  X, 
  HelpCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Tour step interface
export interface TourStep {
  target: string; // CSS selector for the target element
  title: string;
  content: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  offset?: number; // Distance from target element
  highlight?: boolean; // Whether to highlight the target element
}

interface GuidedTourProps {
  steps: TourStep[];
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
  startAt?: number;
  showProgress?: boolean;
  showSkip?: boolean;
  showDismiss?: boolean;
  dismissText?: string;
  completeText?: string;
  nextText?: string;
  prevText?: string;
  skipText?: string;
}

export function GuidedTour({
  steps,
  isOpen,
  onClose,
  onComplete,
  startAt = 0,
  showProgress = true,
  showSkip = true,
  showDismiss = true,
  dismissText = "Don't show again",
  completeText = "Finish",
  nextText = "Next",
  prevText = "Back",
  skipText = "Skip tour",
}: GuidedTourProps) {
  const [currentStep, setCurrentStep] = useState(startAt);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [tooltipPlacement, setTooltipPlacement] = useState<'top' | 'right' | 'bottom' | 'left'>('bottom');
  const [highlightStyle, setHighlightStyle] = useState({
    top: 0,
    left: 0,
    width: 0,
    height: 0,
  });
  const tooltipRef = useRef<HTMLDivElement>(null);
  
  // Calculate position based on target element and placement
  const calculatePosition = () => {
    if (!isOpen || steps.length === 0) return;
    
    const step = steps[currentStep];
    const targetElement = document.querySelector(step.target);
    
    if (!targetElement || !tooltipRef.current) {
      // If target not found, center in viewport
      setPosition({
        top: window.innerHeight / 2 - 100,
        left: window.innerWidth / 2 - 150,
      });
      return;
    }
    
    const targetRect = targetElement.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const offset = step.offset || 20;
    
    // Calculate highlight style
    setHighlightStyle({
      top: targetRect.top + window.scrollY,
      left: targetRect.left + window.scrollX,
      width: targetRect.width,
      height: targetRect.height,
    });
    
    // Determine best placement if not specified
    const placement = step.placement || calculateBestPlacement(targetRect);
    setTooltipPlacement(placement);
    
    // Calculate position based on placement
    let top = 0;
    let left = 0;
    
    switch (placement) {
      case 'top':
        top = targetRect.top - tooltipRect.height - offset + window.scrollY;
        left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2 + window.scrollX;
        break;
      case 'right':
        top = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2 + window.scrollY;
        left = targetRect.right + offset + window.scrollX;
        break;
      case 'bottom':
        top = targetRect.bottom + offset + window.scrollY;
        left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2 + window.scrollX;
        break;
      case 'left':
        top = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2 + window.scrollY;
        left = targetRect.left - tooltipRect.width - offset + window.scrollX;
        break;
    }
    
    // Ensure tooltip stays within viewport
    if (left < 10) left = 10;
    if (left + tooltipRect.width > window.innerWidth - 10) {
      left = window.innerWidth - tooltipRect.width - 10;
    }
    if (top < 10) top = 10;
    if (top + tooltipRect.height > window.innerHeight + window.scrollY - 10) {
      top = window.innerHeight + window.scrollY - tooltipRect.height - 10;
    }
    
    setPosition({ top, left });
    
    // Scroll target into view if needed
    if (
      targetRect.top < 0 ||
      targetRect.bottom > window.innerHeight ||
      targetRect.left < 0 ||
      targetRect.right > window.innerWidth
    ) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };
  
  // Calculate best placement based on available space
  const calculateBestPlacement = (targetRect: DOMRect): 'top' | 'right' | 'bottom' | 'left' => {
    const spaceTop = targetRect.top;
    const spaceRight = window.innerWidth - targetRect.right;
    const spaceBottom = window.innerHeight - targetRect.bottom;
    const spaceLeft = targetRect.left;
    
    const spaces = [
      { placement: 'bottom', space: spaceBottom },
      { placement: 'right', space: spaceRight },
      { placement: 'top', space: spaceTop },
      { placement: 'left', space: spaceLeft },
    ];
    
    // Sort by available space
    spaces.sort((a, b) => b.space - a.space);
    
    return spaces[0].placement as 'top' | 'right' | 'bottom' | 'left';
  };
  
  // Handle next step
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };
  
  // Handle previous step
  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  // Handle skip
  const handleSkip = () => {
    onClose();
  };
  
  // Handle complete
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
    onClose();
  };
  
  // Update position when step changes or window resizes
  useEffect(() => {
    if (!isOpen) return;
    
    calculatePosition();
    
    const handleResize = () => {
      calculatePosition();
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize);
    };
  }, [isOpen, currentStep, steps]);
  
  if (!isOpen || steps.length === 0) {
    return null;
  }
  
  const step = steps[currentStep];
  
  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={handleSkip} />
      
      {/* Highlight */}
      {step.highlight && (
        <div
          className="absolute z-50 border-2 border-primary rounded-md pointer-events-none"
          style={{
            top: highlightStyle.top,
            left: highlightStyle.left,
            width: highlightStyle.width,
            height: highlightStyle.height,
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
          }}
        />
      )}
      
      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-50 w-80"
        style={{
          top: position.top,
          left: position.left,
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="shadow-lg">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                  {showDismiss && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 rounded-full p-0"
                      onClick={onClose}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-muted-foreground">{step.content}</p>
                
                {showProgress && (
                  <div className="mt-4">
                    <div className="flex justify-between text-xs mb-1">
                      <span>Step {currentStep + 1} of {steps.length}</span>
                      <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
                    </div>
                    <Progress value={((currentStep + 1) / steps.length) * 100} className="h-1" />
                  </div>
                )}
              </CardContent>
              
              <CardFooter className="flex justify-between pt-2">
                <div>
                  {currentStep > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePrev}
                    >
                      <ChevronLeft className="mr-1 h-4 w-4" />
                      {prevText}
                    </Button>
                  )}
                </div>
                
                <div className="flex space-x-2">
                  {showSkip && currentStep < steps.length - 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSkip}
                    >
                      {skipText}
                    </Button>
                  )}
                  
                  <Button
                    size="sm"
                    onClick={handleNext}
                  >
                    {currentStep === steps.length - 1 ? (
                      <>
                        {completeText}
                        <CheckCircle className="ml-1 h-4 w-4" />
                      </>
                    ) : (
                      <>
                        {nextText}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              </CardFooter>
            </Card>
            
            {/* Arrow pointing to target based on placement */}
            <div
              className={cn(
                "absolute w-3 h-3 bg-background rotate-45 border",
                tooltipPlacement === 'top' && "bottom-[-6px] left-1/2 -translate-x-1/2 border-t-0 border-l-0",
                tooltipPlacement === 'right' && "left-[-6px] top-1/2 -translate-y-1/2 border-t-0 border-r-0",
                tooltipPlacement === 'bottom' && "top-[-6px] left-1/2 -translate-x-1/2 border-b-0 border-r-0",
                tooltipPlacement === 'left' && "right-[-6px] top-1/2 -translate-y-1/2 border-b-0 border-l-0"
              )}
            />
          </motion.div>
        </AnimatePresence>
      </div>
    </>
  );
}
