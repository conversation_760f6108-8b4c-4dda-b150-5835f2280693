'use client';

import { useState, useEffect } from 'react';
import { GuidedTour, TourStep } from './guided-tour';
import { Button } from '@/components/ui/button';
import { HelpCircle } from 'lucide-react';

// Define tour steps for each onboarding page
const organizationTourSteps: TourStep[] = [
  {
    target: '.multi-step-organization-form',
    title: 'Organization Setup',
    content: 'This form helps us set up your organization profile. You can complete it in multiple steps and save your progress at any time.',
    placement: 'bottom',
    highlight: true,
  },
  {
    target: '.save-continue-button',
    title: 'Save & Continue Later',
    content: 'You can save your progress and continue later. Your data will be securely stored.',
    placement: 'top',
    highlight: true,
  },
  {
    target: '.progress-indicator',
    title: 'Progress Tracking',
    content: 'This indicator shows your progress through the onboarding process.',
    placement: 'bottom',
    highlight: true,
  },
  {
    target: '.contextual-help',
    title: 'Contextual Help',
    content: 'Look for these help icons throughout the form for additional guidance and tips.',
    placement: 'right',
    highlight: true,
  },
];

const teamInvitationTourSteps: TourStep[] = [
  {
    target: '.team-invitation-form',
    title: 'Team Invitations',
    content: 'Invite team members to join your organization. They will receive an email with instructions to create an account.',
    placement: 'bottom',
    highlight: true,
  },
  {
    target: '.role-selection',
    title: 'Role Selection',
    content: 'Assign appropriate roles to team members. Each role has different permissions and access levels.',
    placement: 'right',
    highlight: true,
  },
  {
    target: '.bulk-invite',
    title: 'Bulk Invitations',
    content: 'You can invite multiple team members at once by uploading a CSV file or entering multiple email addresses.',
    placement: 'top',
    highlight: true,
  },
];

const walletSetupTourSteps: TourStep[] = [
  {
    target: '.wallet-setup-form',
    title: 'Wallet Setup',
    content: 'Set up your organization\'s wallet to start trading carbon credits. This is where you\'ll manage your assets.',
    placement: 'bottom',
    highlight: true,
  },
  {
    target: '.wallet-type-selection',
    title: 'Wallet Type',
    content: 'Choose between a smart wallet with enhanced security features or a standard wallet for simpler operations.',
    placement: 'right',
    highlight: true,
  },
  {
    target: '.network-selection',
    title: 'Network Selection',
    content: 'Select the blockchain network for your wallet. Each network has different characteristics and fees.',
    placement: 'left',
    highlight: true,
  },
  {
    target: '.security-settings',
    title: 'Security Settings',
    content: 'Configure security settings for your wallet. Higher security levels provide better protection but may require additional steps for transactions.',
    placement: 'top',
    highlight: true,
  },
];

const verificationTourSteps: TourStep[] = [
  {
    target: '.verification-form',
    title: 'Verification Process',
    content: 'Complete the verification process to unlock full platform functionality. This helps ensure compliance with regulations.',
    placement: 'bottom',
    highlight: true,
  },
  {
    target: '.document-upload',
    title: 'Document Upload',
    content: 'Upload the required documents for verification. Make sure they are clear, legible, and valid.',
    placement: 'right',
    highlight: true,
  },
  {
    target: '.verification-status',
    title: 'Verification Status',
    content: 'Track the status of your verification process here. You\'ll be notified when verification is complete.',
    placement: 'top',
    highlight: true,
  },
];

// Tour page types
export type OnboardingTourPage = 'organization' | 'team' | 'wallet' | 'verification';

interface OnboardingTourProps {
  page: OnboardingTourPage;
  autoStart?: boolean;
}

export function OnboardingTour({ page, autoStart = false }: OnboardingTourProps) {
  const [showTour, setShowTour] = useState(autoStart);
  const [hasSeenTour, setHasSeenTour] = useState(false);

  // Get tour steps based on page
  const getTourSteps = (): TourStep[] => {
    switch (page) {
      case 'organization':
        return organizationTourSteps;
      case 'team':
        return teamInvitationTourSteps;
      case 'wallet':
        return walletSetupTourSteps;
      case 'verification':
        return verificationTourSteps;
      default:
        return [];
    }
  };

  // Check if user has seen tour before
  useEffect(() => {
    const tourSeen = localStorage.getItem(`onboarding-tour-${page}-seen`);
    if (tourSeen) {
      setHasSeenTour(true);
    }
  }, [page]);

  // Handle tour completion
  const handleTourComplete = () => {
    setShowTour(false);
    localStorage.setItem(`onboarding-tour-${page}-seen`, 'true');
    setHasSeenTour(true);
  };

  // Handle tour close
  const handleTourClose = () => {
    setShowTour(false);
  };

  // Start tour
  const startTour = () => {
    setShowTour(true);
  };

  return (
    <>
      {/* Tour button */}
      {!showTour && !autoStart && (
        <Button
          variant="outline"
          size="sm"
          className="fixed bottom-4 right-4 z-40 rounded-full"
          onClick={startTour}
        >
          <HelpCircle className="mr-2 h-4 w-4" />
          {hasSeenTour ? 'Show Guide' : 'Take a Tour'}
        </Button>
      )}

      {/* Guided tour */}
      {/* <GuidedTour
        steps={getTourSteps()}
        isOpen={showTour}
        onClose={handleTourClose}
        onComplete={handleTourComplete}
        showProgress={true}
        showSkip={true}
        showDismiss={true}
      /> */}
    </>
  );
}
