"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export function LegalInfoStep() {
  const { control } = useFormContext();
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: currentYear - 1799 }, (_, i) => (currentYear - i).toString());

  return (
    <div className="space-y-6">
      <Alert variant="info" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          This information is used for verification purposes and will not be publicly displayed.
        </AlertDescription>
      </Alert>
      
      <div className="space-y-4">
        <FormField
          control={control}
          name="legalName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Legal Name
                <InfoTooltip 
                  content="The registered legal name of your organization"
                  className="ml-1"
                />
              </FormLabel>
              <FormControl>
                <AnimatedInput placeholder="Enter legal name" {...field} />
              </FormControl>
              <FormDescription>
                The official registered name of your organization
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="registrationNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Registration Number (Optional)
                <InfoTooltip 
                  content="Your organization's business registration or incorporation number"
                  className="ml-1"
                />
              </FormLabel>
              <FormControl>
                <AnimatedInput placeholder="Enter registration number" {...field} />
              </FormControl>
              <FormDescription>
                Business registration or incorporation number
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="taxId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Tax ID (Optional)
                <InfoTooltip 
                  content={
                    <div className="space-y-1">
                      <p>Your organization's tax identification number:</p>
                      <p>• EIN (US)</p>
                      <p>• VAT Number (EU)</p>
                      <p>• ABN (Australia)</p>
                      <p>• Or equivalent in your country</p>
                    </div>
                  }
                  className="ml-1"
                />
              </FormLabel>
              <FormControl>
                <AnimatedInput placeholder="Enter tax ID" {...field} />
              </FormControl>
              <FormDescription>
                Tax identification number (e.g., EIN, VAT number)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="foundedYear"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Founded Year</FormLabel>
              <FormControl>
                <Select
                  onValueChange={(value) => field.onChange(parseInt(value))}
                  defaultValue={field.value?.toString()}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormDescription>
                The year your organization was founded
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
