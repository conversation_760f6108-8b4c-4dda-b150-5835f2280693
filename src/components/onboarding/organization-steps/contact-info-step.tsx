"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { Globe, Phone, Mail } from "lucide-react";

export function ContactInfoStep() {
  const { control } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <FormField
          control={control}
          name="website"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Website (Optional)
                <InfoTooltip 
                  content="Your organization's official website URL"
                  className="ml-1"
                />
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <AnimatedInput 
                    placeholder="https://www.example.com" 
                    className="pl-10" 
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormDescription>
                Enter your organization's website URL (including https://)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Phone Number (Optional)
                <InfoTooltip 
                  content="A contact phone number for your organization"
                  className="ml-1"
                />
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <AnimatedInput 
                    placeholder="+****************" 
                    className="pl-10" 
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormDescription>
                Include country code (e.g., +1 for US)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Contact Email
                <InfoTooltip 
                  content="The primary email address for your organization"
                  className="ml-1"
                />
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <AnimatedInput 
                    placeholder="<EMAIL>" 
                    className="pl-10" 
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormDescription>
                This email will receive important notifications about your organization
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
