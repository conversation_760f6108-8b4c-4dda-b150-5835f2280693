"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { ContextualHelp, HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import { Building, Briefcase, Users, FileText } from "lucide-react";

export function BasicInfoStep() {
  const { control } = useFormContext();

  return (
    <div className="space-y-6">
      {/* Help card at the top of the form */}
      <HelpCard
        content={{
          title: "Organization Information",
          description: "This information helps us set up your organization profile and tailor our services to your needs.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/onboarding/organization-setup",
          learnMoreText: "Learn more about organization setup",
        }}
        className="mb-6"
      />

      <div className="space-y-4">
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                <InlineHelp
                  content={{
                    title: "Organization Name",
                    description: "This is the official name of your organization that will be displayed on your profile, listings, and transactions.",
                    type: "info",
                  }}
                >
                  Organization Name
                </InlineHelp>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <AnimatedInput placeholder="Enter organization name" {...field} />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    <ContextualHelp
                      content={{
                        title: "Naming Tips",
                        description: "Use your organization's legal name or a recognizable trading name. This will be used for verification purposes.",
                        type: "tip",
                      }}
                    />
                  </div>
                </div>
              </FormControl>
              <FormDescription className="flex items-center">
                <Building className="mr-1 h-3 w-3 text-muted-foreground" />
                This will be displayed publicly on your profile and listings
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="industry"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                <InlineHelp
                  content={{
                    title: "Industry Selection",
                    description: "Selecting the right industry helps us connect you with relevant carbon credit projects and trading partners.",
                    type: "info",
                  }}
                >
                  Industry
                </InlineHelp>
              </FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AGRICULTURE">Agriculture</SelectItem>
                    <SelectItem value="ENERGY">Energy</SelectItem>
                    <SelectItem value="MANUFACTURING">Manufacturing</SelectItem>
                    <SelectItem value="TECHNOLOGY">Technology</SelectItem>
                    <SelectItem value="TRANSPORTATION">Transportation</SelectItem>
                    <SelectItem value="FINANCE">Finance</SelectItem>
                    <SelectItem value="HEALTHCARE">Healthcare</SelectItem>
                    <SelectItem value="RETAIL">Retail</SelectItem>
                    <SelectItem value="EDUCATION">Education</SelectItem>
                    <SelectItem value="GOVERNMENT">Government</SelectItem>
                    <SelectItem value="NONPROFIT">Nonprofit</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormDescription className="flex items-center">
                <Briefcase className="mr-1 h-3 w-3 text-muted-foreground" />
                Select the industry that best describes your organization
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="size"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                <InlineHelp
                  content={{
                    title: "Organization Size",
                    description: "Your organization's size helps us understand your carbon credit needs and tailor our services accordingly.",
                    type: "info",
                    learnMoreLink: "https://docs.carbon-exchange.com/onboarding/organization-size",
                  }}
                >
                  Organization Size
                </InlineHelp>
              </FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SMALL">Small (1-50 employees)</SelectItem>
                    <SelectItem value="MEDIUM">Medium (51-250 employees)</SelectItem>
                    <SelectItem value="LARGE">Large (251-1000 employees)</SelectItem>
                    <SelectItem value="ENTERPRISE">Enterprise (1000+ employees)</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormDescription className="flex items-center">
                <Users className="mr-1 h-3 w-3 text-muted-foreground" />
                This helps us tailor our services to your organization's needs
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                <InlineHelp
                  content={{
                    title: "Organization Description",
                    description: "A brief description helps other users understand your organization's mission and sustainability goals.",
                    type: "tip",
                  }}
                >
                  Description (Optional)
                </InlineHelp>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Textarea
                    placeholder="Briefly describe your organization"
                    className="resize-none"
                    {...field}
                  />
                  <div className="absolute right-3 top-3">
                    <ContextualHelp
                      content={{
                        title: "Description Tips",
                        description: "Include your organization's mission, sustainability goals, and why you're interested in carbon credits. This helps build trust with potential trading partners.",
                        type: "tip",
                      }}
                    />
                  </div>
                </div>
              </FormControl>
              <FormDescription className="flex items-center">
                <FileText className="mr-1 h-3 w-3 text-muted-foreground" />
                A short description of your organization's mission and activities
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
