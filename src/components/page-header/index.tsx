import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface PageHeaderProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}

export function PageHeader({ title, description, actions, children, className }: PageHeaderProps) {
  // If children are provided, use them instead of title/description
  const hasChildren = React.Children.count(children) > 0;

  return (
    <div className={cn("flex flex-col gap-4 pb-6", className)}>
      <div className="flex items-center justify-between">
        <div>
          {hasChildren ? (
            children
          ) : (
            <>
              {title && <h1 className="text-2xl font-bold tracking-tight">{title}</h1>}
              {description && (
                <p className="text-muted-foreground mt-1">{description}</p>
              )}
            </>
          )}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
      <Separator />
    </div>
  );
}

// Heading component for the page header
interface PageHeaderHeadingProps {
  children?: React.ReactNode;
  className?: string;
}

export function PageHeaderHeading({
  children,
  className,
}: PageHeaderHeadingProps) {
  return (
    <h1 className={cn("text-3xl font-bold tracking-tight", className)}>
      {children}
    </h1>
  );
}

// Description component for the page header
interface PageHeaderDescriptionProps {
  children?: React.ReactNode;
  className?: string;
}

export function PageHeaderDescription({
  children,
  className,
}: PageHeaderDescriptionProps) {
  return (
    <p className={cn("text-muted-foreground mt-2 text-lg", className)}>
      {children}
    </p>
  );
}