"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { navigationConfig } from "@/config/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";

export function DashboardSidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);

  const handleMouseEnter = () => {
    if (isCollapsed) {
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (isCollapsed) {
      setIsHovering(false);
    }
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    if (!isCollapsed) {
      setIsHovering(false);
    }
  };

  return (
    <aside
      className={cn(
        "group relative flex flex-col border-r bg-background transition-all duration-300",
        isCollapsed && !isHovering ? "w-[60px]" : "w-[240px]"
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex h-[60px] items-center border-b px-4">
        <button
          onClick={toggleCollapse}
          className="inline-flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={cn(
              "transition-transform duration-200",
              isCollapsed ? "rotate-180" : "rotate-0"
            )}
          >
            <path d="M18 8L22 12L18 16" />
            <path d="M2 12H22" />
          </svg>
          <span className="sr-only">Toggle Sidebar</span>
        </button>
        {(!isCollapsed || isHovering) && (
          <span className="ml-2 text-lg font-semibold">Navigation</span>
        )}
      </div>
      <ScrollArea className="flex-1 py-2">
        <nav className="grid gap-2 px-2">
          {navigationConfig.mainNav.map((section) => (
            <div key={section.title} className="py-2">
              {(!isCollapsed || isHovering) && (
                <h4 className="mb-1 rounded-md px-2 py-1 text-sm font-semibold">
                  {section.title}
                </h4>
              )}
              {section.items?.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-muted",
                    pathname === item.href
                      ? "bg-muted"
                      : "transparent",
                    isCollapsed && !isHovering ? "justify-center" : ""
                  )}
                >
                  {item.icon && (
                    <item.icon className={cn("h-5 w-5", pathname === item.href ? "text-primary" : "text-muted-foreground")} />
                  )}
                  {(!isCollapsed || isHovering) && (
                    <span>{item.title}</span>
                  )}
                </Link>
              ))}
            </div>
          ))}
        </nav>
      </ScrollArea>
    </aside>
  );
}
