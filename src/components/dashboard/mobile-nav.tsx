"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu } from "lucide-react";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { navigationConfig } from "@/config/navigation";
import { ThemeToggle } from "@/components/theme-toggle";
import { Search } from "@/components/dashboard/search";

export function MobileNav() {
  const pathname = usePathname();
  const [open, setOpen] = React.useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="pr-0">
        <div className="px-7">
          <Link
            href="/"
            className="flex items-center"
            onClick={() => setOpen(false)}
          >
            <span className="font-bold">Carbon Exchange</span>
          </Link>
        </div>
        <ScrollArea className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
          <div className="pl-1 pr-7">
            <Search />
          </div>
          <div className="mt-4 pl-1 pr-7">
            {navigationConfig.mainNav.map((item) => (
              <div key={item.title} className="py-2">
                <h4 className="mb-1 rounded-md px-2 py-1 text-sm font-semibold">
                  {item.title}
                </h4>
                {item.items?.map((subItem) => (
                  <Link
                    key={subItem.href}
                    href={subItem.href}
                    onClick={() => setOpen(false)}
                    className={cn(
                      "flex w-full items-center rounded-md p-2 text-sm font-medium hover:underline",
                      pathname === subItem.href
                        ? "bg-muted font-medium text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    {subItem.icon && <subItem.icon className="mr-2 h-4 w-4" />}
                    {subItem.title}
                  </Link>
                ))}
              </div>
            ))}
          </div>
          <div className="mt-4 pl-1 pr-7">
            <ThemeToggle />
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
