"use client";

import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  <PERSON>T<PERSON><PERSON>, 
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  AlertCircle, 
  Wallet, 
  Shield, 
  Check, 
  Loader2,
  RefreshCw
} from "lucide-react";
import { useRouter } from "next/navigation";
import {
  Animated<PERSON><PERSON>,
  AnimatedC<PERSON>Header,
  AnimatedCardContent,
  AnimatedCardFooter,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton
} from "@/components/ui/animated";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";

interface WalletCreationProps {
  onWalletCreated?: () => void;
  className?: string;
}

export function WalletCreation({ 
  onWalletCreated,
  className = ""
}: WalletCreationProps) {
  const router = useRouter();
  const [walletType, setWalletType] = useState<"SMART_WALLET" | "REGULAR_WALLET">("SMART_WALLET");
  const [network, setNetwork] = useState<"ETHEREUM" | "POLYGON">("ETHEREUM");
  const [isTestnet, setIsTestnet] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const createWallet = async () => {
    setIsCreating(true);
    setError(null);

    try {
      const response = await fetch("/api/wallet/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          walletType,
          network,
          securityLevel: "STANDARD",
          testMode: isTestnet,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create wallet");
      }

      const result = await response.json();
      setIsSuccess(true);
      
      toast.success("Wallet created successfully");
      
      // Call the callback if provided
      if (onWalletCreated) {
        onWalletCreated();
      }
    } catch (err) {
      console.error("Error creating wallet:", err);
      setError(err instanceof Error ? err.message : "An error occurred while creating the wallet");
    } finally {
      setIsCreating(false);
    }
  };

  if (isSuccess) {
    return (
      <AnimatedCard className={className}>
        <AnimatedCardHeader>
          <AnimatedCardTitle>Wallet Created</AnimatedCardTitle>
          <AnimatedCardDescription>
            Your wallet has been created successfully
          </AnimatedCardDescription>
        </AnimatedCardHeader>
        <AnimatedCardContent className="flex flex-col items-center justify-center py-6 text-center">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
            <Check className="h-6 w-6 text-green-600" />
          </div>
          <p className="text-sm text-muted-foreground mb-6">
            Your {walletType === "SMART_WALLET" ? "smart" : "regular"} wallet on {network.toLowerCase()} 
            {isTestnet ? " testnet" : ""} is ready to use.
          </p>
          <AnimatedButton 
            onClick={() => router.push("/dashboard/wallet")}
            animationVariant="buttonTap"
          >
            Go to Wallet
          </AnimatedButton>
        </AnimatedCardContent>
      </AnimatedCard>
    );
  }

  return (
    <AnimatedCard className={className}>
      <AnimatedCardHeader>
        <AnimatedCardTitle>Create a Wallet</AnimatedCardTitle>
        <AnimatedCardDescription>
          You need a wallet to manage your funds and assets
        </AnimatedCardDescription>
      </AnimatedCardHeader>
      <AnimatedCardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          <div className="space-y-2">
            <Label>Wallet Type</Label>
            <RadioGroup 
              defaultValue={walletType} 
              onValueChange={(value) => setWalletType(value as "SMART_WALLET" | "REGULAR_WALLET")}
              className="grid grid-cols-2 gap-4"
            >
              <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                <RadioGroupItem value="SMART_WALLET" id="smart-wallet" className="sr-only" />
                <Shield className="mb-3 h-6 w-6" />
                <Label htmlFor="smart-wallet" className="text-base font-medium">Smart Wallet</Label>
                <p className="text-xs text-muted-foreground text-center mt-1">
                  Enhanced security with account abstraction
                </p>
              </div>
              
              <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                <RadioGroupItem value="REGULAR_WALLET" id="regular-wallet" className="sr-only" />
                <Wallet className="mb-3 h-6 w-6" />
                <Label htmlFor="regular-wallet" className="text-base font-medium">Regular Wallet</Label>
                <p className="text-xs text-muted-foreground text-center mt-1">
                  Standard Ethereum wallet
                </p>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label>Network</Label>
            <RadioGroup 
              defaultValue={network} 
              onValueChange={(value) => setNetwork(value as "ETHEREUM" | "POLYGON")}
              className="grid grid-cols-2 gap-4"
            >
              <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                <RadioGroupItem value="ETHEREUM" id="ethereum" className="sr-only" />
                <div className="mb-3 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-blue-600 text-xs font-bold">ETH</span>
                </div>
                <Label htmlFor="ethereum" className="text-base font-medium">Ethereum</Label>
              </div>
              
              <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary">
                <RadioGroupItem value="POLYGON" id="polygon" className="sr-only" />
                <div className="mb-3 h-6 w-6 rounded-full bg-purple-100 flex items-center justify-center">
                  <span className="text-purple-600 text-xs font-bold">MATIC</span>
                </div>
                <Label htmlFor="polygon" className="text-base font-medium">Polygon</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="testnet-mode">Testnet Mode</Label>
              <p className="text-xs text-muted-foreground">
                Use test networks with free tokens
              </p>
            </div>
            <Switch 
              id="testnet-mode" 
              checked={isTestnet}
              onCheckedChange={setIsTestnet}
            />
          </div>
        </div>
      </AnimatedCardContent>
      <AnimatedCardFooter>
        <AnimatedButton 
          onClick={createWallet}
          disabled={isCreating}
          className="w-full"
          animationVariant="buttonTap"
        >
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Wallet...
            </>
          ) : (
            "Create Wallet"
          )}
        </AnimatedButton>
      </AnimatedCardFooter>
    </AnimatedCard>
  );
}
