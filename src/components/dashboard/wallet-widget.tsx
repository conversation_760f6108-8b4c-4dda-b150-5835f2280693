"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Wallet, ArrowRight, RefreshCw } from "lucide-react";
import { truncateAddress } from "@/lib/utils";
import {
  Animated<PERSON><PERSON>,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardFooter,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedSkeleton,
  AnimatedIcon
} from "@/components/ui/animated";

interface WalletData {
  id: string;
  address: string;
  balance: number;
  network: string;
  isTestnet: boolean;
}

export function WalletWidget() {
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWallet = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/wallet");

      if (!response.ok) {
        throw new Error("Failed to fetch wallet data");
      }

      const data = await response.json();
      setWallet(data.wallet);
    } catch (err) {
      console.error("Error fetching wallet:", err);
      setError("Could not load wallet data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWallet();
  }, []);

  if (loading) {
    return (
      <AnimatedCard className="flex flex-col h-[140px]" animationVariant="fadeIn">
        <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <AnimatedCardTitle className="text-sm font-medium">Wallet Balance</AnimatedCardTitle>
          <AnimatedIcon icon={<Wallet className="h-4 w-4" />} size="sm" color="muted-foreground" />
        </AnimatedCardHeader>
        <AnimatedCardContent className="flex-1 flex flex-col justify-center">
          <AnimatedSkeleton className="h-6 w-24 mb-2" />
          <AnimatedSkeleton className="h-4 w-32" />
        </AnimatedCardContent>
        <AnimatedCardFooter className="p-2 flex justify-end">
          <AnimatedButton
            variant="ghost"
            size="sm"
            disabled
            className="text-xs opacity-50"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Refresh
          </AnimatedButton>
        </AnimatedCardFooter>
      </AnimatedCard>
    );
  }

  if (error || !wallet) {
    return (
      <AnimatedCard className="flex flex-col h-[140px]" animationVariant="fadeIn">
        <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <AnimatedCardTitle className="text-sm font-medium">Wallet Balance</AnimatedCardTitle>
          <AnimatedIcon icon={<Wallet className="h-4 w-4" />} size="sm" color="muted-foreground" />
        </AnimatedCardHeader>
        <AnimatedCardContent className="flex-1 flex flex-col justify-center">
          <p className="text-sm text-muted-foreground">
            {error || "No wallet found"}
          </p>
          <AnimatedButton
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => router.push("/onboarding/wallet")}
            animationVariant="buttonTap"
          >
            Create Wallet
          </AnimatedButton>
        </AnimatedCardContent>
      </AnimatedCard>
    );
  }

  return (
    <AnimatedCard className="flex flex-col h-[140px]" animationVariant="fadeIn">
      <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <AnimatedCardTitle className="text-sm font-medium">Wallet Balance</AnimatedCardTitle>
        <AnimatedIcon icon={<Wallet className="h-4 w-4" />} size="sm" color="muted-foreground" />
      </AnimatedCardHeader>
      <AnimatedCardContent className="flex-1 flex flex-col justify-center">
        <div className="text-2xl font-bold">
          {wallet.balance.toFixed(4)} {wallet.network === "ETHEREUM" ? "ETH" : wallet.network === "POLYGON" ? "MATIC" : "Tokens"}
        </div>
        <p className="text-xs text-muted-foreground">
          {truncateAddress(wallet.address)} • {wallet.isTestnet ? "Testnet" : "Mainnet"}
        </p>
      </AnimatedCardContent>
      <AnimatedCardFooter className="p-2 flex justify-between">
        <AnimatedButton
          variant="ghost"
          size="sm"
          onClick={fetchWallet}
          className="text-xs"
          animationVariant="buttonTap"
        >
          <RefreshCw className="h-3 w-3 mr-1" />
          Refresh
        </AnimatedButton>
        <AnimatedButton
          variant="link"
          size="sm"
          onClick={() => router.push("/dashboard/wallet")}
          className="text-xs"
          animationVariant="buttonTap"
        >
          Manage Wallet
          <ArrowRight className="h-3 w-3 ml-1" />
        </AnimatedButton>
      </AnimatedCardFooter>
    </AnimatedCard>
  );
}
