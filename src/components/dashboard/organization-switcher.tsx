"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Check, ChevronsUpDown, PlusCircle, Building } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type Organization = {
  id: string;
  name: string;
  image?: string;
};

export function OrganizationSwitcher() {
  const [open, setOpen] = React.useState(false);
  const [organizations, setOrganizations] = React.useState<Organization[]>([
    {
      id: "1",
      name: "Acme Inc",
      image: "",
    },
    {
      id: "2",
      name: "Globex Corporation",
      image: "",
    },
  ]);
  const [selectedOrganization, setSelectedOrganization] = React.useState<Organization>(organizations[0]);
  const router = useRouter();

  const handleSelect = (organizationId: string) => {
    const org = organizations.find((org) => org.id === organizationId);
    if (org) {
      setSelectedOrganization(org);
      // In a real implementation, this would update the current organization in the session
      // and potentially redirect to the organization's dashboard
      // For now, we'll just close the popover
      setOpen(false);
    }
  };

  const handleCreateOrganization = () => {
    router.push("/organization/create");
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select organization"
          className="w-[200px] justify-between"
        >
          <div className="flex items-center gap-2">
            <Avatar className="h-5 w-5">
              <AvatarImage
                src={selectedOrganization.image}
                alt={selectedOrganization.name}
              />
              <AvatarFallback className="text-xs">
                {selectedOrganization.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <span className="truncate">{selectedOrganization.name}</span>
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandInput placeholder="Search organization..." />
            <CommandEmpty>No organization found.</CommandEmpty>
            <CommandGroup heading="Organizations">
              {organizations.map((org) => (
                <CommandItem
                  key={org.id}
                  onSelect={() => handleSelect(org.id)}
                  className="text-sm"
                >
                  <Avatar className="mr-2 h-5 w-5">
                    <AvatarImage
                      src={org.image}
                      alt={org.name}
                    />
                    <AvatarFallback className="text-xs">
                      {org.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span>{org.name}</span>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      selectedOrganization.id === org.id
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          <CommandSeparator />
          <CommandList>
            <CommandGroup>
              <CommandItem
                onSelect={handleCreateOrganization}
                className="cursor-pointer"
              >
                <PlusCircle className="mr-2 h-5 w-5" />
                Create Organization
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
