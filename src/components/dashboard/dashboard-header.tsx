"use client";

import * as React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { UserNav } from "@/components/dashboard/user-nav";
import { MobileNav } from "@/components/dashboard/mobile-nav";
import { Search } from "@/components/dashboard/search";
import { ThemeToggle } from "@/components/theme-toggle";
import { NotificationsDropdown } from "@/components/dashboard/notifications-dropdown";
import { OrganizationSwitcher } from "@/components/dashboard/organization-switcher";

interface DashboardHeaderProps {
  children?: React.ReactNode;
  className?: string;
}

export function DashboardHeader({
  children,
  className,
}: DashboardHeaderProps) {
  return (
    <header className={cn("sticky top-0 z-40 border-b bg-background", className)}>
      <div className="container flex h-16 items-center justify-between py-4">
        <div className="flex items-center gap-4 md:gap-8">
          <MobileNav />
          <Link href="/" className="hidden items-center space-x-2 md:flex">
            <span className="hidden font-bold sm:inline-block">
              Carbon Exchange
            </span>
          </Link>
          <div className="hidden md:flex">
            <Search />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="hidden md:flex md:items-center md:gap-2">
            <OrganizationSwitcher />
            <NotificationsDropdown />
            <ThemeToggle />
          </div>
          <UserNav />
        </div>
      </div>
      {children}
    </header>
  );
}
