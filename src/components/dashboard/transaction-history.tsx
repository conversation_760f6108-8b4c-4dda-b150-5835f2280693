"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowRight, ArrowUpRight, ArrowDownLeft, RefreshCw, AlertCircle } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useRouter } from "next/navigation";
import {
  Animated<PERSON><PERSON>,
  Animated<PERSON>ardHeader,
  <PERSON><PERSON>ard<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>ooter,
  AnimatedCardTitle,
  AnimatedCardDescription,
  LoadingSpinner
} from "@/components/ui/animated";

interface Transaction {
  id: string;
  type: string;
  status: string;
  amount: number;
  fee: number;
  transactionHash?: string;
  network?: string;
  createdAt: string;
}

interface TransactionHistoryProps {
  limit?: number;
  showViewAll?: boolean;
  className?: string;
}

export function TransactionHistory({ 
  limit = 5, 
  showViewAll = true,
  className = ""
}: TransactionHistoryProps) {
  const router = useRouter();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/wallet?limit=${limit}`);

      if (!response.ok) {
        throw new Error("Failed to fetch transaction history");
      }

      const data = await response.json();
      
      if (data.wallet && data.wallet.transactions) {
        setTransactions(data.wallet.transactions);
      } else {
        setTransactions([]);
      }
    } catch (err) {
      console.error("Error fetching transactions:", err);
      setError("Could not load transaction history");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [limit]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  // Get transaction icon
  const getTransactionIcon = (type: string) => {
    switch (type.toUpperCase()) {
      case "DEPOSIT":
        return <ArrowDownLeft className="h-4 w-4 text-green-500" />;
      case "WITHDRAW":
        return <ArrowUpRight className="h-4 w-4 text-red-500" />;
      default:
        return <ArrowRight className="h-4 w-4" />;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "COMPLETED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Pending
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800">
            {status}
          </Badge>
        );
    }
  };

  if (loading) {
    return (
      <AnimatedCard className={className}>
        <AnimatedCardHeader>
          <AnimatedCardTitle>Recent Transactions</AnimatedCardTitle>
          <AnimatedCardDescription>
            Your recent wallet activity
          </AnimatedCardDescription>
        </AnimatedCardHeader>
        <AnimatedCardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <Skeleton className="h-4 w-16 ml-auto" />
                  <Skeleton className="h-3 w-12 ml-auto" />
                </div>
              </div>
            ))}
          </div>
        </AnimatedCardContent>
      </AnimatedCard>
    );
  }

  if (error) {
    return (
      <AnimatedCard className={className}>
        <AnimatedCardHeader>
          <AnimatedCardTitle>Recent Transactions</AnimatedCardTitle>
          <AnimatedCardDescription>
            Your recent wallet activity
          </AnimatedCardDescription>
        </AnimatedCardHeader>
        <AnimatedCardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchTransactions}
              className="flex items-center"
            >
              <RefreshCw className="h-3 w-3 mr-2" />
              Retry
            </Button>
          </div>
        </AnimatedCardContent>
      </AnimatedCard>
    );
  }

  return (
    <AnimatedCard className={className}>
      <AnimatedCardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <AnimatedCardTitle>Recent Transactions</AnimatedCardTitle>
          <AnimatedCardDescription>
            Your recent wallet activity
          </AnimatedCardDescription>
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={fetchTransactions}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className="h-4 w-4" />
          <span className="sr-only">Refresh</span>
        </Button>
      </AnimatedCardHeader>
      <AnimatedCardContent>
        {transactions.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">No transactions yet</p>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b border-border last:border-0">
                <div className="flex items-center space-x-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div>
                    <p className="text-sm font-medium">{transaction.type}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatDate(transaction.createdAt)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-sm font-medium ${transaction.type.toUpperCase() === "DEPOSIT" ? "text-green-600" : "text-red-600"}`}>
                    {transaction.type.toUpperCase() === "DEPOSIT" ? "+" : "-"}
                    {formatCurrency(transaction.amount, transaction.network)}
                  </p>
                  <div className="mt-1">
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </AnimatedCardContent>
      {showViewAll && transactions.length > 0 && (
        <AnimatedCardFooter>
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => router.push("/dashboard/wallet")}
          >
            View All Transactions
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </AnimatedCardFooter>
      )}
    </AnimatedCard>
  );
}
