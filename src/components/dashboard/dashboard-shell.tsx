"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar";

interface DashboardShellProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showSidebar?: boolean;
}

export function DashboardShell({
  children,
  className,
  showHeader = true,
  showSidebar = true,
}: DashboardShellProps) {
  return (
    <div className="flex min-h-screen flex-col">
      {showHeader && <DashboardHeader />}
      <div className="flex flex-1">
        {showSidebar && <DashboardSidebar />}
        <main className={cn("flex-1 bg-background", className)}>
          {children}
        </main>
      </div>
    </div>
  );
}
