"use client";

import { useRef, useState, useCallback, useEffect } from 'react';

/**
 * A hook to help preserve focus state during animations
 *
 * This hook helps maintain focus on elements that might lose focus during animations
 * or re-renders. It tracks focus state and provides utilities to restore focus.
 *
 * @returns Focus preservation utilities
 */
export function useFocusPreservation<T extends HTMLElement>() {
  // Track if the element is currently focused
  const [isFocused, setIsFocused] = useState(false);

  // Reference to the element
  const elementRef = useRef<T>(null);

  // Track the active element before any animations or re-renders
  const activeElementRef = useRef<Element | null>(null);

  // Track the selection/cursor position
  const selectionStateRef = useRef<{
    selectionStart: number | null;
    selectionEnd: number | null;
    selectionDirection: string | null;
  }>({
    selectionStart: null,
    selectionEnd: null,
    selectionDirection: null,
  });

  // Handle focus event
  const handleFocus = useCallback((e: React.FocusEvent<T>) => {
    setIsFocused(true);
    activeElementRef.current = e.target;

    // Store selection state for input elements
    if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
      selectionStateRef.current = {
        selectionStart: e.target.selectionStart,
        selectionEnd: e.target.selectionEnd,
        selectionDirection: e.target.selectionDirection,
      };
    }
  }, []);

  // Handle blur event
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    activeElementRef.current = null;

    // Clear selection state
    selectionStateRef.current = {
      selectionStart: null,
      selectionEnd: null,
      selectionDirection: null,
    };
  }, []);

  // Restore focus after animations or re-renders if needed
  const restoreFocus = useCallback(() => {
    if (isFocused && elementRef.current) {
      // Use requestAnimationFrame to ensure focus happens after render
      requestAnimationFrame(() => {
        if (elementRef.current && document.activeElement !== elementRef.current) {
          elementRef.current.focus();

          // Restore selection state for input elements
          if (
            (elementRef.current instanceof HTMLInputElement ||
             elementRef.current instanceof HTMLTextAreaElement) &&
            selectionStateRef.current.selectionStart !== null &&
            selectionStateRef.current.selectionEnd !== null
          ) {
            try {
              elementRef.current.setSelectionRange(
                selectionStateRef.current.selectionStart,
                selectionStateRef.current.selectionEnd,
                selectionStateRef.current.selectionDirection || undefined
              );
            } catch (e) {
              // Ignore errors from setSelectionRange
              console.debug('Error restoring selection:', e);
            }
          }
        }
      });
    }
  }, [isFocused]);

  // Update selection state when typing
  const updateSelectionState = useCallback(() => {
    if (
      isFocused &&
      elementRef.current &&
      (elementRef.current instanceof HTMLInputElement ||
       elementRef.current instanceof HTMLTextAreaElement)
    ) {
      selectionStateRef.current = {
        selectionStart: elementRef.current.selectionStart,
        selectionEnd: elementRef.current.selectionEnd,
        selectionDirection: elementRef.current.selectionDirection,
      };
    }
  }, [isFocused]);

  // Automatically restore focus after component updates if it was focused
  useEffect(() => {
    if (isFocused) {
      restoreFocus();
    }
  }, [isFocused, restoreFocus]);

  // Add event listeners to track selection changes
  useEffect(() => {
    const element = elementRef.current;

    if (isFocused && element) {
      // Update selection state on key events
      const handleKeyEvent = () => {
        // Use setTimeout to ensure we get the updated selection after the key event
        setTimeout(updateSelectionState, 0);
      };

      // Update selection state on mouse events
      const handleMouseEvent = () => {
        // Use setTimeout to ensure we get the updated selection after the mouse event
        setTimeout(updateSelectionState, 0);
      };

      element.addEventListener('keyup', handleKeyEvent);
      element.addEventListener('keydown', handleKeyEvent);
      element.addEventListener('mouseup', handleMouseEvent);
      element.addEventListener('mousedown', handleMouseEvent);

      return () => {
        element.removeEventListener('keyup', handleKeyEvent);
        element.removeEventListener('keydown', handleKeyEvent);
        element.removeEventListener('mouseup', handleMouseEvent);
        element.removeEventListener('mousedown', handleMouseEvent);
      };
    }
  }, [isFocused, updateSelectionState]);

  return {
    isFocused,
    elementRef,
    handleFocus,
    handleBlur,
    restoreFocus,
    updateSelectionState,
    // Props that can be spread onto an element
    focusProps: {
      ref: elementRef,
      onFocus: handleFocus,
      onBlur: handleBlur,
    },
  };
}
