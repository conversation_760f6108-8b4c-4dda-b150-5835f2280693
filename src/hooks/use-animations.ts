"use client";

import { useEffect, useState } from 'react';
import { useReducedMotion } from 'framer-motion';
import { VARIANTS, combineVariants } from '@/lib/animations';
import {
  useOptimizedAnimations,
  useLazyAnimation,
  useAnimationProperties
} from '@/lib/animation-optimizations';

/**
 * Hook to manage animations with respect to reduced motion preferences and device performance
 * @returns Animation utilities and variants
 */
export function useAnimations() {
  const prefersReducedMotion = useReducedMotion();
  const {
    shouldSimplifyAnimations,
    durationMultiplier,
    willChange,
    enableStaggering
  } = useOptimizedAnimations();
  const [optimizedVariants, setOptimizedVariants] = useState(VARIANTS);

  // Adjust animations based on reduced motion preference and device performance
  useEffect(() => {
    if (shouldSimplifyAnimations) {
      // Create simplified variants for users who prefer reduced motion or have low-performance devices
      const simplified = Object.entries(VARIANTS).reduce((acc, [key, variant]) => {
        // For most animations, just fade in/out without movement
        const simplifiedVariant = {
          ...variant,
          initial: { opacity: 0 },
          animate: {
            opacity: 1,
            transition: { duration: 0.2 * durationMultiplier }
          },
          exit: {
            opacity: 0,
            transition: { duration: 0.1 * durationMultiplier }
          },
          // Remove any scale or position changes for hover/tap
          whileHover: {
            opacity: 0.9,
            transition: { duration: 0.1 * durationMultiplier }
          },
          whileTap: {
            opacity: 0.8,
            transition: { duration: 0.1 * durationMultiplier }
          },
        };

        return {
          ...acc,
          [key]: simplifiedVariant,
        };
      }, {});

      setOptimizedVariants(simplified);
    } else if (!enableStaggering) {
      // If staggering is disabled but other animations are fine, modify just the staggered animations
      const noStaggerVariants = Object.entries(VARIANTS).reduce((acc, [key, variant]) => {
        // Only modify variants that have staggering
        if (key === 'staggerContainer' || variant.transition?.staggerChildren) {
          return {
            ...acc,
            [key]: {
              ...variant,
              animate: {
                ...variant.animate,
                transition: {
                  ...variant.animate?.transition,
                  staggerChildren: 0,
                  delayChildren: 0,
                }
              },
              exit: {
                ...variant.exit,
                transition: {
                  ...variant.exit?.transition,
                  staggerChildren: 0,
                  staggerDirection: -1,
                }
              }
            }
          };
        }
        return {
          ...acc,
          [key]: variant,
        };
      }, {});

      setOptimizedVariants(noStaggerVariants);
    } else {
      // Add will-change to all variants for better performance
      if (willChange) {
        const optimized = Object.entries(VARIANTS).reduce((acc, [key, variant]) => {
          return {
            ...acc,
            [key]: {
              ...variant,
              style: { willChange }
            }
          };
        }, {});

        setOptimizedVariants(optimized);
      } else {
        setOptimizedVariants(VARIANTS);
      }
    }
  }, [shouldSimplifyAnimations, durationMultiplier, willChange, enableStaggering]);

  return {
    variants: optimizedVariants,
    prefersReducedMotion,
    shouldSimplifyAnimations,
    combineVariants,
    // Export the lazy animation hook for components to use
    useLazyAnimation,
    // Export animation properties for components to use
    useAnimationProperties,
  };
}

/**
 * Hook to get animation variants for a specific component
 * @param variantName The name of the variant to use
 * @param additionalVariants Additional variants to combine with the base variant
 * @returns Animation variants object
 */
export function useAnimationVariant(variantName: keyof typeof VARIANTS, ...additionalVariants: any[]) {
  const { variants, combineVariants } = useAnimations();
  const baseVariant = variants[variantName];

  if (additionalVariants.length === 0) {
    return baseVariant;
  }

  return combineVariants(baseVariant, ...additionalVariants);
}
