import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { OnboardingStep } from "@/lib/onboarding-state";
import { toast } from "@/components/ui/use-toast";

interface OnboardingState {
  currentStep: OnboardingStep;
  organizationId?: string;
  skippedSteps: OnboardingStep[];
  completedSteps: OnboardingStep[];
  isComplete: boolean;
  lastUpdated?: Date;
}

export const useOnboarding = (initialStep?: OnboardingStep) => {
  const [state, setState] = useState<OnboardingState>({
    currentStep: initialStep || OnboardingStep.ORGANIZATION_DETAILS,
    skippedSteps: [],
    completedSteps: [],
    isComplete: false,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchOnboardingState();
  }, []);

  const fetchOnboardingState = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/onboarding");

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch onboarding state");
      }

      const data = await response.json();
      setState(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Something went wrong");
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to load onboarding progress",
      });
    } finally {
      setLoading(false);
    }
  };

  const completeStep = async (step: OnboardingStep) => {
    try {
      setLoading(true);
      const response = await fetch("/api/onboarding", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "complete",
          step,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update onboarding state");
      }

      const data = await response.json();
      setState(data);

      // Navigate to the next step
      navigateToStep(data.currentStep, data.organizationId);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Something went wrong");
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update onboarding progress",
      });
    } finally {
      setLoading(false);
    }
  };

  const skipStep = async (step: OnboardingStep) => {
    try {
      setLoading(true);
      const response = await fetch("/api/onboarding", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "skip",
          step,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to skip step");
      }

      const data = await response.json();
      setState(data);
      toast({
        title: "Step skipped",
        description: "You can come back to this step later from your dashboard.",
      });

      // Navigate to the next step
      navigateToStep(data.currentStep, data.organizationId);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Something went wrong");
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to skip this step",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateState = async (updates: Partial<OnboardingState>) => {
    try {
      setLoading(true);
      const response = await fetch("/api/onboarding", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update onboarding state");
      }

      const data = await response.json();
      setState(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const navigateToStep = (step: OnboardingStep, organizationId?: string) => {
    if (!step || step === OnboardingStep.COMPLETE) {
      router.push('/dashboard');
      return;
    }

    const orgParam = organizationId ? `?organizationId=${organizationId}` : '';

    switch (step) {
      case OnboardingStep.ORGANIZATION_DETAILS:
        router.push('/onboarding');
        break;
      case OnboardingStep.TEAM_INVITATIONS:
        router.push(`/onboarding/team${orgParam}`);
        break;
      case OnboardingStep.SUBSCRIPTION:
        router.push(`/onboarding/subscription${orgParam}`);
        break;
      case OnboardingStep.WALLET_SETUP:
        router.push(`/onboarding/wallet${orgParam}`);
        break;
      case OnboardingStep.VERIFICATION:
        router.push(`/onboarding/verification${orgParam}`);
        break;
      default:
        router.push('/dashboard');
    }
  };

  const goToStep = (step: OnboardingStep) => {
    navigateToStep(step, state.organizationId);
  };

  return {
    state,
    loading,
    error,
    completeStep,
    skipStep,
    updateState,
    goToStep,
    refreshState: fetchOnboardingState,
  };
};
