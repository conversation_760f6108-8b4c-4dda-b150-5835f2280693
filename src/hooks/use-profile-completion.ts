import { useState, useEffect } from 'react';
import { ProfileField } from '@/components/profile/profile-completion-indicator';

interface UseProfileCompletionProps {
  organizationId: string;
}

interface UseProfileCompletionResult {
  fields: ProfileField[];
  completionPercentage: number;
  loading: boolean;
  error: string | null;
  organization: any;
  refreshProfile: () => Promise<void>;
}

export function useProfileCompletion({ organizationId }: UseProfileCompletionProps): UseProfileCompletionResult {
  const [organization, setOrganization] = useState<any>(null);
  const [fields, setFields] = useState<ProfileField[]>([]);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrganization = async () => {
    if (!organizationId) return;

    try {
      setLoading(true);
      setError(null);

      // First try to fetch from the user's organization endpoint
      // This is more likely to succeed as it doesn't require specific organization access checks
      let response = await fetch('/api/user/organization');

      // If that fails, try the specific organization endpoint
      if (!response.ok) {
        response = await fetch(`/api/organizations/${organizationId}`);
      }

      if (!response.ok) {
        // Try to extract a more detailed error message from the response
        let errorMessage = 'Failed to fetch organization data';
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          // If we can't parse the error JSON, use the default message
          console.warn('Could not parse error response:', e);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      const org = data.organization;

      if (!org) {
        throw new Error('Organization data not found in response');
      }

      setOrganization(org);

      // Create fields array based on organization data
      const fieldsList: ProfileField[] = [
        { name: 'name', label: 'Organization Name', completed: !!org.name, required: true },
        { name: 'industry', label: 'Industry', completed: !!org.industry, required: true },
        { name: 'country', label: 'Country', completed: !!org.country, required: true },
        { name: 'size', label: 'Organization Size', completed: !!org.size, required: true },
        { name: 'legalName', label: 'Legal Name', completed: !!org.legalName, required: false },
        { name: 'description', label: 'Description', completed: !!org.description, required: false },
        { name: 'website', label: 'Website', completed: !!org.website, required: false },
        { name: 'address', label: 'Address', completed: !!org.address, required: false },
        { name: 'city', label: 'City', completed: !!org.city, required: false },
        { name: 'phoneNumber', label: 'Phone Number', completed: !!org.phoneNumber, required: false },
        { name: 'registrationNumber', label: 'Registration Number', completed: !!org.registrationNumber, required: false },
        { name: 'taxId', label: 'Tax ID', completed: !!org.taxId, required: false },
        { name: 'primaryContact', label: 'Primary Contact', completed: !!org.primaryContact, required: false },
        { name: 'primaryContactEmail', label: 'Contact Email', completed: !!org.primaryContactEmail, required: false },
      ];

      setFields(fieldsList);
    } catch (error) {
      console.error('Error fetching organization:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');

      // Set default empty fields to prevent UI errors
      setFields([
        { name: 'name', label: 'Organization Name', completed: false, required: true },
        { name: 'industry', label: 'Industry', completed: false, required: true },
        { name: 'country', label: 'Country', completed: false, required: true },
        { name: 'size', label: 'Organization Size', completed: false, required: true },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Calculate completion percentage
  useEffect(() => {
    if (fields.length === 0) return;

    // Count completed required fields and total required fields
    const requiredFields = fields.filter(field => field.required);
    const completedRequiredFields = requiredFields.filter(field => field.completed);

    // Count completed optional fields and total optional fields
    const optionalFields = fields.filter(field => !field.required);
    const completedOptionalFields = optionalFields.filter(field => field.completed);

    // Calculate percentage (required fields count more than optional fields)
    const requiredWeight = 0.7; // 70% of the score comes from required fields
    const optionalWeight = 0.3; // 30% of the score comes from optional fields

    let requiredPercentage = requiredFields.length > 0
      ? (completedRequiredFields.length / requiredFields.length) * 100 * requiredWeight
      : 0;

    let optionalPercentage = optionalFields.length > 0
      ? (completedOptionalFields.length / optionalFields.length) * 100 * optionalWeight
      : 0;

    // If all required fields are completed, ensure at least 70% completion
    if (requiredFields.length > 0 && completedRequiredFields.length === requiredFields.length) {
      requiredPercentage = 70;
    }

    const totalPercentage = Math.min(100, Math.round(requiredPercentage + optionalPercentage));
    setCompletionPercentage(totalPercentage);
  }, [fields]);

  // Fetch organization data on mount or when organizationId changes
  useEffect(() => {
    if (organizationId) {
      fetchOrganization();
    }
  }, [organizationId]);

  return {
    fields,
    completionPercentage,
    loading,
    error,
    organization,
    refreshProfile: fetchOrganization
  };
}
