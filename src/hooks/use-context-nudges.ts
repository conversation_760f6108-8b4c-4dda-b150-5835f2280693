import { useState, useEffect } from 'react';
import { useProfileCompletion } from './use-profile-completion';

export interface ContextNudgeInfo {
  id: string;
  title: string;
  description: string;
  fields: string[];
  variant?: 'default' | 'warning' | 'info';
  requiredFields: string[];
}

interface UseContextNudgesProps {
  organizationId: string;
  context: string;
}

export function useContextNudges({ organizationId, context }: UseContextNudgesProps) {
  const { fields, organization, loading } = useProfileCompletion({ organizationId });
  const [relevantNudges, setRelevantNudges] = useState<ContextNudgeInfo[]>([]);

  useEffect(() => {
    if (loading || !organization) return;

    // Define all possible nudges
    const allNudges: Record<string, ContextNudgeInfo> = {
      // Carbon Credit Listing Nudges
      'carbon-credit-legal': {
        id: 'carbon-credit-legal',
        title: 'Complete Legal Information',
        description: 'Adding your legal registration details helps verify your organization for carbon credit trading.',
        fields: ['legalName', 'registrationNumber', 'taxId'],
        variant: 'warning',
        requiredFields: ['legalName', 'registrationNumber']
      },
      'carbon-credit-contact': {
        id: 'carbon-credit-contact',
        title: 'Add Contact Information',
        description: 'Complete contact details ensure buyers can verify your organization.',
        fields: ['address', 'city', 'phoneNumber'],
        variant: 'info',
        requiredFields: ['address', 'phoneNumber']
      },

      // Wallet Setup Nudges
      'wallet-address': {
        id: 'wallet-address',
        title: 'Complete Address Information',
        description: 'Your full address is required for wallet compliance verification.',
        fields: ['address', 'city', 'state', 'postalCode', 'country'],
        variant: 'warning',
        requiredFields: ['address', 'city', 'country']
      },
      'wallet-legal': {
        id: 'wallet-legal',
        title: 'Add Legal Information',
        description: 'Legal details are required for wallet KYC verification.',
        fields: ['legalName', 'registrationNumber'],
        variant: 'warning',
        requiredFields: ['legalName']
      },

      // Team Invitation Nudges
      'team-description': {
        id: 'team-description',
        title: 'Add Organization Description',
        description: 'Help team members understand your organization by adding a description.',
        fields: ['description'],
        variant: 'info',
        requiredFields: ['description']
      },
      'team-contact': {
        id: 'team-contact',
        title: 'Complete Primary Contact',
        description: 'Adding a primary contact helps team members know who to reach out to.',
        fields: ['primaryContact', 'primaryContactEmail'],
        variant: 'info',
        requiredFields: ['primaryContact', 'primaryContactEmail']
      },

      // Verification Document Nudges
      'verification-legal': {
        id: 'verification-legal',
        title: 'Add Legal Registration Details',
        description: 'Complete legal information speeds up the verification process.',
        fields: ['legalName', 'registrationNumber', 'taxId'],
        variant: 'warning',
        requiredFields: ['legalName', 'registrationNumber']
      },
      'verification-address': {
        id: 'verification-address',
        title: 'Complete Address Information',
        description: 'Your full address is required for document verification.',
        fields: ['address', 'city', 'state', 'postalCode', 'country'],
        variant: 'warning',
        requiredFields: ['address', 'city', 'country']
      },

      // Subscription Nudges
      'subscription-billing': {
        id: 'subscription-billing',
        title: 'Complete Billing Information',
        description: 'Add your organization details to ensure accurate billing information.',
        fields: ['legalName', 'address', 'city', 'country'],
        variant: 'warning',
        requiredFields: ['legalName', 'address', 'country']
      },
      'subscription-contact': {
        id: 'subscription-contact',
        title: 'Add Primary Contact',
        description: 'A primary contact is needed for subscription communications.',
        fields: ['primaryContact', 'primaryContactEmail'],
        variant: 'info',
        requiredFields: ['primaryContact', 'primaryContactEmail']
      },

      // Transaction Nudges
      'transaction-tax': {
        id: 'transaction-tax',
        title: 'Add Tax Information',
        description: 'Your tax ID is required for proper transaction reporting.',
        fields: ['taxId'],
        variant: 'warning',
        requiredFields: ['taxId']
      },
      'transaction-legal': {
        id: 'transaction-legal',
        title: 'Complete Legal Information',
        description: 'Legal details are required for transaction compliance.',
        fields: ['legalName', 'registrationNumber'],
        variant: 'warning',
        requiredFields: ['legalName', 'registrationNumber']
      }
    };

    // Check which nudges are relevant for the current context
    const contextNudgeMap: Record<string, string[]> = {
      'carbon-credits': ['carbon-credit-legal', 'carbon-credit-contact'],
      'wallet': ['wallet-address', 'wallet-legal'],
      'team': ['team-description', 'team-contact'],
      'verification': ['verification-legal', 'verification-address'],
      'subscription': ['subscription-billing', 'subscription-contact'],
      'transactions': ['transaction-tax', 'transaction-legal']
    };

    // Get nudges for the current context
    const contextNudgeIds = contextNudgeMap[context] || [];
    const possibleNudges = contextNudgeIds.map(id => allNudges[id]).filter(Boolean);

    // Filter nudges based on missing fields
    const incompleteNudges = possibleNudges.filter(nudge => {
      // Check if any required fields are missing
      return nudge.requiredFields.some(fieldName => {
        const field = fields.find(f => f.name === fieldName);
        return field && !field.completed;
      });
    });

    setRelevantNudges(incompleteNudges);
  }, [context, fields, loading, organization]);

  return {
    nudges: relevantNudges,
    loading
  };
}
