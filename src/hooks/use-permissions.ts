/**
 * Permission Hooks
 * 
 * This module provides React hooks for checking permissions on the client side.
 */

import { useSession } from 'next-auth/react';
import { useState, useEffect, useCallback } from 'react';
import { useOrganization } from '@/hooks/use-organization';

/**
 * Hook to check if the current user has a permission
 */
export function usePermission(permissionName: string, options: {
  resourceType?: string;
  resourceId?: string;
  teamId?: string;
} = {}) {
  const { data: session, status } = useSession();
  const { organization } = useOrganization();
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  const checkPermission = useCallback(async () => {
    if (status !== 'authenticated' || !session?.user) {
      setHasPermission(false);
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Build the query parameters
      const params = new URLSearchParams({
        permission: permissionName,
      });
      
      if (options.resourceType) {
        params.append('resourceType', options.resourceType);
      }
      
      if (options.resourceId) {
        params.append('resourceId', options.resourceId);
      }
      
      if (options.teamId) {
        params.append('teamId', options.teamId);
      }
      
      // Call the API to check permission
      const response = await fetch(`/api/rbac/check-permission?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to check permission');
      }
      
      const data = await response.json();
      setHasPermission(data.granted);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setHasPermission(false);
    } finally {
      setIsLoading(false);
    }
  }, [permissionName, options.resourceType, options.resourceId, options.teamId, session, status]);
  
  useEffect(() => {
    checkPermission();
  }, [checkPermission]);
  
  return {
    hasPermission,
    isLoading,
    error,
    checkPermission,
  };
}

/**
 * Hook to get all permissions for the current user
 */
export function useUserPermissions() {
  const { data: session, status } = useSession();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchPermissions = useCallback(async () => {
    if (status !== 'authenticated' || !session?.user) {
      setPermissions([]);
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Call the API to get permissions
      const response = await fetch('/api/rbac/user-permissions');
      
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      
      const data = await response.json();
      setPermissions(data.permissions);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setPermissions([]);
    } finally {
      setIsLoading(false);
    }
  }, [session, status]);
  
  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);
  
  return {
    permissions,
    isLoading,
    error,
    refetch: fetchPermissions,
  };
}

/**
 * Hook to get all roles for the current user
 */
export function useUserRoles() {
  const { data: session, status } = useSession();
  const [roles, setRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchRoles = useCallback(async () => {
    if (status !== 'authenticated' || !session?.user) {
      setRoles([]);
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Call the API to get roles
      const response = await fetch('/api/rbac/user-roles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      
      const data = await response.json();
      setRoles(data.roles);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setRoles([]);
    } finally {
      setIsLoading(false);
    }
  }, [session, status]);
  
  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);
  
  return {
    roles,
    isLoading,
    error,
    refetch: fetchRoles,
  };
}
