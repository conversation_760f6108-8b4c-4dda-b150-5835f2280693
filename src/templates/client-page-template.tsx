"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ClientPageTemplateProps {
  userName: string;
}

export default function ClientPageTemplate({ userName }: ClientPageTemplateProps) {
  const { data: session } = useSession();
  const router = useRouter();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Page Title"
              description="Page description goes here"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Page Title", href: "/dashboard/page-path", isCurrent: true }
              ]}
            />
          </div>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Content Section</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Content goes here</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
