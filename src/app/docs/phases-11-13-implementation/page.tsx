import fs from "fs";
import path from "path";
import { Metada<PERSON> } from "next";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { marked } from "marked";

export const metadata: Metadata = {
  title: "Phases 11-13 Implementation | Carbon Exchange",
  description: "Documentation for components implemented in Phases 11-13",
};

export default function DocumentationPage() {
  // Read the markdown file
  const filePath = path.join(process.cwd(), "docs", "phases-11-13-implementation.md");
  const fileContent = fs.readFileSync(filePath, "utf8");
  
  // Convert markdown to HTML
  const htmlContent = marked(fileContent);
  
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center">
        <Button variant="outline" size="sm" asChild>
          <a href="/demo/phases-11-13">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Demo
          </a>
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Phases 11-13 Implementation</CardTitle>
          <CardDescription>
            Documentation for components implemented in Phases 11-13 of the UX improvement plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none dark:prose-invert" dangerouslySetInnerHTML={{ __html: htmlContent }} />
        </CardContent>
      </Card>
    </div>
  );
}
