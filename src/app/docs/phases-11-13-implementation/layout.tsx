import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Documentation | Carbon Exchange",
  description: "Documentation for the Carbon Exchange platform",
};

export default function DocumentationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-muted/40">
      <div className="bg-background py-4 border-b sticky top-0 z-10">
        <div className="container mx-auto flex items-center justify-between">
          <h1 className="text-xl font-bold">Carbon Exchange Documentation</h1>
          <div className="flex items-center space-x-4">
            <a href="/" className="text-sm text-muted-foreground hover:text-foreground">
              Back to Home
            </a>
            <a href="/demo/phases-11-13" className="text-sm text-muted-foreground hover:text-foreground">
              View Demo
            </a>
          </div>
        </div>
      </div>
      {children}
    </div>
  );
}
