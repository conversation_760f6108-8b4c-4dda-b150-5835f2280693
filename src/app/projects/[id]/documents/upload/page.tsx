"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ArrowLeft, Upload, FileText, AlertCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Define form schema
const documentSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  type: z.string().min(1, "Document type is required"),
  url: z.string().url("Please enter a valid URL"),
  notes: z.string().optional(),
});

type DocumentFormValues = z.infer<typeof documentSchema>;

export default function UploadDocumentPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize form
  const form = useForm<DocumentFormValues>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      name: "",
      type: "",
      url: "",
      notes: "",
    },
  });
  
  // Handle form submission
  const onSubmit = async (data: DocumentFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/projects/${params.id}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload document");
      }
      
      toast({
        title: "Document Uploaded",
        description: "Your document has been uploaded successfully.",
      });
      
      router.push(`/projects/${params.id}?tab=documents`);
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <Button variant="outline" onClick={() => router.push(`/projects/${params.id}?tab=documents`)}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
          </div>
          
          <div className="mb-6">
            <h1 className="text-3xl font-bold tracking-tight">Upload Document</h1>
            <p className="text-muted-foreground mt-1">
              Add documentation to your project
            </p>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>
                Enter the details of the document you want to upload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter document name" {...field} />
                        </FormControl>
                        <FormDescription>
                          A descriptive name for the document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select document type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="PROJECT_DESIGN">Project Design</SelectItem>
                            <SelectItem value="METHODOLOGY">Methodology</SelectItem>
                            <SelectItem value="BASELINE_ASSESSMENT">Baseline Assessment</SelectItem>
                            <SelectItem value="MONITORING_PLAN">Monitoring Plan</SelectItem>
                            <SelectItem value="VALIDATION_REPORT">Validation Report</SelectItem>
                            <SelectItem value="VERIFICATION_REPORT">Verification Report</SelectItem>
                            <SelectItem value="LEGAL_DOCUMENT">Legal Document</SelectItem>
                            <SelectItem value="STAKEHOLDER_CONSULTATION">Stakeholder Consultation</SelectItem>
                            <SelectItem value="ENVIRONMENTAL_IMPACT">Environmental Impact</SelectItem>
                            <SelectItem value="SOCIAL_IMPACT">Social Impact</SelectItem>
                            <SelectItem value="FINANCIAL_DOCUMENT">Financial Document</SelectItem>
                            <SelectItem value="OTHER">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The type of document you are uploading
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com/document.pdf" {...field} />
                        </FormControl>
                        <FormDescription>
                          URL to the document (e.g., from Google Drive, Dropbox, etc.)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Additional notes about this document" {...field} />
                        </FormControl>
                        <FormDescription>
                          Any additional information about this document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Alert>
                    <FileText className="h-4 w-4" />
                    <AlertTitle>Document Security</AlertTitle>
                    <AlertDescription>
                      Make sure the document URL is accessible to authorized users. For sensitive documents, consider using a secure sharing service.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.push(`/projects/${params.id}?tab=documents`)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Uploading..." : "Upload Document"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
