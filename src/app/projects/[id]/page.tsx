"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  Leaf,
  FileText,
  Link,
  Wallet,
  BarChart3,
  Calendar,
  MapPin,
  Building2,
  CheckCircle,
  AlertTriangle,
  Clock,
  Edit,
  Trash2,
  Plus,
  ExternalLink,
  AlertCircle
} from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { AnimatedButton } from "@/components/ui/animated-button";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Spinner } from "@/components/ui/spinner";
import { ProjectCreditLinking } from "@/components/projects/project-credit-linking";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function ProjectDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [project, setProject] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [showCreditLinking, setShowCreditLinking] = useState(false);
  const [availableCredits, setAvailableCredits] = useState([]);
  const [projectId, setProjectId] = useState<string>("");

  useEffect(() => {
    // Safely access params.id and store it in state
    if (params && typeof params === 'object' && 'id' in params) {
      setProjectId(params.id);
    }
  }, [params]);

  useEffect(() => {
    if (projectId) {
      fetchProjectDetails();
    }
  }, [projectId]);

  const fetchProjectDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/projects/${projectId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch project details");
      }

      const data = await response.json();
      setProject(data.project);
    } catch (error) {
      console.error("Error fetching project details:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
      toast({
        title: "Error",
        description: "Failed to fetch project details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAvailableCredits = async () => {
    try {
      const response = await fetch(`/api/carbon-credits?status=VERIFIED&verificationStatus=VERIFIED`);

      if (!response.ok) {
        throw new Error("Failed to fetch available carbon credits");
      }

      const data = await response.json();
      setAvailableCredits(data.carbonCredits || []);
    } catch (error) {
      console.error("Error fetching available credits:", error);
      toast({
        title: "Error",
        description: "Failed to fetch available carbon credits",
        variant: "destructive",
      });
    }
  };

  const handleLinkCredits = () => {
    fetchAvailableCredits();
    setShowCreditLinking(true);
  };

  const handleSaveCreditLinks = async (toLink, toUnlink) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/carbon-credits/link`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ toLink, toUnlink }),
      });

      if (!response.ok) {
        throw new Error("Failed to update credit links");
      }

      toast({
        title: "Success",
        description: "Carbon credit links updated successfully",
      });

      setShowCreditLinking(false);
      fetchProjectDetails();
    } catch (error) {
      console.error("Error updating credit links:", error);
      toast({
        title: "Error",
        description: "Failed to update credit links",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Not specified";
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const getStatusBadgeVariant = (status) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
      ACTIVE: "bg-green-100 text-green-800 border-green-200",
      COMPLETED: "bg-blue-100 text-blue-800 border-blue-200",
      SUSPENDED: "bg-red-100 text-red-800 border-red-200",
      CANCELLED: "bg-gray-100 text-gray-800 border-gray-200",
    };

    return variants[status] || "";
  };

  const getVerificationStatusBadgeVariant = (status) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
      VERIFIED: "bg-green-100 text-green-800 border-green-200",
      REJECTED: "bg-red-100 text-red-800 border-red-200",
      EXPIRED: "bg-gray-100 text-gray-800 border-gray-200",
    };

    return variants[status] || "";
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center h-full">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error || !project) {
    return (
      <ProtectedPage>
        <div className="container mx-auto py-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "Project not found"}
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button variant="outline" onClick={() => router.push("/projects")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </div>
        </div>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <Button variant="outline" onClick={() => router.push("/projects")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </div>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-3xl font-bold tracking-tight">{project.name}</h1>
                <Badge variant="outline" className={getStatusBadgeVariant(project.status)}>
                  {project.status}
                </Badge>
                <Badge variant="outline" className={getVerificationStatusBadgeVariant(project.verificationStatus)}>
                  {project.verificationStatus}
                </Badge>
              </div>
              <p className="text-muted-foreground mt-1">
                {project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())} Project
              </p>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={() => router.push(`/projects/${projectId}/edit`)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Project
              </Button>
              <Button variant="outline" onClick={handleLinkCredits}>
                <Link className="mr-2 h-4 w-4" />
                Manage Credits
              </Button>
            </div>
          </div>

          {showCreditLinking ? (
            <ProjectCreditLinking
              project={project}
              availableCredits={availableCredits}
              onSave={handleSaveCreditLinks}
              onCancel={() => setShowCreditLinking(false)}
            />
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4 md:w-auto md:grid-cols-none md:flex">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="credits">Carbon Credits</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="financials">Financials</TabsTrigger>
              </TabsList>

              <div className="mt-6">
                <TabsContent value="overview" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Project Details</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                          <p>{project.description || "No description provided"}</p>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Project Type</h3>
                            <p>{project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Organization</h3>
                            <p>{project.organization?.name || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Start Date</h3>
                            <p>{formatDate(project.startDate)}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">End Date</h3>
                            <p>{formatDate(project.endDate)}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Location</h3>
                            <p>{project.location || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Country</h3>
                            <p>{project.country || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Standard</h3>
                            <p>{project.standard || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Methodology</h3>
                            <p>{project.methodology || "Not specified"}</p>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-2">Emissions Reductions</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-muted-foreground">Estimated Reductions</p>
                            <p className="text-lg font-medium">{project.estimatedReductions?.toLocaleString() || "Not specified"} tCO2e</p>
                          </div>

                          <div>
                            <p className="text-sm text-muted-foreground">Actual Reductions</p>
                            <p className="text-lg font-medium">{project.actualReductions?.toLocaleString() || "Not specified"} tCO2e</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="credits" className="space-y-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle>Carbon Credits</CardTitle>
                      <Button variant="outline" size="sm" onClick={handleLinkCredits}>
                        <Plus className="mr-2 h-4 w-4" />
                        Manage Credits
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {project.carbonCredits && project.carbonCredits.length > 0 ? (
                        <div className="space-y-4">
                          {project.carbonCredits.map((credit) => (
                            <div key={credit.id} className="flex items-center justify-between p-4 border rounded-md">
                              <div>
                                <p className="font-medium">{credit.name}</p>
                                <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                                  <span>Vintage: {credit.vintage}</span>
                                  <span>Quantity: {credit.quantity.toLocaleString()} tCO2e</span>
                                  <span>Available: {credit.availableQuantity.toLocaleString()} tCO2e</span>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}`)}>
                                View Details
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-muted-foreground mb-4">No carbon credits linked to this project yet</p>
                          <Button variant="outline" onClick={handleLinkCredits}>
                            <Plus className="mr-2 h-4 w-4" />
                            Link Carbon Credits
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="documents" className="space-y-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle>Project Documents</CardTitle>
                      <Button variant="outline" size="sm" onClick={() => router.push(`/projects/${projectId}/documents/upload`)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Upload Document
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {project.documents && project.documents.length > 0 ? (
                        <div className="space-y-4">
                          {project.documents.map((document) => (
                            <div key={document.id} className="flex items-center justify-between p-4 border rounded-md">
                              <div className="flex items-center gap-3">
                                <FileText className="h-5 w-5 text-muted-foreground" />
                                <div>
                                  <p className="font-medium">{document.name}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {document.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                                  </p>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm" asChild>
                                <a href={document.url} target="_blank" rel="noopener noreferrer">
                                  <ExternalLink className="mr-2 h-4 w-4" />
                                  View
                                </a>
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-muted-foreground mb-4">No documents uploaded for this project yet</p>
                          <Button variant="outline" onClick={() => router.push(`/projects/${projectId}/documents/upload`)}>
                            <Plus className="mr-2 h-4 w-4" />
                            Upload Document
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="financials" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Financial Metrics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 border rounded-md">
                          <p className="text-sm text-muted-foreground">Budget</p>
                          <p className="text-xl font-medium">${project.budget?.toLocaleString() || "Not specified"}</p>
                        </div>

                        <div className="p-4 border rounded-md">
                          <p className="text-sm text-muted-foreground">ROI</p>
                          <p className="text-xl font-medium">{project.roi ? `${project.roi}%` : "Not specified"}</p>
                        </div>

                        <div className="p-4 border rounded-md">
                          <p className="text-sm text-muted-foreground">Carbon Value</p>
                          <p className="text-xl font-medium">
                            {project.carbonCredits && project.carbonCredits.length > 0
                              ? `$${project.carbonCredits.reduce((sum, credit) => sum + (credit.price * credit.quantity), 0).toLocaleString()}`
                              : "Not available"}
                          </p>
                        </div>
                      </div>

                      {project.financialMetrics && project.financialMetrics.length > 0 ? (
                        <div className="mt-6">
                          <h3 className="text-sm font-medium mb-3">Financial History</h3>
                          <div className="space-y-4">
                            {project.financialMetrics.map((metric) => (
                              <div key={metric.id} className="flex items-center justify-between p-4 border rounded-md">
                                <div>
                                  <p className="font-medium">{metric.name}</p>
                                  <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                                    <span>{metric.period}</span>
                                    <span>{formatDate(metric.startDate)} - {formatDate(metric.endDate)}</span>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-medium">{metric.currency} {metric.value.toLocaleString()}</p>
                                  {metric.changePercent && (
                                    <p className={`text-sm ${metric.changePercent > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                      {metric.changePercent > 0 ? '+' : ''}{metric.changePercent}%
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-6 mt-4">
                          <p className="text-muted-foreground">No financial metrics available for this project</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
