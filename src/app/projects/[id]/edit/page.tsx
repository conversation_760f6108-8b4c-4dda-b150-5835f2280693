"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ArrowLeft, Save, AlertCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Spinner } from "@/components/ui/spinner";

// Define form schema
const projectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  type: z.string().min(1, "Project type is required"),
  status: z.string().min(1, "Project status is required"),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  methodologyVersion: z.string().optional(),
  estimatedReductions: z.coerce.number().optional(),
  actualReductions: z.coerce.number().optional(),
  verifier: z.string().optional(),
  validator: z.string().optional(),
  budget: z.coerce.number().optional(),
  roi: z.coerce.number().optional(),
});

type ProjectFormValues = z.infer<typeof projectSchema>;

export default function EditProjectPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize form
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: "",
      description: "",
      type: "",
      status: "",
      startDate: "",
      endDate: "",
      location: "",
      country: "",
      standard: "",
      methodology: "",
      methodologyVersion: "",
      estimatedReductions: undefined,
      actualReductions: undefined,
      verifier: "",
      validator: "",
      budget: undefined,
      roi: undefined,
    },
  });

  // Fetch project details
  useEffect(() => {
    const fetchProjectDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/projects/${params.id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch project details");
        }

        const data = await response.json();
        const project = data.project;

        // Format dates for form input
        const formatDateForInput = (dateString: string | null) => {
          if (!dateString) return "";
          const date = new Date(dateString);
          return date.toISOString().split('T')[0];
        };

        // Set form values
        form.reset({
          name: project.name,
          description: project.description || "",
          type: project.type,
          status: project.status,
          startDate: formatDateForInput(project.startDate),
          endDate: formatDateForInput(project.endDate),
          location: project.location || "",
          country: project.country || "",
          standard: project.standard || "",
          methodology: project.methodology || "",
          methodologyVersion: project.methodologyVersion || "",
          estimatedReductions: project.estimatedReductions,
          actualReductions: project.actualReductions,
          verifier: project.verifier || "",
          validator: project.validator || "",
          budget: project.budget,
          roi: project.roi,
        });
      } catch (error) {
        console.error("Error fetching project details:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
        toast({
          title: "Error",
          description: "Failed to fetch project details",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectDetails();
  }, [params.id, form]);

  // Handle form submission
  const onSubmit = async (data: ProjectFormValues) => {
    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/projects/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update project");
      }

      toast({
        title: "Project Updated",
        description: "Your project has been updated successfully.",
      });

      router.push(`/projects/${params.id}`);
    } catch (error) {
      console.error("Error updating project:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update project",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center h-full">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <div className="container mx-auto py-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button variant="outline" onClick={() => router.push(`/projects/${params.id}`)}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
          </div>
        </div>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <Button variant="outline" onClick={() => router.push(`/projects/${params.id}`)}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
          </div>

          <div className="mb-6">
            <h1 className="text-3xl font-bold tracking-tight">Edit Project</h1>
            <p className="text-muted-foreground mt-1">
              Update your project details
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>
                Update the information for your carbon reduction project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Project Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter project name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Project Type</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select project type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="RENEWABLE_ENERGY">Renewable Energy</SelectItem>
                              <SelectItem value="FORESTRY">Forestry</SelectItem>
                              <SelectItem value="METHANE_REDUCTION">Methane Reduction</SelectItem>
                              <SelectItem value="ENERGY_EFFICIENCY">Energy Efficiency</SelectItem>
                              <SelectItem value="WASTE_MANAGEMENT">Waste Management</SelectItem>
                              <SelectItem value="AGRICULTURE">Agriculture</SelectItem>
                              <SelectItem value="TRANSPORTATION">Transportation</SelectItem>
                              <SelectItem value="INDUSTRIAL">Industrial</SelectItem>
                              <SelectItem value="OTHER">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea placeholder="Enter project description" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Project Status</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select project status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="PENDING">Pending</SelectItem>
                              <SelectItem value="ACTIVE">Active</SelectItem>
                              <SelectItem value="COMPLETED">Completed</SelectItem>
                              <SelectItem value="SUSPENDED">Suspended</SelectItem>
                              <SelectItem value="CANCELLED">Cancelled</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="startDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start Date</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="endDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>End Date</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter project location" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Country</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter country" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="standard"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Standard</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Verra, Gold Standard" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="methodology"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Methodology</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., VM0015, AMS-III.D" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="methodologyVersion"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Methodology Version</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., 1.0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="estimatedReductions"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Estimated Reductions (tCO2e)</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="actualReductions"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Actual Reductions (tCO2e)</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="verifier"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Verifier</FormLabel>
                          <FormControl>
                            <Input placeholder="Verification body" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="validator"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Validator</FormLabel>
                          <FormControl>
                            <Input placeholder="Validation body" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="budget"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Budget (INR)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="roi"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ROI (%)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.push(`/projects/${params.id}`)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Saving..." : "Save Changes"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
