"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { 
  ArrowLeft, 
  Plus, 
  Filter, 
  Search, 
  Leaf, 
  Wind, 
  Droplets, 
  Lightbulb, 
  Trash2, 
  Factory, 
  Car, 
  Building2, 
  MoreHorizontal 
} from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { showAnimatedToast } from "@/lib/utils";
import { PageTransition } from "@/components/ui/page-transition";
import { AnimatedButton } from "@/components/ui/animated-button";
import { AnimatedCard } from "@/components/ui/animated-card";
import { AnimatedGrid } from "@/components/ui/animated-grid";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Spinner } from "@/components/ui/spinner";

// Project type icon mapping
const projectTypeIcons = {
  RENEWABLE_ENERGY: <Wind className="h-5 w-5" />,
  FORESTRY: <Leaf className="h-5 w-5" />,
  METHANE_REDUCTION: <Factory className="h-5 w-5" />,
  ENERGY_EFFICIENCY: <Lightbulb className="h-5 w-5" />,
  WASTE_MANAGEMENT: <Trash2 className="h-5 w-5" />,
  AGRICULTURE: <Leaf className="h-5 w-5" />,
  TRANSPORTATION: <Car className="h-5 w-5" />,
  INDUSTRIAL: <Building2 className="h-5 w-5" />,
  OTHER: <Droplets className="h-5 w-5" />,
};

// Status badge variants
const statusVariants = {
  PENDING: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
  ACTIVE: "bg-green-100 text-green-800 hover:bg-green-100",
  COMPLETED: "bg-blue-100 text-blue-800 hover:bg-blue-100",
  SUSPENDED: "bg-red-100 text-red-800 hover:bg-red-100",
  CANCELLED: "bg-gray-100 text-gray-800 hover:bg-gray-100",
};

const verificationStatusVariants = {
  PENDING: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
  VERIFIED: "bg-green-100 text-green-800 hover:bg-green-100",
  REJECTED: "bg-red-100 text-red-800 hover:bg-red-100",
  EXPIRED: "bg-gray-100 text-gray-800 hover:bg-gray-100",
};

export default function ProjectsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  
  const [projects, setProjects] = useState([]);
  const [filters, setFilters] = useState({
    types: [],
    statuses: [],
    countries: [],
    standards: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 9,
    pages: 1,
  });
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [countryFilter, setCountryFilter] = useState("all");
  const [standardFilter, setStandardFilter] = useState("all");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [viewMode, setViewMode] = useState("grid");
  
  // Fetch projects on initial load and when filters change
  useEffect(() => {
    fetchProjects();
  }, [
    pagination.page,
    pagination.limit,
    typeFilter,
    statusFilter,
    countryFilter,
    standardFilter,
    sortBy,
    sortOrder,
    searchQuery,
  ]);
  
  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      
      // Build query parameters
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });
      
      if (typeFilter !== "all") params.append("type", typeFilter);
      if (statusFilter !== "all") params.append("status", statusFilter);
      if (countryFilter !== "all") params.append("country", countryFilter);
      if (standardFilter !== "all") params.append("standard", standardFilter);
      if (searchQuery) params.append("search", searchQuery);
      
      const response = await fetch(`/api/projects?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch projects");
      }
      
      const data = await response.json();
      
      setProjects(data.projects);
      setFilters(data.filters);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching projects:", error);
      showAnimatedToast("error", "Error", "Failed to fetch projects");
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSearch = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchProjects();
  };
  
  const handleCreateProject = () => {
    router.push("/projects/create");
  };
  
  const handleViewProject = (projectId) => {
    router.push(`/projects/${projectId}`);
  };
  
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, page }));
  };
  
  const formatDate = (dateString) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };
  
  return (
    <ProtectedPage loadingComponent={<Spinner size="lg" />}>
      <PageTransition>
        <div className="size-full flex flex-col gap-6 justify-between overflow-auto p-4">
          <AnimatedButton
            variant="outline"
            onClick={() => router.push("/dashboard")}
            className="mb-6 w-fit"
            animationVariant="buttonTap"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </AnimatedButton>
          
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Projects</h1>
              <p className="text-muted-foreground">
                Manage your carbon reduction projects
              </p>
            </div>
            
            <AnimatedButton
              onClick={handleCreateProject}
              animationVariant="buttonTap"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Project
            </AnimatedButton>
          </div>
          
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1">
              <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
                <Input
                  type="search"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
                <AnimatedButton type="submit" size="icon" animationVariant="buttonTap">
                  <Search className="h-4 w-4" />
                </AnimatedButton>
              </form>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Project Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {filters.types?.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {filters.statuses?.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.charAt(0) + status.slice(1).toLowerCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={countryFilter} onValueChange={setCountryFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Countries</SelectItem>
                  {filters.countries?.map((country) => (
                    <SelectItem key={country} value={country}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={standardFilter} onValueChange={setStandardFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Standard" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Standards</SelectItem>
                  {filters.standards?.map((standard) => (
                    <SelectItem key={standard} value={standard}>
                      {standard}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Projects</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="mt-0">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array(6).fill(0).map((_, i) => (
                    <Card key={i} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-1/2" />
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-2/3" />
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Skeleton className="h-4 w-1/4" />
                        <Skeleton className="h-8 w-1/4" />
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : projects.length > 0 ? (
                <AnimatedGrid
                  items={projects}
                  keyExtractor={(project) => project.id}
                  gridCols="sm:grid-cols-2 lg:grid-cols-3"
                  renderItem={(project) => (
                    <AnimatedCard 
                      className="overflow-hidden flex flex-col h-full" 
                      animationVariant="hoverLift"
                      onClick={() => handleViewProject(project.id)}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="rounded-full bg-primary/10 p-1">
                              {projectTypeIcons[project.type] || <Leaf className="h-5 w-5" />}
                            </div>
                            <CardTitle className="line-clamp-1">{project.name}</CardTitle>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/projects/${project.id}/edit`);
                              }}>
                                Edit Project
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/projects/${project.id}/credits`);
                              }}>
                                View Credits
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        <CardDescription className="line-clamp-1">
                          {project.description || "No description provided"}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">Type</p>
                            <p>{project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Location</p>
                            <p>{project.country || "Not specified"}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Credits</p>
                            <p>{project._count?.carbonCredits || 0}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Created</p>
                            <p>{formatDate(project.createdAt)}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between pt-2">
                        <div className="flex gap-2">
                          <Badge variant="outline" className={statusVariants[project.status] || ""}>
                            {project.status}
                          </Badge>
                          <Badge variant="outline" className={verificationStatusVariants[project.verificationStatus] || ""}>
                            {project.verificationStatus}
                          </Badge>
                        </div>
                      </CardFooter>
                    </AnimatedCard>
                  )}
                />
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <Leaf className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No projects found</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    You don't have any projects yet. Create your first project to start generating carbon credits.
                  </p>
                  <AnimatedButton onClick={handleCreateProject} animationVariant="buttonTap">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Project
                  </AnimatedButton>
                </div>
              )}
              
              {!isLoading && projects.length > 0 && pagination.pages > 1 && (
                <div className="mt-6">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          href="#" 
                          onClick={(e) => {
                            e.preventDefault();
                            if (pagination.page > 1) {
                              handlePageChange(pagination.page - 1);
                            }
                          }}
                          className={pagination.page <= 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                      
                      {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink 
                            href="#" 
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(page);
                            }}
                            isActive={page === pagination.page}
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      
                      <PaginationItem>
                        <PaginationNext 
                          href="#" 
                          onClick={(e) => {
                            e.preventDefault();
                            if (pagination.page < pagination.pages) {
                              handlePageChange(pagination.page + 1);
                            }
                          }}
                          className={pagination.page >= pagination.pages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="active" className="mt-0">
              {/* Active projects will be filtered in the backend */}
              <div className="flex flex-col items-center justify-center py-12">
                <p className="text-muted-foreground">
                  Please use the status filter to view active projects
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="pending" className="mt-0">
              {/* Pending projects will be filtered in the backend */}
              <div className="flex flex-col items-center justify-center py-12">
                <p className="text-muted-foreground">
                  Please use the status filter to view pending projects
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="completed" className="mt-0">
              {/* Completed projects will be filtered in the backend */}
              <div className="flex flex-col items-center justify-center py-12">
                <p className="text-muted-foreground">
                  Please use the status filter to view completed projects
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
