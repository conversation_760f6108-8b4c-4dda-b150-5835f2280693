"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PageTransition } from "@/components/ui/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import ProjectCreationWizard from "@/components/projects/project-creation-wizard";

export default function CreateProjectPage() {
  const router = useRouter();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <Button variant="outline" onClick={() => router.push("/projects")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </div>

          <ProjectCreationWizard />
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
