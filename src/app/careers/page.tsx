"use client";

import { PageTransition } from "@/components/ui/animated";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { motion } from "framer-motion";
import { useState } from "react";

export default function CareersPage() {
  const [activeJobCategory, setActiveJobCategory] = useState("all");

  // Job listings
  const jobs = [
    {
      id: 1,
      title: "Frontend Developer",
      department: "Engineering",
      location: "San Francisco, CA (Hybrid)",
      type: "Full-time",
      description: "Join our frontend team to build exceptional user experiences using React, Next.js, and Tailwind CSS for our carbon trading platform.",
      requirements: [
        "3+ years of experience with React and modern JavaScript",
        "Experience with Next.js and Tailwind CSS",
        "Strong understanding of responsive design principles",
        "Knowledge of accessibility best practices",
        "Experience with state management solutions"
      ]
    },
    {
      id: 2,
      title: "Blockchain Engineer",
      department: "Engineering",
      location: "Remote",
      type: "Full-time",
      description: "Help us build and optimize our blockchain infrastructure for secure and transparent carbon credit trading.",
      requirements: [
        "3+ years of experience with blockchain technologies",
        "Experience with Ethereum, smart contracts, and web3.js",
        "Understanding of consensus mechanisms and decentralized applications",
        "Strong background in cryptography and security",
        "Experience with Solidity programming"
      ]
    },
    {
      id: 3,
      title: "Carbon Markets Specialist",
      department: "Business",
      location: "San Francisco, CA",
      type: "Full-time",
      description: "Lead our efforts in carbon market analysis, helping shape our platform's features to meet the evolving needs of the carbon credit market.",
      requirements: [
        "5+ years of experience in carbon markets or environmental commodities",
        "Deep understanding of carbon credit verification and certification processes",
        "Experience with carbon offset projects and methodologies",
        "Strong analytical skills and attention to detail",
        "Excellent communication and presentation abilities"
      ]
    },
    {
      id: 4,
      title: "Product Manager",
      department: "Product",
      location: "San Francisco, CA (Hybrid)",
      type: "Full-time",
      description: "Drive product strategy and development for our carbon credit trading platform, working closely with engineering, design, and business teams.",
      requirements: [
        "4+ years of product management experience",
        "Experience with financial or marketplace products",
        "Strong understanding of user-centered design principles",
        "Excellent communication and stakeholder management skills",
        "Data-driven approach to decision making"
      ]
    },
    {
      id: 5,
      title: "UX/UI Designer",
      department: "Design",
      location: "Remote",
      type: "Full-time",
      description: "Create intuitive and visually appealing designs that make carbon credit trading accessible and effective for our users.",
      requirements: [
        "3+ years of experience in UX/UI design",
        "Proficiency with design tools like Figma or Sketch",
        "Experience designing complex financial or marketplace interfaces",
        "Understanding of accessibility standards",
        "Strong portfolio demonstrating user-centered design process"
      ]
    },
    {
      id: 6,
      title: "Marketing Specialist",
      department: "Marketing",
      location: "San Francisco, CA",
      type: "Full-time",
      description: "Help us build awareness and drive adoption of our platform among enterprises engaged in carbon credit trading.",
      requirements: [
        "3+ years of B2B marketing experience",
        "Experience in fintech, sustainability, or environmental sectors",
        "Strong content creation and campaign management skills",
        "Data-driven approach to marketing strategy",
        "Excellent communication and collaboration abilities"
      ]
    }
  ];

  // Filter jobs based on active category
  const filteredJobs = activeJobCategory === "all" 
    ? jobs 
    : jobs.filter(job => job.department.toLowerCase() === activeJobCategory.toLowerCase());

  // Job categories derived from job listings
  const categories = ["All", ...new Set(jobs.map(job => job.department))];

  return (
    <PageTransition animationVariant="fadeIn">
      <div className="bg-white">
        <div className="relative overflow-hidden bg-gradient-to-b from-green-50 to-blue-50 pt-16 pb-24">
          <div className="relative">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:mx-0">
                <motion.h1
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  Join Our Team
                </motion.h1>
                <motion.p
                  className="mt-6 text-lg leading-8 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  Help us build the future of carbon credit trading and make a positive impact on the environment.
                </motion.p>
              </div>
            </div>
          </div>
        </div>

        {/* Our Culture Section */}
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="mx-auto max-w-3xl text-center">
            <motion.h2 
              className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Our Culture
            </motion.h2>
            <motion.p 
              className="mt-6 text-lg leading-8 text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              At Carbonix, we're passionate about leveraging technology to drive environmental change. We foster a collaborative, innovative, and inclusive workplace where everyone can contribute to our mission.
            </motion.p>
          </div>
          
          <div className="mt-16 grid gap-8 md:grid-cols-3">
            <motion.div
              className="bg-gray-50 rounded-lg p-6 border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
            >
              <div className="text-4xl mb-4">💡</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Innovation</h3>
              <p className="text-gray-600">
                We encourage creative thinking and embrace new technologies to solve complex challenges in carbon markets.
              </p>
            </motion.div>
            
            <motion.div
              className="bg-gray-50 rounded-lg p-6 border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
            >
              <div className="text-4xl mb-4">🌎</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Impact</h3>
              <p className="text-gray-600">
                We're driven by our mission to accelerate the transition to a low-carbon economy through effective market mechanisms.
              </p>
            </motion.div>
            
            <motion.div
              className="bg-gray-50 rounded-lg p-6 border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
            >
              <div className="text-4xl mb-4">🤝</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Collaboration</h3>
              <p className="text-gray-600">
                We work together across disciplines, backgrounds, and perspectives to build the best solutions for our users.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="bg-green-50 py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="mx-auto max-w-3xl text-center">
              <motion.h2 
                className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                Why Join Us
              </motion.h2>
              <motion.p 
                className="mt-6 text-lg leading-8 text-gray-600"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                We offer competitive compensation and a comprehensive benefits package designed to support your wellbeing and growth.
              </motion.p>
            </div>
            
            <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <motion.div
                className="bg-white rounded-lg p-6 border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="text-3xl mb-4">💼</div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Competitive Salary</h3>
                <p className="text-gray-600">
                  We offer competitive compensation packages aligned with industry standards.
                </p>
              </motion.div>
              
              <motion.div
                className="bg-white rounded-lg p-6 border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className="text-3xl mb-4">🌴</div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Flexible PTO</h3>
                <p className="text-gray-600">
                  We trust you to manage your time off to rest, recharge, and return inspired.
                </p>
              </motion.div>
              
              <motion.div
                className="bg-white rounded-lg p-6 border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <div className="text-3xl mb-4">🏥</div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Health Benefits</h3>
                <p className="text-gray-600">
                  Comprehensive health, dental, and vision insurance for you and your dependents.
                </p>
              </motion.div>
              
              <motion.div
                className="bg-white rounded-lg p-6 border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <div className="text-3xl mb-4">🎓</div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Learning Stipend</h3>
                <p className="text-gray-600">
                  Annual budget for courses, conferences, and resources to support your growth.
                </p>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Open Positions Section */}
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="mx-auto max-w-3xl text-center">
            <motion.h2 
              className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Open Positions
            </motion.h2>
            <motion.p 
              className="mt-6 text-lg leading-8 text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Join our team and help build the future of carbon credit trading.
            </motion.p>
          </div>
          
          {/* Job Category Filters */}
          <div className="mt-10 flex justify-center flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={activeJobCategory === category.toLowerCase() ? "default" : "outline"}
                className="mb-2"
                onClick={() => setActiveJobCategory(category.toLowerCase())}
              >
                {category}
              </Button>
            ))}
          </div>
          
          {/* Job Listings */}
          <div className="mt-10 grid gap-6 md:grid-cols-2">
            {filteredJobs.map((job) => (
              <motion.div
                key={job.id}
                className="bg-gray-50 rounded-lg p-6 border border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
              >
                <span className="inline-block bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded mb-2">
                  {job.department}
                </span>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{job.title}</h3>
                <div className="flex items-center text-gray-500 mb-4">
                  <span className="inline-flex items-center mr-4">
                    <svg className="mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {job.location}
                  </span>
                  <span className="inline-flex items-center">
                    <svg className="mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {job.type}
                  </span>
                </div>
                <p className="text-gray-600 mb-4">{job.description}</p>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Requirements:</h4>
                  <ul className="list-disc pl-6 space-y-1 text-gray-600 mb-6">
                    {job.requirements.map((req, index) => (
                      <li key={index}>{req}</li>
                    ))}
                  </ul>
                </div>
                <motion.div
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                >
                  <Button className="w-full">Apply Now</Button>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-green-600 py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 text-center">
            <motion.h2
              className="text-3xl font-bold tracking-tight text-white sm:text-4xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Don't See the Right Role?
            </motion.h2>
            <motion.p
              className="max-w-2xl mx-auto text-lg text-green-100 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              We're always looking for talented individuals who are passionate about our mission. Send us your resume, and we'll keep you in mind for future opportunities.
            </motion.p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/contact">
                <Button variant="outline" className="bg-white text-green-600 hover:bg-green-50 border-white">
                  Contact Our Recruiting Team
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}