import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { NextAuthProvider } from "@/components/providers/next-auth-provider";
import { ToastProvider } from "@/components/providers/toast-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Carbonix - Enterprise Carbon Credit Trading Platform",
  description: "Accelerate sustainability goals with secure, transparent carbon credit trading. Streamline compliance and unlock new value from your carbon strategy.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} min-h-screen w-full bg-gradient-to-b from-green-50 to-blue-50`}>
        <NextAuthProvider>
          {children}
          <ToastProvider />
        </NextAuthProvider>
      </body>
    </html>
  );
}
