"use client";

import { ReactNode, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import { NotificationCenter } from "@/components/notifications/notification-center";
import { Button } from "@/components/ui/button";
import { UserAccountNav } from "@/components/auth/user-account-nav";
import { ClientAdminGuard } from "@/components/auth/client-auth-guard";
import { 
  LayoutDashboard, 
  Users, 
  Building2, 
  CreditCard, 
  Settings, 
  Bell, 
  FileCheck, 
  BarChart3, 
  Menu, 
  PanelLeft,
  Folder
} from "lucide-react";
import { motion } from "framer-motion";
import { PageTransition } from "@/components/ui/animated";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const { data: session } = useSession();
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [isHovering, setIsHovering] = useState(false);

  const navigation = [
    { name: "Dashboard", href: "/admin/dashboard", icon: LayoutDashboard },
    { name: "Users", href: "/admin/users", icon: Users },
    { name: "Organizations", href: "/admin/organizations", icon: Building2 },
    { name: "Projects", href: "/admin/projects", icon: Folder },
    { name: "Verification", href: "/admin/verification", icon: FileCheck },
    { name: "Settings", href: "/admin/settings", icon: Settings },
  ];

  return (
    <ClientAdminGuard>
      <div className="flex h-screen">
        {/* Sidebar */}
        <motion.div
          className="hidden md:flex md:flex-shrink-0"
          animate={{ width: sidebarExpanded || isHovering ? 'auto' : '4rem' }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          onHoverStart={() => !sidebarExpanded && setIsHovering(true)}
          onHoverEnd={() => !sidebarExpanded && setIsHovering(false)}
        >
          <div className={cn(
            "flex flex-col",
            sidebarExpanded || isHovering ? "w-64" : "w-16"
          )}>
            <div className="flex flex-col h-0 flex-1 border-r border-gray-200 bg-white overflow-hidden">
              <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
                <motion.div
                  className="flex items-center flex-shrink-0 px-4"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div
                    className="mr-3 h-6 w-6 flex items-center justify-center cursor-pointer"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSidebarExpanded(!sidebarExpanded)}
                  >
                    <PanelLeft className="h-5 w-5" />
                  </motion.div>
                  <motion.div
                    className="whitespace-nowrap"
                    initial={{ opacity: 0 }}
                    animate={{
                      opacity: sidebarExpanded || isHovering ? 1 : 0,
                      width: sidebarExpanded || isHovering ? 'auto' : 0
                    }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                  >
                    <h1 className="text-xl font-bold">Admin Panel</h1>
                  </motion.div>
                </motion.div>
                <motion.nav
                  className="mt-5 flex-1 px-2 bg-white space-y-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                >
                  {navigation.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index, duration: 0.3 }}
                    >
                      <Link
                        href={item.href}
                        className={cn(
                          pathname === item.href
                            ? "bg-gray-100 text-gray-900"
                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                          "group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                        )}
                      >
                        <motion.div
                          className="mr-3 h-6 w-6 flex items-center justify-center"
                          whileHover={{ scale: 1.1, rotate: pathname === item.href ? 0 : 5 }}
                          whileTap={{ scale: 0.95 }}
                          animate={pathname === item.href ? { scale: 1.05 } : { scale: 1 }}
                        >
                          {item.icon && (
                            <item.icon
                              className={cn(
                                "h-5 w-5",
                                pathname === item.href && "text-primary"
                              )}
                            />
                          )}
                        </motion.div>
                        <motion.span
                          whileHover={{ x: pathname === item.href ? 0 : 3 }}
                          transition={{ type: "spring", stiffness: 400 }}
                          animate={{
                            opacity: sidebarExpanded || isHovering ? 1 : 0,
                            width: sidebarExpanded || isHovering ? 'auto' : 0,
                            marginLeft: sidebarExpanded || isHovering ? '0.75rem' : 0,
                            display: sidebarExpanded || isHovering ? 'inline' : 'none'
                          }}
                        >
                          {item.name}
                        </motion.span>
                      </Link>
                    </motion.div>
                  ))}
                </motion.nav>
              </div>
              <motion.div
                className="flex-shrink-0 flex border-t border-gray-200 p-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.3 }}
              >
                <div className="flex items-center space-x-2 w-full">
                  {session?.user && (
                    <motion.div
                      className="flex-shrink-0 cursor-pointer"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <UserAccountNav user={session.user} />
                    </motion.div>
                  )}
                  {sidebarExpanded || isHovering ? (
                    <motion.div
                      animate={{
                        opacity: sidebarExpanded || isHovering ? 1 : 0,
                        width: sidebarExpanded || isHovering ? 'auto' : 0,
                      }}
                      className="flex-1 ml-2"
                      initial={{ opacity: 0 }}
                      transition={{ delay: 0.3, duration: 0.2 }}
                    >
                      <span className="text-sm font-medium text-gray-700">
                        {session?.user?.name || session?.user?.email || "Admin"}
                      </span>
                    </motion.div>
                  ) : null}
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Main content */}
        <div className="flex flex-col w-0 flex-1 overflow-hidden">
          {/* Header - transparent with only notification */}
          <div className="relative z-10 flex-shrink-0 flex h-16 bg-transparent">
            <div className="flex-1 px-4 flex justify-between items-center">
              <motion.div
                className="md:hidden"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarExpanded(!sidebarExpanded)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </motion.div>
              <div className="ml-auto flex items-center">
                <NotificationCenter />
              </div>
            </div>
          </div>

          <main className="flex-1 relative z-0 overflow-y-auto focus:outline-none p-4 -mt-10">
            <PageTransition>
              {children}
            </PageTransition>
          </main>
        </div>
      </div>
    </ClientAdminGuard>
  );
}
