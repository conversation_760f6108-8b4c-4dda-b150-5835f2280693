import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import UserManagementClient from "./user-management-client";

export const metadata: Metadata = {
  title: "User Management | Admin",
  description: "Manage user accounts, roles, and permissions",
};

export default async function UserManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <PageHeaderWithBreadcrumb
        title="User Management"
        description="Manage user accounts, roles, and permissions"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Users", href: "/admin/users" },
        ]}
      />
      <UserManagementClient />
    </div>
  );
}
