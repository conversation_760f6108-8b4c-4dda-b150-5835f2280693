import { Metadata } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { AuditTrailViewer } from "@/components/compliance/audit-trail-viewer";
import { 
  PageHeader, 
  PageHeaderDescription, 
  PageHeaderHeading 
} from "@/components/page-header";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Shield, FileText, AlertCircle } from "lucide-react";

export const metadata: Metadata = {
  title: "Compliance Audit Trail | Admin",
  description: "View and manage compliance audit logs",
};

export default async function AdminComplianceAuditTrailPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/compliance">Compliance</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Audit Trail</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <PageHeader>
        <PageHeaderHeading>Compliance Audit Trail</PageHeaderHeading>
        <PageHeaderDescription>
          View and manage detailed audit logs for compliance-related activities
        </PageHeaderDescription>
      </PageHeader>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all" className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            All Logs
          </TabsTrigger>
          <TabsTrigger value="kyc" className="flex items-center">
            <Shield className="mr-2 h-4 w-4" />
            KYC Logs
          </TabsTrigger>
          <TabsTrigger value="aml" className="flex items-center">
            <AlertCircle className="mr-2 h-4 w-4" />
            AML Logs
          </TabsTrigger>
          <TabsTrigger value="tax" className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            Tax Reports
          </TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4 pt-4">
          <AuditTrailViewer isAdmin={true} />
        </TabsContent>
        <TabsContent value="kyc" className="space-y-4 pt-4">
          <AuditTrailViewer isAdmin={true} />
        </TabsContent>
        <TabsContent value="aml" className="space-y-4 pt-4">
          <AuditTrailViewer isAdmin={true} />
        </TabsContent>
        <TabsContent value="tax" className="space-y-4 pt-4">
          <AuditTrailViewer isAdmin={true} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
