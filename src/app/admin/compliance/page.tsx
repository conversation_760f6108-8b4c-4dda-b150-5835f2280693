import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading
} from "@/components/page-header";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AuditTrailViewer } from "@/components/compliance/audit-trail-viewer";
import {
  Shield,
  FileText,
  AlertCircle,
  // Users, // Unused import
  // Building, // Unused import
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ArrowR<PERSON>
} from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Compliance Administration",
  description: "Manage compliance activities and view audit trails",
};

export default async function ComplianceAdminPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  // Get compliance statistics
  const kycStats = await getKycStats();
  const amlStats = await getAmlStats();
  const auditStats = await getAuditStats();

  return (
    <div className="container mx-auto py-6 space-y-8">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Compliance</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <PageHeader>
        <PageHeaderHeading>Compliance Administration</PageHeaderHeading>
        <PageHeaderDescription>
          Manage compliance activities, review KYC/AML requests, and view audit trails
        </PageHeaderDescription>
      </PageHeader>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">KYC Verifications</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kycStats.total}</div>
            <p className="text-xs text-muted-foreground">
              Total KYC verification requests
            </p>
            <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <CheckCircle className="h-4 w-4 text-green-500 mb-1" />
                <span className="font-medium">{kycStats.approved}</span>
                <span className="text-muted-foreground">Approved</span>
              </div>
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <Clock className="h-4 w-4 text-amber-500 mb-1" />
                <span className="font-medium">{kycStats.pending}</span>
                <span className="text-muted-foreground">Pending</span>
              </div>
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <XCircle className="h-4 w-4 text-red-500 mb-1" />
                <span className="font-medium">{kycStats.rejected}</span>
                <span className="text-muted-foreground">Rejected</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full" asChild>
              <Link href="/admin/compliance/kyc">
                <span className="flex items-center justify-center">
                  View KYC Requests
                  <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AML Checks</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{amlStats.total}</div>
            <p className="text-xs text-muted-foreground">
              Total AML checks performed
            </p>
            <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <CheckCircle className="h-4 w-4 text-green-500 mb-1" />
                <span className="font-medium">{amlStats.lowRisk}</span>
                <span className="text-muted-foreground">Low Risk</span>
              </div>
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <AlertTriangle className="h-4 w-4 text-amber-500 mb-1" />
                <span className="font-medium">{amlStats.mediumRisk}</span>
                <span className="text-muted-foreground">Medium Risk</span>
              </div>
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <XCircle className="h-4 w-4 text-red-500 mb-1" />
                <span className="font-medium">{amlStats.highRisk}</span>
                <span className="text-muted-foreground">High Risk</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full" asChild>
              <Link href="/admin/compliance/aml">
                <span className="flex items-center justify-center">
                  View AML Checks
                  <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Audit Logs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{auditStats.total}</div>
            <p className="text-xs text-muted-foreground">
              Total compliance audit logs
            </p>
            <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <Shield className="h-4 w-4 text-blue-500 mb-1" />
                <span className="font-medium">{auditStats.kyc}</span>
                <span className="text-muted-foreground">KYC Logs</span>
              </div>
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <AlertCircle className="h-4 w-4 text-purple-500 mb-1" />
                <span className="font-medium">{auditStats.aml}</span>
                <span className="text-muted-foreground">AML Logs</span>
              </div>
              <div className="flex flex-col items-center p-2 bg-muted rounded-md">
                <FileText className="h-4 w-4 text-green-500 mb-1" />
                <span className="font-medium">{auditStats.tax}</span>
                <span className="text-muted-foreground">Tax Reports</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full" asChild>
              <Link href="/admin/compliance/audit-trail">
                <span className="flex items-center justify-center">
                  View Audit Trail
                  <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <Tabs defaultValue="audit-trail">
        <TabsList>
          <TabsTrigger value="audit-trail" className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            Audit Trail
          </TabsTrigger>
          <TabsTrigger value="kyc-requests" className="flex items-center">
            <Shield className="mr-2 h-4 w-4" />
            KYC Requests
          </TabsTrigger>
          <TabsTrigger value="aml-checks" className="flex items-center">
            <AlertCircle className="mr-2 h-4 w-4" />
            AML Checks
          </TabsTrigger>
        </TabsList>
        <TabsContent value="audit-trail" className="space-y-4 pt-4">
          <AuditTrailViewer isAdmin={true} />
        </TabsContent>
        <TabsContent value="kyc-requests" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>KYC Verification Requests</CardTitle>
              <CardDescription>
                Review and approve KYC verification requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This tab will display KYC verification requests that need review.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" asChild>
                <Link href="/admin/compliance/kyc">
                  <span className="flex items-center">
                    View All KYC Requests
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </span>
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="aml-checks" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>AML Checks</CardTitle>
              <CardDescription>
                Review AML check results and manage high-risk cases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This tab will display AML checks that need review, especially high-risk cases.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" asChild>
                <Link href="/admin/compliance/aml">
                  <span className="flex items-center">
                    View All AML Checks
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </span>
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Get KYC statistics
async function getKycStats() {
  try {
    const total = await db.kycVerification.count();
    const approved = await db.kycVerification.count({
      where: { status: "APPROVED" },
    });
    const pending = await db.kycVerification.count({
      where: {
        OR: [
          { status: "PENDING" },
          { status: "IN_REVIEW" },
        ]
      },
    });
    const rejected = await db.kycVerification.count({
      where: { status: "REJECTED" },
    });

    return {
      total,
      approved,
      pending,
      rejected,
    };
  } catch (error) {
    console.error("Error getting KYC stats:", error);
    return {
      total: 0,
      approved: 0,
      pending: 0,
      rejected: 0,
    };
  }
}

// Get AML statistics
async function getAmlStats() {
  try {
    const total = await db.amlCheck.count();
    const lowRisk = await db.amlCheck.count({
      where: { riskLevel: "LOW" },
    });
    const mediumRisk = await db.amlCheck.count({
      where: { riskLevel: "MEDIUM" },
    });
    const highRisk = await db.amlCheck.count({
      where: {
        OR: [
          { riskLevel: "HIGH" },
          { riskLevel: "CRITICAL" },
        ]
      },
    });

    return {
      total,
      lowRisk,
      mediumRisk,
      highRisk,
    };
  } catch (error) {
    console.error("Error getting AML stats:", error);
    return {
      total: 0,
      lowRisk: 0,
      mediumRisk: 0,
      highRisk: 0,
    };
  }
}

// Get audit statistics
async function getAuditStats() {
  try {
    const total = await db.auditLog.count({
      where: {
        OR: [
          { type: "KYC_VERIFICATION_REQUESTED" },
          { type: "KYC_VERIFICATION_APPROVED" },
          { type: "KYC_VERIFICATION_REJECTED" },
          { type: "AML_CHECK_PERFORMED" },
          { type: "DOCUMENT_VERIFIED" },
          { type: "TAX_REPORT_GENERATED" },
        ],
      },
    });

    const kyc = await db.auditLog.count({
      where: {
        OR: [
          { type: "KYC_VERIFICATION_REQUESTED" },
          { type: "KYC_VERIFICATION_APPROVED" },
          { type: "KYC_VERIFICATION_REJECTED" },
          { type: "DOCUMENT_VERIFIED" },
        ],
      },
    });

    const aml = await db.auditLog.count({
      where: { type: "AML_CHECK_PERFORMED" },
    });

    const tax = await db.auditLog.count({
      where: { type: "TAX_REPORT_GENERATED" },
    });

    return {
      total,
      kyc,
      aml,
      tax,
    };
  } catch (error) {
    console.error("Error getting audit stats:", error);
    return {
      total: 0,
      kyc: 0,
      aml: 0,
      tax: 0,
    };
  }
}
