"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  AlertCircle, 
  Building2, 
  CheckCircle2, 
  XCircle, 
  Eye, 
  FileText, 
  Download, 
  ExternalLink, 
  Loader2 
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { format } from "date-fns";

interface Organization {
  id: string;
  name: string;
  legalName: string;
  status: string;
  verificationStatus: string;
  country: string;
  industry: string;
  createdAt: string;
  documents: Document[];
  _count: {
    users: number;
  };
}

interface Document {
  id: string;
  name: string;
  type: string;
  url: string;
  status: string;
  createdAt: string;
}

export default function OrganizationApprovalPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("pending");
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionDialog, setShowRejectionDialog] = useState(false);

  // Fetch organizations
  const fetchOrganizations = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/admin/organizations?status=${activeTab === "pending" ? "PENDING" : activeTab === "approved" ? "ACTIVE" : "REJECTED"}&verificationStatus=${activeTab === "pending" ? "PENDING" : activeTab === "approved" ? "VERIFIED" : "REJECTED"}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch organizations");
      }

      const data = await response.json();
      setOrganizations(data.organizations);
    } catch (error) {
      console.error("Error fetching organizations:", error);
      toast({
        title: "Error",
        description: "Failed to load organizations",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch organization details
  const fetchOrganizationDetails = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/organizations/${id}`);

      if (!response.ok) {
        throw new Error("Failed to fetch organization details");
      }

      const data = await response.json();
      setSelectedOrg(data.organization);
    } catch (error) {
      console.error("Error fetching organization details:", error);
      toast({
        title: "Error",
        description: "Failed to load organization details",
        variant: "destructive",
      });
    }
  };

  // Approve organization
  const approveOrganization = async (id: string) => {
    try {
      setIsApproving(true);
      const response = await fetch(`/api/admin/organizations/${id}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to approve organization");
      }

      toast({
        title: "Success",
        description: "Organization approved successfully",
      });

      // Refresh the list
      fetchOrganizations();
      setSelectedOrg(null);
    } catch (error) {
      console.error("Error approving organization:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to approve organization",
        variant: "destructive",
      });
    } finally {
      setIsApproving(false);
    }
  };

  // Reject organization
  const rejectOrganization = async (id: string, reason: string) => {
    try {
      setIsRejecting(true);
      const response = await fetch(`/api/admin/organizations/${id}/reject`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to reject organization");
      }

      toast({
        title: "Success",
        description: "Organization rejected successfully",
      });

      // Refresh the list
      fetchOrganizations();
      setSelectedOrg(null);
      setShowRejectionDialog(false);
      setRejectionReason("");
    } catch (error) {
      console.error("Error rejecting organization:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to reject organization",
        variant: "destructive",
      });
    } finally {
      setIsRejecting(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setSelectedOrg(null);
  };

  // Load organizations when tab changes
  useEffect(() => {
    if (session?.user) {
      fetchOrganizations();
    }
  }, [session, activeTab]);

  // Redirect if not admin
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else if (status === "authenticated" && session?.user?.role !== "ADMIN") {
      router.push("/dashboard");
      toast({
        title: "Access Denied",
        description: "You do not have permission to access this page",
        variant: "destructive",
      });
    }
  }, [status, session, router]);

  if (status === "loading" || (status === "authenticated" && session?.user?.role !== "ADMIN")) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Organization Approval</h1>
        <p className="text-muted-foreground">
          Review and approve organization verification requests
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Organizations</CardTitle>
              <CardDescription>
                Review organization verification requests
              </CardDescription>
              <Tabs defaultValue={activeTab} onValueChange={handleTabChange} className="mt-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                  <TabsTrigger value="approved">Approved</TabsTrigger>
                  <TabsTrigger value="rejected">Rejected</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-full bg-muted animate-pulse" />
                      <div className="space-y-2">
                        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                        <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : organizations.length === 0 ? (
                <div className="text-center py-8">
                  <Building2 className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-medium">No organizations</h3>
                  <p className="text-sm text-muted-foreground">
                    {activeTab === "pending"
                      ? "There are no pending organization verification requests"
                      : activeTab === "approved"
                      ? "There are no approved organizations"
                      : "There are no rejected organizations"}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {organizations.map((org) => (
                    <div
                      key={org.id}
                      className={`flex items-center space-x-4 p-3 rounded-md cursor-pointer hover:bg-muted transition-colors ${
                        selectedOrg?.id === org.id ? "bg-muted" : ""
                      }`}
                      onClick={() => fetchOrganizationDetails(org.id)}
                    >
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                        <Building2 className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{org.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {org.country} • {format(new Date(org.createdAt), "MMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          {selectedOrg ? (
            <Card>
              <CardHeader className="flex flex-row items-start justify-between">
                <div>
                  <CardTitle>{selectedOrg.name}</CardTitle>
                  <CardDescription>
                    {selectedOrg.legalName} • {selectedOrg.country}
                  </CardDescription>
                </div>
                <Badge
                  variant={
                    selectedOrg.verificationStatus === "VERIFIED"
                      ? "secondary"
                      : selectedOrg.verificationStatus === "PENDING"
                      ? "outline"
                      : "destructive"
                  }
                >
                  {selectedOrg.verificationStatus}
                </Badge>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Industry</h3>
                    <p>{selectedOrg.industry}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Users</h3>
                    <p>{selectedOrg._count.users}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                    <p>{selectedOrg.status}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Created</h3>
                    <p>{format(new Date(selectedOrg.createdAt), "MMM d, yyyy")}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Verification Documents</h3>
                  {selectedOrg.documents.length === 0 ? (
                    <p className="text-muted-foreground">No documents uploaded</p>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Document</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {selectedOrg.documents.map((doc) => (
                          <TableRow key={doc.id}>
                            <TableCell>{doc.name}</TableCell>
                            <TableCell>{doc.type.replace(/_/g, " ")}</TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  doc.status === "APPROVED"
                                    ? "secondary"
                                    : doc.status === "PENDING"
                                    ? "outline"
                                    : "destructive"
                                }
                              >
                                {doc.status}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <a
                                href={doc.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-blue-500 hover:text-blue-700"
                              >
                                <ExternalLink className="h-4 w-4 mr-1" />
                                View
                              </a>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => router.push(`/admin/organizations/${selectedOrg.id}`)}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Button>
                <div className="space-x-2">
                  {selectedOrg.verificationStatus === "PENDING" && (
                    <>
                      <Button
                        variant="destructive"
                        onClick={() => setShowRejectionDialog(true)}
                        disabled={isRejecting || isApproving}
                      >
                        {isRejecting ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <XCircle className="mr-2 h-4 w-4" />
                        )}
                        Reject
                      </Button>
                      <Button
                        onClick={() => approveOrganization(selectedOrg.id)}
                        disabled={isApproving || isRejecting}
                      >
                        {isApproving ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                        )}
                        Approve
                      </Button>
                    </>
                  )}
                </div>
              </CardFooter>
            </Card>
          ) : (
            <Card className="h-full flex items-center justify-center">
              <CardContent className="text-center py-10">
                <Building2 className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">Select an organization</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Select an organization from the list to view details and manage verification
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Rejection Dialog */}
      <Dialog open={showRejectionDialog} onOpenChange={setShowRejectionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Organization</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this organization. This will be sent to the organization.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Reason for rejection"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRejectionDialog(false)}
              disabled={isRejecting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedOrg && rejectOrganization(selectedOrg.id, rejectionReason)}
              disabled={isRejecting || !rejectionReason.trim()}
            >
              {isRejecting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <XCircle className="mr-2 h-4 w-4" />
              )}
              Reject Organization
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
