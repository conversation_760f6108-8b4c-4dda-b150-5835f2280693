"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { 
  AlertCircle, 
  CheckCircle2, 
  XCircle, 
  FileText, 
  Building2, 
  ArrowLeft,
  ExternalLink,
  Clock,
  AlertTriangle,
  FileCheck,
  Download,
  Eye
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/dialog";

export default function OrganizationVerificationPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [organization, setOrganization] = useState<any>(null);
  const [documents, setDocuments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);

  useEffect(() => {
    if (session?.user?.role !== "ADMIN") {
      setError("You do not have permission to access this page");
      setIsLoading(false);
      return;
    }

    fetchOrganizationDetails();
  }, [session, params.id]);

  const fetchOrganizationDetails = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/organizations/${params.id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch organization details");
      }

      setOrganization(data.organization);
      setDocuments(data.documents || []);
    } catch (error) {
      console.error("Error fetching organization details:", error);
      setError("Failed to load organization details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationUpdate = async (status: string) => {
    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/admin/organizations/${params.id}/verification`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          verificationStatus: status,
          notes,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update verification status");
      }

      toast({
        title: "Verification Updated",
        description: `Organization verification status updated to ${status}`,
      });

      // Refresh the organization details
      fetchOrganizationDetails();
    } catch (error) {
      console.error("Error updating verification status:", error);
      toast({
        title: "Error",
        description: "Failed to update verification status",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getVerificationStatusBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            {status}
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {status}
          </Badge>
        );
      case "IN_REVIEW":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            {status}
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            {status}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            {status}
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {status}
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            {status}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "BUSINESS_REGISTRATION":
        return <Building2 className="h-4 w-4" />;
      case "TAX_CERTIFICATE":
        return <FileCheck className="h-4 w-4" />;
      case "IDENTITY_PROOF":
        return <FileText className="h-4 w-4" />;
      case "ADDRESS_PROOF":
        return <FileText className="h-4 w-4" />;
      case "BANK_STATEMENT":
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleDocumentStatusUpdate = async (documentId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/documents/${documentId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          notes,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update document status");
      }

      toast({
        title: "Document Status Updated",
        description: `Document status updated to ${status}`,
      });

      // Refresh the organization details
      fetchOrganizationDetails();
    } catch (error) {
      console.error("Error updating document status:", error);
      toast({
        title: "Error",
        description: "Failed to update document status",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex items-center mb-6">
          <Skeleton className="h-10 w-10 rounded-full mr-2" />
          <div>
            <Skeleton className="h-6 w-[200px] mb-1" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-[300px] rounded-md" />
          <Skeleton className="h-[300px] rounded-md" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
          <div className="mb-4 rounded-full bg-destructive/10 p-3">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="mb-1 text-lg font-medium">Access Denied</h3>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            {error}
          </p>
          <Button onClick={() => router.push("/dashboard")}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
          <div className="mb-4 rounded-full bg-destructive/10 p-3">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="mb-1 text-lg font-medium">Organization Not Found</h3>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            The organization you are looking for does not exist or has been deleted.
          </p>
          <Button onClick={() => router.push("/admin/organizations")}>
            Go to Organizations
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <Button 
        variant="outline" 
        className="mb-6" 
        onClick={() => router.push("/admin/organizations")}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Organizations
      </Button>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">{organization.name}</h1>
          <div className="flex items-center mt-2">
            <p className="text-muted-foreground mr-2">Verification Status:</p>
            {getVerificationStatusBadge(organization.verificationStatus)}
          </div>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline"
            onClick={() => router.push(`/admin/organizations/${params.id}`)}
          >
            View Organization
          </Button>
          {organization.website && (
            <Button 
              variant="outline"
              onClick={() => window.open(organization.website, "_blank")}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Visit Website
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="documents">
        <TabsList className="mb-6">
          <TabsTrigger value="documents">Verification Documents</TabsTrigger>
          <TabsTrigger value="organization">Organization Details</TabsTrigger>
        </TabsList>

        <TabsContent value="documents">
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Verification Documents</CardTitle>
                  <CardDescription>
                    Review the documents submitted by the organization
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {documents.length > 0 ? (
                    <div className="space-y-4">
                      {documents.map((doc) => (
                        <div 
                          key={doc.id} 
                          className="flex items-center justify-between p-3 rounded-md border hover:bg-muted cursor-pointer"
                          onClick={() => setSelectedDocument(doc)}
                        >
                          <div className="flex items-center space-x-3">
                            {getDocumentTypeIcon(doc.type)}
                            <div>
                              <p className="font-medium">{doc.name}</p>
                              <p className="text-xs text-muted-foreground">{doc.type.replace(/_/g, " ")}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getDocumentStatusBadge(doc.status)}
                            <Button variant="ghost" size="icon">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
                      <div className="mb-4 rounded-full bg-primary/10 p-3">
                        <FileText className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="mb-1 text-lg font-medium">No Documents</h3>
                      <p className="mb-4 text-center text-sm text-muted-foreground">
                        This organization has not submitted any verification documents yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Verification Decision</CardTitle>
                  <CardDescription>
                    Approve or reject the organization's verification request
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium mb-2">Notes (optional)</p>
                      <Textarea
                        placeholder="Add notes about the verification decision..."
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        rows={4}
                      />
                    </div>
                    <div className="flex flex-col space-y-2">
                      <p className="text-sm font-medium">Current Status</p>
                      <div className="flex items-center space-x-2">
                        {getVerificationStatusBadge(organization.verificationStatus)}
                        <span className="text-sm text-muted-foreground">
                          Last updated: {new Date(organization.updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="destructive"
                    onClick={() => handleVerificationUpdate("REJECTED")}
                    disabled={isSubmitting}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject Verification
                  </Button>
                  <Button
                    onClick={() => handleVerificationUpdate("VERIFIED")}
                    disabled={isSubmitting}
                  >
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Approve Verification
                  </Button>
                </CardFooter>
              </Card>

              {organization.verificationStatus === "VERIFIED" && (
                <Card className="mt-6 bg-green-50 dark:bg-green-950">
                  <CardHeader>
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-green-600 dark:text-green-400" />
                      <CardTitle>Verification Approved</CardTitle>
                    </div>
                    <CardDescription>
                      This organization has been verified
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">
                      The organization has been verified and can now access all platform features.
                    </p>
                  </CardContent>
                </Card>
              )}

              {organization.verificationStatus === "REJECTED" && (
                <Card className="mt-6 bg-red-50 dark:bg-red-950">
                  <CardHeader>
                    <div className="flex items-center">
                      <XCircle className="mr-2 h-5 w-5 text-red-600 dark:text-red-400" />
                      <CardTitle>Verification Rejected</CardTitle>
                    </div>
                    <CardDescription>
                      This organization's verification has been rejected
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">
                      The organization can submit new documents and request verification again.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="organization">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Organization Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Legal Name</p>
                      <p className="text-sm text-muted-foreground">{organization.legalName || "N/A"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Registration Number</p>
                      <p className="text-sm text-muted-foreground">{organization.registrationNumber || "N/A"}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Tax ID</p>
                      <p className="text-sm text-muted-foreground">{organization.taxId || "N/A"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Founded Year</p>
                      <p className="text-sm text-muted-foreground">{organization.foundedYear || "N/A"}</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Industry</p>
                    <p className="text-sm text-muted-foreground">{organization.industry || "N/A"}</p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Size</p>
                    <p className="text-sm text-muted-foreground">{organization.size || "N/A"}</p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Website</p>
                    {organization.website ? (
                      <a 
                        href={organization.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline flex items-center"
                      >
                        {organization.website}
                        <ExternalLink className="ml-1 h-3 w-3" />
                      </a>
                    ) : (
                      <p className="text-sm text-muted-foreground">N/A</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium">Address</p>
                    <p className="text-sm text-muted-foreground">
                      {organization.address ? (
                        <>
                          {organization.address}
                          {organization.city && `, ${organization.city}`}
                          {organization.state && `, ${organization.state}`}
                          {organization.postalCode && ` ${organization.postalCode}`}
                        </>
                      ) : (
                        "N/A"
                      )}
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Country</p>
                    <p className="text-sm text-muted-foreground">{organization.country || "N/A"}</p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Phone Number</p>
                    <p className="text-sm text-muted-foreground">{organization.phoneNumber || "N/A"}</p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Primary Contact</p>
                    <p className="text-sm text-muted-foreground">{organization.primaryContact || "N/A"}</p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Primary Contact Email</p>
                    {organization.primaryContactEmail ? (
                      <a 
                        href={`mailto:${organization.primaryContactEmail}`}
                        className="text-sm text-blue-600 hover:underline"
                      >
                        {organization.primaryContactEmail}
                      </a>
                    ) : (
                      <p className="text-sm text-muted-foreground">N/A</p>
                    )}
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium">Primary Contact Phone</p>
                    <p className="text-sm text-muted-foreground">{organization.primaryContactPhone || "N/A"}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Document Viewer Dialog */}
      <Dialog open={!!selectedDocument} onOpenChange={(open) => !open && setSelectedDocument(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{selectedDocument?.name}</DialogTitle>
            <DialogDescription>
              {selectedDocument?.type.replace(/_/g, " ")} • {getDocumentStatusBadge(selectedDocument?.status)}
            </DialogDescription>
          </DialogHeader>

          <div className="mt-4 space-y-4">
            <div className="rounded-md border overflow-hidden">
              {selectedDocument?.url.endsWith('.pdf') ? (
                <div className="h-[400px] flex items-center justify-center bg-muted">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">PDF document preview not available</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => window.open(selectedDocument?.url, '_blank')}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View PDF
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="relative h-[400px] w-full">
                  <Image
                    src={selectedDocument?.url}
                    alt={selectedDocument?.name}
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium">Document Notes</p>
              <Textarea
                placeholder="Add notes about this document..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => window.open(selectedDocument?.url, '_blank')}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Original
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = selectedDocument?.url;
                  link.download = selectedDocument?.name;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="destructive"
                onClick={() => {
                  handleDocumentStatusUpdate(selectedDocument?.id, "REJECTED");
                  setSelectedDocument(null);
                }}
              >
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
              <Button
                onClick={() => {
                  handleDocumentStatusUpdate(selectedDocument?.id, "APPROVED");
                  setSelectedDocument(null);
                }}
              >
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Approve
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
