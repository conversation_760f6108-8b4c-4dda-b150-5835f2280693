import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import OrganizationManagementClient from "./organization-management-client";

export const metadata: Metadata = {
  title: "Organization Management | Admin",
  description: "Manage organizations, approvals, and settings",
};

export default async function OrganizationManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <PageHeaderWithBreadcrumb
        title="Organization Management"
        description="Manage organizations, approvals, and settings"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Organizations", href: "/admin/organizations" },
        ]}
      />
      <OrganizationManagementClient />
    </div>
  );
}
