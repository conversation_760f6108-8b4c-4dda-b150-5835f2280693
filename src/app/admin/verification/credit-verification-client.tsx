"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  MoreHorizontal, 
  Search, 
  Building2, 
  Folder, 
  FileCheck,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Filter,
  X,
  Check,
  AlertTriangle,
  Globe,
  MapPin,
  FileText,
  Leaf,
  BarChart,
  Eye,
  Clock,
  CreditCard
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardDescription, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  StaggeredList
} from "@/components/ui/animated";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function CreditVerificationClient() {
  const router = useRouter();
  const [verificationRequests, setVerificationRequests] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("IN_REVIEW");
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalPages: 1,
    totalItems: 0,
  });
  const [activeTab, setActiveTab] = useState("pending");

  useEffect(() => {
    fetchVerificationRequests();
  }, [pagination.page, statusFilter, searchQuery, activeTab]);

  const fetchVerificationRequests = async () => {
    try {
      setIsLoading(true);
      
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        status: statusFilter,
      });
      
      if (searchQuery) {
        params.append("search", searchQuery);
      }
      
      const response = await fetch(`/api/admin/verifications?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch verification requests");
      }

      setVerificationRequests(data.verificationRequests || []);
      setPagination({
        ...pagination,
        totalPages: data.totalPages || 1,
        totalItems: data.totalItems || 0,
      });
    } catch (error) {
      console.error("Error fetching verification requests:", error);
      toast({
        title: "Error",
        description: "Failed to load verification requests",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (requestId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/verifications/${requestId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update verification status");
      }

      toast({
        title: "Success",
        description: "Verification status updated successfully",
      });

      fetchVerificationRequests();
    } catch (error) {
      console.error("Error updating verification status:", error);
      toast({
        title: "Error",
        description: "Failed to update verification status",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Verified</Badge>;
      case "PENDING":
        return <Badge variant="secondary">Pending</Badge>;
      case "IN_REVIEW":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Review</Badge>;
      case "REJECTED":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const clearFilters = () => {
    setSearchQuery("");
    setPagination({
      ...pagination,
      page: 1,
    });
  };

  // Sample data for demonstration
  const sampleVerificationRequests = [
    {
      id: "ver_1",
      type: "CARBON_CREDIT",
      status: "IN_REVIEW",
      submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
      documentCount: 5,
      project: {
        id: "proj_1",
        name: "Solar Farm Initiative",
        projectType: "RENEWABLE_ENERGY"
      },
      organization: {
        id: "org_1",
        name: "SolarTech Innovations",
        logo: ""
      },
      carbonCredits: {
        amount: 15000,
        vintage: "2023",
        standard: "Verra"
      }
    },
    {
      id: "ver_2",
      type: "CARBON_CREDIT",
      status: "IN_REVIEW",
      submittedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
      documentCount: 7,
      project: {
        id: "proj_2",
        name: "Amazon Reforestation",
        projectType: "FORESTRY"
      },
      organization: {
        id: "org_2",
        name: "Green Earth Foundation",
        logo: ""
      },
      carbonCredits: {
        amount: 25000,
        vintage: "2023",
        standard: "Gold Standard"
      }
    },
    {
      id: "ver_3",
      type: "CARBON_CREDIT",
      status: "PENDING",
      submittedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
      documentCount: 3,
      project: {
        id: "proj_3",
        name: "Landfill Methane Capture",
        projectType: "METHANE_CAPTURE"
      },
      organization: {
        id: "org_3",
        name: "CleanAir Solutions",
        logo: ""
      },
      carbonCredits: {
        amount: 8000,
        vintage: "2023",
        standard: "American Carbon Registry"
      }
    },
    {
      id: "ver_4",
      type: "CARBON_CREDIT",
      status: "VERIFIED",
      submittedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      documentCount: 6,
      project: {
        id: "proj_4",
        name: "Industrial Energy Efficiency",
        projectType: "ENERGY_EFFICIENCY"
      },
      organization: {
        id: "org_4",
        name: "EcoIndustry GmbH",
        logo: ""
      },
      carbonCredits: {
        amount: 12000,
        vintage: "2022",
        standard: "Verra"
      }
    },
    {
      id: "ver_5",
      type: "CARBON_CREDIT",
      status: "REJECTED",
      submittedAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString(),
      documentCount: 4,
      project: {
        id: "proj_5",
        name: "Waste-to-Energy Plant",
        projectType: "WASTE_MANAGEMENT"
      },
      organization: {
        id: "org_5",
        name: "WasteTech Solutions",
        logo: ""
      },
      carbonCredits: {
        amount: 5000,
        vintage: "2022",
        standard: "Climate Action Reserve"
      }
    }
  ];

  // Use sample data if API is not yet implemented
  const displayRequests = verificationRequests.length > 0 ? verificationRequests : 
    sampleVerificationRequests.filter(req => {
      if (activeTab === "pending") return req.status === "IN_REVIEW" || req.status === "PENDING";
      if (activeTab === "verified") return req.status === "VERIFIED";
      if (activeTab === "rejected") return req.status === "REJECTED";
      return true;
    });

  const getVerificationStats = () => {
    const pending = sampleVerificationRequests.filter(req => req.status === "PENDING").length;
    const inReview = sampleVerificationRequests.filter(req => req.status === "IN_REVIEW").length;
    const verified = sampleVerificationRequests.filter(req => req.status === "VERIFIED").length;
    const rejected = sampleVerificationRequests.filter(req => req.status === "REJECTED").length;
    
    return { pending, inReview, verified, rejected, total: sampleVerificationRequests.length };
  };

  const stats = getVerificationStats();

  return (
    <div className="space-y-6">
      <StaggeredList className="grid gap-4 md:grid-cols-4">
        <AnimatedCard className={activeTab === "pending" ? "border-primary" : ""} onClick={() => setActiveTab("pending")}>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Pending Verification
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.pending + stats.inReview}</div>
              <Clock className="h-5 w-5 text-amber-500" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard className={activeTab === "verified" ? "border-primary" : ""} onClick={() => setActiveTab("verified")}>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Verified Credits
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.verified}</div>
              <Check className="h-5 w-5 text-green-500" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard className={activeTab === "rejected" ? "border-primary" : ""} onClick={() => setActiveTab("rejected")}>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Rejected Requests
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.rejected}</div>
              <X className="h-5 w-5 text-red-500" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard className={activeTab === "all" ? "border-primary" : ""} onClick={() => setActiveTab("all")}>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Total Requests
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.total}</div>
              <CreditCard className="h-5 w-5 text-blue-500" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>
      </StaggeredList>

      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search verification requests..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear
          </Button>
        </div>
        <div className="flex flex-row gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Statuses</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="IN_REVIEW">In Review</SelectItem>
              <SelectItem value="VERIFIED">Verified</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <AnimatedCard>
        <AnimatedCardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Project</TableHead>
                <TableHead>Organization</TableHead>
                <TableHead>Carbon Credits</TableHead>
                <TableHead>Standard</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Documents</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array(5).fill(0).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell colSpan={8} className="h-16 text-center">
                      <div className="flex justify-center">
                        <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : displayRequests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    No verification requests found.
                  </TableCell>
                </TableRow>
              ) : (
                displayRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <Folder className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{request.project.name}</div>
                          <div className="text-xs text-muted-foreground">{request.project.projectType}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>{request.organization.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Leaf className="h-4 w-4 text-green-500" />
                        <span>{request.carbonCredits.amount.toLocaleString()}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Badge variant="outline">{request.carbonCredits.standard}</Badge>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(request.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{new Date(request.submittedAt).toLocaleDateString()}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span>{request.documentCount}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => router.push(`/admin/verification/${request.id}`)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/admin/projects/${request.project.id}`)}>
                            <Folder className="mr-2 h-4 w-4" />
                            View Project
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                          {request.status !== "VERIFIED" && (
                            <DropdownMenuItem onClick={() => handleStatusChange(request.id, "VERIFIED")}>
                              <Check className="mr-2 h-4 w-4 text-green-500" />
                              Approve Verification
                            </DropdownMenuItem>
                          )}
                          {request.status !== "IN_REVIEW" && (
                            <DropdownMenuItem onClick={() => handleStatusChange(request.id, "IN_REVIEW")}>
                              <Clock className="mr-2 h-4 w-4 text-blue-500" />
                              Mark as In Review
                            </DropdownMenuItem>
                          )}
                          {request.status !== "REJECTED" && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <X className="mr-2 h-4 w-4 text-red-500" />
                                  Reject Verification
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Reject Verification</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to reject this verification request? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => handleStatusChange(request.id, "REJECTED")}
                                    className="bg-red-500 hover:bg-red-600"
                                  >
                                    Reject
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </AnimatedCardContent>
        <CardFooter className="flex items-center justify-between border-t p-4">
          <div className="text-sm text-muted-foreground">
            Showing {displayRequests.length} of {pagination.totalItems || displayRequests.length} verification requests
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}
                  disabled={pagination.page === 1}
                />
              </PaginationItem>
              {Array.from({ length: pagination.totalPages || 1 }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    isActive={page === pagination.page}
                    onClick={() => setPagination({ ...pagination, page })}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext 
                  onClick={() => setPagination({ ...pagination, page: Math.min(pagination.totalPages || 1, pagination.page + 1) })}
                  disabled={pagination.page === (pagination.totalPages || 1)}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardFooter>
      </AnimatedCard>
    </div>
  );
}
