import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import CreditVerificationClient from "./credit-verification-client";

export const metadata: Metadata = {
  title: "Credit Verification | Admin",
  description: "Verify and approve carbon credit listings",
};

export default async function CreditVerificationPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <PageHeaderWithBreadcrumb
        title="Credit Verification"
        description="Verify and approve carbon credit listings"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Verification", href: "/admin/verification" },
        ]}
      />
      <CreditVerificationClient />
    </div>
  );
}
