"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  AlertCircle, 
  CheckCircle2, 
  XCircle, 
  Clock,
  AlertTriangle,
  FileCheck,
  RefreshCw,
  Building,
  Calendar,
  FileText
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function AdminVerificationsPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [verificationRequests, setVerificationRequests] = useState<any[]>([]);
  const [statusFilter, setStatusFilter] = useState("IN_REVIEW");

  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      setError("You must be logged in to access the admin dashboard");
      setIsLoading(false);
      return;
    }

    if (session.user.role !== "ADMIN") {
      setError("You do not have permission to access the admin dashboard");
      setIsLoading(false);
      return;
    }

    fetchVerificationRequests();
  }, [session, status, statusFilter]);

  const fetchVerificationRequests = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/stats");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch verification requests");
      }

      // Filter verification requests based on status filter
      let filteredRequests = data.verificationRequests || [];
      
      if (statusFilter !== "ALL") {
        filteredRequests = filteredRequests.filter(
          (req: any) => req.status === statusFilter
        );
      }

      setVerificationRequests(filteredRequests);
    } catch (error) {
      console.error("Error fetching verification requests:", error);
      toast({
        title: "Error",
        description: "Failed to load verification requests",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            Verified
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case "IN_REVIEW":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            In Review
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading && verificationRequests.length === 0) {
    return (
      <div className="container py-10">
        <div className="mb-8">
          <Skeleton className="h-10 w-[250px] mb-2" />
          <Skeleton className="h-4 w-[350px]" />
        </div>
        <Skeleton className="h-[500px] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
          <div className="mb-4 rounded-full bg-destructive/10 p-3">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="mb-1 text-lg font-medium">Access Denied</h3>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            {error}
          </p>
          <Button onClick={() => router.push("/dashboard")}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Verification Requests</h1>
        <p className="text-muted-foreground">
          Review and manage organization verification requests
        </p>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <FileCheck className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Status:</span>
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Statuses</SelectItem>
              <SelectItem value="IN_REVIEW">In Review</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="VERIFIED">Verified</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button variant="outline" onClick={fetchVerificationRequests}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Verification Requests</CardTitle>
          <CardDescription>
            {verificationRequests.length} {verificationRequests.length === 1 ? "request" : "requests"} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {verificationRequests.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[250px]">Organization</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {verificationRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.organization.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {request.organization.legalName || "N/A"}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(request.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>{request.documentCount}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{new Date(request.createdAt).toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{new Date(request.updatedAt).toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/organizations/${request.organization.id}/verification`)}
                        >
                          Review
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
              <div className="mb-4 rounded-full bg-primary/10 p-3">
                <FileCheck className="h-6 w-6 text-primary" />
              </div>
              <h3 className="mb-1 text-lg font-medium">No Verification Requests</h3>
              <p className="mb-4 text-center text-sm text-muted-foreground">
                There are no verification requests matching your filter criteria.
              </p>
              <Button variant="outline" onClick={() => setStatusFilter("ALL")}>
                View All Requests
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="mt-8 grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Verification Process</CardTitle>
            <CardDescription>
              Guidelines for reviewing verification requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-2">
                <div className="mt-0.5 rounded-full bg-blue-100 p-1 dark:bg-blue-800">
                  <Clock className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                </div>
                <div>
                  <h3 className="text-sm font-medium">1. Review Documents</h3>
                  <p className="text-sm text-muted-foreground">
                    Check all submitted documents for authenticity, clarity, and validity.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <div className="mt-0.5 rounded-full bg-blue-100 p-1 dark:bg-blue-800">
                  <Building className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                </div>
                <div>
                  <h3 className="text-sm font-medium">2. Verify Organization Details</h3>
                  <p className="text-sm text-muted-foreground">
                    Confirm that the organization details match the submitted documents.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <div className="mt-0.5 rounded-full bg-blue-100 p-1 dark:bg-blue-800">
                  <AlertTriangle className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                </div>
                <div>
                  <h3 className="text-sm font-medium">3. Check for Red Flags</h3>
                  <p className="text-sm text-muted-foreground">
                    Look for inconsistencies, suspicious information, or potential fraud.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <div className="mt-0.5 rounded-full bg-blue-100 p-1 dark:bg-blue-800">
                  <CheckCircle2 className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                </div>
                <div>
                  <h3 className="text-sm font-medium">4. Make Decision</h3>
                  <p className="text-sm text-muted-foreground">
                    Approve or reject the verification request with clear notes explaining the decision.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Verification Statistics</CardTitle>
            <CardDescription>
              Overview of verification activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="rounded-lg border p-3">
                  <div className="text-sm font-medium text-muted-foreground">Pending</div>
                  <div className="mt-1 flex items-center">
                    <Clock className="mr-2 h-5 w-5 text-yellow-500" />
                    <div className="text-2xl font-bold">
                      {verificationRequests.filter(req => req.status === "PENDING").length}
                    </div>
                  </div>
                </div>
                <div className="rounded-lg border p-3">
                  <div className="text-sm font-medium text-muted-foreground">In Review</div>
                  <div className="mt-1 flex items-center">
                    <AlertTriangle className="mr-2 h-5 w-5 text-orange-500" />
                    <div className="text-2xl font-bold">
                      {verificationRequests.filter(req => req.status === "IN_REVIEW").length}
                    </div>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="rounded-lg border p-3">
                  <div className="text-sm font-medium text-muted-foreground">Approved</div>
                  <div className="mt-1 flex items-center">
                    <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                    <div className="text-2xl font-bold">
                      {verificationRequests.filter(req => req.status === "VERIFIED").length}
                    </div>
                  </div>
                </div>
                <div className="rounded-lg border p-3">
                  <div className="text-sm font-medium text-muted-foreground">Rejected</div>
                  <div className="mt-1 flex items-center">
                    <XCircle className="mr-2 h-5 w-5 text-red-500" />
                    <div className="text-2xl font-bold">
                      {verificationRequests.filter(req => req.status === "REJECTED").length}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
