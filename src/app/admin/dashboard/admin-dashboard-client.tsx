"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Users,
  Building2,
  CreditCard,
  BarChart3,
  Settings,
  Wallet,
  FileText,
  ShieldCheck,
  Clock,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  CheckCircle2,
  XCircle,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Folder
} from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardDescription,
  AnimatedCardHeader,
  AnimatedCardTitle,
  StaggeredList
} from "@/components/ui/animated";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/charts";
import AdminMetricsCards from "@/components/admin/admin-metrics-cards";
import AdminRecentActivity from "@/components/admin/admin-recent-activity";
import AdminOrganizationTable from "@/components/admin/admin-organization-table";
import AdminUserTable from "@/components/admin/admin-user-table";

export default function AdminDashboardClient() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalOrganizations: 0,
    totalCarbonCredits: 0,
    totalTransactions: 0,
    transactionVolume: 0,
    pendingVerifications: 0,
    activeSubscriptions: 0,
    platformRevenue: 0,
    userGrowth: [],
    transactionGrowth: [],
    recentTransactions: [],
    recentOrganizations: [],
    pendingOrganizations: [],
    verificationRequests: [],
  });
  const [isLoading, setIsLoading] = useState(true); // Used for loading state

  useEffect(() => {
    async function fetchAdminStats() {
      setIsLoading(true);
      try {
        const response = await fetch("/api/admin/stats");
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error ?? "Failed to fetch admin statistics");
        }

        setStats(data);
      } catch (error) {
        console.error("Error fetching admin statistics:", error);
        toast({
          title: "Error",
          description: "Failed to load admin statistics",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchAdminStats();
  }, []);

  // Sample data for charts
  const userGrowthData = [
    { name: "Jan", value: 120 },
    { name: "Feb", value: 150 },
    { name: "Mar", value: 180 },
    { name: "Apr", value: 220 },
    { name: "May", value: 300 },
    { name: "Jun", value: 350 },
  ];

  const transactionData = [
    { name: "Jan", value: 5000 },
    { name: "Feb", value: 8000 },
    { name: "Mar", value: 12000 },
    { name: "Apr", value: 15000 },
    { name: "May", value: 20000 },
    { name: "Jun", value: 25000 },
  ];

  const creditDistributionData = [
    { name: "Renewable Energy", value: 45 },
    { name: "Forestry", value: 30 },
    { name: "Methane Capture", value: 15 },
    { name: "Other", value: 10 },
  ];

  return (
    <div className="space-y-8">
      <div className="mb-8">
        {/* <h1 className="text-3xl font-bold">Admin Dashboard</h1> */}
        <p className="text-muted-foreground">
          Monitor platform performance and manage administrative tasks
        </p>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="organizations">Organizations</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="verifications">Verifications</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <AdminMetricsCards stats={stats} />

          <div className="grid gap-6 mt-8 md:grid-cols-2">
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>User Growth</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Monthly user registration trends
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <LineChart
                  data={userGrowthData}
                  categories={["value"]}
                  index="name"
                  colors={["#2563eb"]}
                  valueFormatter={(value) => `${value} users`}
                  showLegend={false}
                />
              </AnimatedCardContent>
            </AnimatedCard>

            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Transaction Volume</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Monthly transaction volume in INR
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <BarChart
                  data={transactionData}
                  categories={["value"]}
                  index="name"
                  colors={["#16a34a"]}
                  valueFormatter={(value) => `₹${value}`}
                  showLegend={false}
                />
              </AnimatedCardContent>
            </AnimatedCard>
          </div>

          <div className="grid gap-6 mt-8 md:grid-cols-2">
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Recent Organizations</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Recently registered organizations
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <AdminRecentActivity
                  items={stats.recentOrganizations || []}
                  type="organizations"
                />
              </AnimatedCardContent>
            </AnimatedCard>

            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Carbon Credit Distribution</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Distribution by project type
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <PieChart
                  data={creditDistributionData}
                  nameKey="name"
                  dataKey="value"
                  valueFormatter={(value) => `${value}%`}
                />
              </AnimatedCardContent>
            </AnimatedCard>
          </div>

          <div className="mt-8">
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Recent Transactions</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Latest platform transactions
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <AdminRecentActivity
                  items={stats.recentTransactions || []}
                  type="transactions"
                />
              </AnimatedCardContent>
            </AnimatedCard>
          </div>

          <StaggeredList className="mt-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/users")}>
              <AnimatedCardHeader>
                <div className="flex items-center justify-between">
                  <AnimatedCardTitle>User Management</AnimatedCardTitle>
                  <Users className="h-5 w-5" />
                </div>
                <AnimatedCardDescription>
                  Manage user accounts, roles, and permissions
                </AnimatedCardDescription>
              </AnimatedCardHeader>
            </AnimatedCard>

            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/organizations")}>
              <AnimatedCardHeader>
                <div className="flex items-center justify-between">
                  <AnimatedCardTitle>Organization Management</AnimatedCardTitle>
                  <Building2 className="h-5 w-5" />
                </div>
                <AnimatedCardDescription>
                  Manage organizations, approvals, and settings
                </AnimatedCardDescription>
              </AnimatedCardHeader>
            </AnimatedCard>

            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/projects")}>
              <AnimatedCardHeader>
                <div className="flex items-center justify-between">
                  <AnimatedCardTitle>Project Management</AnimatedCardTitle>
                  <Folder className="h-5 w-5" />
                </div>
                <AnimatedCardDescription>
                  Manage carbon credit projects and monitoring
                </AnimatedCardDescription>
              </AnimatedCardHeader>
            </AnimatedCard>

            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/verification")}>
              <AnimatedCardHeader>
                <div className="flex items-center justify-between">
                  <AnimatedCardTitle>Credit Verification</AnimatedCardTitle>
                  <FileText className="h-5 w-5" />
                </div>
                <AnimatedCardDescription>
                  Verify and approve carbon credit listings
                </AnimatedCardDescription>
              </AnimatedCardHeader>
            </AnimatedCard>

            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/settings")}>
              <AnimatedCardHeader>
                <div className="flex items-center justify-between">
                  <AnimatedCardTitle>Platform Settings</AnimatedCardTitle>
                  <Settings className="h-5 w-5" />
                </div>
                <AnimatedCardDescription>
                  Configure platform-wide settings and fees
                </AnimatedCardDescription>
              </AnimatedCardHeader>
            </AnimatedCard>
          </StaggeredList>
        </TabsContent>

        <TabsContent value="organizations">
          <AdminOrganizationTable />
        </TabsContent>

        <TabsContent value="users">
          <AdminUserTable />
        </TabsContent>

        <TabsContent value="verifications">
          <div className="space-y-4">
            <h2 className="text-2xl font-bold">Pending Verifications</h2>
            <p className="text-muted-foreground">
              Review and process verification requests
            </p>
            {stats.verificationRequests && stats.verificationRequests.length > 0 ? (
              <div className="grid gap-4">
                {stats.verificationRequests.map((request: any) => (
                  <AnimatedCard key={request.id} className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push(`/admin/organizations/${request.organization.id}/verification`)}>
                    <AnimatedCardContent className="flex items-center justify-between p-4">
                      <div>
                        <h3 className="font-medium">{request.organization.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Submitted: {new Date(request.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {request.status === "IN_REVIEW" && (
                          <Clock className="h-5 w-5 text-amber-500" />
                        )}
                        {request.status === "APPROVED" && (
                          <CheckCircle2 className="h-5 w-5 text-green-500" />
                        )}
                        {request.status === "REJECTED" && (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                        <span>{request.status.replace("_", " ")}</span>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                ))}
              </div>
            ) : (
              <AnimatedCard>
                <AnimatedCardContent className="p-6 text-center">
                  <p className="text-muted-foreground">No pending verification requests</p>
                </AnimatedCardContent>
              </AnimatedCard>
            )}
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="space-y-8">
            <div className="grid gap-6 md:grid-cols-2">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>User Growth</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Monthly user registration trends
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <LineChart
                    data={userGrowthData}
                    categories={["value"]}
                    index="name"
                    colors={["#2563eb"]}
                    valueFormatter={(value) => `${value} users`}
                    showLegend={false}
                  />
                </AnimatedCardContent>
              </AnimatedCard>

              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Transaction Volume</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Monthly transaction volume in INR
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <BarChart
                    data={transactionData}
                    categories={["value"]}
                    index="name"
                    colors={["#16a34a"]}
                    valueFormatter={(value) => `₹${value}`}
                    showLegend={false}
                  />
                </AnimatedCardContent>
              </AnimatedCard>
            </div>

            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Carbon Credit Distribution</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Distribution by project type
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <PieChart
                    data={creditDistributionData}
                    nameKey="name"
                    dataKey="value"
                    valueFormatter={(value) => `${value}%`}
                  />
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Distribution Analysis</h3>
                    <p className="text-muted-foreground">
                      Renewable energy projects make up the largest portion of carbon credits on the platform, followed by forestry projects. This distribution reflects the current market trends in carbon offset projects.
                    </p>
                    <p className="text-muted-foreground">
                      Methane capture projects, while fewer in number, often represent high-value credits due to the potency of methane as a greenhouse gas.
                    </p>
                  </div>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
