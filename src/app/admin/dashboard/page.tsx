import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import AdminDashboardClient from "./admin-dashboard-client";

export const metadata: Metadata = {
  title: "Admin Dashboard",
  description: "Monitor platform performance and manage administrative tasks",
};

export default async function AdminDashboardPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <PageHeaderWithBreadcrumb
        title="Admin Dashboard"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Dashboard", href: "/admin/dashboard" },
        ]}
      />
      <AdminDashboardClient />
    </div>
  );
}
