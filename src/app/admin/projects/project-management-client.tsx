"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  MoreHorizontal, 
  Search, 
  Building2, 
  Folder, 
  FileCheck,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Filter,
  X,
  Check,
  AlertTriangle,
  Globe,
  MapPin,
  FileText,
  Leaf,
  BarChart,
  Eye
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardDescription, 
  AnimatedCardHeader, 
  AnimatedCardTitle 
} from "@/components/ui/animated";

export default function ProjectManagementClient() {
  const router = useRouter();
  const [projects, setProjects] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [verificationFilter, setVerificationFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalPages: 1,
    totalItems: 0,
  });

  useEffect(() => {
    fetchProjects();
  }, [pagination.page, statusFilter, verificationFilter, typeFilter, searchQuery]);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
      });
      
      if (searchQuery) {
        params.append("search", searchQuery);
      }
      
      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }
      
      if (verificationFilter !== "all") {
        params.append("verificationStatus", verificationFilter);
      }
      
      if (typeFilter !== "all") {
        params.append("projectType", typeFilter);
      }
      
      const response = await fetch(`/api/admin/projects?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch projects");
      }

      setProjects(data.projects || []);
      setPagination({
        ...pagination,
        totalPages: data.totalPages || 1,
        totalItems: data.totalItems || 0,
      });
    } catch (error) {
      console.error("Error fetching projects:", error);
      toast({
        title: "Error",
        description: "Failed to load projects",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (projectId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/projects/${projectId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update project status");
      }

      toast({
        title: "Success",
        description: "Project status updated successfully",
      });

      fetchProjects();
    } catch (error) {
      console.error("Error updating project status:", error);
      toast({
        title: "Error",
        description: "Failed to update project status",
        variant: "destructive",
      });
    }
  };

  const handleVerificationChange = async (projectId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/projects/${projectId}/verification`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ verificationStatus: newStatus }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update verification status");
      }

      toast({
        title: "Success",
        description: "Verification status updated successfully",
      });

      fetchProjects();
    } catch (error) {
      console.error("Error updating verification status:", error);
      toast({
        title: "Error",
        description: "Failed to update verification status",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>;
      case "INACTIVE":
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Inactive</Badge>;
      case "SUSPENDED":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Suspended</Badge>;
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case "COMPLETED":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getVerificationBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Verified</Badge>;
      case "PENDING":
        return <Badge variant="secondary">Pending</Badge>;
      case "IN_REVIEW":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Review</Badge>;
      case "REJECTED":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="outline">Not Verified</Badge>;
    }
  };

  const getProjectTypeBadge = (type: string) => {
    switch (type) {
      case "RENEWABLE_ENERGY":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Renewable Energy</Badge>;
      case "FORESTRY":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Forestry</Badge>;
      case "METHANE_CAPTURE":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Methane Capture</Badge>;
      case "ENERGY_EFFICIENCY":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Energy Efficiency</Badge>;
      case "WASTE_MANAGEMENT":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Waste Management</Badge>;
      default:
        return <Badge variant="outline">Other</Badge>;
    }
  };

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setVerificationFilter("all");
    setTypeFilter("all");
    setPagination({
      ...pagination,
      page: 1,
    });
  };

  // Sample data for demonstration
  const sampleProjects = [
    {
      id: "proj_1",
      name: "Solar Farm Initiative",
      description: "Large-scale solar farm in Arizona",
      projectType: "RENEWABLE_ENERGY",
      status: "ACTIVE",
      verificationStatus: "VERIFIED",
      location: "Arizona, USA",
      organization: {
        id: "org_1",
        name: "SolarTech Innovations",
        logo: ""
      },
      carbonCredits: 15000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "proj_2",
      name: "Amazon Reforestation",
      description: "Reforestation project in the Amazon rainforest",
      projectType: "FORESTRY",
      status: "ACTIVE",
      verificationStatus: "VERIFIED",
      location: "Brazil",
      organization: {
        id: "org_2",
        name: "Green Earth Foundation",
        logo: ""
      },
      carbonCredits: 25000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "proj_3",
      name: "Landfill Methane Capture",
      description: "Methane capture from municipal landfill",
      projectType: "METHANE_CAPTURE",
      status: "PENDING",
      verificationStatus: "IN_REVIEW",
      location: "Ontario, Canada",
      organization: {
        id: "org_3",
        name: "CleanAir Solutions",
        logo: ""
      },
      carbonCredits: 8000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "proj_4",
      name: "Industrial Energy Efficiency",
      description: "Energy efficiency improvements in manufacturing",
      projectType: "ENERGY_EFFICIENCY",
      status: "ACTIVE",
      verificationStatus: "PENDING",
      location: "Germany",
      organization: {
        id: "org_4",
        name: "EcoIndustry GmbH",
        logo: ""
      },
      carbonCredits: 12000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "proj_5",
      name: "Waste-to-Energy Plant",
      description: "Converting municipal waste to energy",
      projectType: "WASTE_MANAGEMENT",
      status: "INACTIVE",
      verificationStatus: "REJECTED",
      location: "Singapore",
      organization: {
        id: "org_5",
        name: "WasteTech Solutions",
        logo: ""
      },
      carbonCredits: 5000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  // Use sample data if API is not yet implemented
  const displayProjects = projects.length > 0 ? projects : sampleProjects;

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search projects..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear
          </Button>
        </div>
        <div className="flex flex-row gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="INACTIVE">Inactive</SelectItem>
              <SelectItem value="SUSPENDED">Suspended</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="COMPLETED">Completed</SelectItem>
            </SelectContent>
          </Select>
          <Select value={verificationFilter} onValueChange={setVerificationFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by verification" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Verification</SelectItem>
              <SelectItem value="VERIFIED">Verified</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="IN_REVIEW">In Review</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
            </SelectContent>
          </Select>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="RENEWABLE_ENERGY">Renewable Energy</SelectItem>
              <SelectItem value="FORESTRY">Forestry</SelectItem>
              <SelectItem value="METHANE_CAPTURE">Methane Capture</SelectItem>
              <SelectItem value="ENERGY_EFFICIENCY">Energy Efficiency</SelectItem>
              <SelectItem value="WASTE_MANAGEMENT">Waste Management</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <AnimatedCard>
        <AnimatedCardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Project</TableHead>
                <TableHead>Organization</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Verification</TableHead>
                <TableHead>Carbon Credits</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array(5).fill(0).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell colSpan={8} className="h-16 text-center">
                      <div className="flex justify-center">
                        <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : displayProjects.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    No projects found.
                  </TableCell>
                </TableRow>
              ) : (
                displayProjects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <Folder className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{project.name}</div>
                          <div className="text-xs text-muted-foreground">{project.description?.substring(0, 30)}...</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>{project.organization?.name || "N/A"}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getProjectTypeBadge(project.projectType)}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>{project.location || "N/A"}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(project.status)}</TableCell>
                    <TableCell>{getVerificationBadge(project.verificationStatus)}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Leaf className="h-4 w-4 text-green-500" />
                        <span>{project.carbonCredits?.toLocaleString() || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => router.push(`/admin/projects/${project.id}`)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/admin/projects/${project.id}/verification`)}>
                            <FileCheck className="mr-2 h-4 w-4" />
                            Verification Documents
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/admin/projects/${project.id}/monitor`)}>
                            <BarChart className="mr-2 h-4 w-4" />
                            Monitoring Data
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                          <DropdownMenuItem 
                            onClick={() => handleStatusChange(project.id, "ACTIVE")}
                            disabled={project.status === "ACTIVE"}
                          >
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            Activate
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleStatusChange(project.id, "INACTIVE")}
                            disabled={project.status === "INACTIVE"}
                          >
                            <X className="mr-2 h-4 w-4 text-gray-500" />
                            Deactivate
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleStatusChange(project.id, "COMPLETED")}
                            disabled={project.status === "COMPLETED"}
                          >
                            <Check className="mr-2 h-4 w-4 text-blue-500" />
                            Mark as Completed
                          </DropdownMenuItem>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <AlertTriangle className="mr-2 h-4 w-4 text-red-500" />
                                Suspend
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Suspend Project</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to suspend this project? This will prevent any new carbon credits from being issued.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction 
                                  onClick={() => handleStatusChange(project.id, "SUSPENDED")}
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  Suspend
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Verification</DropdownMenuLabel>
                          <DropdownMenuItem 
                            onClick={() => handleVerificationChange(project.id, "VERIFIED")}
                            disabled={project.verificationStatus === "VERIFIED"}
                          >
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            Mark as Verified
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleVerificationChange(project.id, "IN_REVIEW")}
                            disabled={project.verificationStatus === "IN_REVIEW"}
                          >
                            <FileText className="mr-2 h-4 w-4 text-blue-500" />
                            Mark as In Review
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleVerificationChange(project.id, "REJECTED")}
                            disabled={project.verificationStatus === "REJECTED"}
                          >
                            <X className="mr-2 h-4 w-4 text-red-500" />
                            Reject Verification
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </AnimatedCardContent>
        <CardFooter className="flex items-center justify-between border-t p-4">
          <div className="text-sm text-muted-foreground">
            Showing {displayProjects.length} of {pagination.totalItems || displayProjects.length} projects
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}
                  disabled={pagination.page === 1}
                />
              </PaginationItem>
              {Array.from({ length: pagination.totalPages || 1 }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    isActive={page === pagination.page}
                    onClick={() => setPagination({ ...pagination, page })}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext 
                  onClick={() => setPagination({ ...pagination, page: Math.min(pagination.totalPages || 1, pagination.page + 1) })}
                  disabled={pagination.page === (pagination.totalPages || 1)}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardFooter>
      </AnimatedCard>
    </div>
  );
}
