import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import ProjectManagementClient from "./project-management-client";

export const metadata: Metadata = {
  title: "Project Management | Admin",
  description: "Manage carbon credit projects and monitoring",
};

export default async function ProjectManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <PageHeaderWithBreadcrumb
        title="Project Management"
        description="Manage carbon credit projects and monitoring"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Projects", href: "/admin/projects" },
        ]}
      />
      <ProjectManagementClient />
    </div>
  );
}
