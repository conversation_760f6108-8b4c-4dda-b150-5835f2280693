"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AlertCircle, Users, Building2, CreditCard, BarChart3, Settings } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  AnimatedCardContent,
  AnimatedCardDescription,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedButton,
  PageTransition,
  StaggeredList
} from "@/components/ui/animated";

export default function AdminDashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalOrganizations: 0,
    totalCarbonCredits: 0,
    totalTransactions: 0,
    transactionVolume: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAdminStats() {
      try {
        const response = await fetch("/api/admin/stats");
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error ?? "Failed to fetch admin statistics");
        }

        setStats(data);
      } catch (error) {
        console.error("Error fetching admin statistics:", error);
        setError("Failed to load admin statistics");
      } finally {
        setIsLoading(false);
      }
    }

    if (session?.user?.role === "ADMIN") {
      fetchAdminStats();
    } else if (status !== "loading") {
      setIsLoading(false);
      setError("You do not have permission to access the admin dashboard");
    }
  }, [session, status]);

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || session?.user?.role !== "ADMIN") {
    return (
      <div className="container py-10">
        <AnimatedCard>
          <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
            <AlertCircle className="mb-4 h-10 w-10 text-destructive" />
            <h3 className="mb-1 text-lg font-medium">Access Denied</h3>
            <p className="mb-4 text-center text-sm text-muted-foreground">
              {error || "You do not have permission to access the admin dashboard"}
            </p>
            <AnimatedButton variant="outline" onClick={() => router.push("/dashboard")} animationVariant="buttonTap">
              Back to Dashboard
            </AnimatedButton>
          </AnimatedCardContent>
        </AnimatedCard>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Manage users, organizations, and platform settings
        </p>
      </div>

      <StaggeredList className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <AnimatedCard>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Total Users
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalUsers}</div>
              <Users className="h-5 w-5 text-muted-foreground" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Total Organizations
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalOrganizations}</div>
              <Building2 className="h-5 w-5 text-muted-foreground" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Carbon Credits
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalCarbonCredits}</div>
              <CreditCard className="h-5 w-5 text-muted-foreground" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard>
          <AnimatedCardHeader className="pb-2">
            <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
              Transaction Volume
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                ${stats.transactionVolume.toLocaleString()}
              </div>
              <BarChart3 className="h-5 w-5 text-muted-foreground" />
            </div>
          </AnimatedCardContent>
        </AnimatedCard>
      </StaggeredList>

      <StaggeredList className="mt-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/users")}>
          <AnimatedCardHeader>
            <div className="flex items-center justify-between">
              <AnimatedCardTitle>User Management</AnimatedCardTitle>
              <Users className="h-5 w-5" />
            </div>
            <AnimatedCardDescription>
              Manage user accounts, roles, and permissions
            </AnimatedCardDescription>
          </AnimatedCardHeader>
        </AnimatedCard>

        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/organizations")}>
          <AnimatedCardHeader>
            <div className="flex items-center justify-between">
              <AnimatedCardTitle>Organization Management</AnimatedCardTitle>
              <Building2 className="h-5 w-5" />
            </div>
            <AnimatedCardDescription>
              Manage organizations, approvals, and settings
            </AnimatedCardDescription>
          </AnimatedCardHeader>
        </AnimatedCard>

        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/subscriptions")}>
          <AnimatedCardHeader>
            <div className="flex items-center justify-between">
              <AnimatedCardTitle>Subscription Management</AnimatedCardTitle>
              <CreditCard className="h-5 w-5" />
            </div>
            <AnimatedCardDescription>
              Manage subscription plans and billing
            </AnimatedCardDescription>
          </AnimatedCardHeader>
        </AnimatedCard>

        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/analytics")}>
          <AnimatedCardHeader>
            <div className="flex items-center justify-between">
              <AnimatedCardTitle>Analytics & Reports</AnimatedCardTitle>
              <BarChart3 className="h-5 w-5" />
            </div>
            <AnimatedCardDescription>
              View platform analytics and generate reports
            </AnimatedCardDescription>
          </AnimatedCardHeader>
        </AnimatedCard>

        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/admin/settings")}>
          <AnimatedCardHeader>
            <div className="flex items-center justify-between">
              <AnimatedCardTitle>Platform Settings</AnimatedCardTitle>
              <Settings className="h-5 w-5" />
            </div>
            <AnimatedCardDescription>
              Configure platform-wide settings and fees
            </AnimatedCardDescription>
          </AnimatedCardHeader>
        </AnimatedCard>
      </StaggeredList>
    </div>
    </PageTransition>
  );
}
