import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import PlatformConfigurationClient from "./platform-configuration-client";

export const metadata: Metadata = {
  title: "Platform Configuration | Admin",
  description: "Configure platform-wide settings and fees",
};

export default async function PlatformConfigurationPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is admin
  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <PageHeaderWithBreadcrumb
        title="Platform Configuration"
        description="Configure platform-wide settings and fees"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Settings", href: "/admin/settings" },
        ]}
      />
      <PlatformConfigurationClient />
    </div>
  );
}
