"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Alert<PERSON>ircle,
  CreditCard,
  Percent,
  DollarSign,
  Building2,
  Loader2,
  Save,
  Plus,
  Edit,
  Trash2
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ValidatedForm } from "@/components/forms/validated-form";
import { platformFeeSchema, organizationFeeSchema } from "@/lib/validation/financial-schemas";

// Use the schemas from our validation library
type PlatformFeeValues = {
  defaultListingFeeRate: number;
  defaultTransactionFeeRate: number;
  defaultSubscriptionFee: number;
};

type OrganizationFeeValues = {
  organizationId: string;
  listingFeeRate: number;
  transactionFeeRate: number;
  subscriptionFee: number;
  effectiveFrom: Date;
  notes?: string;
};

interface Organization {
  id: string;
  name: string;
  legalName: string;
  status: string;
  listingFeeRate: number;
  transactionFeeRate: number;
  subscriptionFee: number;
}

interface FeeHistory {
  id: string;
  organizationId: string;
  organizationName: string;
  listingFeeRate: number;
  transactionFeeRate: number;
  subscriptionFee: number;
  effectiveFrom: string;
  createdAt: string;
  notes: string | null;
}

export default function FeesSettingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("platform");
  const [isLoading, setIsLoading] = useState(true);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [feeHistory, setFeeHistory] = useState<FeeHistory[]>([]);
  const [showOrgFeeDialog, setShowOrgFeeDialog] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Platform fee form default values
  const [platformFeeValues, setPlatformFeeValues] = useState<PlatformFeeValues>({
    defaultListingFeeRate: 2.5,
    defaultTransactionFeeRate: 1.5,
    defaultSubscriptionFee: 99,
  });

  // Organization fee form default values
  const [orgFeeValues, setOrgFeeValues] = useState<OrganizationFeeValues>({
    organizationId: "",
    listingFeeRate: 2.5,
    transactionFeeRate: 1.5,
    subscriptionFee: 99,
    effectiveFrom: new Date(),
    notes: "",
  });

  // Fetch platform fee settings
  const fetchPlatformFeeSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/settings/fees");

      if (!response.ok) {
        throw new Error("Failed to fetch fee settings");
      }

      const data = await response.json();

      setPlatformFeeValues({
        defaultListingFeeRate: data.settings.defaultListingFeeRate,
        defaultTransactionFeeRate: data.settings.defaultTransactionFeeRate,
        defaultSubscriptionFee: data.settings.defaultSubscriptionFee,
      });
    } catch (error) {
      console.error("Error fetching fee settings:", error);
      toast({
        title: "Error",
        description: "Failed to load fee settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch organizations
  const fetchOrganizations = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/organizations?status=ACTIVE");

      if (!response.ok) {
        throw new Error("Failed to fetch organizations");
      }

      const data = await response.json();
      setOrganizations(data.organizations);
    } catch (error) {
      console.error("Error fetching organizations:", error);
      toast({
        title: "Error",
        description: "Failed to load organizations",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch fee history
  const fetchFeeHistory = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/settings/fees/history");

      if (!response.ok) {
        throw new Error("Failed to fetch fee history");
      }

      const data = await response.json();
      setFeeHistory(data.history);
    } catch (error) {
      console.error("Error fetching fee history:", error);
      toast({
        title: "Error",
        description: "Failed to load fee history",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Save platform fee settings
  const savePlatformFeeSettings = async (data: PlatformFeeValues) => {
    try {
      const response = await fetch("/api/admin/settings/fees", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save fee settings");
      }

      // Update local state with the saved values
      setPlatformFeeValues(data);

      toast({
        title: "Success",
        description: "Platform fee settings saved successfully",
      });
    } catch (error) {
      console.error("Error saving fee settings:", error);
      throw new Error(error instanceof Error ? error.message : "Failed to save fee settings");
    }
  };

  // Save organization fee settings
  const saveOrganizationFeeSettings = async (data: OrganizationFeeValues) => {
    try {
      const response = await fetch(`/api/admin/organizations/${data.organizationId}/fees`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          listingFeeRate: data.listingFeeRate,
          transactionFeeRate: data.transactionFeeRate,
          subscriptionFee: data.subscriptionFee,
          effectiveFrom: data.effectiveFrom,
          notes: data.notes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save organization fee settings");
      }

      toast({
        title: "Success",
        description: "Organization fee settings saved successfully",
      });

      // Refresh data
      fetchOrganizations();
      fetchFeeHistory();
      setShowOrgFeeDialog(false);

      // Reset form values
      setOrgFeeValues({
        organizationId: "",
        listingFeeRate: platformFeeValues.defaultListingFeeRate,
        transactionFeeRate: platformFeeValues.defaultTransactionFeeRate,
        subscriptionFee: platformFeeValues.defaultSubscriptionFee,
        effectiveFrom: new Date(),
        notes: "",
      });
    } catch (error) {
      console.error("Error saving organization fee settings:", error);
      throw new Error(error instanceof Error ? error.message : "Failed to save organization fee settings");
    }
  };

  // Handle organization selection for fee settings
  const handleOrgSelection = (org: Organization) => {
    setSelectedOrg(org);
    setIsEditMode(true);
    setOrgFeeValues({
      organizationId: org.id,
      listingFeeRate: org.listingFeeRate,
      transactionFeeRate: org.transactionFeeRate,
      subscriptionFee: org.subscriptionFee,
      effectiveFrom: new Date(),
      notes: `Updated fees for ${org.name}`,
    });
    setShowOrgFeeDialog(true);
  };

  // Handle new organization fee
  const handleNewOrgFee = () => {
    setSelectedOrg(null);
    setIsEditMode(false);
    setOrgFeeValues({
      organizationId: "",
      listingFeeRate: platformFeeValues.defaultListingFeeRate,
      transactionFeeRate: platformFeeValues.defaultTransactionFeeRate,
      subscriptionFee: platformFeeValues.defaultSubscriptionFee,
      effectiveFrom: new Date(),
      notes: "",
    });
    setShowOrgFeeDialog(true);
  };

  // Load data when tab changes
  useEffect(() => {
    if (session?.user) {
      if (activeTab === "platform") {
        fetchPlatformFeeSettings();
      } else if (activeTab === "organizations") {
        fetchOrganizations();
      } else if (activeTab === "history") {
        fetchFeeHistory();
      }
    }
  }, [session, activeTab]);

  // Redirect if not admin
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else if (status === "authenticated" && session?.user?.role !== "ADMIN") {
      router.push("/dashboard");
      toast({
        title: "Access Denied",
        description: "You do not have permission to access this page",
        variant: "destructive",
      });
    }
  }, [status, session, router]);

  if (status === "loading" || (status === "authenticated" && session?.user?.role !== "ADMIN")) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Fee Configuration</h1>
        <p className="text-muted-foreground">
          Manage platform and organization-specific fees
        </p>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="platform">Platform Fees</TabsTrigger>
          <TabsTrigger value="organizations">Organization Fees</TabsTrigger>
          <TabsTrigger value="history">Fee History</TabsTrigger>
        </TabsList>

        <TabsContent value="platform" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Platform Fee Settings</CardTitle>
              <CardDescription>
                Configure default fee rates for all organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <div className="h-10 w-full bg-muted animate-pulse rounded" />
                  <div className="h-10 w-full bg-muted animate-pulse rounded" />
                  <div className="h-10 w-full bg-muted animate-pulse rounded" />
                </div>
              ) : (
                <ValidatedForm
                  schema={platformFeeSchema}
                  defaultValues={platformFeeValues}
                  onSubmit={savePlatformFeeSettings}
                  className="space-y-6"
                >
                  {({ control, formState, isSubmitting, formError }) => (
                    <>
                    {formError && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{formError.message}</AlertDescription>
                      </Alert>
                    )}
                    <FormField
                      control={control}
                      name="defaultListingFeeRate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Listing Fee Rate (%)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                max="100"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <Percent className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </div>
                          </FormControl>
                          <FormDescription>
                            Fee charged when an organization lists carbon credits on the marketplace
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="defaultTransactionFeeRate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Transaction Fee Rate (%)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                max="100"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <Percent className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </div>
                          </FormControl>
                          <FormDescription>
                            Fee charged on each carbon credit transaction
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="defaultSubscriptionFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Subscription Fee (INR)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </div>
                          </FormControl>
                          <FormDescription>
                            Monthly subscription fee for organizations
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="mr-2 h-4 w-4" />
                      )}
                      Save Platform Fees
                    </Button>
                    </>
                  )}
                </ValidatedForm>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organizations" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Organization Fee Settings</CardTitle>
                <CardDescription>
                  Configure custom fee rates for specific organizations
                </CardDescription>
              </div>
              <Button onClick={handleNewOrgFee}>
                <Plus className="mr-2 h-4 w-4" />
                New Fee Setting
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="h-16 w-full bg-muted animate-pulse rounded" />
                  ))}
                </div>
              ) : organizations.length === 0 ? (
                <div className="text-center py-8">
                  <Building2 className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-medium">No active organizations</h3>
                  <p className="text-sm text-muted-foreground">
                    There are no active organizations to configure fees for
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Organization</TableHead>
                      <TableHead>Listing Fee</TableHead>
                      <TableHead>Transaction Fee</TableHead>
                      <TableHead>Subscription Fee</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {organizations.map((org) => (
                      <TableRow key={org.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{org.name}</p>
                            <p className="text-sm text-muted-foreground">{org.legalName}</p>
                          </div>
                        </TableCell>
                        <TableCell>{org.listingFeeRate}%</TableCell>
                        <TableCell>{org.transactionFeeRate}%</TableCell>
                        <TableCell>${org.subscriptionFee.toFixed(2)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOrgSelection(org)}
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Fee Change History</CardTitle>
              <CardDescription>
                View history of fee changes for organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="h-16 w-full bg-muted animate-pulse rounded" />
                  ))}
                </div>
              ) : feeHistory.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-medium">No fee history</h3>
                  <p className="text-sm text-muted-foreground">
                    There is no fee change history to display
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Organization</TableHead>
                      <TableHead>Listing Fee</TableHead>
                      <TableHead>Transaction Fee</TableHead>
                      <TableHead>Subscription Fee</TableHead>
                      <TableHead>Effective From</TableHead>
                      <TableHead>Created</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {feeHistory.map((history) => (
                      <TableRow key={history.id}>
                        <TableCell>{history.organizationName}</TableCell>
                        <TableCell>{history.listingFeeRate}%</TableCell>
                        <TableCell>{history.transactionFeeRate}%</TableCell>
                        <TableCell>${history.subscriptionFee.toFixed(2)}</TableCell>
                        <TableCell>
                          {format(new Date(history.effectiveFrom), "MMM d, yyyy")}
                        </TableCell>
                        <TableCell>
                          {format(new Date(history.createdAt), "MMM d, yyyy")}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Organization Fee Dialog */}
      <Dialog open={showOrgFeeDialog} onOpenChange={setShowOrgFeeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode
                ? `Update Fees for ${selectedOrg?.name}`
                : "Set Organization Fees"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update custom fee rates for this organization"
                : "Set custom fee rates for an organization"}
            </DialogDescription>
          </DialogHeader>
          <ValidatedForm
              schema={organizationFeeSchema}
              defaultValues={orgFeeValues}
              onSubmit={saveOrganizationFeeSettings}
              className="space-y-6"
            >
              {({ control, formState, isSubmitting, formError }) => (
                <>
                {formError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{formError.message}</AlertDescription>
                  </Alert>
                )}
              {!isEditMode && (
                <FormField
                  control={control}
                  name="organizationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Organization</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an organization" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {organizations.map((org) => (
                            <SelectItem key={org.id} value={org.id}>
                              {org.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the organization to set custom fees for
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={control}
                name="listingFeeRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Listing Fee Rate (%)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <Percent className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="transactionFeeRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Transaction Fee Rate (%)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <Percent className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="subscriptionFee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subscription Fee (INR)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="effectiveFrom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Effective From</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        value={field.value ? format(field.value, "yyyy-MM-dd") : ""}
                        onChange={(e) => field.onChange(new Date(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      When these fee changes will take effect
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Reason for fee change"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Internal notes about this fee change
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowOrgFeeDialog(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 h-4 w-4" />
                  )}
                  Save Fee Settings
                </Button>
              </DialogFooter>
                </>
              )}
            </ValidatedForm>
        </DialogContent>
      </Dialog>
    </div>
  );
}
