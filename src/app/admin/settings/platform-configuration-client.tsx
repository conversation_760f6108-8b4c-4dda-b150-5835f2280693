"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  AlertCircle, 
  Settings, 
  DollarSign, 
  FileCheck, 
  Mail, 
  Globe, 
  Wallet, 
  Shield, 
  Server, 
  RefreshCw,
  Save,
  Undo
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardDescription, 
  Animated<PERSON>ardHeader, 
  Animated<PERSON>ardT<PERSON><PERSON>,
  AnimatedButton
} from "@/components/ui/animated";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function PlatformConfigurationClient() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [settings, setSettings] = useState({
    // Fee settings
    transactionFeePercent: 1.5,
    listingFeeAmount: 10,
    minimumTransactionAmount: 50,
    maximumTransactionAmount: 1000000,
    
    // Verification settings
    requireVerificationForTrading: true,
    autoApproveVerification: false,
    verificationDocumentTypes: ["BUSINESS_REGISTRATION", "TAX_CERTIFICATE", "IDENTITY_PROOF"],
    
    // Email settings
    emailNotificationsEnabled: true,
    adminEmailAddress: "<EMAIL>",
    supportEmailAddress: "<EMAIL>",
    
    // Platform settings
    maintenanceMode: false,
    allowNewRegistrations: true,
    allowNewListings: true,
    platformName: "Carbonix",
    platformUrl: "https://carbonexchange.com",
    
    // Blockchain settings
    defaultNetwork: "polygon",
    useTestnet: true,
    gasLimit: 500000,
    
    // Terms and policies
    termsOfServiceUrl: "https://carbonexchange.com/terms",
    privacyPolicyUrl: "https://carbonexchange.com/privacy",
  });
  const [originalSettings, setOriginalSettings] = useState({...settings});

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/settings");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch platform settings");
      }

      setSettings(data.settings);
      setOriginalSettings(data.settings);
    } catch (error) {
      console.error("Error fetching platform settings:", error);
      toast({
        title: "Error",
        description: "Failed to load platform settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsSaving(true);
      const response = await fetch("/api/admin/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to save platform settings");
      }

      setOriginalSettings({...settings});
      
      toast({
        title: "Success",
        description: "Platform settings saved successfully",
      });
    } catch (error) {
      console.error("Error saving platform settings:", error);
      toast({
        title: "Error",
        description: "Failed to save platform settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (key: string, value: any) => {
    setSettings({
      ...settings,
      [key]: value,
    });
  };

  const resetSettings = () => {
    setSettings({...originalSettings});
    toast({
      title: "Reset",
      description: "Settings have been reset to their original values",
    });
  };

  const hasChanges = () => {
    return JSON.stringify(settings) !== JSON.stringify(originalSettings);
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-8">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="fees">Fees & Limits</TabsTrigger>
          <TabsTrigger value="verification">Verification</TabsTrigger>
          <TabsTrigger value="blockchain">Blockchain</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="legal">Legal</TabsTrigger>
        </TabsList>

        <div className="flex justify-end space-x-2 mb-6">
          <AnimatedButton 
            variant="outline" 
            onClick={resetSettings} 
            disabled={!hasChanges() || isLoading || isSaving}
            animationVariant="buttonTap"
          >
            <Undo className="mr-2 h-4 w-4" />
            Reset Changes
          </AnimatedButton>
          <AnimatedButton 
            onClick={saveSettings} 
            disabled={!hasChanges() || isLoading || isSaving}
            animationVariant="buttonTap"
          >
            {isSaving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </AnimatedButton>
        </div>

        <TabsContent value="general">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>General Platform Settings</AnimatedCardTitle>
              <AnimatedCardDescription>
                Configure basic platform settings and functionality
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="platformName" className="flex items-center">
                  <Globe className="mr-2 h-4 w-4" />
                  Platform Name
                </Label>
                <Input
                  id="platformName"
                  value={settings.platformName}
                  onChange={(e) => handleInputChange("platformName", e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  The name of your carbon trading platform
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="platformUrl" className="flex items-center">
                  <Globe className="mr-2 h-4 w-4" />
                  Platform URL
                </Label>
                <Input
                  id="platformUrl"
                  type="url"
                  value={settings.platformUrl}
                  onChange={(e) => handleInputChange("platformUrl", e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  The URL of your carbon trading platform
                </p>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="maintenanceMode">
                    Maintenance Mode
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Put the platform in maintenance mode (only admins can access)
                  </p>
                </div>
                <Switch
                  id="maintenanceMode"
                  checked={settings.maintenanceMode}
                  onCheckedChange={(checked) => handleInputChange("maintenanceMode", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allowNewRegistrations">
                    Allow New Registrations
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow new users to register on the platform
                  </p>
                </div>
                <Switch
                  id="allowNewRegistrations"
                  checked={settings.allowNewRegistrations}
                  onCheckedChange={(checked) => handleInputChange("allowNewRegistrations", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allowNewListings">
                    Allow New Listings
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow users to create new carbon credit listings
                  </p>
                </div>
                <Switch
                  id="allowNewListings"
                  checked={settings.allowNewListings}
                  onCheckedChange={(checked) => handleInputChange("allowNewListings", checked)}
                />
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="fees">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Fees & Transaction Limits</AnimatedCardTitle>
              <AnimatedCardDescription>
                Configure platform fees and transaction limits
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="transactionFeePercent" className="flex items-center">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Transaction Fee Percentage
                </Label>
                <div className="flex items-center">
                  <Input
                    id="transactionFeePercent"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={settings.transactionFeePercent}
                    onChange={(e) => handleInputChange("transactionFeePercent", parseFloat(e.target.value))}
                  />
                  <span className="ml-2">%</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Percentage fee charged on each transaction
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="listingFeeAmount" className="flex items-center">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Listing Fee Amount
                </Label>
                <div className="flex items-center">
                  <span className="mr-2">$</span>
                  <Input
                    id="listingFeeAmount"
                    type="number"
                    min="0"
                    step="1"
                    value={settings.listingFeeAmount}
                    onChange={(e) => handleInputChange("listingFeeAmount", parseFloat(e.target.value))}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Fixed fee charged for creating a new carbon credit listing
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="minimumTransactionAmount" className="flex items-center">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Minimum Transaction Amount
                </Label>
                <div className="flex items-center">
                  <span className="mr-2">$</span>
                  <Input
                    id="minimumTransactionAmount"
                    type="number"
                    min="0"
                    step="1"
                    value={settings.minimumTransactionAmount}
                    onChange={(e) => handleInputChange("minimumTransactionAmount", parseFloat(e.target.value))}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Minimum amount allowed for a single transaction
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maximumTransactionAmount" className="flex items-center">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Maximum Transaction Amount
                </Label>
                <div className="flex items-center">
                  <span className="mr-2">$</span>
                  <Input
                    id="maximumTransactionAmount"
                    type="number"
                    min="0"
                    step="1000"
                    value={settings.maximumTransactionAmount}
                    onChange={(e) => handleInputChange("maximumTransactionAmount", parseFloat(e.target.value))}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Maximum amount allowed for a single transaction
                </p>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="verification">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Verification Settings</AnimatedCardTitle>
              <AnimatedCardDescription>
                Configure verification requirements and processes
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="requireVerificationForTrading">
                    Require Verification for Trading
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Require organizations to be verified before they can trade carbon credits
                  </p>
                </div>
                <Switch
                  id="requireVerificationForTrading"
                  checked={settings.requireVerificationForTrading}
                  onCheckedChange={(checked) => handleInputChange("requireVerificationForTrading", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="autoApproveVerification">
                    Auto-Approve Verification
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically approve verification requests (not recommended for production)
                  </p>
                </div>
                <Switch
                  id="autoApproveVerification"
                  checked={settings.autoApproveVerification}
                  onCheckedChange={(checked) => handleInputChange("autoApproveVerification", checked)}
                />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label className="flex items-center">
                  <FileCheck className="mr-2 h-4 w-4" />
                  Required Document Types
                </Label>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="docBusinessRegistration"
                      checked={settings.verificationDocumentTypes.includes("BUSINESS_REGISTRATION")}
                      onChange={(e) => {
                        const updatedTypes = e.target.checked
                          ? [...settings.verificationDocumentTypes, "BUSINESS_REGISTRATION"]
                          : settings.verificationDocumentTypes.filter(type => type !== "BUSINESS_REGISTRATION");
                        handleInputChange("verificationDocumentTypes", updatedTypes);
                      }}
                    />
                    <Label htmlFor="docBusinessRegistration">Business Registration</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="docTaxCertificate"
                      checked={settings.verificationDocumentTypes.includes("TAX_CERTIFICATE")}
                      onChange={(e) => {
                        const updatedTypes = e.target.checked
                          ? [...settings.verificationDocumentTypes, "TAX_CERTIFICATE"]
                          : settings.verificationDocumentTypes.filter(type => type !== "TAX_CERTIFICATE");
                        handleInputChange("verificationDocumentTypes", updatedTypes);
                      }}
                    />
                    <Label htmlFor="docTaxCertificate">Tax Certificate</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="docIdentityProof"
                      checked={settings.verificationDocumentTypes.includes("IDENTITY_PROOF")}
                      onChange={(e) => {
                        const updatedTypes = e.target.checked
                          ? [...settings.verificationDocumentTypes, "IDENTITY_PROOF"]
                          : settings.verificationDocumentTypes.filter(type => type !== "IDENTITY_PROOF");
                        handleInputChange("verificationDocumentTypes", updatedTypes);
                      }}
                    />
                    <Label htmlFor="docIdentityProof">Identity Proof</Label>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  Document types required for organization verification
                </p>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="blockchain">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Blockchain Settings</AnimatedCardTitle>
              <AnimatedCardDescription>
                Configure blockchain network and gas settings
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="defaultNetwork" className="flex items-center">
                  <Wallet className="mr-2 h-4 w-4" />
                  Default Network
                </Label>
                <Select
                  value={settings.defaultNetwork}
                  onValueChange={(value) => handleInputChange("defaultNetwork", value)}
                >
                  <SelectTrigger id="defaultNetwork">
                    <SelectValue placeholder="Select network" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ethereum">Ethereum</SelectItem>
                    <SelectItem value="polygon">Polygon</SelectItem>
                    <SelectItem value="arbitrum">Arbitrum</SelectItem>
                    <SelectItem value="optimism">Optimism</SelectItem>
                    <SelectItem value="base">Base</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Default blockchain network for carbon credit tokenization
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="useTestnet">
                    Use Testnet
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Use testnet instead of mainnet (for development and testing)
                  </p>
                </div>
                <Switch
                  id="useTestnet"
                  checked={settings.useTestnet}
                  onCheckedChange={(checked) => handleInputChange("useTestnet", checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gasLimit" className="flex items-center">
                  <Wallet className="mr-2 h-4 w-4" />
                  Gas Limit
                </Label>
                <Input
                  id="gasLimit"
                  type="number"
                  min="0"
                  step="1000"
                  value={settings.gasLimit}
                  onChange={(e) => handleInputChange("gasLimit", parseInt(e.target.value))}
                />
                <p className="text-sm text-muted-foreground">
                  Default gas limit for transactions
                </p>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="email">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Email Settings</AnimatedCardTitle>
              <AnimatedCardDescription>
                Configure email notifications and addresses
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="emailNotificationsEnabled">
                    Email Notifications
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Enable email notifications for platform events
                  </p>
                </div>
                <Switch
                  id="emailNotificationsEnabled"
                  checked={settings.emailNotificationsEnabled}
                  onCheckedChange={(checked) => handleInputChange("emailNotificationsEnabled", checked)}
                />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="adminEmailAddress" className="flex items-center">
                  <Mail className="mr-2 h-4 w-4" />
                  Admin Email Address
                </Label>
                <Input
                  id="adminEmailAddress"
                  type="email"
                  value={settings.adminEmailAddress}
                  onChange={(e) => handleInputChange("adminEmailAddress", e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Email address for administrative notifications
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supportEmailAddress" className="flex items-center">
                  <Mail className="mr-2 h-4 w-4" />
                  Support Email Address
                </Label>
                <Input
                  id="supportEmailAddress"
                  type="email"
                  value={settings.supportEmailAddress}
                  onChange={(e) => handleInputChange("supportEmailAddress", e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Email address for support inquiries
                </p>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="legal">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Legal Settings</AnimatedCardTitle>
              <AnimatedCardDescription>
                Configure terms of service and privacy policy
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="termsOfServiceUrl" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Terms of Service URL
                </Label>
                <Input
                  id="termsOfServiceUrl"
                  type="url"
                  value={settings.termsOfServiceUrl}
                  onChange={(e) => handleInputChange("termsOfServiceUrl", e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  URL to your platform's terms of service
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="privacyPolicyUrl" className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Privacy Policy URL
                </Label>
                <Input
                  id="privacyPolicyUrl"
                  type="url"
                  value={settings.privacyPolicyUrl}
                  onChange={(e) => handleInputChange("privacyPolicyUrl", e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  URL to your platform's privacy policy
                </p>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>
      </Tabs>

      {hasChanges() && (
        <div className="fixed bottom-4 right-4 p-4 bg-background border rounded-lg shadow-lg z-50">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <span>You have unsaved changes</span>
            <AnimatedButton 
              size="sm" 
              onClick={saveSettings} 
              disabled={isSaving}
              animationVariant="buttonTap"
            >
              {isSaving ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </>
              )}
            </AnimatedButton>
          </div>
        </div>
      )}
    </div>
  );
}
