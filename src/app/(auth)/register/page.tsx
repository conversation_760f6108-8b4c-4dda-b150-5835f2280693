import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { RegisterForm } from "./register-form";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import {
  Animated<PERSON>ard,
  Animated<PERSON>ardContent,
  Animated<PERSON><PERSON>Header,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  PageTransition
} from "@/components/ui/animated";

export const metadata: Metadata = {
  title: "Register | Carbonix",
  description: "Create a new Carbonix account",
};

export default function RegisterPage() {
  return (
    <PageTransition animationVariant="fadeIn">
      <div className="flex w-full flex-col items-center justify-center flex-grow">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[400px]">
          <AnimatedCard className="w-full">
            <AnimatedCardHeader className="space-y-1">
              <AnimatedCardTitle className="text-2xl font-semibold tracking-tight text-center">
                Create an account
              </AnimatedCardTitle>
              <AnimatedCardDescription className="text-center">
                Enter your details to create a new account
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <RegisterForm />
            </AnimatedCardContent>
            <AnimatedCardFooter className="flex justify-center">
              <p className="text-center text-sm text-muted-foreground">
                <Link
                  href="/login"
                  className="hover:text-brand underline underline-offset-4"
                >
                  Already have an account? Sign In
                </Link>
              </p>
            </AnimatedCardFooter>
          </AnimatedCard>
        </div>
      </div>
    </PageTransition>
  );
}
