"use client";

import { useRouter } from "next/navigation";
import <PERSON> from "next/link";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AnimatedCard<PERSON><PERSON><PERSON>,
  AnimatedButton,
  AnimatedInput,
  AnimatedFormItem,
  AnimatedFormMessage,
  PageTransition
} from "@/components/ui/animated";
import { toast } from "@/components/ui/use-toast";
import { ValidatedForm } from "@/components/forms/validated-form";
import { forgotPasswordSchema } from "@/lib/validation/schemas";
import { FormField } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

type ForgotPasswordFormValues = {
  email: string;
};

export default function ForgotPasswordPage() {
  const router = useRouter();

  // Default form values
  const defaultValues: ForgotPasswordFormValues = {
    email: "",
  };

  // Handle form submission
  const onSubmit = async (data: ForgotPasswordFormValues) => {
    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to request password reset");
      }

      toast({
        title: "Reset link sent",
        description: "If an account exists with that email, we've sent a password reset link.",
      });

      // Redirect to login after showing the toast
      setTimeout(() => {
        router.push("/login");
      }, 2000);

      return data;
    } catch (err) {
      console.error("Error requesting password reset:", err);
      throw new Error(err instanceof Error ? err.message : "Something went wrong");
    }
  };

  return (
    <PageTransition>
      <div className="flex w-full flex-col items-center justify-center flex-grow">
        <div className="w-full max-w-md space-y-4">
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle className="text-center">
                Forgot Password
              </AnimatedCardTitle>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <ValidatedForm
                schema={forgotPasswordSchema}
                defaultValues={defaultValues}
                onSubmit={onSubmit}
                className="space-y-4"
                animationVariant="fadeIn"
              >
                {({ control, formState, isSubmitting, formError }) => (
                  <>
                  {formError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{formError.message}</AlertDescription>
                    </Alert>
                  )}

                  <FormField
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <AnimatedFormItem>
                        <label
                          htmlFor="email"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Email
                        </label>
                        <AnimatedInput
                          id="email"
                          placeholder="<EMAIL>"
                          type="email"
                          autoCapitalize="none"
                          autoComplete="email"
                          autoCorrect="off"
                          disabled={isSubmitting}
                          {...field}
                        />
                        {formState.errors.email && (
                          <AnimatedFormMessage className="text-red-500">
                            {formState.errors.email.message as string}
                          </AnimatedFormMessage>
                        )}
                      </AnimatedFormItem>
                    )}
                  />

                  <AnimatedButton
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                    animationVariant="buttonTap"
                  >
                    {isSubmitting ? "Sending..." : "Send Reset Link"}
                  </AnimatedButton>
                  </>
                )}
              </ValidatedForm>
            </AnimatedCardContent>
          </AnimatedCard>

          <p className="px-8 text-center text-sm text-muted-foreground">
            <Link
              href="/login"
              className="hover:text-brand underline underline-offset-4"
            >
              Back to Login
            </Link>
          </p>
        </div>
      </div>
    </PageTransition>
  );
}
