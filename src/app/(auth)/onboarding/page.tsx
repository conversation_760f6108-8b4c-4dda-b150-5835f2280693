"use client";

import { useState } from "react";
import { z } from "zod";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/use-toast";
import {
  <PERSON><PERSON><PERSON>,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedInput,
  AnimatedFormMessage,
  PageTransition
} from "@/components/ui/animated";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
// Import other components
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";
import { SubscriptionForm } from "./subscription-form";
import { useOnboarding } from "@/hooks/use-onboarding";
import { OnboardingStep } from "@/lib/onboarding-state";
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  organizationNameSchema,
  urlSchema,
  simpleEmailSchema,
  phoneNumberSchema,
  countrySchema,
  addressSchema,
  citySchema,
  industrySchema
} from "@/lib/validation/schemas";

import { TeamInvitationForm } from "./team-form";
import { MultiStepOrganizationForm } from "@/components/onboarding/multi-step-organization-form";
import { OrganizationFormSkeleton } from "@/components/onboarding/organization-form-skeleton";

// Organization schema with validation
const onboardingSchema = z.object({
  organizationName: organizationNameSchema,
  description: z.string().optional(),
  website: urlSchema,
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  country: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  phoneNumber: phoneNumberSchema,
  industry: z.string().optional(),
  size: z.string().optional(),
  foundedYear: z.string().optional(),
  primaryContact: z.string().optional(),
  primaryContactEmail: simpleEmailSchema.optional().or(z.string().length(0)),
  primaryContactPhone: phoneNumberSchema,
});

type OnboardingFormValues = z.infer<typeof onboardingSchema>;

export default function OnboardingPage() {

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const {
    state,
    loading: loadingState,
    completeStep,
    skipStep,
    updateState
  } = useOnboarding(OnboardingStep.ORGANIZATION_DETAILS);

  const methods = useForm<OnboardingFormValues>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      size: "SMALL",
    },
  });

  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: currentYear - 1799 }, (_, i) => (currentYear - i).toString());

  async function onSubmit(data: OnboardingFormValues) {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.organizationName,
          description: data.description,
          website: data.website,
          legalName: data.legalName,
          registrationNumber: data.registrationNumber,
          taxId: data.taxId,
          country: data.country,
          address: data.address,
          city: data.city,
          state: data.state,
          postalCode: data.postalCode,
          phoneNumber: data.phoneNumber,
          industry: data.industry,
          size: data.size,
          foundedYear: data.foundedYear ? Number(data.foundedYear) : undefined,
          primaryContact: data.primaryContact,
          primaryContactEmail: data.primaryContactEmail,
          primaryContactPhone: data.primaryContactPhone,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || "An error occurred during onboarding");
        setIsLoading(false);
        return;
      }

      // Update the onboarding state with the organization ID
      await updateState({
        organizationId: result.organization.id
      });

      // Mark this step as complete
      await completeStep(OnboardingStep.ORGANIZATION_DETAILS);

      toast({
        title: "Organization created",
        description: "Your organization has been created successfully.",
      });
    } catch {
      setError("An error occurred. Please try again.");
      setIsLoading(false);
    }
  }

  const handleSkipStep = async () => {
    if (state.currentStep === OnboardingStep.ORGANIZATION_DETAILS) {
      setError("Organization setup is required and cannot be skipped.");
      return;
    }

    await skipStep(state.currentStep);
  };

  if (loadingState) {
    return (
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
          <OrganizationFormSkeleton />
        </div>
      </div>
    );
  }

  return (
    <PageTransition animationVariant="fadeIn">
      <div className="w-full flex-grow items-center">
        <div className="mx-auto h-fit flex w-full flex-col space-y-6 sm:w-[650px]">
        <div className="flex flex-col space-y-2 text-center w-full h-fit">
          <h1 className="text-2xl font-semibold tracking-tight h-fit">
            Complete Your Organization Profile
          </h1>
          <p className="text-sm text-muted-foreground h-fit">
            Set up your organization to start trading carbon credits
          </p>
        </div>

        <OnboardingProgress
          currentStep={state.currentStep}
          completedSteps={state.completedSteps}
          skippedSteps={state.skippedSteps}
        />

        <AnimatedCard className="h-fit w-full" animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>
              {state.currentStep === OnboardingStep.ORGANIZATION_DETAILS
                ? "Organization Details"
                : state.currentStep === OnboardingStep.TEAM_INVITATIONS
                ? "Invite Team Members"
                : "Subscription Plan"}
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            {state.currentStep === OnboardingStep.ORGANIZATION_DETAILS && (
              <MultiStepOrganizationForm
                onSuccess={async (organizationId) => {
                  // Update the onboarding state with the organization ID
                  await updateState({
                    organizationId: organizationId
                  });

                  // Mark this step as complete
                  await completeStep(OnboardingStep.ORGANIZATION_DETAILS);
                }}
              />
            )}

            {/* Team members invitation step */}
            {state.currentStep === OnboardingStep.TEAM_INVITATIONS && (
              <div className="space-y-4">
                {state.organizationId ? (
                  <>
                    <TeamInvitationForm
                      organizationId={state.organizationId}
                      onSuccess={() => completeStep(OnboardingStep.TEAM_INVITATIONS)}
                    />
                    <div className="flex justify-between mt-4">
                      <AnimatedButton
                        type="button"
                        variant="outline"
                        onClick={() => updateState({ currentStep: OnboardingStep.ORGANIZATION_DETAILS })}
                        animationVariant="buttonTap"
                      >
                        Back
                      </AnimatedButton>
                      <AnimatedButton
                        type="button"
                        onClick={() => handleSkipStep()}
                        animationVariant="buttonTap"
                      >
                        Skip & Continue
                      </AnimatedButton>
                    </div>
                  </>
                ) : (
                  <Alert className="bg-yellow-50 border-yellow-200">
                    <InfoIcon className="h-4 w-4 text-yellow-500" />
                    <AlertTitle>Organization Missing</AlertTitle>
                    <AlertDescription>
                      You need to create an organization first.
                      <AnimatedButton
                        variant="link"
                        className="p-0 ml-2 h-auto"
                        onClick={() => updateState({ currentStep: OnboardingStep.ORGANIZATION_DETAILS })}
                        animationVariant="buttonTap"
                      >
                        Go back
                      </AnimatedButton>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Subscription plan step */}
            {state.currentStep === OnboardingStep.SUBSCRIPTION && (
              <div className="space-y-4">
                {state.organizationId ? (
                  <>
                    <SubscriptionForm
                      organizationId={state.organizationId}
                      onSuccess={() => completeStep(OnboardingStep.SUBSCRIPTION)}
                    />
                    <div className="flex justify-between mt-4">
                      <AnimatedButton
                        type="button"
                        variant="outline"
                        onClick={() => updateState({ currentStep: OnboardingStep.TEAM_INVITATIONS })}
                        animationVariant="buttonTap"
                      >
                        Back
                      </AnimatedButton>
                      <AnimatedButton
                        type="button"
                        onClick={() => handleSkipStep()}
                        animationVariant="buttonTap"
                      >
                        Skip & Continue
                      </AnimatedButton>
                    </div>
                  </>
                ) : (
                  <Alert className="bg-yellow-50 border-yellow-200">
                    <InfoIcon className="h-4 w-4 text-yellow-500" />
                    <AlertTitle>Organization Missing</AlertTitle>
                    <AlertDescription>
                      You need to create an organization first.
                      <AnimatedButton
                        variant="link"
                        className="p-0 ml-2 h-auto"
                        onClick={() => updateState({ currentStep: OnboardingStep.ORGANIZATION_DETAILS })}
                        animationVariant="buttonTap"
                      >
                        Go back
                      </AnimatedButton>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </AnimatedCardContent>
        </AnimatedCard>

        {state.skippedSteps && state.skippedSteps.length > 0 && (
          <Alert variant="default" className="bg-blue-50 border-blue-200">
            <InfoIcon className="h-4 w-4 text-blue-500" />
            <AlertTitle>You have skipped steps</AlertTitle>
            <AlertDescription>
              <p className="mb-2">You can return to complete these steps later from your dashboard:</p>
              <ul className="list-disc list-inside">
                {state.skippedSteps.map((step) => (
                  <li key={step} className="text-sm">
                    {step === OnboardingStep.TEAM_INVITATIONS && "Team Invitations"}
                    {step === OnboardingStep.SUBSCRIPTION && "Subscription Plan"}
                    {step === OnboardingStep.WALLET_SETUP && "Wallet Setup"}
                    {step === OnboardingStep.VERIFICATION && "Verification Documents"}
                    <AnimatedButton
                      variant="link"
                      size="sm"
                      className="p-0 ml-2 h-auto"
                      onClick={() => updateState({ currentStep: step })}
                      animationVariant="buttonTap"
                    >
                      Complete now
                    </AnimatedButton>
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
    </PageTransition>
  );
}
