"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon, AlertCircle } from "lucide-react";
import { useOnboarding } from "@/hooks/use-onboarding";
import { OnboardingStep } from "@/lib/onboarding-state";
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress";
import { Skeleton } from "@/components/ui/skeleton";
import { GuidedWalletSetup } from "@/components/wallet/guided-wallet-setup";

export default function GuidedWalletPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const organizationId = searchParams.get("organizationId");

  const {
    state,
    loading: loadingState,
    completeStep,
    skipStep,
    updateState
  } = useOnboarding(OnboardingStep.WALLET_SETUP);

  useEffect(() => {
    // If organizationId is provided in URL but not in state, update the state
    if (organizationId && !state.organizationId) {
      updateState({ organizationId });
    }

    if (!organizationId && !state.organizationId) {
      router.push("/onboarding");
    }
  }, [organizationId, state.organizationId, router, updateState]);

  function handleSkipStep() {
    skipStep(OnboardingStep.WALLET_SETUP).then(() => {
      const currentOrgId = organizationId || state.organizationId;
      if (currentOrgId) {
        router.push(`/onboarding/verification?organizationId=${currentOrgId}`);
      } else {
        router.push("/dashboard");
      }
    });
  }

  if (loadingState) {
    return (
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
          <Skeleton className="h-[450px] w-full rounded-md" />
        </div>
      </div>
    );
  }

  if (!organizationId && !state.organizationId) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTitle>Missing organization ID</AlertTitle>
              <AlertDescription>
                Organization ID is missing. Please restart the onboarding process.
              </AlertDescription>
            </Alert>
            <div className="mt-4 flex justify-center">
              <Button onClick={() => router.push("/onboarding")}>
                Back to Onboarding
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-full w-full items-center">
      <div className="mx-auto h-fit flex w-full flex-col space-y-6 sm:w-[650px]">
        <div className="flex flex-col space-y-2 text-center w-full h-fit">
          <h1 className="text-2xl font-semibold tracking-tight h-fit">
            Set Up Your Organization Wallet
          </h1>
          <p className="text-sm text-muted-foreground h-fit">
            Configure your blockchain wallet to start trading carbon credits
          </p>
        </div>

        <OnboardingProgress
          currentStep={state.currentStep}
          completedSteps={state.completedSteps}
          skippedSteps={state.skippedSteps}
        />

        <GuidedWalletSetup />

        <div className="flex justify-center">
          <Button
            type="button"
            variant="outline"
            onClick={handleSkipStep}
          >
            Skip Wallet Setup & Continue
          </Button>
        </div>

        {state.skippedSteps && state.skippedSteps.length > 0 && (
          <Alert variant="default" className="bg-blue-50 border-blue-200">
            <InfoIcon className="h-4 w-4 text-blue-500" />
            <AlertTitle>You have skipped steps</AlertTitle>
            <AlertDescription>
              <p className="mb-2">You can return to complete these steps later from your dashboard:</p>
              <ul className="list-disc list-inside">
                {state.skippedSteps.map((step) => (
                  <li key={step} className="text-sm">
                    {step === OnboardingStep.TEAM_INVITATIONS && "Team Invitations"}
                    {step === OnboardingStep.SUBSCRIPTION && "Subscription Plan"}
                    {step === OnboardingStep.WALLET_SETUP && "Wallet Setup"}
                    {step === OnboardingStep.VERIFICATION && "Verification Documents"}
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 ml-2 h-auto"
                      onClick={() => updateState({ currentStep: step })}
                    >
                      Complete now
                    </Button>
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
