"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon, AlertCircle } from "lucide-react";
import { useOnboarding } from "@/hooks/use-onboarding";
import { OnboardingStep } from "@/lib/onboarding-state";
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress";
import { Skeleton } from "@/components/ui/skeleton";
import { ValidatedForm } from "@/components/forms/validated-form";
import { walletCreationSchema } from "@/lib/validation/blockchain-schemas";

type WalletFormValues = {
  walletType: "SMART_WALLET" | "REGULAR_WALLET";
  network: "ETHEREUM" | "POLYGON" | "ARBITRUM" | "OPTIMISM" | "BASE";
  securityLevel: "STANDARD" | "HIGH";
  testMode: boolean;
};

export function WalletContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const organizationId = searchParams.get("organizationId");

  const [walletCreated, setWalletCreated] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string | null>(null);
  const [testMode, setTestMode] = useState(true);
  const [walletType, setWalletType] = useState<"SMART_WALLET" | "REGULAR_WALLET">("SMART_WALLET");

  const {
    state,
    loading: loadingState,
    completeStep,
    skipStep,
    updateState
  } = useOnboarding(OnboardingStep.WALLET_SETUP);

  useEffect(() => {
    // If organizationId is provided in URL but not in state, update the state
    if (organizationId && !state.organizationId) {
      updateState({ organizationId });
    }

    if (!organizationId && !state.organizationId) {
      router.push("/onboarding");
    }
  }, [organizationId, state.organizationId, router, updateState]);

  async function onSubmit(data: WalletFormValues) {
    const currentOrgId = organizationId || state.organizationId;

    if (!currentOrgId) {
      throw new Error("Organization ID is missing. Please restart the onboarding process.");
    }

    try {
      const response = await fetch("/api/wallet/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: currentOrgId,
          walletType: data.walletType,
          network: data.network,
          securityLevel: data.securityLevel,
          testMode: data.testMode,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "An error occurred while setting up your wallet");
      }

      setWalletCreated(true);
      setWalletAddress(result.wallet.address);

      // Mark this step as completed in the onboarding state
      await completeStep(OnboardingStep.WALLET_SETUP);

      toast({
        title: "Wallet created",
        description: "Your organization wallet has been set up successfully.",
      });
    } catch (error) {
      console.error("Error creating wallet:", error);
      throw new Error(error instanceof Error ? error.message : "An error occurred. Please try again.");
    }
  }

  function handleComplete() {
    const currentOrgId = organizationId || state.organizationId;

    if (currentOrgId) {
      router.push(`/onboarding/verification?organizationId=${currentOrgId}`);
    } else {
      router.push("/dashboard");
    }
  }

  if (loadingState) {
    return (
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
          <Skeleton className="h-[450px] w-full rounded-md" />
        </div>
      </div>
    );
  }

  if (!organizationId && !state.organizationId) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTitle>Missing organization ID</AlertTitle>
              <AlertDescription>
                Organization ID is missing. Please restart the onboarding process.
              </AlertDescription>
            </Alert>
            <div className="mt-4 flex justify-center">
              <Button onClick={() => router.push("/onboarding")}>
                Back to Onboarding
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-full w-full items-center">
      <div className="mx-auto h-fit flex w-full flex-col space-y-6 sm:w-[650px]">
        <div className="flex flex-col space-y-2 text-center w-full h-fit">
          <h1 className="text-2xl font-semibold tracking-tight h-fit">
            Set Up Your Organization Wallet
          </h1>
          <p className="text-sm text-muted-foreground h-fit">
            Configure your blockchain wallet to start trading carbon credits
          </p>
        </div>

        <OnboardingProgress
          currentStep={state.currentStep}
          completedSteps={state.completedSteps}
          skippedSteps={state.skippedSteps}
        />

        <Card>
          <CardHeader>
            <CardTitle>Wallet Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            {!walletCreated ? (
              <ValidatedForm
                schema={walletCreationSchema}
                defaultValues={{
                  walletType: "SMART_WALLET",
                  network: "POLYGON",
                  securityLevel: "STANDARD",
                  testMode: true,
                }}
                onSubmit={onSubmit}
                className="space-y-6"
              >
                {({ control, formState, isSubmitting, formError, setValue, watch }) => {
                  // Update local state when form values change
                  const currentWalletType = watch("walletType");
                  const currentTestMode = watch("testMode");

                  if (currentWalletType !== walletType) {
                    setWalletType(currentWalletType);
                  }

                  if (currentTestMode !== testMode) {
                    setTestMode(currentTestMode);
                  }

                  return (
                    <>
                      {formError && (
                        <Alert variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>{formError.message}</AlertDescription>
                        </Alert>
                      )}

                      <Tabs defaultValue="testnet" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger
                            value="testnet"
                            onClick={() => setValue("testMode", true)}
                          >
                            Testnet (Recommended)
                          </TabsTrigger>
                          <TabsTrigger
                            value="mainnet"
                            onClick={() => setValue("testMode", false)}
                          >
                            Mainnet (Production)
                          </TabsTrigger>
                        </TabsList>
                        <TabsContent value="testnet" className="py-4">
                          <Alert className="mb-4">
                            <InfoIcon className="h-4 w-4" />
                            <AlertTitle>Test Environment</AlertTitle>
                            <AlertDescription>
                              Start with a testnet wallet to practice trading carbon credits without using real funds.
                              You can switch to mainnet later when you're ready.
                            </AlertDescription>
                          </Alert>
                        </TabsContent>
                        <TabsContent value="mainnet" className="py-4">
                          <Alert className="mb-4">
                            <InfoIcon className="h-4 w-4" />
                            <AlertTitle>Production Environment</AlertTitle>
                            <AlertDescription>
                              Mainnet wallets use real funds and are intended for actual carbon credit trading.
                              Make sure you understand the implications before proceeding.
                            </AlertDescription>
                          </Alert>
                        </TabsContent>
                      </Tabs>

                      <div className="space-y-4">
                        <div>
                          <h3 className="text-lg font-medium mb-2">Wallet Type</h3>
                          <RadioGroup
                            defaultValue="SMART_WALLET"
                            onValueChange={(value) =>
                              setValue("walletType", value as "SMART_WALLET" | "REGULAR_WALLET")
                            }
                            className="grid grid-cols-1 gap-4 md:grid-cols-2"
                          >
                            <div className="rounded-lg border p-4 cursor-pointer hover:border-primary">
                              <div className="flex items-start space-x-3">
                                <RadioGroupItem value="SMART_WALLET" id="smart" />
                                <div className="grid gap-1">
                                  <Label htmlFor="smart" className="font-medium">
                                    Smart Wallet (Recommended)
                                  </Label>
                                  <p className="text-sm text-muted-foreground">
                                    Enhanced security features, team management capabilities, and recovery options.
                                    Perfect for organizations.
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="rounded-lg border p-4 cursor-pointer hover:border-primary">
                              <div className="flex items-start space-x-3">
                                <RadioGroupItem value="REGULAR_WALLET" id="regular" />
                                <div className="grid gap-1">
                                  <Label htmlFor="regular" className="font-medium">
                                    Regular Wallet
                                  </Label>
                                  <p className="text-sm text-muted-foreground">
                                    Standard Ethereum wallet with basic functionality. Simpler to set up but
                                    with fewer security features.
                                  </p>
                                </div>
                              </div>
                            </div>
                          </RadioGroup>
                          {formState.errors.walletType && (
                            <p className="text-sm text-destructive">{formState.errors.walletType.message as string}</p>
                          )}
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-2">Select Network</h3>
                          <Select
                            defaultValue="POLYGON"
                            onValueChange={(value) =>
                              setValue("network", value as "ETHEREUM" | "POLYGON" | "ARBITRUM" | "OPTIMISM" | "BASE")
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select network" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="POLYGON">
                                {testMode ? "Polygon Mumbai (Testnet)" : "Polygon"}
                              </SelectItem>
                              <SelectItem value="ETHEREUM">
                                {testMode ? "Ethereum Sepolia (Testnet)" : "Ethereum"}
                              </SelectItem>
                              <SelectItem value="ARBITRUM">
                                {testMode ? "Arbitrum Goerli (Testnet)" : "Arbitrum"}
                              </SelectItem>
                              <SelectItem value="OPTIMISM">
                                {testMode ? "Optimism Goerli (Testnet)" : "Optimism"}
                              </SelectItem>
                              <SelectItem value="BASE">
                                {testMode ? "Base Goerli (Testnet)" : "Base"}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          {formState.errors.network && (
                            <p className="text-sm text-destructive">{formState.errors.network.message as string}</p>
                          )}
                        </div>

                        {walletType === "SMART_WALLET" && (
                          <div>
                            <h3 className="text-lg font-medium mb-2">Security Level</h3>
                            <RadioGroup
                              defaultValue="STANDARD"
                              onValueChange={(value) =>
                                setValue("securityLevel", value as "STANDARD" | "HIGH")
                              }
                              className="grid grid-cols-1 gap-4 md:grid-cols-2"
                            >
                              <div className="rounded-lg border p-4 cursor-pointer hover:border-primary">
                                <div className="flex items-start space-x-3">
                                  <RadioGroupItem value="STANDARD" id="standard" />
                                  <div className="grid gap-1">
                                    <Label htmlFor="standard" className="font-medium">
                                      Standard
                                    </Label>
                                    <p className="text-sm text-muted-foreground">
                                      Basic security features with single recovery option.
                                      Suitable for most organizations.
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div className="rounded-lg border p-4 cursor-pointer hover:border-primary">
                                <div className="flex items-start space-x-3">
                                  <RadioGroupItem value="HIGH" id="high" />
                                  <div className="grid gap-1">
                                    <Label htmlFor="high" className="font-medium">
                                      High
                                    </Label>
                                    <p className="text-sm text-muted-foreground">
                                      Advanced security with multi-signature requirements and
                                      multiple recovery paths. Recommended for large transactions.
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </RadioGroup>
                            {formState.errors.securityLevel && (
                              <p className="text-sm text-destructive">{formState.errors.securityLevel.message as string}</p>
                            )}
                          </div>
                        )}
                      </div>

                      <div className="flex justify-between">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => router.push(`/onboarding/subscription?organizationId=${organizationId || state.organizationId}`)}
                        >
                          Back
                        </Button>
                        <div className="space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => skipStep(OnboardingStep.WALLET_SETUP)}
                          >
                            Skip & Continue
                          </Button>
                          <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? "Setting up your wallet..." : "Create Wallet"}
                          </Button>
                        </div>
                      </div>
                    </>
                  );
                }}
              </ValidatedForm>
            ) : (
              <div className="space-y-6">
                <div className="rounded-lg bg-green-50 p-6 text-center">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                    <svg
                      className="h-6 w-6 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <h3 className="mt-3 text-lg font-medium text-green-800">
                    Wallet Created Successfully
                  </h3>
                  <p className="mt-1 text-sm text-green-700">
                    Your organization wallet is ready to use
                  </p>
                </div>

                {walletAddress && (
                  <div className="rounded-lg border p-4">
                    <p className="text-sm font-medium mb-1">Wallet Address</p>
                    <div className="bg-gray-50 p-3 rounded-md font-mono text-sm break-all">
                      {walletAddress}
                    </div>
                  </div>
                )}

                <div className="rounded-lg border p-4 space-y-3">
                  <h4 className="font-medium">Next Steps</h4>
                  <ul className="space-y-1 text-sm list-disc pl-5">
                    <li>Visit your dashboard to start trading carbon credits</li>
                    <li>Review your organization's verification status</li>
                    <li>Set up additional team members and permissions</li>
                    <li>
                      {testMode
                        ? "Get testnet tokens from the faucet to start practicing"
                        : "Fund your wallet to start trading"}
                    </li>
                  </ul>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleComplete}>
                    {organizationId ? 'Continue to Verification' : 'Go to Dashboard'}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {state.skippedSteps && state.skippedSteps.length > 0 && (
          <Alert variant="default" className="bg-blue-50 border-blue-200">
            <InfoIcon className="h-4 w-4 text-blue-500" />
            <AlertTitle>You have skipped steps</AlertTitle>
            <AlertDescription>
              <p className="mb-2">You can return to complete these steps later from your dashboard:</p>
              <ul className="list-disc list-inside">
                {state.skippedSteps.map((step) => (
                  <li key={step} className="text-sm">
                    {step === OnboardingStep.TEAM_INVITATIONS && "Team Invitations"}
                    {step === OnboardingStep.SUBSCRIPTION && "Subscription Plan"}
                    {step === OnboardingStep.WALLET_SETUP && "Wallet Setup"}
                    {step === OnboardingStep.VERIFICATION && "Verification Documents"}
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 ml-2 h-auto"
                      onClick={() => updateState({ currentStep: step })}
                    >
                      Complete now
                    </Button>
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
