"use client";

import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";

// Import the guided wallet page with dynamic loading
const GuidedWalletPage = dynamic(() => import("./guided-wallet-page"), {
  loading: () => (
    <div className="container flex h-full w-full flex-col items-center">
      <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
        <Skeleton className="h-8 w-3/4 mx-auto" />
        <Skeleton className="h-4 w-1/2 mx-auto" />
        <Skeleton className="h-[450px] w-full rounded-md" />
      </div>
    </div>
  ),
  ssr: false,
});

export default function WalletSetupPage() {
  return (
    <Suspense fallback={
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
          <Skeleton className="h-[450px] w-full rounded-md" />
        </div>
      </div>
    }>
      <GuidedWalletPage />
    </Suspense>
  );
}
