"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, ArrowRight, InfoIcon, PartyPopper } from "lucide-react";
import { useOnboarding } from "@/hooks/use-onboarding";
import { OnboardingStep } from "@/lib/onboarding-state";
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress";
import { useConfetti } from "@/hooks/use-confetti";
import { Confetti } from "@/components/ui/confetti";

export function CompleteContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const organizationId = searchParams.get("organizationId");
  const [organizationDetails, setOrganizationDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isActive, startConfetti } = useConfetti();

  const {
    state,
    loading: loadingState
  } = useOnboarding(OnboardingStep.COMPLETE);

  useEffect(() => {
    if (!organizationId) {
      setError("Organization ID is missing. Please restart the onboarding process.");
      setIsLoading(false);
      return;
    }

    // Fetch organization details
    const fetchOrganizationDetails = async () => {
      try {
        const response = await fetch(`/api/organizations/${organizationId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch organization details");
        }

        const data = await response.json();
        setOrganizationDetails(data.organization);

        // Start confetti animation when organization details are loaded
        setTimeout(() => {
          startConfetti();
        }, 500); // Small delay for better user experience
      } catch (err) {
        setError("Failed to fetch organization details. Please try again.");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizationDetails();
  }, [organizationId]);

  if (error) {
    return (
      <div className="flex h-screen w-screen flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push("/onboarding")}>
            Back to Onboarding
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[650px]">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">Loading organization details...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-screen flex-col items-center py-10">
      {isActive && (
        <Confetti
          particleCount={200}
          spread={90}
          origin={{ x: 0.5, y: 0.3 }}
          colors={['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0']}
          duration={5000}
        />
      )}
      <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight flex items-center justify-center">
            <PartyPopper className="h-6 w-6 text-yellow-500 mr-2" />
            Onboarding Complete
            <PartyPopper className="h-6 w-6 text-yellow-500 ml-2" />
          </h1>
          <p className="text-sm text-muted-foreground">
            Your organization setup is complete. You're ready to start using the platform.
          </p>
        </div>

        <OnboardingProgress
          currentStep={state.currentStep}
          completedSteps={state.completedSteps}
          skippedSteps={state.skippedSteps}
        />

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
              Setup Complete
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="rounded-lg bg-green-50 p-6 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="mt-3 text-lg font-medium text-green-800">
                Congratulations! 🎉
              </h3>
              <p className="mt-1 text-sm text-green-700">
                <span className="font-semibold">{organizationDetails?.name}</span> has been successfully set up on our platform
              </p>
              <p className="mt-2 text-sm text-green-700">
                Welcome to the carbon trading ecosystem!
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Completed Steps</h3>
              <ul className="space-y-2">
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Organization profile created
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Team members invited
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Subscription plan selected
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Wallet configured
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Verification documents submitted
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Next Steps</h3>
              <ul className="space-y-2">
                <li className="flex items-center text-sm">
                  <ArrowRight className="h-4 w-4 text-primary mr-2" />
                  Visit your dashboard to start trading carbon credits
                </li>
                <li className="flex items-center text-sm">
                  <ArrowRight className="h-4 w-4 text-primary mr-2" />
                  Complete your organization profile with additional information
                </li>
                <li className="flex items-center text-sm">
                  <ArrowRight className="h-4 w-4 text-primary mr-2" />
                  Set up additional team members and permissions
                </li>
                <li className="flex items-center text-sm">
                  <ArrowRight className="h-4 w-4 text-primary mr-2" />
                  Explore available carbon credits in the marketplace
                </li>
              </ul>
            </div>

            <Alert>
              <AlertTitle>Verification Status: Pending</AlertTitle>
              <AlertDescription>
                Your verification documents are being reviewed. You'll be notified once the verification is complete.
              </AlertDescription>
            </Alert>

            <div className="flex justify-center mt-4">
              <Button
                onClick={() => router.push("/dashboard")}
                size="lg"
                className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-8 py-2 rounded-md shadow-md transition-all duration-300 hover:shadow-lg"
              >
                <PartyPopper className="h-5 w-5 mr-2" />
                Start Your Carbon Trading Journey
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
