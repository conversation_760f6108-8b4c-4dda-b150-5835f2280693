"use client";

import { Suspense } from "react";
import { CompleteContent } from "./complete-content";
import { Skeleton } from "@/components/ui/skeleton";

export default function OnboardingCompletePage() {
  return (
    <Suspense fallback={
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[650px]">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">Loading organization details...</p>
          </div>
        </div>
      </div>
    }>
      <CompleteContent />
    </Suspense>
  );
}
