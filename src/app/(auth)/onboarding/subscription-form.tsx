"use client";

import { useState } from "react";
import { CheckCircle, CheckCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

interface SubscriptionFormProps {
  organizationId: string;
  onSuccess: () => void;
}

interface PlanFeature {
  name: string;
  included: boolean;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: string;
  priceDetail: string;
  features: PlanFeature[];
  popular?: boolean;
}

export function SubscriptionForm({ organizationId, onSuccess }: SubscriptionFormProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>("basic");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const plans: SubscriptionPlan[] = [
    {
      id: "free",
      name: "Free Tier",
      description: "Perfect for individual users and small organizations",
      price: "Free",
      priceDetail: "No credit card required",
      features: [
        { name: "Access to carbon credit marketplace", included: true },
        { name: "Basic analytics", included: true },
        { name: "Up to 3 team members", included: true },
        { name: "Limited monthly transactions", included: true },
        { name: "Email support", included: true },
        { name: "API access", included: false },
        { name: "Advanced analytics", included: false },
        { name: "White-label solution", included: false },
      ],
    },
    {
      id: "basic",
      name: "Basic",
      description: "For growing organizations with moderate trading volume",
      price: "₹9000",
      priceDetail: "per month",
      popular: true,
      features: [
        { name: "Access to carbon credit marketplace", included: true },
        { name: "Basic analytics", included: true },
        { name: "Up to 10 team members", included: true },
        { name: "Unlimited monthly transactions", included: true },
        { name: "Priority email support", included: true },
        { name: "API access", included: true },
        { name: "Advanced analytics", included: false },
        { name: "White-label solution", included: false },
      ],
    },
    {
      id: "premium",
      name: "Premium",
      description: "For organizations with high trading volume",
      price: "₹15000",
      priceDetail: "per month",
      features: [
        { name: "Access to carbon credit marketplace", included: true },
        { name: "Basic analytics", included: true },
        { name: "Unlimited team members", included: true },
        { name: "Unlimited monthly transactions", included: true },
        { name: "Priority email & phone support", included: true },
        { name: "API access", included: true },
        { name: "Advanced analytics", included: true },
        { name: "White-label solution", included: true },
      ],
    },
  ];

  const handleSelectPlan = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch("/api/subscriptions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId,
          plan: selectedPlan.toUpperCase(),
        }),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "Failed to select subscription plan");
      }

      toast({
        title: "Subscription plan selected",
        description: `You've selected the ${selectedPlan} plan.`,
      });

      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to select plan",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-sm text-muted-foreground">
        Choose a subscription plan that best fits your organization's needs.
        You can upgrade or downgrade your plan at any time.
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {plans.map((plan) => (
          <Card
            key={plan.id}
            className={cn(
              "cursor-pointer transition-colors border-2",
              selectedPlan === plan.id
                ? "border-primary"
                : "hover:border-muted-foreground/20"
            )}
            onClick={() => setSelectedPlan(plan.id)}
          >
            {plan.popular && (
              <div className="absolute top-0 right-0 bg-primary text-primary-foreground text-xs py-1 px-3 rounded-bl-md">
                Popular
              </div>
            )}
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <div className="mt-2 space-y-1">
                <div className="text-3xl font-bold">{plan.price}</div>
                <div className="text-xs text-muted-foreground">{plan.priceDetail}</div>
                <p className="text-sm text-muted-foreground pt-2">{plan.description}</p>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex items-center text-sm">
                    {feature.included ? (
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <CheckCircle2 className="h-4 w-4 mr-2 text-gray-300" />
                    )}
                    <span className={feature.included ? "" : "text-muted-foreground"}>
                      {feature.name}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                variant={selectedPlan === plan.id ? "default" : "outline"}
                className="w-full"
                onClick={() => setSelectedPlan(plan.id)}
              >
                {selectedPlan === plan.id ? "Selected" : "Select Plan"}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {error && <p className="text-sm text-red-500 mt-2">{error}</p>}

      <div className="flex justify-end">
        <Button
          onClick={handleSelectPlan}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Processing..." : "Confirm Subscription"}
        </Button>
      </div>
    </div>
  );
}
