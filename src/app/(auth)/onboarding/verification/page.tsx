"use client";

import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { VerificationContent } from "./verification-content";

export default function VerificationPage() {
  return (
    <Suspense fallback={
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
          <Skeleton className="h-[450px] w-full rounded-md" />
        </div>
      </div>
    }>
      <VerificationContent />
    </Suspense>
  );
}
