"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { VerificationForm } from "@/app/(auth)/onboarding/verification-form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useOnboarding } from "@/hooks/use-onboarding";
import { OnboardingStep } from "@/lib/onboarding-state";
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress";

export function VerificationContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const organizationId = searchParams.get("organizationId");

  const {
    state,
    loading: loadingState,
    completeStep,
    skipStep,
    updateState
  } = useOnboarding(OnboardingStep.VERIFICATION);

  // If organizationId is provided in URL but not in state, update the state
  useEffect(() => {
    if (organizationId && !state.organizationId) {
      updateState({ organizationId });
    }
  }, [organizationId, state.organizationId, updateState]);

  // Handle missing organization ID
  useEffect(() => {
    if (!organizationId) {
      router.push("/onboarding");
    }
  }, [organizationId, router]);

  const handleSkipStep = async () => {
    await skipStep(OnboardingStep.VERIFICATION);
  };

  if (loadingState) {
    return (
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <div className="h-8 w-3/4 mx-auto bg-muted animate-pulse rounded" />
          <div className="h-4 w-1/2 mx-auto bg-muted animate-pulse rounded" />
          <div className="h-[450px] w-full bg-muted animate-pulse rounded-md" />
        </div>
      </div>
    );
  }

  if (!organizationId) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[650px]">
          <Alert variant="destructive">
            <AlertTitle>Missing organization ID</AlertTitle>
            <AlertDescription>
              Organization ID is missing. Please restart the onboarding process.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-screen-lg mx-auto py-10">
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">Organization Verification</h1>
          <p className="text-muted-foreground">
            Upload verification documents to complete your organization setup
          </p>
        </div>

        <OnboardingProgress
          currentStep={state.currentStep}
          completedSteps={state.completedSteps}
          skippedSteps={state.skippedSteps}
        />

        <Card>
          <CardHeader>
            <CardTitle>Verification Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <InfoIcon className="h-4 w-4" />
              <AlertTitle>Verification Required</AlertTitle>
              <AlertDescription>
                Your organization needs to be verified before you can start trading carbon credits.
                Please upload the required documents to complete the verification process.
              </AlertDescription>
            </Alert>

            <VerificationForm
              organizationId={state.organizationId || organizationId || ""}
              onSuccess={() => completeStep(OnboardingStep.VERIFICATION)}
            />

            <div className="flex justify-between mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/onboarding")}
              >
                Back
              </Button>
              <Button
                type="button"
                variant="ghost"
                onClick={handleSkipStep}
              >
                Skip for now
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
