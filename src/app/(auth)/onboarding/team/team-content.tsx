"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";
import { TeamInvitationForm } from "../team-form";
import { EnhancedTeamInvitationForm } from "@/components/onboarding/enhanced-team-invitation-form";
import { useOnboarding } from "@/hooks/use-onboarding";
import { OnboardingStep } from "@/lib/onboarding-state";
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress";
import { Skeleton } from "@/components/ui/skeleton";

export function TeamContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const organizationId = searchParams.get("organizationId");
  // State is managed by the useOnboarding hook

  const {
    state,
    loading: loadingState,
    completeStep,
    skipStep,
    updateState
  } = useOnboarding(OnboardingStep.TEAM_INVITATIONS);

  useEffect(() => {
    // If organizationId is provided in URL but not in state, update the state
    if (organizationId && !state.organizationId) {
      updateState({ organizationId });
    }
  }, [organizationId, state.organizationId, updateState]);

  const handleSkipStep = async () => {
    await skipStep(OnboardingStep.TEAM_INVITATIONS);
  };

  if (loadingState) {
    return (
      <div className="container flex h-full w-full flex-col items-center">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[650px]">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
          <Skeleton className="h-[450px] w-full rounded-md" />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full items-center">
      <div className="mx-auto h-fit flex w-full flex-col space-y-6 sm:w-[650px]">
        <div className="flex flex-col space-y-2 text-center w-full h-fit">
          <h1 className="text-2xl font-semibold tracking-tight h-fit">
            Invite Your Team Members
          </h1>
          <p className="text-sm text-muted-foreground h-fit">
            Add team members to collaborate on your carbon trading platform
          </p>
        </div>

        <OnboardingProgress
          currentStep={state.currentStep}
          completedSteps={state.completedSteps}
          skippedSteps={state.skippedSteps}
        />

        <Card className="h-fit w-full">
          <CardHeader>
            <CardTitle>Invite Team Members</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {(state.organizationId || organizationId) ? (
                <>
                  <EnhancedTeamInvitationForm
                    organizationId={state.organizationId || organizationId || ""}
                    onSuccess={() => completeStep(OnboardingStep.TEAM_INVITATIONS)}
                    onSkip={() => handleSkipStep()}
                  />
                  <div className="flex justify-start mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.push('/onboarding')}
                    >
                      Back to Previous Step
                    </Button>
                  </div>
                </>
              ) : (
                <Alert className="bg-yellow-50 border-yellow-200">
                  <InfoIcon className="h-4 w-4 text-yellow-500" />
                  <AlertTitle>Organization Missing</AlertTitle>
                  <AlertDescription>
                    You need to create an organization first.
                    <Button
                      variant="link"
                      className="p-0 ml-2 h-auto"
                      onClick={() => router.push('/onboarding')}
                    >
                      Go back
                    </Button>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {state.skippedSteps && state.skippedSteps.length > 0 && (
          <Alert variant="default" className="bg-blue-50 border-blue-200">
            <InfoIcon className="h-4 w-4 text-blue-500" />
            <AlertTitle>You have skipped steps</AlertTitle>
            <AlertDescription>
              <p className="mb-2">You can return to complete these steps later from your dashboard:</p>
              <ul className="list-disc list-inside">
                {state.skippedSteps.map((step) => (
                  <li key={step} className="text-sm">
                    {step === OnboardingStep.TEAM_INVITATIONS && "Team Invitations"}
                    {step === OnboardingStep.SUBSCRIPTION && "Subscription Plan"}
                    {step === OnboardingStep.WALLET_SETUP && "Wallet Setup"}
                    {step === OnboardingStep.VERIFICATION && "Verification Documents"}
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 ml-2 h-auto"
                      onClick={() => updateState({ currentStep: step })}
                    >
                      Complete now
                    </Button>
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
