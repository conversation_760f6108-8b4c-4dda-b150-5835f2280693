'use client';

import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, AlertCircle } from 'lucide-react';
import { ValidatedForm } from '@/components/forms/validated-form';
import { organizationSchema } from '@/lib/validation/schemas';
import { Alert, AlertDescription } from '@/components/ui/alert';

type OrganizationFormValues = {
  name: string;
  industry: string;
  size: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE';
  country: string;
  description?: string;
  website?: string;
  legalName?: string;
  registrationNumber?: string;
  taxId?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  phoneNumber?: string;
  foundedYear?: number;
  primaryContact?: string;
  primaryContactEmail?: string;
  primaryContactPhone?: string;
};

export function OrganizationForm() {
  const router = useRouter();

  // Form submission handler
  const handleCreateOrganization = async (data: OrganizationFormValues) => {
    try {
      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create organization');
      }

      const result = await response.json();

      toast({
        title: 'Organization Created',
        description: 'Your organization has been created successfully.',
      });

      // Redirect to subscription selection page
      router.push(`/onboarding/subscription?organizationId=${result.id}`);
    } catch (error) {
      console.error('Error creating organization:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to create organization');
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Create Your Organization</CardTitle>
        <CardDescription>
          Provide essential details to get started. You can complete your profile later.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ValidatedForm
          schema={organizationSchema}
          defaultValues={{
            name: '',
            industry: '',
            size: 'SMALL',
            country: '',
            description: '',
            website: '',
            legalName: '',
            registrationNumber: '',
            taxId: '',
            address: '',
            city: '',
            state: '',
            postalCode: '',
            phoneNumber: '',
            foundedYear: new Date().getFullYear(),
            primaryContact: '',
            primaryContactEmail: '',
            primaryContactPhone: '',
          }}
          onSubmit={handleCreateOrganization}
          className="space-y-8"
        >
          {({ control, formState, isSubmitting, formError }) => (
            <>
              {/* Display form error if any */}
              {formError && (
                <Alert variant="destructive" className="py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{formError.message}</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* Essential Information */}
                <div className="space-y-4 md:col-span-2">
                  <h3 className="text-lg font-medium">Essential Information</h3>

                  <FormField
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization Name*</FormLabel>
                        <FormControl>
                          <Input placeholder="Acme Corporation" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name your organization is known by
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={control}
                      name="industry"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Industry*</FormLabel>
                          <FormControl>
                            <Input placeholder="Energy, Manufacturing, etc." {...field} />
                          </FormControl>
                          <FormDescription>
                            The industry your organization operates in
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="size"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Organization Size*</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select organization size" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="SMALL">Small (&lt;50 employees)</SelectItem>
                              <SelectItem value="MEDIUM">Medium (50-250 employees)</SelectItem>
                              <SelectItem value="LARGE">Large (251-1000 employees)</SelectItem>
                              <SelectItem value="ENTERPRISE">Enterprise (&gt;1000 employees)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country*</FormLabel>
                        <FormControl>
                          <Input placeholder="United States" {...field} />
                        </FormControl>
                        <FormDescription>
                          The country where your organization is based
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Basic Information */}
                <div className="space-y-4 md:col-span-2">
                  <h3 className="text-lg font-medium">Basic Information</h3>

                  <FormField
                    control={control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tell us about your organization"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          A brief description of your organization
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={control}
                      name="website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website</FormLabel>
                          <FormControl>
                            <Input placeholder="https://example.com" {...field} />
                          </FormControl>
                          <FormDescription>
                            Optional - you can add this later
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="foundedYear"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Founded Year</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder={new Date().getFullYear().toString()}
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Additional Information (Optional) */}
                <div className="space-y-4 md:col-span-2 mt-8">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Additional Information</h3>
                    <span className="text-sm text-muted-foreground">Optional - can be completed later</span>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={control}
                      name="legalName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Legal Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Acme Corporation Inc." {...field} />
                          </FormControl>
                          <FormDescription>
                            The registered legal name of your organization
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input placeholder="+****************" {...field} />
                          </FormControl>
                          <FormDescription>
                            A contact phone number for your organization
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center mt-8">
                <p className="text-sm text-muted-foreground">
                  You can complete your profile after registration
                </p>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Organization
                </Button>
              </div>
            </>
          )}
        </ValidatedForm>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-gray-500">
          * Required fields
        </p>
      </CardFooter>
    </Card>
  );
}
