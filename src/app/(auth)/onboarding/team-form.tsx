"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { UserRole } from "@/lib/authorization";

const inviteSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.string().refine(value => ["ADMIN", "USER"].includes(value), {
    message: "Please select a valid role",
  }),
});

type InviteFormValues = z.infer<typeof inviteSchema>;

interface TeamInvitationFormProps {
  organizationId: string;
  onSuccess: () => void;
}

export function TeamInvitationForm({ organizationId, onSuccess }: TeamInvitationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitedEmails, setInvitedEmails] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<InviteFormValues>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      role: "USER",
    },
  });

  const onSubmit = async (data: InviteFormValues) => {
    if (invitedEmails.includes(data.email)) {
      toast({
        title: "Already invited",
        description: `${data.email} has already been invited.`,
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch("/api/invitations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          role: data.role,
          organizationId,
        }),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "Failed to send invitation");
      }

      setInvitedEmails([...invitedEmails, data.email]);
      toast({
        title: "Invitation sent",
        description: `An invitation has been sent to ${data.email}.`,
      });

      reset({ email: "", role: "USER" });
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to send invitation",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleContinue = () => {
    onSuccess();
  };

  const removeInvite = (email: string) => {
    setInvitedEmails(invitedEmails.filter((e) => e !== email));
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground mb-4">
        Invite team members to collaborate with you on your CarbonX platform.
        You can add more team members later from your organization settings.
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-2">
            <Input
              id="email"
              placeholder="Email address"
              disabled={isSubmitting}
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
            )}
          </div>

          <div>
            <Select
              onValueChange={(value) => setValue("role", value)}
              defaultValue="USER"
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ORGANIZATION_ADMIN">Admin</SelectItem>
                <SelectItem value="USER">Member</SelectItem>
              </SelectContent>
            </Select>
            {errors.role && (
              <p className="text-sm text-red-500 mt-1">{errors.role.message}</p>
            )}
          </div>
        </div>

        {error && <p className="text-sm text-red-500">{error}</p>}

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handleContinue}
          >
            Continue
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Sending..." : "Send Invitation"}
          </Button>
        </div>
      </form>

      {invitedEmails.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Invitations sent:</h3>
          <div className="flex flex-wrap gap-2">
            {invitedEmails.map((email) => (
              <Badge key={email} variant="secondary" className="pl-2 pr-1 py-1">
                {email}
                <button
                  type="button"
                  onClick={() => removeInvite(email)}
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
