'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// Define DocumentType enum since it's not available in @prisma/client
enum DocumentType {
  BUSINESS_REGISTRATION = 'BUSINESS_REGISTRATION',
  TAX_CERTIFICATE = 'TAX_CERTIFICATE',
  IDENTITY_PROOF = 'IDENTITY_PROOF',
  ADDRESS_PROOF = 'ADDRESS_PROOF',
  BANK_STATEMENT = 'BANK_STATEMENT',
  OTHER = 'OTHER',
}
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabe<PERSON>, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, X, Check, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Form schema
const verificationFormSchema = z.object({
  organizationId: z.string(),
  documents: z.array(
    z.object({
      name: z.string().min(2, 'Document name is required'),
      type: z.enum([
        'BUSINESS_REGISTRATION',
        'TAX_CERTIFICATE',
        'IDENTITY_PROOF',
        'ADDRESS_PROOF',
        'BANK_STATEMENT',
        'OTHER',
      ]),
      // Allow any string for URL in development environment
      url: process.env.NODE_ENV === 'development'
        ? z.string().min(1, 'Document URL is required')
        : z.string().url('Please enter a valid URL'),
      notes: z.string().optional(),
    })
  ).min(1, 'At least one document is required'),
});

type VerificationFormValues = z.infer<typeof verificationFormSchema>;

interface VerificationFormProps {
  organizationId: string;
  onSuccess?: () => void;
}

export function VerificationForm({ organizationId, onSuccess }: VerificationFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);

  // Initialize form
  const form = useForm<VerificationFormValues>({
    resolver: zodResolver(verificationFormSchema),
    defaultValues: {
      organizationId,
      documents: [
        {
          name: '',
          type: 'BUSINESS_REGISTRATION',
          url: '',
          notes: '',
        },
      ],
    },
  });

  // Document type labels
  const documentTypeLabels: Record<DocumentType, string> = {
    BUSINESS_REGISTRATION: 'Business Registration Certificate',
    TAX_CERTIFICATE: 'Tax Certificate',
    IDENTITY_PROOF: 'Identity Proof',
    ADDRESS_PROOF: 'Address Proof',
    BANK_STATEMENT: 'Bank Statement',
    OTHER: 'Other Document',
  };

  // Add a new document field
  const addDocument = () => {
    const documents = form.getValues('documents');
    form.setValue('documents', [
      ...documents,
      {
        name: '',
        type: 'OTHER',
        url: '',
        notes: '',
      },
    ]);
  };

  // Remove a document field
  const removeDocument = (index: number) => {
    const documents = form.getValues('documents');
    if (documents.length > 1) {
      form.setValue(
        'documents',
        documents.filter((_, i) => i !== index)
      );
    }
  };

  // Upload file to storage service
  const uploadFile = async (index: number, file: File) => {
    setUploadingIndex(index);

    try {
      // In development mode, simulate successful upload without actually uploading
      if (process.env.NODE_ENV === 'development') {
        // Simulate a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Create a mock URL for development
        const mockUrl = `/uploads/${Date.now()}-${file.name}`;

        // Update the form with the mock file URL
        const documents = form.getValues('documents');
        documents[index].url = mockUrl;
        documents[index].name = file.name;
        form.setValue('documents', documents);

        toast({
          title: 'Development Mode',
          description: 'File upload simulated in development mode.',
        });
      } else {
        // Create a FormData object to send the file
        const formData = new FormData();
        formData.append('file', file);
        formData.append('organizationId', organizationId);
        formData.append('documentType', form.getValues(`documents.${index}.type`));

        // Upload the file
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to upload file');
        }

        const result = await response.json();

        // Update the form with the uploaded file URL
        const documents = form.getValues('documents');
        documents[index].url = result.url;
        documents[index].name = file.name;
        form.setValue('documents', documents);

        toast({
          title: 'File Uploaded',
          description: 'Your file has been uploaded successfully.',
        });
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to upload file',
        variant: 'destructive',
      });
    } finally {
      setUploadingIndex(null);
    }
  };

  // Handle file input change
  const handleFileChange = async (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await uploadFile(index, file);
    }
  };

  // Form submission handler
  const onSubmit = async (data: VerificationFormValues) => {
    setIsSubmitting(true);

    try {
      // In development mode, simulate successful submission
      if (process.env.NODE_ENV === 'development') {
        // Simulate a delay
        await new Promise(resolve => setTimeout(resolve, 800));

        console.log('Development mode - Verification data:', data);

        // Mark verification step as completed in onboarding state
        const onboardingResponse = await fetch('/api/onboarding', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'complete',
            step: 'verification'
          }),
        });

        if (!onboardingResponse.ok) {
          console.error('Failed to update onboarding state');
        }

        toast({
          title: 'Development Mode',
          description: 'Verification submitted successfully in development mode.',
        });

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        } else {
          // Redirect to completion page if no callback provided
          router.push(`/onboarding/complete?organizationId=${organizationId}`);
        }
      } else {
        // Submit verification documents in production
        const verifyResponse = await fetch('/api/organizations/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!verifyResponse.ok) {
          const errorData = await verifyResponse.json();
          throw new Error(errorData.message || 'Failed to submit verification');
        }

        // Mark verification step as completed in onboarding state
        const onboardingResponse = await fetch('/api/onboarding', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'complete',
            step: 'verification'
          }),
        });

        if (!onboardingResponse.ok) {
          console.error('Failed to update onboarding state, but verification was submitted');
        }

        toast({
          title: 'Verification Submitted',
          description: 'Your organization verification documents have been submitted successfully.',
        });

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        } else {
          // Redirect to completion page if no callback provided
          router.push(`/onboarding/complete?organizationId=${organizationId}`);
        }
      }
    } catch (error) {
      console.error('Error submitting verification:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to submit verification',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Verify Your Organization</CardTitle>
        <CardDescription>
          Upload documents to verify your organization&apos;s identity
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Verification Required</AlertTitle>
          <AlertDescription>
            Your organization needs to be verified before you can start trading carbon credits.
            Please upload the required documents to complete the verification process.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Verification Documents</h3>
                <Button
                  type="button"
                  variant="outline"
                  onClick={addDocument}
                >
                  Add Document
                </Button>
              </div>

              {form.getValues('documents').map((doc, index) => (
                <div key={`document-${doc.type || ''}-${index}`} className="rounded-md border p-4">
                  <div className="flex items-start justify-between">
                    <h4 className="text-sm font-medium">Document {index + 1}</h4>
                    {form.getValues('documents').length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeDocument(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name={`documents.${index}.type`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Document Type*</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select document type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(documentTypeLabels).map(([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`documents.${index}.name`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Document Name*</FormLabel>
                          <FormControl>
                            <Input placeholder="Business Registration" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="mt-4">
                    <FormField
                      control={form.control}
                      name={`documents.${index}.url`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Document File*</FormLabel>
                          <div className="flex items-center gap-2">
                            <FormControl>
                              <Input
                                {...field}
                                value={field.value}
                                className="hidden"
                              />
                            </FormControl>
                            <div className="relative flex-1">
                              <Input
                                type="file"
                                onChange={(e) => handleFileChange(index, e)}
                                className="cursor-pointer"
                                disabled={uploadingIndex === index}
                              />
                              {field.value && (
                                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                                  <Check className="h-4 w-4 text-green-500" />
                                </div>
                              )}
                            </div>
                            {uploadingIndex === index && (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            )}
                          </div>
                          {field.value && (
                            <FormDescription>
                              File uploaded: {form.getValues(`documents.${index}.name`)}
                            </FormDescription>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="mt-4">
                    <FormField
                      control={form.control}
                      name={`documents.${index}.notes`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Additional information about this document"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Optional notes about this document
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Submit Verification
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col items-start space-y-2 text-sm text-gray-500">
        <p>
          * Required fields
        </p>
        <p>
          Verification typically takes 1-3 business days. You will be notified once your
          organization has been verified.
        </p>
      </CardFooter>
    </Card>
  );
}
