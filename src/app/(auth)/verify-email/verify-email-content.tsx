"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, AlertCircle } from "lucide-react";

export function VerifyEmailContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    async function verifyEmail() {
      try {
        const response = await fetch("/api/auth/verify-email", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to verify email");
        }

        setSuccess(data.message || "Email verified successfully");

        // Redirect based on whether user needs onboarding
        setTimeout(() => {
          if (data.requiresOnboarding) {
            router.push("/onboarding");
          } else {
            router.push("/login?verified=true");
          }
        }, 2000);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    }

    if (token) {
      verifyEmail();
    } else {
      setError("No verification token provided");
      setIsLoading(false);
    }
  }, [token, router]);

  return (
    <div className="flex w-full flex-col items-center justify-center flex-grow">
      <div className="w-full max-w-md space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Email Verification</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
              </div>
            )}

            {error && (
              <div className="flex flex-col items-center space-y-2 p-2 text-center">
                <AlertCircle className="h-10 w-10 text-red-500" />
                <p className="text-red-500">{error}</p>
                <Button
                  variant="outline"
                  onClick={() => router.push("/login")}
                  className="mt-2"
                >
                  Back to Login
                </Button>
              </div>
            )}

            {success && (
              <div className="flex flex-col items-center space-y-2 p-2 text-center">
                <CheckCircle className="h-10 w-10 text-green-500" />
                <p className="text-green-500">{success}</p>
                <p className="text-sm text-muted-foreground">
                  Redirecting you to continue your setup...
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
