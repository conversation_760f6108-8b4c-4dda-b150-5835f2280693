"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

// Component that uses useSearchParams
function VerifyEmailContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    async function verifyEmail() {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/verify-email?token=${token}`, {
          method: 'GET',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to verify email');
        }

        const data = await response.json();
        setSuccess(data.message || 'Email verified successfully');

        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Something went wrong');
      } finally {
        setIsLoading(false);
      }
    }

    if (token) {
      verifyEmail();
    } else {
      setError('No verification token provided');
    }
  }, [token, router]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Email Verification</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
              </div>
            )}

            {error && (
              <div className="rounded-md bg-destructive/15 p-4 text-center text-destructive">
                <p>{error}</p>
              </div>
            )}

            {success && (
              <div className="rounded-md bg-green-100 p-4 text-center text-green-800">
                <p>{success}</p>
                <p className="mt-2 text-sm">Redirecting to login page...</p>
              </div>
            )}

            {!isLoading && !success && (
              <div className="text-center">
                <p className="mb-4">
                  {token
                    ? "Verifying your email address..."
                    : "No verification token provided. Please check your email for the verification link."}
                </p>
                <Button
                  variant="outline"
                  onClick={() => router.push('/login')}
                  className="mt-2"
                >
                  Back to Login
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center text-sm text-muted-foreground">
          <p>
            Need help?{" "}
            <Link
              href="/contact"
              className="font-medium text-primary underline underline-offset-4 hover:text-primary/90"
            >
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Email Verification</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Main page component with Suspense boundary
export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <VerifyEmailContent />
    </Suspense>
  );
}
