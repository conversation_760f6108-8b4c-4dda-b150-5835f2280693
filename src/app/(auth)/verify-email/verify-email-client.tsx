"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

// Client component that uses useSearchParams
export default function VerifyEmailClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    async function verifyEmail() {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/verify-email?token=${token}`, {
          method: 'GET',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to verify email');
        }

        setSuccess("Email verified successfully");
        setTimeout(() => {
          router.push("/login");
        }, 3000);
      } catch {
        setError("An error occurred. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }

    if (token) {
      verifyEmail();
    }
  }, [token, router]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            Email Verification
          </h1>
          <p className="text-sm text-muted-foreground">
            {token ? "Verifying your email address..." : "Please check your email for a verification link"}
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              Verify Email
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            {isLoading && (
              <div className="flex flex-col items-center space-y-4">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                <p>Verifying your email...</p>
              </div>
            )}

            {error && (
              <div className="space-y-4">
                <p className="text-red-500">{error}</p>
                <p>The verification link may have expired or is invalid.</p>
                <Link href="/login">
                  <Button variant="outline" className="mt-2">
                    Back to Login
                  </Button>
                </Link>
              </div>
            )}

            {success && (
              <div className="space-y-4">
                <p className="text-green-500">{success}</p>
                <p>You will be redirected to the login page shortly.</p>
                <Link href="/login">
                  <Button className="mt-2">
                    Go to Login
                  </Button>
                </Link>
              </div>
            )}

            {!token && !isLoading && !error && !success && (
              <div className="space-y-4">
                <p>
                  We&apos;ve sent a verification link to your email address.
                  Please check your inbox and click the link to verify your email.
                </p>
                <p className="text-sm text-muted-foreground">
                  If you don&apos;t see the email, check your spam folder.
                </p>
                <Link href="/login">
                  <Button variant="outline" className="mt-2">
                    Back to Login
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
