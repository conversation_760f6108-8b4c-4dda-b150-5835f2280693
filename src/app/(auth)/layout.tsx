import { ReactNode } from "react";
import { AppHeader } from "@/components/ui/app-header";

interface AuthLayoutProps {
  children: ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <main className="flex flex-col gap-6 p-4 min-h-screen">
      <AppHeader />
      <section className="w-full flex flex-col gap-2 flex-grow">
      {children}
      </section>
    </main>
  );
}
