"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Check<PERSON>ircle, Loader2 } from "lucide-react";
import Link from "next/link";
import {
  AnimatedButton,
  AnimatedInput,
  AnimatedFormItem,
  AnimatedFormMessage,
  AnimatedFormDescription
} from "@/components/ui/animated";
import { RequiredLabel } from "@/components/ui/required-label";
import { ValidatedForm } from "@/components/forms/validated-form";
import { loginSchema } from "@/lib/validation/schemas";
import { FormField } from "@/components/ui/form";
import { AlertCircle } from "lucide-react";
import { useCallback } from "react";

type LoginFormValues = {
  email: string;
  password: string;
};

export function LoginFormContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const registered = searchParams.get("registered") === "true";
  const verified = searchParams.get("verified") === "true";

  const handleLogin = useCallback(async (data: LoginFormValues) => {
    try {
      // Sign in with credentials
      const result = await signIn("credentials", {
        email: data?.email,
        password: data?.password,
        redirect: false,
      });

      // Handle authentication error
      if (result?.error) {
        // Check for specific error messages from the auth provider
        if (result.error === "CredentialsSignin") {
          throw new Error("Invalid email or password");
        } else {
          throw new Error(result.error || "Authentication failed. Please try again.");
        }
      }

      // Check if user needs to complete onboarding
      const userResponse = await fetch("/api/me");

      if (!userResponse.ok) {
        // Handle non-200 responses
        const statusText = userResponse.statusText || "";
        throw new Error(`Failed to fetch user data (${userResponse.status}${statusText ? ': ' + statusText : ''}). Please try again.`);
      }

      const userData = await userResponse.json();

      // Add proper null checks
      if (userData && !userData.error) {
        // Check if user has an organization
        if (!userData.organizationId) {
          router.push("/onboarding");
        } else {
          router.push("/dashboard");
        }

        router.refresh();
      } else {
        // Handle error in user data
        throw new Error(userData?.error || "Failed to load user data. Please try again.");
      }
    } catch (error: any) {
      // Log the error for debugging
      console.error("Login error:", error);

      // Improve error handling with better error messages
      const errorMessage = error?.message || "An unexpected error occurred. Please try again.";
      throw new Error(errorMessage);
    }
  }, [router]);

  return (
    <div className="grid gap-4">
      {registered && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription className="text-green-700">
            Registration successful! Please check your email to verify your account.
          </AlertDescription>
        </Alert>
      )}

      {verified && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription className="text-green-700">
            Email verification successful! You can now log in.
          </AlertDescription>
        </Alert>
      )}

      <ValidatedForm
        schema={loginSchema}
        defaultValues={{ email: "", password: "" }}
        onSubmit={handleLogin}
        className="space-y-4"
        animationVariant="fadeIn"
        showErrorSummary={false}
        formOptions={{
          showToast: false,
        }}
      >
        {({ control, formState, isSubmitting, formError }) => (
          <div className="grid gap-6">
            {/* Display form error if any */}
            {formError && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{formError.message}</AlertDescription>
              </Alert>
            )}

            {/* Account Credentials */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Account Credentials</h3>

              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <AnimatedFormItem>
                    <RequiredLabel htmlFor="email">
                      Email
                    </RequiredLabel>
                    <AnimatedInput
                      id="email"
                      placeholder="<EMAIL>"
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      disabled={isSubmitting}
                      {...field}
                    />
                    <AnimatedFormDescription className="text-xs text-muted-foreground">
                      Enter the email address associated with your account
                    </AnimatedFormDescription>
                    {formState.errors.email && (
                      <AnimatedFormMessage className="text-destructive">
                        {formState.errors.email.message as string}
                      </AnimatedFormMessage>
                    )}
                  </AnimatedFormItem>
                )}
              />

              <FormField
                control={control}
                name="password"
                render={({ field }) => (
                  <AnimatedFormItem>
                    <div className="flex items-center justify-between">
                      <RequiredLabel htmlFor="password">
                        Password
                      </RequiredLabel>
                      <Link
                        href="/forgot-password"
                        className="text-sm text-primary underline-offset-4 hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <AnimatedInput
                      id="password"
                      placeholder="********"
                      type="password"
                      autoComplete="current-password"
                      disabled={isSubmitting}
                      {...field}
                    />
                    <AnimatedFormDescription className="text-xs text-muted-foreground">
                      Enter your password to access your account
                    </AnimatedFormDescription>
                    {formState.errors.password && (
                      <AnimatedFormMessage className="text-destructive">
                        {formState.errors.password.message as string}
                      </AnimatedFormMessage>
                    )}
                  </AnimatedFormItem>
                )}
              />
            </div>

            <AnimatedButton
              type="submit"
              className="w-full mt-2"
              disabled={isSubmitting}
              animationVariant="buttonTap"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </AnimatedButton>
          </div>
        )}
      </ValidatedForm>
    </div>
  );
}
