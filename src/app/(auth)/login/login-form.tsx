"use client";

import { Suspense } from "react";
import { LoginFormContent } from "./login-form-content";

export function LoginForm() {
  return (
    <Suspense fallback={
      <div className="grid gap-6">
        <div className="h-4 w-full bg-gray-100 animate-pulse rounded"></div>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="h-4 w-16 bg-gray-100 animate-pulse rounded"></div>
            <div className="h-10 w-full bg-gray-100 animate-pulse rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <div className="h-4 w-16 bg-gray-100 animate-pulse rounded"></div>
              <div className="h-4 w-24 bg-gray-100 animate-pulse rounded"></div>
            </div>
            <div className="h-10 w-full bg-gray-100 animate-pulse rounded"></div>
          </div>
          <div className="h-10 w-full bg-gray-100 animate-pulse rounded"></div>
        </div>
      </div>
    }>
      <LoginFormContent />
    </Suspense>
  );
}
