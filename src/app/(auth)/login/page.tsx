import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { LoginForm } from "./login-form";
import { PageTransition } from "@/components/ui/animated";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Animated<PERSON>ard, Animated<PERSON>ardContent, AnimatedCardHeader, AnimatedCardTitle, AnimatedCardDescription, AnimatedCardFooter } from "@/components/ui/animated";

export const metadata: Metadata = {
  title: "Login | Carbonix",
  description: "Login to your Carbonix account",
};

export default function LoginPage() {
  return (
    <PageTransition animationVariant="fadeIn">
      <div className="flex w-full flex-col items-center justify-center flex-grow">
        <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[400px]">
          <AnimatedCard className="w-full">
            <AnimatedCardHeader className="space-y-1">
              <AnimatedCardTitle className="text-2xl font-semibold tracking-tight text-center">
                Welcome back
              </AnimatedCardTitle>
              <AnimatedCardDescription className="text-center">
                Enter your credentials to sign in to your account
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <LoginForm />
            </AnimatedCardContent>
            <AnimatedCardFooter className="flex justify-center">
              <p className="text-center text-sm text-muted-foreground">
                <Link
                  href="/register"
                  className="hover:text-brand underline underline-offset-4"
                >
                  Don&apos;t have an account? Sign Up
                </Link>
              </p>
            </AnimatedCardFooter>
          </AnimatedCard>
        </div>
      </div>
    </PageTransition>
  );
}
