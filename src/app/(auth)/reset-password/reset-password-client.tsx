"use client";

import { useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedFormMessage,
  AnimatedFormItem,
  StaggeredList
} from "@/components/ui/animated";
import { motion } from "framer-motion";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";
import { ValidatedForm } from "@/components/forms/validated-form";
import { forgotPasswordSchema, resetPasswordSchema } from "@/lib/validation/schemas";
import { FormField } from "@/components/ui/form";

type RequestResetFormValues = {
  email: string;
};

type ResetPasswordFormValues = {
  password: string;
  confirmPassword: string;
  token: string;
};

function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [success, setSuccess] = useState<string | null>(null);

  // Default form values for request reset
  const requestResetDefaultValues: RequestResetFormValues = {
    email: "",
  };

  // Default form values for password reset
  const resetPasswordDefaultValues: ResetPasswordFormValues = {
    password: "",
    confirmPassword: "",
    token: token || "",
  };

  // Handle request reset form submission
  const onRequestReset = async (data: RequestResetFormValues) => {
    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to request password reset");
      }

      setSuccess(
        "If an account exists with that email, we've sent a password reset link."
      );

      return data;
    } catch (err) {
      console.error("Error requesting password reset:", err);
      throw new Error(err instanceof Error ? err.message : "Something went wrong");
    }
  };

  // Handle reset password form submission
  const onResetPassword = async (data: ResetPasswordFormValues) => {
    if (!token) {
      throw new Error("Invalid reset token");
    }

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          password: data.password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to reset password");
      }

      setSuccess("Password reset successful. You can now log in with your new password.");

      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push("/login");
      }, 3000);

      return data;
    } catch (err) {
      console.error("Error resetting password:", err);
      throw new Error(err instanceof Error ? err.message : "Something went wrong");
    }
  };

  // Animation variants for form elements
  const inputVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  };

  const buttonVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.3, delay: 0.2 } },
    whileHover: { scale: 1.03, transition: { duration: 0.2 } },
    whileTap: { scale: 0.97, transition: { duration: 0.1 } },
  };

  const successVariants = {
    initial: { opacity: 0, height: 0 },
    animate: { opacity: 1, height: "auto", transition: { duration: 0.3 } },
  };

  return (
    <div className="flex w-full flex-col items-center justify-center flex-grow">
      <div className="w-full max-w-md space-y-4">
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle className="text-center">
              {token ? "Reset Your Password" : "Reset Password"}
            </AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            {!token ? (
              <ValidatedForm
                schema={forgotPasswordSchema}
                defaultValues={requestResetDefaultValues}
                onSubmit={onRequestReset}
                className="space-y-4"
                animationVariant="fadeIn"
              >
                {({ control, formState, isSubmitting, formError }) => (
                  <StaggeredList className="space-y-4" staggerDelay={0.1}>
                    {formError && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{formError.message}</AlertDescription>
                      </Alert>
                    )}

                    <FormField
                      control={control}
                      name="email"
                      render={({ field }) => (
                        <AnimatedFormItem>
                          <label
                            htmlFor="email"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Email
                          </label>
                          <Input
                            id="email"
                            placeholder="<EMAIL>"
                            type="email"
                            autoCapitalize="none"
                            autoComplete="email"
                            autoCorrect="off"
                            disabled={isSubmitting}
                            className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                            {...field}
                          />
                          {formState.errors.email && (
                            <AnimatedFormMessage className="text-red-500">
                              {formState.errors.email.message as string}
                            </AnimatedFormMessage>
                          )}
                        </AnimatedFormItem>
                      )}
                    />

                    {success && (
                      <motion.p
                        variants={successVariants}
                        className="text-sm text-green-500 px-3 py-2 bg-green-50 rounded-md"
                      >
                        {success}
                      </motion.p>
                    )}

                    <motion.div variants={buttonVariants}>
                      <Button
                        type="submit"
                        className="w-full transition-all duration-200 hover:shadow-md"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <span className="flex items-center justify-center gap-2">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Sending...
                          </span>
                        ) : "Send Reset Link"}
                      </Button>
                    </motion.div>
                  </StaggeredList>
                )}
              </ValidatedForm>
            ) : (
              <ValidatedForm
                schema={resetPasswordSchema}
                defaultValues={resetPasswordDefaultValues}
                onSubmit={onResetPassword}
                className="space-y-4"
                animationVariant="fadeIn"
              >
                {({ control, formState, isSubmitting, formError }) => (
                  <StaggeredList className="space-y-4" staggerDelay={0.1}>
                    {formError && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{formError.message}</AlertDescription>
                      </Alert>
                    )}

                    <FormField
                      control={control}
                      name="password"
                      render={({ field }) => (
                        <AnimatedFormItem>
                          <label
                            htmlFor="password"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            New Password
                          </label>
                          <Input
                            id="password"
                            placeholder="********"
                            type="password"
                            autoComplete="new-password"
                            disabled={isSubmitting}
                            className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                            {...field}
                          />
                          {formState.errors.password && (
                            <AnimatedFormMessage className="text-red-500">
                              {formState.errors.password.message as string}
                            </AnimatedFormMessage>
                          )}
                        </AnimatedFormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <AnimatedFormItem>
                          <label
                            htmlFor="confirmPassword"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Confirm Password
                          </label>
                          <Input
                            id="confirmPassword"
                            placeholder="********"
                            type="password"
                            autoComplete="new-password"
                            disabled={isSubmitting}
                            className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                            {...field}
                          />
                          {formState.errors.confirmPassword && (
                            <AnimatedFormMessage className="text-red-500">
                              {formState.errors.confirmPassword.message as string}
                            </AnimatedFormMessage>
                          )}
                        </AnimatedFormItem>
                      )}
                    />

                    {success && (
                      <motion.p
                        variants={successVariants}
                        className="text-sm text-green-500 px-3 py-2 bg-green-50 rounded-md"
                      >
                        {success}
                      </motion.p>
                    )}

                    <motion.div variants={buttonVariants}>
                      <Button
                        type="submit"
                        className="w-full transition-all duration-200 hover:shadow-md"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <span className="flex items-center justify-center gap-2">
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Resetting...
                          </span>
                        ) : "Reset Password"}
                      </Button>
                    </motion.div>
                  </StaggeredList>
                )}
              </ValidatedForm>
            )}
          </AnimatedCardContent>
        </AnimatedCard>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="px-8 text-center text-sm text-muted-foreground"
        >
          <Link
            href="/login"
            className="hover:text-brand underline underline-offset-4 transition-colors duration-200"
          >
            Back to Login
          </Link>
        </motion.p>
      </div>
    </div>
  );
}

// Wrap the client component in a Suspense boundary to satisfy Next.js requirements
export default function ResetPasswordClient() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">Loading...</div>}>
      <ResetPasswordForm />
    </Suspense>
  );
}
