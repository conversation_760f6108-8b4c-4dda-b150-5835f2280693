"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { ArrowLeft, AlertTriangle, Save, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/spinner";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedButton,
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedCardHeader,
  AnimatedCardTitle,
  PageTransition,
  AnimatedForm
} from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";

interface ModifyOrderClientProps {
  userName: string;
  orderId: string;
}

interface Order {
  id: string;
  type: string;
  side: string;
  status: string;
  quantity: number;
  price: number;
  timeInForce: string;
  notes: string | null;
  listing: {
    id: string;
    title: string;
    carbonCredit: {
      id: string;
      name: string;
      quantity: number;
      price: number;
    };
  };
}

const modifyOrderSchema = z.object({
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive").optional(),
  timeInForce: z.enum(["GTC", "IOC", "FOK", "GTD"], {
    required_error: "Please select a time in force option",
  }),
  notes: z.string().optional(),
});

type ModifyOrderFormValues = z.infer<typeof modifyOrderSchema>;

export default function ModifyOrderClient({ userName, orderId }: ModifyOrderClientProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalPrice, setTotalPrice] = useState(0);

  const form = useForm<ModifyOrderFormValues>({
    resolver: zodResolver(modifyOrderSchema),
    defaultValues: {
      quantity: 0,
      price: 0,
      timeInForce: "GTC",
      notes: "",
    },
  });

  useEffect(() => {
    async function fetchOrderDetails() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/orders/${orderId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch order details");
        }
        
        const data = await response.json();
        setOrder(data.order);
        
        // Set form default values
        form.reset({
          quantity: data.order.quantity,
          price: data.order.type === "LIMIT" ? data.order.price : undefined,
          timeInForce: data.order.timeInForce,
          notes: data.order.notes || "",
        });
        
        // Calculate total price
        if (data.order.type === "LIMIT") {
          setTotalPrice(data.order.quantity * data.order.price);
        } else {
          setTotalPrice(data.order.quantity * data.order.listing.carbonCredit.price);
        }
      } catch (error) {
        console.error("Error fetching order details:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchOrderDetails();
  }, [orderId, form]);

  // Calculate total price when quantity or price changes
  useEffect(() => {
    const quantity = form.watch("quantity");
    const price = form.watch("price") || (order?.listing.carbonCredit.price || 0);
    
    setTotalPrice(quantity * price);
  }, [form.watch("quantity"), form.watch("price"), order]);

  const onSubmit = async (values: ModifyOrderFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/orders/${orderId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          quantity: values.quantity,
          price: order?.type === "LIMIT" ? values.price : undefined,
          timeInForce: values.timeInForce,
          notes: values.notes,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to modify order");
      }
      
      toast({
        title: "Order Modified",
        description: "Your order has been modified successfully",
      });
      
      router.push(`/marketplace/orders/${orderId}`);
    } catch (error) {
      console.error("Error modifying order:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to modify order",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage loadingComponent={<Spinner size="lg" />}>
        <div className="flex h-full items-center justify-center">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">{error}</p>
              <Button variant="outline" onClick={() => router.push(`/marketplace/orders/${orderId}`)}>
                Back to Order Details
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (!order) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">Order not found</p>
              <Button variant="outline" onClick={() => router.push("/marketplace/my-orders")}>
                Back to My Orders
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (order.status !== "ACTIVE") {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">
                This order cannot be modified because it is {order.status.toLowerCase()}
              </p>
              <Button variant="outline" onClick={() => router.push(`/marketplace/orders/${orderId}`)}>
                Back to Order Details
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Modify Order"
              description="Update your order details"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "My Orders", href: "/marketplace/my-orders" },
                { label: "Order Details", href: `/marketplace/orders/${orderId}` },
                { label: "Modify Order", href: `/marketplace/orders/${orderId}/modify`, isCurrent: true }
              ]}
            />
            <AnimatedButton
              variant="outline"
              onClick={() => router.push(`/marketplace/orders/${orderId}`)}
              animationVariant="buttonTap"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Order Details
            </AnimatedButton>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Modify Order</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Update the details of your {order.type.toLowerCase()} {order.side.toLowerCase()} order
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <AnimatedForm {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <FormField
                        control={form.control}
                        name="quantity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Quantity (tons)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                step="1"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Available: {order.listing.carbonCredit.quantity.toLocaleString()} tons
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {order.type === "LIMIT" && (
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Price per Ton ($)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0.01"
                                  step="0.01"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormDescription>
                                Current market price: ${order.listing.carbonCredit.price.toFixed(2)} / ton
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      <FormField
                        control={form.control}
                        name="timeInForce"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Time in Force</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select time in force" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="GTC">Good Till Cancelled (GTC)</SelectItem>
                                <SelectItem value="IOC">Immediate or Cancel (IOC)</SelectItem>
                                <SelectItem value="FOK">Fill or Kill (FOK)</SelectItem>
                                <SelectItem value="GTD">Good Till Date (GTD)</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Determines how long the order will remain active
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Notes (Optional)</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Add any additional notes about this order"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => router.push(`/marketplace/orders/${orderId}`)}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <Spinner className="mr-2 h-4 w-4" />
                              Updating...
                            </>
                          ) : (
                            <>
                              <Save className="mr-2 h-4 w-4" />
                              Update Order
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </AnimatedForm>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>

            <div>
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Order Summary</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium">Carbon Credit</h3>
                      <p>{order.listing.carbonCredit.name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Order Type</h3>
                      <p className="capitalize">{order.type.toLowerCase()}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Side</h3>
                      <p className="capitalize">{order.side.toLowerCase()}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Total Value</h3>
                      <p className="text-xl font-bold">
                        ${totalPrice.toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </p>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter>
                  <p className="text-xs text-muted-foreground">
                    Changes will take effect immediately after updating the order.
                  </p>
                </AnimatedCardFooter>
              </AnimatedCard>
            </div>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
