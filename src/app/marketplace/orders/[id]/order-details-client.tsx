"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { format } from "date-fns";
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  FileText, 
  Download, 
  Edit, 
  Trash2,
  ExternalLink
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  <PERSON><PERSON><PERSON>eader, 
  Di<PERSON><PERSON><PERSON><PERSON>, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Spinner } from "@/components/ui/spinner";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedButton,
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedCardHeader,
  AnimatedCardTitle,
  PageTransition,
  StaggeredList
} from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";

interface OrderDetailsClientProps {
  userName: string;
  orderId: string;
}

interface Order {
  id: string;
  type: string;
  side: string;
  status: string;
  quantity: number;
  price: number;
  timeInForce: string;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  userId: string;
  user: {
    name: string;
    email: string;
  };
  listing: {
    id: string;
    title: string;
    carbonCredit: {
      id: string;
      name: string;
      standard: string;
      vintage: number;
      quantity: number;
      organization: {
        name: string;
      };
    };
  };
  transactions: {
    id: string;
    type: string;
    amount: number;
    status: string;
    createdAt: string;
    transactionHash: string | null;
  }[];
}

export default function OrderDetailsClient({ userName, orderId }: OrderDetailsClientProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("details");
  const [isConfirmingCancel, setIsConfirmingCancel] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  useEffect(() => {
    async function fetchOrderDetails() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/orders/${orderId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch order details");
        }
        
        const data = await response.json();
        setOrder(data.order);
      } catch (error) {
        console.error("Error fetching order details:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchOrderDetails();
  }, [orderId]);

  const handleCancelOrder = async () => {
    try {
      setIsCancelling(true);
      const response = await fetch(`/api/orders/${orderId}/cancel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to cancel order");
      }
      
      toast({
        title: "Order Cancelled",
        description: "Your order has been cancelled successfully",
      });
      
      // Refresh order data
      const updatedResponse = await fetch(`/api/orders/${orderId}`);
      const updatedData = await updatedResponse.json();
      setOrder(updatedData.order);
      
      setIsConfirmingCancel(false);
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel order",
        variant: "destructive",
      });
    } finally {
      setIsCancelling(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "ACTIVE":
        return <Badge className="bg-green-500">Active</Badge>;
      case "PENDING":
        return <Badge variant="outline" className="border-yellow-500 text-yellow-500">Pending</Badge>;
      case "FILLED":
        return <Badge className="bg-blue-500">Filled</Badge>;
      case "CANCELLED":
        return <Badge variant="secondary">Cancelled</Badge>;
      case "EXPIRED":
        return <Badge variant="destructive">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy HH:mm:ss");
  };

  if (isLoading) {
    return (
      <ProtectedPage loadingComponent={<Spinner size="lg" />}>
        <div className="flex h-full items-center justify-center">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">{error}</p>
              <Button variant="outline" onClick={() => router.push("/marketplace/my-orders")}>
                Back to My Orders
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (!order) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">Order not found</p>
              <Button variant="outline" onClick={() => router.push("/marketplace/my-orders")}>
                Back to My Orders
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Order Details"
              description="View detailed information about your order"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "My Orders", href: "/marketplace/my-orders" },
                { label: "Order Details", href: `/marketplace/orders/${orderId}`, isCurrent: true }
              ]}
            />
            <AnimatedButton
              variant="outline"
              onClick={() => router.push("/marketplace/my-orders")}
              animationVariant="buttonTap"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to My Orders
            </AnimatedButton>
          </div>

          <div className="mb-6">
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center gap-2">
                <h2 className="text-2xl font-bold">Order #{order.id.substring(0, 8)}</h2>
                {getStatusBadge(order.status)}
              </div>
              <div className="flex flex-wrap gap-2">
                {order.status === "ACTIVE" && (
                  <>
                    <AnimatedButton
                      variant="outline"
                      onClick={() => router.push(`/marketplace/orders/${orderId}/modify`)}
                      animationVariant="buttonTap"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Modify Order
                    </AnimatedButton>
                    <Dialog open={isConfirmingCancel} onOpenChange={setIsConfirmingCancel}>
                      <DialogTrigger asChild>
                        <AnimatedButton
                          variant="destructive"
                          animationVariant="buttonTap"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Cancel Order
                        </AnimatedButton>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Cancel Order</DialogTitle>
                          <DialogDescription>
                            Are you sure you want to cancel this order? This action cannot be undone.
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsConfirmingCancel(false)}>
                            No, Keep Order
                          </Button>
                          <Button 
                            variant="destructive" 
                            onClick={handleCancelOrder}
                            disabled={isCancelling}
                          >
                            {isCancelling ? (
                              <>
                                <Spinner className="mr-2 h-4 w-4" />
                                Cancelling...
                              </>
                            ) : (
                              <>Yes, Cancel Order</>
                            )}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </>
                )}
                {order.status === "FILLED" && (
                  <AnimatedButton
                    variant="outline"
                    onClick={() => router.push(`/marketplace/orders/${orderId}/receipt`)}
                    animationVariant="buttonTap"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Receipt
                  </AnimatedButton>
                )}
              </div>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="details" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Order Details
              </TabsTrigger>
              <TabsTrigger value="transactions" className="flex items-center">
                <ExternalLink className="mr-2 h-4 w-4" />
                Transactions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Order Information</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <h3 className="font-medium">Order Type</h3>
                      <p className="capitalize">{order.type.toLowerCase()}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Side</h3>
                      <p className="capitalize">{order.side.toLowerCase()}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Quantity</h3>
                      <p>{order.quantity.toLocaleString()} tons</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Price</h3>
                      <p>${order.price.toFixed(2)} / ton</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Total Value</h3>
                      <p>${(order.quantity * order.price).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Time in Force</h3>
                      <p>{order.timeInForce}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Created At</h3>
                      <p>{formatDate(order.createdAt)}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Last Updated</h3>
                      <p>{formatDate(order.updatedAt)}</p>
                    </div>
                  </div>
                  {order.notes && (
                    <div className="mt-6">
                      <h3 className="font-medium">Notes</h3>
                      <p className="mt-1 whitespace-pre-wrap">{order.notes}</p>
                    </div>
                  )}
                </AnimatedCardContent>
              </AnimatedCard>

              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Carbon Credit Information</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <h3 className="font-medium">Carbon Credit</h3>
                      <p>{order.listing.carbonCredit.name}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Organization</h3>
                      <p>{order.listing.carbonCredit.organization.name}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Standard</h3>
                      <p>{order.listing.carbonCredit.standard}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Vintage</h3>
                      <p>{order.listing.carbonCredit.vintage}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <AnimatedButton
                      variant="outline"
                      onClick={() => router.push(`/marketplace/${order.listing.id}`)}
                      animationVariant="buttonTap"
                    >
                      View Listing
                    </AnimatedButton>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="transactions" className="space-y-6">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Transaction History</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    All transactions related to this order
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  {order.transactions && order.transactions.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Transaction Hash</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {order.transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                            <TableCell className="capitalize">{transaction.type.toLowerCase()}</TableCell>
                            <TableCell>${transaction.amount.toFixed(2)}</TableCell>
                            <TableCell>
                              {transaction.status === "COMPLETED" ? (
                                <Badge className="bg-green-500">Completed</Badge>
                              ) : transaction.status === "PENDING" ? (
                                <Badge variant="outline" className="border-yellow-500 text-yellow-500">Pending</Badge>
                              ) : transaction.status === "FAILED" ? (
                                <Badge variant="destructive">Failed</Badge>
                              ) : (
                                <Badge variant="outline">{transaction.status}</Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              {transaction.transactionHash ? (
                                <a
                                  href={`https://etherscan.io/tx/${transaction.transactionHash}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center text-blue-600 hover:underline"
                                >
                                  {`${transaction.transactionHash.substring(0, 8)}...${transaction.transactionHash.substring(transaction.transactionHash.length - 8)}`}
                                  <ExternalLink className="ml-1 h-3 w-3" />
                                </a>
                              ) : (
                                "N/A"
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-6">
                      <p className="text-muted-foreground">No transactions found for this order</p>
                    </div>
                  )}
                </AnimatedCardContent>
              </AnimatedCard>
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
