import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageTransition } from "@/components/ui/animated";
import OrderMatchingPreferences from "@/components/marketplace/order-matching-preferences";

export default async function MarketplacePreferencesPage() {
  const session = await auth();
  
  if (!session?.user) {
    redirect("/auth/signin?callbackUrl=/marketplace/preferences");
  }
  
  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <PageHeaderWithBreadcrumb
            title="Marketplace Preferences"
            description="Configure your trading preferences and order matching settings"
            breadcrumbItems={[
              { label: "Marketplace", href: "/marketplace" },
              { label: "Preferences", href: "/marketplace/preferences", isCurrent: true }
            ]}
          />
          
          <div className="mt-6 space-y-6">
            <OrderMatchingPreferences organizationId={session.user.organizationId} />
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
