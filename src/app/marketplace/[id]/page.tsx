"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, ArrowLeft, Loader2 } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  status: string;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
  };
}

export default function MarketplaceDetailPage({
  params,
}: {
  params: { id: string };
}) {
  // Extract creditId directly from params
  const creditId = params.id;

  const router = useRouter();
  const { data: session } = useSession();
  const [carbonCredit, setCarbonCredit] = useState<CarbonCredit | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [isPurchasing, setIsPurchasing] = useState(false);

  useEffect(() => {
    async function fetchCarbonCredit() {
      try {
        const response = await fetch(`/api/carbon-credits/${creditId}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch carbon credit");
        }

        setCarbonCredit(data.carbonCredit);
        setPurchaseQuantity(1);
      } catch (error) {
        console.error("Error fetching carbon credit:", error);
        setError("Failed to load carbon credit");
      } finally {
        setIsLoading(false);
      }
    }

    fetchCarbonCredit();
  }, [creditId]);

  const handlePurchase = async () => {
    if (!carbonCredit || !session?.user) {
      toast({
        title: "Error",
        description: "You must be logged in to purchase carbon credits",
        variant: "destructive",
      });
      return;
    }

    if (purchaseQuantity <= 0 || purchaseQuantity > carbonCredit.quantity) {
      toast({
        title: "Invalid quantity",
        description: "Please enter a valid quantity to purchase",
        variant: "destructive",
      });
      return;
    }

    setIsPurchasing(true);

    try {
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "BUY",
          quantity: purchaseQuantity,
          price: carbonCredit.price,
          carbonCreditId: carbonCredit.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create purchase order");
      }

      toast({
        title: "Purchase successful",
        description: `You have successfully purchased ${purchaseQuantity} tons of carbon credits.`,
      });

      // Refresh the page to update the available credits
      router.refresh();

      // Fetch the updated carbon credit
      const updatedResponse = await fetch(`/api/carbon-credits/${creditId}`);
      const updatedData = await updatedResponse.json();

      if (updatedResponse.ok) {
        setCarbonCredit(updatedData.carbonCredit);
      }
    } catch (error) {
      console.error("Error purchasing carbon credits:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to purchase carbon credits",
        variant: "destructive",
      });
    } finally {
      setIsPurchasing(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="w-full h-full py-10">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-center text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (error || !carbonCredit) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center space-y-4">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <p className="text-center text-lg font-medium">{error || "Carbon credit not found"}</p>
          <Button variant="outline" onClick={() => router.push("/marketplace")}>
            Back to Marketplace
          </Button>
        </div>
      </div>
    );
  }

  const isAvailable = carbonCredit.status === "LISTED" && carbonCredit.quantity > 0;
  const canPurchase = isAvailable && session?.user?.organizationId && carbonCredit.organization.id !== session.user.organizationId;

  return (
    <div className="size-full overflow-auto p-4">
      <Button
        variant="outline"
        onClick={() => router.push("/marketplace")}
        className="mb-6"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Marketplace
      </Button>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl">{carbonCredit.name}</CardTitle>
                  <CardDescription>
                    Offered by {carbonCredit.organization.name}
                  </CardDescription>
                </div>
                {isAvailable ? (
                  <Badge>Available</Badge>
                ) : (
                  <Badge variant="destructive">Unavailable</Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-line">
                {carbonCredit.description || "No description provided."}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <h3 className="font-medium">Vintage Year</h3>
                  <p>{carbonCredit.vintage}</p>
                </div>
                <div>
                  <h3 className="font-medium">Standard</h3>
                  <p>{carbonCredit.standard}</p>
                </div>
                <div>
                  <h3 className="font-medium">Methodology</h3>
                  <p>{carbonCredit.methodology}</p>
                </div>
                <div>
                  <h3 className="font-medium">Location</h3>
                  <p>{carbonCredit.location || "Not specified"}</p>
                </div>
                <div>
                  <h3 className="font-medium">Listed Date</h3>
                  <p>{formatDate(carbonCredit.createdAt)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Last Updated</h3>
                  <p>{formatDate(carbonCredit.updatedAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Trading Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Available Quantity</h3>
                  <p className="text-2xl font-bold">
                    {carbonCredit.quantity.toLocaleString()} tons
                  </p>
                </div>
                <Separator />
                <div>
                  <h3 className="font-medium">Price per Ton</h3>
                  <p className="text-2xl font-bold">${carbonCredit.price.toFixed(2)}</p>
                </div>
                <Separator />
                <div>
                  <h3 className="font-medium">Total Value</h3>
                  <p className="text-2xl font-bold">
                    ${(carbonCredit.quantity * carbonCredit.price).toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              {canPurchase ? (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full">Purchase Carbon Credits</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Purchase Carbon Credits</DialogTitle>
                      <DialogDescription>
                        You are about to purchase carbon credits from {carbonCredit.organization.name}.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="mt-4 space-y-4">
                      <div className="rounded-md bg-muted p-4">
                        <h3 className="font-medium">{carbonCredit.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {carbonCredit.vintage} • {carbonCredit.standard} • {carbonCredit.methodology}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="quantity" className="text-sm font-medium">
                          Quantity (tons)
                        </label>
                        <Input
                          id="quantity"
                          type="number"
                          min={1}
                          max={carbonCredit.quantity}
                          value={purchaseQuantity}
                          onChange={(e) => setPurchaseQuantity(parseInt(e.target.value))}
                        />
                        <p className="text-xs text-muted-foreground">
                          Available: {carbonCredit.quantity.toLocaleString()} tons
                        </p>
                      </div>
                      <div className="rounded-md bg-muted p-4">
                        <div className="flex justify-between">
                          <span>Price per ton:</span>
                          <span>${carbonCredit.price.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between font-medium">
                          <span>Total price:</span>
                          <span>${(carbonCredit.price * purchaseQuantity).toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {}}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handlePurchase}
                        disabled={isPurchasing || purchaseQuantity <= 0 || purchaseQuantity > carbonCredit.quantity}
                      >
                        {isPurchasing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          "Confirm Purchase"
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              ) : (
                <Button className="w-full" disabled>
                  {!session?.user
                    ? "Sign in to purchase"
                    : !session.user.organizationId
                    ? "Join an organization to purchase"
                    : carbonCredit.organization.id === session.user.organizationId
                    ? "You cannot purchase your own credits"
                    : "Not available for purchase"}
                </Button>
              )}
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Seller Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <h3 className="font-medium">Organization</h3>
                <p>{carbonCredit.organization.name}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
