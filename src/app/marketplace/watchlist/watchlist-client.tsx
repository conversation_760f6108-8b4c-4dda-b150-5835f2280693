"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  Bell,
  Eye,
  EyeOff,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus,
  Search,
  Plus,
  Trash2,
  ExternalLink
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Spinner } from "@/components/ui/spinner";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardDescription,
  Animated<PERSON>ardFooter,
  AnimatedCardHeader,
  AnimatedCardTitle,
  PageTransition,
  StaggeredListComponent
} from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";

interface WatchlistClientProps {
  userName: string;
}

interface WatchlistItem {
  id: string;
  carbonCreditId: string;
  userId: string;
  createdAt: string;
  priceAlerts: PriceAlert[];
  carbonCredit: {
    id: string;
    name: string;
    description: string | null;
    standard: string;
    vintage: number;
    price: number;
    priceHistory: {
      date: string;
      price: number;
    }[];
    organization: {
      name: string;
    };
  };
}

interface PriceAlert {
  id: string;
  watchlistItemId: string;
  condition: "ABOVE" | "BELOW";
  price: number;
  active: boolean;
  triggered: boolean;
  createdAt: string;
}

export default function WatchlistClient({ userName }: WatchlistClientProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [watchlistItems, setWatchlistItems] = useState<WatchlistItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<WatchlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [selectedItem, setSelectedItem] = useState<WatchlistItem | null>(null);
  const [isConfirmingRemove, setIsConfirmingRemove] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    async function fetchWatchlist() {
      try {
        setIsLoading(true);
        const response = await fetch("/api/watchlist");

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch watchlist");
        }

        const data = await response.json();
        setWatchlistItems(data.watchlistItems);
        setFilteredItems(data.watchlistItems);
      } catch (error) {
        console.error("Error fetching watchlist:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchWatchlist();
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredItems(watchlistItems);
    } else {
      const lowercasedSearch = searchTerm.toLowerCase();
      const filtered = watchlistItems.filter((item) =>
        item.carbonCredit.name.toLowerCase().includes(lowercasedSearch) ||
        item.carbonCredit.standard.toLowerCase().includes(lowercasedSearch) ||
        item.carbonCredit.organization.name.toLowerCase().includes(lowercasedSearch)
      );
      setFilteredItems(filtered);
    }
  }, [searchTerm, watchlistItems]);

  useEffect(() => {
    if (activeTab === "all") {
      setFilteredItems(watchlistItems);
    } else if (activeTab === "with-alerts") {
      const itemsWithAlerts = watchlistItems.filter(
        (item) => item.priceAlerts && item.priceAlerts.length > 0
      );
      setFilteredItems(itemsWithAlerts);
    } else if (activeTab === "price-up") {
      const itemsWithPriceUp = watchlistItems.filter((item) => {
        const history = item.carbonCredit.priceHistory;
        if (history && history.length >= 2) {
          const latestPrice = history[history.length - 1].price;
          const previousPrice = history[history.length - 2].price;
          return latestPrice > previousPrice;
        }
        return false;
      });
      setFilteredItems(itemsWithPriceUp);
    } else if (activeTab === "price-down") {
      const itemsWithPriceDown = watchlistItems.filter((item) => {
        const history = item.carbonCredit.priceHistory;
        if (history && history.length >= 2) {
          const latestPrice = history[history.length - 1].price;
          const previousPrice = history[history.length - 2].price;
          return latestPrice < previousPrice;
        }
        return false;
      });
      setFilteredItems(itemsWithPriceDown);
    }
  }, [activeTab, watchlistItems]);

  const handleRemoveFromWatchlist = async (itemId: string) => {
    try {
      setIsRemoving(true);
      const response = await fetch(`/api/watchlist/${itemId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to remove from watchlist");
      }

      // Update local state
      setWatchlistItems(watchlistItems.filter((item) => item.id !== itemId));

      toast({
        title: "Removed from Watchlist",
        description: "Item has been removed from your watchlist",
      });

      setIsConfirmingRemove(false);
    } catch (error) {
      console.error("Error removing from watchlist:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove from watchlist",
        variant: "destructive",
      });
    } finally {
      setIsRemoving(false);
    }
  };

  const getPriceChangeIndicator = (item: WatchlistItem) => {
    const history = item.carbonCredit.priceHistory;
    if (history && history.length >= 2) {
      const latestPrice = history[history.length - 1].price;
      const previousPrice = history[history.length - 2].price;
      const percentChange = ((latestPrice - previousPrice) / previousPrice) * 100;

      if (percentChange > 0) {
        return (
          <div className="flex items-center text-green-600">
            <TrendingUp className="mr-1 h-4 w-4" />
            <span>+{percentChange.toFixed(2)}%</span>
          </div>
        );
      } else if (percentChange < 0) {
        return (
          <div className="flex items-center text-red-600">
            <TrendingDown className="mr-1 h-4 w-4" />
            <span>{percentChange.toFixed(2)}%</span>
          </div>
        );
      } else {
        return (
          <div className="flex items-center text-gray-600">
            <Minus className="mr-1 h-4 w-4" />
            <span>0.00%</span>
          </div>
        );
      }
    }

    return (
      <div className="flex items-center text-gray-600">
        <Minus className="mr-1 h-4 w-4" />
        <span>N/A</span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <ProtectedPage loadingComponent={<Spinner size="lg" />}>
        <div className="flex h-full items-center justify-center">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">{error}</p>
              <Button variant="outline" onClick={() => router.push("/marketplace")}>
                Back to Marketplace
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Watchlist"
              description="Track carbon credits you're interested in"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Watchlist", href: "/marketplace/watchlist", isCurrent: true }
              ]}
            />
            <div className="flex space-x-2">
              <AnimatedButton
                variant="outline"
                onClick={() => router.push("/marketplace/watchlist/alerts")}
                animationVariant="buttonTap"
              >
                <Bell className="mr-2 h-4 w-4" />
                Price Alerts
              </AnimatedButton>
              <AnimatedButton
                variant="outline"
                onClick={() => router.push("/marketplace")}
                animationVariant="buttonTap"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Marketplace
              </AnimatedButton>
            </div>
          </div>

          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
              <TabsList>
                <TabsTrigger value="all">All Items</TabsTrigger>
                <TabsTrigger value="with-alerts">With Alerts</TabsTrigger>
                <TabsTrigger value="price-up">Price Up</TabsTrigger>
                <TabsTrigger value="price-down">Price Down</TabsTrigger>
              </TabsList>
            </Tabs>
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search watchlist..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {filteredItems.length === 0 ? (
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-12">
                <Eye className="mb-4 h-12 w-12 text-muted-foreground" />
                <h3 className="text-xl font-medium">Your watchlist is empty</h3>
                <p className="mb-6 text-center text-muted-foreground">
                  {activeTab !== "all"
                    ? "No items match the selected filter."
                    : "Add carbon credits to your watchlist to track their prices and receive alerts."}
                </p>
                <AnimatedButton
                  onClick={() => router.push("/marketplace")}
                  animationVariant="buttonTap"
                >
                  Browse Marketplace
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <StaggeredListComponent className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredItems.map((item) => (
                <AnimatedCard key={item.id} className="overflow-hidden">
                  <AnimatedCardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <AnimatedCardTitle className="line-clamp-1">
                          {item.carbonCredit.name}
                        </AnimatedCardTitle>
                        <p className="text-sm text-muted-foreground">
                          {item.carbonCredit.organization.name}
                        </p>
                      </div>
                      <Dialog
                        open={isConfirmingRemove && selectedItem?.id === item.id}
                        onOpenChange={(open) => {
                          setIsConfirmingRemove(open);
                          if (!open) setSelectedItem(null);
                        }}
                      >
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setSelectedItem(item)}
                          >
                            <EyeOff className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Remove from Watchlist</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to remove this item from your watchlist?
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setIsConfirmingRemove(false);
                                setSelectedItem(null);
                              }}
                            >
                              Cancel
                            </Button>
                            <Button
                              variant="destructive"
                              onClick={() => handleRemoveFromWatchlist(item.id)}
                              disabled={isRemoving}
                            >
                              {isRemoving ? (
                                <>
                                  <Spinner className="mr-2 h-4 w-4" />
                                  Removing...
                                </>
                              ) : (
                                <>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Remove
                                </>
                              )}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Current Price</p>
                        <p className="text-xl font-bold">₹{item.carbonCredit.price.toFixed(2)}</p>
                        {getPriceChangeIndicator(item)}
                      </div>
                      <div>
                        <p className="text-sm font-medium">Details</p>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Standard:</span>
                            <Badge variant="outline">{item.carbonCredit.standard}</Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Vintage:</span>
                            <Badge variant="outline">{item.carbonCredit.vintage}</Badge>
                          </div>
                        </div>
                      </div>
                    </div>

                    {item.priceAlerts && item.priceAlerts.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium">Price Alerts</p>
                        <div className="mt-2 space-y-2">
                          {item.priceAlerts.map((alert) => (
                            <div
                              key={alert.id}
                              className={`flex items-center justify-between rounded-md border p-2 ${
                                alert.triggered
                                  ? "border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/20"
                                  : "border-yellow-200 bg-yellow-50 dark:border-yellow-900 dark:bg-yellow-900/20"
                              }`}
                            >
                              <div className="flex items-center">
                                <Bell className="mr-2 h-4 w-4" />
                                <span className="text-sm">
                                  {alert.condition === "ABOVE" ? "Above" : "Below"} ₹{alert.price.toFixed(2)}
                                </span>
                              </div>
                              <Badge
                                variant={alert.triggered ? "default" : "outline"}
                                className={alert.triggered ? "bg-green-500" : ""}
                              >
                                {alert.triggered ? "Triggered" : "Active"}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </AnimatedCardContent>
                  <AnimatedCardFooter className="flex justify-between">
                    <AnimatedButton
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/marketplace/watchlist/alerts/create?creditId=${item.carbonCreditId}`)}
                      animationVariant="buttonTap"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Alert
                    </AnimatedButton>
                    <AnimatedButton
                      variant="default"
                      size="sm"
                      onClick={() => {
                        // Find a listing for this carbon credit
                        router.push(`/marketplace?creditId=${item.carbonCreditId}`);
                      }}
                      animationVariant="buttonTap"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Listings
                    </AnimatedButton>
                  </AnimatedCardFooter>
                </AnimatedCard>
              ))}
            </StaggeredListComponent>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
