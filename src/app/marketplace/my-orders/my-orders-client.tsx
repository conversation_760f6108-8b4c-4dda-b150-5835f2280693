"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  ArrowRight, 
  Loader2, 
  AlertTriangle,
  Search,
  Filter,
  ArrowUpRight,
  ArrowDownLeft,
  ExternalLink,
  Clock,
  CheckCircle2,
  XCircle,
  MoreHorizontal
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";

interface MyOrdersClientProps {
  userName: string;
}

interface Order {
  id: string;
  type: string;
  side: string;
  status: string;
  quantity: number;
  filledQuantity: number;
  price: number;
  timeInForce: string;
  carbonCredit: {
    id: string;
    name: string;
    vintage: number;
    standard: string;
  };
  listing: {
    id: string;
    title: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function MyOrdersClient({ userName }: MyOrdersClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [activeTab, setActiveTab] = useState(searchParams.get("status") || "all");
  const [searchQuery, setSearchQuery] = useState("");
  const [sideFilter, setSideFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    async function fetchOrders() {
      try {
        setIsLoading(true);
        
        const response = await fetch("/api/orders/my-orders");
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch orders");
        }
        
        const data = await response.json();
        setOrders(data.orders || []);
        setFilteredOrders(data.orders || []);
        setTotalPages(Math.ceil(data.orders.length / pageSize));
      } catch (error) {
        console.error("Error fetching orders:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchOrders();
  }, [pageSize]);

  // Apply filters when tab, search, or filters change
  useEffect(() => {
    let filtered = [...orders];
    
    // Apply status filter (tab)
    if (activeTab !== "all") {
      filtered = filtered.filter(order => order.status === activeTab.toUpperCase());
    }
    
    // Apply side filter
    if (sideFilter !== "all") {
      filtered = filtered.filter(order => order.side === sideFilter.toUpperCase());
    }
    
    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter(order => order.type === typeFilter.toUpperCase());
    }
    
    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(order => 
        order.carbonCredit.name.toLowerCase().includes(query) ||
        order.listing.title.toLowerCase().includes(query) ||
        order.carbonCredit.standard.toLowerCase().includes(query) ||
        order.id.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortBy) {
        case "createdAt":
          valueA = new Date(a.createdAt).getTime();
          valueB = new Date(b.createdAt).getTime();
          break;
        case "price":
          valueA = a.price;
          valueB = b.price;
          break;
        case "quantity":
          valueA = a.quantity;
          valueB = b.quantity;
          break;
        default:
          valueA = a.createdAt;
          valueB = b.createdAt;
      }
      
      return sortOrder === "asc" ? valueA - valueB : valueB - valueA;
    });
    
    setFilteredOrders(filtered);
    setTotalPages(Math.ceil(filtered.length / pageSize));
    setCurrentPage(1); // Reset to first page when filters change
  }, [orders, activeTab, searchQuery, sideFilter, typeFilter, sortBy, sortOrder, pageSize]);

  const handleCancelOrder = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/cancel`, {
        method: "POST",
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to cancel order");
      }
      
      // Update the order status in the local state
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId ? { ...order, status: "CANCELLED" } : order
        )
      );
      
      toast({
        title: "Order Cancelled",
        description: "Your order has been cancelled successfully",
      });
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel order",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "OPEN":
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Open</Badge>;
      case "FILLED":
        return <Badge variant="outline" className="bg-green-100 text-green-800">Filled</Badge>;
      case "PARTIALLY_FILLED":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Partially Filled</Badge>;
      case "CANCELLED":
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      case "EXPIRED":
        return <Badge variant="outline" className="bg-red-100 text-red-800">Expired</Badge>;
      case "REJECTED":
        return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSideBadge = (side: string) => {
    switch (side) {
      case "BUY":
        return <Badge className="bg-green-700 text-white">Buy</Badge>;
      case "SELL":
        return <Badge className="bg-red-700 text-white">Sell</Badge>;
      default:
        return <Badge>{side}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "MARKET":
        return <Badge variant="secondary">Market</Badge>;
      case "LIMIT":
        return <Badge variant="secondary">Limit</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  const resetFilters = () => {
    setSearchQuery("");
    setSideFilter("all");
    setTypeFilter("all");
    setSortBy("createdAt");
    setSortOrder("desc");
  };

  // Get paginated orders
  const paginatedOrders = filteredOrders.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading your orders...</p>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <AlertTriangle className="h-10 w-10 text-destructive mb-4" />
              <h3 className="text-lg font-medium mb-2">Error Loading Orders</h3>
              <p className="text-muted-foreground mb-6">{error}</p>
              <AnimatedButton onClick={() => router.push("/marketplace")} animationVariant="buttonTap">
                Return to Marketplace
              </AnimatedButton>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <PageHeaderWithBreadcrumb
              title="My Orders"
              description="View and manage your marketplace orders"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "My Orders", href: "/marketplace/my-orders", isCurrent: true }
              ]}
            />
          </div>
          
          <div className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full">
                <TabsTrigger value="all" className="flex-1">All Orders</TabsTrigger>
                <TabsTrigger value="open" className="flex-1">Open</TabsTrigger>
                <TabsTrigger value="filled" className="flex-1">Filled</TabsTrigger>
                <TabsTrigger value="partially_filled" className="flex-1">Partially Filled</TabsTrigger>
                <TabsTrigger value="cancelled" className="flex-1">Cancelled</TabsTrigger>
              </TabsList>
              
              <div className="flex flex-col md:flex-row gap-4 mt-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search orders..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                
                <div className="flex flex-wrap gap-2">
                  <Select value={sideFilter} onValueChange={setSideFilter}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Side" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sides</SelectItem>
                      <SelectItem value="buy">Buy</SelectItem>
                      <SelectItem value="sell">Sell</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="market">Market</SelectItem>
                      <SelectItem value="limit">Limit</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Sort By" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="createdAt">Date</SelectItem>
                      <SelectItem value="price">Price</SelectItem>
                      <SelectItem value="quantity">Quantity</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={sortOrder} onValueChange={setSortOrder}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Order" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="desc">Newest First</SelectItem>
                      <SelectItem value="asc">Oldest First</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button variant="outline" onClick={resetFilters}>
                    Reset
                  </Button>
                </div>
              </div>
              
              <TabsContent value={activeTab} className="mt-6">
                {paginatedOrders.length > 0 ? (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Carbon Credit</TableHead>
                          <TableHead>Side</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Price</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedOrders.map((order) => (
                          <TableRow key={order.id}>
                            <TableCell>
                              {format(new Date(order.createdAt), "MMM d, yyyy HH:mm")}
                            </TableCell>
                            <TableCell>
                              <div className="max-w-[200px]">
                                <div className="font-medium truncate">{order.listing.title}</div>
                                <div className="text-xs text-muted-foreground">
                                  {order.carbonCredit.vintage} • {order.carbonCredit.standard}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{getSideBadge(order.side)}</TableCell>
                            <TableCell>{getTypeBadge(order.type)}</TableCell>
                            <TableCell>${order.price.toFixed(2)}</TableCell>
                            <TableCell>
                              <div>
                                {order.filledQuantity.toLocaleString()} / {order.quantity.toLocaleString()}
                              </div>
                              {order.status === "PARTIALLY_FILLED" && (
                                <div className="text-xs text-muted-foreground">
                                  {Math.round((order.filledQuantity / order.quantity) * 100)}% filled
                                </div>
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(order.status)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/marketplace/my-orders/${order.id}`)}>
                                    View Details
                                  </DropdownMenuItem>
                                  {order.status === "OPEN" && (
                                    <>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem onClick={() => router.push(`/marketplace/my-orders/${order.id}/modify`)}>
                                        Modify Order
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleCancelOrder(order.id)}>
                                        Cancel Order
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
                    <div className="mb-4 rounded-full bg-primary/10 p-3">
                      <Search className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="mb-1 text-lg font-medium">No Orders Found</h3>
                    <p className="mb-4 text-center text-sm text-muted-foreground">
                      {orders.length === 0
                        ? "You haven't placed any orders yet."
                        : "No orders match your search criteria. Try adjusting your filters."}
                    </p>
                    {orders.length === 0 ? (
                      <AnimatedButton
                        onClick={() => router.push("/marketplace")}
                        animationVariant="buttonTap"
                      >
                        Browse Marketplace
                      </AnimatedButton>
                    ) : (
                      <AnimatedButton
                        variant="outline"
                        onClick={resetFilters}
                        animationVariant="buttonTap"
                      >
                        Reset Filters
                      </AnimatedButton>
                    )}
                  </div>
                )}
                
                {paginatedOrders.length > 0 && (
                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm text-muted-foreground">
                        Showing
                      </p>
                      <Select
                        value={pageSize.toString()}
                        onValueChange={(value) => setPageSize(parseInt(value))}
                      >
                        <SelectTrigger className="h-8 w-[70px]">
                          <SelectValue placeholder={pageSize.toString()} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5</SelectItem>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="20">20</SelectItem>
                          <SelectItem value="50">50</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-muted-foreground">
                        per page
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <div className="text-sm">
                        Page {currentPage} of {totalPages}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
