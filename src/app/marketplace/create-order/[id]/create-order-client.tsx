"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  <PERSON>L<PERSON>t,
  ArrowRight,
  <PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>rian<PERSON>,
  <PERSON>,
  Info,
  AlertCircle
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

interface CreateOrderClientProps {
  userName: string;
  listingId: string;
}

interface Listing {
  id: string;
  title: string;
  description: string;
  quantity: number;
  availableQuantity: number;
  minPurchaseQuantity: number;
  price: number;
  pricingStrategy: string;
  carbonCredit: {
    id: string;
    name: string;
    vintage: number;
    standard: string;
    methodology: string;
    location: string | null;
  };
  organization: {
    id: string;
    name: string;
  };
}

const createOrderSchema = z.object({
  orderType: z.enum(["MARKET", "LIMIT"]).default("MARKET"),
  side: z.enum(["BUY", "SELL"]).default("BUY"),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive").optional(),
  timeInForce: z.enum(["GTC", "IOC", "FOK"]).default("GTC"),
  notes: z.string().optional(),
});

type CreateOrderFormValues = z.infer<typeof createOrderSchema>;

export default function CreateOrderClient({ userName, listingId }: CreateOrderClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [listing, setListing] = useState<Listing | null>(null);
  const [step, setStep] = useState(1);
  const [totalPrice, setTotalPrice] = useState(0);
  const [fees, setFees] = useState(0);
  const [orderCreated, setOrderCreated] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  const form = useForm<CreateOrderFormValues>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      orderType: "MARKET",
      side: "BUY",
      quantity: 0,
      price: undefined,
      timeInForce: "GTC",
      notes: "",
    },
  });

  useEffect(() => {
    async function fetchListing() {
      try {
        setIsLoading(true);

        const response = await fetch(`/api/marketplace/listings/${listingId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch listing");
        }

        const data = await response.json();
        setListing(data.listing);

        // Set default values
        form.setValue("quantity", data.listing.minPurchaseQuantity);
        if (data.listing.pricingStrategy === "FIXED") {
          form.setValue("price", data.listing.price);
        }
      } catch (error) {
        console.error("Error fetching listing:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchListing();
  }, [listingId, form]);

  // Calculate total price and fees when quantity or price changes
  useEffect(() => {
    const quantity = form.watch("quantity");
    const price = form.watch("price") || (listing?.price || 0);

    const total = quantity * price;
    setTotalPrice(total);

    // Calculate platform fees (example: 2%)
    const platformFee = total * 0.02;
    setFees(platformFee);
  }, [form.watch("quantity"), form.watch("price"), listing]);

  const handleNext = () => {
    if (step === 1) {
      const isValid = form.trigger(["quantity", "orderType", "price"]);
      if (isValid) {
        setStep(2);
      }
    }
  };

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    } else {
      router.push(`/marketplace/${listingId}`);
    }
  };

  const onSubmit = async (values: CreateOrderFormValues) => {
    if (!session?.user) {
      toast({
        title: "Error",
        description: "You must be logged in to create an order",
        variant: "destructive",
      });
      return;
    }

    if (!listing) {
      toast({
        title: "Error",
        description: "Listing information is missing",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: values.orderType,
          side: values.side,
          quantity: values.quantity,
          price: values.price || listing.price,
          timeInForce: values.timeInForce,
          notes: values.notes,
          carbonCreditId: listing.carbonCredit.id,
          listingId: listing.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create order");
      }

      setOrderCreated(true);
      setOrderId(data.order.id);

      toast({
        title: "Order Created",
        description: "Your order has been created successfully",
      });
    } catch (error) {
      console.error("Error creating order:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create order",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading listing details...</p>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !listing) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <AlertTriangle className="h-10 w-10 text-destructive mb-4" />
              <h3 className="text-lg font-medium mb-2">Error Loading Listing</h3>
              <p className="text-muted-foreground mb-6">{error || "Failed to load listing"}</p>
              <AnimatedButton onClick={() => router.push("/marketplace")} animationVariant="buttonTap">
                Return to Marketplace
              </AnimatedButton>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (orderCreated) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6">
              <PageHeaderWithBreadcrumb
                title="Order Created"
                description="Your order has been created successfully"
                breadcrumbItems={[
                  { label: "Marketplace", href: "/marketplace" },
                  { label: "Listing", href: `/marketplace/${listingId}` },
                  { label: "Create Order", href: `/marketplace/create-order/${listingId}`, isCurrent: true }
                ]}
              />
            </div>

            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                  <Check className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-medium mb-2">Order Created Successfully</h3>
                <p className="text-muted-foreground text-center mb-6">
                  Your order has been created and is now being processed.
                </p>

                <div className="w-full max-w-md mb-6 rounded-md border p-4">
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Order Type:</span>
                      <span className="text-sm">{form.getValues("orderType")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Quantity:</span>
                      <span className="text-sm">{form.getValues("quantity").toLocaleString()} tons</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Price per Ton:</span>
                      <span className="text-sm">${(form.getValues("price") || listing.price).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Total Amount:</span>
                      <span className="text-sm">${totalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <AnimatedButton
                    variant="outline"
                    onClick={() => router.push("/marketplace")}
                    animationVariant="buttonTap"
                  >
                    Return to Marketplace
                  </AnimatedButton>
                  <AnimatedButton
                    onClick={() => router.push("/marketplace/my-orders")}
                    animationVariant="buttonTap"
                  >
                    View My Orders
                  </AnimatedButton>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <PageHeaderWithBreadcrumb
              title="Create Order"
              description="Place an order for carbon credits"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Listing", href: `/marketplace/${listingId}` },
                { label: "Create Order", href: `/marketplace/create-order/${listingId}`, isCurrent: true }
              ]}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>
                    {step === 1 ? "Order Details" : "Review Order"}
                  </AnimatedCardTitle>
                  <AnimatedCardDescription>
                    {step === 1 ? "Specify your order details" : "Review and confirm your order"}
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      {step === 1 && (
                        <>
                          <FormField
                            control={form.control}
                            name="orderType"
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel>Order Type</FormLabel>
                                <FormControl>
                                  <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="flex flex-col space-y-1"
                                  >
                                    <div className="flex items-center space-x-2">
                                      <RadioGroupItem value="MARKET" id="market" />
                                      <Label htmlFor="market">Market Order (Execute at current price)</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <RadioGroupItem value="LIMIT" id="limit" />
                                      <Label htmlFor="limit">Limit Order (Execute at specified price or better)</Label>
                                    </div>
                                  </RadioGroup>
                                </FormControl>
                                <FormDescription>
                                  Choose how your order will be executed
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="quantity"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Quantity (tons)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min={listing.minPurchaseQuantity}
                                    max={listing.availableQuantity}
                                    step={1}
                                    {...field}
                                    onChange={(e) => field.onChange(Number(e.target.value))}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Minimum: {listing.minPurchaseQuantity.toLocaleString()} tons | Available: {listing.availableQuantity.toLocaleString()} tons
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {form.watch("orderType") === "LIMIT" && (
                            <FormField
                              control={form.control}
                              name="price"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Price per Ton (INR)</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min={0.01}
                                      step={0.01}
                                      placeholder={listing.price.toString()}
                                      {...field}
                                      onChange={(e) => field.onChange(Number(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Current market price: ${listing.price.toFixed(2)} per ton
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          <FormField
                            control={form.control}
                            name="timeInForce"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Time in Force</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select time in force" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="GTC">Good Till Canceled (GTC)</SelectItem>
                                    <SelectItem value="IOC">Immediate or Cancel (IOC)</SelectItem>
                                    <SelectItem value="FOK">Fill or Kill (FOK)</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Specify how long your order will remain active
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="notes"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Notes (Optional)</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Add any notes or special instructions for this order..."
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  These notes will be visible to the seller
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="flex justify-between pt-4">
                            <AnimatedButton
                              type="button"
                              variant="outline"
                              onClick={handleBack}
                              animationVariant="buttonTap"
                            >
                              <ArrowLeft className="mr-2 h-4 w-4" />
                              Back to Listing
                            </AnimatedButton>
                            <AnimatedButton
                              type="button"
                              onClick={handleNext}
                              disabled={!form.formState.isValid}
                              animationVariant="buttonTap"
                            >
                              Review Order
                              <ArrowRight className="ml-2 h-4 w-4" />
                            </AnimatedButton>
                          </div>
                        </>
                      )}

                      {step === 2 && (
                        <>
                          <Alert>
                            <Info className="h-4 w-4" />
                            <AlertTitle>Order Summary</AlertTitle>
                            <AlertDescription>
                              Please review your order details before confirming
                            </AlertDescription>
                          </Alert>

                          <div className="space-y-4 rounded-md border p-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm font-medium">Order Type</p>
                                <p className="text-sm">{form.getValues("orderType")}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Side</p>
                                <p className="text-sm">{form.getValues("side")}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Quantity</p>
                                <p className="text-sm">{form.getValues("quantity").toLocaleString()} tons</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Price per Ton</p>
                                <p className="text-sm">${(form.getValues("price") || listing.price).toFixed(2)}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Time in Force</p>
                                <p className="text-sm">{form.getValues("timeInForce")}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Total Price</p>
                                <p className="text-sm">${totalPrice.toFixed(2)}</p>
                              </div>
                            </div>

                            {form.getValues("notes") && (
                              <>
                                <Separator />
                                <div>
                                  <p className="text-sm font-medium">Notes</p>
                                  <p className="text-sm">{form.getValues("notes")}</p>
                                </div>
                              </>
                            )}
                          </div>

                          <Separator />

                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span>Subtotal:</span>
                              <span>${totalPrice.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Platform Fee (2%):</span>
                              <span>${fees.toFixed(2)}</span>
                            </div>
                            <Separator />
                            <div className="flex justify-between font-medium">
                              <span>Total:</span>
                              <span>${(totalPrice + fees).toFixed(2)}</span>
                            </div>
                          </div>

                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Important</AlertTitle>
                            <AlertDescription>
                              By confirming this order, you agree to purchase the specified quantity of carbon credits at the stated price. This action cannot be undone.
                            </AlertDescription>
                          </Alert>

                          <div className="flex justify-between pt-4">
                            <AnimatedButton
                              type="button"
                              variant="outline"
                              onClick={handleBack}
                              animationVariant="buttonTap"
                            >
                              <ArrowLeft className="mr-2 h-4 w-4" />
                              Back
                            </AnimatedButton>
                            <AnimatedButton
                              type="submit"
                              disabled={isSubmitting}
                              animationVariant="buttonTap"
                            >
                              {isSubmitting ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                "Confirm Order"
                              )}
                            </AnimatedButton>
                          </div>
                        </>
                      )}
                    </form>
                  </Form>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>

            <div className="space-y-6">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Listing Details</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium">{listing.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {listing.carbonCredit.vintage} • {listing.carbonCredit.standard} • {listing.carbonCredit.methodology}
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="font-medium">Available</p>
                        <p>{listing.availableQuantity.toLocaleString()} tons</p>
                      </div>
                      <div>
                        <p className="font-medium">Price</p>
                        <p>${listing.price.toFixed(2)} / ton</p>
                      </div>
                      <div>
                        <p className="font-medium">Min. Purchase</p>
                        <p>{listing.minPurchaseQuantity.toLocaleString()} tons</p>
                      </div>
                      <div>
                        <p className="font-medium">Seller</p>
                        <p>{listing.organization.name}</p>
                      </div>
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>

              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Order Tips</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                      <span>Market orders execute immediately at the current price</span>
                    </li>
                    <li className="flex items-start">
                      <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                      <span>Limit orders execute only at your specified price or better</span>
                    </li>
                    <li className="flex items-start">
                      <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                      <span>GTC orders remain active until filled or canceled</span>
                    </li>
                    <li className="flex items-start">
                      <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                      <span>IOC orders fill immediately available quantity and cancel the rest</span>
                    </li>
                    <li className="flex items-start">
                      <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                      <span>FOK orders must fill completely or are canceled entirely</span>
                    </li>
                  </ul>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
