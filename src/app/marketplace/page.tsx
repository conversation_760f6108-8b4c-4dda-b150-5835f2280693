"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Loader2, Filter, Search, AlertCircle, ArrowLeft } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import MarketplaceNavigation from "@/components/marketplace/marketplace-navigation";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardFooter,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedInput,
  AnimatedSelect,
  AnimatedSelectContent,
  AnimatedSelectItem,
  AnimatedSelectTrigger,
  SelectValue
} from "@/components/ui/animated";
import { PageTransition } from "@/components/ui/page-transition";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Spinner } from "@/components/ui/spinner";

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  organization: {
    name: string;
  };
}

const standards = [
  "All Standards",
  "Verra",
  "Gold Standard",
  "American Carbon Registry",
  "Climate Action Reserve",
  "Plan Vivo",
  "Clean Development Mechanism",
  "Other",
];

const methodologies = [
  "All Methodologies",
  "Renewable Energy",
  "Energy Efficiency",
  "Forestry and Land Use",
  "Waste Management",
  "Transportation",
  "Industrial Processes",
  "Agriculture",
  "Other",
];

export default function MarketplacePage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [carbonCredits, setCarbonCredits] = useState<CarbonCredit[]>([]);
  const [filteredCredits, setFilteredCredits] = useState<CarbonCredit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [standardFilter, setStandardFilter] = useState("All Standards");
  const [methodologyFilter, setMethodologyFilter] = useState("All Methodologies");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [vintageFilter, setVintageFilter] = useState<number | null>(null);
  const [selectedCredit, setSelectedCredit] = useState<CarbonCredit | null>(null);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [isPurchasing, setIsPurchasing] = useState(false);

  useEffect(() => {
    async function fetchCarbonCredits() {
      try {
        const response = await fetch("/api/carbon-credits?status=LISTED");
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch carbon credits");
        }

        setCarbonCredits(data.carbonCredits);
        setFilteredCredits(data.carbonCredits);

        // Set initial price range based on available credits
        if (data.carbonCredits.length > 0) {
          const prices = data.carbonCredits.map((credit: CarbonCredit) => credit.price);
          const minPrice = Math.floor(Math.min(...prices));
          const maxPrice = Math.ceil(Math.max(...prices));
          setPriceRange([minPrice, maxPrice]);
        }
      } catch (error) {
        console.error("Error fetching carbon credits:", error);
        setError("Failed to load carbon credits");
      } finally {
        setIsLoading(false);
      }
    }

    fetchCarbonCredits();
  }, []);

  useEffect(() => {
    // Apply filters
    let filtered = carbonCredits;

    // Search term filter
    if (searchTerm) {
      filtered = filtered.filter(
        (credit) =>
          credit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (credit.description && credit.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
          credit.standard.toLowerCase().includes(searchTerm.toLowerCase()) ||
          credit.methodology.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (credit.location && credit.location.toLowerCase().includes(searchTerm.toLowerCase())) ||
          credit.organization.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Standard filter
    if (standardFilter !== "All Standards") {
      filtered = filtered.filter((credit) => credit.standard === standardFilter);
    }

    // Methodology filter
    if (methodologyFilter !== "All Methodologies") {
      filtered = filtered.filter((credit) => credit.methodology === methodologyFilter);
    }

    // Price range filter
    filtered = filtered.filter(
      (credit) => credit.price >= priceRange[0] && credit.price <= priceRange[1]
    );

    // Vintage filter
    if (vintageFilter) {
      filtered = filtered.filter((credit) => credit.vintage === vintageFilter);
    }

    setFilteredCredits(filtered);
  }, [carbonCredits, searchTerm, standardFilter, methodologyFilter, priceRange, vintageFilter]);

  const handlePurchase = async () => {
    if (!selectedCredit || !session?.user) {
      toast({
        title: "Error",
        description: "You must be logged in to purchase carbon credits",
        variant: "destructive",
      });
      return;
    }

    setIsPurchasing(true);

    try {
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "BUY",
          quantity: purchaseQuantity,
          price: selectedCredit.price,
          carbonCreditId: selectedCredit.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create purchase order");
      }

      toast({
        title: "Purchase successful",
        description: `You have successfully purchased ${purchaseQuantity} tons of carbon credits.`,
      });

      // Refresh the page to update the available credits
      router.refresh();
      setSelectedCredit(null);
    } catch (error) {
      console.error("Error purchasing carbon credits:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to purchase carbon credits",
        variant: "destructive",
      });
    } finally {
      setIsPurchasing(false);
    }
  };

  const resetFilters = () => {
    setSearchTerm("");
    setStandardFilter("All Standards");
    setMethodologyFilter("All Methodologies");

    // Reset price range to min/max of available credits
    if (carbonCredits.length > 0) {
      const prices = carbonCredits.map((credit) => credit.price);
      const minPrice = Math.floor(Math.min(...prices));
      const maxPrice = Math.ceil(Math.max(...prices));
      setPriceRange([minPrice, maxPrice]);
    } else {
      setPriceRange([0, 100]);
    }

    setVintageFilter(null);
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-center text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center space-y-4">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <p className="text-center text-lg font-medium">{error}</p>
          <Button variant="outline" onClick={() => router.push("/dashboard")}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedPage loadingComponent={<Spinner size="lg" />}>
      <PageTransition>
        <div className="size-full flex flex-col gap-6 justify-between overflow-auto p-4">
        <AnimatedButton
          variant="outline"
          onClick={() => router.push("/dashboard")}
          className="mb-6 w-fit"
          animationVariant="buttonTap"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </AnimatedButton>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Carbon Credit Marketplace</h1>
        <p className="text-muted-foreground">
          Browse and purchase carbon credits from verified projects
        </p>
      </div>

      <MarketplaceNavigation className="mt-4" />

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <AnimatedInput
            placeholder="Search carbon credits..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Sheet>
          <SheetTrigger asChild>
            <AnimatedButton variant="outline" className="gap-2" animationVariant="buttonTap">
              <Filter className="h-4 w-4" />
              Filters
              {(standardFilter !== "All Standards" ||
                methodologyFilter !== "All Methodologies" ||
                vintageFilter !== null) && (
                <Badge variant="secondary" className="ml-1 rounded-full px-1">
                  {(standardFilter !== "All Standards" ? 1 : 0) +
                    (methodologyFilter !== "All Methodologies" ? 1 : 0) +
                    (vintageFilter !== null ? 1 : 0)}
                </Badge>
              )}
            </AnimatedButton>
          </SheetTrigger>
          <SheetContent className="sm:max-w-md">
            <SheetHeader>
              <SheetTitle>Filter Carbon Credits</SheetTitle>
              <SheetDescription>
                Refine your search with these filters
              </SheetDescription>
            </SheetHeader>
            <div className="mt-6 space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Standard</h3>
                <AnimatedSelect
                  value={standardFilter}
                  onValueChange={setStandardFilter}
                >
                  <AnimatedSelectTrigger>
                    <SelectValue placeholder="Select standard" />
                  </AnimatedSelectTrigger>
                  <AnimatedSelectContent>
                    {standards.map((standard) => (
                      <AnimatedSelectItem key={standard} value={standard}>
                        {standard}
                      </AnimatedSelectItem>
                    ))}
                  </AnimatedSelectContent>
                </AnimatedSelect>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Methodology</h3>
                <AnimatedSelect
                  value={methodologyFilter}
                  onValueChange={setMethodologyFilter}
                >
                  <AnimatedSelectTrigger>
                    <SelectValue placeholder="Select methodology" />
                  </AnimatedSelectTrigger>
                  <AnimatedSelectContent>
                    {methodologies.map((methodology) => (
                      <AnimatedSelectItem key={methodology} value={methodology}>
                        {methodology}
                      </AnimatedSelectItem>
                    ))}
                  </AnimatedSelectContent>
                </AnimatedSelect>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Price Range (INR)</h3>
                  <span className="text-xs text-muted-foreground">
                    ${priceRange[0]} - ${priceRange[1]}
                  </span>
                </div>
                <Slider
                  value={priceRange}
                  min={0}
                  max={
                    carbonCredits.length > 0
                      ? Math.ceil(Math.max(...carbonCredits.map((c) => c.price))) + 10
                      : 100
                  }
                  step={1}
                  onValueChange={(value) => setPriceRange(value as [number, number])}
                  className="py-4"
                />
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Vintage Year</h3>
                <AnimatedSelect
                  value={vintageFilter?.toString() || ""}
                  onValueChange={(value) =>
                    setVintageFilter(value ? parseInt(value) : null)
                  }
                >
                  <AnimatedSelectTrigger>
                    <SelectValue placeholder="Select vintage year" />
                  </AnimatedSelectTrigger>
                  <AnimatedSelectContent>
                    <AnimatedSelectItem value="">Any Year</AnimatedSelectItem>
                    {Array.from(
                      new Set(carbonCredits.map((credit) => credit.vintage))
                    )
                      .sort((a, b) => b - a)
                      .map((year) => (
                        <AnimatedSelectItem key={year} value={year.toString()}>
                          {year}
                        </AnimatedSelectItem>
                      ))}
                  </AnimatedSelectContent>
                </AnimatedSelect>
              </div>

              <AnimatedButton onClick={resetFilters} variant="outline" className="w-full" animationVariant="buttonTap">
                Reset Filters
              </AnimatedButton>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {filteredCredits.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
          <div className="mb-4 rounded-full bg-primary/10 p-3">
            <Search className="h-6 w-6 text-primary" />
          </div>
          <h3 className="mb-1 text-lg font-medium">No carbon credits found</h3>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            {carbonCredits.length === 0
              ? "There are no carbon credits available on the marketplace yet."
              : "No carbon credits match your search criteria. Try adjusting your filters."}
          </p>
          {carbonCredits.length > 0 && (
            <AnimatedButton variant="outline" onClick={resetFilters} animationVariant="buttonTap">
              Reset Filters
            </AnimatedButton>
          )}
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredCredits.map((credit) => (
            <AnimatedCard key={credit.id} className="overflow-hidden">
              <AnimatedCardHeader>
                <AnimatedCardTitle className="line-clamp-1">{credit.name}</AnimatedCardTitle>
                <p className="text-sm text-muted-foreground">
                  {credit.organization.name}
                </p>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <p className="line-clamp-2 text-sm">
                  {credit.description || "No description provided."}
                </p>
                <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="font-medium">Quantity</p>
                    <p>{credit.quantity.toLocaleString()} tons</p>
                  </div>
                  <div>
                    <p className="font-medium">Price</p>
                    <p>${credit.price.toFixed(2)} / ton</p>
                  </div>
                  <div>
                    <p className="font-medium">Vintage</p>
                    <p>{credit.vintage}</p>
                  </div>
                  <div>
                    <p className="font-medium">Standard</p>
                    <p>{credit.standard}</p>
                  </div>
                  <div>
                    <p className="font-medium">Methodology</p>
                    <p className="truncate">{credit.methodology}</p>
                  </div>
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="truncate">{credit.location || "N/A"}</p>
                  </div>
                </div>
              </AnimatedCardContent>
              <AnimatedCardFooter className="flex justify-between">
                <AnimatedButton
                  variant="outline"
                  onClick={() => router.push(`/marketplace/${credit.id}`)}
                  animationVariant="buttonTap"
                >
                  View Details
                </AnimatedButton>
                <Dialog>
                  <DialogTrigger asChild>
                    <AnimatedButton
                      onClick={() => {
                        setSelectedCredit(credit);
                        setPurchaseQuantity(1);
                      }}
                      animationVariant="buttonTap"
                    >
                      Purchase
                    </AnimatedButton>
                  </DialogTrigger>
                  <DialogContent>
                    {selectedCredit && (
                      <>
                        <DialogHeader>
                          <DialogTitle>Purchase Carbon Credits</DialogTitle>
                          <DialogDescription>
                            You are about to purchase carbon credits from {selectedCredit.organization.name}.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="mt-4 space-y-4">
                          <div className="rounded-md bg-muted p-4">
                            <h3 className="font-medium">{selectedCredit.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {selectedCredit.vintage} • {selectedCredit.standard} • {selectedCredit.methodology}
                            </p>
                          </div>
                          <div className="space-y-2">
                            <label htmlFor="quantity" className="text-sm font-medium">
                              Quantity (tons)
                            </label>
                            <AnimatedInput
                              id="quantity"
                              type="number"
                              min={1}
                              max={selectedCredit.quantity}
                              value={purchaseQuantity}
                              onChange={(e) => setPurchaseQuantity(parseInt(e.target.value))}
                            />
                            <p className="text-xs text-muted-foreground">
                              Available: {selectedCredit.quantity.toLocaleString()} tons
                            </p>
                          </div>
                          <div className="rounded-md bg-muted p-4">
                            <div className="flex justify-between">
                              <span>Price per ton:</span>
                              <span>${selectedCredit.price.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between font-medium">
                              <span>Total price:</span>
                              <span>${(selectedCredit.price * purchaseQuantity).toFixed(2)}</span>
                            </div>
                          </div>
                        </div>
                        <DialogFooter>
                          <AnimatedButton
                            variant="outline"
                            onClick={() => setSelectedCredit(null)}
                            animationVariant="buttonTap"
                          >
                            Cancel
                          </AnimatedButton>
                          <AnimatedButton
                            onClick={handlePurchase}
                            disabled={isPurchasing || !session?.user}
                            animationVariant="buttonTap"
                          >
                            {isPurchasing ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              "Confirm Purchase"
                            )}
                          </AnimatedButton>
                        </DialogFooter>
                      </>
                    )}
                  </DialogContent>
                </Dialog>
              </AnimatedCardFooter>
            </AnimatedCard>
          ))}
        </div>
      )}
    </div>
    </PageTransition>
    </ProtectedPage>
  );
}
