"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON><PERSON>, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  ArrowLeft, 
  ArrowRight, 
  Loader2, 
  AlertTriangle,
  Check,
  Info,
  AlertCircle
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface CreateListingConfirmClientProps {
  userName: string;
}

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  status: string;
  verificationStatus: string;
}

interface ListingData {
  carbonCreditId: string;
  quantity: number;
  minPurchaseQuantity: number;
  title: string;
  description: string;
  pricingStrategy: string;
  price?: number;
  auctionEndTime?: string;
  auctionReservePrice?: number;
  auctionMinIncrement?: number;
  dynamicPricingRules?: string;
  tieredPricingRules?: string;
  visibility: string;
  expirationDate?: string;
  tags?: string[];
  featured?: boolean;
  allowPartialFills?: boolean;
  allowCounterOffers?: boolean;
}

export default function CreateListingConfirmClient({ userName }: CreateListingConfirmClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [listingData, setListingData] = useState<ListingData | null>(null);
  const [carbonCredit, setCarbonCredit] = useState<CarbonCredit | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);

  useEffect(() => {
    // Get listing data from session storage
    const storedData = sessionStorage.getItem("createListingData");
    if (!storedData) {
      setError("No listing data found. Please start the listing process again.");
      setIsLoading(false);
      return;
    }

    try {
      const parsedData = JSON.parse(storedData);
      setListingData(parsedData);

      // Fetch carbon credit details
      async function fetchCarbonCredit() {
        try {
          const response = await fetch(`/api/carbon-credits/${parsedData.carbonCreditId}`);
          
          if (!response.ok) {
            throw new Error("Failed to fetch carbon credit details");
          }
          
          const data = await response.json();
          setCarbonCredit(data.carbonCredit);
        } catch (error) {
          console.error("Error fetching carbon credit:", error);
          setError("Failed to fetch carbon credit details");
        } finally {
          setIsLoading(false);
        }
      }

      fetchCarbonCredit();
    } catch (error) {
      console.error("Error parsing listing data:", error);
      setError("Invalid listing data. Please start the listing process again.");
      setIsLoading(false);
    }
  }, []);

  const handleSubmit = async () => {
    if (!termsAccepted) {
      toast({
        title: "Terms Required",
        description: "Please accept the terms and conditions to create your listing",
        variant: "destructive",
      });
      return;
    }

    if (!listingData) {
      toast({
        title: "Error",
        description: "No listing data found. Please start the listing process again.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/marketplace/listings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(listingData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create listing");
      }

      toast({
        title: "Listing Created",
        description: "Your carbon credit listing has been created successfully",
      });

      // Clear session storage
      sessionStorage.removeItem("createListingData");

      // Redirect to the new listing
      router.push(`/marketplace/${data.listing.id}`);
    } catch (error) {
      console.error("Error creating listing:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create listing",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading listing details...</p>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !listingData || !carbonCredit) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <AlertTriangle className="h-10 w-10 text-destructive mb-4" />
              <h3 className="text-lg font-medium mb-2">Error Loading Listing Data</h3>
              <p className="text-muted-foreground mb-6">{error || "Failed to load listing data"}</p>
              <AnimatedButton onClick={() => router.push("/marketplace/create-listing")} animationVariant="buttonTap">
                Start Over
              </AnimatedButton>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <PageHeaderWithBreadcrumb
              title="Confirm Listing"
              description="Review and confirm your carbon credit listing"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Create Listing", href: "/marketplace/create-listing" },
                { label: "Details", href: "/marketplace/create-listing/details" },
                { label: "Pricing", href: "/marketplace/create-listing/pricing" },
                { label: "Confirm", href: "/marketplace/create-listing/confirm", isCurrent: true }
              ]}
            />
          </div>
          
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <Check className="h-4 w-4" />
                </div>
                <span className="font-medium">Select Credit</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <Check className="h-4 w-4" />
                </div>
                <span className="font-medium">Details</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <Check className="h-4 w-4" />
                </div>
                <span className="font-medium">Pricing</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  4
                </div>
                <span className="font-medium">Confirm</span>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Listing Summary</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Review the details of your carbon credit listing
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">{listingData.title}</h3>
                      <p className="text-sm text-muted-foreground">{listingData.description}</p>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Carbon Credit</p>
                        <p className="text-sm">{carbonCredit.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Standard</p>
                        <p className="text-sm">{carbonCredit.standard}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Methodology</p>
                        <p className="text-sm">{carbonCredit.methodology}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Vintage</p>
                        <p className="text-sm">{carbonCredit.vintage}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Location</p>
                        <p className="text-sm">{carbonCredit.location || "N/A"}</p>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Quantity</p>
                        <p className="text-sm">{listingData.quantity.toLocaleString()} tons</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Minimum Purchase</p>
                        <p className="text-sm">{listingData.minPurchaseQuantity.toLocaleString()} tons</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Pricing Strategy</p>
                        <p className="text-sm">{listingData.pricingStrategy}</p>
                      </div>
                      {listingData.pricingStrategy === "FIXED" && (
                        <div>
                          <p className="text-sm font-medium">Price per Ton</p>
                          <p className="text-sm">${listingData.price?.toFixed(2)}</p>
                        </div>
                      )}
                      {listingData.pricingStrategy === "AUCTION" && (
                        <>
                          <div>
                            <p className="text-sm font-medium">Auction End Date</p>
                            <p className="text-sm">{new Date(listingData.auctionEndTime || "").toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Reserve Price</p>
                            <p className="text-sm">${listingData.auctionReservePrice?.toFixed(2)}</p>
                          </div>
                        </>
                      )}
                      <div>
                        <p className="text-sm font-medium">Visibility</p>
                        <p className="text-sm">{listingData.visibility}</p>
                      </div>
                      {listingData.expirationDate && (
                        <div>
                          <p className="text-sm font-medium">Expiration Date</p>
                          <p className="text-sm">{new Date(listingData.expirationDate).toLocaleDateString()}</p>
                        </div>
                      )}
                    </div>
                    
                    {listingData.tags && listingData.tags.length > 0 && (
                      <>
                        <Separator />
                        <div>
                          <p className="text-sm font-medium mb-2">Tags</p>
                          <div className="flex flex-wrap gap-2">
                            {listingData.tags.map((tag) => (
                              <Badge key={tag} variant="secondary">{tag}</Badge>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
              
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Terms and Conditions</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent className="space-y-6">
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Important Information</AlertTitle>
                    <AlertDescription>
                      By creating this listing, you agree to the following terms:
                    </AlertDescription>
                  </Alert>
                  
                  <div className="space-y-4 text-sm">
                    <p>
                      1. You confirm that you have the legal right to sell the carbon credits listed.
                    </p>
                    <p>
                      2. You agree to the platform's terms of service and marketplace rules.
                    </p>
                    <p>
                      3. You understand that a platform fee may be charged on successful transactions.
                    </p>
                    <p>
                      4. You agree to fulfill all orders placed through this listing in a timely manner.
                    </p>
                    <p>
                      5. You understand that once a transaction is completed, it cannot be reversed.
                    </p>
                  </div>
                  
                  <div className="flex items-start space-x-2 pt-4">
                    <Checkbox
                      id="terms"
                      checked={termsAccepted}
                      onCheckedChange={(checked) => setTermsAccepted(checked === true)}
                    />
                    <div className="grid gap-1.5 leading-none">
                      <Label
                        htmlFor="terms"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        I accept the terms and conditions
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        You must accept the terms to create your listing
                      </p>
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
              
              <div className="flex justify-between">
                <AnimatedButton
                  variant="outline"
                  onClick={() => router.push("/marketplace/create-listing/pricing")}
                  animationVariant="buttonTap"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </AnimatedButton>
                <AnimatedButton
                  onClick={handleSubmit}
                  disabled={isSubmitting || !termsAccepted}
                  animationVariant="buttonTap"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Listing...
                    </>
                  ) : (
                    "Create Listing"
                  )}
                </AnimatedButton>
              </div>
            </div>
            
            <div className="space-y-6">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Listing Preview</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="rounded-md border p-4 space-y-4">
                    <div>
                      <h3 className="font-medium">{listingData.title}</h3>
                      <p className="text-sm text-muted-foreground truncate">
                        {carbonCredit.vintage} • {carbonCredit.standard} • {carbonCredit.methodology}
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="font-medium">Quantity</p>
                        <p>{listingData.quantity.toLocaleString()} tons</p>
                      </div>
                      <div>
                        <p className="font-medium">Price</p>
                        <p>
                          {listingData.pricingStrategy === "FIXED" 
                            ? `$${listingData.price?.toFixed(2)} / ton` 
                            : listingData.pricingStrategy === "AUCTION"
                            ? "Auction"
                            : "Variable"}
                        </p>
                      </div>
                    </div>
                    
                    <div className="pt-2">
                      <p className="text-xs text-muted-foreground">
                        This is how your listing will appear in the marketplace
                      </p>
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
              
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Listing Tips</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <span>Use a clear, descriptive title</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <span>Include detailed information about the project</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <span>Add relevant tags to improve discoverability</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <span>Set a competitive price based on market rates</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <span>Respond promptly to buyer inquiries</span>
                    </li>
                  </ul>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
