"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  <PERSON>L<PERSON><PERSON>, 
  ArrowRight, 
  Loader2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Tag,
  Plus,
  X
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

interface CreateListingDetailsClientProps {
  userName: string;
}

const listingDetailsSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  tags: z.array(z.string()).optional(),
  visibility: z.enum(["PUBLIC", "PRIVATE"]).default("PUBLIC"),
  expirationDate: z.string().optional(),
  additionalDocuments: z.array(z.string()).optional(),
});

type ListingDetailsFormValues = z.infer<typeof listingDetailsSchema>;

export default function CreateListingDetailsClient({ userName }: CreateListingDetailsClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [tagInput, setTagInput] = useState("");
  const [tags, setTags] = useState<string[]>([]);

  const form = useForm<ListingDetailsFormValues>({
    resolver: zodResolver(listingDetailsSchema),
    defaultValues: {
      title: "",
      description: "",
      tags: [],
      visibility: "PUBLIC",
      expirationDate: "",
      additionalDocuments: [],
    },
  });

  // Check if we have listing data in session storage
  useEffect(() => {
    const storedData = sessionStorage.getItem("createListingData");
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      if (parsedData.title) {
        form.setValue("title", parsedData.title);
      }
      if (parsedData.description) {
        form.setValue("description", parsedData.description);
      }
      if (parsedData.tags && Array.isArray(parsedData.tags)) {
        setTags(parsedData.tags);
        form.setValue("tags", parsedData.tags);
      }
      if (parsedData.visibility) {
        form.setValue("visibility", parsedData.visibility);
      }
      if (parsedData.expirationDate) {
        form.setValue("expirationDate", parsedData.expirationDate);
      }
    }
  }, [form]);

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      form.setValue("tags", newTags);
      setTagInput("");
    }
  };

  const removeTag = (tag: string) => {
    const newTags = tags.filter(t => t !== tag);
    setTags(newTags);
    form.setValue("tags", newTags);
  };

  const onSubmit = (values: ListingDetailsFormValues) => {
    // Store the form data in session storage
    const existingData = sessionStorage.getItem("createListingData");
    const parsedData = existingData ? JSON.parse(existingData) : {};
    
    const updatedData = {
      ...parsedData,
      ...values,
    };
    
    sessionStorage.setItem("createListingData", JSON.stringify(updatedData));
    
    // Navigate to the next step
    router.push("/marketplace/create-listing/pricing");
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <PageHeaderWithBreadcrumb
              title="Listing Details"
              description="Provide details about your carbon credit listing"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Create Listing", href: "/marketplace/create-listing" },
                { label: "Details", href: "/marketplace/create-listing/details", isCurrent: true }
              ]}
            />
          </div>
          
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <Check className="h-4 w-4" />
                </div>
                <span className="font-medium">Select Credit</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  2
                </div>
                <span className="font-medium">Details</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground">
                  3
                </div>
                <span className="text-muted-foreground">Pricing</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground">
                  4
                </div>
                <span className="text-muted-foreground">Confirm</span>
              </div>
            </div>
          </div>
          
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Listing Details</AnimatedCardTitle>
              <AnimatedCardDescription>
                Provide information about your carbon credit listing
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Listing Title</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter a descriptive title for your listing" />
                        </FormControl>
                        <FormDescription>
                          A clear title helps buyers find your carbon credits
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            rows={6}
                            placeholder="Describe your carbon credits, their environmental impact, and any other relevant details..."
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Provide a detailed description of your carbon credits to attract buyers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="space-y-2">
                    <FormLabel>Tags</FormLabel>
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 hover:bg-transparent"
                            onClick={() => removeTag(tag)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                      <div className="flex items-center gap-2">
                        <Input
                          value={tagInput}
                          onChange={(e) => setTagInput(e.target.value)}
                          placeholder="Add tag..."
                          className="h-8 w-32"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addTag();
                            }
                          }}
                        />
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          className="h-8"
                          onClick={addTag}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <FormDescription>
                      Add tags to help buyers find your listing (e.g., renewable, forestry, solar)
                    </FormDescription>
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="visibility"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Visibility</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select visibility" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="PUBLIC">Public (Visible to all users)</SelectItem>
                            <SelectItem value="PRIVATE">Private (Visible to selected users only)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Control who can see your listing
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="expirationDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Expiration Date (Optional)</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormDescription>
                          Set an expiration date for your listing (leave blank for no expiration)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="flex justify-between pt-4">
                    <AnimatedButton
                      type="button"
                      variant="outline"
                      onClick={() => router.push("/marketplace/create-listing")}
                      animationVariant="buttonTap"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back
                    </AnimatedButton>
                    <AnimatedButton
                      type="submit"
                      animationVariant="buttonTap"
                    >
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </AnimatedButton>
                  </div>
                </form>
              </Form>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
