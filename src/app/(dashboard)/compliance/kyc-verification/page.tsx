import { Metadata } from "next";
import { auth } from "@/lib/auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { KycVerificationWizard } from "@/components/compliance/kyc-verification-wizard";
import { KycVerificationSkeleton } from "@/components/compliance/kyc-verification-skeleton";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "KYC Verification",
  description: "Complete your identity verification to access all platform features",
};

export default async function KycVerificationPage() {
  const session = await auth();

  if (!session) {
    redirect("/login");
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="KYC Verification"
        description="Complete your identity verification to access all platform features"
      />
      <div className="grid gap-8">
        <Suspense fallback={<KycVerificationSkeleton />}>
          <KycVerificationWizard userId={session.user.id} />
        </Suspense>
      </div>
    </DashboardShell>
  );
}
