import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { KycVerificationWizard } from "@/components/compliance/kyc-verification-wizard";
import { KycVerificationSkeleton } from "@/components/compliance/kyc-verification-skeleton";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Organization KYC Verification",
  description: "Complete your organization's identity verification to access all platform features",
};

interface OrganizationKycVerificationPageProps {
  params: {
    organizationId: string;
  };
}

export default async function OrganizationKycVerificationPage({
  params,
}: OrganizationKycVerificationPageProps) {
  const { organizationId } = params;
  const session = await auth();

  if (!session) {
    redirect("/login");
  }

  // Check if user has access to this organization
  // This would be implemented with a proper authorization check
  
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Organization KYC Verification"
        description="Complete your organization's identity verification to access all platform features"
      />
      <div className="grid gap-8">
        <Suspense fallback={<KycVerificationSkeleton />}>
          <KycVerificationWizard organizationId={organizationId} />
        </Suspense>
      </div>
    </DashboardShell>
  );
}
