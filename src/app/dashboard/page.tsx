import { auth } from "@/lib/auth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { WalletWidget } from "@/components/dashboard/wallet-widget";
import { CarbonCreditsWidget } from "@/components/dashboard/carbon-credits-widget";
import { DollarSign, CreditCard, Users, Activity } from "lucide-react";

// Import the client component
import DashboardClient from "./dashboard-client";

export default async function DashboardPage() {
  const session = await auth();
  const userName = session?.user?.name || "User";

  return (
    <DashboardClient userName={userName} />
  );
}
