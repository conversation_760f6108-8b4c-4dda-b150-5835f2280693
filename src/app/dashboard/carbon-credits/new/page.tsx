"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { CarbonCreditForm } from "@/components/forms/carbon-credit-form";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function NewCarbonCreditPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const handleSuccess = () => {
    router.push("/dashboard/carbon-credits");
  };

  if (status === "loading") {
    return (
      <div className="container py-10">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!session?.user?.organizationId) {
    return (
      <div className="container py-10">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You must be part of an organization to create carbon credits.
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button variant="outline" onClick={() => router.push("/dashboard")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Create Carbon Credit</h1>
          <p className="text-muted-foreground">
            List your carbon credits on the marketplace for other enterprises to purchase.
          </p>
        </div>
        <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Carbon Credits
        </Button>
      </div>

      <CarbonCreditForm onSuccess={handleSuccess} />
    </div>
  );
}
