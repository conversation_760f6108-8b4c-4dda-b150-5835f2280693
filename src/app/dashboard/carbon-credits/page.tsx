"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Pagination, PaginationContent, PaginationItem, <PERSON><PERSON>ationLink, Pa<PERSON>ation<PERSON><PERSON><PERSON>, PaginationPrevious } from "@/components/ui/pagination";
import { 
  Plus, 
  Search, 
  Filter, 
  ListFilter, 
  ArrowUpDown, 
  Eye, 
  Edit, 
  Trash2, 
  FileText, 
  Loader2,
  Leaf,
  Coins,
  RotateCw,
  CheckCircle2,
  XCircle,
  AlertTriangle
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/animated";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  availableQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  status: string;
  verificationStatus: string;
  createdAt: string;
  updatedAt: string;
}

export default function CarbonCreditsPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [carbonCredits, setCarbonCredits] = useState<CarbonCredit[]>([]);
  const [filteredCredits, setFilteredCredits] = useState<CarbonCredit[]>([]);
  const [statusFilter, setStatusFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortField, setSortField] = useState("createdAt");
  const [sortDirection, setSortDirection] = useState("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);
  const [activeTab, setActiveTab] = useState("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [creditToDelete, setCreditToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch carbon credits on component mount
  useEffect(() => {
    const fetchCarbonCredits = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, this would call an API to fetch carbon credits
        // For now, we'll use mock data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockCredits: CarbonCredit[] = [
          {
            id: "1",
            name: "Renewable Energy Project",
            description: "Wind farm generating clean energy",
            quantity: 1000,
            availableQuantity: 1000,
            price: 15.5,
            vintage: 2023,
            standard: "Verra",
            methodology: "VM0025",
            location: "United States",
            status: "PENDING",
            verificationStatus: "PENDING",
            createdAt: "2023-05-10T10:00:00Z",
            updatedAt: "2023-05-10T10:00:00Z",
          },
          {
            id: "2",
            name: "Forestry Conservation",
            description: "Forest conservation project preventing deforestation",
            quantity: 2500,
            availableQuantity: 2500,
            price: 12.75,
            vintage: 2022,
            standard: "Gold Standard",
            methodology: "GS-FOR",
            location: "Brazil",
            status: "VERIFIED",
            verificationStatus: "APPROVED",
            createdAt: "2023-04-15T14:30:00Z",
            updatedAt: "2023-04-20T09:15:00Z",
          },
          {
            id: "3",
            name: "Methane Capture",
            description: "Landfill methane capture and utilization",
            quantity: 1500,
            availableQuantity: 1500,
            price: 18.25,
            vintage: 2023,
            standard: "Verra",
            methodology: "VM0033",
            location: "Canada",
            status: "LISTED",
            verificationStatus: "APPROVED",
            createdAt: "2023-03-22T11:45:00Z",
            updatedAt: "2023-03-25T16:20:00Z",
          },
          {
            id: "4",
            name: "Solar Energy Project",
            description: "Solar farm in desert region",
            quantity: 3000,
            availableQuantity: 2000,
            price: 14.5,
            vintage: 2022,
            standard: "Gold Standard",
            methodology: "GS-RE",
            location: "Morocco",
            status: "TOKENIZED",
            verificationStatus: "APPROVED",
            createdAt: "2023-02-18T08:10:00Z",
            updatedAt: "2023-02-20T13:40:00Z",
          },
          {
            id: "5",
            name: "Reforestation Project",
            description: "Reforestation of degraded land",
            quantity: 5000,
            availableQuantity: 4000,
            price: 10.25,
            vintage: 2021,
            standard: "Verra",
            methodology: "VM0007",
            location: "Indonesia",
            status: "LISTED",
            verificationStatus: "APPROVED",
            createdAt: "2023-01-05T15:30:00Z",
            updatedAt: "2023-01-10T09:45:00Z",
          },
        ];
        
        setCarbonCredits(mockCredits);
        applyFiltersAndSort(mockCredits);
      } catch (error) {
        console.error("Error fetching carbon credits:", error);
        toast({
          title: "Error",
          description: "Failed to fetch carbon credits",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCarbonCredits();
  }, []);

  // Apply filters, sorting, and pagination
  const applyFiltersAndSort = (credits: CarbonCredit[]) => {
    let filtered = [...credits];
    
    // Filter by tab
    if (activeTab !== "all") {
      filtered = filtered.filter(credit => credit.status === activeTab);
    }
    
    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(credit => credit.status === statusFilter);
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(credit => 
        credit.name.toLowerCase().includes(query) ||
        credit.standard.toLowerCase().includes(query) ||
        credit.methodology.toLowerCase().includes(query) ||
        (credit.location && credit.location.toLowerCase().includes(query))
      );
    }
    
    // Sort
    filtered.sort((a, b) => {
      const fieldA = a[sortField as keyof CarbonCredit];
      const fieldB = b[sortField as keyof CarbonCredit];
      
      if (typeof fieldA === "string" && typeof fieldB === "string") {
        return sortDirection === "asc"
          ? fieldA.localeCompare(fieldB)
          : fieldB.localeCompare(fieldA);
      } else if (typeof fieldA === "number" && typeof fieldB === "number") {
        return sortDirection === "asc"
          ? fieldA - fieldB
          : fieldB - fieldA;
      } else {
        return 0;
      }
    });
    
    // Calculate total pages
    setTotalPages(Math.ceil(filtered.length / itemsPerPage));
    
    // Apply pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedCredits = filtered.slice(startIndex, startIndex + itemsPerPage);
    
    setFilteredCredits(paginatedCredits);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setCurrentPage(1);
    applyFiltersAndSort(carbonCredits);
  };

  // Handle status filter change
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1);
    applyFiltersAndSort(carbonCredits);
  };

  // Handle search query change
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
    applyFiltersAndSort(carbonCredits);
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    const direction = field === sortField && sortDirection === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortDirection(direction);
    applyFiltersAndSort(carbonCredits);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    applyFiltersAndSort(carbonCredits);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!creditToDelete) return;
    
    setIsDeleting(true);
    
    try {
      // In a real implementation, this would call an API to delete the carbon credit
      // For now, we'll simulate a successful deletion
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Remove from state
      const updatedCredits = carbonCredits.filter(credit => credit.id !== creditToDelete);
      setCarbonCredits(updatedCredits);
      applyFiltersAndSort(updatedCredits);
      
      toast({
        title: "Carbon credit deleted",
        description: "The carbon credit has been deleted successfully.",
      });
    } catch (error) {
      console.error("Error deleting carbon credit:", error);
      toast({
        title: "Error",
        description: "Failed to delete carbon credit",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setCreditToDelete(null);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Pending
          </Badge>
        );
      case "VERIFIED":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            Verified
          </Badge>
        );
      case "LISTED":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <Leaf className="h-3 w-3" />
            Listed
          </Badge>
        );
      case "TOKENIZED":
        return (
          <Badge variant="success" className="flex items-center gap-1 bg-green-100 text-green-800 hover:bg-green-200">
            <Coins className="h-3 w-3" />
            Tokenized
          </Badge>
        );
      case "RETIRED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Retired
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Carbon Credits"
              description="Manage your carbon credits"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits", isCurrent: true }
              ]}
            />
            
            <div className="mt-4 flex space-x-2 sm:mt-0">
              <Button onClick={() => router.push("/dashboard/carbon-credits/create")}>
                <Plus className="mr-2 h-4 w-4" />
                Create Credit
              </Button>
              
              <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits/batch")}>
                <ListFilter className="mr-2 h-4 w-4" />
                Batch Operations
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="PENDING">Pending</TabsTrigger>
                <TabsTrigger value="VERIFIED">Verified</TabsTrigger>
                <TabsTrigger value="LISTED">Listed</TabsTrigger>
                <TabsTrigger value="TOKENIZED">Tokenized</TabsTrigger>
                <TabsTrigger value="RETIRED">Retired</TabsTrigger>
              </TabsList>
              
              <div className="flex space-x-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search credits..."
                    className="pl-8 w-[200px] sm:w-[300px]"
                    value={searchQuery}
                    onChange={(e) => handleSearchChange(e.target.value)}
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                  <SelectTrigger className="w-[180px]">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="VERIFIED">Verified</SelectItem>
                    <SelectItem value="LISTED">Listed</SelectItem>
                    <SelectItem value="TOKENIZED">Tokenized</SelectItem>
                    <SelectItem value="RETIRED">Retired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <TabsContent value={activeTab} className="mt-6">
              <Card>
                <CardContent className="p-0">
                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : filteredCredits.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">No carbon credits found</h3>
                      <p className="text-sm text-muted-foreground">
                        {searchQuery || statusFilter !== "all" || activeTab !== "all"
                          ? "Try adjusting your search or filters"
                          : "You don't have any carbon credits yet"}
                      </p>
                      {!searchQuery && statusFilter === "all" && activeTab === "all" && (
                        <Button className="mt-4" onClick={() => router.push("/dashboard/carbon-credits/create")}>
                          <Plus className="mr-2 h-4 w-4" />
                          Create Your First Credit
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead 
                              className="cursor-pointer"
                              onClick={() => handleSortChange("name")}
                            >
                              <div className="flex items-center">
                                Name
                                {sortField === "name" && (
                                  <ArrowUpDown className="ml-2 h-4 w-4" />
                                )}
                              </div>
                            </TableHead>
                            <TableHead 
                              className="cursor-pointer"
                              onClick={() => handleSortChange("vintage")}
                            >
                              <div className="flex items-center">
                                Vintage
                                {sortField === "vintage" && (
                                  <ArrowUpDown className="ml-2 h-4 w-4" />
                                )}
                              </div>
                            </TableHead>
                            <TableHead>Standard</TableHead>
                            <TableHead 
                              className="cursor-pointer"
                              onClick={() => handleSortChange("availableQuantity")}
                            >
                              <div className="flex items-center">
                                Available
                                {sortField === "availableQuantity" && (
                                  <ArrowUpDown className="ml-2 h-4 w-4" />
                                )}
                              </div>
                            </TableHead>
                            <TableHead 
                              className="cursor-pointer"
                              onClick={() => handleSortChange("price")}
                            >
                              <div className="flex items-center">
                                Price
                                {sortField === "price" && (
                                  <ArrowUpDown className="ml-2 h-4 w-4" />
                                )}
                              </div>
                            </TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredCredits.map((credit) => (
                            <TableRow key={credit.id}>
                              <TableCell className="font-medium">{credit.name}</TableCell>
                              <TableCell>{credit.vintage}</TableCell>
                              <TableCell>{credit.standard}</TableCell>
                              <TableCell>{credit.availableQuantity.toLocaleString()}</TableCell>
                              <TableCell>${credit.price.toFixed(2)}</TableCell>
                              <TableCell>{getStatusBadge(credit.status)}</TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <RotateCw className="h-4 w-4" />
                                      <span className="sr-only">Actions</span>
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}`)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      View Details
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}/edit`)}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    {credit.status === "PENDING" && (
                                      <DropdownMenuItem onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}/verify`)}>
                                        <CheckCircle2 className="mr-2 h-4 w-4" />
                                        Verify
                                      </DropdownMenuItem>
                                    )}
                                    {credit.status === "VERIFIED" && (
                                      <DropdownMenuItem onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}/tokenize`)}>
                                        <Coins className="mr-2 h-4 w-4" />
                                        Tokenize
                                      </DropdownMenuItem>
                                    )}
                                    {(credit.status === "VERIFIED" || credit.status === "TOKENIZED") && (
                                      <DropdownMenuItem onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}/retire`)}>
                                        <XCircle className="mr-2 h-4 w-4" />
                                        Retire
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem 
                                      className="text-destructive focus:text-destructive"
                                      onClick={() => {
                                        setCreditToDelete(credit.id);
                                        setDeleteDialogOpen(true);
                                      }}
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
                {!isLoading && filteredCredits.length > 0 && (
                  <CardFooter className="flex items-center justify-center py-4">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious 
                            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                          />
                        </PaginationItem>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => handlePageChange(page)}
                              isActive={page === currentPage}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}
                        <PaginationItem>
                          <PaginationNext 
                            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Carbon Credit</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this carbon credit? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </ProtectedPage>
  );
}
