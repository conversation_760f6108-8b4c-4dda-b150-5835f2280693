"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Plus } from "lucide-react";
import { CarbonCreditListingNudges } from "@/components/carbon-credits/listing-nudges";

export default function ListCarbonCreditsPage() {
  const { data: session } = useSession();
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch user's organization
  useEffect(() => {
    const fetchUserOrganization = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/user/organization');
        if (response.ok) {
          const data = await response.json();
          if (data.organization?.id) {
            setOrganizationId(data.organization.id);
          }
        }
      } catch (error) {
        console.error('Error fetching user organization:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserOrganization();
  }, []);

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">List Carbon Credits</h1>
            <p className="text-muted-foreground">
              Create a new carbon credit listing on the marketplace
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Listing
          </Button>
        </div>

        {/* Context-specific nudges */}
        {organizationId && (
          <CarbonCreditListingNudges organizationId={organizationId} />
        )}

        <Card>
          <CardHeader>
            <CardTitle>Carbon Credit Details</CardTitle>
            <CardDescription>
              Provide information about the carbon credits you want to list
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-6">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Credit Name*</Label>
                  <Input id="name" placeholder="e.g., Forest Conservation Project" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="standard">Standard*</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select standard" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="verra">Verra (VCS)</SelectItem>
                      <SelectItem value="gold_standard">Gold Standard</SelectItem>
                      <SelectItem value="american_carbon_registry">American Carbon Registry</SelectItem>
                      <SelectItem value="climate_action_reserve">Climate Action Reserve</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description*</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the carbon credit project, its impact, and other relevant details"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity (tCO2e)*</Label>
                  <Input id="quantity" type="number" min="1" placeholder="100" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price">Price per Credit (INR)*</Label>
                  <Input id="price" type="number" min="0.01" step="0.01" placeholder="10.00" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="vintage">Vintage Year*</Label>
                  <Input id="vintage" type="number" min="2000" max={new Date().getFullYear()} placeholder="2023" />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="location">Project Location</Label>
                  <Input id="location" placeholder="e.g., Amazon Rainforest, Brazil" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="methodology">Methodology*</Label>
                  <Input id="methodology" placeholder="e.g., VM0015" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="serialNumber">Serial Number / ID</Label>
                <Input id="serialNumber" placeholder="e.g., VCS-123456-789" />
              </div>

              <div className="flex justify-end space-x-4">
                <Button variant="outline">Cancel</Button>
                <Button>Submit Listing</Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
