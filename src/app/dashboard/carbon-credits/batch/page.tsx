"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Check, AlertCircle, Loader2, RefreshCw, ListFilter, FileText, Pencil, Tag, Trash2 } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

// Schema for batch operations
const batchOperationSchema = z.object({
  operation: z.enum(["verify", "list", "retire", "update-price", "delete"]),
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
});

// Verification schema
const verificationSchema = z.object({
  operation: z.literal("verify"),
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  type: z.enum(["INITIAL", "ANNUAL", "METHODOLOGY", "OWNERSHIP", "RETIREMENT"]),
  notes: z.string().optional(),
});

// Listing schema
const listingSchema = z.object({
  operation: z.literal("list"),
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  listingDate: z.string().optional(),
});

// Retirement schema
const retirementSchema = z.object({
  operation: z.literal("retire"),
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  reason: z.string().min(1, "Retirement reason is required"),
  beneficiary: z.string().optional(),
  retirementDate: z.string().optional(),
});

// Price update schema
const priceUpdateSchema = z.object({
  operation: z.literal("update-price"),
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  price: z.coerce.number().positive("Price must be positive"),
  reason: z.string().optional(),
});

// Delete schema
const deleteSchema = z.object({
  operation: z.literal("delete"),
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  confirmation: z.literal("DELETE", {
    errorMap: () => ({ message: "Type DELETE to confirm" }),
  }),
});

// Union of all operation schemas
const formSchema = z.discriminatedUnion("operation", [
  verificationSchema,
  listingSchema,
  retirementSchema,
  priceUpdateSchema,
  deleteSchema,
]);

type BatchOperationFormValues = z.infer<typeof batchOperationSchema>;
type FormValues = z.infer<typeof formSchema>;

interface CarbonCredit {
  id: string;
  name: string;
  vintage: number;
  standard: string;
  methodology: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  status: string;
  verificationStatus: string;
  selected: boolean;
}

export default function BatchOperationsCarbonCreditPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [carbonCredits, setCarbonCredits] = useState<CarbonCredit[]>([]);
  const [filteredCredits, setFilteredCredits] = useState<CarbonCredit[]>([]);
  const [statusFilter, setStatusFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      operation: "verify",
      carbonCreditIds: [],
      type: "INITIAL",
      notes: "",
    } as any,
  });

  // Watch for operation changes to update form validation
  const operation = form.watch("operation");

  // Fetch carbon credits on component mount
  useState(() => {
    const fetchCarbonCredits = async () => {
      try {
        setIsLoading(true);

        // In a real implementation, this would call an API to fetch carbon credits
        // For now, we'll use mock data

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data
        const mockCredits: CarbonCredit[] = [
          {
            id: "1",
            name: "Renewable Energy Project",
            vintage: 2023,
            standard: "Verra",
            methodology: "VM0025",
            quantity: 1000,
            availableQuantity: 1000,
            price: 15.5,
            status: "PENDING",
            verificationStatus: "PENDING",
            selected: false,
          },
          {
            id: "2",
            name: "Forestry Conservation",
            vintage: 2022,
            standard: "Gold Standard",
            methodology: "GS-FOR",
            quantity: 2500,
            availableQuantity: 2500,
            price: 12.75,
            status: "VERIFIED",
            verificationStatus: "APPROVED",
            selected: false,
          },
          {
            id: "3",
            name: "Methane Capture",
            vintage: 2023,
            standard: "Verra",
            methodology: "VM0033",
            quantity: 1500,
            availableQuantity: 1500,
            price: 18.25,
            status: "LISTED",
            verificationStatus: "APPROVED",
            selected: false,
          },
          {
            id: "4",
            name: "Solar Energy Project",
            vintage: 2022,
            standard: "Gold Standard",
            methodology: "GS-RE",
            quantity: 3000,
            availableQuantity: 2000,
            price: 14.5,
            status: "TOKENIZED",
            verificationStatus: "APPROVED",
            selected: false,
          },
          {
            id: "5",
            name: "Reforestation Project",
            vintage: 2021,
            standard: "Verra",
            methodology: "VM0007",
            quantity: 5000,
            availableQuantity: 4000,
            price: 10.25,
            status: "LISTED",
            verificationStatus: "APPROVED",
            selected: false,
          },
        ];

        setCarbonCredits(mockCredits);
        setFilteredCredits(mockCredits);
      } catch (error) {
        console.error("Error fetching carbon credits:", error);
        toast({
          title: "Error",
          description: "Failed to fetch carbon credits",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCarbonCredits();
  }, []);

  // Filter carbon credits based on status and search query
  const filterCredits = () => {
    let filtered = [...carbonCredits];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(credit => credit.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(credit =>
        credit.name.toLowerCase().includes(query) ||
        credit.standard.toLowerCase().includes(query) ||
        credit.methodology.toLowerCase().includes(query)
      );
    }

    setFilteredCredits(filtered);
  };

  // Handle status filter change
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    filterCredits();
  };

  // Handle search query change
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    filterCredits();
  };

  // Toggle selection of a carbon credit
  const toggleSelection = (id: string) => {
    setCarbonCredits(prev =>
      prev.map(credit =>
        credit.id === id ? { ...credit, selected: !credit.selected } : credit
      )
    );

    setFilteredCredits(prev =>
      prev.map(credit =>
        credit.id === id ? { ...credit, selected: !credit.selected } : credit
      )
    );

    // Update form value
    const selectedIds = form.getValues("carbonCreditIds");
    const newSelectedIds = selectedIds.includes(id)
      ? selectedIds.filter(selectedId => selectedId !== id)
      : [...selectedIds, id];

    form.setValue("carbonCreditIds", newSelectedIds);
  };

  // Select all visible carbon credits
  const selectAll = (selected: boolean) => {
    setCarbonCredits(prev =>
      prev.map(credit => {
        // Only update if the credit is in the filtered list
        const isFiltered = filteredCredits.some(fc => fc.id === credit.id);
        return isFiltered ? { ...credit, selected } : credit;
      })
    );

    setFilteredCredits(prev =>
      prev.map(credit => ({ ...credit, selected }))
    );

    // Update form value
    const newSelectedIds = selected
      ? filteredCredits.map(credit => credit.id)
      : [];

    form.setValue("carbonCreditIds", newSelectedIds);
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsProcessing(true);

    try {
      // In a real implementation, this would call an API to process the batch operation
      // For now, we'll simulate a successful operation

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Get selected carbon credits
      const selectedCredits = carbonCredits.filter(credit =>
        data.carbonCreditIds.includes(credit.id)
      );

      let successMessage = "";

      switch (data.operation) {
        case "verify":
          successMessage = `Submitted ${selectedCredits.length} carbon credits for verification`;
          break;
        case "list":
          successMessage = `Listed ${selectedCredits.length} carbon credits on the marketplace`;
          break;
        case "retire":
          successMessage = `Retired ${selectedCredits.length} carbon credits`;
          break;
        case "update-price":
          successMessage = `Updated price for ${selectedCredits.length} carbon credits`;
          break;
        case "delete":
          successMessage = `Deleted ${selectedCredits.length} carbon credits`;
          break;
      }

      toast({
        title: "Operation successful",
        description: successMessage,
      });

      router.push("/dashboard/carbon-credits");
      router.refresh();
    } catch (error) {
      console.error("Error processing batch operation:", error);
      toast({
        title: "Operation failed",
        description: error instanceof Error ? error.message : "Failed to process batch operation",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Batch Operations"
              description="Perform operations on multiple carbon credits at once"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: "Batch Operations", href: "/dashboard/carbon-credits/batch", isCurrent: true }
              ]}
            />
          </div>

          <div className="mb-6">
            <Alert variant="default" className="bg-blue-50 border-blue-200">
              <InfoIcon className="h-4 w-4 text-blue-500" />
              <AlertTitle>Batch Operations</AlertTitle>
              <AlertDescription>
                <p>
                  Select multiple carbon credits and perform operations like verification, listing, retirement, price updates, or deletion.
                </p>
              </AlertDescription>
            </Alert>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Carbon Credits</CardTitle>
                  <CardDescription>
                    Select the carbon credits to include in the batch operation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-4 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="select-all"
                        checked={filteredCredits.length > 0 && filteredCredits.every(credit => credit.selected)}
                        onCheckedChange={(checked) => selectAll(!!checked)}
                      />
                      <label
                        htmlFor="select-all"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Select All
                      </label>
                    </div>

                    <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
                      <Select
                        value={statusFilter}
                        onValueChange={handleStatusFilterChange}
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="PENDING">Pending</SelectItem>
                          <SelectItem value="VERIFIED">Verified</SelectItem>
                          <SelectItem value="LISTED">Listed</SelectItem>
                          <SelectItem value="TOKENIZED">Tokenized</SelectItem>
                          <SelectItem value="RETIRED">Retired</SelectItem>
                        </SelectContent>
                      </Select>

                      <div className="relative">
                        <ListFilter className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="search"
                          placeholder="Search credits..."
                          className="pl-8"
                          value={searchQuery}
                          onChange={(e) => handleSearchChange(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>

                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : filteredCredits.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">No carbon credits found</h3>
                      <p className="text-sm text-muted-foreground">
                        {searchQuery || statusFilter !== "all"
                          ? "Try adjusting your search or filters"
                          : "You don't have any carbon credits yet"}
                      </p>
                    </div>
                  ) : (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[50px]">Select</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Vintage</TableHead>
                            <TableHead>Standard</TableHead>
                            <TableHead>Available</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredCredits.map((credit) => (
                            <TableRow key={credit.id}>
                              <TableCell>
                                <Checkbox
                                  checked={credit.selected}
                                  onCheckedChange={() => toggleSelection(credit.id)}
                                />
                              </TableCell>
                              <TableCell className="font-medium">{credit.name}</TableCell>
                              <TableCell>{credit.vintage}</TableCell>
                              <TableCell>{credit.standard}</TableCell>
                              <TableCell>{credit.availableQuantity.toLocaleString()}</TableCell>
                              <TableCell>${credit.price.toFixed(2)}</TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    credit.status === "PENDING"
                                      ? "outline"
                                      : credit.status === "VERIFIED"
                                      ? "secondary"
                                      : credit.status === "LISTED"
                                      ? "default"
                                      : credit.status === "TOKENIZED"
                                      ? "success"
                                      : "destructive"
                                  }
                                >
                                  {credit.status}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Batch Operation</CardTitle>
                  <CardDescription>
                    Choose an operation to perform on the selected carbon credits
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <FormField
                        control={form.control}
                        name="operation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Operation</FormLabel>
                            <Select
                              onValueChange={(value: any) => {
                                field.onChange(value);
                                // Reset form when operation changes
                                form.reset({
                                  operation: value,
                                  carbonCreditIds: form.getValues("carbonCreditIds"),
                                } as any);
                              }}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select an operation" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="verify">Verify</SelectItem>
                                <SelectItem value="list">List on Marketplace</SelectItem>
                                <SelectItem value="retire">Retire</SelectItem>
                                <SelectItem value="update-price">Update Price</SelectItem>
                                <SelectItem value="delete">Delete</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Select the operation to perform on the selected carbon credits
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Verification fields */}
                      {operation === "verify" && (
                        <FormField
                          control={form.control}
                          name="type"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Verification Type</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select verification type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="INITIAL">Initial Verification</SelectItem>
                                  <SelectItem value="ANNUAL">Annual Verification</SelectItem>
                                  <SelectItem value="METHODOLOGY">Methodology Verification</SelectItem>
                                  <SelectItem value="OWNERSHIP">Ownership Verification</SelectItem>
                                  <SelectItem value="RETIREMENT">Retirement Verification</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Select the type of verification to perform
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Listing fields */}
                      {operation === "list" && (
                        <FormField
                          control={form.control}
                          name="listingDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Listing Date (Optional)</FormLabel>
                              <FormControl>
                                <Input type="date" {...field} />
                              </FormControl>
                              <FormDescription>
                                Leave blank to list immediately
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Retirement fields */}
                      {operation === "retire" && (
                        <>
                          <FormField
                            control={form.control}
                            name="reason"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Retirement Reason</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter reason for retirement"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Explain why you are retiring these carbon credits
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="beneficiary"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Beneficiary (Optional)</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter beneficiary name"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Who benefits from this retirement
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}

                      {/* Price update fields */}
                      {operation === "update-price" && (
                        <>
                          <FormField
                            control={form.control}
                            name="price"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>New Price (INR)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min={0.01}
                                    step={0.01}
                                    placeholder="Enter new price"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  New price per ton in INR
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="reason"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Reason for Price Change (Optional)</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Enter reason for price change"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}

                      {/* Delete fields */}
                      {operation === "delete" && (
                        <FormField
                          control={form.control}
                          name="confirmation"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirmation</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='Type "DELETE" to confirm'
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription className="text-destructive">
                                This action cannot be undone
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Common fields for all operations */}
                      {operation === "verify" && (
                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Notes (Optional)</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Enter additional notes"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      <Button
                        type="submit"
                        disabled={
                          isProcessing ||
                          !carbonCredits.some(credit => credit.selected) ||
                          form.getValues("carbonCreditIds").length === 0
                        }
                        className="w-full"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            {operation === "verify" && <FileText className="mr-2 h-4 w-4" />}
                            {operation === "list" && <Tag className="mr-2 h-4 w-4" />}
                            {operation === "retire" && <Check className="mr-2 h-4 w-4" />}
                            {operation === "update-price" && <Pencil className="mr-2 h-4 w-4" />}
                            {operation === "delete" && <Trash2 className="mr-2 h-4 w-4" />}

                            {operation === "verify" && "Submit for Verification"}
                            {operation === "list" && "List on Marketplace"}
                            {operation === "retire" && "Retire Credits"}
                            {operation === "update-price" && "Update Price"}
                            {operation === "delete" && "Delete Credits"}
                          </>
                        )}
                      </Button>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="mt-6">
            <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Carbon Credits
            </Button>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
