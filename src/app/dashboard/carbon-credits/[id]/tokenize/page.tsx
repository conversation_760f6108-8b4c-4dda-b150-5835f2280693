"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Coins, Loader2, AlertCircle, CheckCircle2, Info, Zap, Shield } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

// Schema for tokenization form
const tokenizationFormSchema = z.object({
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]),
  useTestnet: z.boolean().default(true),
  securityLevel: z.enum(["standard", "high"]).default("standard"),
  gasOption: z.enum(["standard", "fast", "instant"]).default("standard"),
  autoRetry: z.boolean().default(true),
});

type TokenizationFormValues = z.infer<typeof tokenizationFormSchema>;

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  availableQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  status: string;
  verificationStatus: string;
  tokenizationStatus: string;
  tokenId: string | null;
  tokenNetwork: string | null;
  tokenizedAt: string | null;
  organization: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function TokenizeCarbonCreditPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [carbonCredit, setCarbonCredit] = useState<CarbonCredit | null>(null);
  const [estimatedGas, setEstimatedGas] = useState<{
    standard: number;
    fast: number;
    instant: number;
  }>({
    standard: 0,
    fast: 0,
    instant: 0,
  });
  const [estimatedTime, setEstimatedTime] = useState<{
    standard: string;
    fast: string;
    instant: string;
  }>({
    standard: "",
    fast: "",
    instant: "",
  });

  const form = useForm<TokenizationFormValues>({
    resolver: zodResolver(tokenizationFormSchema),
    defaultValues: {
      network: "polygon",
      useTestnet: true,
      securityLevel: "standard",
      gasOption: "standard",
      autoRetry: true,
    },
  });

  // Watch for network and gas option changes
  const network = form.watch("network");
  const gasOption = form.watch("gasOption");
  const useTestnet = form.watch("useTestnet");

  // Fetch carbon credit details on component mount
  useEffect(() => {
    const fetchCarbonCredit = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, this would call an API to fetch the carbon credit
        // For now, we'll use mock data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockCredit: CarbonCredit = {
          id: params.id,
          name: "Renewable Energy Project",
          description: "Wind farm generating clean energy",
          quantity: 1000,
          availableQuantity: 1000,
          price: 15.5,
          vintage: 2023,
          standard: "Verra",
          methodology: "VM0025",
          location: "United States",
          status: "VERIFIED",
          verificationStatus: "APPROVED",
          tokenizationStatus: "PENDING",
          tokenId: null,
          tokenNetwork: null,
          tokenizedAt: null,
          organization: {
            id: "org-123",
            name: "Green Energy Co.",
          },
          createdAt: "2023-05-10T10:00:00Z",
          updatedAt: "2023-05-10T10:00:00Z",
        };
        
        setCarbonCredit(mockCredit);
        
        // Estimate gas costs
        estimateGasCosts(form.getValues("network"));
      } catch (error) {
        console.error("Error fetching carbon credit:", error);
        toast({
          title: "Error",
          description: "Failed to fetch carbon credit details",
          variant: "destructive",
        });
        router.push("/dashboard/carbon-credits");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCarbonCredit();
  }, [params.id, router]);

  // Estimate gas costs based on selected network
  const estimateGasCosts = async (network: string) => {
    try {
      // In a real implementation, this would call an API to estimate gas costs
      // For now, we'll use mock data
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock gas costs based on network
      let gasEstimates = {
        standard: 0,
        fast: 0,
        instant: 0,
      };
      
      let timeEstimates = {
        standard: "",
        fast: "",
        instant: "",
      };
      
      switch (network) {
        case "ethereum":
          gasEstimates = {
            standard: 25,
            fast: 35,
            instant: 50,
          };
          timeEstimates = {
            standard: "~5 minutes",
            fast: "~2 minutes",
            instant: "~30 seconds",
          };
          break;
        case "polygon":
          gasEstimates = {
            standard: 0.5,
            fast: 1,
            instant: 2,
          };
          timeEstimates = {
            standard: "~1 minute",
            fast: "~30 seconds",
            instant: "~15 seconds",
          };
          break;
        case "arbitrum":
          gasEstimates = {
            standard: 1.5,
            fast: 2.5,
            instant: 4,
          };
          timeEstimates = {
            standard: "~2 minutes",
            fast: "~1 minute",
            instant: "~30 seconds",
          };
          break;
        case "optimism":
          gasEstimates = {
            standard: 1,
            fast: 2,
            instant: 3,
          };
          timeEstimates = {
            standard: "~2 minutes",
            fast: "~1 minute",
            instant: "~30 seconds",
          };
          break;
        case "base":
          gasEstimates = {
            standard: 0.8,
            fast: 1.5,
            instant: 2.5,
          };
          timeEstimates = {
            standard: "~1.5 minutes",
            fast: "~45 seconds",
            instant: "~20 seconds",
          };
          break;
      }
      
      setEstimatedGas(gasEstimates);
      setEstimatedTime(timeEstimates);
    } catch (error) {
      console.error("Error estimating gas costs:", error);
      toast({
        title: "Warning",
        description: "Failed to estimate gas costs. Using default values.",
        variant: "default",
      });
    }
  };

  // Handle network change
  const handleNetworkChange = (value: string) => {
    form.setValue("network", value as any);
    estimateGasCosts(value);
  };

  // Handle form submission
  const onSubmit = async (data: TokenizationFormValues) => {
    if (!carbonCredit) return;
    
    setIsSubmitting(true);
    
    try {
      // In a real implementation, this would call an API to tokenize the carbon credit
      // For now, we'll simulate a successful tokenization
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Tokenization initiated",
        description: "Your carbon credit is being tokenized. You can check the status on the next page.",
      });
      
      router.push(`/dashboard/carbon-credits/${params.id}/tokenize/status`);
    } catch (error) {
      console.error("Error tokenizing carbon credit:", error);
      toast({
        title: "Error",
        description: "Failed to tokenize carbon credit",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get network badge
  const getNetworkBadge = (network: string) => {
    switch (network) {
      case "ethereum":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            Ethereum
          </Badge>
        );
      case "polygon":
        return (
          <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">
            Polygon
          </Badge>
        );
      case "arbitrum":
        return (
          <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
            Arbitrum
          </Badge>
        );
      case "optimism":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
            Optimism
          </Badge>
        );
      case "base":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            Base
          </Badge>
        );
      default:
        return <Badge>{network}</Badge>;
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Tokenize Carbon Credit"
              description="Convert your carbon credit to a blockchain token"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: carbonCredit?.name || "Carbon Credit", href: `/dashboard/carbon-credits/${params.id}` },
                { label: "Tokenize", href: `/dashboard/carbon-credits/${params.id}/tokenize`, isCurrent: true }
              ]}
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : !carbonCredit ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                Carbon credit not found or you don't have permission to view it.
              </AlertDescription>
            </Alert>
          ) : carbonCredit.status !== "VERIFIED" ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Cannot Tokenize</AlertTitle>
              <AlertDescription>
                This carbon credit must be verified before it can be tokenized.
                Current status: {carbonCredit.status}
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-6 md:grid-cols-3">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Tokenization Settings</CardTitle>
                    <CardDescription>
                      Configure how your carbon credit will be tokenized on the blockchain
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                        <FormField
                          control={form.control}
                          name="network"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Blockchain Network</FormLabel>
                              <Select 
                                onValueChange={handleNetworkChange} 
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a network" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="ethereum">Ethereum</SelectItem>
                                  <SelectItem value="polygon">Polygon</SelectItem>
                                  <SelectItem value="arbitrum">Arbitrum</SelectItem>
                                  <SelectItem value="optimism">Optimism</SelectItem>
                                  <SelectItem value="base">Base</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Select the blockchain network where your carbon credit will be tokenized
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="useTestnet"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Use Testnet</FormLabel>
                                <FormDescription>
                                  Use a test network instead of the main network (recommended for first-time users)
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="securityLevel"
                          render={({ field }) => (
                            <FormItem className="space-y-3">
                              <FormLabel>Security Level</FormLabel>
                              <FormControl>
                                <RadioGroup
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  className="flex flex-col space-y-1"
                                >
                                  <FormItem className="flex items-center space-x-3 space-y-0">
                                    <FormControl>
                                      <RadioGroupItem value="standard" />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      Standard Security
                                    </FormLabel>
                                  </FormItem>
                                  <FormDescription className="pl-7">
                                    Basic security features suitable for most users
                                  </FormDescription>
                                  
                                  <FormItem className="flex items-center space-x-3 space-y-0">
                                    <FormControl>
                                      <RadioGroupItem value="high" />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      High Security
                                    </FormLabel>
                                  </FormItem>
                                  <FormDescription className="pl-7">
                                    Enhanced security with multi-signature requirements and additional verification steps
                                  </FormDescription>
                                </RadioGroup>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="gasOption"
                          render={({ field }) => (
                            <FormItem className="space-y-3">
                              <FormLabel>Transaction Speed</FormLabel>
                              <FormControl>
                                <RadioGroup
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  className="flex flex-col space-y-1"
                                >
                                  <FormItem className="flex items-center space-x-3 space-y-0">
                                    <FormControl>
                                      <RadioGroupItem value="standard" />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      Standard
                                    </FormLabel>
                                    <Badge variant="outline" className="ml-auto">
                                      ${estimatedGas.standard.toFixed(2)}
                                    </Badge>
                                    <Badge variant="outline">
                                      {estimatedTime.standard}
                                    </Badge>
                                  </FormItem>
                                  
                                  <FormItem className="flex items-center space-x-3 space-y-0">
                                    <FormControl>
                                      <RadioGroupItem value="fast" />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      Fast
                                    </FormLabel>
                                    <Badge variant="outline" className="ml-auto">
                                      ${estimatedGas.fast.toFixed(2)}
                                    </Badge>
                                    <Badge variant="outline">
                                      {estimatedTime.fast}
                                    </Badge>
                                  </FormItem>
                                  
                                  <FormItem className="flex items-center space-x-3 space-y-0">
                                    <FormControl>
                                      <RadioGroupItem value="instant" />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      Instant
                                    </FormLabel>
                                    <Badge variant="outline" className="ml-auto">
                                      ${estimatedGas.instant.toFixed(2)}
                                    </Badge>
                                    <Badge variant="outline">
                                      {estimatedTime.instant}
                                    </Badge>
                                  </FormItem>
                                </RadioGroup>
                              </FormControl>
                              <FormDescription>
                                Select how quickly you want your transaction to be processed
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="autoRetry"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Auto-Retry</FormLabel>
                                <FormDescription>
                                  Automatically retry the transaction if it fails due to network congestion
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <div className="flex justify-between">
                          <Button 
                            type="button" 
                            variant="outline" 
                            onClick={() => router.push(`/dashboard/carbon-credits/${params.id}`)}
                          >
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Details
                          </Button>
                          
                          <Button 
                            type="submit" 
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Tokenizing...
                              </>
                            ) : (
                              <>
                                <Coins className="mr-2 h-4 w-4" />
                                Tokenize Carbon Credit
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>
              
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Carbon Credit Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium">Name</h3>
                        <p className="text-sm">{carbonCredit.name}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Quantity</h3>
                        <p className="text-sm">{carbonCredit.quantity.toLocaleString()} tons</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Vintage</h3>
                        <p className="text-sm">{carbonCredit.vintage}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Standard</h3>
                        <p className="text-sm">{carbonCredit.standard}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Methodology</h3>
                        <p className="text-sm">{carbonCredit.methodology}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Status</h3>
                        <p className="text-sm">{carbonCredit.status}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Network Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium">Selected Network</h3>
                        <div className="mt-1">
                          {getNetworkBadge(network)}
                          {useTestnet && (
                            <Badge variant="outline" className="ml-2">
                              Testnet
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Gas Cost (Estimated)</h3>
                        <p className="text-sm">
                          ${gasOption === "standard" 
                            ? estimatedGas.standard.toFixed(2) 
                            : gasOption === "fast" 
                            ? estimatedGas.fast.toFixed(2) 
                            : estimatedGas.instant.toFixed(2)}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Processing Time (Estimated)</h3>
                        <p className="text-sm">
                          {gasOption === "standard" 
                            ? estimatedTime.standard 
                            : gasOption === "fast" 
                            ? estimatedTime.fast 
                            : estimatedTime.instant}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Important Information</AlertTitle>
                  <AlertDescription>
                    <p className="mb-2">
                      Tokenizing your carbon credit will create a digital asset on the blockchain that represents your carbon credit.
                    </p>
                    <p>
                      This process is irreversible and will incur gas fees.
                    </p>
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
