"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Coins, Loader2, AlertCircle, CheckCircle2, Info, ExternalLink, Copy, RefreshCw } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

interface TokenizationStatus {
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
  message: string;
  progress: number;
  transactionHash: string | null;
  tokenId: string | null;
  contractAddress: string | null;
  network: string;
  isTestnet: boolean;
  createdAt: string;
  updatedAt: string;
  estimatedCompletionTime: string | null;
  errorMessage: string | null;
}

interface CarbonCredit {
  id: string;
  name: string;
  quantity: number;
  vintage: number;
  standard: string;
  methodology: string;
  status: string;
  tokenizationStatus: TokenizationStatus;
}

export default function TokenizationStatusPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [carbonCredit, setCarbonCredit] = useState<CarbonCredit | null>(null);
  const [activeTab, setActiveTab] = useState("status");

  // Fetch carbon credit and tokenization status on component mount
  useEffect(() => {
    const fetchTokenizationStatus = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, this would call an API to fetch the tokenization status
        // For now, we'll use mock data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockCredit: CarbonCredit = {
          id: params.id,
          name: "Renewable Energy Project",
          quantity: 1000,
          vintage: 2023,
          standard: "Verra",
          methodology: "VM0025",
          status: "TOKENIZING",
          tokenizationStatus: {
            status: "PROCESSING",
            message: "Tokenization in progress",
            progress: 60,
            transactionHash: "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
            tokenId: null,
            contractAddress: "******************************************",
            network: "polygon",
            isTestnet: true,
            createdAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
            updatedAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
            estimatedCompletionTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now
            errorMessage: null,
          },
        };
        
        setCarbonCredit(mockCredit);
      } catch (error) {
        console.error("Error fetching tokenization status:", error);
        toast({
          title: "Error",
          description: "Failed to fetch tokenization status",
          variant: "destructive",
        });
        router.push(`/dashboard/carbon-credits/${params.id}`);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTokenizationStatus();
    
    // Set up polling for status updates if tokenization is in progress
    const intervalId = setInterval(() => {
      if (carbonCredit?.tokenizationStatus.status === "PROCESSING") {
        refreshStatus();
      }
    }, 10000); // Poll every 10 seconds
    
    return () => clearInterval(intervalId);
  }, [params.id, router, carbonCredit?.tokenizationStatus.status]);

  // Refresh tokenization status
  const refreshStatus = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    
    try {
      // In a real implementation, this would call an API to fetch the latest status
      // For now, we'll simulate a status update
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (carbonCredit) {
        // Simulate progress update
        const currentProgress = carbonCredit.tokenizationStatus.progress;
        let newStatus = carbonCredit.tokenizationStatus.status;
        let newMessage = carbonCredit.tokenizationStatus.message;
        let newTokenId = carbonCredit.tokenizationStatus.tokenId;
        let newProgress = Math.min(100, currentProgress + 10);
        
        // If progress reaches 100%, mark as completed
        if (newProgress >= 100) {
          newStatus = "COMPLETED";
          newMessage = "Tokenization completed successfully";
          newTokenId = "123456789";
        }
        
        setCarbonCredit({
          ...carbonCredit,
          status: newProgress >= 100 ? "TOKENIZED" : "TOKENIZING",
          tokenizationStatus: {
            ...carbonCredit.tokenizationStatus,
            status: newStatus as any,
            message: newMessage,
            progress: newProgress,
            tokenId: newTokenId,
            updatedAt: new Date().toISOString(),
          },
        });
      }
    } catch (error) {
      console.error("Error refreshing tokenization status:", error);
      toast({
        title: "Error",
        description: "Failed to refresh tokenization status",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Pending
          </Badge>
        );
      case "PROCESSING":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Loader2 className="h-3 w-3 animate-spin" />
            Processing
          </Badge>
        );
      case "COMPLETED":
        return (
          <Badge variant="success" className="flex items-center gap-1 bg-green-100 text-green-800 hover:bg-green-200">
            <CheckCircle2 className="h-3 w-3" />
            Completed
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get network badge
  const getNetworkBadge = (network: string) => {
    switch (network) {
      case "ethereum":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            Ethereum
          </Badge>
        );
      case "polygon":
        return (
          <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">
            Polygon
          </Badge>
        );
      case "arbitrum":
        return (
          <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
            Arbitrum
          </Badge>
        );
      case "optimism":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
            Optimism
          </Badge>
        );
      case "base":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            Base
          </Badge>
        );
      default:
        return <Badge>{network}</Badge>;
    }
  };

  // Get blockchain explorer URL
  const getExplorerUrl = (network: string, isTestnet: boolean, type: "transaction" | "token" | "address", value: string) => {
    let baseUrl = "";
    
    switch (network) {
      case "ethereum":
        baseUrl = isTestnet ? "https://sepolia.etherscan.io" : "https://etherscan.io";
        break;
      case "polygon":
        baseUrl = isTestnet ? "https://mumbai.polygonscan.com" : "https://polygonscan.com";
        break;
      case "arbitrum":
        baseUrl = isTestnet ? "https://testnet.arbiscan.io" : "https://arbiscan.io";
        break;
      case "optimism":
        baseUrl = isTestnet ? "https://sepolia-optimism.etherscan.io" : "https://optimistic.etherscan.io";
        break;
      case "base":
        baseUrl = isTestnet ? "https://goerli.basescan.org" : "https://basescan.org";
        break;
      default:
        baseUrl = "https://etherscan.io";
    }
    
    switch (type) {
      case "transaction":
        return `${baseUrl}/tx/${value}`;
      case "token":
        return `${baseUrl}/token/${value}`;
      case "address":
        return `${baseUrl}/address/${value}`;
      default:
        return baseUrl;
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: `${label} has been copied to your clipboard.`,
    });
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Tokenization Status"
              description="Track the status of your carbon credit tokenization"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: carbonCredit?.name || "Carbon Credit", href: `/dashboard/carbon-credits/${params.id}` },
                { label: "Tokenize", href: `/dashboard/carbon-credits/${params.id}/tokenize` },
                { label: "Status", href: `/dashboard/carbon-credits/${params.id}/tokenize/status`, isCurrent: true }
              ]}
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : !carbonCredit ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                Carbon credit not found or you don't have permission to view it.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-6 md:grid-cols-3">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Tokenization Status</CardTitle>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={refreshStatus}
                        disabled={isRefreshing || carbonCredit.tokenizationStatus.status === "COMPLETED"}
                      >
                        <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
                        Refresh
                      </Button>
                    </div>
                    <CardDescription>
                      Current status of your carbon credit tokenization
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium">Status</h3>
                        <div className="mt-1">
                          {getStatusBadge(carbonCredit.tokenizationStatus.status)}
                        </div>
                      </div>
                      <div className="text-right">
                        <h3 className="text-sm font-medium">Last Updated</h3>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(carbonCredit.tokenizationStatus.updatedAt)}
                        </p>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium mb-2">Progress</h3>
                      <Progress value={carbonCredit.tokenizationStatus.progress} className="h-2" />
                      <p className="mt-2 text-sm text-muted-foreground">
                        {carbonCredit.tokenizationStatus.message}
                      </p>
                    </div>
                    
                    {carbonCredit.tokenizationStatus.estimatedCompletionTime && carbonCredit.tokenizationStatus.status === "PROCESSING" && (
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Estimated Completion</AlertTitle>
                        <AlertDescription>
                          The tokenization process is expected to complete around {formatDate(carbonCredit.tokenizationStatus.estimatedCompletionTime)}.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {carbonCredit.tokenizationStatus.errorMessage && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>
                          {carbonCredit.tokenizationStatus.errorMessage}
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="status">Status Details</TabsTrigger>
                        <TabsTrigger value="transaction">Transaction Details</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="status" className="space-y-4 pt-4">
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h3 className="text-sm font-medium">Carbon Credit</h3>
                              <p className="text-sm">{carbonCredit.name}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Quantity</h3>
                              <p className="text-sm">{carbonCredit.quantity.toLocaleString()} tons</p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h3 className="text-sm font-medium">Vintage</h3>
                              <p className="text-sm">{carbonCredit.vintage}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Standard</h3>
                              <p className="text-sm">{carbonCredit.standard}</p>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h3 className="text-sm font-medium">Started At</h3>
                              <p className="text-sm">{formatDate(carbonCredit.tokenizationStatus.createdAt)}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Network</h3>
                              <div className="flex items-center mt-1">
                                {getNetworkBadge(carbonCredit.tokenizationStatus.network)}
                                {carbonCredit.tokenizationStatus.isTestnet && (
                                  <Badge variant="outline" className="ml-2">
                                    Testnet
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          {carbonCredit.tokenizationStatus.tokenId && (
                            <div>
                              <h3 className="text-sm font-medium">Token ID</h3>
                              <div className="flex items-center mt-1">
                                <p className="text-sm font-mono">{carbonCredit.tokenizationStatus.tokenId}</p>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="ml-2 h-6 w-6 p-0" 
                                  onClick={() => copyToClipboard(carbonCredit.tokenizationStatus.tokenId!, "Token ID")}
                                >
                                  <Copy className="h-3 w-3" />
                                  <span className="sr-only">Copy token ID</span>
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="transaction" className="space-y-4 pt-4">
                        {carbonCredit.tokenizationStatus.transactionHash && (
                          <div>
                            <h3 className="text-sm font-medium">Transaction Hash</h3>
                            <div className="flex items-center mt-1">
                              <p className="text-sm font-mono truncate max-w-[300px]">
                                {carbonCredit.tokenizationStatus.transactionHash}
                              </p>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="ml-2 h-6 w-6 p-0" 
                                onClick={() => copyToClipboard(carbonCredit.tokenizationStatus.transactionHash!, "Transaction hash")}
                              >
                                <Copy className="h-3 w-3" />
                                <span className="sr-only">Copy transaction hash</span>
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="ml-1 h-6 w-6 p-0" 
                                onClick={() => window.open(
                                  getExplorerUrl(
                                    carbonCredit.tokenizationStatus.network,
                                    carbonCredit.tokenizationStatus.isTestnet,
                                    "transaction",
                                    carbonCredit.tokenizationStatus.transactionHash!
                                  ),
                                  "_blank"
                                )}
                              >
                                <ExternalLink className="h-3 w-3" />
                                <span className="sr-only">View transaction</span>
                              </Button>
                            </div>
                          </div>
                        )}
                        
                        {carbonCredit.tokenizationStatus.contractAddress && (
                          <div>
                            <h3 className="text-sm font-medium">Contract Address</h3>
                            <div className="flex items-center mt-1">
                              <p className="text-sm font-mono truncate max-w-[300px]">
                                {carbonCredit.tokenizationStatus.contractAddress}
                              </p>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="ml-2 h-6 w-6 p-0" 
                                onClick={() => copyToClipboard(carbonCredit.tokenizationStatus.contractAddress!, "Contract address")}
                              >
                                <Copy className="h-3 w-3" />
                                <span className="sr-only">Copy contract address</span>
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="ml-1 h-6 w-6 p-0" 
                                onClick={() => window.open(
                                  getExplorerUrl(
                                    carbonCredit.tokenizationStatus.network,
                                    carbonCredit.tokenizationStatus.isTestnet,
                                    "address",
                                    carbonCredit.tokenizationStatus.contractAddress!
                                  ),
                                  "_blank"
                                )}
                              >
                                <ExternalLink className="h-3 w-3" />
                                <span className="sr-only">View contract</span>
                              </Button>
                            </div>
                          </div>
                        )}
                        
                        <div>
                          <h3 className="text-sm font-medium">Blockchain Explorer</h3>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mt-2" 
                            onClick={() => window.open(
                              getExplorerUrl(
                                carbonCredit.tokenizationStatus.network,
                                carbonCredit.tokenizationStatus.isTestnet,
                                "transaction",
                                carbonCredit.tokenizationStatus.transactionHash || ""
                              ),
                              "_blank"
                            )}
                            disabled={!carbonCredit.tokenizationStatus.transactionHash}
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            View on Blockchain Explorer
                          </Button>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button 
                      variant="outline" 
                      onClick={() => router.push(`/dashboard/carbon-credits/${params.id}`)}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Details
                    </Button>
                    
                    {carbonCredit.tokenizationStatus.status === "COMPLETED" && (
                      <Button onClick={() => router.push("/dashboard/wallet")}>
                        <Coins className="mr-2 h-4 w-4" />
                        View in Wallet
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              </div>
              
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>What's Happening?</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm">
                        Your carbon credit is being tokenized on the blockchain. This process involves several steps:
                      </p>
                      
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li className={carbonCredit.tokenizationStatus.progress >= 20 ? "text-green-600 font-medium" : ""}>
                          Preparing carbon credit data
                        </li>
                        <li className={carbonCredit.tokenizationStatus.progress >= 40 ? "text-green-600 font-medium" : ""}>
                          Creating smart contract transaction
                        </li>
                        <li className={carbonCredit.tokenizationStatus.progress >= 60 ? "text-green-600 font-medium" : ""}>
                          Submitting transaction to the blockchain
                        </li>
                        <li className={carbonCredit.tokenizationStatus.progress >= 80 ? "text-green-600 font-medium" : ""}>
                          Waiting for transaction confirmation
                        </li>
                        <li className={carbonCredit.tokenizationStatus.progress >= 100 ? "text-green-600 font-medium" : ""}>
                          Finalizing tokenization
                        </li>
                      </ol>
                      
                      <p className="text-sm">
                        The time required for tokenization depends on network congestion and the selected transaction speed.
                      </p>
                    </div>
                  </CardContent>
                </Card>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>What's Next?</AlertTitle>
                  <AlertDescription>
                    <p className="mb-2">
                      Once tokenization is complete, your carbon credit will be available as a digital asset in your wallet.
                    </p>
                    <p>
                      You can then trade, transfer, or retire your tokenized carbon credit.
                    </p>
                  </AlertDescription>
                </Alert>
                
                {carbonCredit.tokenizationStatus.status === "PROCESSING" && (
                  <Alert variant="default" className="bg-yellow-50 border-yellow-200">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <AlertTitle>Processing</AlertTitle>
                    <AlertDescription>
                      <p>
                        You can leave this page and come back later. The tokenization process will continue in the background.
                      </p>
                    </AlertDescription>
                  </Alert>
                )}
                
                {carbonCredit.tokenizationStatus.status === "COMPLETED" && (
                  <Alert variant="default" className="bg-green-50 border-green-200">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertTitle>Success!</AlertTitle>
                    <AlertDescription>
                      <p>
                        Your carbon credit has been successfully tokenized and is now available in your wallet.
                      </p>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
