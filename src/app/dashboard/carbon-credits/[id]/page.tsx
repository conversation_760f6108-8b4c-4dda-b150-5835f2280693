"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, ArrowLeft, Edit, Trash2, CheckCircle2, XCircle, Coins, Send, History, Zap } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { TokenizeButton } from "@/components/carbon-credits/tokenize-button";
import { TransferButton } from "@/components/carbon-credits/transfer-button";
import { RetireButton } from "@/components/carbon-credits/retire-button";
import { TransactionHistory } from "@/components/carbon-credits/transaction-history";
import { GasEstimation } from "@/components/carbon-credits/gas-estimation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  availableQuantity: number;
  retiredQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  status: string;
  createdAt: string;
  updatedAt: string;
  tokenId: string | null;
  tokenAddress: string | null;
  tokenNetwork: string | null;
  tokenChainId: number | null;
  organization: {
    id: string;
    name: string;
  };
  user: {
    name: string | null;
    email: string;
  };
}

export default function CarbonCreditDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { data: session } = useSession();
  const router = useRouter();
  const [carbonCredit, setCarbonCredit] = useState<CarbonCredit | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  useEffect(() => {
    async function fetchCarbonCredit() {
      try {
        const response = await fetch(`/api/carbon-credits/${params.id}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch carbon credit");
        }

        setCarbonCredit(data.carbonCredit);
      } catch (error) {
        console.error("Error fetching carbon credit:", error);
        setError("Failed to load carbon credit");
      } finally {
        setIsLoading(false);
      }
    }

    fetchCarbonCredit();
  }, [params.id]);

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const response = await fetch(`/api/carbon-credits/${params.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete carbon credit");
      }

      toast({
        title: "Carbon credit deleted",
        description: "The carbon credit has been deleted successfully.",
      });

      router.push("/dashboard/carbon-credits");
    } catch (error) {
      console.error("Error deleting carbon credit:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete carbon credit",
        variant: "destructive",
      });
      setIsDeleting(false);
    }
  };

  const handleStatusUpdate = async (newStatus: string) => {
    setIsUpdatingStatus(true);

    try {
      const response = await fetch(`/api/carbon-credits/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update carbon credit status");
      }

      setCarbonCredit({
        ...carbonCredit!,
        status: newStatus,
      });

      toast({
        title: "Status updated",
        description: `Carbon credit status has been updated to ${newStatus.charAt(0) + newStatus.slice(1).toLowerCase()}.`,
      });
    } catch (error) {
      console.error("Error updating carbon credit status:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update carbon credit status",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "PENDING":
        return "secondary";
      case "VERIFIED":
        return "outline";
      case "LISTED":
        return "default";
      case "SOLD":
        return "success";
      case "RETIRED":
        return "destructive";
      default:
        return "outline";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !carbonCredit) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center space-y-4">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <p className="text-center text-lg font-medium">{error || "Carbon credit not found"}</p>
          <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits")}>
            Back to Carbon Credits
          </Button>
        </div>
      </div>
    );
  }

  const canEdit = session?.user?.organizationId === carbonCredit.organization.id;
  const isPending = carbonCredit.status === "PENDING";
  const isVerified = carbonCredit.status === "VERIFIED";
  const isListed = carbonCredit.status === "LISTED";
  const isTokenized = carbonCredit.status === "TOKENIZED";

  // Function to refresh the carbon credit data
  const refreshCarbonCredit = async () => {
    try {
      const response = await fetch(`/api/carbon-credits/${params.id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch carbon credit");
      }

      setCarbonCredit(data.carbonCredit);
    } catch (error) {
      console.error("Error refreshing carbon credit:", error);
      toast({
        title: "Error",
        description: "Failed to refresh carbon credit data",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container py-10">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <Button
            variant="outline"
            onClick={() => router.push("/dashboard/carbon-credits")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Carbon Credits
          </Button>
          <h1 className="text-3xl font-bold">{carbonCredit.name}</h1>
          <div className="mt-2 flex items-center gap-2">
            <Badge variant={getStatusBadgeVariant(carbonCredit.status)}>
              {carbonCredit.status.charAt(0) + carbonCredit.status.slice(1).toLowerCase()}
            </Badge>
            <p className="text-sm text-muted-foreground">
              Created on {formatDate(carbonCredit.createdAt)}
            </p>
          </div>
        </div>
        {canEdit && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/carbon-credits/${params.id}/edit`)}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Carbon Credit</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete this carbon credit? This action cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="mt-4">
                  <Button variant="outline" onClick={() => {}}>
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Tabs defaultValue="details">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="transactions">
                <History className="mr-2 h-4 w-4" />
                Transactions
              </TabsTrigger>
              <TabsTrigger value="gas">
                <Zap className="mr-2 h-4 w-4" />
                Gas Estimation
              </TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="space-y-6 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-line">
                    {carbonCredit.description || "No description provided."}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Project Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <h3 className="font-medium">Vintage Year</h3>
                      <p>{carbonCredit.vintage}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Standard</h3>
                      <p>{carbonCredit.standard}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Methodology</h3>
                      <p>{carbonCredit.methodology}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Location</h3>
                      <p>{carbonCredit.location || "Not specified"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="transactions" className="pt-4">
              <TransactionHistory carbonCreditId={carbonCredit.id} />
            </TabsContent>

            <TabsContent value="gas" className="pt-4">
              <div className="space-y-6">
                {isTokenized ? (
                  <>
                    <GasEstimation
                      carbonCreditId={carbonCredit.id}
                      type="transfer"
                      amount={carbonCredit.availableQuantity || carbonCredit.quantity}
                      toAddress="******************************************"
                      network={carbonCredit.tokenNetwork as any}
                    />

                    <GasEstimation
                      carbonCreditId={carbonCredit.id}
                      type="retire"
                      amount={carbonCredit.availableQuantity || carbonCredit.quantity}
                      network={carbonCredit.tokenNetwork as any}
                    />
                  </>
                ) : isVerified ? (
                  <GasEstimation
                    carbonCreditId={carbonCredit.id}
                    type="tokenize"
                  />
                ) : (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="py-6 text-center text-muted-foreground">
                        <p>Carbon credit must be verified before gas estimation is available.</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Trading Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Total Quantity</h3>
                  <p className="text-2xl font-bold">
                    {carbonCredit.quantity.toLocaleString()} tons
                  </p>
                </div>
                <Separator />
                <div>
                  <h3 className="font-medium">Available Quantity</h3>
                  <p className="text-xl font-semibold">
                    {(carbonCredit.availableQuantity || carbonCredit.quantity).toLocaleString()} tons
                  </p>
                </div>
                {carbonCredit.retiredQuantity > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="font-medium">Retired Quantity</h3>
                      <p className="text-xl font-semibold">
                        {carbonCredit.retiredQuantity.toLocaleString()} tons
                      </p>
                    </div>
                  </>
                )}
                <Separator />
                <div>
                  <h3 className="font-medium">Price per Ton</h3>
                  <p className="text-2xl font-bold">₹{carbonCredit.price.toFixed(2)}</p>
                </div>
                <Separator />
                <div>
                  <h3 className="font-medium">Total Value</h3>
                  <p className="text-2xl font-bold">
                    ₹{(carbonCredit.quantity * carbonCredit.price).toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {canEdit && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
                <CardDescription>
                  Manage the status of your carbon credit
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isPending && (
                    <Button
                      className="w-full"
                      onClick={() => handleStatusUpdate("VERIFIED")}
                      disabled={isUpdatingStatus}
                    >
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      {isUpdatingStatus ? "Updating..." : "Mark as Verified"}
                    </Button>
                  )}

                  {isVerified && (
                    <>
                      <Button
                        className="w-full"
                        onClick={() => handleStatusUpdate("LISTED")}
                        disabled={isUpdatingStatus}
                      >
                        <CheckCircle2 className="mr-2 h-4 w-4" />
                        {isUpdatingStatus ? "Updating..." : "List on Marketplace"}
                      </Button>

                      <TokenizeButton
                        carbonCreditId={carbonCredit.id}
                        disabled={isUpdatingStatus}
                        onSuccess={refreshCarbonCredit}
                      />
                    </>
                  )}

                  {isListed && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => handleStatusUpdate("VERIFIED")}
                      disabled={isUpdatingStatus}
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      {isUpdatingStatus ? "Updating..." : "Remove from Marketplace"}
                    </Button>
                  )}

                  {isTokenized && (
                    <div className="space-y-2">
                      <div className="rounded-md border p-3">
                        <h3 className="font-medium">Token Information</h3>
                        <div className="mt-2 space-y-1 text-sm">
                          <p><span className="font-medium">Token ID:</span> {carbonCredit.tokenId}</p>
                          <p><span className="font-medium">Network:</span> {carbonCredit.tokenNetwork}</p>
                          <p className="break-all"><span className="font-medium">Contract:</span> {carbonCredit.tokenAddress}</p>
                        </div>
                      </div>

                      <div className="flex flex-col gap-2">
                        <TransferButton
                          carbonCreditId={carbonCredit.id}
                          maxAmount={carbonCredit.availableQuantity || carbonCredit.quantity}
                          onSuccess={refreshCarbonCredit}
                        />

                        <RetireButton
                          carbonCreditId={carbonCredit.id}
                          maxAmount={carbonCredit.availableQuantity || carbonCredit.quantity}
                          onSuccess={refreshCarbonCredit}
                        />
                      </div>
                    </div>
                  )}

                  {(isVerified || isListed) && !isTokenized && (
                    <Button
                      variant="destructive"
                      className="w-full"
                      onClick={() => handleStatusUpdate("RETIRED")}
                      disabled={isUpdatingStatus}
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      {isUpdatingStatus ? "Updating..." : "Retire Carbon Credit"}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Organization</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{carbonCredit.organization.name}</p>
              <p className="text-sm text-muted-foreground">
                Created by: {carbonCredit.user.name || carbonCredit.user.email}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
