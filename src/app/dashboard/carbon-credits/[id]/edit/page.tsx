"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { ArrowLeft, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CarbonCreditUpdateWizard } from "@/components/carbon-credits/carbon-credit-update-wizard";
import { PageTransition } from "@/components/animations/page-transition";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";

interface CarbonCredit {
  id: string;
  name: string;
  description: string;
  quantity: number;
  availableQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  location?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
  };
}

export default function EditCarbonCreditPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { data: session } = useSession();
  const [carbonCredit, setCarbonCredit] = useState<CarbonCredit | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCarbonCredit() {
      try {
        const response = await fetch(`/api/carbon-credits/${params.id}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch carbon credit");
        }

        setCarbonCredit(data.carbonCredit);
      } catch (error) {
        console.error("Error fetching carbon credit:", error);
        setError("Failed to load carbon credit");
      } finally {
        setIsLoading(false);
      }
    }

    fetchCarbonCredit();
  }, [params.id]);

  // Check if user has permission to edit this carbon credit
  const canEdit = session?.user?.organizationId === carbonCredit?.organization.id;

  const handleSuccess = () => {
    router.push(`/dashboard/carbon-credits/${params.id}`);
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (error || !carbonCredit) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center space-y-4">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <p className="text-center text-lg font-medium">{error || "Carbon credit not found"}</p>
          <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits")}>
            Back to Carbon Credits
          </Button>
        </div>
      </div>
    );
  }

  if (!canEdit) {
    return (
      <div className="container py-10">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You do not have permission to edit this carbon credit.
          </AlertDescription>
        </Alert>
        <Button
          variant="outline"
          onClick={() => router.push(`/dashboard/carbon-credits/${params.id}`)}
          className="mt-4"
        >
          Back to Carbon Credit
        </Button>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="container py-10">
        <PageHeaderWithBreadcrumb
          title="Edit Carbon Credit"
          description="Update the details of your carbon credit"
          breadcrumbItems={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
            { label: carbonCredit.name, href: `/dashboard/carbon-credits/${params.id}` },
            { label: "Edit", href: `/dashboard/carbon-credits/${params.id}/edit`, isCurrent: true },
          ]}
        />

        <div className="mt-6">
          <CarbonCreditUpdateWizard 
            carbonCredit={carbonCredit} 
            onSuccess={handleSuccess} 
          />
        </div>
      </div>
    </PageTransition>
  );
}
