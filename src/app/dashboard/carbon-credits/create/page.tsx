"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileUp, PenLine, Database, ArrowRight } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";

export default function CarbonCreditCreatePage() {
  const router = useRouter();
  const { data: session } = useSession();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Create Carbon Credit"
              description="Choose a method to create a new carbon credit"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: "Create", href: "/dashboard/carbon-credits/create", isCurrent: true }
              ]}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card className="hover:border-primary/50 transition-all cursor-pointer" 
                  onClick={() => router.push("/dashboard/carbon-credits/create/manual")}>
              <CardHeader>
                <PenLine className="h-8 w-8 mb-2 text-primary" />
                <CardTitle>Manual Entry</CardTitle>
                <CardDescription>
                  Create a carbon credit by manually entering all details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Best for creating individual carbon credits with detailed information.
                </p>
                <ul className="list-disc list-inside text-sm space-y-1 text-muted-foreground">
                  <li>Full control over all credit details</li>
                  <li>Step-by-step guided process</li>
                  <li>Upload supporting documentation</li>
                </ul>
              </CardContent>
              <div className="px-6 pb-4">
                <Button variant="outline" className="w-full">
                  Start Manual Entry
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </Card>

            <Card className="hover:border-primary/50 transition-all cursor-pointer"
                  onClick={() => router.push("/dashboard/carbon-credits/create/import")}>
              <CardHeader>
                <FileUp className="h-8 w-8 mb-2 text-primary" />
                <CardTitle>Bulk Import</CardTitle>
                <CardDescription>
                  Import multiple carbon credits from a CSV file
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Best for importing multiple carbon credits at once.
                </p>
                <ul className="list-disc list-inside text-sm space-y-1 text-muted-foreground">
                  <li>Upload CSV with credit details</li>
                  <li>Validate and review before importing</li>
                  <li>Batch process multiple credits</li>
                </ul>
              </CardContent>
              <div className="px-6 pb-4">
                <Button variant="outline" className="w-full">
                  Start Bulk Import
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </Card>

            <Card className="hover:border-primary/50 transition-all cursor-pointer"
                  onClick={() => router.push("/dashboard/carbon-credits/create/api")}>
              <CardHeader>
                <Database className="h-8 w-8 mb-2 text-primary" />
                <CardTitle>API Import</CardTitle>
                <CardDescription>
                  Import carbon credits from external registries
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Best for importing from verified external sources.
                </p>
                <ul className="list-disc list-inside text-sm space-y-1 text-muted-foreground">
                  <li>Connect to Verra, Gold Standard, etc.</li>
                  <li>Automatic verification</li>
                  <li>Maintain data integrity</li>
                </ul>
              </CardContent>
              <div className="px-6 pb-4">
                <Button variant="outline" className="w-full">
                  Start API Import
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </Card>
          </div>

          <div className="mt-6">
            <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Carbon Credits
            </Button>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
