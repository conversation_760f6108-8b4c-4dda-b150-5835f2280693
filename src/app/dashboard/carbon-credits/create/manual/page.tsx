"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { CarbonCreditForm } from "@/components/forms/carbon-credit-form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";

export default function ManualCarbonCreditCreatePage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSuccess = () => {
    router.push("/dashboard/carbon-credits");
    router.refresh();
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Manual Carbon Credit Entry"
              description="Create a new carbon credit by entering details manually"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: "Create", href: "/dashboard/carbon-credits/create" },
                { label: "Manual Entry", href: "/dashboard/carbon-credits/create/manual", isCurrent: true }
              ]}
            />
          </div>

          <div className="mb-6">
            <Alert variant="default" className="bg-blue-50 border-blue-200">
              <InfoIcon className="h-4 w-4 text-blue-500" />
              <AlertTitle>Important Information</AlertTitle>
              <AlertDescription>
                <p className="mb-2">
                  All carbon credits undergo verification before they can be listed on the marketplace.
                  Please provide accurate information and be prepared to submit supporting documentation.
                </p>
                <p>
                  After submission, your carbon credit will be in a pending state until verified by our team.
                </p>
              </AlertDescription>
            </Alert>
          </div>

          <div className="grid gap-6">
            <CarbonCreditForm onSuccess={handleSuccess} />
          </div>

          <div className="mt-6">
            <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits/create")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Creation Options
            </Button>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
