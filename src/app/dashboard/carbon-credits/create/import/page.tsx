"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Upload, Download, FileText, Check, AlertCircle, Loader2 } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON>Des<PERSON>, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

// Schema for CSV upload
const csvUploadSchema = z.object({
  file: z.instanceof(FileList).refine(files => files.length === 1, {
    message: "Please select a CSV file",
  }),
});

type CsvUploadFormValues = z.infer<typeof csvUploadSchema>;

interface CsvPreviewData {
  headers: string[];
  rows: string[][];
  errors: { row: number; message: string }[];
}

export default function BulkImportCarbonCreditPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isUploading, setIsUploading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [previewData, setPreviewData] = useState<CsvPreviewData | null>(null);
  const [activeTab, setActiveTab] = useState("upload");

  const form = useForm<CsvUploadFormValues>({
    resolver: zodResolver(csvUploadSchema),
    defaultValues: {
      file: undefined,
    },
  });

  const downloadTemplate = () => {
    // Create CSV template content
    const headers = [
      "name",
      "description",
      "quantity",
      "price",
      "vintage",
      "standard",
      "methodology",
      "location",
      "country",
      "projectId",
      "serialNumber",
      "verificationBody",
    ].join(",");
    
    const exampleRow = [
      "Renewable Energy Project",
      "Wind farm in Texas generating clean energy",
      "1000",
      "15.50",
      "2023",
      "Verra",
      "VM0025",
      "Texas, USA",
      "USA",
      "VCS-123456",
      "SN-789012",
      "Verra Registry",
    ].join(",");
    
    const csvContent = `${headers}\n${exampleRow}`;
    
    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "carbon-credits-template.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Template downloaded",
      description: "CSV template has been downloaded successfully.",
    });
  };

  const parseCSV = (text: string): CsvPreviewData => {
    const rows = text.split(/\\r?\\n/).filter(row => row.trim());
    const headers = rows[0].split(",").map(header => header.trim());
    
    const dataRows = rows.slice(1).map(row => row.split(",").map(cell => cell.trim()));
    
    // Basic validation
    const errors: { row: number; message: string }[] = [];
    
    dataRows.forEach((row, index) => {
      if (row.length !== headers.length) {
        errors.push({
          row: index + 2, // +2 because index is 0-based and we skip header row
          message: `Row has ${row.length} columns, expected ${headers.length}`,
        });
      }
      
      // Check for required fields
      const requiredFields = ["name", "quantity", "price", "vintage", "standard", "methodology"];
      requiredFields.forEach(field => {
        const fieldIndex = headers.indexOf(field);
        if (fieldIndex !== -1 && !row[fieldIndex]) {
          errors.push({
            row: index + 2,
            message: `Missing required field: ${field}`,
          });
        }
      });
    });
    
    return { headers, rows: dataRows, errors };
  };

  const onSubmit = async (data: CsvUploadFormValues) => {
    setIsUploading(true);
    
    try {
      const file = data.file[0];
      
      if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
        toast({
          title: "Invalid file type",
          description: "Please upload a CSV file",
          variant: "destructive",
        });
        return;
      }
      
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const text = e.target?.result as string;
        const parsedData = parseCSV(text);
        setPreviewData(parsedData);
        setActiveTab("preview");
        setIsUploading(false);
      };
      
      reader.onerror = () => {
        toast({
          title: "Error reading file",
          description: "There was an error reading the CSV file",
          variant: "destructive",
        });
        setIsUploading(false);
      };
      
      reader.readAsText(file);
    } catch (error) {
      console.error("Error uploading CSV:", error);
      toast({
        title: "Error",
        description: "Failed to upload CSV file",
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  const handleImport = async () => {
    if (!previewData || previewData.errors.length > 0) {
      toast({
        title: "Cannot import",
        description: "Please fix the errors in your CSV file before importing",
        variant: "destructive",
      });
      return;
    }
    
    setIsImporting(true);
    
    try {
      // Convert preview data to carbon credit objects
      const headers = previewData.headers;
      const carbonCredits = previewData.rows.map(row => {
        const credit: Record<string, any> = {};
        
        headers.forEach((header, index) => {
          // Convert numeric fields
          if (["quantity", "price", "vintage"].includes(header)) {
            credit[header] = Number(row[index]);
          } else {
            credit[header] = row[index];
          }
        });
        
        return credit;
      });
      
      // Call API to import carbon credits
      const response = await fetch("/api/carbon-credits/bulk-import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ carbonCredits }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || "Failed to import carbon credits");
      }
      
      toast({
        title: "Import successful",
        description: `Successfully imported ${result.imported} carbon credits`,
      });
      
      router.push("/dashboard/carbon-credits");
      router.refresh();
    } catch (error) {
      console.error("Error importing carbon credits:", error);
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "Failed to import carbon credits",
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Bulk Import Carbon Credits"
              description="Import multiple carbon credits from a CSV file"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: "Create", href: "/dashboard/carbon-credits/create" },
                { label: "Bulk Import", href: "/dashboard/carbon-credits/create/import", isCurrent: true }
              ]}
            />
          </div>

          <div className="mb-6">
            <Alert variant="default" className="bg-blue-50 border-blue-200">
              <InfoIcon className="h-4 w-4 text-blue-500" />
              <AlertTitle>Bulk Import Instructions</AlertTitle>
              <AlertDescription>
                <p className="mb-2">
                  Upload a CSV file with carbon credit details. Download our template for the correct format.
                </p>
                <p>
                  All imported credits will be in a pending state until verified by our team.
                </p>
              </AlertDescription>
            </Alert>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="upload">Upload CSV</TabsTrigger>
              <TabsTrigger value="preview" disabled={!previewData}>Preview Data</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upload">
              <div className="grid gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Upload CSV File</CardTitle>
                    <CardDescription>
                      Upload a CSV file containing carbon credit details
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                        <FormField
                          control={form.control}
                          name="file"
                          render={({ field: { onChange, value, ...rest } }) => (
                            <FormItem>
                              <FormLabel>CSV File</FormLabel>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".csv"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                />
                              </FormControl>
                              <FormDescription>
                                Upload a CSV file with carbon credit details
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="flex justify-between">
                          <Button type="button" variant="outline" onClick={downloadTemplate}>
                            <Download className="mr-2 h-4 w-4" />
                            Download Template
                          </Button>
                          
                          <Button type="submit" disabled={isUploading}>
                            {isUploading ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Uploading...
                              </>
                            ) : (
                              <>
                                <Upload className="mr-2 h-4 w-4" />
                                Upload and Preview
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="preview">
              {previewData && (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Preview Data</CardTitle>
                      <CardDescription>
                        Review the data before importing
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {previewData.errors.length > 0 && (
                        <Alert variant="destructive" className="mb-6">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle>Validation Errors</AlertTitle>
                          <AlertDescription>
                            <ul className="list-disc list-inside">
                              {previewData.errors.map((error, index) => (
                                <li key={index}>
                                  Row {error.row}: {error.message}
                                </li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Row</TableHead>
                              {previewData.headers.map((header, index) => (
                                <TableHead key={index}>{header}</TableHead>
                              ))}
                              <TableHead>Status</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {previewData.rows.map((row, rowIndex) => {
                              const hasError = previewData.errors.some(
                                (error) => error.row === rowIndex + 2
                              );
                              
                              return (
                                <TableRow key={rowIndex}>
                                  <TableCell>{rowIndex + 1}</TableCell>
                                  {row.map((cell, cellIndex) => (
                                    <TableCell key={cellIndex}>{cell}</TableCell>
                                  ))}
                                  <TableCell>
                                    {hasError ? (
                                      <Badge variant="destructive">Error</Badge>
                                    ) : (
                                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                        Valid
                                      </Badge>
                                    )}
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" onClick={() => setActiveTab("upload")}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Upload
                      </Button>
                      
                      <Button 
                        onClick={handleImport} 
                        disabled={isImporting || previewData.errors.length > 0}
                      >
                        {isImporting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Check className="mr-2 h-4 w-4" />
                            Import Carbon Credits
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="mt-6">
            <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits/create")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Creation Options
            </Button>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
