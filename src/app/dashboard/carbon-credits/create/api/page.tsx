"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Database, Search, Check, AlertCircle, Loader2, RefreshCw } from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, AlertDes<PERSON>, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

// Schema for API import
const apiImportSchema = z.object({
  registry: z.enum(["VERRA", "GOLD_STANDARD", "AMERICAN_CARBON_REGISTRY", "CLIMATE_ACTION_RESERVE"]),
  projectId: z.string().min(1, "Project ID is required"),
});

type ApiImportFormValues = z.infer<typeof apiImportSchema>;

interface CarbonCreditPreview {
  id: string;
  name: string;
  description: string;
  quantity: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string;
  serialNumber: string;
  selected: boolean;
}

export default function ApiImportCarbonCreditPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isSearching, setIsSearching] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [activeTab, setActiveTab] = useState("search");
  const [previewData, setPreviewData] = useState<CarbonCreditPreview[]>([]);

  const form = useForm<ApiImportFormValues>({
    resolver: zodResolver(apiImportSchema),
    defaultValues: {
      registry: "VERRA",
      projectId: "",
    },
  });

  const onSubmit = async (data: ApiImportFormValues) => {
    setIsSearching(true);
    
    try {
      // In a real implementation, this would call an API to search for carbon credits
      // For now, we'll simulate a response with mock data
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock data based on the registry and project ID
      const mockCredits: CarbonCreditPreview[] = [
        {
          id: "1",
          name: `${data.registry} Renewable Energy Project`,
          description: "Wind farm generating clean energy",
          quantity: 1000,
          vintage: 2023,
          standard: data.registry === "VERRA" ? "VCS" : "GS",
          methodology: data.registry === "VERRA" ? "VM0025" : "GS-RE",
          location: "United States",
          serialNumber: `${data.registry}-${data.projectId}-001`,
          selected: true,
        },
        {
          id: "2",
          name: `${data.registry} Forestry Conservation`,
          description: "Forest conservation project preventing deforestation",
          quantity: 2500,
          vintage: 2022,
          standard: data.registry === "VERRA" ? "VCS" : "GS",
          methodology: data.registry === "VERRA" ? "VM0015" : "GS-FOR",
          location: "Brazil",
          serialNumber: `${data.registry}-${data.projectId}-002`,
          selected: true,
        },
        {
          id: "3",
          name: `${data.registry} Methane Capture`,
          description: "Landfill methane capture and utilization",
          quantity: 1500,
          vintage: 2023,
          standard: data.registry === "VERRA" ? "VCS" : "GS",
          methodology: data.registry === "VERRA" ? "VM0033" : "GS-WM",
          location: "Canada",
          serialNumber: `${data.registry}-${data.projectId}-003`,
          selected: true,
        },
      ];
      
      setPreviewData(mockCredits);
      setActiveTab("preview");
    } catch (error) {
      console.error("Error searching for carbon credits:", error);
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search for carbon credits",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const toggleSelection = (id: string) => {
    setPreviewData(prev => 
      prev.map(credit => 
        credit.id === id ? { ...credit, selected: !credit.selected } : credit
      )
    );
  };

  const selectAll = (selected: boolean) => {
    setPreviewData(prev => 
      prev.map(credit => ({ ...credit, selected }))
    );
  };

  const handleImport = async () => {
    const selectedCredits = previewData.filter(credit => credit.selected);
    
    if (selectedCredits.length === 0) {
      toast({
        title: "No credits selected",
        description: "Please select at least one carbon credit to import",
        variant: "destructive",
      });
      return;
    }
    
    setIsImporting(true);
    
    try {
      // In a real implementation, this would call an API to import the selected credits
      // For now, we'll simulate a successful import
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Import successful",
        description: `Successfully imported ${selectedCredits.length} carbon credits`,
      });
      
      router.push("/dashboard/carbon-credits");
      router.refresh();
    } catch (error) {
      console.error("Error importing carbon credits:", error);
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "Failed to import carbon credits",
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="API Import Carbon Credits"
              description="Import carbon credits from external registries"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Carbon Credits", href: "/dashboard/carbon-credits" },
                { label: "Create", href: "/dashboard/carbon-credits/create" },
                { label: "API Import", href: "/dashboard/carbon-credits/create/api", isCurrent: true }
              ]}
            />
          </div>

          <div className="mb-6">
            <Alert variant="default" className="bg-blue-50 border-blue-200">
              <InfoIcon className="h-4 w-4 text-blue-500" />
              <AlertTitle>API Import Instructions</AlertTitle>
              <AlertDescription>
                <p className="mb-2">
                  Import carbon credits directly from verified external registries like Verra and Gold Standard.
                </p>
                <p>
                  Enter the project ID from the registry to search for available credits.
                </p>
              </AlertDescription>
            </Alert>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="search">Search Registry</TabsTrigger>
              <TabsTrigger value="preview" disabled={previewData.length === 0}>Preview Credits</TabsTrigger>
            </TabsList>
            
            <TabsContent value="search">
              <div className="grid gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Search External Registry</CardTitle>
                    <CardDescription>
                      Search for carbon credits in external registries
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                        <FormField
                          control={form.control}
                          name="registry"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Registry</FormLabel>
                              <Select 
                                onValueChange={field.onChange} 
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a registry" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="VERRA">Verra Registry</SelectItem>
                                  <SelectItem value="GOLD_STANDARD">Gold Standard</SelectItem>
                                  <SelectItem value="AMERICAN_CARBON_REGISTRY">American Carbon Registry</SelectItem>
                                  <SelectItem value="CLIMATE_ACTION_RESERVE">Climate Action Reserve</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Select the registry where your carbon credits are registered
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="projectId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Project ID</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter project ID" {...field} />
                              </FormControl>
                              <FormDescription>
                                Enter the project ID from the selected registry
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <Button type="submit" disabled={isSearching}>
                          {isSearching ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Searching...
                            </>
                          ) : (
                            <>
                              <Search className="mr-2 h-4 w-4" />
                              Search Registry
                            </>
                          )}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="preview">
              {previewData.length > 0 && (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Available Carbon Credits</CardTitle>
                      <CardDescription>
                        Select the carbon credits you want to import
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox 
                            id="select-all" 
                            checked={previewData.every(credit => credit.selected)}
                            onCheckedChange={(checked) => selectAll(!!checked)}
                          />
                          <label
                            htmlFor="select-all"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Select All
                          </label>
                        </div>
                        
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => form.handleSubmit(onSubmit)()}
                        >
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Refresh
                        </Button>
                      </div>
                      
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-[50px]">Select</TableHead>
                              <TableHead>Name</TableHead>
                              <TableHead>Vintage</TableHead>
                              <TableHead>Quantity</TableHead>
                              <TableHead>Standard</TableHead>
                              <TableHead>Methodology</TableHead>
                              <TableHead>Serial Number</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {previewData.map((credit) => (
                              <TableRow key={credit.id}>
                                <TableCell>
                                  <Checkbox 
                                    checked={credit.selected}
                                    onCheckedChange={() => toggleSelection(credit.id)}
                                  />
                                </TableCell>
                                <TableCell className="font-medium">{credit.name}</TableCell>
                                <TableCell>{credit.vintage}</TableCell>
                                <TableCell>{credit.quantity.toLocaleString()}</TableCell>
                                <TableCell>{credit.standard}</TableCell>
                                <TableCell>{credit.methodology}</TableCell>
                                <TableCell>{credit.serialNumber}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" onClick={() => setActiveTab("search")}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Search
                      </Button>
                      
                      <Button 
                        onClick={handleImport} 
                        disabled={isImporting || !previewData.some(credit => credit.selected)}
                      >
                        {isImporting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Check className="mr-2 h-4 w-4" />
                            Import Selected Credits
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="mt-6">
            <Button variant="outline" onClick={() => router.push("/dashboard/carbon-credits/create")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Creation Options
            </Button>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
