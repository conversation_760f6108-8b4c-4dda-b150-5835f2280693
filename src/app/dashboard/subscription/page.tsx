"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Check, X, CreditCard, AlertCircle, RefreshCw } from "lucide-react";
import { SubscriptionUpgradeNudges } from "@/components/subscription/upgrade-nudges";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

type SubscriptionPlan = {
  name: string;
  price: number;
  billingCycle: string;
  features: {
    maxUsers: number;
    maxCarbonCredits: number;
    maxWallets: number;
    supportedNetworks: number;
    customFees: boolean;
    analytics: boolean;
    apiAccess: boolean;
    support: string;
  };
};

type Subscription = {
  id: string;
  plan: string;
  status: string;
  endDate: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  planDetails: SubscriptionPlan;
};

export default function SubscriptionPage() {
  const { data: session } = useSession();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plans, setPlans] = useState<Record<string, SubscriptionPlan>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const { toast } = useToast();

  const fetchSubscription = async () => {
    if (!session?.user?.organizationId) return;

    try {
      setIsLoading(true);

      // Fetch all plans
      const plansResponse = await fetch("/api/subscriptions");
      const plansData = await plansResponse.json();
      setPlans(plansData);

      // Fetch current subscription
      const response = await fetch(`/api/subscriptions/organization/${session.user.organizationId}`);

      if (response.ok) {
        const data = await response.json();
        setSubscription(data);
        setSelectedPlan(data.plan);
      } else if (response.status === 404) {
        // No subscription yet
        setSubscription(null);
        setSelectedPlan("FREE");
      } else {
        throw new Error("Failed to fetch subscription");
      }
    } catch (error) {
      console.error("Error fetching subscription:", error);
      toast({
        title: "Error",
        description: "Failed to load subscription details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createSubscription = async (plan: string) => {
    if (!session?.user?.organizationId) return;

    try {
      setIsUpdating(true);
      const response = await fetch("/api/subscriptions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: session.user.organizationId,
          plan,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create subscription");
      }

      const data = await response.json();
      setSubscription(data);
      setSelectedPlan(data.plan);

      toast({
        title: "Success",
        description: `Subscription created successfully: ${plans[plan].name}`,
      });
    } catch (error) {
      console.error("Error creating subscription:", error);
      toast({
        title: "Error",
        description: "Failed to create subscription",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const updateSubscription = async (plan: string) => {
    if (!subscription?.id) return;

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/subscriptions/${subscription.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          plan,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update subscription");
      }

      const data = await response.json();
      setSubscription(data);
      setSelectedPlan(data.plan);

      toast({
        title: "Success",
        description: `Subscription updated to: ${plans[plan].name}`,
      });
    } catch (error) {
      console.error("Error updating subscription:", error);
      toast({
        title: "Error",
        description: "Failed to update subscription",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const cancelSubscription = async () => {
    if (!subscription?.id) return;

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/subscriptions/${subscription.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to cancel subscription");
      }

      const data = await response.json();
      setSubscription(data);
      setShowCancelDialog(false);

      toast({
        title: "Success",
        description: "Subscription cancelled successfully",
      });
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      toast({
        title: "Error",
        description: "Failed to cancel subscription",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const renewSubscription = async () => {
    if (!subscription?.id) return;

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/subscriptions/${subscription.id}/renew`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to renew subscription");
      }

      const data = await response.json();
      setSubscription(data);

      toast({
        title: "Success",
        description: "Subscription renewed successfully",
      });
    } catch (error) {
      console.error("Error renewing subscription:", error);
      toast({
        title: "Error",
        description: "Failed to renew subscription",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchSubscription();
    }
  }, [session]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isExpired = subscription?.endDate
    ? new Date(subscription.endDate) < new Date()
    : false;

  const daysUntilExpiration = subscription?.endDate
    ? Math.max(
        0,
        Math.ceil(
          (new Date(subscription.endDate).getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        )
      )
    : 0;

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
          <p className="text-muted-foreground">
            Manage your organization's subscription plan
          </p>
        </div>
        <Button variant="outline" onClick={fetchSubscription}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Context-specific nudges */}
      {session?.user?.organizationId && (
        <SubscriptionUpgradeNudges organizationId={session.user.organizationId} />
      )}

      {isLoading ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-8 w-16" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Array.from({ length: 5 }).map((_, j) => (
                      <Skeleton key={j} className="h-4 w-full" />
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {subscription && (
            <Card>
              <CardHeader>
                <CardTitle>Current Subscription</CardTitle>
                <CardDescription>
                  Your organization's current subscription details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Plan</span>
                    <div className="flex items-center">
                      <span className="font-bold mr-2">
                        {subscription.planDetails.name}
                      </span>
                      <Badge variant={subscription.status === "ACTIVE" ? "default" : "destructive"}>
                        {subscription.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Price</span>
                    <span className="font-bold">
                      {formatCurrency(subscription.planDetails.price)}/{subscription.planDetails.billingCycle}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Renewal Date</span>
                    <span className="font-bold">
                      {formatDate(subscription.endDate)}
                      {isExpired ? (
                        <Badge variant="destructive" className="ml-2">
                          Expired
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="ml-2">
                          {daysUntilExpiration} days left
                        </Badge>
                      )}
                    </span>
                  </div>
                </div>

                {isExpired && (
                  <Alert variant="destructive" className="mt-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Subscription Expired</AlertTitle>
                    <AlertDescription>
                      Your subscription has expired. Please renew to continue using all features.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-end space-x-2 mt-6">
                  {subscription.status === "ACTIVE" ? (
                    <Button
                      variant="destructive"
                      onClick={() => setShowCancelDialog(true)}
                      disabled={isUpdating}
                    >
                      Cancel Subscription
                    </Button>
                  ) : (
                    <Button
                      onClick={renewSubscription}
                      disabled={isUpdating}
                    >
                      Renew Subscription
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          <h2 className="text-xl font-bold mt-8 mb-4">Available Plans</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(plans).map(([planKey, plan]) => (
              <Card
                key={planKey}
                className={
                  selectedPlan === planKey
                    ? "border-primary shadow-md"
                    : ""
                }
              >
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <div className="text-3xl font-bold">
                    {formatCurrency(plan.price)}
                    <span className="text-sm font-normal text-muted-foreground">
                      /{plan.billingCycle}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>
                        {plan.features.maxUsers === -1
                          ? "Unlimited users"
                          : `Up to ${plan.features.maxUsers} users`}
                      </span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>
                        {plan.features.maxCarbonCredits === -1
                          ? "Unlimited carbon credits"
                          : `Up to ${plan.features.maxCarbonCredits} carbon credits`}
                      </span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>
                        {plan.features.maxWallets === -1
                          ? "Unlimited wallets"
                          : `Up to ${plan.features.maxWallets} wallets`}
                      </span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>
                        {plan.features.supportedNetworks === -1
                          ? "All supported networks"
                          : `${plan.features.supportedNetworks} blockchain networks`}
                      </span>
                    </li>
                    <li className="flex items-center">
                      {plan.features.customFees ? (
                        <>
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                          <span>Custom fee settings</span>
                        </>
                      ) : (
                        <>
                          <X className="h-4 w-4 mr-2 text-red-500" />
                          <span className="text-muted-foreground">Custom fee settings</span>
                        </>
                      )}
                    </li>
                    <li className="flex items-center">
                      {plan.features.analytics ? (
                        <>
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                          <span>Advanced analytics</span>
                        </>
                      ) : (
                        <>
                          <X className="h-4 w-4 mr-2 text-red-500" />
                          <span className="text-muted-foreground">Advanced analytics</span>
                        </>
                      )}
                    </li>
                    <li className="flex items-center">
                      {plan.features.apiAccess ? (
                        <>
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                          <span>API access</span>
                        </>
                      ) : (
                        <>
                          <X className="h-4 w-4 mr-2 text-red-500" />
                          <span className="text-muted-foreground">API access</span>
                        </>
                      )}
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>
                        {plan.features.support === "dedicated"
                          ? "Dedicated support"
                          : plan.features.support === "priority"
                          ? "Priority support"
                          : "Email support"}
                      </span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  {subscription ? (
                    <Button
                      className="w-full"
                      variant={selectedPlan === planKey ? "outline" : "default"}
                      disabled={isUpdating || selectedPlan === planKey}
                      onClick={() => updateSubscription(planKey)}
                    >
                      {selectedPlan === planKey
                        ? "Current Plan"
                        : `Upgrade to ${plan.name}`}
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      disabled={isUpdating}
                      onClick={() => createSubscription(planKey)}
                    >
                      Select {plan.name}
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>

          <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Cancel Subscription</DialogTitle>
                <DialogDescription>
                  Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.
                </DialogDescription>
              </DialogHeader>
              <div className="flex items-center justify-between p-4 bg-muted rounded-md">
                <div className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  <div>
                    <p className="font-medium">{subscription?.planDetails.name} Plan</p>
                    <p className="text-sm text-muted-foreground">
                      Active until {formatDate(subscription?.endDate || "")}
                    </p>
                  </div>
                </div>
                <Badge>{subscription?.status}</Badge>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
                  Keep Subscription
                </Button>
                <Button variant="destructive" onClick={cancelSubscription} disabled={isUpdating}>
                  {isUpdating ? "Cancelling..." : "Cancel Subscription"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
}
