"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import {
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedInput,
  PageTransition,
  StaggeredList
} from "@/components/ui/animated";
import { AnimationSettings } from "@/components/settings/animation-settings";
import { ProtectedPage } from "@/components/auth/protected-page";

export default function SettingsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("profile");

  // Mock organization data
  const mockOrganization = {
    name: "Green Energy Co.",
    description: "Renewable energy solutions provider",
    website: "https://greenenergy.example.com",
    logo: "",
    transactionFeeRate: 1.0,
    listingFeeRate: 0.5,
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Settings"
              description="Manage your account and organization settings"
              breadcrumbItems={[
                { label: "Settings", href: "/dashboard/settings", isCurrent: true }
              ]}
            />
          </div>
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <AnimatedButton
                variant={activeTab === "profile" ? "default" : "outline"}
                onClick={() => setActiveTab("profile")}
                animationVariant="buttonTap"
              >
                Profile
              </AnimatedButton>
            {session?.user?.role === "ORGANIZATION_ADMIN" && (
              <AnimatedButton
                variant={activeTab === "organization" ? "default" : "outline"}
                onClick={() => setActiveTab("organization")}
                animationVariant="buttonTap"
              >
                Organization
              </AnimatedButton>
            )}
            <AnimatedButton
              variant={activeTab === "security" ? "default" : "outline"}
              onClick={() => setActiveTab("security")}
              animationVariant="buttonTap"
            >
              Security
            </AnimatedButton>
            <AnimatedButton
              variant={activeTab === "notifications" ? "default" : "outline"}
              onClick={() => setActiveTab("notifications")}
              animationVariant="buttonTap"
            >
              Notifications
            </AnimatedButton>
            <AnimatedButton
              variant={activeTab === "animations" ? "default" : "outline"}
              onClick={() => setActiveTab("animations")}
              animationVariant="buttonTap"
            >
              Animations
            </AnimatedButton>
          </div>

          {activeTab === "profile" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Profile Information</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <form className="space-y-4">
                  <div className="grid gap-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Name
                    </label>
                    <AnimatedInput
                      id="name"
                      defaultValue={session?.user?.name || ""}
                      placeholder="Your name"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      Email
                    </label>
                    <AnimatedInput
                      id="email"
                      type="email"
                      defaultValue={session?.user?.email || ""}
                      placeholder="Your email"
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">
                      Your email cannot be changed
                    </p>
                  </div>
                  <AnimatedButton animationVariant="buttonTap">Save Changes</AnimatedButton>
                </form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "organization" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Organization Settings</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <form className="space-y-4">
                  <div className="grid gap-2">
                    <label htmlFor="org-name" className="text-sm font-medium">
                      Organization Name
                    </label>
                    <AnimatedInput
                      id="org-name"
                      defaultValue={mockOrganization.name}
                      placeholder="Organization name"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="org-description" className="text-sm font-medium">
                      Description
                    </label>
                    <textarea
                      id="org-description"
                      className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      defaultValue={mockOrganization.description}
                      placeholder="Organization description"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="org-website" className="text-sm font-medium">
                      Website
                    </label>
                    <AnimatedInput
                      id="org-website"
                      type="url"
                      defaultValue={mockOrganization.website}
                      placeholder="https://example.com"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="org-logo" className="text-sm font-medium">
                      Logo
                    </label>
                    <AnimatedInput id="org-logo" type="file" accept="image/*" />
                  </div>
                  <AnimatedButton animationVariant="buttonTap">Save Organization Settings</AnimatedButton>
                </form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "security" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Security Settings</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <form className="space-y-4">
                  <div className="grid gap-2">
                    <label htmlFor="current-password" className="text-sm font-medium">
                      Current Password
                    </label>
                    <AnimatedInput
                      id="current-password"
                      type="password"
                      placeholder="Enter current password"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="new-password" className="text-sm font-medium">
                      New Password
                    </label>
                    <AnimatedInput
                      id="new-password"
                      type="password"
                      placeholder="Enter new password"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="confirm-password" className="text-sm font-medium">
                      Confirm New Password
                    </label>
                    <AnimatedInput
                      id="confirm-password"
                      type="password"
                      placeholder="Confirm new password"
                    />
                  </div>
                  <AnimatedButton animationVariant="buttonTap">Change Password</AnimatedButton>
                </form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "notifications" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Notification Preferences</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <form className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Email Notifications</h3>
                      <p className="text-xs text-muted-foreground">
                        Receive email notifications for important updates
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Transaction Alerts</h3>
                      <p className="text-xs text-muted-foreground">
                        Get notified when transactions occur
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Market Updates</h3>
                      <p className="text-xs text-muted-foreground">
                        Receive updates about new carbon credits and market changes
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Platform Announcements</h3>
                      <p className="text-xs text-muted-foreground">
                        Get notified about platform updates and announcements
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <AnimatedButton animationVariant="buttonTap">Save Preferences</AnimatedButton>
                </form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "animations" && (
            <AnimationSettings />
          )}
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
