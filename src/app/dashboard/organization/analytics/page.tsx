"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart 
} from "@/components/ui/charts";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON> as LineChartIcon, 
  <PERSON><PERSON>hart as PieChartIcon, 
  Download, 
  Calendar, 
  RefreshCw, 
  Loader2 
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { format, subDays, subMonths } from "date-fns";

interface AnalyticsData {
  carbonCredits: {
    total: number;
    verified: number;
    listed: number;
    sold: number;
    retired: number;
    totalVolume: number;
    byVintage: { vintage: number; count: number }[];
    byStandard: { standard: string; count: number }[];
    byStatus: { status: string; count: number }[];
  };
  transactions: {
    total: number;
    volume: number;
    fees: number;
    byDate: { date: string; count: number; volume: number }[];
    byType: { type: string; count: number; volume: number }[];
  };
  revenue: {
    total: number;
    byMonth: { month: string; amount: number }[];
    bySource: { source: string; amount: number }[];
  };
}

export default function OrganizationAnalyticsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("30d");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  // Fetch analytics data
  const fetchAnalyticsData = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const response = await fetch(`/api/organization/analytics?timeRange=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch analytics data");
      }

      const data = await response.json();
      setAnalyticsData(data);
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchAnalyticsData(true);
  };

  // Export data as CSV
  const exportData = () => {
    if (!analyticsData) return;

    // Create CSV content based on active tab
    let csvContent = "";
    let fileName = "";

    if (activeTab === "overview" || activeTab === "carbon-credits") {
      csvContent = "Vintage,Count\n";
      analyticsData.carbonCredits.byVintage.forEach((item) => {
        csvContent += `${item.vintage},${item.count}\n`;
      });
      csvContent += "\nStandard,Count\n";
      analyticsData.carbonCredits.byStandard.forEach((item) => {
        csvContent += `${item.standard},${item.count}\n`;
      });
      csvContent += "\nStatus,Count\n";
      analyticsData.carbonCredits.byStatus.forEach((item) => {
        csvContent += `${item.status},${item.count}\n`;
      });
      fileName = "carbon-credits-analytics.csv";
    } else if (activeTab === "transactions") {
      csvContent = "Date,Count,Volume\n";
      analyticsData.transactions.byDate.forEach((item) => {
        csvContent += `${item.date},${item.count},${item.volume}\n`;
      });
      csvContent += "\nType,Count,Volume\n";
      analyticsData.transactions.byType.forEach((item) => {
        csvContent += `${item.type},${item.count},${item.volume}\n`;
      });
      fileName = "transactions-analytics.csv";
    } else if (activeTab === "revenue") {
      csvContent = "Month,Amount\n";
      analyticsData.revenue.byMonth.forEach((item) => {
        csvContent += `${item.month},${item.amount}\n`;
      });
      csvContent += "\nSource,Amount\n";
      analyticsData.revenue.bySource.forEach((item) => {
        csvContent += `${item.source},${item.amount}\n`;
      });
      fileName = "revenue-analytics.csv";
    }

    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Load analytics data when time range changes
  useEffect(() => {
    if (session?.user) {
      fetchAnalyticsData();
    }
  }, [session, timeRange]);

  // Redirect if not authenticated or not part of an organization
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else if (status === "authenticated" && !session?.user?.organizationId) {
      router.push("/dashboard");
      toast({
        title: "No Organization",
        description: "You must be part of an organization to access analytics",
        variant: "destructive",
      });
    }
  }, [status, session, router]);

  if (status === "loading" || (status === "authenticated" && !session?.user?.organizationId)) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Organization Analytics</h1>
          <p className="text-muted-foreground">
            Insights and metrics for your organization
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportData} disabled={isLoading || !analyticsData}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="carbon-credits">Carbon Credits</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="w-full h-[200px] animate-pulse bg-muted" />
              ))}
            </div>
          ) : analyticsData ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Carbon Credits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.carbonCredits.total}</div>
                    <p className="text-xs text-muted-foreground">
                      {analyticsData.carbonCredits.totalVolume.toLocaleString()} tons
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.transactions.total}</div>
                    <p className="text-xs text-muted-foreground">
                      ${analyticsData.transactions.volume.toLocaleString()}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">
                      ${analyticsData.revenue.total.toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Fees paid: ${analyticsData.transactions.fees.toLocaleString()}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Carbon Credits by Status</CardTitle>
                    <CardDescription>
                      Distribution of carbon credits by status
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <PieChart
                      data={analyticsData.carbonCredits.byStatus.map((item) => ({
                        name: item.status,
                        value: item.count,
                      }))}
                      colors={["#10b981", "#3b82f6", "#ef4444", "#f59e0b", "#8b5cf6"]}
                      height={300}
                    />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Transactions Over Time</CardTitle>
                    <CardDescription>
                      Number and volume of transactions over time
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <LineChart
                      data={analyticsData.transactions.byDate.map((item) => ({
                        name: item.date,
                        "Transaction Count": item.count,
                        "Volume ($)": item.volume,
                      }))}
                      categories={["Transaction Count", "Volume ($)"]}
                      colors={["#3b82f6", "#10b981"]}
                      height={300}
                    />
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Monthly Revenue</CardTitle>
                  <CardDescription>
                    Revenue by month for the selected time period
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={analyticsData.revenue.byMonth.map((item) => ({
                      name: item.month,
                      Revenue: item.amount,
                    }))}
                    categories={["Revenue"]}
                    colors={["#8b5cf6"]}
                    height={300}
                  />
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-10">
              <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-lg font-medium">No analytics data available</h3>
              <p className="text-sm text-muted-foreground">
                There is no analytics data available for the selected time period
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="carbon-credits" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="w-full h-[300px] animate-pulse bg-muted" />
              ))}
            </div>
          ) : analyticsData ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Credits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.carbonCredits.total}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Verified</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.carbonCredits.verified}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Listed</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.carbonCredits.listed}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Sold</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.carbonCredits.sold}</div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Carbon Credits by Vintage</CardTitle>
                    <CardDescription>
                      Distribution of carbon credits by vintage year
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <BarChart
                      data={analyticsData.carbonCredits.byVintage.map((item) => ({
                        name: item.vintage.toString(),
                        Credits: item.count,
                      }))}
                      categories={["Credits"]}
                      colors={["#3b82f6"]}
                      height={300}
                    />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Carbon Credits by Standard</CardTitle>
                    <CardDescription>
                      Distribution of carbon credits by standard
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <PieChart
                      data={analyticsData.carbonCredits.byStandard.map((item) => ({
                        name: item.standard,
                        value: item.count,
                      }))}
                      colors={["#10b981", "#3b82f6", "#ef4444", "#f59e0b", "#8b5cf6"]}
                      height={300}
                    />
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Carbon Credits by Status</CardTitle>
                  <CardDescription>
                    Distribution of carbon credits by status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={analyticsData.carbonCredits.byStatus.map((item) => ({
                      name: item.status,
                      Credits: item.count,
                    }))}
                    categories={["Credits"]}
                    colors={["#10b981"]}
                    height={300}
                  />
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-10">
              <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-lg font-medium">No carbon credit data available</h3>
              <p className="text-sm text-muted-foreground">
                There is no carbon credit data available for the selected time period
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="w-full h-[300px] animate-pulse bg-muted" />
              ))}
            </div>
          ) : analyticsData ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analyticsData.transactions.total}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">
                      ${analyticsData.transactions.volume.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Fees Paid</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">
                      ${analyticsData.transactions.fees.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Transactions Over Time</CardTitle>
                  <CardDescription>
                    Number and volume of transactions over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart
                    data={analyticsData.transactions.byDate.map((item) => ({
                      name: item.date,
                      "Transaction Count": item.count,
                      "Volume ($)": item.volume,
                    }))}
                    categories={["Transaction Count", "Volume ($)"]}
                    colors={["#3b82f6", "#10b981"]}
                    height={300}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Transactions by Type</CardTitle>
                  <CardDescription>
                    Distribution of transactions by type
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={analyticsData.transactions.byType.map((item) => ({
                      name: item.type,
                      "Transaction Count": item.count,
                      "Volume ($)": item.volume,
                    }))}
                    categories={["Transaction Count", "Volume ($)"]}
                    colors={["#8b5cf6", "#f59e0b"]}
                    height={300}
                  />
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-10">
              <LineChartIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-lg font-medium">No transaction data available</h3>
              <p className="text-sm text-muted-foreground">
                There is no transaction data available for the selected time period
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 2 }).map((_, i) => (
                <Card key={i} className="w-full h-[300px] animate-pulse bg-muted" />
              ))}
            </div>
          ) : analyticsData ? (
            <>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    ${analyticsData.revenue.total.toLocaleString()}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Monthly Revenue</CardTitle>
                  <CardDescription>
                    Revenue by month for the selected time period
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={analyticsData.revenue.byMonth.map((item) => ({
                      name: item.month,
                      Revenue: item.amount,
                    }))}
                    categories={["Revenue"]}
                    colors={["#8b5cf6"]}
                    height={300}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Revenue by Source</CardTitle>
                  <CardDescription>
                    Distribution of revenue by source
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={analyticsData.revenue.bySource.map((item) => ({
                      name: item.source,
                      value: item.amount,
                    }))}
                    colors={["#10b981", "#3b82f6", "#ef4444", "#f59e0b"]}
                    height={300}
                  />
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-10">
              <PieChartIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-lg font-medium">No revenue data available</h3>
              <p className="text-sm text-muted-foreground">
                There is no revenue data available for the selected time period
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
