"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { VerificationDocumentNudges } from "@/components/verification/document-nudges";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  AlertCircle,
  CheckCircle2,
  XCircle,
  FileText,
  Upload,
  Clock,
  AlertTriangle,
  FileCheck,
  Info,
  Trash2
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function OrganizationVerificationPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [organization, setOrganization] = useState<any>(null);
  const [documents, setDocuments] = useState<any[]>([]);
  const [verificationNotes, setVerificationNotes] = useState<any[]>([]);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [documentType, setDocumentType] = useState("");
  const [documentName, setDocumentName] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      setError("You must be logged in to access organization verification");
      setIsLoading(false);
      return;
    }

    if (!session.user.organizationId) {
      setError("You must be part of an organization to access verification");
      setIsLoading(false);
      return;
    }

    fetchOrganizationDetails();
  }, [session, status]);

  const fetchOrganizationDetails = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/organizations/verification");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch organization details");
      }

      setOrganization(data.organization);
      setDocuments(data.documents || []);
      setVerificationNotes(data.verificationNotes || []);
    } catch (error) {
      console.error("Error fetching organization details:", error);
      toast({
        title: "Error",
        description: "Failed to load organization verification details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);

      // Auto-fill document name from file name if not already set
      if (!documentName) {
        setDocumentName(file.name.split('.')[0]);
      }
    }
  };

  const handleUploadDocument = async () => {
    if (!selectedFile || !documentType || !documentName) {
      toast({
        title: "Missing Information",
        description: "Please provide document type, name, and select a file",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);

      // Create form data
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("type", documentType);
      formData.append("name", documentName);

      // Upload document
      const response = await fetch("/api/organizations/verification/documents", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to upload document");
      }

      toast({
        title: "Document Uploaded",
        description: "Your document has been uploaded successfully",
      });

      // Reset form
      setDocumentType("");
      setDocumentName("");
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      setUploadDialogOpen(false);

      // Refresh documents
      fetchOrganizationDetails();
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const response = await fetch(`/api/organizations/verification/documents/${documentId}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete document");
      }

      toast({
        title: "Document Deleted",
        description: "Your document has been deleted successfully",
      });

      // Refresh documents
      fetchOrganizationDetails();
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Error",
        description: "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  const handleRequestVerification = async () => {
    try {
      const response = await fetch("/api/organizations/verification/request", {
        method: "POST",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to request verification");
      }

      toast({
        title: "Verification Requested",
        description: "Your verification request has been submitted",
      });

      // Refresh organization details
      fetchOrganizationDetails();
    } catch (error) {
      console.error("Error requesting verification:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to request verification",
        variant: "destructive",
      });
    }
  };

  const getVerificationStatusBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            Verified
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case "IN_REVIEW":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            In Review
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            Approved
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "BUSINESS_REGISTRATION":
        return <FileCheck className="h-4 w-4" />;
      case "TAX_CERTIFICATE":
        return <FileText className="h-4 w-4" />;
      case "IDENTITY_PROOF":
        return <FileText className="h-4 w-4" />;
      case "ADDRESS_PROOF":
        return <FileText className="h-4 w-4" />;
      case "BANK_STATEMENT":
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case "BUSINESS_REGISTRATION":
        return "Business Registration";
      case "TAX_CERTIFICATE":
        return "Tax Certificate";
      case "IDENTITY_PROOF":
        return "Identity Proof";
      case "ADDRESS_PROOF":
        return "Address Proof";
      case "BANK_STATEMENT":
        return "Bank Statement";
      default:
        return type.replace(/_/g, " ");
    }
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="mb-8">
          <Skeleton className="h-10 w-[250px] mb-2" />
          <Skeleton className="h-4 w-[350px]" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-[300px] rounded-md" />
          <Skeleton className="h-[300px] rounded-md" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
          <div className="mb-4 rounded-full bg-destructive/10 p-3">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="mb-1 text-lg font-medium">Access Denied</h3>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            {error}
          </p>
          <Button onClick={() => router.push("/dashboard")}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
          <div className="mb-4 rounded-full bg-destructive/10 p-3">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <h3 className="mb-1 text-lg font-medium">Organization Not Found</h3>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            The organization you are looking for does not exist or has been deleted.
          </p>
          <Button onClick={() => router.push("/dashboard")}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Organization Verification</h1>
        <p className="text-muted-foreground">
          Verify your organization to access all platform features
        </p>
      </div>

      {/* Context-specific nudges */}
      {session?.user?.organizationId && (
        <VerificationDocumentNudges organizationId={session.user.organizationId} />
      )}

      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Verification Status</CardTitle>
                {getVerificationStatusBadge(organization.verificationStatus)}
              </div>
              <CardDescription>
                Current status of your organization verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {organization.verificationStatus === "VERIFIED" && (
                  <div className="rounded-md bg-green-50 dark:bg-green-950 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-green-800 dark:text-green-300">
                          Verification Approved
                        </h3>
                        <div className="mt-2 text-sm text-green-700 dark:text-green-400">
                          <p>
                            Your organization has been verified. You now have full access to all platform features.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {organization.verificationStatus === "PENDING" && (
                  <div className="rounded-md bg-blue-50 dark:bg-blue-950 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                          Verification Pending
                        </h3>
                        <div className="mt-2 text-sm text-blue-700 dark:text-blue-400">
                          <p>
                            Your verification request is pending. Please upload all required documents to proceed with verification.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {organization.verificationStatus === "IN_REVIEW" && (
                  <div className="rounded-md bg-yellow-50 dark:bg-yellow-950 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                          Verification In Review
                        </h3>
                        <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-400">
                          <p>
                            Your verification request is being reviewed by our team. This process typically takes 1-3 business days.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {organization.verificationStatus === "REJECTED" && (
                  <div className="rounded-md bg-red-50 dark:bg-red-950 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
                          Verification Rejected
                        </h3>
                        <div className="mt-2 text-sm text-red-700 dark:text-red-400">
                          <p>
                            Your verification request has been rejected. Please review the verification notes below and update your documents accordingly.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Required Documents</h3>
                  <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                    <li>Business Registration Certificate</li>
                    <li>Tax Certificate or Tax ID</li>
                    <li>Proof of Identity (for organization admin)</li>
                    <li>Proof of Address (utility bill, etc.)</li>
                    <li>Bank Statement (optional)</li>
                  </ul>
                </div>

                {verificationNotes.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Verification Notes</h3>
                    <div className="space-y-2">
                      {verificationNotes.map((note) => (
                        <div key={note.id} className="rounded-md border p-3 text-sm">
                          <div className="flex justify-between items-start">
                            <div className="font-medium">{note.status}</div>
                            <div className="text-xs text-muted-foreground">
                              {new Date(note.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                          <p className="mt-1">{note.content}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              {organization.verificationStatus === "PENDING" && documents.length > 0 && (
                <Button onClick={handleRequestVerification}>
                  Submit for Verification
                </Button>
              )}
              {organization.verificationStatus === "REJECTED" && (
                <Button onClick={handleRequestVerification}>
                  Resubmit for Verification
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Verification Documents</CardTitle>
                <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Document
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Upload Verification Document</DialogTitle>
                      <DialogDescription>
                        Upload a document to verify your organization
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="documentType">Document Type</Label>
                        <Select value={documentType} onValueChange={setDocumentType}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select document type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="BUSINESS_REGISTRATION">Business Registration</SelectItem>
                            <SelectItem value="TAX_CERTIFICATE">Tax Certificate</SelectItem>
                            <SelectItem value="IDENTITY_PROOF">Identity Proof</SelectItem>
                            <SelectItem value="ADDRESS_PROOF">Address Proof</SelectItem>
                            <SelectItem value="BANK_STATEMENT">Bank Statement</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="documentName">Document Name</Label>
                        <Input
                          id="documentName"
                          placeholder="Enter document name"
                          value={documentName}
                          onChange={(e) => setDocumentName(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="file">Document File</Label>
                        <Input
                          id="file"
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileChange}
                          accept=".pdf,.jpg,.jpeg,.png"
                        />
                        <p className="text-xs text-muted-foreground">
                          Accepted formats: PDF, JPG, JPEG, PNG. Maximum size: 5MB.
                        </p>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleUploadDocument} disabled={isUploading}>
                        {isUploading ? "Uploading..." : "Upload Document"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>
                Upload and manage your verification documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              {documents.length > 0 ? (
                <div className="space-y-4">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 rounded-md border hover:bg-muted cursor-pointer"
                      onClick={() => setSelectedDocument(doc)}
                    >
                      <div className="flex items-center space-x-3">
                        {getDocumentTypeIcon(doc.type)}
                        <div>
                          <p className="font-medium">{doc.name}</p>
                          <p className="text-xs text-muted-foreground">{getDocumentTypeName(doc.type)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getDocumentStatusBadge(doc.status)}
                        {(doc.status === "PENDING" || organization.verificationStatus !== "IN_REVIEW") && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteDocument(doc.id);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete document</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
                  <div className="mb-4 rounded-full bg-primary/10 p-3">
                    <FileText className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="mb-1 text-lg font-medium">No Documents</h3>
                  <p className="mb-4 text-center text-sm text-muted-foreground">
                    You haven't uploaded any verification documents yet.
                  </p>
                  <Button onClick={() => setUploadDialogOpen(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Document
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Verification Requirements</CardTitle>
                <CardDescription>
                  Information about the verification process
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Why Verification?</h3>
                      <p className="text-sm text-muted-foreground">
                        Verification helps ensure the legitimacy of organizations on our platform and is required for trading carbon credits.
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start space-x-2">
                    <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Verification Process</h3>
                      <p className="text-sm text-muted-foreground">
                        1. Upload all required documents<br />
                        2. Submit for verification<br />
                        3. Our team reviews your documents (1-3 business days)<br />
                        4. Receive verification status update
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start space-x-2">
                    <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Document Requirements</h3>
                      <p className="text-sm text-muted-foreground">
                        All documents must be clear, legible, and valid. Documents in languages other than English should be accompanied by certified translations.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Document Viewer Dialog */}
      <Dialog open={!!selectedDocument} onOpenChange={(open) => !open && setSelectedDocument(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{selectedDocument?.name}</DialogTitle>
            <DialogDescription>
              {selectedDocument && getDocumentTypeName(selectedDocument.type)} • {selectedDocument && getDocumentStatusBadge(selectedDocument.status)}
            </DialogDescription>
          </DialogHeader>

          <div className="mt-4 space-y-4">
            <div className="rounded-md border overflow-hidden">
              {selectedDocument?.url.endsWith('.pdf') ? (
                <div className="h-[400px] flex items-center justify-center bg-muted">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">PDF document preview not available</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => window.open(selectedDocument?.url, '_blank')}
                    >
                      View PDF
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="relative h-[400px] w-full">
                  <Image
                    src={selectedDocument?.url}
                    alt={selectedDocument?.name}
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                </div>
              )}
            </div>

            {selectedDocument?.notes && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Document Notes</h3>
                <div className="rounded-md border p-3 text-sm">
                  {selectedDocument.notes}
                </div>
              </div>
            )}

            <div className="flex items-start space-x-2">
              <Info className="h-5 w-5 text-blue-500 mt-0.5" />
              <div className="text-sm text-muted-foreground">
                <p>
                  Uploaded on {selectedDocument && new Date(selectedDocument.createdAt).toLocaleDateString()} •
                  Last updated on {selectedDocument && new Date(selectedDocument.updatedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => window.open(selectedDocument?.url, '_blank')}
            >
              View Original
            </Button>
            {(selectedDocument?.status === "PENDING" || organization.verificationStatus !== "IN_REVIEW") && (
              <Button
                variant="destructive"
                onClick={() => {
                  handleDeleteDocument(selectedDocument?.id);
                  setSelectedDocument(null);
                }}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Document
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
