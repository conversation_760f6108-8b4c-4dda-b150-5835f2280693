"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  PageTran<PERSON>tion,
  Animated<PERSON><PERSON>,
  Animated<PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList,
} from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Activity, Leaf, ShoppingCart, Wallet, Settings, Plus, ArrowRight, RefreshCw } from "lucide-react";
import { CarbonCreditsWidget } from "@/components/dashboard/carbon-credits-widget";
import { ProfileCompletionBanner } from "@/components/dashboard/profile-completion-banner";
import { ProfileCompletionIndicator } from "@/components/profile/profile-completion-indicator";
import { TransactionHistory } from "@/components/dashboard/transaction-history";
import { But<PERSON> } from "@/components/ui/button";
import { truncateAddress, formatCurrency } from "@/lib/utils";
import { AnimatedSpinner } from "@/components/ui/animated-icon";

interface DashboardClientProps {
  userName: string;
}

interface WalletData {
  id: string;
  address: string;
  balance: number;
  network: string;
  isTestnet: boolean;
}

export default function DashboardClient({ userName }: Readonly<DashboardClientProps>) {
  const { data: session } = useSession();
  const router = useRouter();
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [profileCompletion] = useState(0);
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [walletLoading, setWalletLoading] = useState(true);
  const [walletError, setWalletError] = useState<string | null>(null);

  const fetchWallet = async () => {
    setWalletLoading(true);
    setWalletError(null);

    try {
      const response = await fetch("/api/wallet");

      if (!response.ok) {
        throw new Error("Failed to fetch wallet data");
      }

      const data = await response.json();
      setWallet(data.wallet);
    } catch (err) {
      console.error("Error fetching wallet:", err);
      setWalletError("Could not load wallet data");
    } finally {
      setWalletLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user?.organizationId) {
      setOrganizationId(session.user.organizationId);
    }

    // Fetch wallet data when component mounts
    fetchWallet();
  }, [session]);

  return (
    <PageTransition>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <PageHeaderWithBreadcrumb
            title={`Welcome, ${userName}`}
            description="Your carbon trading dashboard"
            breadcrumbItems={[
              { label: "Dashboard", href: "/dashboard", isCurrent: true }
            ]}
          />
        </div>

        {organizationId && profileCompletion < 100 && (
          <div className="mb-6">
            <ProfileCompletionBanner organizationId={organizationId} />
          </div>
        )}

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Total Carbon Credits
              </AnimatedCardTitle>
              <Leaf className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">5,000</div>
              <p className="text-xs text-muted-foreground">
                +20% from last month
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Active Listings
              </AnimatedCardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                +2 new listings this week
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Wallet Balance
              </AnimatedCardTitle>
              <div className="flex items-center">
                {walletLoading ? (
                  <AnimatedSpinner size="sm" className="mr-2" />
                ) : (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 mr-1"
                    onClick={fetchWallet}
                  >
                    <RefreshCw className="h-3 w-3" />
                  </Button>
                )}
                <Wallet className="h-4 w-4 text-muted-foreground" />
              </div>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {walletLoading ? (
                <div className="space-y-2">
                  <div className="h-6 w-24 bg-muted rounded animate-pulse"></div>
                  <div className="h-4 w-32 bg-muted rounded animate-pulse"></div>
                </div>
              ) : walletError ? (
                <div>
                  <div className="text-2xl font-bold text-muted-foreground">--</div>
                  <p className="text-xs text-red-500">{walletError}</p>
                </div>
              ) : wallet ? (
                <>
                  <div className="text-2xl font-bold">
                    {formatCurrency(wallet.balance, wallet.network)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {truncateAddress(wallet.address)} • {wallet.isTestnet ? "Testnet" : "Mainnet"}
                  </p>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-2">
                  <p className="text-sm text-muted-foreground mb-2">No wallet found</p>
                  <Button
                    size="sm"
                    onClick={() => router.push("/dashboard/wallet")}
                  >
                    Create Wallet
                  </Button>
                </div>
              )}
            </AnimatedCardContent>
          </AnimatedCard>
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Active Subscriptions
              </AnimatedCardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">Enterprise</div>
              <p className="text-xs text-muted-foreground">
                Renews in 26 days
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        <div className="mt-6 grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <StaggeredList className="col-span-4">
            <AnimatedCard className="col-span-4">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Carbon Credits</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Your carbon credit portfolio
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <CarbonCreditsWidget />
              </AnimatedCardContent>
              <AnimatedCardFooter>
                <AnimatedButton
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/dashboard/carbon-credits")}
                >
                  View All Carbon Credits
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </AnimatedCardFooter>
            </AnimatedCard>
          </StaggeredList>

          <StaggeredList className="col-span-3">
            <AnimatedCard className="col-span-3">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Quick Actions</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Common tasks and actions
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-4">
                  <Button
                    className="w-full justify-start"
                    onClick={() => router.push("/dashboard/carbon-credits/create")}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create Carbon Credit
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => router.push("/dashboard/marketplace")}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Browse Marketplace
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => router.push("/dashboard/wallet")}
                  >
                    <Wallet className="mr-2 h-4 w-4" />
                    Manage Wallet
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => router.push("/dashboard/carbon-credits/batch")}
                  >
                    <Activity className="mr-2 h-4 w-4" />
                    Batch Operations
                  </Button>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>
        </div>

        <div className="mt-6 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <TransactionHistory className="col-span-2" />

          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Profile Completion</AnimatedCardTitle>
              <AnimatedCardDescription>
                Complete your profile to unlock all features
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {organizationId ? (
                <ProfileCompletionIndicator
                  organizationId={organizationId}
                  showDetails={true}
                />
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Organization information not available
                  </p>
                </div>
              )}
            </AnimatedCardContent>
            <AnimatedCardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push("/settings/organization")}
              >
                Complete Profile
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </AnimatedCardFooter>
          </AnimatedCard>
        </div>
      </div>
    </PageTransition>
  );
}
