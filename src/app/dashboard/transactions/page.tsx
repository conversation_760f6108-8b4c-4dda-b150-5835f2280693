"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { TransactionHistoryNudges } from "@/components/transactions/history-nudges";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertCircle, Search, Filter, ArrowUpRight, ArrowDownLeft } from "lucide-react";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { PageTransition } from "@/components/ui/animated";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Transaction {
  id: string;
  type: string;
  quantity: number;
  price: number;
  totalAmount: number;
  feeAmount: number;
  status: string;
  createdAt: string;
  carbonCredit: {
    name: string;
    vintage: number;
    standard: string;
    methodology: string;
  };
  buyer: {
    name: string;
  };
  seller: {
    name: string;
  };
}

export default function TransactionsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTransactions() {
      try {
        const response = await fetch("/api/orders");
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch transactions");
        }

        setTransactions(data.orders);
        setFilteredTransactions(data.orders);
      } catch (error) {
        console.error("Error fetching transactions:", error);
        setError("Failed to load transactions");
      } finally {
        setIsLoading(false);
      }
    }

    fetchTransactions();
  }, []);

  useEffect(() => {
    // Apply filters
    let filtered = transactions;

    // Search term filter
    if (searchTerm) {
      filtered = filtered.filter(
        (transaction) =>
          transaction.carbonCredit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          transaction.buyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          transaction.seller.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          transaction.carbonCredit.standard.toLowerCase().includes(searchTerm.toLowerCase()) ||
          transaction.carbonCredit.methodology.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Type filter
    if (typeFilter) {
      filtered = filtered.filter((transaction) => transaction.type === typeFilter);
    }

    setFilteredTransactions(filtered);
  }, [transactions, searchTerm, typeFilter]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10">
        <div className="flex flex-col items-center space-y-4">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <p className="text-center text-lg font-medium">{error}</p>
          <Button variant="outline" onClick={() => router.push("/dashboard")}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <PageHeaderWithBreadcrumb
            title="Transactions"
            description="View your organization's carbon credit transactions"
            breadcrumbItems={[
              { label: "Transactions", href: "/dashboard/transactions", isCurrent: true }
            ]}
          />
        </div>

      {/* Context-specific nudges */}
      {session?.user?.organizationId && (
        <TransactionHistoryNudges organizationId={session.user.organizationId} />
      )}

      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search transactions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-xs"
          />
        </div>
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select
            value={typeFilter || "1"}
            onValueChange={(value) => setTypeFilter(value === "" ? null : value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">All Types</SelectItem>
              <SelectItem value="BUY">Purchases</SelectItem>
              <SelectItem value="SELL">Sales</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {filteredTransactions.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <div className="mb-4 rounded-full bg-primary/10 p-3">
              <Search className="h-6 w-6 text-primary" />
            </div>
            <h3 className="mb-1 text-lg font-medium">No transactions found</h3>
            <p className="mb-4 text-center text-sm text-muted-foreground">
              {transactions.length === 0
                ? "You haven't made any transactions yet."
                : "No transactions match your search criteria."}
            </p>
            {transactions.length > 0 && (
              <Button variant="outline" onClick={() => {
                setSearchTerm("");
                setTypeFilter(null);
              }}>
                Reset Filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Transaction History</CardTitle>
            <CardDescription>
              Showing {filteredTransactions.length} of {transactions.length} transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Carbon Credit</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Fee</TableHead>
                    <TableHead>Counterparty</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((transaction) => {
                    const isBuyer = session?.user?.organizationId === transaction.buyer.id;
                    const counterparty = isBuyer ? transaction.seller.name : transaction.buyer.name;

                    return (
                      <TableRow key={transaction.id}>
                        <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {isBuyer ? (
                              <ArrowUpRight className="mr-2 h-4 w-4 text-destructive" />
                            ) : (
                              <ArrowDownLeft className="mr-2 h-4 w-4 text-green-500" />
                            )}
                            {isBuyer ? "Purchase" : "Sale"}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{transaction.carbonCredit.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {transaction.carbonCredit.vintage} • {transaction.carbonCredit.standard}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.quantity.toLocaleString()} tons</TableCell>
                        <TableCell>₹{transaction.price.toFixed(2)}</TableCell>
                        <TableCell>{formatCurrency(transaction.totalAmount)}</TableCell>
                        <TableCell>{formatCurrency(transaction.feeAmount)}</TableCell>
                        <TableCell>{counterparty}</TableCell>
                        <TableCell>
                          <Badge variant={transaction.status === "COMPLETED" ? "outline" : "secondary"}>
                            {transaction.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
    </PageTransition>
  );
}
