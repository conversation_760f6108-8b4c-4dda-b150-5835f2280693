"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { ProtectedPage } from "@/components/auth/protected-page";
import { TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { truncateAddress, formatCurrency } from "@/lib/utils";
// Import wallet components
import {
  SmartWalletComponent,
  MultiChainWalletComponent,
  EnhancedPortfolioComponent
} from "@/components/wallet";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import {
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedInput,
  AnimatedTabs,
  LoadingSpinner,
  PageTransition,
  StaggeredListComponent
} from "@/components/ui/animated";
import { WalletCreation } from "@/components/dashboard/wallet-creation";

export default function WalletPage() {
  const { data: session } = useSession();
  const [wallet, setWallet] = useState<any>(null);
  const [smartWallet, setSmartWallet] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("regular");

  // Deposit/withdraw state
  const [amount, setAmount] = useState("");
  const [isDepositing, setIsDepositing] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [transactionLoading, setTransactionLoading] = useState(false);
  const [transactionError, setTransactionError] = useState<string | null>(null);
  const [transactionSuccess, setTransactionSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user) {
      fetchWallets();
    }
  }, [session]);

  const fetchWallets = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch regular wallet
      const walletResponse = await fetch("/api/wallet");

      if (!walletResponse.ok) {
        const errorData = await walletResponse.json();
        throw new Error(errorData.error ?? "Failed to fetch wallet");
      }

      const walletData = await walletResponse.json();
      setWallet(walletData.wallet);

      // Try to fetch smart wallet
      try {
        const smartWalletResponse = await fetch("/api/wallet/smart-wallet");

        if (smartWalletResponse.ok) {
          const smartWalletData = await smartWalletResponse.json();
          setSmartWallet(smartWalletData.wallet);
        }
      } catch (smartWalletError) {
        // Smart wallet might not exist yet, which is fine
        console.log("Smart wallet not found or error:", smartWalletError);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error fetching wallets:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeposit = async () => {
    if (!wallet) return;

    setTransactionLoading(true);
    setTransactionError(null);
    setTransactionSuccess(null);

    try {
      const response = await fetch("/api/wallet?operation=deposit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error ?? "Deposit failed");
      }

      const data = await response.json();
      setTransactionSuccess(`Successfully deposited ${amount} ETH`);
      setWallet({
        ...wallet,
        balance: data.wallet.balance,
      });

      // Reset form
      setAmount("");
      setIsDepositing(false);
    } catch (error) {
      setTransactionError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error depositing:", error);
    } finally {
      setTransactionLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!wallet) return;

    setTransactionLoading(true);
    setTransactionError(null);
    setTransactionSuccess(null);

    try {
      const response = await fetch("/api/wallet?operation=withdraw", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error ?? "Withdrawal failed");
      }

      const data = await response.json();
      setTransactionSuccess(`Successfully withdrew ${amount} ETH`);
      setWallet({
        ...wallet,
        balance: data.wallet.balance,
      });

      // Reset form
      setAmount("");
      setIsWithdrawing(false);
    } catch (error) {
      setTransactionError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error withdrawing:", error);
    } finally {
      setTransactionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <PageHeaderWithBreadcrumb
          title="Wallet"
          description="Manage your funds and digital assets"
          breadcrumbItems={[
            { label: "Wallet", href: "/dashboard/wallet", isCurrent: true }
          ]}
        />

        <AnimatedCard>
          <AnimatedCardContent className="pt-6">
            <div className="text-center py-4">
              <p className="text-red-500 mb-4">{error}</p>
              <AnimatedButton onClick={fetchWallets} animationVariant="buttonTap">Retry</AnimatedButton>
            </div>
          </AnimatedCardContent>
        </AnimatedCard>
      </div>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Wallet"
              description="Manage your funds and digital assets"
              breadcrumbItems={[
                { label: "Wallet", href: "/dashboard/wallet", isCurrent: true }
              ]}
            />
          </div>
          <div className="space-y-6">
            <AnimatedTabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full mb-4">
                <TabsTrigger value="regular" className="flex-1">Regular Wallet</TabsTrigger>
                <TabsTrigger value="smart" className="flex-1">Smart Wallet</TabsTrigger>
                <TabsTrigger value="multi-chain" className="flex-1">Multi-Chain</TabsTrigger>
                <TabsTrigger value="portfolio" className="flex-1">Portfolio</TabsTrigger>
              </TabsList>

              <TabsContent value="regular">
                {wallet ? (
                  <StaggeredListComponent className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Wallet Address</AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium">{truncateAddress(wallet.address)}</p>
                          <AnimatedButton
                            variant="outline"
                            size="sm"
                            onClick={() => navigator.clipboard.writeText(wallet.address)}
                            animationVariant="buttonTap"
                          >
                            Copy
                          </AnimatedButton>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>

                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Balance</AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <p className="text-2xl font-bold">{formatCurrency(wallet.balance, wallet.network)}</p>
                      </AnimatedCardContent>
                    </AnimatedCard>

                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Actions</AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent className="flex space-x-2">
                        <AnimatedButton
                          onClick={() => setIsDepositing(true)}
                          animationVariant="buttonTap"
                        >
                          Deposit
                        </AnimatedButton>
                        <AnimatedButton
                          variant="outline"
                          onClick={() => setIsWithdrawing(true)}
                          disabled={wallet.balance <= 0}
                          animationVariant="buttonTap"
                        >
                          Withdraw
                        </AnimatedButton>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </StaggeredListComponent>
                ) : (
                  <WalletCreation onWalletCreated={fetchWallets} />
                )}

          {(isDepositing || isWithdrawing) && (
            <AnimatedCard className="mt-4" animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>{isDepositing ? "Deposit Funds" : "Withdraw Funds"}</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-4">
                  <div className="grid w-full items-center gap-1.5">
                    <label htmlFor="transaction-amount">Amount (ETH)</label>
                    <AnimatedInput
                      id="transaction-amount"
                      type="number"
                      step="0.000001"
                      min="0"
                      placeholder="Enter amount"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      disabled={transactionLoading}
                    />
                  </div>

                  {transactionError && (
                    <div className="rounded-md bg-red-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-red-700">{transactionError}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {transactionSuccess && (
                    <div className="rounded-md bg-green-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-green-700">{transactionSuccess}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <AnimatedButton
                      onClick={isDepositing ? handleDeposit : handleWithdraw}
                      disabled={
                        transactionLoading ||
                        !amount ||
                        parseFloat(amount) <= 0 ||
                        (isWithdrawing && wallet && parseFloat(amount) > wallet.balance)
                      }
                      animationVariant="buttonTap"
                    >
                      {transactionLoading
                        ? isDepositing ? "Depositing..." : "Withdrawing..."
                        : isDepositing ? "Deposit" : "Withdraw"
                      }
                    </AnimatedButton>
                    <AnimatedButton
                      variant="outline"
                      onClick={() => {
                        setIsDepositing(false);
                        setIsWithdrawing(false);
                        setAmount("");
                        setTransactionError(null);
                        setTransactionSuccess(null);
                      }}
                      disabled={transactionLoading}
                      animationVariant="buttonTap"
                    >
                      Cancel
                    </AnimatedButton>
                  </div>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          <AnimatedCard className="mt-4" animationVariant="fadeIn">
            <AnimatedCardHeader>
              <AnimatedCardTitle>Transaction History</AnimatedCardTitle>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {wallet?.transactions?.length > 0 ? (
                <div className="rounded-md border">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Fee
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {wallet.transactions.map((transaction: any) => (
                        <tr key={transaction.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {transaction.type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {formatCurrency(transaction.amount, wallet.network)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {formatCurrency(transaction.fee, wallet.network)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                transaction.status === "COMPLETED"
                                  ? "bg-green-100 text-green-800"
                                  : transaction.status === "PENDING"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {transaction.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {new Date(transaction.createdAt).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-muted-foreground">No transactions yet.</p>
              )}
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="smart">
          <SmartWalletComponent
            wallet={smartWallet}
            onWalletCreated={fetchWallets}
          />
        </TabsContent>

        <TabsContent value="multi-chain">
          <MultiChainWalletComponent />
        </TabsContent>

              <TabsContent value="portfolio">
                <EnhancedPortfolioComponent />
              </TabsContent>
            </AnimatedTabs>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
