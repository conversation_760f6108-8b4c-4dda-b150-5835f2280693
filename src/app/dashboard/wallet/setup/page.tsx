"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Wallet } from "lucide-react";
import { WalletSetupNudges } from "@/components/wallet/setup-nudges";

export default function WalletSetupPage() {
  const { data: session } = useSession();
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch user's organization
  useEffect(() => {
    const fetchUserOrganization = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/user/organization');
        if (response.ok) {
          const data = await response.json();
          if (data.organization?.id) {
            setOrganizationId(data.organization.id);
          }
        }
      } catch (error) {
        console.error('Error fetching user organization:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserOrganization();
  }, []);

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Set Up Your Wallet</h1>
          <p className="text-muted-foreground">
            Create a new wallet to manage your carbon credits
          </p>
        </div>
        
        {/* Context-specific nudges */}
        {organizationId && (
          <WalletSetupNudges organizationId={organizationId} />
        )}
        
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Wallet Type</CardTitle>
              <CardDescription>
                Choose the type of wallet you want to create
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup defaultValue="smart_wallet" className="space-y-4">
                <div className="flex items-start space-x-3 rounded-md border p-4">
                  <RadioGroupItem value="smart_wallet" id="smart_wallet" className="mt-1" />
                  <div className="space-y-2">
                    <Label htmlFor="smart_wallet" className="font-medium">Smart Wallet</Label>
                    <p className="text-sm text-muted-foreground">
                      Recommended for most users. Smart wallets offer enhanced security and recovery options.
                    </p>
                    <ul className="text-sm text-muted-foreground list-disc pl-4 space-y-1">
                      <li>No seed phrase to manage</li>
                      <li>Social recovery options</li>
                      <li>Lower gas fees with batched transactions</li>
                    </ul>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 rounded-md border p-4">
                  <RadioGroupItem value="regular_wallet" id="regular_wallet" className="mt-1" />
                  <div className="space-y-2">
                    <Label htmlFor="regular_wallet" className="font-medium">Regular Wallet</Label>
                    <p className="text-sm text-muted-foreground">
                      Standard Ethereum wallet with a seed phrase. More technical but offers full control.
                    </p>
                    <ul className="text-sm text-muted-foreground list-disc pl-4 space-y-1">
                      <li>Full control over your private keys</li>
                      <li>Compatible with all standard wallets</li>
                      <li>Requires secure seed phrase management</li>
                    </ul>
                  </div>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Network & Security</CardTitle>
              <CardDescription>
                Configure your wallet network and security settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="network">Network</Label>
                <Select defaultValue="ethereum">
                  <SelectTrigger id="network">
                    <SelectValue placeholder="Select network" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ethereum">Ethereum</SelectItem>
                    <SelectItem value="polygon">Polygon</SelectItem>
                    <SelectItem value="arbitrum">Arbitrum</SelectItem>
                    <SelectItem value="optimism">Optimism</SelectItem>
                    <SelectItem value="base">Base</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Choose the blockchain network for your wallet
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="security">Security Level</Label>
                <Select defaultValue="standard">
                  <SelectTrigger id="security">
                    <SelectValue placeholder="Select security level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  High security requires additional verification for transactions
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="testMode" className="rounded border-gray-300" />
                  <Label htmlFor="testMode">Use Test Network</Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  Recommended for testing. No real funds will be used.
                </p>
              </div>
              
              <Button className="w-full">
                <Wallet className="mr-2 h-4 w-4" />
                Create Wallet
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
