"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { format } from "date-fns";
import { Bell, Check, Mail, CreditCard, MessageSquare, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Pagination } from "@/components/ui/pagination";
import { useToast } from "@/components/ui/use-toast";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { PageTransition } from "@/components/ui/animated";

type Notification = {
  id: string;
  title: string;
  message: string;
  type: "SYSTEM" | "ORDER" | "TRANSACTION" | "CREDIT";
  read: boolean;
  createdAt: string;
};

type NotificationResponse = {
  notifications: Notification[];
  pagination: {
    total: number;
    unread: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
};

export default function NotificationsPage() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    unread: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const { toast } = useToast();

  const fetchNotifications = async (offset = 0, limit = 20, unreadOnly = false) => {
    if (!session?.user) return;

    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/notifications?offset=${offset}&limit=${limit}${unreadOnly ? "&unreadOnly=true" : ""}`
      );
      const data: NotificationResponse = await response.json();

      setNotifications(data.notifications);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      toast({
        title: "Error",
        description: "Failed to load notifications",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const markAsRead = async (id: string) => {
    if (!session?.user) return;

    try {
      const response = await fetch(`/api/notifications?id=${id}`, {
        method: "PATCH",
      });

      if (response.ok) {
        setNotifications(
          notifications.map((notification) =>
            notification.id === id ? { ...notification, read: true } : notification
          )
        );
        setPagination({
          ...pagination,
          unread: Math.max(0, pagination.unread - 1),
        });
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const markAllAsRead = async () => {
    if (!session?.user) return;

    try {
      const response = await fetch("/api/notifications?markAllRead=true", {
        method: "PATCH",
      });

      if (response.ok) {
        setNotifications(
          notifications.map((notification) => ({ ...notification, read: true }))
        );
        setPagination({
          ...pagination,
          unread: 0,
        });
        toast({
          title: "Success",
          description: "All notifications marked as read",
        });
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast({
        title: "Error",
        description: "Failed to mark notifications as read",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (session?.user) {
      if (activeTab === "unread") {
        fetchNotifications(0, pagination.limit, true);
      } else if (activeTab === "all") {
        fetchNotifications(0, pagination.limit, false);
      } else {
        // Filter by type
        fetchNotifications(0, pagination.limit, false);
      }
    }
  }, [session, activeTab]);

  const handlePageChange = (newOffset: number) => {
    fetchNotifications(newOffset, pagination.limit, activeTab === "unread");
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "SYSTEM":
        return <Bell className="h-5 w-5 text-blue-500" />;
      case "ORDER":
        return <CreditCard className="h-5 w-5 text-green-500" />;
      case "TRANSACTION":
        return <MessageSquare className="h-5 w-5 text-purple-500" />;
      case "CREDIT":
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const filteredNotifications = activeTab === "all"
    ? notifications
    : activeTab === "unread"
      ? notifications.filter(n => !n.read)
      : notifications.filter(n => n.type === activeTab);

  return (
    <PageTransition>
      <div className="container mx-auto py-6">
        <div className="mb-6 flex items-center justify-between">
          <PageHeaderWithBreadcrumb
            title="Notifications"
            description="View and manage your notifications"
            breadcrumbItems={[
              { label: "Notifications", href: "/dashboard/notifications", isCurrent: true }
            ]}
          />
          {pagination.unread > 0 && (
            <Button onClick={markAllAsRead}>Mark all as read</Button>
          )}
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">
              All
              <Badge variant="secondary" className="ml-2">
                {pagination.total}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="unread">
              Unread
              <Badge variant="secondary" className="ml-2">
                {pagination.unread}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="SYSTEM">System</TabsTrigger>
            <TabsTrigger value="ORDER">Orders</TabsTrigger>
            <TabsTrigger value="TRANSACTION">Transactions</TabsTrigger>
            <TabsTrigger value="CREDIT">Carbon Credits</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-5 w-1/3" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-2/3" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
              ))}
            </div>
            ) : filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center p-6 text-center">
                  <Mail className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No notifications</h3>
                  <p className="text-sm text-muted-foreground">
                    You don't have any {activeTab === "unread" ? "unread " : ""}
                    notifications{activeTab !== "all" && activeTab !== "unread" ? ` of type ${activeTab.toLowerCase()}` : ""}.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="space-y-4">
                  {filteredNotifications.map((notification) => (
                    <Card key={notification.id} className={!notification.read ? "border-primary/50" : ""}>
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <h3 className="font-medium">{notification.title}</h3>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-muted-foreground">
                                  {format(new Date(notification.createdAt), "MMM d, yyyy 'at' h:mm a")}
                                </span>
                                {!notification.read && (
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                    onClick={() => markAsRead(notification.id)}
                                  >
                                    <Check className="h-4 w-4" />
                                    <span className="sr-only">Mark as read</span>
                                  </Button>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{notification.message}</p>
                            {!notification.read && (
                              <Badge variant="outline" className="mt-2">Unread</Badge>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {pagination.total > pagination.limit && (
                  <div className="flex items-center justify-center mt-6">
                    <Pagination>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.max(0, pagination.offset - pagination.limit))}
                        disabled={pagination.offset === 0}
                      >
                        Previous
                      </Button>
                      <div className="mx-4 text-sm text-muted-foreground">
                        Page {Math.floor(pagination.offset / pagination.limit) + 1} of{" "}
                        {Math.ceil(pagination.total / pagination.limit)}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.offset + pagination.limit)}
                        disabled={!pagination.hasMore}
                      >
                        Next
                      </Button>
                    </Pagination>
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </PageTransition>
  );
}
