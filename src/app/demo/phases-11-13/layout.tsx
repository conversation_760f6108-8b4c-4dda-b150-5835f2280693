import { Metadata } from "next";
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: "Phases 11-13 Demo | Carbon Exchange",
  description: "Demonstration of components implemented for Phases 11, 12, and 13 of the UX improvement plan",
};

export default function DemoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-muted/40">
      <div className="bg-background py-4 border-b sticky top-0 z-10">
        <div className="container mx-auto flex items-center justify-between">
          <h1 className="text-xl font-bold">Carbon Exchange</h1>
          <div className="flex items-center space-x-4">
            <a href="/" className="text-sm text-muted-foreground hover:text-foreground">
              Back to Home
            </a>
            <a href="/docs/phases-11-13-implementation" className="text-sm text-muted-foreground hover:text-foreground">
              Documentation
            </a>
          </div>
        </div>
      </div>
      {children}
      <Toaster />
    </div>
  );
}
