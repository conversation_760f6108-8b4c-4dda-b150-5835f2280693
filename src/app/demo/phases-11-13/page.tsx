"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ProjectPortfolioDashboard } from "@/components/projects/project-portfolio-dashboard";
import { ProjectCreditLinking } from "@/components/projects/project-credit-linking";
import { RetirementWizard } from "@/components/carbon-credits/retirement-wizard";
import { ImpactDashboard } from "@/components/impact/impact-dashboard";
import { TransactionDashboard } from "@/components/blockchain/transaction-dashboard";
import { GasOptimization } from "@/components/blockchain/gas-optimization";
import { toast } from "@/components/ui/use-toast";

// Mock data for demonstration
const mockProjects = [
  {
    id: "project-1",
    name: "Renewable Energy Project - Solar Farm",
    description: "A large-scale solar farm project in Arizona that generates clean energy.",
    type: "RENEWABLE_ENERGY",
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: "2022-01-15",
    endDate: "2032-01-15",
    location: "Arizona, USA",
    country: "USA",
    standard: "Gold Standard",
    methodology: "GS-RE-AM-01",
    estimatedReductions: 50000,
    actualReductions: 25000,
    createdAt: "2021-12-01",
    updatedAt: "2023-05-15",
    carbonCredits: [
      {
        id: "credit-1",
        name: "Solar Farm Carbon Credit 2022",
        quantity: 10000,
        availableQuantity: 8000,
        retiredQuantity: 2000,
        price: 15,
        vintage: 2022,
        standard: "Gold Standard",
        methodology: "GS-RE-AM-01",
        status: "VERIFIED",
        createdAt: "2022-02-15",
        updatedAt: "2023-01-10",
      },
      {
        id: "credit-2",
        name: "Solar Farm Carbon Credit 2023",
        quantity: 15000,
        availableQuantity: 15000,
        retiredQuantity: 0,
        price: 18,
        vintage: 2023,
        standard: "Gold Standard",
        methodology: "GS-RE-AM-01",
        status: "VERIFIED",
        createdAt: "2023-02-20",
        updatedAt: "2023-02-20",
      }
    ],
    organization: {
      id: "org-1",
      name: "Green Energy Solutions"
    }
  },
  {
    id: "project-2",
    name: "Reforestation Project - Amazon Basin",
    description: "A reforestation project in the Amazon Basin to restore degraded forest land.",
    type: "FORESTRY",
    status: "ACTIVE",
    verificationStatus: "IN_PROGRESS",
    startDate: "2021-06-10",
    endDate: "2041-06-10",
    location: "Amazon Basin, Brazil",
    country: "Brazil",
    standard: "Verified Carbon Standard",
    methodology: "VM0007",
    estimatedReductions: 100000,
    actualReductions: 20000,
    createdAt: "2021-05-01",
    updatedAt: "2023-04-20",
    carbonCredits: [
      {
        id: "credit-3",
        name: "Amazon Reforestation Credit 2022",
        quantity: 20000,
        availableQuantity: 15000,
        retiredQuantity: 5000,
        price: 22,
        vintage: 2022,
        standard: "Verified Carbon Standard",
        methodology: "VM0007",
        status: "VERIFIED",
        createdAt: "2022-07-10",
        updatedAt: "2023-03-15",
      }
    ],
    organization: {
      id: "org-1",
      name: "Green Energy Solutions"
    }
  },
  {
    id: "project-3",
    name: "Methane Capture - Landfill Gas",
    description: "A project to capture and utilize methane gas from a large landfill site.",
    type: "METHANE_REDUCTION",
    status: "PENDING",
    verificationStatus: "PENDING",
    startDate: "2023-03-01",
    endDate: "2033-03-01",
    location: "California, USA",
    country: "USA",
    standard: "American Carbon Registry",
    methodology: "ACR-LFG-01",
    estimatedReductions: 75000,
    actualReductions: 0,
    createdAt: "2023-01-15",
    updatedAt: "2023-01-15",
    carbonCredits: [],
    organization: {
      id: "org-2",
      name: "CleanTech Innovations"
    }
  }
];

const mockAvailableCredits = [
  {
    id: "credit-1",
    name: "Solar Farm Carbon Credit 2022",
    quantity: 10000,
    availableQuantity: 8000,
    retiredQuantity: 2000,
    price: 15,
    vintage: 2022,
    standard: "Gold Standard",
    methodology: "GS-RE-AM-01",
    status: "VERIFIED",
    createdAt: "2022-02-15",
    updatedAt: "2023-01-10",
    projectId: "project-1"
  },
  {
    id: "credit-2",
    name: "Solar Farm Carbon Credit 2023",
    quantity: 15000,
    availableQuantity: 15000,
    retiredQuantity: 0,
    price: 18,
    vintage: 2023,
    standard: "Gold Standard",
    methodology: "GS-RE-AM-01",
    status: "VERIFIED",
    createdAt: "2023-02-20",
    updatedAt: "2023-02-20",
    projectId: "project-1"
  },
  {
    id: "credit-3",
    name: "Amazon Reforestation Credit 2022",
    quantity: 20000,
    availableQuantity: 15000,
    retiredQuantity: 5000,
    price: 22,
    vintage: 2022,
    standard: "Verified Carbon Standard",
    methodology: "VM0007",
    status: "VERIFIED",
    createdAt: "2022-07-10",
    updatedAt: "2023-03-15",
    projectId: "project-2"
  },
  {
    id: "credit-4",
    name: "Wind Farm Carbon Credit 2023",
    quantity: 12000,
    availableQuantity: 12000,
    retiredQuantity: 0,
    price: 16,
    vintage: 2023,
    standard: "Gold Standard",
    methodology: "GS-RE-AM-02",
    status: "VERIFIED",
    createdAt: "2023-01-05",
    updatedAt: "2023-01-05"
  },
  {
    id: "credit-5",
    name: "Energy Efficiency Credit 2022",
    quantity: 8000,
    availableQuantity: 6000,
    retiredQuantity: 2000,
    price: 14,
    vintage: 2022,
    standard: "Climate Action Reserve",
    methodology: "CAR-EE-01",
    status: "VERIFIED",
    createdAt: "2022-05-20",
    updatedAt: "2023-02-10"
  }
];

const mockRetirements = [
  {
    id: "retirement-1",
    creditId: "credit-1",
    creditName: "Solar Farm Carbon Credit 2022",
    quantity: 1000,
    purpose: "corporate_offsetting",
    beneficiary: "Acme Corporation",
    retirementDate: "2023-01-15",
    projectId: "project-1",
    projectName: "Renewable Energy Project - Solar Farm",
    projectType: "RENEWABLE_ENERGY",
    standard: "Gold Standard",
    methodology: "GS-RE-AM-01",
    vintage: 2022
  },
  {
    id: "retirement-2",
    creditId: "credit-1",
    creditName: "Solar Farm Carbon Credit 2022",
    quantity: 1000,
    purpose: "product_offsetting",
    beneficiary: "Acme Corporation - Product Line A",
    retirementDate: "2023-02-20",
    projectId: "project-1",
    projectName: "Renewable Energy Project - Solar Farm",
    projectType: "RENEWABLE_ENERGY",
    standard: "Gold Standard",
    methodology: "GS-RE-AM-01",
    vintage: 2022
  },
  {
    id: "retirement-3",
    creditId: "credit-3",
    creditName: "Amazon Reforestation Credit 2022",
    quantity: 5000,
    purpose: "environmental_contribution",
    beneficiary: "Green Earth Foundation",
    retirementDate: "2023-03-10",
    projectId: "project-2",
    projectName: "Reforestation Project - Amazon Basin",
    projectType: "FORESTRY",
    standard: "Verified Carbon Standard",
    methodology: "VM0007",
    vintage: 2022
  },
  {
    id: "retirement-4",
    creditId: "credit-5",
    creditName: "Energy Efficiency Credit 2022",
    quantity: 2000,
    purpose: "event_offsetting",
    beneficiary: "Global Climate Conference 2023",
    retirementDate: "2023-04-05",
    standard: "Climate Action Reserve",
    methodology: "CAR-EE-01",
    vintage: 2022
  }
];

const mockTransactions = [
  {
    id: "tx-1",
    hash: "******************************************90abcdef1234567890abcdef",
    type: "TRANSFER",
    status: "CONFIRMED",
    timestamp: "2023-05-15T10:30:00Z",
    from: "******************************************",
    to: "******************************************",
    value: 1000000000000000000, // 1 ETH
    gasUsed: 21000,
    gasPrice: 30000000000, // 30 Gwei
    chainId: 1,
    blockNumber: 17000000,
    nonce: 42
  },
  {
    id: "tx-2",
    hash: "******************************************34567890abcdef1234567890",
    type: "CONTRACT_INTERACTION",
    status: "PENDING",
    timestamp: "2023-05-15T11:45:00Z",
    from: "******************************************",
    to: "******************************************",
    value: 0,
    gasUsed: 150000,
    gasPrice: 50000000000, // 50 Gwei
    chainId: 1,
    nonce: 43
  },
  {
    id: "tx-3",
    hash: "******************************************ef1234567890abcdef123456",
    type: "MINT",
    status: "FAILED",
    timestamp: "2023-05-14T09:15:00Z",
    from: "******************************************",
    to: "******************************************",
    value: 0,
    gasUsed: 200000,
    gasPrice: 40000000000, // 40 Gwei
    chainId: 1,
    nonce: 41,
    error: "Out of gas"
  }
];

const mockChains = [
  {
    id: 1,
    name: "Ethereum Mainnet",
    nativeCurrency: {
      name: "Ether",
      symbol: "ETH",
      decimals: 18
    },
    blockExplorerUrl: "https://etherscan.io"
  },
  {
    id: 137,
    name: "Polygon",
    nativeCurrency: {
      name: "MATIC",
      symbol: "MATIC",
      decimals: 18
    },
    blockExplorerUrl: "https://polygonscan.com"
  }
];

const mockGasPriceRecommendations = {
  1: {
    slow: 30,
    standard: 50,
    fast: 80,
    rapid: 120
  },
  137: {
    slow: 50,
    standard: 100,
    fast: 200,
    rapid: 300
  }
};

export default function PhasesDemo() {
  const [activeTab, setActiveTab] = useState("phase11");
  const [selectedProject, setSelectedProject] = useState<any>(mockProjects[0]);
  const [showLinkingInterface, setShowLinkingInterface] = useState(false);
  const [showRetirementWizard, setShowRetirementWizard] = useState(false);
  const [showGasOptimization, setShowGasOptimization] = useState(false);
  
  const handleProjectSelect = (projectId: string) => {
    const project = mockProjects.find(p => p.id === projectId);
    if (project) {
      setSelectedProject(project);
      toast({
        title: "Project Selected",
        description: `Selected project: ${project.name}`,
      });
    }
  };
  
  const handleLinkCredits = (projectId: string) => {
    const project = mockProjects.find(p => p.id === projectId);
    if (project) {
      setSelectedProject(project);
      setShowLinkingInterface(true);
    }
  };
  
  const handleSaveLinking = (linkedCredits: string[], unlinkedCredits: string[]) => {
    toast({
      title: "Credits Updated",
      description: `Linked ${linkedCredits.length} credits and unlinked ${unlinkedCredits.length} credits.`,
    });
    setShowLinkingInterface(false);
  };
  
  const handleRetirementComplete = (data: any) => {
    toast({
      title: "Retirement Successful",
      description: `Successfully retired ${data.quantity} tons of carbon credits.`,
    });
    setShowRetirementWizard(false);
  };
  
  const handleViewTransaction = (transactionId: string) => {
    toast({
      title: "Transaction Details",
      description: `Viewing details for transaction ${transactionId}`,
    });
  };
  
  const handleSpeedUp = (transactionId: string) => {
    setShowGasOptimization(true);
  };
  
  const handleCancel = (transactionId: string) => {
    toast({
      title: "Transaction Cancelled",
      description: `Cancelled transaction ${transactionId}`,
    });
  };
  
  const handleSaveGasSettings = (data: any) => {
    toast({
      title: "Gas Settings Saved",
      description: `Updated gas settings with ${data.enableEIP1559 ? 'EIP-1559' : 'Legacy'} transaction type.`,
    });
    setShowGasOptimization(false);
  };
  
  return (
    <div className="container mx-auto py-8 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Phases 11-13 Demo</CardTitle>
          <CardDescription>
            Demonstration of components implemented for Phases 11, 12, and 13 of the UX improvement plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="phase11">Phase 11: Project-Credit Relationship</TabsTrigger>
              <TabsTrigger value="phase12">Phase 12: Retirement & Impact</TabsTrigger>
              <TabsTrigger value="phase13">Phase 13: Blockchain Transactions</TabsTrigger>
            </TabsList>
            
            <TabsContent value="phase11" className="mt-6">
              {showLinkingInterface ? (
                <ProjectCreditLinking 
                  project={selectedProject}
                  availableCredits={mockAvailableCredits}
                  onSave={handleSaveLinking}
                  onCancel={() => setShowLinkingInterface(false)}
                />
              ) : (
                <ProjectPortfolioDashboard 
                  projects={mockProjects}
                  onProjectSelect={handleProjectSelect}
                  onLinkCredits={handleLinkCredits}
                />
              )}
            </TabsContent>
            
            <TabsContent value="phase12" className="mt-6">
              {showRetirementWizard ? (
                <RetirementWizard 
                  carbonCredit={mockAvailableCredits[0]}
                  onComplete={handleRetirementComplete}
                  onCancel={() => setShowRetirementWizard(false)}
                />
              ) : (
                <div className="space-y-6">
                  <Button onClick={() => setShowRetirementWizard(true)}>
                    Start Retirement Process
                  </Button>
                  <ImpactDashboard 
                    retirements={mockRetirements}
                    onExport={() => toast({ title: "Exporting data..." })}
                    onShare={() => toast({ title: "Sharing dashboard..." })}
                  />
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="phase13" className="mt-6">
              {showGasOptimization ? (
                <GasOptimization 
                  transaction={mockTransactions[1]}
                  chains={mockChains}
                  gasPriceRecommendations={mockGasPriceRecommendations}
                  onSave={handleSaveGasSettings}
                  onCancel={() => setShowGasOptimization(false)}
                />
              ) : (
                <TransactionDashboard 
                  transactions={mockTransactions}
                  chains={mockChains}
                  gasPriceRecommendations={mockGasPriceRecommendations}
                  onViewTransaction={handleViewTransaction}
                  onSpeedUp={handleSpeedUp}
                  onCancel={handleCancel}
                  onExport={() => toast({ title: "Exporting transactions..." })}
                />
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
