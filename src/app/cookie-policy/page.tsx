"use client";

import { PageTransition } from "@/components/ui/animated";
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

export default function CookiePolicyPage() {
  // Current date for "Last Updated"
  const currentDate = new Date();
  const formattedDate = `${currentDate.toLocaleString('default', { month: 'long' })} ${currentDate.getDate()}, ${currentDate.getFullYear()}`;

  return (
    <PageTransition animationVariant="fadeIn">
      <div className="bg-white">
        <div className="relative overflow-hidden bg-gradient-to-b from-green-50 to-blue-50 pt-16 pb-16">
          <div className="relative">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:mx-0">
                <motion.h1
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  Cookie Policy
                </motion.h1>
                <motion.p
                  className="mt-6 text-lg leading-8 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  Last Updated: {formattedDate}
                </motion.p>
              </div>
            </div>
          </div>
        </div>

        {/* Cookie Policy Content */}
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="mx-auto max-w-3xl">
            <motion.div
              className="prose prose-lg prose-green max-w-none"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <p>
                This Cookie Policy explains how Carbonix ("we", "us", or "our") uses cookies and similar technologies to recognize you when you visit our website and platform. It explains what these technologies are and why we use them, as well as your rights to control our use of them.
              </p>

              <p>
                Please read this Cookie Policy carefully before using our website and platform. By using our services, you are accepting the practices described in this Cookie Policy.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">What Are Cookies?</h2>
              <p>
                Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners to make their websites work, or to work more efficiently, as well as to provide reporting information.
              </p>
              <p className="mt-4">
                Cookies set by the website owner (in this case, Carbonix) are called "first-party cookies". Cookies set by parties other than the website owner are called "third-party cookies". Third-party cookies enable third-party features or functionality to be provided on or through the website (e.g., advertising, interactive content, and analytics). The parties that set these third-party cookies can recognize your computer both when it visits the website in question and also when it visits certain other websites.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Why Do We Use Cookies?</h2>
              <p>
                We use first-party and third-party cookies for several reasons. Some cookies are required for technical reasons in order for our website to operate, and we refer to these as "essential" or "strictly necessary" cookies. Other cookies also enable us to track and target the interests of our users to enhance the experience on our website and platform. Third parties serve cookies through our website for advertising, analytics, and other purposes.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Types of Cookies We Use</h2>
              
              <h3 className="text-xl font-semibold mt-6 mb-3">Essential Cookies</h3>
              <p>
                These cookies are strictly necessary to provide you with services available through our website and to use some of its features, such as access to secure areas. Because these cookies are strictly necessary to deliver the website, you cannot refuse them without impacting how our website functions.
              </p>

              <h3 className="text-xl font-semibold mt-6 mb-3">Performance & Functionality Cookies</h3>
              <p>
                These cookies are used to enhance the performance and functionality of our website but are non-essential to their use. However, without these cookies, certain functionality may become unavailable.
              </p>

              <h3 className="text-xl font-semibold mt-6 mb-3">Analytics & Customization Cookies</h3>
              <p>
                These cookies collect information that is used either in aggregate form to help us understand how our website is being used or how effective our marketing campaigns are, or to help us customize our website for you to enhance your experience.
              </p>

              <h3 className="text-xl font-semibold mt-6 mb-3">Advertising Cookies</h3>
              <p>
                These cookies are used to make advertising messages more relevant to you. They perform functions like preventing the same ad from continuously reappearing, ensuring that ads are properly displayed, and in some cases selecting advertisements that are based on your interests.
              </p>

              <h3 className="text-xl font-semibold mt-6 mb-3">Social Media Cookies</h3>
              <p>
                These cookies are used to enable you to share pages and content that you find interesting on our website through third-party social networking and other websites. These cookies may also be used for advertising purposes.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Your Choices Regarding Cookies</h2>
              <p>
                If you would like to restrict or block cookies set on our website, you can do this through your browser settings. The "Help" function within your browser should tell you how. Alternatively, you may wish to visit <a href="https://www.allaboutcookies.org" target="_blank" rel="noopener noreferrer" className="text-green-600 hover:text-green-500">www.allaboutcookies.org</a>, which contains comprehensive information on how to do this on a wide variety of browsers and devices.
              </p>
              <p className="mt-4">
                Please be aware that restricting cookies may impact the functionality of our website. For example, you may not be able to access certain parts of our platform, such as your account or personalized content.
              </p>
              <p className="mt-4">
                If you wish to withdraw your consent at any time, you will need to delete your cookies using your internet browser settings.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Cookie Preferences</h2>
              <p>
                When you first visit our website, you will be presented with a cookie banner that allows you to accept or decline non-essential cookies. You can change your preferences at any time by clicking the button below:
              </p>
              <div className="mt-6">
                <Button
                  variant="outline"
                  className="border-green-600 text-green-600 hover:bg-green-50"
                  onClick={() => alert("This would open the cookie preferences dialog in a production environment.")}
                >
                  Manage Cookie Preferences
                </Button>
              </div>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Updates to This Cookie Policy</h2>
              <p>
                We may update this Cookie Policy from time to time to reflect changes in technology, regulation, or our business practices. Any changes will become effective when we post the revised Cookie Policy on our website. We encourage you to check back periodically to stay informed about our use of cookies.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Contact Us</h2>
              <p>
                If you have any questions about our use of cookies or this Cookie Policy, please contact us at:
              </p>
              <p className="mt-4">
                Carbonix<br />
                123 Green Street, Suite 100<br />
                San Francisco, CA 94107<br />
                Email: <EMAIL>
              </p>
              
              <div className="mt-12 pt-8 border-t border-gray-200">
                <p className="text-gray-600">
                  By using our website and platform, you acknowledge that you have read and understood this Cookie Policy.
                </p>
                <div className="mt-6">
                  <Link href="/privacy" className="text-green-600 hover:text-green-500 font-medium">
                    Privacy Policy
                  </Link>{' '}
                  |{' '}
                  <Link href="/terms" className="text-green-600 hover:text-green-500 font-medium">
                    Terms of Service
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}