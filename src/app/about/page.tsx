"use client";

import { PageTransition } from "@/components/ui/animated";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { motion } from "framer-motion";

export default function AboutPage() {
  return (
    <PageTransition animationVariant="fadeIn">
      <div className="bg-white">
        <div className="relative overflow-hidden bg-gradient-to-b from-green-50 to-blue-50 pt-16 pb-32">
          <div className="relative">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:mx-0">
                <motion.h1
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  About Carbonix
                </motion.h1>
                <motion.p
                  className="mt-6 text-lg leading-8 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  We're building the future of carbon credit trading for enterprises. Our mission is to accelerate the transition to a low-carbon economy through transparent and efficient market mechanisms.
                </motion.p>
              </div>
            </div>
          </div>
        </div>

        {/* Values section */}
        <div className="mx-auto mt-16 max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:mx-0">
            <motion.h2 
              className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Our Values
            </motion.h2>
            <motion.p 
              className="mt-6 text-lg leading-8 text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              At Carbonix, we believe in the power of markets to drive positive environmental change. Our platform is built on the principles of transparency, efficiency, and trust.
            </motion.p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <motion.div
                className="flex flex-col"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-green-100">
                    🌱
                  </div>
                  Transparency
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">We believe in complete transparency in carbon markets. Our blockchain-based platform ensures verifiable and traceable carbon credits.</p>
                </dd>
              </motion.div>
              <motion.div
                className="flex flex-col"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-green-100">
                    🔄
                  </div>
                  Efficiency
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">We streamline the carbon credit trading process, reducing friction and administrative overhead for all market participants.</p>
                </dd>
              </motion.div>
              <motion.div
                className="flex flex-col"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-green-100">
                    🔒
                  </div>
                  Trust
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">Our platform creates trust through rigorous verification processes and secure technology infrastructure.</p>
                </dd>
              </motion.div>
            </dl>
          </div>
        </div>

        {/* Team section */}
        <div className="mx-auto mt-24 max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:mx-0">
            <motion.h2 
              className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Our Team
            </motion.h2>
            <motion.p 
              className="mt-6 text-lg leading-8 text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              We're a team of experts in carbon markets, blockchain technology, and sustainable finance.
            </motion.p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-14 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="border border-gray-200 rounded-lg p-6"
            >
              <div className="h-24 w-24 rounded-full bg-green-100 mb-4 flex items-center justify-center text-2xl font-bold">JD</div>
              <h3 className="text-xl font-semibold leading-7 tracking-tight text-gray-900">Jane Doe</h3>
              <p className="text-base font-semibold leading-6 text-green-600">Chief Executive Officer</p>
              <p className="mt-2 text-sm leading-6 text-gray-600">15+ years in carbon markets and sustainable finance.</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="border border-gray-200 rounded-lg p-6"
            >
              <div className="h-24 w-24 rounded-full bg-blue-100 mb-4 flex items-center justify-center text-2xl font-bold">JS</div>
              <h3 className="text-xl font-semibold leading-7 tracking-tight text-gray-900">John Smith</h3>
              <p className="text-base font-semibold leading-6 text-green-600">Chief Technology Officer</p>
              <p className="mt-2 text-sm leading-6 text-gray-600">Blockchain expert with experience in fintech solutions.</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="border border-gray-200 rounded-lg p-6"
            >
              <div className="h-24 w-24 rounded-full bg-purple-100 mb-4 flex items-center justify-center text-2xl font-bold">AK</div>
              <h3 className="text-xl font-semibold leading-7 tracking-tight text-gray-900">Aisha Khan</h3>
              <p className="text-base font-semibold leading-6 text-green-600">Head of Sustainability</p>
              <p className="mt-2 text-sm leading-6 text-gray-600">Environmental scientist specializing in carbon credit verification.</p>
            </motion.div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mx-auto mt-24 max-w-7xl sm:mt-32 sm:px-6 lg:px-8">
          <div className="relative overflow-hidden bg-gray-900 px-6 py-20 shadow-xl sm:rounded-3xl sm:px-24">
            <motion.div
              className="mx-auto max-w-2xl text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Join us in the fight against climate change
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
                Ready to make a difference? Start trading carbon credits on our platform today.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link href="/register">
                    <Button className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-gray-900 shadow-sm hover:bg-gray-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white">Get started</Button>
                  </Link>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link href="/contact">
                    <Button variant="outline" className="rounded-md px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-white/10">Contact us</Button>
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        <div className="mt-24 mb-24"></div>
      </div>
    </PageTransition>
  );
}