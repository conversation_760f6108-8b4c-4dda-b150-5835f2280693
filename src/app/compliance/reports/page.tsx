"use client";

import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/animations/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ReportManagement } from "@/components/compliance/report-management";

export default function ComplianceReportsPage() {
  const { data: session } = useSession();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Compliance Reports"
              description="Manage and generate compliance reports for your organization"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "Reports", href: "/compliance/reports", isCurrent: true }
              ]}
            />
          </div>

          <ReportManagement userId={session?.user?.id} organizationId={session?.user?.organizationId} />
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
