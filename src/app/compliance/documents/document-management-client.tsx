"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON>ard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Loader2, 
  AlertTriangle,
  FileText,
  Upload,
  Clock,
  CheckCircle,
  XCircle,
  ArrowRight,
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  Calendar,
  Shield,
  FileCheck
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ComplianceDocumentType, ComplianceStatus } from "@/lib/compliance";

interface DocumentManagementClientProps {
  userName: string;
}

interface Document {
  id: string;
  name: string;
  type: ComplianceDocumentType;
  category: string;
  status: ComplianceStatus;
  uploadDate: string;
  expiryDate?: string;
  fileSize: string;
  fileType: string;
  url: string;
}

export default function DocumentManagementClient({ userName }: DocumentManagementClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    async function fetchDocuments() {
      try {
        setIsLoading(true);
        
        // Fetch documents
        const response = await fetch('/api/compliance/documents');
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch documents");
        }
        
        const data = await response.json();
        setDocuments(data.documents);
      } catch (error) {
        console.error("Error fetching documents:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchDocuments();
  }, []);

  // Mock data for development
  const mockDocuments: Document[] = [
    {
      id: "doc-1",
      name: "Passport",
      type: ComplianceDocumentType.PASSPORT,
      category: "Identity",
      status: ComplianceStatus.APPROVED,
      uploadDate: "2023-06-15",
      expiryDate: "2028-06-15",
      fileSize: "2.4 MB",
      fileType: "PDF",
      url: "/documents/passport.pdf"
    },
    {
      id: "doc-2",
      name: "Utility Bill",
      type: ComplianceDocumentType.PROOF_OF_ADDRESS,
      category: "Address",
      status: ComplianceStatus.APPROVED,
      uploadDate: "2023-06-10",
      expiryDate: "2024-06-10",
      fileSize: "1.2 MB",
      fileType: "PDF",
      url: "/documents/utility-bill.pdf"
    },
    {
      id: "doc-3",
      name: "Business Registration",
      type: ComplianceDocumentType.BUSINESS_REGISTRATION,
      category: "Business",
      status: ComplianceStatus.APPROVED,
      uploadDate: "2023-05-28",
      fileSize: "3.5 MB",
      fileType: "PDF",
      url: "/documents/business-registration.pdf"
    },
    {
      id: "doc-4",
      name: "Tax Certificate",
      type: ComplianceDocumentType.TAX_CERTIFICATE,
      category: "Tax",
      status: ComplianceStatus.IN_REVIEW,
      uploadDate: "2023-06-05",
      fileSize: "1.8 MB",
      fileType: "PDF",
      url: "/documents/tax-certificate.pdf"
    },
    {
      id: "doc-5",
      name: "Bank Statement",
      type: ComplianceDocumentType.BANK_STATEMENT,
      category: "Financial",
      status: ComplianceStatus.APPROVED,
      uploadDate: "2023-05-20",
      expiryDate: "2023-08-20",
      fileSize: "2.1 MB",
      fileType: "PDF",
      url: "/documents/bank-statement.pdf"
    },
    {
      id: "doc-6",
      name: "Project Description",
      type: ComplianceDocumentType.OTHER,
      category: "Project",
      status: ComplianceStatus.APPROVED,
      uploadDate: "2023-04-15",
      fileSize: "4.2 MB",
      fileType: "PDF",
      url: "/documents/project-description.pdf"
    },
    {
      id: "doc-7",
      name: "Verification Report",
      type: ComplianceDocumentType.OTHER,
      category: "Verification",
      status: ComplianceStatus.REJECTED,
      uploadDate: "2023-05-10",
      fileSize: "3.7 MB",
      fileType: "PDF",
      url: "/documents/verification-report.pdf"
    }
  ];

  const allDocuments = documents.length > 0 ? documents : mockDocuments;

  // Filter documents based on search query, category, and status
  const filteredDocuments = allDocuments.filter(doc => {
    const matchesSearch = searchQuery === "" || 
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === "all" || doc.category === selectedCategory;
    
    const matchesStatus = selectedStatus === "all" || doc.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Filter documents based on tab
  const tabFilteredDocuments = activeTab === "all" 
    ? filteredDocuments 
    : activeTab === "pending" 
      ? filteredDocuments.filter(doc => doc.status === ComplianceStatus.PENDING || doc.status === ComplianceStatus.IN_REVIEW)
      : activeTab === "approved"
        ? filteredDocuments.filter(doc => doc.status === ComplianceStatus.APPROVED)
        : activeTab === "rejected"
          ? filteredDocuments.filter(doc => doc.status === ComplianceStatus.REJECTED)
          : activeTab === "expiring"
            ? filteredDocuments.filter(doc => doc.expiryDate && new Date(doc.expiryDate) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
            : filteredDocuments;

  // Get unique categories
  const categories = ["all", ...new Set(allDocuments.map(doc => doc.category))];

  const getStatusBadge = (status: ComplianceStatus) => {
    switch (status) {
      case ComplianceStatus.APPROVED:
        return <Badge variant="success">Approved</Badge>;
      case ComplianceStatus.PENDING:
      case ComplianceStatus.IN_REVIEW:
        return <Badge variant="warning">In Review</Badge>;
      case ComplianceStatus.REJECTED:
        return <Badge variant="destructive">Rejected</Badge>;
      case ComplianceStatus.EXPIRED:
        return <Badge variant="outline">Expired</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getDocumentTypeIcon = (type: ComplianceDocumentType) => {
    switch (type) {
      case ComplianceDocumentType.PASSPORT:
      case ComplianceDocumentType.DRIVERS_LICENSE:
      case ComplianceDocumentType.NATIONAL_ID:
        return <Shield className="h-5 w-5 text-blue-600" />;
      case ComplianceDocumentType.PROOF_OF_ADDRESS:
      case ComplianceDocumentType.UTILITY_BILL:
        return <FileText className="h-5 w-5 text-green-600" />;
      case ComplianceDocumentType.BANK_STATEMENT:
        return <FileText className="h-5 w-5 text-yellow-600" />;
      case ComplianceDocumentType.BUSINESS_REGISTRATION:
      case ComplianceDocumentType.TAX_CERTIFICATE:
        return <FileCheck className="h-5 w-5 text-purple-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const handleUpload = () => {
    setIsUploading(true);
    // Simulate upload delay
    setTimeout(() => {
      setIsUploading(false);
      router.push("/compliance/documents/upload");
    }, 500);
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Document Management"
              description="Manage your compliance documents and certifications"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "Documents", href: "/compliance/documents", isCurrent: true }
              ]}
            />
            <div className="mt-4 sm:mt-0">
              <AnimatedButton 
                onClick={handleUpload}
                disabled={isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Document
                  </>
                )}
              </AnimatedButton>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading documents...</span>
            </div>
          ) : error ? (
            <AnimatedCard className="border-destructive/50 bg-destructive/10">
              <AnimatedCardContent className="flex items-center py-6">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <p className="ml-2 text-destructive">{error}</p>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <>
              <AnimatedCard className="mb-6">
                <AnimatedCardContent className="p-4">
                  <div className="grid gap-4 md:grid-cols-3">
                    <div>
                      <Label htmlFor="search">Search Documents</Label>
                      <div className="relative mt-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="search"
                          placeholder="Search by name, type, or category..."
                          className="pl-8"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={selectedCategory}
                        onValueChange={setSelectedCategory}
                      >
                        <SelectTrigger id="category" className="mt-1">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          {categories.filter(c => c !== "all").map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={selectedStatus}
                        onValueChange={setSelectedStatus}
                      >
                        <SelectTrigger id="status" className="mt-1">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value={ComplianceStatus.APPROVED}>Approved</SelectItem>
                          <SelectItem value={ComplianceStatus.IN_REVIEW}>In Review</SelectItem>
                          <SelectItem value={ComplianceStatus.REJECTED}>Rejected</SelectItem>
                          <SelectItem value={ComplianceStatus.EXPIRED}>Expired</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>

              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="w-full">
                  <TabsTrigger value="all" className="flex items-center">
                    <FileText className="mr-2 h-4 w-4" />
                    All Documents
                  </TabsTrigger>
                  <TabsTrigger value="pending" className="flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    Pending
                  </TabsTrigger>
                  <TabsTrigger value="approved" className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approved
                  </TabsTrigger>
                  <TabsTrigger value="rejected" className="flex items-center">
                    <XCircle className="mr-2 h-4 w-4" />
                    Rejected
                  </TabsTrigger>
                  <TabsTrigger value="expiring" className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Expiring Soon
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value={activeTab} className="space-y-6">
                  {tabFilteredDocuments.length > 0 ? (
                    <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {tabFilteredDocuments.map((doc) => (
                        <AnimatedCard key={doc.id} className="overflow-hidden">
                          <AnimatedCardHeader className="pb-2">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center space-x-2">
                                <div className="bg-muted p-2 rounded-full">
                                  {getDocumentTypeIcon(doc.type)}
                                </div>
                                <div>
                                  <AnimatedCardTitle className="text-base">{doc.name}</AnimatedCardTitle>
                                  <AnimatedCardDescription className="text-xs">
                                    {doc.category} • {doc.fileType} • {doc.fileSize}
                                  </AnimatedCardDescription>
                                </div>
                              </div>
                              {getStatusBadge(doc.status)}
                            </div>
                          </AnimatedCardHeader>
                          <AnimatedCardContent className="pb-2">
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Uploaded</span>
                                <span>{doc.uploadDate}</span>
                              </div>
                              {doc.expiryDate && (
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">Expires</span>
                                  <span>{doc.expiryDate}</span>
                                </div>
                              )}
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Type</span>
                                <span>{doc.type}</span>
                              </div>
                            </div>
                          </AnimatedCardContent>
                          <AnimatedCardFooter className="flex justify-between pt-2">
                            <AnimatedButton 
                              variant="ghost" 
                              size="sm"
                              onClick={() => window.open(doc.url, '_blank')}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </AnimatedButton>
                            <div className="space-x-1">
                              <AnimatedButton 
                                variant="ghost" 
                                size="sm"
                                onClick={() => window.open(doc.url, '_blank')}
                              >
                                <Download className="h-4 w-4" />
                              </AnimatedButton>
                              <AnimatedButton 
                                variant="ghost" 
                                size="sm"
                                onClick={() => {
                                  if (confirm("Are you sure you want to delete this document?")) {
                                    // Handle delete
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </AnimatedButton>
                            </div>
                          </AnimatedCardFooter>
                        </AnimatedCard>
                      ))}
                    </StaggeredList>
                  ) : (
                    <AnimatedCard>
                      <AnimatedCardContent className="flex flex-col items-center justify-center py-12">
                        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No Documents Found</h3>
                        <p className="text-sm text-muted-foreground mt-2 text-center max-w-md">
                          {searchQuery || selectedCategory !== "all" || selectedStatus !== "all"
                            ? "No documents match your current filters. Try adjusting your search criteria."
                            : "You haven't uploaded any documents yet. Click the Upload Document button to get started."}
                        </p>
                        {(searchQuery || selectedCategory !== "all" || selectedStatus !== "all") && (
                          <AnimatedButton 
                            variant="outline" 
                            className="mt-4"
                            onClick={() => {
                              setSearchQuery("");
                              setSelectedCategory("all");
                              setSelectedStatus("all");
                            }}
                          >
                            Clear Filters
                          </AnimatedButton>
                        )}
                        {!searchQuery && selectedCategory === "all" && selectedStatus === "all" && (
                          <AnimatedButton 
                            className="mt-4"
                            onClick={handleUpload}
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            Upload Document
                          </AnimatedButton>
                        )}
                      </AnimatedCardContent>
                    </AnimatedCard>
                  )}
                </TabsContent>
              </Tabs>
            </>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
