import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import DocumentManagementClient from "./document-management-client";

export const metadata = {
  title: "Document Management - Carbonix",
  description: "Manage your compliance documents and certifications",
};

export default async function DocumentManagementPage() {
  const session = await auth();
  
  if (!session?.user) {
    redirect("/login");
  }
  
  const userName = session.user.name || "User";
  
  return <DocumentManagementClient userName={userName} />;
}
