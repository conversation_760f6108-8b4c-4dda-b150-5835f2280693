import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import AmlMonitoringClient from "./aml-monitoring-client";

export const metadata = {
  title: "AML Monitoring - Carbonix",
  description: "Monitor and manage your Anti-Money Laundering (AML) compliance",
};

export default async function AmlMonitoringPage() {
  const session = await auth();
  
  if (!session?.user) {
    redirect("/login");
  }
  
  const userName = session.user.name || "User";
  
  return <AmlMonitoringClient userName={userName} />;
}
