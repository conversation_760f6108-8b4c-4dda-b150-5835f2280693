"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON><PERSON>, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Loader2, 
  AlertTriangle,
  AlertCircle,
  BarChart4,
  Clock,
  CheckCircle,
  XCircle,
  ArrowRight,
  Bell,
  FileText,
  Search,
  Filter
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ComplianceRiskLevel, ComplianceStatus } from "@/lib/compliance";

interface AmlMonitoringClientProps {
  userName: string;
}

interface AmlStatusData {
  status: ComplianceStatus;
  riskLevel: ComplianceRiskLevel;
  riskScore: number;
  lastChecked: string;
  expiresAt: string;
  alerts: {
    id: string;
    type: string;
    description: string;
    severity: ComplianceRiskLevel;
    date: string;
    status: string;
  }[];
  transactions: {
    id: string;
    date: string;
    type: string;
    amount: number;
    counterparty: string;
    riskScore: number;
    status: string;
  }[];
}

export default function AmlMonitoringClient({ userName }: AmlMonitoringClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [amlStatus, setAmlStatus] = useState<AmlStatusData | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    async function fetchAmlStatus() {
      try {
        setIsLoading(true);
        
        // Fetch AML status
        const response = await fetch('/api/compliance/aml/status');
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch AML status");
        }
        
        const data = await response.json();
        setAmlStatus(data);
      } catch (error) {
        console.error("Error fetching AML status:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchAmlStatus();
  }, []);

  // Mock data for development
  const mockAmlStatus: AmlStatusData = {
    status: ComplianceStatus.APPROVED,
    riskLevel: ComplianceRiskLevel.LOW,
    riskScore: 15,
    lastChecked: "2023-06-10",
    expiresAt: "2024-06-10",
    alerts: [
      {
        id: "alert-1",
        type: "TRANSACTION",
        description: "Unusual transaction pattern detected",
        severity: ComplianceRiskLevel.MEDIUM,
        date: "2023-06-05",
        status: "RESOLVED"
      },
      {
        id: "alert-2",
        type: "WALLET",
        description: "Connection to high-risk wallet detected",
        severity: ComplianceRiskLevel.HIGH,
        date: "2023-05-28",
        status: "INVESTIGATING"
      },
      {
        id: "alert-3",
        type: "GEOGRAPHIC",
        description: "Transaction with high-risk jurisdiction",
        severity: ComplianceRiskLevel.LOW,
        date: "2023-05-15",
        status: "RESOLVED"
      }
    ],
    transactions: [
      {
        id: "tx-1",
        date: "2023-06-08",
        type: "PURCHASE",
        amount: 5000,
        counterparty: "Green Earth Co.",
        riskScore: 10,
        status: "COMPLETED"
      },
      {
        id: "tx-2",
        date: "2023-05-25",
        type: "SALE",
        amount: 3000,
        counterparty: "Carbon Solutions Inc.",
        riskScore: 25,
        status: "COMPLETED"
      },
      {
        id: "tx-3",
        date: "2023-05-12",
        type: "PURCHASE",
        amount: 8000,
        counterparty: "EcoTech Ltd.",
        riskScore: 5,
        status: "COMPLETED"
      },
      {
        id: "tx-4",
        date: "2023-04-30",
        type: "SALE",
        amount: 12000,
        counterparty: "Climate Innovations",
        riskScore: 15,
        status: "COMPLETED"
      },
      {
        id: "tx-5",
        date: "2023-04-15",
        type: "PURCHASE",
        amount: 4500,
        counterparty: "Green Planet Corp.",
        riskScore: 8,
        status: "COMPLETED"
      }
    ]
  };

  const status = amlStatus || mockAmlStatus;

  const getRiskBadge = (riskLevel: ComplianceRiskLevel) => {
    switch (riskLevel) {
      case ComplianceRiskLevel.LOW:
        return <Badge variant="success">Low Risk</Badge>;
      case ComplianceRiskLevel.MEDIUM:
        return <Badge variant="warning">Medium Risk</Badge>;
      case ComplianceRiskLevel.HIGH:
        return <Badge variant="destructive">High Risk</Badge>;
      case ComplianceRiskLevel.CRITICAL:
        return <Badge variant="destructive">Critical Risk</Badge>;
      default:
        return <Badge variant="secondary">{riskLevel}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case 'APPROVED':
      case 'RESOLVED':
      case 'COMPLETED':
        return <Badge variant="success" className="ml-2">Resolved</Badge>;
      case 'PENDING':
      case 'INVESTIGATING':
      case 'IN_REVIEW':
        return <Badge variant="warning" className="ml-2">Investigating</Badge>;
      case 'REJECTED':
      case 'FLAGGED':
        return <Badge variant="destructive" className="ml-2">Flagged</Badge>;
      default:
        return <Badge variant="secondary" className="ml-2">{status}</Badge>;
    }
  };

  const getAlertIcon = (severity: ComplianceRiskLevel) => {
    switch (severity) {
      case ComplianceRiskLevel.LOW:
        return <AlertCircle className="h-5 w-5 text-green-600" />;
      case ComplianceRiskLevel.MEDIUM:
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case ComplianceRiskLevel.HIGH:
      case ComplianceRiskLevel.CRITICAL:
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTransactionRiskColor = (score: number) => {
    if (score <= 20) {
      return "text-green-600";
    } else if (score <= 50) {
      return "text-yellow-600";
    } else if (score <= 80) {
      return "text-orange-600";
    } else {
      return "text-red-600";
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="AML Monitoring"
              description="Monitor and manage your Anti-Money Laundering (AML) compliance"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "AML Monitoring", href: "/compliance/aml", isCurrent: true }
              ]}
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading AML monitoring data...</span>
            </div>
          ) : error ? (
            <AnimatedCard className="border-destructive/50 bg-destructive/10">
              <AnimatedCardContent className="flex items-center py-6">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <p className="ml-2 text-destructive">{error}</p>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="w-full">
                <TabsTrigger value="overview" className="flex items-center">
                  <BarChart4 className="mr-2 h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="alerts" className="flex items-center">
                  <Bell className="mr-2 h-4 w-4" />
                  Alerts
                </TabsTrigger>
                <TabsTrigger value="transactions" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Transactions
                </TabsTrigger>
                <TabsTrigger value="reports" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Reports
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-6">
                <div className="grid gap-4 md:grid-cols-3">
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        Risk Level
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">{status.riskScore}/100</div>
                        {getRiskBadge(status.riskLevel)}
                      </div>
                      <Progress 
                        value={status.riskScore} 
                        className="h-2 mt-2"
                        color={
                          status.riskLevel === ComplianceRiskLevel.LOW ? "bg-green-600" :
                          status.riskLevel === ComplianceRiskLevel.MEDIUM ? "bg-yellow-600" :
                          "bg-red-600"
                        }
                      />
                      <p className="text-xs text-muted-foreground mt-2">
                        Last checked: {status.lastChecked}
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        AML Status
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-medium">{status.status}</div>
                        {getStatusBadge(status.status)}
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Expires: {status.expiresAt}
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        Active Alerts
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="text-2xl font-bold">
                        {status.alerts.filter(a => a.status.toUpperCase() !== 'RESOLVED').length}
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        {status.alerts.length} total alerts
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </div>
                
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Recent AML Alerts</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Recent alerts from AML monitoring
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-4">
                      {status.alerts.length > 0 ? (
                        <StaggeredList className="space-y-4">
                          {status.alerts.slice(0, 3).map((alert) => (
                            <div key={alert.id} className="flex items-start space-x-4">
                              <div className="bg-muted p-2 rounded-full">
                                {getAlertIcon(alert.severity)}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <p className="font-medium">{alert.description}</p>
                                  {getStatusBadge(alert.status)}
                                </div>
                                <div className="flex items-center justify-between mt-1">
                                  <p className="text-xs text-muted-foreground">{alert.date}</p>
                                  {getRiskBadge(alert.severity)}
                                </div>
                              </div>
                            </div>
                          ))}
                        </StaggeredList>
                      ) : (
                        <div className="text-center py-4">
                          <CheckCircle className="h-8 w-8 mx-auto text-green-600 mb-2" />
                          <p className="text-sm font-medium">No alerts found</p>
                          <p className="text-xs text-muted-foreground">
                            Your account has no active AML alerts
                          </p>
                        </div>
                      )}
                    </div>
                  </AnimatedCardContent>
                  <AnimatedCardFooter>
                    <AnimatedButton 
                      variant="ghost" 
                      className="w-full" 
                      onClick={() => setActiveTab("alerts")}
                    >
                      View All Alerts
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </AnimatedButton>
                  </AnimatedCardFooter>
                </AnimatedCard>
                
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Recent Transactions</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Recent transactions with AML risk assessment
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Counterparty</TableHead>
                          <TableHead>Risk Score</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {status.transactions.slice(0, 3).map((tx) => (
                          <TableRow key={tx.id}>
                            <TableCell>{tx.date}</TableCell>
                            <TableCell>{tx.type}</TableCell>
                            <TableCell>${tx.amount.toLocaleString()}</TableCell>
                            <TableCell>{tx.counterparty}</TableCell>
                            <TableCell>
                              <span className={getTransactionRiskColor(tx.riskScore)}>
                                {tx.riskScore}/100
                              </span>
                            </TableCell>
                            <TableCell>{getStatusBadge(tx.status)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </AnimatedCardContent>
                  <AnimatedCardFooter>
                    <AnimatedButton 
                      variant="ghost" 
                      className="w-full" 
                      onClick={() => setActiveTab("transactions")}
                    >
                      View All Transactions
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </AnimatedButton>
                  </AnimatedCardFooter>
                </AnimatedCard>
              </TabsContent>
              
              <TabsContent value="alerts" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <AnimatedCardTitle>AML Alerts</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          All alerts from AML monitoring
                        </AnimatedCardDescription>
                      </div>
                      <div className="flex space-x-2 mt-4 sm:mt-0">
                        <AnimatedButton variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </AnimatedButton>
                        <AnimatedButton variant="outline" size="sm">
                          <Search className="h-4 w-4 mr-2" />
                          Search
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      {status.alerts.length > 0 ? (
                        <StaggeredList className="divide-y">
                          {status.alerts.map((alert) => (
                            <div key={alert.id} className="py-4 flex items-start space-x-4">
                              <div className="bg-muted p-2 rounded-full">
                                {getAlertIcon(alert.severity)}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <p className="font-medium">{alert.description}</p>
                                  {getStatusBadge(alert.status)}
                                </div>
                                <div className="flex items-center justify-between mt-1">
                                  <p className="text-xs text-muted-foreground">{alert.date}</p>
                                  {getRiskBadge(alert.severity)}
                                </div>
                                <p className="text-sm text-muted-foreground mt-2">
                                  Alert ID: {alert.id} • Type: {alert.type}
                                </p>
                              </div>
                              <div>
                                <AnimatedButton 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => router.push(`/compliance/aml/alerts/${alert.id}`)}
                                >
                                  Details
                                </AnimatedButton>
                              </div>
                            </div>
                          ))}
                        </StaggeredList>
                      ) : (
                        <div className="text-center py-8">
                          <CheckCircle className="h-12 w-12 mx-auto text-green-600 mb-4" />
                          <h3 className="text-lg font-medium">No Alerts Found</h3>
                          <p className="text-sm text-muted-foreground mt-2">
                            Your account has no AML alerts
                          </p>
                        </div>
                      )}
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
              
              <TabsContent value="transactions" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <AnimatedCardTitle>Transaction Monitoring</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          AML risk assessment for all transactions
                        </AnimatedCardDescription>
                      </div>
                      <div className="flex space-x-2 mt-4 sm:mt-0">
                        <AnimatedButton variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </AnimatedButton>
                        <AnimatedButton variant="outline" size="sm">
                          <Search className="h-4 w-4 mr-2" />
                          Search
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Counterparty</TableHead>
                          <TableHead>Risk Score</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {status.transactions.map((tx) => (
                          <TableRow key={tx.id}>
                            <TableCell>{tx.date}</TableCell>
                            <TableCell>{tx.type}</TableCell>
                            <TableCell>${tx.amount.toLocaleString()}</TableCell>
                            <TableCell>{tx.counterparty}</TableCell>
                            <TableCell>
                              <span className={getTransactionRiskColor(tx.riskScore)}>
                                {tx.riskScore}/100
                              </span>
                            </TableCell>
                            <TableCell>{getStatusBadge(tx.status)}</TableCell>
                            <TableCell>
                              <AnimatedButton 
                                variant="ghost" 
                                size="sm"
                                onClick={() => router.push(`/compliance/aml/transactions/${tx.id}`)}
                              >
                                Details
                              </AnimatedButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
              
              <TabsContent value="reports" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>AML Reports</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Generate and view AML compliance reports
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/reports/aml/summary")}>
                          <AnimatedCardContent className="p-6">
                            <div className="flex items-center space-x-4">
                              <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                                <FileText className="h-6 w-6 text-blue-700" />
                              </div>
                              <div>
                                <h3 className="font-medium">AML Summary Report</h3>
                                <p className="text-sm text-muted-foreground">Overview of AML compliance status</p>
                              </div>
                            </div>
                          </AnimatedCardContent>
                        </AnimatedCard>
                        
                        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/reports/aml/transactions")}>
                          <AnimatedCardContent className="p-6">
                            <div className="flex items-center space-x-4">
                              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                                <FileText className="h-6 w-6 text-green-700" />
                              </div>
                              <div>
                                <h3 className="font-medium">Transaction Report</h3>
                                <p className="text-sm text-muted-foreground">Detailed transaction analysis</p>
                              </div>
                            </div>
                          </AnimatedCardContent>
                        </AnimatedCard>
                        
                        <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/reports/aml/alerts")}>
                          <AnimatedCardContent className="p-6">
                            <div className="flex items-center space-x-4">
                              <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                                <Bell className="h-6 w-6 text-yellow-700" />
                              </div>
                              <div>
                                <h3 className="font-medium">Alert Report</h3>
                                <p className="text-sm text-muted-foreground">Summary of AML alerts</p>
                              </div>
                            </div>
                          </AnimatedCardContent>
                        </AnimatedCard>
                      </div>
                      
                      <Separator />
                      
                      <div className="flex justify-end">
                        <AnimatedButton 
                          onClick={() => router.push("/compliance/reports")}
                        >
                          View All Reports
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
