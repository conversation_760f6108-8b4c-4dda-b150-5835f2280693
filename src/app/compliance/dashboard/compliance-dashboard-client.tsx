"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  Animated<PERSON><PERSON>,
  Animated<PERSON>ardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import {
  Loader2,
  AlertTriangle,
  Shield,
  FileText,
  AlertCircle,
  FileCheck,
  BarChart4,
  Clock,
  CheckCircle,
  XCircle,
  ArrowRight,
  DollarSign,
  FileSearch
} from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";

interface ComplianceDashboardClientProps {
  userName: string;
}

interface ComplianceStatus {
  kycStatus: string;
  kycLevel: string;
  kycLastUpdated: string;
  kycExpiryDate: string;
  amlStatus: string;
  amlLastChecked: string;
  amlRiskScore: number;
  documentsSubmitted: number;
  documentsVerified: number;
  documentsRejected: number;
  documentsExpiring: number;
  carbonCreditsVerified: number;
  carbonCreditsInReview: number;
  carbonCreditsRejected: number;
  complianceScore: number;
}

interface ComplianceActivity {
  id: string;
  type: string;
  description: string;
  date: string;
  status: string;
}

export default function ComplianceDashboardClient({ userName }: ComplianceDashboardClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [complianceStatus, setComplianceStatus] = useState<ComplianceStatus | null>(null);
  const [recentActivities, setRecentActivities] = useState<ComplianceActivity[]>([]);

  useEffect(() => {
    async function fetchComplianceData() {
      try {
        setIsLoading(true);

        // Fetch compliance status
        const statusResponse = await fetch('/api/compliance/status');

        if (!statusResponse.ok) {
          const errorData = await statusResponse.json();
          throw new Error(errorData.error || "Failed to fetch compliance status");
        }

        const statusData = await statusResponse.json();
        setComplianceStatus(statusData);

        // Fetch recent activities
        const activitiesResponse = await fetch('/api/compliance/activities');

        if (!activitiesResponse.ok) {
          const errorData = await activitiesResponse.json();
          throw new Error(errorData.error || "Failed to fetch compliance activities");
        }

        const activitiesData = await activitiesResponse.json();
        setRecentActivities(activitiesData.activities);
      } catch (error) {
        console.error("Error fetching compliance data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchComplianceData();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case 'VERIFIED':
      case 'APPROVED':
      case 'COMPLETED':
        return <Badge variant="success" className="ml-2">Verified</Badge>;
      case 'PENDING':
      case 'IN_REVIEW':
        return <Badge variant="warning" className="ml-2">Pending</Badge>;
      case 'REJECTED':
      case 'FAILED':
        return <Badge variant="destructive" className="ml-2">Rejected</Badge>;
      case 'EXPIRED':
        return <Badge variant="outline" className="ml-2">Expired</Badge>;
      default:
        return <Badge variant="secondary" className="ml-2">{status}</Badge>;
    }
  };

  const getRiskBadge = (score: number) => {
    if (score <= 25) {
      return <Badge variant="success">Low Risk</Badge>;
    } else if (score <= 50) {
      return <Badge variant="warning">Medium Risk</Badge>;
    } else if (score <= 75) {
      return <Badge variant="destructive">High Risk</Badge>;
    } else {
      return <Badge variant="destructive">Critical Risk</Badge>;
    }
  };

  // Mock data for development
  const mockComplianceStatus: ComplianceStatus = {
    kycStatus: "VERIFIED",
    kycLevel: "Advanced",
    kycLastUpdated: "2023-05-15",
    kycExpiryDate: "2024-05-15",
    amlStatus: "APPROVED",
    amlLastChecked: "2023-06-10",
    amlRiskScore: 15,
    documentsSubmitted: 8,
    documentsVerified: 7,
    documentsRejected: 1,
    documentsExpiring: 2,
    carbonCreditsVerified: 12,
    carbonCreditsInReview: 3,
    carbonCreditsRejected: 1,
    complianceScore: 85
  };

  const mockRecentActivities: ComplianceActivity[] = [
    { id: "ACT-001", type: "KYC", description: "KYC verification approved", date: "2023-06-15", status: "APPROVED" },
    { id: "ACT-002", type: "DOCUMENT", description: "Business registration document uploaded", date: "2023-06-14", status: "VERIFIED" },
    { id: "ACT-003", type: "AML", description: "AML check completed", date: "2023-06-10", status: "APPROVED" },
    { id: "ACT-004", type: "CARBON", description: "Carbon credit verification submitted", date: "2023-06-08", status: "IN_REVIEW" },
    { id: "ACT-005", type: "DOCUMENT", description: "Tax certificate uploaded", date: "2023-06-05", status: "VERIFIED" },
    { id: "ACT-006", type: "CARBON", description: "Carbon credit verification approved", date: "2023-06-01", status: "APPROVED" },
    { id: "ACT-007", type: "DOCUMENT", description: "Identity proof document rejected", date: "2023-05-28", status: "REJECTED" },
    { id: "ACT-008", type: "DOCUMENT", description: "Updated identity proof document uploaded", date: "2023-05-25", status: "VERIFIED" }
  ];

  const status = complianceStatus || mockComplianceStatus;
  const activities = recentActivities.length > 0 ? recentActivities : mockRecentActivities;

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'KYC':
        return <Shield className="h-5 w-5 text-blue-600" />;
      case 'DOCUMENT':
        return <FileText className="h-5 w-5 text-purple-600" />;
      case 'AML':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'CARBON':
        return <FileCheck className="h-5 w-5 text-green-600" />;
      default:
        return <FileSearch className="h-5 w-5 text-gray-600" />;
    }
  };

  const getActivityStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case 'APPROVED':
      case 'VERIFIED':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'REJECTED':
      case 'FAILED':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'IN_REVIEW':
      case 'PENDING':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Compliance Dashboard"
              description="Monitor and manage your compliance requirements"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "Dashboard", href: "/compliance/dashboard", isCurrent: true }
              ]}
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading compliance data...</span>
            </div>
          ) : error ? (
            <AnimatedCard className="border-destructive/50 bg-destructive/10">
              <AnimatedCardContent className="flex items-center py-6">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <p className="ml-2 text-destructive">{error}</p>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="w-full">
                <TabsTrigger value="overview" className="flex items-center">
                  <BarChart4 className="mr-2 h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="kyc" className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  KYC Verification
                </TabsTrigger>
                <TabsTrigger value="aml" className="flex items-center">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  AML Monitoring
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Documents
                </TabsTrigger>
                <TabsTrigger value="carbon" className="flex items-center">
                  <FileCheck className="mr-2 h-4 w-4" />
                  Carbon Verification
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        Compliance Score
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex flex-col space-y-2">
                        <div className="text-2xl font-bold">{status.complianceScore}%</div>
                        <Progress value={status.complianceScore} className="h-2" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/risk-assessment")}>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        Risk Assessment
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
                          <span className="font-medium">
                            {status.amlRiskScore < 30 ? "Low" : status.amlRiskScore < 70 ? "Medium" : "High"} Risk
                          </span>
                        </div>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        KYC Status
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-medium">{status.kycLevel}</div>
                        {getStatusBadge(status.kycStatus)}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Last updated: {status.kycLastUpdated}
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        AML Risk Score
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-medium">{status.amlRiskScore}/100</div>
                        {getRiskBadge(status.amlRiskScore)}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Last checked: {status.amlLastChecked}
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium">
                        Document Status
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-medium">{status.documentsVerified}/{status.documentsSubmitted} Verified</div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {status.documentsExpiring} document(s) expiring soon
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </div>

                <AnimatedCard className="mb-6">
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Quick Access</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Quickly access compliance features
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/kyc/verification")}>
                        <AnimatedCardContent className="p-4">
                          <div className="flex flex-col items-center text-center space-y-2">
                            <Shield className="h-8 w-8 text-primary" />
                            <span className="font-medium">KYC Verification</span>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>

                      <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/risk-assessment")}>
                        <AnimatedCardContent className="p-4">
                          <div className="flex flex-col items-center text-center space-y-2">
                            <AlertTriangle className="h-8 w-8 text-yellow-500" />
                            <span className="font-medium">Risk Assessment</span>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>

                      <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/documents")}>
                        <AnimatedCardContent className="p-4">
                          <div className="flex flex-col items-center text-center space-y-2">
                            <FileText className="h-8 w-8 text-blue-500" />
                            <span className="font-medium">Documents</span>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>

                      <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/carbon-verification")}>
                        <AnimatedCardContent className="p-4">
                          <div className="flex flex-col items-center text-center space-y-2">
                            <FileCheck className="h-8 w-8 text-green-500" />
                            <span className="font-medium">Carbon Verification</span>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <div className="grid gap-6 md:grid-cols-2">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Recent Compliance Activities</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Recent compliance-related activities and events
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="space-y-4">
                        {activities.slice(0, 5).map((activity) => (
                          <div key={activity.id} className="flex items-start space-x-4">
                            <div className="bg-muted p-2 rounded-full">
                              {getActivityIcon(activity.type)}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <p className="text-sm font-medium">{activity.description}</p>
                                {getActivityStatusIcon(activity.status)}
                              </div>
                              <p className="text-xs text-muted-foreground">{activity.date}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </AnimatedCardContent>
                    <AnimatedCardFooter>
                      <AnimatedButton
                        variant="ghost"
                        className="w-full"
                        onClick={() => router.push("/compliance/activities")}
                      >
                        View All Activities
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </AnimatedButton>
                    </AnimatedCardFooter>
                  </AnimatedCard>

                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Compliance Requirements</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Required compliance actions and status
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Shield className="h-5 w-5 text-blue-600 mr-2" />
                            <span className="text-sm font-medium">KYC Verification</span>
                          </div>
                          {getStatusBadge(status.kycStatus)}
                        </div>

                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                            <span className="text-sm font-medium">AML Screening</span>
                          </div>
                          {getStatusBadge(status.amlStatus)}
                        </div>

                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 text-purple-600 mr-2" />
                            <span className="text-sm font-medium">Required Documents</span>
                          </div>
                          <Badge variant={status.documentsVerified === status.documentsSubmitted ? "success" : "warning"} className="ml-2">
                            {status.documentsVerified}/{status.documentsSubmitted}
                          </Badge>
                        </div>

                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <DollarSign className="h-5 w-5 text-green-600 mr-2" />
                            <span className="text-sm font-medium">Tax Information</span>
                          </div>
                          <Badge variant="success" className="ml-2">Completed</Badge>
                        </div>
                      </div>
                    </AnimatedCardContent>
                    <AnimatedCardFooter>
                      <AnimatedButton
                        variant="ghost"
                        className="w-full"
                        onClick={() => router.push("/compliance/requirements")}
                      >
                        View All Requirements
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </AnimatedButton>
                    </AnimatedCardFooter>
                  </AnimatedCard>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/kyc/verification")}>
                    <AnimatedCardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                          <Shield className="h-6 w-6 text-blue-700" />
                        </div>
                        <div>
                          <h3 className="font-medium">KYC Verification</h3>
                          <p className="text-sm text-muted-foreground">Manage your KYC verification</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/aml")}>
                    <AnimatedCardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                          <AlertCircle className="h-6 w-6 text-yellow-700" />
                        </div>
                        <div>
                          <h3 className="font-medium">AML Monitoring</h3>
                          <p className="text-sm text-muted-foreground">View AML monitoring status</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/compliance/documents")}>
                    <AnimatedCardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                          <FileText className="h-6 w-6 text-purple-700" />
                        </div>
                        <div>
                          <h3 className="font-medium">Document Management</h3>
                          <p className="text-sm text-muted-foreground">Manage compliance documents</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </div>
              </TabsContent>

              <TabsContent value="kyc" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>KYC Verification Status</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Current status of your Know Your Customer (KYC) verification
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">Status</p>
                          <div className="flex items-center">
                            <span className="font-medium">{status.kycStatus}</span>
                            {getStatusBadge(status.kycStatus)}
                          </div>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Verification Level</p>
                          <Badge variant="secondary">{status.kycLevel}</Badge>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Last Updated</p>
                          <span className="text-sm">{status.kycLastUpdated}</span>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Expiry Date</p>
                          <span className="text-sm">{status.kycExpiryDate}</span>
                        </div>
                      </div>

                      <Separator />

                      <div className="flex justify-end space-x-4">
                        <AnimatedButton
                          variant="outline"
                          onClick={() => router.push("/compliance/kyc/verification")}
                        >
                          View Details
                        </AnimatedButton>
                        <AnimatedButton
                          onClick={() => router.push("/compliance/kyc/verification")}
                        >
                          Update KYC
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="aml" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>AML Monitoring Status</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Current status of your Anti-Money Laundering (AML) monitoring
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">Status</p>
                          <div className="flex items-center">
                            <span className="font-medium">{status.amlStatus}</span>
                            {getStatusBadge(status.amlStatus)}
                          </div>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Risk Score</p>
                          <div className="flex items-center">
                            <span className="font-medium">{status.amlRiskScore}/100</span>
                            <span className="ml-2">{getRiskBadge(status.amlRiskScore)}</span>
                          </div>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Last Checked</p>
                          <span className="text-sm">{status.amlLastChecked}</span>
                        </div>
                      </div>

                      <Separator />

                      <div className="flex justify-end space-x-4">
                        <AnimatedButton
                          variant="outline"
                          onClick={() => router.push("/compliance/aml")}
                        >
                          View Details
                        </AnimatedButton>
                        <AnimatedButton
                          onClick={() => router.push("/compliance/aml")}
                        >
                          View AML Reports
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="documents" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Document Management</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Manage your compliance documents
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="grid gap-4 md:grid-cols-3">
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Submitted Documents</p>
                          <div className="text-2xl font-bold">{status.documentsSubmitted}</div>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Verified Documents</p>
                          <div className="text-2xl font-bold">{status.documentsVerified}</div>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Expiring Soon</p>
                          <div className="text-2xl font-bold">{status.documentsExpiring}</div>
                        </div>
                      </div>

                      <Separator />

                      <div className="flex justify-end space-x-4">
                        <AnimatedButton
                          variant="outline"
                          onClick={() => router.push("/compliance/documents")}
                        >
                          View All Documents
                        </AnimatedButton>
                        <AnimatedButton
                          onClick={() => router.push("/compliance/documents/upload")}
                        >
                          Upload Document
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="carbon" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Carbon Credit Verification</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Status of your carbon credit verification
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="grid gap-4 md:grid-cols-3">
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Verified Credits</p>
                          <div className="text-2xl font-bold">{status.carbonCreditsVerified}</div>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">In Review</p>
                          <div className="text-2xl font-bold">{status.carbonCreditsInReview}</div>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Rejected</p>
                          <div className="text-2xl font-bold">{status.carbonCreditsRejected}</div>
                        </div>
                      </div>

                      <Separator />

                      <div className="flex justify-end space-x-4">
                        <AnimatedButton
                          variant="outline"
                          onClick={() => router.push("/compliance/carbon-verification")}
                        >
                          View All Verifications
                        </AnimatedButton>
                        <AnimatedButton
                          onClick={() => router.push("/compliance/carbon-verification")}
                        >
                          Submit for Verification
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
