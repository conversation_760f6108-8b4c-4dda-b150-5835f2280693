import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageTransition } from "@/components/ui/animated";
import RiskAssessmentDashboard from "@/components/compliance/risk-assessment-dashboard";

export default async function RiskAssessmentPage() {
  const session = await auth();
  
  if (!session?.user) {
    redirect("/auth/signin?callbackUrl=/compliance/risk-assessment");
  }
  
  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <PageHeaderWithBreadcrumb
            title="Risk Assessment"
            description="Monitor and manage compliance risk for your organization"
            breadcrumbItems={[
              { label: "Compliance", href: "/compliance" },
              { label: "Risk Assessment", href: "/compliance/risk-assessment", isCurrent: true }
            ]}
          />
          
          <div className="mt-6 space-y-6">
            <RiskAssessmentDashboard organizationId={session.user.organizationId} />
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
