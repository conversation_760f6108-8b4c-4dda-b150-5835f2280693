"use client";

import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/animations/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { AuditTrail } from "@/components/compliance/audit-trail";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Shield, FileText, AlertCircle, Clock } from "lucide-react";

export default function ComplianceAuditTrailPage() {
  const { data: session } = useSession();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Audit Trail"
              description="Comprehensive record of all compliance and system activities"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "Audit Trail", href: "/compliance/audit-trail", isCurrent: true }
              ]}
            />
          </div>

          <Tabs defaultValue="all-logs">
            <TabsList className="w-full md:w-auto">
              <TabsTrigger value="all-logs" className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                All Logs
              </TabsTrigger>
              <TabsTrigger value="kyc-logs" className="flex items-center">
                <Shield className="mr-2 h-4 w-4" />
                KYC Logs
              </TabsTrigger>
              <TabsTrigger value="aml-logs" className="flex items-center">
                <AlertCircle className="mr-2 h-4 w-4" />
                AML Logs
              </TabsTrigger>
              <TabsTrigger value="verification-logs" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Verification Logs
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all-logs" className="mt-6">
              <AuditTrail
                userId={session?.user?.id}
                organizationId={session?.user?.organizationId}
              />
            </TabsContent>

            <TabsContent value="kyc-logs" className="mt-6">
              <AuditTrail
                userId={session?.user?.id}
                organizationId={session?.user?.organizationId}
                selectedComponent="KYC"
              />
            </TabsContent>

            <TabsContent value="aml-logs" className="mt-6">
              <AuditTrail
                userId={session?.user?.id}
                organizationId={session?.user?.organizationId}
                selectedComponent="AML"
              />
            </TabsContent>

            <TabsContent value="verification-logs" className="mt-6">
              <AuditTrail
                userId={session?.user?.id}
                organizationId={session?.user?.organizationId}
                selectedComponent="VERIFICATION"
              />
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
