"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/ui/animated";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardDescription, 
  AnimatedCardFooter, 
  AnimatedCardHeader, 
  AnimatedCardT<PERSON>le,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  FileCheck, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  FileText,
  Upload,
  Search,
  Filter
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CarbonVerificationClientProps {
  userName: string;
}

export default function CarbonVerificationClient({ userName }: CarbonVerificationClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock carbon credit verification data
  const verifications = [
    {
      id: "1",
      carbonCredit: {
        id: "cc1",
        name: "Rainforest Protection Project - 2023",
        standard: "Verra",
        vintage: 2023,
        quantity: 1000,
      },
      status: "PENDING",
      submittedAt: "2023-06-15",
      documents: [
        { id: "d1", type: "PROJECT_DESCRIPTION", name: "Project_Description.pdf" },
        { id: "d2", type: "METHODOLOGY", name: "Methodology.pdf" },
        { id: "d3", type: "VERIFICATION_REPORT", name: "Verification_Report.pdf" },
      ],
    },
    {
      id: "2",
      carbonCredit: {
        id: "cc2",
        name: "Wind Energy Project - 2022",
        standard: "Gold Standard",
        vintage: 2022,
        quantity: 500,
      },
      status: "APPROVED",
      submittedAt: "2023-05-20",
      approvedAt: "2023-05-25",
      documents: [
        { id: "d4", type: "PROJECT_DESCRIPTION", name: "Project_Description.pdf" },
        { id: "d5", type: "METHODOLOGY", name: "Methodology.pdf" },
        { id: "d6", type: "VERIFICATION_REPORT", name: "Verification_Report.pdf" },
        { id: "d7", type: "VALIDATION_REPORT", name: "Validation_Report.pdf" },
      ],
    },
    {
      id: "3",
      carbonCredit: {
        id: "cc3",
        name: "Solar Farm Project - 2023",
        standard: "Verra",
        vintage: 2023,
        quantity: 750,
      },
      status: "REJECTED",
      submittedAt: "2023-04-10",
      rejectedAt: "2023-04-15",
      rejectionReason: "Incomplete documentation. Missing validation report.",
      documents: [
        { id: "d8", type: "PROJECT_DESCRIPTION", name: "Project_Description.pdf" },
        { id: "d9", type: "METHODOLOGY", name: "Methodology.pdf" },
      ],
    },
    {
      id: "4",
      carbonCredit: {
        id: "cc4",
        name: "Reforestation Project - 2022",
        standard: "Verra",
        vintage: 2022,
        quantity: 1200,
      },
      status: "APPROVED",
      submittedAt: "2023-03-05",
      approvedAt: "2023-03-10",
      documents: [
        { id: "d10", type: "PROJECT_DESCRIPTION", name: "Project_Description.pdf" },
        { id: "d11", type: "METHODOLOGY", name: "Methodology.pdf" },
        { id: "d12", type: "VERIFICATION_REPORT", name: "Verification_Report.pdf" },
        { id: "d13", type: "VALIDATION_REPORT", name: "Validation_Report.pdf" },
      ],
    },
  ];

  // Filter verifications based on search query and status filter
  const filteredVerifications = verifications.filter((verification) => {
    const matchesSearch = 
      verification.carbonCredit.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      verification.carbonCredit.standard.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = 
      statusFilter === "all" || 
      verification.status.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesStatus;
  });

  // Function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        );
      case "REQUIRES_UPDATE":
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 hover:bg-orange-100">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Requires Update
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            {status}
          </Badge>
        );
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Carbon Credit Verification"
              description="Manage verification of your carbon credits"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "Carbon Verification", href: "/compliance/carbon-verification", isCurrent: true }
              ]}
            />
          </div>

          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name or standard..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <div className="flex items-center">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Filter by status" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="requires_update">Requires Update</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <AnimatedButton 
              onClick={() => router.push("/compliance/carbon-verification/submit")}
            >
              <Upload className="mr-2 h-4 w-4" />
              Submit New
            </AnimatedButton>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full mb-4">
              <TabsTrigger value="all" className="flex items-center">
                <FileCheck className="mr-2 h-4 w-4" />
                All Verifications
              </TabsTrigger>
              <TabsTrigger value="pending" className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                Pending
              </TabsTrigger>
              <TabsTrigger value="approved" className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4" />
                Approved
              </TabsTrigger>
              <TabsTrigger value="rejected" className="flex items-center">
                <XCircle className="mr-2 h-4 w-4" />
                Rejected
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              {filteredVerifications.length > 0 ? (
                <StaggeredList>
                  {filteredVerifications.map((verification) => (
                    <AnimatedCard key={verification.id}>
                      <AnimatedCardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <AnimatedCardTitle>{verification.carbonCredit.name}</AnimatedCardTitle>
                            <AnimatedCardDescription>
                              {verification.carbonCredit.standard} • Vintage {verification.carbonCredit.vintage} • {verification.carbonCredit.quantity} credits
                            </AnimatedCardDescription>
                          </div>
                          {getStatusBadge(verification.status)}
                        </div>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="space-y-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Submitted</p>
                              <p className="text-sm">{verification.submittedAt}</p>
                            </div>
                            {verification.status === "APPROVED" && (
                              <div className="space-y-1 mt-2 sm:mt-0">
                                <p className="text-sm font-medium">Approved</p>
                                <p className="text-sm">{verification.approvedAt}</p>
                              </div>
                            )}
                            {verification.status === "REJECTED" && (
                              <div className="space-y-1 mt-2 sm:mt-0">
                                <p className="text-sm font-medium">Rejected</p>
                                <p className="text-sm">{verification.rejectedAt}</p>
                              </div>
                            )}
                            <div className="space-y-1 mt-2 sm:mt-0">
                              <p className="text-sm font-medium">Documents</p>
                              <p className="text-sm">{verification.documents.length} files</p>
                            </div>
                          </div>

                          {verification.status === "REJECTED" && verification.rejectionReason && (
                            <div className="bg-red-50 border-l-4 border-red-400 p-4">
                              <div className="flex">
                                <div className="flex-shrink-0">
                                  <XCircle className="h-5 w-5 text-red-400" />
                                </div>
                                <div className="ml-3">
                                  <p className="text-sm text-red-700">
                                    {verification.rejectionReason}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          <Separator />

                          <div>
                            <h3 className="text-sm font-medium mb-2">Submitted Documents</h3>
                            <div className="grid gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
                              {verification.documents.map((doc) => (
                                <div key={doc.id} className="flex items-center space-x-2 p-2 border rounded-md">
                                  <FileText className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-xs font-medium truncate">{doc.type}</p>
                                    <p className="text-xs text-muted-foreground truncate">{doc.name}</p>
                                  </div>
                                  <AnimatedButton 
                                    variant="ghost" 
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                  >
                                    <Search className="h-3 w-3" />
                                  </AnimatedButton>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </AnimatedCardContent>
                      <AnimatedCardFooter>
                        <AnimatedButton 
                          variant="outline" 
                          size="sm"
                          className="w-full"
                          onClick={() => router.push(`/compliance/carbon-verification/${verification.id}`)}
                        >
                          View Details
                        </AnimatedButton>
                      </AnimatedCardFooter>
                    </AnimatedCard>
                  ))}
                </StaggeredList>
              ) : (
                <AnimatedCard>
                  <AnimatedCardContent className="py-8">
                    <div className="text-center">
                      <FileCheck className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-2 text-lg font-medium">No verifications found</h3>
                      <p className="mt-1 text-sm text-muted-foreground">
                        {searchQuery || statusFilter !== "all" 
                          ? "Try adjusting your search or filters" 
                          : "Submit your first carbon credit for verification"}
                      </p>
                      <div className="mt-6">
                        <AnimatedButton 
                          onClick={() => router.push("/compliance/carbon-verification/submit")}
                        >
                          Submit for Verification
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              <StaggeredList>
                {filteredVerifications
                  .filter(v => v.status === "PENDING")
                  .map((verification) => (
                    <AnimatedCard key={verification.id}>
                      <AnimatedCardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <AnimatedCardTitle>{verification.carbonCredit.name}</AnimatedCardTitle>
                            <AnimatedCardDescription>
                              {verification.carbonCredit.standard} • Vintage {verification.carbonCredit.vintage} • {verification.carbonCredit.quantity} credits
                            </AnimatedCardDescription>
                          </div>
                          {getStatusBadge(verification.status)}
                        </div>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="space-y-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Submitted</p>
                              <p className="text-sm">{verification.submittedAt}</p>
                            </div>
                            <div className="space-y-1 mt-2 sm:mt-0">
                              <p className="text-sm font-medium">Documents</p>
                              <p className="text-sm">{verification.documents.length} files</p>
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <h3 className="text-sm font-medium mb-2">Submitted Documents</h3>
                            <div className="grid gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
                              {verification.documents.map((doc) => (
                                <div key={doc.id} className="flex items-center space-x-2 p-2 border rounded-md">
                                  <FileText className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-xs font-medium truncate">{doc.type}</p>
                                    <p className="text-xs text-muted-foreground truncate">{doc.name}</p>
                                  </div>
                                  <AnimatedButton 
                                    variant="ghost" 
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                  >
                                    <Search className="h-3 w-3" />
                                  </AnimatedButton>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </AnimatedCardContent>
                      <AnimatedCardFooter>
                        <AnimatedButton 
                          variant="outline" 
                          size="sm"
                          className="w-full"
                          onClick={() => router.push(`/compliance/carbon-verification/${verification.id}`)}
                        >
                          View Details
                        </AnimatedButton>
                      </AnimatedCardFooter>
                    </AnimatedCard>
                  ))}
              </StaggeredList>
              {filteredVerifications.filter(v => v.status === "PENDING").length === 0 && (
                <AnimatedCard>
                  <AnimatedCardContent className="py-8">
                    <div className="text-center">
                      <Clock className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-2 text-lg font-medium">No pending verifications</h3>
                      <p className="mt-1 text-sm text-muted-foreground">
                        All your carbon credits have been processed
                      </p>
                      <div className="mt-6">
                        <AnimatedButton 
                          onClick={() => router.push("/compliance/carbon-verification/submit")}
                        >
                          Submit New Verification
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
            </TabsContent>

            <TabsContent value="approved" className="space-y-4">
              <StaggeredList>
                {filteredVerifications
                  .filter(v => v.status === "APPROVED")
                  .map((verification) => (
                    <AnimatedCard key={verification.id}>
                      <AnimatedCardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <AnimatedCardTitle>{verification.carbonCredit.name}</AnimatedCardTitle>
                            <AnimatedCardDescription>
                              {verification.carbonCredit.standard} • Vintage {verification.carbonCredit.vintage} • {verification.carbonCredit.quantity} credits
                            </AnimatedCardDescription>
                          </div>
                          {getStatusBadge(verification.status)}
                        </div>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="space-y-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Submitted</p>
                              <p className="text-sm">{verification.submittedAt}</p>
                            </div>
                            <div className="space-y-1 mt-2 sm:mt-0">
                              <p className="text-sm font-medium">Approved</p>
                              <p className="text-sm">{verification.approvedAt}</p>
                            </div>
                            <div className="space-y-1 mt-2 sm:mt-0">
                              <p className="text-sm font-medium">Documents</p>
                              <p className="text-sm">{verification.documents.length} files</p>
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <h3 className="text-sm font-medium mb-2">Submitted Documents</h3>
                            <div className="grid gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
                              {verification.documents.map((doc) => (
                                <div key={doc.id} className="flex items-center space-x-2 p-2 border rounded-md">
                                  <FileText className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-xs font-medium truncate">{doc.type}</p>
                                    <p className="text-xs text-muted-foreground truncate">{doc.name}</p>
                                  </div>
                                  <AnimatedButton 
                                    variant="ghost" 
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                  >
                                    <Search className="h-3 w-3" />
                                  </AnimatedButton>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </AnimatedCardContent>
                      <AnimatedCardFooter className="flex justify-between">
                        <AnimatedButton 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/compliance/carbon-verification/${verification.id}`)}
                        >
                          View Details
                        </AnimatedButton>
                        <AnimatedButton 
                          size="sm"
                          onClick={() => router.push(`/carbon-credits/${verification.carbonCredit.id}/tokenize`)}
                        >
                          Tokenize Credit
                        </AnimatedButton>
                      </AnimatedCardFooter>
                    </AnimatedCard>
                  ))}
              </StaggeredList>
              {filteredVerifications.filter(v => v.status === "APPROVED").length === 0 && (
                <AnimatedCard>
                  <AnimatedCardContent className="py-8">
                    <div className="text-center">
                      <CheckCircle className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-2 text-lg font-medium">No approved verifications</h3>
                      <p className="mt-1 text-sm text-muted-foreground">
                        You don't have any approved carbon credit verifications yet
                      </p>
                      <div className="mt-6">
                        <AnimatedButton 
                          onClick={() => router.push("/compliance/carbon-verification/submit")}
                        >
                          Submit for Verification
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
            </TabsContent>

            <TabsContent value="rejected" className="space-y-4">
              <StaggeredList>
                {filteredVerifications
                  .filter(v => v.status === "REJECTED")
                  .map((verification) => (
                    <AnimatedCard key={verification.id}>
                      <AnimatedCardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <AnimatedCardTitle>{verification.carbonCredit.name}</AnimatedCardTitle>
                            <AnimatedCardDescription>
                              {verification.carbonCredit.standard} • Vintage {verification.carbonCredit.vintage} • {verification.carbonCredit.quantity} credits
                            </AnimatedCardDescription>
                          </div>
                          {getStatusBadge(verification.status)}
                        </div>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="space-y-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Submitted</p>
                              <p className="text-sm">{verification.submittedAt}</p>
                            </div>
                            <div className="space-y-1 mt-2 sm:mt-0">
                              <p className="text-sm font-medium">Rejected</p>
                              <p className="text-sm">{verification.rejectedAt}</p>
                            </div>
                            <div className="space-y-1 mt-2 sm:mt-0">
                              <p className="text-sm font-medium">Documents</p>
                              <p className="text-sm">{verification.documents.length} files</p>
                            </div>
                          </div>

                          {verification.rejectionReason && (
                            <div className="bg-red-50 border-l-4 border-red-400 p-4">
                              <div className="flex">
                                <div className="flex-shrink-0">
                                  <XCircle className="h-5 w-5 text-red-400" />
                                </div>
                                <div className="ml-3">
                                  <p className="text-sm text-red-700">
                                    {verification.rejectionReason}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          <Separator />

                          <div>
                            <h3 className="text-sm font-medium mb-2">Submitted Documents</h3>
                            <div className="grid gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
                              {verification.documents.map((doc) => (
                                <div key={doc.id} className="flex items-center space-x-2 p-2 border rounded-md">
                                  <FileText className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-xs font-medium truncate">{doc.type}</p>
                                    <p className="text-xs text-muted-foreground truncate">{doc.name}</p>
                                  </div>
                                  <AnimatedButton 
                                    variant="ghost" 
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                  >
                                    <Search className="h-3 w-3" />
                                  </AnimatedButton>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </AnimatedCardContent>
                      <AnimatedCardFooter className="flex justify-between">
                        <AnimatedButton 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/compliance/carbon-verification/${verification.id}`)}
                        >
                          View Details
                        </AnimatedButton>
                        <AnimatedButton 
                          size="sm"
                          onClick={() => router.push(`/compliance/carbon-verification/resubmit/${verification.id}`)}
                        >
                          Resubmit
                        </AnimatedButton>
                      </AnimatedCardFooter>
                    </AnimatedCard>
                  ))}
              </StaggeredList>
              {filteredVerifications.filter(v => v.status === "REJECTED").length === 0 && (
                <AnimatedCard>
                  <AnimatedCardContent className="py-8">
                    <div className="text-center">
                      <XCircle className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-2 text-lg font-medium">No rejected verifications</h3>
                      <p className="mt-1 text-sm text-muted-foreground">
                        You don't have any rejected carbon credit verifications
                      </p>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
