import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import CarbonVerificationClient from "./carbon-verification-client";

export const metadata = {
  title: "Carbon Verification - Carbonix",
  description: "Verify and manage your carbon credits",
};

export default async function CarbonVerificationPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const userName = session.user.name || "User";

  return <CarbonVerificationClient userName={userName} />;
}
