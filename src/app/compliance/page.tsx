"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/animations/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ComplianceDashboard } from "@/components/compliance/compliance-dashboard";
import { ReportManagement } from "@/components/compliance/report-management";
import { AuditTrail } from "@/components/compliance/audit-trail";
import {
  Shield,
  FileText,
  AlertCircle,
  FileCheck,
  BarChart4,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function CompliancePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Compliance"
              description="Manage compliance requirements and documentation"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance", isCurrent: true }
              ]}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full mb-4">
              <TabsTrigger value="dashboard" className="flex items-center">
                <BarChart4 className="mr-2 h-4 w-4" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="kyc" className="flex items-center">
                <Shield className="mr-2 h-4 w-4" />
                KYC Verification
              </TabsTrigger>
              <TabsTrigger value="aml" className="flex items-center">
                <AlertCircle className="mr-2 h-4 w-4" />
                AML Monitoring
              </TabsTrigger>
              <TabsTrigger value="carbon-verification" className="flex items-center">
                <FileCheck className="mr-2 h-4 w-4" />
                Carbon Verification
              </TabsTrigger>
              <TabsTrigger value="reports" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Reports
              </TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-4">
              <ComplianceDashboard userId={session?.user?.id} organizationId={session?.user?.organizationId} />
            </TabsContent>

            <TabsContent value="kyc" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>KYC Verification</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Manage your Know Your Customer (KYC) verification status
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <p className="text-sm">
                      KYC verification is required to comply with regulatory requirements and to ensure the security of our platform.
                    </p>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h3 className="text-sm font-medium mb-2">Current Status</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Verified
                          </span>
                          <span className="text-sm text-muted-foreground">Advanced Level</span>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-2">Verification Level</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Advanced
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter className="flex justify-between">
                  <AnimatedButton
                    variant="outline"
                    onClick={() => router.push("/compliance/kyc/status")}
                    animationVariant="buttonTap"
                  >
                    View Status
                  </AnimatedButton>
                  <AnimatedButton
                    onClick={() => router.push("/compliance/kyc/update")}
                    animationVariant="buttonTap"
                  >
                    Update KYC
                  </AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="aml" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>AML Monitoring</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Anti-Money Laundering monitoring and alerts
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <p className="text-sm">
                      AML monitoring helps prevent financial crimes and ensures compliance with regulatory requirements.
                    </p>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h3 className="text-sm font-medium mb-2">Current Status</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Approved
                          </span>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-2">Risk Level</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <AlertTriangle className="mr-1 h-3 w-3" />
                            Medium
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter className="flex justify-between">
                  <AnimatedButton
                    variant="outline"
                    onClick={() => router.push("/compliance/aml/checks")}
                    animationVariant="buttonTap"
                  >
                    View Checks
                  </AnimatedButton>
                  <AnimatedButton
                    onClick={() => router.push("/compliance/aml/alerts")}
                    animationVariant="buttonTap"
                  >
                    View Alerts
                  </AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="carbon-verification" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Carbon Credit Verification</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Manage verification of carbon credits
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <p className="text-sm">
                      Carbon credit verification ensures the authenticity and quality of carbon credits on our platform.
                    </p>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div>
                        <h3 className="text-sm font-medium mb-2">Pending</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <Clock className="mr-1 h-3 w-3" />
                            3
                          </span>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-2">Approved</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            12
                          </span>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-2">Rejected</h3>
                        <div className="flex items-center space-x-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <XCircle className="mr-1 h-3 w-3" />
                            1
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter className="flex justify-between">
                  <AnimatedButton
                    variant="outline"
                    onClick={() => router.push("/compliance/carbon-verification/pending")}
                    animationVariant="buttonTap"
                  >
                    View Pending
                  </AnimatedButton>
                  <AnimatedButton
                    onClick={() => router.push("/compliance/carbon-verification/submit")}
                    animationVariant="buttonTap"
                  >
                    Submit for Verification
                  </AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="reports" className="space-y-4">
              <ReportManagement userId={session?.user?.id} organizationId={session?.user?.organizationId} />
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
