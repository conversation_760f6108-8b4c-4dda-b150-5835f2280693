"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/ui/animated";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedCardHeader,
  AnimatedCardT<PERSON>le,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import {
  Shield,
  Upload,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  RefreshCw,
  History
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export default function KycVerificationPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("status");

  // Mock KYC status data
  const kycStatus = {
    status: "APPROVED",
    level: "ADVANCED",
    lastUpdated: "2023-05-15",
    expiresAt: "2024-05-15",
    documents: [
      {
        id: "1",
        type: "PASSPORT",
        name: "Passport.pdf",
        status: "APPROVED",
        uploadedAt: "2023-05-10",
      },
      {
        id: "2",
        type: "SELFIE",
        name: "Selfie.jpg",
        status: "APPROVED",
        uploadedAt: "2023-05-10",
      },
      {
        id: "3",
        type: "PROOF_OF_ADDRESS",
        name: "UtilityBill.pdf",
        status: "APPROVED",
        uploadedAt: "2023-05-10",
      },
      {
        id: "4",
        type: "BANK_STATEMENT",
        name: "BankStatement.pdf",
        status: "APPROVED",
        uploadedAt: "2023-05-10",
      },
    ],
    history: [
      {
        id: "1",
        status: "SUBMITTED",
        date: "2023-05-10",
        notes: "Initial submission",
      },
      {
        id: "2",
        status: "PENDING",
        date: "2023-05-11",
        notes: "Under review",
      },
      {
        id: "3",
        status: "APPROVED",
        date: "2023-05-15",
        notes: "All documents verified",
      },
    ],
  };

  // Function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        );
      case "REQUIRES_UPDATE":
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 hover:bg-orange-100">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Requires Update
          </Badge>
        );
      case "SUBMITTED":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            <Upload className="mr-1 h-3 w-3" />
            Submitted
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            {status}
          </Badge>
        );
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="KYC Verification"
              description="Manage your Know Your Customer (KYC) verification"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "KYC Verification", href: "/compliance/kyc", isCurrent: true }
              ]}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full mb-4">
              <TabsTrigger value="status" className="flex items-center">
                <Shield className="mr-2 h-4 w-4" />
                Status
              </TabsTrigger>
              <TabsTrigger value="documents" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Documents
              </TabsTrigger>
              <TabsTrigger value="update" className="flex items-center">
                <RefreshCw className="mr-2 h-4 w-4" />
                Update
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center">
                <History className="mr-2 h-4 w-4" />
                History
              </TabsTrigger>
            </TabsList>

            <TabsContent value="status" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>KYC Verification Status</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Current status of your KYC verification
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Status</p>
                        <div>{getStatusBadge(kycStatus.status)}</div>
                      </div>
                      <div className="space-y-1 mt-4 md:mt-0">
                        <p className="text-sm font-medium">Verification Level</p>
                        <Badge variant="secondary">{kycStatus.level}</Badge>
                      </div>
                      <div className="space-y-1 mt-4 md:mt-0">
                        <p className="text-sm font-medium">Last Updated</p>
                        <p className="text-sm">{kycStatus.lastUpdated}</p>
                      </div>
                      <div className="space-y-1 mt-4 md:mt-0">
                        <p className="text-sm font-medium">Expires On</p>
                        <p className="text-sm">{kycStatus.expiresAt}</p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium mb-2">Verification Progress</h3>
                      <Progress value={100} className="h-2" />
                      <div className="flex justify-between mt-1">
                        <span className="text-xs text-muted-foreground">Submitted</span>
                        <span className="text-xs text-muted-foreground">Under Review</span>
                        <span className="text-xs text-muted-foreground">Approved</span>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Verification Details</h3>
                      <div className="bg-muted p-4 rounded-md">
                        <p className="text-sm">
                          Your KYC verification has been approved at the <strong>Advanced</strong> level. This allows you to:
                        </p>
                        <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                          <li>Trade unlimited amounts of carbon credits</li>
                          <li>Access all platform features</li>
                          <li>Participate in all marketplace activities</li>
                          <li>Withdraw funds without limits</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter>
                  <AnimatedButton
                    variant="outline"
                    onClick={() => setActiveTab("documents")}
                    animationVariant="buttonTap"
                  >
                    View Documents
                  </AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Verification Documents</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Documents submitted for KYC verification
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-muted">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                              Document Type
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                              File Name
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                              Uploaded
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-card divide-y divide-gray-200">
                          {kycStatus.documents.map((doc) => (
                            <tr key={doc.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                {doc.type}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                {doc.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                {getStatusBadge(doc.status)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                {doc.uploadedAt}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <AnimatedButton
                                  variant="ghost"
                                  size="sm"
                                  animationVariant="buttonTap"
                                >
                                  View
                                </AnimatedButton>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter>
                  <AnimatedButton
                    onClick={() => setActiveTab("update")}
                    animationVariant="buttonTap"
                  >
                    Update Documents
                  </AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="update" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Update KYC Verification</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Update your KYC verification documents or level
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-6">
                    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <AlertTriangle className="h-5 w-5 text-yellow-400" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-yellow-700">
                            Your KYC verification is already approved at the Advanced level. You only need to update if your documents have expired or if your information has changed.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Select Verification Level</h3>
                      <div className="grid gap-4 md:grid-cols-3">
                        <div className="border rounded-md p-4 cursor-pointer hover:border-primary">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">Basic</h4>
                            <Badge variant="outline">Current: Advanced</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            Limited trading capabilities with basic verification.
                          </p>
                          <ul className="list-disc list-inside text-xs space-y-1 text-muted-foreground">
                            <li>Trade up to $10,000 per day</li>
                            <li>Basic platform features</li>
                          </ul>
                        </div>
                        <div className="border rounded-md p-4 cursor-pointer hover:border-primary">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">Intermediate</h4>
                            <Badge variant="outline">Current: Advanced</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            Enhanced trading capabilities with more verification.
                          </p>
                          <ul className="list-disc list-inside text-xs space-y-1 text-muted-foreground">
                            <li>Trade up to $100,000 per day</li>
                            <li>Most platform features</li>
                          </ul>
                        </div>
                        <div className="border rounded-md p-4 cursor-pointer border-primary bg-primary/5">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">Advanced</h4>
                            <Badge>Current</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            Full trading capabilities with complete verification.
                          </p>
                          <ul className="list-disc list-inside text-xs space-y-1 text-muted-foreground">
                            <li>Unlimited trading</li>
                            <li>All platform features</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Update Documents</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Select the documents you want to update and upload new versions.
                      </p>
                      <div className="space-y-4">
                        {kycStatus.documents.map((doc) => (
                          <div key={doc.id} className="flex items-center justify-between border p-4 rounded-md">
                            <div>
                              <p className="font-medium">{doc.type}</p>
                              <p className="text-sm text-muted-foreground">Current: {doc.name}</p>
                            </div>
                            <AnimatedButton
                              variant="outline"
                              size="sm"
                              animationVariant="buttonTap"
                            >
                              Upload New
                            </AnimatedButton>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </AnimatedCardContent>
                <AnimatedCardFooter className="flex justify-between">
                  <AnimatedButton
                    variant="outline"
                    onClick={() => setActiveTab("status")}
                    animationVariant="buttonTap"
                  >
                    Cancel
                  </AnimatedButton>
                  <AnimatedButton
                    onClick={() => {
                      // Submit update logic would go here
                      setActiveTab("status");
                    }}
                    animationVariant="buttonTap"
                  >
                    Submit Updates
                  </AnimatedButton>
                </AnimatedCardFooter>
              </AnimatedCard>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Verification History</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    History of your KYC verification process
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <div className="relative">
                      {kycStatus.history.map((event, index) => (
                        <div key={event.id} className="mb-8 flex items-start">
                          <div className="flex flex-col items-center mr-4">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground">
                              {event.status === "APPROVED" ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : event.status === "PENDING" ? (
                                <Clock className="h-4 w-4" />
                              ) : event.status === "REJECTED" ? (
                                <XCircle className="h-4 w-4" />
                              ) : (
                                <Upload className="h-4 w-4" />
                              )}
                            </div>
                            {index < kycStatus.history.length - 1 && (
                              <div className="w-0.5 h-full bg-muted mt-2"></div>
                            )}
                          </div>
                          <div className="bg-card p-4 rounded-md shadow-sm border flex-1">
                            <div className="flex justify-between items-center mb-2">
                              <h3 className="font-medium">{event.status}</h3>
                              <span className="text-xs text-muted-foreground">{event.date}</span>
                            </div>
                            <p className="text-sm">{event.notes}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
