"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Loader2, 
  AlertTriangle,
  Shield,
  FileText,
  Upload,
  CheckCircle,
  XCircle,
  Clock,
  ArrowRight
} from "lucide-react";
import { KycVerificationForm } from "@/components/compliance/kyc-verification-form";
import { KycLevel, ComplianceStatus, ComplianceDocumentType } from "@/lib/compliance";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

interface KycVerificationClientProps {
  userName: string;
}

interface KycStatusData {
  status: ComplianceStatus;
  level: KycLevel;
  lastChecked: string;
  expiresAt: string;
  documents: {
    id: string;
    type: ComplianceDocumentType;
    name: string;
    url: string;
    status: ComplianceStatus;
    createdAt: string;
  }[];
}

export default function KycVerificationClient({ userName }: KycVerificationClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [kycStatus, setKycStatus] = useState<KycStatusData | null>(null);
  const [activeTab, setActiveTab] = useState("verification");

  useEffect(() => {
    async function fetchKycStatus() {
      try {
        setIsLoading(true);
        
        // Fetch KYC status
        const response = await fetch('/api/compliance/kyc/status');
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch KYC status");
        }
        
        const data = await response.json();
        setKycStatus(data);
      } catch (error) {
        console.error("Error fetching KYC status:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchKycStatus();
  }, []);

  // Mock data for development
  const mockKycStatus: KycStatusData = {
    status: ComplianceStatus.IN_REVIEW,
    level: KycLevel.INTERMEDIATE,
    lastChecked: "2023-06-15",
    expiresAt: "2024-06-15",
    documents: [
      {
        id: "doc-1",
        type: ComplianceDocumentType.PASSPORT,
        name: "Passport",
        url: "/documents/passport.pdf",
        status: ComplianceStatus.APPROVED,
        createdAt: "2023-06-15"
      },
      {
        id: "doc-2",
        type: ComplianceDocumentType.SELFIE,
        name: "Selfie with ID",
        url: "/documents/selfie.jpg",
        status: ComplianceStatus.APPROVED,
        createdAt: "2023-06-15"
      },
      {
        id: "doc-3",
        type: ComplianceDocumentType.PROOF_OF_ADDRESS,
        name: "Utility Bill",
        url: "/documents/utility-bill.pdf",
        status: ComplianceStatus.IN_REVIEW,
        createdAt: "2023-06-15"
      }
    ]
  };

  const status = kycStatus || mockKycStatus;

  const getStatusBadge = (status: ComplianceStatus) => {
    switch (status) {
      case ComplianceStatus.APPROVED:
        return <Badge variant="success" className="ml-2">Approved</Badge>;
      case ComplianceStatus.PENDING:
      case ComplianceStatus.IN_REVIEW:
        return <Badge variant="warning" className="ml-2">In Review</Badge>;
      case ComplianceStatus.REJECTED:
        return <Badge variant="destructive" className="ml-2">Rejected</Badge>;
      case ComplianceStatus.EXPIRED:
        return <Badge variant="outline" className="ml-2">Expired</Badge>;
      default:
        return <Badge variant="secondary" className="ml-2">{status}</Badge>;
    }
  };

  const getDocumentStatusIcon = (status: ComplianceStatus) => {
    switch (status) {
      case ComplianceStatus.APPROVED:
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case ComplianceStatus.PENDING:
      case ComplianceStatus.IN_REVIEW:
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case ComplianceStatus.REJECTED:
        return <XCircle className="h-5 w-5 text-red-600" />;
      case ComplianceStatus.EXPIRED:
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getDocumentTypeIcon = (type: ComplianceDocumentType) => {
    switch (type) {
      case ComplianceDocumentType.PASSPORT:
      case ComplianceDocumentType.DRIVERS_LICENSE:
      case ComplianceDocumentType.NATIONAL_ID:
        return <Shield className="h-5 w-5 text-blue-600" />;
      case ComplianceDocumentType.SELFIE:
        return <FileText className="h-5 w-5 text-purple-600" />;
      case ComplianceDocumentType.PROOF_OF_ADDRESS:
      case ComplianceDocumentType.UTILITY_BILL:
        return <FileText className="h-5 w-5 text-green-600" />;
      case ComplianceDocumentType.BANK_STATEMENT:
        return <FileText className="h-5 w-5 text-yellow-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="KYC Verification"
              description="Complete your Know Your Customer (KYC) verification"
              breadcrumbItems={[
                { label: "Compliance", href: "/compliance" },
                { label: "KYC", href: "/compliance/kyc" },
                { label: "Verification", href: "/compliance/kyc/verification", isCurrent: true }
              ]}
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading KYC verification data...</span>
            </div>
          ) : error ? (
            <AnimatedCard className="border-destructive/50 bg-destructive/10">
              <AnimatedCardContent className="flex items-center py-6">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <p className="ml-2 text-destructive">{error}</p>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="w-full">
                <TabsTrigger value="verification" className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Verification
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Documents
                </TabsTrigger>
                <TabsTrigger value="history" className="flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  History
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="verification" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>KYC Verification Status</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Current status of your Know Your Customer (KYC) verification
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">Status</p>
                          <div className="flex items-center">
                            <span className="font-medium">{status.status}</span>
                            {getStatusBadge(status.status)}
                          </div>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Verification Level</p>
                          <Badge variant="secondary">{status.level}</Badge>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Last Updated</p>
                          <span className="text-sm">{status.lastChecked}</span>
                        </div>
                        <div className="space-y-1 mt-4 md:mt-0">
                          <p className="text-sm font-medium">Expiry Date</p>
                          <span className="text-sm">{status.expiresAt}</span>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      {session?.user && (
                        <KycVerificationForm 
                          userId={session.user.id}
                          organizationId={session.user.organizationId || ""}
                          currentStatus={{
                            status: status.status,
                            level: status.level,
                            documents: status.documents
                          }}
                        />
                      )}
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
              
              <TabsContent value="documents" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Verification Documents</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Documents submitted for KYC verification
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      {status.documents.length > 0 ? (
                        <StaggeredList className="divide-y">
                          {status.documents.map((doc) => (
                            <div key={doc.id} className="py-4 flex items-center justify-between">
                              <div className="flex items-center space-x-4">
                                <div className="bg-muted p-2 rounded-full">
                                  {getDocumentTypeIcon(doc.type)}
                                </div>
                                <div>
                                  <p className="font-medium">{doc.name}</p>
                                  <p className="text-sm text-muted-foreground">
                                    Uploaded on {doc.createdAt}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4">
                                {getDocumentStatusIcon(doc.status)}
                                <AnimatedButton 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => window.open(doc.url, '_blank')}
                                >
                                  View
                                </AnimatedButton>
                              </div>
                            </div>
                          ))}
                        </StaggeredList>
                      ) : (
                        <div className="text-center py-8">
                          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <h3 className="text-lg font-medium">No Documents Found</h3>
                          <p className="text-sm text-muted-foreground mt-2">
                            You haven't submitted any documents yet. Please go to the Verification tab to submit documents.
                          </p>
                        </div>
                      )}
                      
                      <Separator />
                      
                      <div className="flex justify-end">
                        <AnimatedButton 
                          onClick={() => setActiveTab("verification")}
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Upload New Document
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
              
              <TabsContent value="history" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Verification History</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      History of your KYC verification attempts
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-6">
                      <div className="text-center py-8">
                        <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">Verification History</h3>
                        <p className="text-sm text-muted-foreground mt-2">
                          Your verification history will be displayed here.
                        </p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
