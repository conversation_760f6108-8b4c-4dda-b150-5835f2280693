"use client";

import { PageTransition } from "@/components/ui/animated";
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

export default function AccessibilityPage() {
  // Current date for "Last Updated"
  const currentDate = new Date();
  const formattedDate = `${currentDate.toLocaleString('default', { month: 'long' })} ${currentDate.getDate()}, ${currentDate.getFullYear()}`;

  return (
    <PageTransition animationVariant="fadeIn">
      <div className="bg-white">
        <div className="relative overflow-hidden bg-gradient-to-b from-green-50 to-blue-50 pt-16 pb-16">
          <div className="relative">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:mx-0">
                <motion.h1
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  Accessibility
                </motion.h1>
                <motion.p
                  className="mt-6 text-lg leading-8 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  Last Updated: {formattedDate}
                </motion.p>
              </div>
            </div>
          </div>
        </div>

        {/* Accessibility Content */}
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="mx-auto max-w-3xl">
            <motion.div
              className="prose prose-lg prose-green max-w-none"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <p>
                At Carbonix, we are committed to ensuring that our website and platform are accessible to all users, including those with disabilities. We strive to meet the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA standards.
              </p>

              <h2 className="text-2xl font-bold mt-8 mb-4">Our Commitment to Accessibility</h2>
              <p>
                We believe that digital accessibility is a fundamental right, not a luxury. Our goal is to create an inclusive experience that accommodates the diverse needs of all our users, regardless of ability or technology.
              </p>
              <p className="mt-4">
                We are continuously working to improve the accessibility and usability of our platform to ensure that all users, including those with disabilities, can effectively access and interact with our services.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Accessibility Features</h2>
              <p>
                Our website includes the following accessibility features:
              </p>
              <ul className="list-disc pl-6 mt-4 space-y-2">
                <li><strong>Keyboard Navigation:</strong> All functionality is operable through a keyboard interface for users who cannot use a mouse.</li>
                <li><strong>Text Alternatives:</strong> We provide text alternatives for non-text content to ensure it can be changed into other forms people need, such as large print, braille, speech, or simpler language.</li>
                <li><strong>Responsive Design:</strong> Our website is designed to be responsive and adjust to different screen sizes and devices.</li>
                <li><strong>Color Contrast:</strong> We maintain sufficient color contrast between text and backgrounds to ensure readability.</li>
                <li><strong>Focus Indicators:</strong> Visible focus indicators help keyboard users navigate the website.</li>
                <li><strong>Semantic HTML:</strong> We use proper HTML markup to improve the structure and meaning of our content.</li>
                <li><strong>ARIA Landmarks:</strong> ARIA landmarks help screen reader users navigate the website more efficiently.</li>
              </ul>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Compatibility with Assistive Technologies</h2>
              <p>
                Our platform is designed to be compatible with various assistive technologies, including:
              </p>
              <ul className="list-disc pl-6 mt-4 space-y-2">
                <li>Screen readers (such as JAWS, NVDA, VoiceOver, and TalkBack)</li>
                <li>Screen magnification software</li>
                <li>Speech recognition software</li>
                <li>Alternative keyboard and mouse input devices</li>
              </ul>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Known Accessibility Issues</h2>
              <p>
                While we strive to make our platform fully accessible, we acknowledge that there may be areas that need improvement. We are actively working to identify and address any accessibility issues.
              </p>
              <p className="mt-4">
                Some known issues that we are currently working to resolve:
              </p>
              <ul className="list-disc pl-6 mt-4 space-y-2">
                <li>Some interactive charts and data visualizations may not be fully accessible to screen reader users. We are working on providing alternative text-based representations of this data.</li>
                <li>Some dynamic content updates may not be properly announced to screen reader users. We are implementing ARIA live regions to address this issue.</li>
              </ul>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Accessibility Feedback</h2>
              <p>
                We welcome your feedback on the accessibility of our platform. If you encounter any barriers or have suggestions for improvement, please let us know. Your input helps us identify and address accessibility issues.
              </p>
              <div className="mt-6">
                <Link href="/contact">
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    Contact Us About Accessibility
                  </Button>
                </Link>
              </div>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Accessibility Statement Scope</h2>
              <p>
                This accessibility statement applies to the Carbonix website and platform at carbonexchange.com and all associated subdomains.
              </p>
              <p className="mt-4">
                We are committed to ongoing accessibility improvements and conduct regular audits and testing to ensure compliance with accessibility standards.
              </p>
              
              <h2 className="text-2xl font-bold mt-8 mb-4">Accessibility Resources</h2>
              <p>
                For more information about web accessibility, please visit these resources:
              </p>
              <ul className="list-disc pl-6 mt-4 space-y-2">
                <li><a href="https://www.w3.org/WAI/standards-guidelines/wcag/" target="_blank" rel="noopener noreferrer" className="text-green-600 hover:text-green-500">Web Content Accessibility Guidelines (WCAG)</a></li>
                <li><a href="https://www.w3.org/WAI/" target="_blank" rel="noopener noreferrer" className="text-green-600 hover:text-green-500">Web Accessibility Initiative (WAI)</a></li>
                <li><a href="https://www.ada.gov/" target="_blank" rel="noopener noreferrer" className="text-green-600 hover:text-green-500">Americans with Disabilities Act (ADA)</a></li>
              </ul>
              
              <div className="mt-12 pt-8 border-t border-gray-200">
                <p className="text-gray-600">
                  We are committed to ensuring that our platform is accessible to all users, regardless of ability, and we appreciate your support and feedback in helping us achieve this goal.
                </p>
                <div className="mt-6">
                  <Link href="/contact" className="text-green-600 hover:text-green-500 font-medium">
                    Contact Us
                  </Link>{' '}
                  |{' '}
                  <Link href="/privacy" className="text-green-600 hover:text-green-500 font-medium">
                    Privacy Policy
                  </Link>{' '}
                  |{' '}
                  <Link href="/terms" className="text-green-600 hover:text-green-500 font-medium">
                    Terms of Service
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}