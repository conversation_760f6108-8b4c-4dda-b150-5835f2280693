"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedInput,
  PageTransition,
  StaggeredList
} from "@/components/ui/animated";
import { AnimationSettings } from "@/components/settings/animation-settings";
import { ValidatedForm } from "@/components/forms/validated-form";
import { userProfileSchema, passwordChangeSchema } from "@/lib/validation/schemas";
import { AlertCircle } from "lucide-react";

export default function SettingsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("profile");

  // User profile state
  const [profileValues, setProfileValues] = useState({
    name: session?.user?.name || "",
    email: session?.user?.email || "",
    jobTitle: "",
    phoneNumber: "",
    bio: "",
  });

  // Password change state
  const [passwordValues, setPasswordValues] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Mock organization data
  const mockOrganization = {
    name: "Green Energy Co.",
    description: "Renewable energy solutions provider",
    website: "https://greenenergy.example.com",
    logo: "",
    transactionFeeRate: 1.0,
    listingFeeRate: 0.5,
  };

  // Handle profile form submission
  const handleProfileSubmit = async (data: any) => {
    try {
      // API call would go here
      console.log("Profile data submitted:", data);

      // Update local state
      setProfileValues(data);

      // Show success message
      alert("Profile updated successfully");

      return data;
    } catch (error) {
      console.error("Error updating profile:", error);
      throw new Error("Failed to update profile");
    }
  };

  // Handle password change form submission
  const handlePasswordSubmit = async (data: any) => {
    try {
      // API call would go here
      console.log("Password change submitted:", data);

      // Reset form
      setPasswordValues({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // Show success message
      alert("Password changed successfully");

      return data;
    } catch (error) {
      console.error("Error changing password:", error);
      throw new Error("Failed to change password");
    }
  };

  return (
    <PageTransition>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account and organization settings
          </p>
        </div>

      <div className="flex items-center space-x-4">
        <AnimatedButton
          variant={activeTab === "profile" ? "default" : "outline"}
          onClick={() => setActiveTab("profile")}
          animationVariant="buttonTap"
        >
          Profile
        </AnimatedButton>
        {session?.user?.role === "ORGANIZATION_ADMIN" && (
          <AnimatedButton
            variant={activeTab === "organization" ? "default" : "outline"}
            onClick={() => setActiveTab("organization")}
            animationVariant="buttonTap"
          >
            Organization
          </AnimatedButton>
        )}
        <AnimatedButton
          variant={activeTab === "security" ? "default" : "outline"}
          onClick={() => setActiveTab("security")}
          animationVariant="buttonTap"
        >
          Security
        </AnimatedButton>
        <AnimatedButton
          variant={activeTab === "notifications" ? "default" : "outline"}
          onClick={() => setActiveTab("notifications")}
          animationVariant="buttonTap"
        >
          Notifications
        </AnimatedButton>
        <AnimatedButton
          variant={activeTab === "animations" ? "default" : "outline"}
          onClick={() => setActiveTab("animations")}
          animationVariant="buttonTap"
        >
          Animations
        </AnimatedButton>
      </div>

      {activeTab === "profile" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Profile Information</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <ValidatedForm
              schema={userProfileSchema}
              defaultValues={profileValues}
              onSubmit={handleProfileSubmit}
              className="space-y-4"
            >
              {({ control, formState, isSubmitting, formError }) => (
                <>
                  {formError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{formError.message}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid gap-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Name
                    </label>
                    <Input
                      id="name"
                      {...control.register("name")}
                      placeholder="Your name"
                    />
                    {formState.errors.name && (
                      <p className="text-xs text-destructive">{formState.errors.name.message}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      Email
                    </label>
                    <Input
                      id="email"
                      type="email"
                      {...control.register("email")}
                      placeholder="Your email"
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">
                      Your email cannot be changed
                    </p>
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="jobTitle" className="text-sm font-medium">
                      Job Title
                    </label>
                    <Input
                      id="jobTitle"
                      {...control.register("jobTitle")}
                      placeholder="Your job title"
                    />
                    {formState.errors.jobTitle && (
                      <p className="text-xs text-destructive">{formState.errors.jobTitle.message}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="phoneNumber" className="text-sm font-medium">
                      Phone Number
                    </label>
                    <Input
                      id="phoneNumber"
                      {...control.register("phoneNumber")}
                      placeholder="Your phone number"
                    />
                    {formState.errors.phoneNumber && (
                      <p className="text-xs text-destructive">{formState.errors.phoneNumber.message}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="bio" className="text-sm font-medium">
                      Bio
                    </label>
                    <Textarea
                      id="bio"
                      {...control.register("bio")}
                      placeholder="A short bio about yourself"
                      rows={4}
                    />
                    {formState.errors.bio && (
                      <p className="text-xs text-destructive">{formState.errors.bio.message}</p>
                    )}
                  </div>

                  <AnimatedButton
                    type="submit"
                    disabled={isSubmitting}
                    animationVariant="buttonTap"
                  >
                    {isSubmitting ? "Saving..." : "Save Changes"}
                  </AnimatedButton>
                </>
              )}
            </ValidatedForm>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "organization" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Organization Settings</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <form className="space-y-4">
              <div className="grid gap-2">
                <label htmlFor="org-name" className="text-sm font-medium">
                  Organization Name
                </label>
                <AnimatedInput
                  id="org-name"
                  defaultValue={mockOrganization.name}
                  placeholder="Organization name"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="org-description" className="text-sm font-medium">
                  Description
                </label>
                <textarea
                  id="org-description"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  defaultValue={mockOrganization.description}
                  placeholder="Organization description"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="org-website" className="text-sm font-medium">
                  Website
                </label>
                <AnimatedInput
                  id="org-website"
                  type="url"
                  defaultValue={mockOrganization.website}
                  placeholder="https://example.com"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="org-logo" className="text-sm font-medium">
                  Logo
                </label>
                <AnimatedInput id="org-logo" type="file" accept="image/*" />
              </div>
              <AnimatedButton animationVariant="buttonTap">Save Organization Settings</AnimatedButton>
            </form>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "security" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Security Settings</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <ValidatedForm
              schema={passwordChangeSchema}
              defaultValues={passwordValues}
              onSubmit={handlePasswordSubmit}
              className="space-y-4"
            >
              {({ control, formState, isSubmitting, formError }) => (
                <>
                  {formError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{formError.message}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid gap-2">
                    <label htmlFor="currentPassword" className="text-sm font-medium">
                      Current Password
                    </label>
                    <Input
                      id="currentPassword"
                      type="password"
                      {...control.register("currentPassword")}
                      placeholder="Enter current password"
                    />
                    {formState.errors.currentPassword && (
                      <p className="text-xs text-destructive">{formState.errors.currentPassword.message}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="newPassword" className="text-sm font-medium">
                      New Password
                    </label>
                    <Input
                      id="newPassword"
                      type="password"
                      {...control.register("newPassword")}
                      placeholder="Enter new password"
                    />
                    {formState.errors.newPassword && (
                      <p className="text-xs text-destructive">{formState.errors.newPassword.message}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="confirmPassword" className="text-sm font-medium">
                      Confirm New Password
                    </label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      {...control.register("confirmPassword")}
                      placeholder="Confirm new password"
                    />
                    {formState.errors.confirmPassword && (
                      <p className="text-xs text-destructive">{formState.errors.confirmPassword.message}</p>
                    )}
                  </div>

                  <AnimatedButton
                    type="submit"
                    disabled={isSubmitting}
                    animationVariant="buttonTap"
                  >
                    {isSubmitting ? "Changing Password..." : "Change Password"}
                  </AnimatedButton>
                </>
              )}
            </ValidatedForm>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "notifications" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Notification Preferences</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <form className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Email Notifications</h3>
                  <p className="text-xs text-muted-foreground">
                    Receive email notifications for important updates
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Transaction Alerts</h3>
                  <p className="text-xs text-muted-foreground">
                    Get notified when transactions occur
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Market Updates</h3>
                  <p className="text-xs text-muted-foreground">
                    Receive updates about new carbon credits and market changes
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Platform Announcements</h3>
                  <p className="text-xs text-muted-foreground">
                    Get notified about platform updates and announcements
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <AnimatedButton animationVariant="buttonTap">Save Preferences</AnimatedButton>
            </form>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "animations" && (
        <AnimationSettings />
      )}
    </div>
    </PageTransition>
  );
}
