"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Bell,
  Mail,
  MessageSquare,
  CreditCard,
  FileText,
  ShieldAlert,
  Loader2,
  Save,
  AlertCircle
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ValidatedForm } from "@/components/forms/validated-form";
import { notificationPreferencesSchema } from "@/lib/validation/schemas";

// Use the notificationPreferencesSchema from our validation library
type NotificationPreferencesValues = {
  emailNotifications: boolean;
  inAppNotifications: boolean;

  // System notifications
  systemEmailEnabled: boolean;
  systemInAppEnabled: boolean;

  // Transaction notifications
  transactionEmailEnabled: boolean;
  transactionInAppEnabled: boolean;

  // Credit notifications
  creditEmailEnabled: boolean;
  creditInAppEnabled: boolean;

  // Billing notifications
  billingEmailEnabled: boolean;
  billingInAppEnabled: boolean;

  // Document notifications
  documentEmailEnabled: boolean;
  documentInAppEnabled: boolean;

  // Security notifications
  securityEmailEnabled: boolean;
  securityInAppEnabled: boolean;

  // Marketing notifications
  marketingEmailEnabled: boolean;
  marketingInAppEnabled: boolean;
};

export default function NotificationPreferencesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [formValues, setFormValues] = useState<NotificationPreferencesValues>({
    emailNotifications: true,
    inAppNotifications: true,
    systemEmailEnabled: true,
    systemInAppEnabled: true,
    transactionEmailEnabled: true,
    transactionInAppEnabled: true,
    creditEmailEnabled: true,
    creditInAppEnabled: true,
    billingEmailEnabled: true,
    billingInAppEnabled: true,
    documentEmailEnabled: true,
    documentInAppEnabled: true,
    securityEmailEnabled: true,
    securityInAppEnabled: true,
    marketingEmailEnabled: false,
    marketingInAppEnabled: false,
  });

  // Fetch notification preferences
  const fetchNotificationPreferences = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/user/notification-preferences");

      if (!response.ok) {
        throw new Error("Failed to fetch notification preferences");
      }

      const data = await response.json();

      // Update form values
      setFormValues({
        emailNotifications: data.preferences.emailNotifications,
        inAppNotifications: data.preferences.inAppNotifications,
        systemEmailEnabled: data.preferences.systemEmailEnabled,
        systemInAppEnabled: data.preferences.systemInAppEnabled,
        transactionEmailEnabled: data.preferences.transactionEmailEnabled,
        transactionInAppEnabled: data.preferences.transactionInAppEnabled,
        creditEmailEnabled: data.preferences.creditEmailEnabled,
        creditInAppEnabled: data.preferences.creditInAppEnabled,
        billingEmailEnabled: data.preferences.billingEmailEnabled,
        billingInAppEnabled: data.preferences.billingInAppEnabled,
        documentEmailEnabled: data.preferences.documentEmailEnabled,
        documentInAppEnabled: data.preferences.documentInAppEnabled,
        securityEmailEnabled: data.preferences.securityEmailEnabled,
        securityInAppEnabled: data.preferences.securityInAppEnabled,
        marketingEmailEnabled: data.preferences.marketingEmailEnabled,
        marketingInAppEnabled: data.preferences.marketingInAppEnabled,
      });
    } catch (error) {
      console.error("Error fetching notification preferences:", error);
      toast({
        title: "Error",
        description: "Failed to load notification preferences",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Save notification preferences
  const onSubmit = async (data: NotificationPreferencesValues) => {
    try {
      const response = await fetch("/api/user/notification-preferences", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to save notification preferences");
      }

      // Update local state with the saved values
      setFormValues(data);

      toast({
        title: "Success",
        description: "Notification preferences saved successfully",
      });
    } catch (error) {
      console.error("Error saving notification preferences:", error);
      throw new Error("Failed to save notification preferences");
    }
  };

  // Helper function to update form values when main toggles change
  const updateCategoryToggles = (
    mainToggle: string,
    isEnabled: boolean,
    setValue: (name: string, value: any) => void
  ) => {
    if (!isEnabled) {
      if (mainToggle === "emailNotifications") {
        setValue("systemEmailEnabled", false);
        setValue("transactionEmailEnabled", false);
        setValue("creditEmailEnabled", false);
        setValue("billingEmailEnabled", false);
        setValue("documentEmailEnabled", false);
        setValue("securityEmailEnabled", false);
        setValue("marketingEmailEnabled", false);
      } else if (mainToggle === "inAppNotifications") {
        setValue("systemInAppEnabled", false);
        setValue("transactionInAppEnabled", false);
        setValue("creditInAppEnabled", false);
        setValue("billingInAppEnabled", false);
        setValue("documentInAppEnabled", false);
        setValue("securityInAppEnabled", false);
        setValue("marketingInAppEnabled", false);
      }
    }
  };

  // Load notification preferences on mount
  useEffect(() => {
    if (session?.user) {
      fetchNotificationPreferences();
    }
  }, [session]);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading") {
    return (
      <div className="container py-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Notification Preferences</h1>
        <p className="text-muted-foreground">
          Manage how you receive notifications from the platform
        </p>
      </div>

      <ValidatedForm
        schema={notificationPreferencesSchema}
        defaultValues={formValues}
        onSubmit={onSubmit}
        className="space-y-6"
      >
        {({ control, formState, isSubmitting, formError, watch, setValue }) => {
          // Watch for changes to main toggles
          const emailNotificationsValue = watch("emailNotifications");
          const inAppNotificationsValue = watch("inAppNotifications");

          return (
            <>
            {formError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{formError.message}</AlertDescription>
              </Alert>
            )}
          <Card>
            <CardHeader>
              <CardTitle>Global Settings</CardTitle>
              <CardDescription>
                Control all notifications by channel
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={control}
                name="emailNotifications"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <div className="flex items-center">
                        <Mail className="mr-2 h-4 w-4" />
                        <FormLabel className="text-base">Email Notifications</FormLabel>
                      </div>
                      <FormDescription>
                        Receive notifications via email
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          updateCategoryToggles("emailNotifications", checked, setValue);
                        }}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="inAppNotifications"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <div className="flex items-center">
                        <Bell className="mr-2 h-4 w-4" />
                        <FormLabel className="text-base">In-App Notifications</FormLabel>
                      </div>
                      <FormDescription>
                        Receive notifications within the application
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          updateCategoryToggles("inAppNotifications", checked, setValue);
                        }}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Tabs defaultValue="system" className="space-y-6">
            <TabsList className="grid grid-cols-3 md:grid-cols-6">
              <TabsTrigger value="system">System</TabsTrigger>
              <TabsTrigger value="transaction">Transactions</TabsTrigger>
              <TabsTrigger value="credit">Carbon Credits</TabsTrigger>
              <TabsTrigger value="billing">Billing</TabsTrigger>
              <TabsTrigger value="document">Documents</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>

            <TabsContent value="system" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>System Notifications</CardTitle>
                  <CardDescription>
                    Notifications about system updates, maintenance, and announcements
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={control}
                    name="systemEmailEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email</FormLabel>
                          <FormDescription>
                            Receive system notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !emailNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="systemInAppEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>In-App</FormLabel>
                          <FormDescription>
                            Receive system notifications within the application
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !inAppNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="transaction" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Transaction Notifications</CardTitle>
                  <CardDescription>
                    Notifications about carbon credit transactions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={control}
                    name="transactionEmailEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email</FormLabel>
                          <FormDescription>
                            Receive transaction notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !emailNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="transactionInAppEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>In-App</FormLabel>
                          <FormDescription>
                            Receive transaction notifications within the application
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !inAppNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="credit" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Carbon Credit Notifications</CardTitle>
                  <CardDescription>
                    Notifications about carbon credit listings, verifications, and status changes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={control}
                    name="creditEmailEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email</FormLabel>
                          <FormDescription>
                            Receive carbon credit notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !emailNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="creditInAppEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>In-App</FormLabel>
                          <FormDescription>
                            Receive carbon credit notifications within the application
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !inAppNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="billing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Billing Notifications</CardTitle>
                  <CardDescription>
                    Notifications about invoices, payments, and subscription changes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={control}
                    name="billingEmailEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email</FormLabel>
                          <FormDescription>
                            Receive billing notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !emailNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="billingInAppEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>In-App</FormLabel>
                          <FormDescription>
                            Receive billing notifications within the application
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !inAppNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="document" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Document Notifications</CardTitle>
                  <CardDescription>
                    Notifications about document uploads, verifications, and status changes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={control}
                    name="documentEmailEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email</FormLabel>
                          <FormDescription>
                            Receive document notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !emailNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="documentInAppEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>In-App</FormLabel>
                          <FormDescription>
                            Receive document notifications within the application
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !inAppNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Security Notifications</CardTitle>
                  <CardDescription>
                    Notifications about security events, login attempts, and account changes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={control}
                    name="securityEmailEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email</FormLabel>
                          <FormDescription>
                            Receive security notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !emailNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="securityInAppEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>In-App</FormLabel>
                          <FormDescription>
                            Receive security notifications within the application
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading || !inAppNotificationsValue}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end">
            <Button type="submit" disabled={isLoading || isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Preferences
            </Button>
          </div>
            </>
          );
        }}
      </ValidatedForm>
    </div>
  );
}
