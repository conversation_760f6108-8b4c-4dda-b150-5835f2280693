"use client";

import { useState, useEffect } from "react";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RequiredLabel } from "@/components/ui/required-label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Loader2, AlertCircle } from "lucide-react";
import { ProfileCompletionIndicator } from "@/components/profile/profile-completion-indicator";
import { useProfileCompletion } from "@/hooks/use-profile-completion";
import { ValidatedForm } from "@/components/forms/validated-form";
import { organizationSchema } from "@/lib/validation/schemas";

// Use the organizationSchema from our validation library
type OrganizationFormValues = {
  name: string;
  description?: string;
  website?: string;
  legalName?: string;
  registrationNumber?: string;
  taxId?: string;
  country: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  phoneNumber?: string;
  industry: string;
  size: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE';
  foundedYear?: number;
  primaryContact?: string;
  primaryContactEmail?: string;
  primaryContactPhone?: string;
};

export default function OrganizationSettingsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [formDefaultValues, setFormDefaultValues] = useState<OrganizationFormValues>({
    name: '',
    description: '',
    website: '',
    legalName: '',
    registrationNumber: '',
    taxId: '',
    country: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    phoneNumber: '',
    industry: '',
    size: 'SMALL',
    foundedYear: new Date().getFullYear(),
    primaryContact: '',
    primaryContactEmail: '',
    primaryContactPhone: '',
  });

  // Get organization ID from query params or fetch from user
  useEffect(() => {
    const orgId = searchParams.get('organizationId');
    if (orgId) {
      setOrganizationId(orgId);
    } else {
      // Fetch user's organization
      const fetchUserOrganization = async () => {
        try {
          const response = await fetch('/api/user/organization');
          if (response.ok) {
            const data = await response.json();
            if (data.organization?.id) {
              setOrganizationId(data.organization.id);
            }
          }
        } catch (error) {
          console.error('Error fetching user organization:', error);
        }
      };

      fetchUserOrganization();
    }
  }, [searchParams]);

  const { organization, loading, refreshProfile } = useProfileCompletion({
    organizationId: organizationId || ''
  });

  // Update form values when organization data is loaded
  useEffect(() => {
    if (organization) {
      setFormDefaultValues({
        name: organization.name || '',
        description: organization.description || '',
        website: organization.website || '',
        legalName: organization.legalName || '',
        registrationNumber: organization.registrationNumber || '',
        taxId: organization.taxId || '',
        country: organization.country || '',
        address: organization.address || '',
        city: organization.city || '',
        state: organization.state || '',
        postalCode: organization.postalCode || '',
        phoneNumber: organization.phoneNumber || '',
        industry: organization.industry || '',
        size: organization.size || 'SMALL',
        foundedYear: organization.foundedYear || new Date().getFullYear(),
        primaryContact: organization.primaryContact || '',
        primaryContactEmail: organization.primaryContactEmail || '',
        primaryContactPhone: organization.primaryContactPhone || '',
      });
    }
  }, [organization]);

  // Form submission handler
  const onSubmit = async (data: OrganizationFormValues) => {
    if (!organizationId) {
      throw new Error("Organization ID is missing");
    }

    try {
      const response = await fetch(`/api/organizations/${organizationId}/update`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update organization');
      }

      const result = await response.json();

      toast({
        title: 'Organization Updated',
        description: 'Your organization profile has been updated successfully.',
      });

      // Refresh profile data
      refreshProfile();
    } catch (error) {
      console.error('Error updating organization:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to update organization');
    }
  };

  if (loading) {
    return (
      <div className="container py-10">
        <div className="mx-auto max-w-4xl">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Organization Settings</h1>
              <p className="text-muted-foreground">
                Manage your organization profile and settings
              </p>
            </div>

            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mx-auto max-w-4xl">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Organization Settings</h1>
            <p className="text-muted-foreground">
              Manage your organization profile and settings
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-5">
            <div className="md:col-span-3">
              <Card>
                <CardHeader>
                  <CardTitle>Organization Profile</CardTitle>
                  <CardDescription>
                    Update your organization details and information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ValidatedForm
                    schema={organizationSchema}
                    defaultValues={formDefaultValues}
                    onSubmit={onSubmit}
                    className="space-y-8"
                  >
                    {({ control, formState, isSubmitting, formError }) => (
                      <>
                      {formError && (
                        <div className="bg-destructive/15 p-3 rounded-md flex items-start space-x-2 text-destructive">
                          <AlertCircle className="h-5 w-5 mt-0.5" />
                          <div>
                            <p className="font-medium">Error</p>
                            <p className="text-sm">{formError.message}</p>
                          </div>
                        </div>
                      )}
                      {/* Essential Information */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Essential Information</h3>

                        <FormField
                          control={control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <RequiredLabel>Organization Name</RequiredLabel>
                              <FormControl>
                                <Input placeholder="Acme Corporation" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                          <FormField
                            control={control}
                            name="industry"
                            render={({ field }) => (
                              <FormItem>
                                <RequiredLabel>Industry</RequiredLabel>
                                <FormControl>
                                  <Input placeholder="Energy, Manufacturing, etc." {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="size"
                            render={({ field }) => (
                              <FormItem>
                                <RequiredLabel>Organization Size</RequiredLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select organization size" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="SMALL">Small (&lt;50 employees)</SelectItem>
                                    <SelectItem value="MEDIUM">Medium (50-250 employees)</SelectItem>
                                    <SelectItem value="LARGE">Large (251-1000 employees)</SelectItem>
                                    <SelectItem value="ENTERPRISE">Enterprise (&gt;1000 employees)</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={control}
                          name="country"
                          render={({ field }) => (
                            <FormItem>
                              <RequiredLabel>Country</RequiredLabel>
                              <FormControl>
                                <Input placeholder="United States" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Basic Information */}
                      <div className="space-y-4 mt-8">
                        <h3 className="text-lg font-medium">Basic Information</h3>

                        <FormField
                          control={control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Tell us about your organization"
                                  className="resize-none"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                          <FormField
                            control={control}
                            name="website"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Website</FormLabel>
                                <FormControl>
                                  <Input placeholder="https://example.com" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="foundedYear"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Founded Year</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder={new Date().getFullYear().toString()}
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      <div className="space-y-4 mt-8">
                        <h3 className="text-lg font-medium">Legal Information</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Legal information is required for verification and compliance purposes
                        </p>

                        <FormField
                          control={control}
                          name="legalName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Legal Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Acme Corporation Inc." {...field} />
                              </FormControl>
                              <FormDescription>
                                The registered legal name of your organization
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                          <FormField
                            control={control}
                            name="registrationNumber"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Registration Number</FormLabel>
                                <FormControl>
                                  <Input placeholder="Business registration number" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Your business registration or incorporation number
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="taxId"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Tax ID / VAT Number</FormLabel>
                                <FormControl>
                                  <Input placeholder="Tax identification number" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Required for tax reporting and compliance
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      <div className="space-y-4 mt-8">
                        <h3 className="text-lg font-medium">Contact Information</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Contact details help with verification and communication
                        </p>

                        <FormField
                          control={control}
                          name="address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Address</FormLabel>
                              <FormControl>
                                <Input placeholder="123 Main St" {...field} />
                              </FormControl>
                              <FormDescription>
                                Your organization's primary address
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                          <FormField
                            control={control}
                            name="city"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>City</FormLabel>
                                <FormControl>
                                  <Input placeholder="New York" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="state"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>State/Province</FormLabel>
                                <FormControl>
                                  <Input placeholder="NY" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="postalCode"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Postal Code</FormLabel>
                                <FormControl>
                                  <Input placeholder="10001" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={control}
                          name="phoneNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone Number</FormLabel>
                              <FormControl>
                                <Input placeholder="+****************" {...field} />
                              </FormControl>
                              <FormDescription>
                                A contact phone number for your organization
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="space-y-4 mt-8">
                        <h3 className="text-lg font-medium">Primary Contact</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          The main point of contact for your organization
                        </p>

                        <FormField
                          control={control}
                          name="primaryContact"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Contact Name</FormLabel>
                              <FormControl>
                                <Input placeholder="John Doe" {...field} />
                              </FormControl>
                              <FormDescription>
                                The name of your organization's primary contact person
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                          <FormField
                            control={control}
                            name="primaryContactEmail"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Contact Email</FormLabel>
                                <FormControl>
                                  <Input placeholder="<EMAIL>" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Email address for important communications
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="primaryContactPhone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Contact Phone</FormLabel>
                                <FormControl>
                                  <Input placeholder="+****************" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Phone number for urgent communications
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-8">
                        <p className="text-sm text-muted-foreground">
                          <span className="text-destructive">*</span> Required fields
                        </p>
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                          Save Changes
                        </Button>
                      </div>
                      </>
                    )}
                  </ValidatedForm>
                </CardContent>
              </Card>
            </div>

            <div className="md:col-span-2">
              <div className="space-y-6">
                {organizationId && (
                  <ProfileCompletionIndicator
                    organizationId={organizationId}
                    showDetails={true}
                  />
                )}

                <Card>
                  <CardHeader>
                    <CardTitle>Tips</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4 text-sm">
                      <p>
                        <strong>Complete your profile</strong> to unlock all platform features and improve your visibility to potential partners.
                      </p>
                      <p>
                        <strong>Legal information</strong> helps verify your organization and enables carbon credit trading.
                      </p>
                      <p>
                        <strong>Contact details</strong> ensure you receive important notifications about your account and transactions.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
