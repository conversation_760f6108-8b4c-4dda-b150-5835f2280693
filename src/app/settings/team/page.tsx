"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TeamInvitationForm } from "@/components/forms/team-invitation-form";
import { toast } from "@/components/ui/use-toast";
import { AlertCircle, UserPlus, UserMinus, UserCog } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface TeamMember {
  id: string;
  name: string | null;
  email: string;
  role: string;
  createdAt: string;
}

export default function TeamPage() {
  const { data: session } = useSession();
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [memberToRemove, setMemberToRemove] = useState<TeamMember | null>(null);
  const [memberToPromote, setMemberToPromote] = useState<TeamMember | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const organizationId = session?.user?.organizationId;
  const isAdmin = session?.user?.role === "ORGANIZATION_ADMIN" || session?.user?.role === "ADMIN";

  useEffect(() => {
    async function fetchTeamMembers() {
      if (!organizationId) {
        setIsLoading(false);
        setError("You must be part of an organization to view team members");
        return;
      }

      try {
        const response = await fetch(`/api/organizations/${organizationId}/members`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch team members");
        }

        setTeamMembers(data.members);
      } catch (error) {
        console.error("Error fetching team members:", error);
        setError("Failed to load team members");
      } finally {
        setIsLoading(false);
      }
    }

    fetchTeamMembers();
  }, [organizationId]);

  const handleRemoveMember = async () => {
    if (!memberToRemove || !organizationId) return;

    setIsProcessing(true);

    try {
      const response = await fetch(`/api/organizations/${organizationId}/members/${memberToRemove.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to remove team member");
      }

      // Update the team members list
      setTeamMembers(teamMembers.filter((member) => member.id !== memberToRemove.id));

      toast({
        title: "Team member removed",
        description: `${memberToRemove.email} has been removed from your organization.`,
      });
    } catch (error) {
      console.error("Error removing team member:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove team member",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setMemberToRemove(null);
    }
  };

  const handlePromoteMember = async () => {
    if (!memberToPromote || !organizationId) return;

    setIsProcessing(true);

    try {
      const response = await fetch(`/api/organizations/${organizationId}/members/${memberToPromote.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          role: "ORGANIZATION_ADMIN",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update team member role");
      }

      // Update the team members list
      setTeamMembers(
        teamMembers.map((member) =>
          member.id === memberToPromote.id
            ? { ...member, role: "ORGANIZATION_ADMIN" }
            : member
        )
      );

      toast({
        title: "Team member promoted",
        description: `${memberToPromote.email} has been promoted to Admin.`,
      });
    } catch (error) {
      console.error("Error promoting team member:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to promote team member",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setMemberToPromote(null);
    }
  };

  const handleInvitationSuccess = () => {
    toast({
      title: "Invitations sent",
      description: "Team members have been invited to your organization.",
    });
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-2">
              <AlertCircle className="h-8 w-8 text-destructive" />
              <p className="text-center">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Team Management</h1>
        <p className="text-muted-foreground">
          Manage your organization's team members and permissions.
        </p>
      </div>

      <Tabs defaultValue="members">
        <TabsList className="mb-4">
          <TabsTrigger value="members">Team Members</TabsTrigger>
          {isAdmin && <TabsTrigger value="invite">Invite Members</TabsTrigger>}
        </TabsList>

        <TabsContent value="members">
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
              <CardDescription>
                Your organization has {teamMembers.length} team members.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>
                          {member.name
                            ? member.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")
                                .toUpperCase()
                            : member.email.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                        <AvatarImage src={`https://avatar.vercel.sh/${member.email}`} />
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {member.name || member.email}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {member.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={member.role === "ORGANIZATION_ADMIN" ? "default" : "outline"}>
                        {member.role === "ORGANIZATION_ADMIN" ? "Admin" : "Member"}
                      </Badge>
                      
                      {isAdmin && session?.user?.id !== member.id && (
                        <div className="flex space-x-2">
                          {member.role !== "ORGANIZATION_ADMIN" && (
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => setMemberToPromote(member)}
                                >
                                  <UserCog className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Promote to Admin</DialogTitle>
                                  <DialogDescription>
                                    Are you sure you want to promote {member.email} to Admin? 
                                    They will have full access to manage the organization.
                                  </DialogDescription>
                                </DialogHeader>
                                <DialogFooter>
                                  <Button
                                    variant="outline"
                                    onClick={() => setMemberToPromote(null)}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    onClick={handlePromoteMember}
                                    disabled={isProcessing}
                                  >
                                    {isProcessing ? "Promoting..." : "Promote to Admin"}
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          )}
                          
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setMemberToRemove(member)}
                              >
                                <UserMinus className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Remove Team Member</DialogTitle>
                                <DialogDescription>
                                  Are you sure you want to remove {member.email} from your organization? 
                                  This action cannot be undone.
                                </DialogDescription>
                              </DialogHeader>
                              <DialogFooter>
                                <Button
                                  variant="outline"
                                  onClick={() => setMemberToRemove(null)}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  variant="destructive"
                                  onClick={handleRemoveMember}
                                  disabled={isProcessing}
                                >
                                  {isProcessing ? "Removing..." : "Remove"}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {teamMembers.length === 0 && (
                  <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8">
                    <UserPlus className="mb-2 h-8 w-8 text-muted-foreground" />
                    <p className="mb-1 text-lg font-medium">No team members yet</p>
                    <p className="text-center text-sm text-muted-foreground">
                      Invite team members to collaborate on your carbon credit trading platform.
                    </p>
                    {isAdmin && (
                      <Button
                        className="mt-4"
                        onClick={() => document.querySelector('[data-value="invite"]')?.click()}
                      >
                        Invite Team Members
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {isAdmin && (
          <TabsContent value="invite">
            {organizationId && (
              <TeamInvitationForm
                organizationId={organizationId}
                onSuccess={handleInvitationSuccess}
              />
            )}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
