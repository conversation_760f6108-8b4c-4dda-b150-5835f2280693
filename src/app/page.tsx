"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";
import { PageTransition } from "@/components/ui/animated";
import { CollapsibleFaq } from "@/components/ui/collapsible-faq";

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const featureCardVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.4 }
  },
  hover: {
    scale: 1.03,
    boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)",
    transition: { duration: 0.2 }
  }
};

const pricingCardVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  },
  hover: {
    y: -5,
    boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.08)",
    transition: { duration: 0.2 }
  }
};

// Custom hook to check if element is in view
function useAnimatedSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return { ref, isInView };
}

export default function HomePage() {
  const [scrollY, setScrollY] = useState(0);

  // Track scroll position for parallax effects
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Animation sections
  const featuresSection = useAnimatedSection();
  const howItWorksSection = useAnimatedSection();
  const pricingSection = useAnimatedSection();

  // Smooth scroll function with animation
  const scrollToSection = (sectionId: string) => (e: React.MouseEvent) => {
    e.preventDefault();

    const section = document.getElementById(sectionId);
    if (section) {
      // Add a subtle highlight animation to the section
      const highlightSection = () => {
        // Create a temporary overlay for the highlight effect
        const overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.right = '0';
        overlay.style.bottom = '0';
        overlay.style.backgroundColor = 'rgba(34, 197, 94, 0.05)'; // Very light green
        overlay.style.zIndex = '1';
        overlay.style.pointerEvents = 'none';
        overlay.style.opacity = '0';
        overlay.style.transition = 'opacity 0.5s ease-in-out';

        section.style.position = 'relative';
        section.appendChild(overlay);

        // Animate the overlay
        setTimeout(() => {
          overlay.style.opacity = '1';

          setTimeout(() => {
            overlay.style.opacity = '0';

            // Remove the overlay after animation
            setTimeout(() => {
              section.removeChild(overlay);
            }, 500);
          }, 800);
        }, 300);
      };

      const headerHeight = 64; // Height of the fixed header
      const targetPosition = section.getBoundingClientRect().top + window.pageYOffset - headerHeight;

      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // Add highlight effect after scrolling
      setTimeout(highlightSection, 500);
    }
  };
  return (
    <div className="flex flex-col h-full overflow-auto">
      {/* Header */}
      <motion.header
        className="bg-white shadow-sm fixed w-full z-10"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <motion.div
              className="flex-shrink-0 flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <a
                href="#hero"
                onClick={scrollToSection("hero")}
                className="text-2xl font-bold text-green-600 cursor-pointer hover:text-green-700 transition-colors duration-200"
              >
                Carbonix
              </a>
            </motion.div>

            {/* Mobile menu button */}
            <motion.div
              className="sm:hidden flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <button
                onClick={() => {
                  const mobileMenu = document.getElementById('mobile-menu');
                  if (mobileMenu) {
                    mobileMenu.classList.toggle('hidden');
                  }
                }}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-green-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500"
                aria-expanded="false"
              >
                <span className="sr-only">Open main menu</span>
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </motion.div>

            <motion.div
              className="hidden sm:ml-6 sm:flex sm:items-center sm:space-x-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{
                duration: 0.5,
                delay: 0.4,
                staggerChildren: 0.1,
                delayChildren: 0.6
              }}
            >
              <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
                <a
                  href="#features"
                  onClick={scrollToSection("features")}
                  className="text-gray-700 hover:text-green-600 transition-colors duration-200 cursor-pointer"
                >
                  Features
                </a>
              </motion.div>
              <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
                <a
                  href="#how-it-works"
                  onClick={scrollToSection("how-it-works")}
                  className="text-gray-700 hover:text-green-600 transition-colors duration-200 cursor-pointer"
                >
                  How It Works
                </a>
              </motion.div>
              <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
                <a
                  href="#pricing"
                  onClick={scrollToSection("pricing")}
                  className="text-gray-700 hover:text-green-600 transition-colors duration-200 cursor-pointer"
                >
                  Pricing
                </a>
              </motion.div>
              <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
                <Link href="/login">
                  <Button variant="outline" className="transition-all duration-200 hover:shadow-md">Sign In</Button>
                </Link>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
              >
                <Link href="/register">
                  <Button className="transition-all duration-200 hover:shadow-md">Get Started</Button>
                </Link>
              </motion.div>
            </motion.div>
          </div>

          {/* Mobile menu, show/hide based on menu state */}
          <div id="mobile-menu" className="hidden sm:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white shadow-lg rounded-b-lg">
              <a
                href="#features"
                onClick={(e) => {
                  const mobileMenu = document.getElementById('mobile-menu');
                  if (mobileMenu) {
                    mobileMenu.classList.add('hidden');
                  }
                  scrollToSection("features")(e);
                }}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-green-600 hover:bg-gray-50"
              >
                Features
              </a>
              <a
                href="#how-it-works"
                onClick={(e) => {
                  const mobileMenu = document.getElementById('mobile-menu');
                  if (mobileMenu) {
                    mobileMenu.classList.add('hidden');
                  }
                  scrollToSection("how-it-works")(e);
                }}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-green-600 hover:bg-gray-50"
              >
                How It Works
              </a>
              <a
                href="#pricing"
                onClick={(e) => {
                  const mobileMenu = document.getElementById('mobile-menu');
                  if (mobileMenu) {
                    mobileMenu.classList.add('hidden');
                  }
                  scrollToSection("pricing")(e);
                }}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-green-600 hover:bg-gray-50"
              >
                Pricing
              </a>
              <div className="pt-4 pb-3 border-t border-gray-200">
                <div className="flex items-center px-3">
                  <Link href="/login" className="block w-full">
                    <Button variant="outline" className="w-full transition-all duration-200 hover:shadow-md text-sm">Sign In</Button>
                  </Link>
                </div>
                <div className="mt-3 px-3">
                  <Link href="/register" className="block w-full">
                    <Button className="w-full transition-all duration-200 hover:shadow-md text-sm">Get Started</Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Hero Section */}
      <PageTransition animationVariant="fadeIn">
        <main className="flex-grow">
          <div
            id="hero"
            className="py-20 md:py-24 lg:py-28 flex items-center justify-center relative overflow-hidden"
            style={{
              backgroundPosition: `center ${-scrollY * 0.1}px`,
              minHeight: '70vh'
            }}
          >
            {/* Decorative background elements */}
            <motion.div
              className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.1 }}
              transition={{ duration: 1.5 }}
            >
              <div
                className="absolute w-64 h-64 md:w-96 md:h-96 rounded-full bg-green-200 -top-10 -left-10 md:-top-20 md:-left-20"
                style={{ transform: `translateY(${scrollY * 0.1}px)` }}
              />
              <div
                className="absolute w-48 h-48 md:w-64 md:h-64 rounded-full bg-blue-200 bottom-20 right-10 md:bottom-40 md:right-20"
                style={{ transform: `translateY(${scrollY * 0.15}px)` }}
              />
            </motion.div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-1 flex items-center justify-center">
              <motion.div
                className="text-center py-4"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <motion.h1
                  className="text-3xl tracking-tight font-extrabold text-gray-900 sm:text-4xl md:text-5xl"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <motion.span
                    className="block"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                  >
                    Accelerate Your
                  </motion.span>
                  <motion.span
                    className="block text-green-600"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    Sustainability Goals
                  </motion.span>
                </motion.h1>
                <motion.p
                  className="mt-2 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-3 md:text-lg md:max-w-2xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  Secure, transparent carbon credit trading.
                  Meet climate goals, demonstrate ESG leadership, and unlock new value from your carbon strategy—all on one secure, blockchain-verified marketplace.
                </motion.p>

                <motion.div
                  className="mt-4 max-w-lg mx-auto text-left text-sm text-gray-600 sm:text-base grid grid-cols-1 sm:grid-cols-2 gap-3 px-4 sm:px-0"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 text-green-500">•</div>
                    <p className="ml-2">Streamline compliance and voluntary carbon initiatives</p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 text-green-500">•</div>
                    <p className="ml-2">Access premium, verified carbon credits with complete traceability</p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 text-green-500">•</div>
                    <p className="ml-2">Reduce transaction costs and administrative overhead by 40%</p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-5 w-5 text-green-500">•</div>
                    <p className="ml-2">Connect directly with trusted partners in our vetted enterprise network</p>
                  </div>
                </motion.div>
                <motion.div
                  className="mt-6 max-w-md mx-auto flex flex-col sm:flex-row sm:justify-center md:mt-8 px-4 sm:px-0"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                >
                  <motion.div
                    className="rounded-md shadow"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link href="/register">
                      <Button className="w-full px-6 py-2 transition-all duration-200 hover:shadow-lg text-sm">Get Started</Button>
                    </Link>
                  </motion.div>
                  <motion.div
                    className="mt-3 rounded-md shadow sm:mt-0 sm:ml-3 w-full sm:w-auto"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <a
                      href="#how-it-works"
                      onClick={scrollToSection("how-it-works")}
                    >
                      <Button
                        variant="outline"
                        className="w-full px-6 py-2 transition-all duration-200 hover:bg-green-50 text-sm"
                      >
                        Learn More
                      </Button>
                    </a>
                  </motion.div>
                </motion.div>
              </motion.div>
            </div>
          </div>

        {/* Features Section */}
          <div id="features" className="py-12 sm:py-16 bg-white relative" ref={featuresSection.ref}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                className="text-center"
                variants={fadeIn}
                initial="hidden"
                animate={featuresSection.isInView ? "visible" : "hidden"}
                transition={{ duration: 0.5 }}
              >
                <h2 className="text-3xl font-extrabold text-gray-900">
                  Key Features
                </h2>
                <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
                  Everything you need to trade carbon credits efficiently and securely.
                </p>
              </motion.div>

              <motion.div
                className="mt-12 grid gap-6 sm:gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                variants={staggerContainer}
                initial="hidden"
                animate={featuresSection.isInView ? "visible" : "hidden"}
              >
                <motion.div
                  className="bg-gray-50 p-6 rounded-lg cursor-pointer transition-all"
                  variants={featureCardVariants}
                  whileHover="hover"
                >
                  <motion.div
                    className="text-green-600 text-4xl mb-4"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >🌱</motion.div>
                  <h3 className="text-xl font-medium text-gray-900">Self-Onboarding</h3>
                  <p className="mt-2 text-gray-500">
                    Quick and easy onboarding process for enterprises to join the platform.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gray-50 p-6 rounded-lg cursor-pointer transition-all"
                  variants={featureCardVariants}
                  whileHover="hover"
                >
                  <motion.div
                    className="text-green-600 text-4xl mb-4"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >📊</motion.div>
                  <h3 className="text-xl font-medium text-gray-900">Carbon Credit Listing</h3>
                  <p className="mt-2 text-gray-500">
                    List your carbon credits with detailed information and set your price.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gray-50 p-6 rounded-lg cursor-pointer transition-all"
                  variants={featureCardVariants}
                  whileHover="hover"
                >
                  <motion.div
                    className="text-green-600 text-4xl mb-4"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >💰</motion.div>
                  <h3 className="text-xl font-medium text-gray-900">Secure Wallet</h3>
                  <p className="mt-2 text-gray-500">
                    Manage your funds securely with our integrated blockchain wallet.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gray-50 p-6 rounded-lg cursor-pointer transition-all"
                  variants={featureCardVariants}
                  whileHover="hover"
                >
                  <motion.div
                    className="text-green-600 text-4xl mb-4"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >🔄</motion.div>
                  <h3 className="text-xl font-medium text-gray-900">Trade Matching</h3>
                  <p className="mt-2 text-gray-500">
                    Efficient matching of buy and sell orders for carbon credits.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gray-50 p-6 rounded-lg cursor-pointer transition-all"
                  variants={featureCardVariants}
                  whileHover="hover"
                >
                  <motion.div
                    className="text-green-600 text-4xl mb-4"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >📈</motion.div>
                  <h3 className="text-xl font-medium text-gray-900">Analytics & Reporting</h3>
                  <p className="mt-2 text-gray-500">
                    Comprehensive analytics and reports for your carbon trading activities.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gray-50 p-6 rounded-lg cursor-pointer transition-all"
                  variants={featureCardVariants}
                  whileHover="hover"
                >
                  <motion.div
                    className="text-green-600 text-4xl mb-4"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >🔔</motion.div>
                  <h3 className="text-xl font-medium text-gray-900">Notifications</h3>
                  <p className="mt-2 text-gray-500">
                    Stay updated with real-time notifications for all platform activities.
                  </p>
                </motion.div>
              </motion.div>
            </div>
          </div>

        {/* How It Works Section */}
          <div id="how-it-works" className="py-12 sm:py-16 bg-gray-50 relative" ref={howItWorksSection.ref}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                className="text-center"
                variants={fadeIn}
                initial="hidden"
                animate={howItWorksSection.isInView ? "visible" : "hidden"}
              >
                <h2 className="text-3xl font-extrabold text-gray-900">
                  How It Works
                </h2>
                <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
                  A simple process to start trading carbon credits on our platform.
                </p>
              </motion.div>

              <motion.div
                className="mt-12"
                variants={staggerContainer}
                initial="hidden"
                animate={howItWorksSection.isInView ? "visible" : "hidden"}
              >
                <div className="grid gap-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
                  <motion.div
                    className="text-center"
                    variants={fadeIn}
                    whileHover={{ y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 text-xl font-bold"
                      whileHover={{ scale: 1.1, backgroundColor: "#DCFCE7" }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      1
                    </motion.div>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Register</h3>
                    <p className="mt-2 text-gray-500">
                      Create an account and complete the verification process.
                    </p>
                  </motion.div>

                  <motion.div
                    className="text-center"
                    variants={fadeIn}
                    whileHover={{ y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 text-xl font-bold"
                      whileHover={{ scale: 1.1, backgroundColor: "#DCFCE7" }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      2
                    </motion.div>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Onboard</h3>
                    <p className="mt-2 text-gray-500">
                      Set up your organization profile and subscription plan.
                    </p>
                  </motion.div>

                  <motion.div
                    className="text-center"
                    variants={fadeIn}
                    whileHover={{ y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 text-xl font-bold"
                      whileHover={{ scale: 1.1, backgroundColor: "#DCFCE7" }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      3
                    </motion.div>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">List Credits</h3>
                    <p className="mt-2 text-gray-500">
                      Add your carbon credits to the marketplace with details.
                    </p>
                  </motion.div>

                  <motion.div
                    className="text-center"
                    variants={fadeIn}
                    whileHover={{ y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 text-xl font-bold"
                      whileHover={{ scale: 1.1, backgroundColor: "#DCFCE7" }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      4
                    </motion.div>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Trade</h3>
                    <p className="mt-2 text-gray-500">
                      Buy and sell carbon credits with other enterprises.
                    </p>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </div>

        {/* Pricing Section */}
          <div id="pricing" className="py-12 sm:py-16 bg-white relative" ref={pricingSection.ref}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                className="text-center"
                variants={fadeIn}
                initial="hidden"
                animate={pricingSection.isInView ? "visible" : "hidden"}
              >
                <h2 className="text-3xl font-extrabold text-gray-900">
                  Pricing Plans
                </h2>
                <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
                  Choose the plan that best fits your organization's needs.
                </p>
              </motion.div>

              <motion.div
                className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
                variants={staggerContainer}
                initial="hidden"
                animate={pricingSection.isInView ? "visible" : "hidden"}
              >
                <motion.div
                  className="bg-gray-50 p-6 sm:p-8 rounded-lg border border-gray-200 relative z-0"
                  variants={pricingCardVariants}
                  whileHover="hover"
                >
                  <h3 className="text-2xl font-bold text-gray-900">Free</h3>
                  <p className="mt-4 text-gray-500">
                    Basic access for small organizations.
                  </p>
                  <motion.p
                    className="mt-6 text-5xl font-extrabold text-gray-900"
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                  >$0</motion.p>
                  <p className="mt-2 text-gray-500">per month</p>
                  <motion.ul
                    className="mt-6 space-y-4"
                    variants={staggerContainer}
                    initial="hidden"
                    animate="visible"
                  >
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500 }}
                      >✓</motion.span>
                      <span>5 carbon credit listings</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.1 }}
                      >✓</motion.span>
                      <span>Standard transaction fee (1%)</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                      >✓</motion.span>
                      <span>Standard listing fee (0.5%)</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.3 }}
                      >✓</motion.span>
                      <span>Basic analytics</span>
                    </motion.li>
                  </motion.ul>
                  <motion.div
                    className="mt-8"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <Link href="/register">
                      <Button variant="outline" className="w-full transition-all duration-200 hover:bg-green-50">
                        Get Started
                      </Button>
                    </Link>
                  </motion.div>
                </motion.div>

                <motion.div
                  className="bg-green-50 p-6 sm:p-8 rounded-lg border-2 border-green-500 shadow-lg relative z-10"
                  variants={pricingCardVariants}
                  whileHover="hover"
                  initial={{ scale: 1.05, y: -10 }}
                >
                  <motion.div
                    className="absolute -mt-5 ml-5 px-3 py-1 bg-green-500 text-white text-sm font-semibold rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 500, delay: 0.5 }}
                  >
                    Popular
                  </motion.div>
                  <h3 className="text-2xl font-bold text-gray-900">Premium</h3>
                  <p className="mt-4 text-gray-500">
                    Advanced features for growing organizations.
                  </p>
                  <motion.p
                    className="mt-6 text-5xl font-extrabold text-gray-900"
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                  >$99</motion.p>
                  <p className="mt-2 text-gray-500">per month</p>
                  <motion.ul
                    className="mt-6 space-y-4"
                    variants={staggerContainer}
                    initial="hidden"
                    animate="visible"
                  >
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500 }}
                      >✓</motion.span>
                      <span>Unlimited carbon credit listings</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.1 }}
                      >✓</motion.span>
                      <span>Reduced transaction fee (0.5%)</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                      >✓</motion.span>
                      <span>Reduced listing fee (0.25%)</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.3 }}
                      >✓</motion.span>
                      <span>Advanced analytics</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.4 }}
                      >✓</motion.span>
                      <span>Priority support</span>
                    </motion.li>
                  </motion.ul>
                  <motion.div
                    className="mt-8"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <Link href="/register">
                      <Button className="w-full transition-all duration-200 hover:shadow-lg">Get Started</Button>
                    </Link>
                  </motion.div>
                </motion.div>

                <motion.div
                  className="bg-gray-50 p-6 sm:p-8 rounded-lg border border-gray-200 relative z-0"
                  variants={pricingCardVariants}
                  whileHover="hover"
                >
                  <h3 className="text-2xl font-bold text-gray-900">Enterprise</h3>
                  <p className="mt-4 text-gray-500">
                    Custom solutions for large organizations.
                  </p>
                  <motion.p
                    className="mt-6 text-5xl font-extrabold text-gray-900"
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                  >Custom</motion.p>
                  <p className="mt-2 text-gray-500">contact for pricing</p>
                  <motion.ul
                    className="mt-6 space-y-4"
                    variants={staggerContainer}
                    initial="hidden"
                    animate="visible"
                  >
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500 }}
                      >✓</motion.span>
                      <span>All Premium features</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.1 }}
                      >✓</motion.span>
                      <span>Custom transaction fees</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                      >✓</motion.span>
                      <span>API access</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.3 }}
                      >✓</motion.span>
                      <span>Dedicated account manager</span>
                    </motion.li>
                    <motion.li className="flex items-start" variants={fadeIn}>
                      <motion.span
                        className="text-green-500 mr-2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, delay: 0.4 }}
                      >✓</motion.span>
                      <span>Custom integrations</span>
                    </motion.li>
                  </motion.ul>
                  <motion.div
                    className="mt-8"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <Link href="/contact">
                      <Button variant="outline" className="w-full transition-all duration-200 hover:bg-green-50">
                        Contact Sales
                      </Button>
                    </Link>
                  </motion.div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </main>
      </PageTransition>

      {/* Testimonials Section */}
      <motion.div
        className="py-16 bg-gray-50"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-gray-900">
              What Our Clients Say
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              Trusted by leading companies across various industries.
            </p>
          </motion.div>

          <motion.div
            className="grid gap-8 md:grid-cols-3"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.div
              className="bg-white p-6 rounded-lg shadow-md border border-gray-100"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center mb-4">
                <motion.div
                  className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 text-blue-600 font-bold"
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  AC
                </motion.div>
                <div>
                  <h3 className="text-lg font-semibold">AcmeCorp Industries</h3>
                  <p className="text-gray-500 text-sm">Manufacturing Sector</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "Carbonix has transformed how we manage and trade our carbon credits, saving us time and resources while ensuring compliance."
              </p>
              <div className="mt-4 flex text-yellow-400">
                <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
              </div>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-lg shadow-md border border-gray-100"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center mb-4">
                <motion.div
                  className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mr-4 text-green-600 font-bold"
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  EF
                </motion.div>
                <div>
                  <h3 className="text-lg font-semibold">EcoFuture Energy</h3>
                  <p className="text-gray-500 text-sm">Renewable Energy</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "The platform's transparency and security features give us confidence in every transaction. The analytics help us make data-driven decisions."
              </p>
              <div className="mt-4 flex text-yellow-400">
                <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
              </div>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-lg shadow-md border border-gray-100"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center mb-4">
                <motion.div
                  className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center mr-4 text-purple-600 font-bold"
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  GS
                </motion.div>
                <div>
                  <h3 className="text-lg font-semibold">GreenSustain Corp</h3>
                  <p className="text-gray-500 text-sm">Agriculture & Forestry</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "We've increased our carbon credit trading volume by 150% since joining Carbonix, with significantly reduced administrative overhead."
              </p>
              <div className="mt-4 flex text-yellow-400">
                <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* Case Studies Section */}
      <motion.div
        className="py-16 bg-white"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-gray-900">
              Success Stories
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              See how organizations are achieving their sustainability goals with our platform.
            </p>
          </motion.div>

          <motion.div
            className="grid gap-10 md:grid-cols-2"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.div
              className="bg-gray-50 rounded-lg overflow-hidden border border-gray-200"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-2xl font-bold text-gray-900">Global Transport Ltd.</h3>
                  <span className="bg-green-100 text-green-800 text-xs font-semibold px-3 py-1 rounded-full">
                    Transportation
                  </span>
                </div>
                <h4 className="text-lg font-semibold mb-2 text-gray-700">Challenge</h4>
                <p className="text-gray-600 mb-4">
                  Managing carbon credits across 15 countries with different regulations and reporting requirements.
                </p>
                <h4 className="text-lg font-semibold mb-2 text-gray-700">Solution</h4>
                <p className="text-gray-600 mb-4">
                  Implemented Carbonix platform to centralize all carbon credit management and trading activities.
                </p>
                <h4 className="text-lg font-semibold mb-2 text-gray-700">Results</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-600 mb-4">
                  <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    40% reduction in administrative costs
                  </motion.li>
                  <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    25% better rates on carbon credit transactions
                  </motion.li>
                  <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    Simplified compliance reporting across all regions
                  </motion.li>
                </ul>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                >
                  <Link href="/case-studies/global-transport">
                    <Button variant="outline" className="transition-all duration-200">
                      Read Full Case Study
                    </Button>
                  </Link>
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              className="bg-gray-50 rounded-lg overflow-hidden border border-gray-200"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)" }}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-2xl font-bold text-gray-900">TechInnovate Inc.</h3>
                  <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full">
                    Technology
                  </span>
                </div>
                <h4 className="text-lg font-semibold mb-2 text-gray-700">Challenge</h4>
                <p className="text-gray-600 mb-4">
                  Achieving carbon neutrality for data centers while maintaining transparency for stakeholders.
                </p>
                <h4 className="text-lg font-semibold mb-2 text-gray-700">Solution</h4>
                <p className="text-gray-600 mb-4">
                  Used Carbonix to source high-quality carbon credits and provide transparent reporting.
                </p>
                <h4 className="text-lg font-semibold mb-2 text-gray-700">Results</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-600 mb-4">
                  <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    Achieved carbon neutrality 2 years ahead of schedule
                  </motion.li>
                  <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    Created auditable trail of all carbon offset activities
                  </motion.li>
                  <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    Improved ESG ratings by 18% year-over-year
                  </motion.li>
                </ul>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                >
                  <Link href="/case-studies/techinnovate">
                    <Button variant="outline" className="transition-all duration-200">
                      Read Full Case Study
                    </Button>
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* Integration Partners Section */}
      <motion.div
        className="py-16 bg-gray-50"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-gray-900">
              Seamless Integrations
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              Connect Carbonix with your existing tools and platforms.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ staggerChildren: 0.1, delayChildren: 0.3 }}
            viewport={{ once: true }}
          >
            {/* These would typically be actual logos, using placeholders for now */}
            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mb-3 text-blue-600 text-xl">
                <span>S</span>
              </div>
              <span className="font-medium text-gray-900">Salesforce</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-3 text-green-600 text-xl">
                <span>Q</span>
              </div>
              <span className="font-medium text-gray-900">QuickBooks</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center mb-3 text-purple-600 text-xl">
                <span>Z</span>
              </div>
              <span className="font-medium text-gray-900">Zapier</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-3 text-red-600 text-xl">
                <span>G</span>
              </div>
              <span className="font-medium text-gray-900">Google Cloud</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center mb-3 text-yellow-600 text-xl">
                <span>P</span>
              </div>
              <span className="font-medium text-gray-900">Power BI</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center mb-3 text-indigo-600 text-xl">
                <span>M</span>
              </div>
              <span className="font-medium text-gray-900">Microsoft 365</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-cyan-100 flex items-center justify-center mb-3 text-cyan-600 text-xl">
                <span>A</span>
              </div>
              <span className="font-medium text-gray-900">AWS</span>
            </motion.div>

            <motion.div
              className="p-6 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center border border-gray-100"
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.7 }}
              viewport={{ once: true }}
            >
              <div className="h-12 w-12 rounded-full bg-pink-100 flex items-center justify-center mb-3 text-pink-600 text-xl">
                <span>+</span>
              </div>
              <span className="font-medium text-gray-900">Many More</span>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* Security & Compliance Section */}
      <motion.div
        className="py-16 bg-white"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-gray-900">
              Enterprise-Grade Security & Compliance
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              Your data and transactions are protected by industry-leading security measures.
            </p>
          </motion.div>

          <motion.div
            className="grid gap-8 md:grid-cols-2 lg:grid-cols-4"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.div
              className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
            >
              <div className="text-blue-600 text-3xl mb-4">🔒</div>
              <h3 className="text-lg font-semibold text-gray-900">SOC 2 Type II Certified</h3>
              <p className="mt-2 text-gray-600">
                Our platform maintains strict controls for security, availability, and confidentiality.
              </p>
            </motion.div>

            <motion.div
              className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
            >
              <div className="text-blue-600 text-3xl mb-4">🛡️</div>
              <h3 className="text-lg font-semibold text-gray-900">ISO 27001 Compliant</h3>
              <p className="mt-2 text-gray-600">
                We follow international information security management standards.
              </p>
            </motion.div>

            <motion.div
              className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
            >
              <div className="text-blue-600 text-3xl mb-4">🔐</div>
              <h3 className="text-lg font-semibold text-gray-900">Enterprise SSO</h3>
              <p className="mt-2 text-gray-600">
                Seamless integration with your existing identity providers and authentication systems.
              </p>
            </motion.div>

            <motion.div
              className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm"
              variants={fadeIn}
              whileHover={{ y: -5, boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)" }}
            >
              <div className="text-blue-600 text-3xl mb-4">📊</div>
              <h3 className="text-lg font-semibold text-gray-900">Audit Trail</h3>
              <p className="mt-2 text-gray-600">
                Comprehensive logging and audit capabilities for all transactions and system activities.
              </p>
            </motion.div>
          </motion.div>

          <motion.div
            className="mt-12 flex justify-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Link href="/security">
              <Button variant="outline" className="px-8 py-3 transition-all duration-200">
                View Our Security Documentation
              </Button>
            </Link>
          </motion.div>
        </div>
      </motion.div>

      {/* FAQ Section */}
      <motion.div
        className="py-16 bg-gray-50"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-gray-900">
              Frequently Asked Questions
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              Get answers to common questions about Carbonix.
            </p>
          </motion.div>

          <motion.div
            className="space-y-6"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <CollapsibleFaq
              question="How does Carbonix verify carbon credits?"
              answer="We use a combination of blockchain verification, third-party audits, and compliance checks to ensure all carbon credits on our platform meet international standards and regulations."
            />

            <CollapsibleFaq
              question="Can we integrate Carbonix with our existing systems?"
              answer="Yes, Carbonix offers robust API integrations and connects with many popular enterprise systems including ERPs, CRMs, and financial platforms. Our team can assist with custom integration needs."
            />

            <CollapsibleFaq
              question="How long does onboarding typically take?"
              answer="Most organizations can complete basic onboarding within 1-2 weeks. For enterprises with complex requirements or custom integrations, the process may take 3-4 weeks. Our dedicated onboarding team provides support throughout the process."
            />

            <CollapsibleFaq
              question="What kind of reporting capabilities does the platform offer?"
              answer="Carbonix provides comprehensive reporting tools including transaction histories, carbon credit portfolios, compliance reporting, financial analytics, and customizable dashboards. Reports can be exported in various formats and scheduled for automatic delivery."
            />

            <CollapsibleFaq
              question="How does pricing work for larger organizations?"
              answer="For enterprise customers, we offer custom pricing packages based on trading volume, number of users, and specific feature requirements. Our Enterprise plan includes dedicated account management, priority support, and customized training programs."
            />
          </motion.div>

          <motion.div
            className="mt-10 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            viewport={{ once: true }}
          >
            <p className="text-gray-600">
              Still have questions? We're here to help.
            </p>
            <motion.div
              className="mt-4"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              <Link href="/contact">
                <Button variant="outline" className="px-8 py-3 transition-all duration-200">
                  Contact Sales
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* CTA Section */}
      <motion.div
        className="py-16 bg-gradient-to-r from-green-600 to-blue-600 text-white"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h2
            className="text-3xl font-extrabold mb-4"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            Ready to Transform Your Carbon Credit Management?
          </motion.h2>
          <motion.p
            className="text-xl text-green-100 max-w-3xl mx-auto mb-8"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Join leading organizations that are simplifying carbon credit trading while meeting sustainability goals.
          </motion.p>
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/register">
                <Button className="px-8 py-3 bg-white text-green-600 hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl w-full sm:w-auto">
                  Get Started Today
                </Button>
              </Link>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/contact">
                <Button variant="outline" className="px-8 py-3 bg-transparent border-white text-white hover:bg-white/10 transition-all duration-200 w-full sm:w-auto">
                  Schedule a Demo
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* Newsletter Signup */}
      <motion.div
        className="py-16 bg-gray-800"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-8"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-white">
              Stay Updated
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-300 mx-auto">
              Subscribe to our newsletter for the latest carbon market insights and platform updates.
            </p>
          </motion.div>

          <motion.div
            className="max-w-md mx-auto"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <form className="mt-8 sm:flex" onSubmit={(e) => e.preventDefault()}>
              <input
                type="email"
                name="email"
                id="email"
                required
                className="w-full px-5 py-3 placeholder-gray-500 focus:ring-2 focus:ring-green-500 focus:border-green-500 border-gray-300 rounded-md"
                placeholder="Enter your email"
              />
              <motion.div
                className="mt-3 rounded-md sm:mt-0 sm:ml-3 sm:flex-shrink-0"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                <Button className="w-full flex items-center justify-center px-5 py-3">
                  Subscribe
                </Button>
              </motion.div>
            </form>
            <p className="mt-3 text-sm text-gray-400">
              We care about your data. Read our <Link href="/privacy" className="underline hover:text-white">Privacy Policy</Link>.
            </p>
          </motion.div>
        </div>
      </motion.div>

      {/* Contact Form */}
      <motion.div
        className="py-16 bg-white"
        id="contact"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-8"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-extrabold text-gray-900">
              Get in Touch
            </h2>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
              Have questions about our platform? Fill out the form below and we'll get back to you.
            </p>
          </motion.div>

          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <form className="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8" onSubmit={(e) => e.preventDefault()}>
              <div>
                <label htmlFor="first-name" className="block text-sm font-medium text-gray-700">
                  First name
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="first-name"
                    id="first-name"
                    autoComplete="given-name"
                    className="py-3 px-4 block w-full shadow-sm focus:ring-green-500 focus:border-green-500 border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="last-name" className="block text-sm font-medium text-gray-700">
                  Last name
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="last-name"
                    id="last-name"
                    autoComplete="family-name"
                    className="py-3 px-4 block w-full shadow-sm focus:ring-green-500 focus:border-green-500 border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                  Company
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="company"
                    id="company"
                    autoComplete="organization"
                    className="py-3 px-4 block w-full shadow-sm focus:ring-green-500 focus:border-green-500 border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <div className="mt-1">
                  <input
                    id="contact-email"
                    name="contact-email"
                    type="email"
                    autoComplete="email"
                    className="py-3 px-4 block w-full shadow-sm focus:ring-green-500 focus:border-green-500 border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                  Subject
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="subject"
                    id="subject"
                    className="py-3 px-4 block w-full shadow-sm focus:ring-green-500 focus:border-green-500 border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                  Message
                </label>
                <div className="mt-1">
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    className="py-3 px-4 block w-full shadow-sm focus:ring-green-500 focus:border-green-500 border border-gray-300 rounded-md"
                  ></textarea>
                </div>
              </div>
              <div className="sm:col-span-2">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    Send Message
                  </Button>
                </motion.div>
              </div>
            </form>
          </motion.div>
        </div>
      </motion.div>

      {/* Footer */}
      <motion.footer
        className="bg-gray-800"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.5 }}
      >
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <motion.div
            className="grid grid-cols-1 md:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.div variants={fadeIn}>
              <h3 className="text-white text-lg font-semibold">Carbonix</h3>
              <p className="mt-2 text-gray-400 text-sm">
                The B2B platform for carbon credit trading.
              </p>
            </motion.div>

            <motion.div variants={fadeIn}>
              <h3 className="text-white text-sm font-semibold">PLATFORM</h3>
              <motion.ul
                className="mt-4 space-y-2"
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.li variants={fadeIn}>
                  <Link href="#features" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Features
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="#how-it-works" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    How It Works
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="#pricing" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Pricing
                  </Link>
                </motion.li>
              </motion.ul>
            </motion.div>

            <motion.div variants={fadeIn}>
              <h3 className="text-white text-sm font-semibold">COMPANY</h3>
              <motion.ul
                className="mt-4 space-y-2"
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.li variants={fadeIn}>
                  <Link href="/about" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    About Us
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="/contact" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Contact
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="/careers" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Careers
                  </Link>
                </motion.li>
              </motion.ul>
            </motion.div>

            <motion.div variants={fadeIn}>
              <h3 className="text-white text-sm font-semibold">LEGAL</h3>
              <motion.ul
                className="mt-4 space-y-2"
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.li variants={fadeIn}>
                  <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Privacy Policy
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Terms of Service
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="/cookie-policy" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Cookie Policy
                  </Link>
                </motion.li>
                <motion.li variants={fadeIn}>
                  <Link href="/accessibility" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                    Accessibility
                  </Link>
                </motion.li>
              </motion.ul>
            </motion.div>
          </motion.div>

          <motion.div
            className="mt-8 border-t border-gray-700 pt-8 flex flex-col sm:flex-row justify-between items-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.5 }}
          >
            <p className="text-gray-400 text-sm">
              &copy; {new Date().getFullYear()} Carbonix. All rights reserved.
            </p>
            <div className="flex mt-4 sm:mt-0 space-x-6">
              <a href="#" className="text-gray-400 hover:text-white">
                <span className="sr-only">LinkedIn</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.454C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.225 0z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.054 10.054 0 01-3.127 1.184 4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.667 2.476c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
            </div>
          </motion.div>
        </div>
      </motion.footer>
    </div>
  );
}
