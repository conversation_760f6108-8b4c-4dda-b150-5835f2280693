"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { Badge } from "@/components/ui/badge";
import { 
  Wallet, 
  Plus, 
  ArrowUpRight,
  ArrowDownLeft,
  ExternalLink,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { truncateAddress } from "@/lib/utils";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface MultiChainViewClientProps {
  userName: string;
}

interface Wallet {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  walletType: string;
  balance: number;
  isSmartWallet: boolean;
}

interface NetworkInfo {
  id: string;
  name: string;
  icon: string;
  testnet: boolean;
  wallets: Wallet[];
  totalBalance: number;
}

export default function MultiChainViewClient({ userName }: MultiChainViewClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [networks, setNetworks] = useState<NetworkInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeNetwork, setActiveNetwork] = useState<string | null>(null);

  useEffect(() => {
    async function fetchWallets() {
      try {
        setIsLoading(true);
        
        // Fetch all wallets
        const response = await fetch("/api/wallet/all");
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch wallets");
        }
        
        const data = await response.json();
        
        // Group wallets by network
        const networkMap: Record<string, NetworkInfo> = {};
        
        data.wallets.forEach((wallet: Wallet) => {
          const networkKey = `${wallet.network.toLowerCase()}-${wallet.isTestnet ? 'testnet' : 'mainnet'}`;
          
          if (!networkMap[networkKey]) {
            networkMap[networkKey] = {
              id: networkKey,
              name: wallet.network,
              icon: `/icons/${wallet.network.toLowerCase()}.svg`,
              testnet: wallet.isTestnet,
              wallets: [],
              totalBalance: 0,
            };
          }
          
          networkMap[networkKey].wallets.push(wallet);
          networkMap[networkKey].totalBalance += wallet.balance;
        });
        
        const networkList = Object.values(networkMap);
        setNetworks(networkList);
        
        // Set active network to the first one if available
        if (networkList.length > 0 && !activeNetwork) {
          setActiveNetwork(networkList[0].id);
        }
      } catch (error) {
        console.error("Error fetching wallets:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchWallets();
  }, [activeNetwork]);

  const getNetworkBadge = (network: string, isTestnet: boolean) => {
    const networkColors: Record<string, string> = {
      ethereum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      polygon: isTestnet ? "bg-purple-100 text-purple-800" : "bg-purple-700 text-white",
      arbitrum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      optimism: isTestnet ? "bg-red-100 text-red-800" : "bg-red-700 text-white",
      base: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
    };
    
    const color = networkColors[network.toLowerCase()] || "bg-gray-100 text-gray-800";
    
    return (
      <Badge className={color}>
        {network} {isTestnet && "(Testnet)"}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Multi-Chain View"
                description="Manage wallets across multiple blockchains"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Multi-Chain", href: "/wallet/multi-chain", isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Wallets</h3>
                <p className="text-muted-foreground mb-6">{error}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Multi-Chain View"
              description="Manage wallets across multiple blockchains"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Multi-Chain", href: "/wallet/multi-chain", isCurrent: true }
              ]}
            />
            <AnimatedButton
              onClick={() => router.push("/wallet/create")}
              animationVariant="buttonTap"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Wallet
            </AnimatedButton>
          </div>
          
          <div className="space-y-6">
            {networks.length > 0 ? (
              <>
                <Tabs value={activeNetwork || networks[0].id} onValueChange={setActiveNetwork}>
                  <TabsList className="w-full mb-6">
                    {networks.map((network) => (
                      <TabsTrigger key={network.id} value={network.id} className="flex-1">
                        <div className="flex items-center">
                          <img
                            src={network.icon}
                            alt={network.name}
                            className="h-4 w-4 mr-2"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = "/icons/default-chain.svg";
                            }}
                          />
                          {network.name} {network.testnet && "(Testnet)"}
                        </div>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  
                  {networks.map((network) => (
                    <TabsContent key={network.id} value={network.id} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <AnimatedCard>
                          <AnimatedCardHeader>
                            <AnimatedCardTitle>Total Balance</AnimatedCardTitle>
                            <AnimatedCardDescription>
                              Combined balance across all {network.name} wallets
                            </AnimatedCardDescription>
                          </AnimatedCardHeader>
                          <AnimatedCardContent>
                            <div className="text-3xl font-bold">
                              {network.totalBalance.toFixed(6)} {network.name}
                            </div>
                          </AnimatedCardContent>
                        </AnimatedCard>
                        
                        <AnimatedCard>
                          <AnimatedCardHeader>
                            <AnimatedCardTitle>Network</AnimatedCardTitle>
                          </AnimatedCardHeader>
                          <AnimatedCardContent>
                            <div className="flex items-center space-x-2">
                              <img
                                src={network.icon}
                                alt={network.name}
                                className="h-6 w-6"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = "/icons/default-chain.svg";
                                }}
                              />
                              <div>
                                <div className="font-medium">{network.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {network.testnet ? "Testnet" : "Mainnet"}
                                </div>
                              </div>
                            </div>
                          </AnimatedCardContent>
                        </AnimatedCard>
                        
                        <AnimatedCard>
                          <AnimatedCardHeader>
                            <AnimatedCardTitle>Wallets</AnimatedCardTitle>
                          </AnimatedCardHeader>
                          <AnimatedCardContent>
                            <div className="text-3xl font-bold">{network.wallets.length}</div>
                            <div className="text-sm text-muted-foreground">
                              {network.wallets.filter(w => w.isSmartWallet).length} Smart Wallets
                            </div>
                          </AnimatedCardContent>
                        </AnimatedCard>
                      </div>
                      
                      <AnimatedCard>
                        <AnimatedCardHeader>
                          <AnimatedCardTitle>{network.name} Wallets</AnimatedCardTitle>
                          <AnimatedCardDescription>
                            Manage your {network.name} wallets
                          </AnimatedCardDescription>
                        </AnimatedCardHeader>
                        <AnimatedCardContent>
                          <div className="rounded-md border">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="w-[250px]">Wallet</TableHead>
                                  <TableHead>Address</TableHead>
                                  <TableHead>Type</TableHead>
                                  <TableHead>Balance</TableHead>
                                  <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {network.wallets.map((wallet) => (
                                  <TableRow key={wallet.id}>
                                    <TableCell>
                                      <div className="font-medium">{wallet.name || "My Wallet"}</div>
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex items-center space-x-2">
                                        <span className="font-mono">{truncateAddress(wallet.address)}</span>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="h-6 w-6 p-0"
                                          onClick={() => window.open(`https://${wallet.isTestnet ? 'testnet.' : ''}${wallet.network.toLowerCase()}.etherscan.io/address/${wallet.address}`, '_blank')}
                                        >
                                          <ExternalLink className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      {wallet.isSmartWallet ? "Smart Wallet" : "Regular Wallet"}
                                    </TableCell>
                                    <TableCell>
                                      {wallet.balance.toFixed(6)} {network.name}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button variant="ghost" className="h-8 w-8 p-0">
                                            <span className="sr-only">Open menu</span>
                                            <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              viewBox="0 0 24 24"
                                              fill="none"
                                              stroke="currentColor"
                                              strokeWidth="2"
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              className="h-4 w-4"
                                            >
                                              <circle cx="12" cy="12" r="1" />
                                              <circle cx="12" cy="5" r="1" />
                                              <circle cx="12" cy="19" r="1" />
                                            </svg>
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                          <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}`)}>
                                            View Details
                                          </DropdownMenuItem>
                                          <DropdownMenuSeparator />
                                          <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}/send`)}>
                                            <ArrowUpRight className="mr-2 h-4 w-4" />
                                            Send
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}/receive`)}>
                                            <ArrowDownLeft className="mr-2 h-4 w-4" />
                                            Receive
                                          </DropdownMenuItem>
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>
                    </TabsContent>
                  ))}
                </Tabs>
              </>
            ) : (
              <AnimatedCard>
                <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                  <Wallet className="h-10 w-10 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Wallets Found</h3>
                  <p className="text-muted-foreground mb-6">
                    You don't have any wallets yet. Create your first wallet to get started.
                  </p>
                  <AnimatedButton
                    onClick={() => router.push("/wallet/create")}
                    animationVariant="buttonTap"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create Wallet
                  </AnimatedButton>
                </AnimatedCardContent>
              </AnimatedCard>
            )}
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
