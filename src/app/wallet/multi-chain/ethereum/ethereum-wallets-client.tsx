"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { Badge } from "@/components/ui/badge";
import { 
  Wallet, 
  Plus, 
  ArrowUpRight,
  ArrowDownLeft,
  ExternalLink,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { truncateAddress } from "@/lib/utils";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface EthereumWalletsClientProps {
  userName: string;
}

interface Wallet {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  walletType: string;
  balance: number;
  isSmartWallet: boolean;
}

export default function EthereumWalletsClient({ userName }: EthereumWalletsClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [mainnetWallets, setMainnetWallets] = useState<Wallet[]>([]);
  const [testnetWallets, setTestnetWallets] = useState<Wallet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("mainnet");

  useEffect(() => {
    async function fetchEthereumWallets() {
      try {
        setIsLoading(true);
        
        // Fetch Ethereum wallets
        const response = await fetch("/api/wallet/network/ethereum");
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch Ethereum wallets");
        }
        
        const data = await response.json();
        
        // Separate mainnet and testnet wallets
        const mainnet: Wallet[] = [];
        const testnet: Wallet[] = [];
        
        data.wallets.forEach((wallet: Wallet) => {
          if (wallet.isTestnet) {
            testnet.push(wallet);
          } else {
            mainnet.push(wallet);
          }
        });
        
        setMainnetWallets(mainnet);
        setTestnetWallets(testnet);
      } catch (error) {
        console.error("Error fetching Ethereum wallets:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchEthereumWallets();
  }, []);

  const calculateTotalBalance = (wallets: Wallet[]) => {
    return wallets.reduce((total, wallet) => total + wallet.balance, 0);
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Ethereum Wallets"
                description="Manage your Ethereum wallets"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Multi-Chain", href: "/wallet/multi-chain" },
                  { label: "Ethereum", href: "/wallet/multi-chain/ethereum", isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Wallets</h3>
                <p className="text-muted-foreground mb-6">{error}</p>
                <AnimatedButton onClick={() => router.push("/wallet/multi-chain")} animationVariant="buttonTap">
                  Return to Multi-Chain View
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Ethereum Wallets"
              description="Manage your Ethereum wallets"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Multi-Chain", href: "/wallet/multi-chain" },
                { label: "Ethereum", href: "/wallet/multi-chain/ethereum", isCurrent: true }
              ]}
            />
            <AnimatedButton
              onClick={() => router.push("/wallet/create")}
              animationVariant="buttonTap"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Ethereum Wallet
            </AnimatedButton>
          </div>
          
          <div className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full mb-6">
                <TabsTrigger value="mainnet" className="flex-1">
                  <div className="flex items-center">
                    <img
                      src="/icons/ethereum.svg"
                      alt="Ethereum"
                      className="h-4 w-4 mr-2"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "/icons/default-chain.svg";
                      }}
                    />
                    Ethereum Mainnet
                  </div>
                </TabsTrigger>
                <TabsTrigger value="testnet" className="flex-1">
                  <div className="flex items-center">
                    <img
                      src="/icons/ethereum.svg"
                      alt="Ethereum Testnet"
                      className="h-4 w-4 mr-2"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "/icons/default-chain.svg";
                      }}
                    />
                    Ethereum Testnet
                  </div>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="mainnet" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Total Balance</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Combined balance across all Ethereum Mainnet wallets
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="text-3xl font-bold">
                        {calculateTotalBalance(mainnetWallets).toFixed(6)} ETH
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Network</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center space-x-2">
                        <img
                          src="/icons/ethereum.svg"
                          alt="Ethereum"
                          className="h-6 w-6"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = "/icons/default-chain.svg";
                          }}
                        />
                        <div>
                          <div className="font-medium">Ethereum</div>
                          <div className="text-sm text-muted-foreground">Mainnet</div>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Wallets</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="text-3xl font-bold">{mainnetWallets.length}</div>
                      <div className="text-sm text-muted-foreground">
                        {mainnetWallets.filter(w => w.isSmartWallet).length} Smart Wallets
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </div>
                
                {mainnetWallets.length > 0 ? (
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Ethereum Mainnet Wallets</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Manage your Ethereum Mainnet wallets
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-[250px]">Wallet</TableHead>
                              <TableHead>Address</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Balance</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {mainnetWallets.map((wallet) => (
                              <TableRow key={wallet.id}>
                                <TableCell>
                                  <div className="font-medium">{wallet.name || "My Wallet"}</div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center space-x-2">
                                    <span className="font-mono">{truncateAddress(wallet.address)}</span>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 w-6 p-0"
                                      onClick={() => window.open(`https://etherscan.io/address/${wallet.address}`, '_blank')}
                                    >
                                      <ExternalLink className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {wallet.isSmartWallet ? "Smart Wallet" : "Regular Wallet"}
                                </TableCell>
                                <TableCell>
                                  {wallet.balance.toFixed(6)} ETH
                                </TableCell>
                                <TableCell className="text-right">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" className="h-8 w-8 p-0">
                                        <span className="sr-only">Open menu</span>
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          className="h-4 w-4"
                                        >
                                          <circle cx="12" cy="12" r="1" />
                                          <circle cx="12" cy="5" r="1" />
                                          <circle cx="12" cy="19" r="1" />
                                        </svg>
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                      <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}`)}>
                                        View Details
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}/send`)}>
                                        <ArrowUpRight className="mr-2 h-4 w-4" />
                                        Send
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}/receive`)}>
                                        <ArrowDownLeft className="mr-2 h-4 w-4" />
                                        Receive
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                ) : (
                  <AnimatedCard>
                    <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                      <Wallet className="h-10 w-10 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Ethereum Mainnet Wallets</h3>
                      <p className="text-muted-foreground mb-6">
                        You don't have any Ethereum Mainnet wallets yet. Create your first wallet to get started.
                      </p>
                      <AnimatedButton
                        onClick={() => router.push("/wallet/create")}
                        animationVariant="buttonTap"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Create Ethereum Wallet
                      </AnimatedButton>
                    </AnimatedCardContent>
                  </AnimatedCard>
                )}
              </TabsContent>
              
              <TabsContent value="testnet" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Total Balance</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Combined balance across all Ethereum Testnet wallets
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="text-3xl font-bold">
                        {calculateTotalBalance(testnetWallets).toFixed(6)} ETH
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Network</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center space-x-2">
                        <img
                          src="/icons/ethereum.svg"
                          alt="Ethereum Testnet"
                          className="h-6 w-6"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = "/icons/default-chain.svg";
                          }}
                        />
                        <div>
                          <div className="font-medium">Ethereum</div>
                          <div className="text-sm text-muted-foreground">Testnet (Sepolia/Goerli)</div>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Wallets</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="text-3xl font-bold">{testnetWallets.length}</div>
                      <div className="text-sm text-muted-foreground">
                        {testnetWallets.filter(w => w.isSmartWallet).length} Smart Wallets
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </div>
                
                {testnetWallets.length > 0 ? (
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Ethereum Testnet Wallets</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Manage your Ethereum Testnet wallets
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-[250px]">Wallet</TableHead>
                              <TableHead>Address</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Balance</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {testnetWallets.map((wallet) => (
                              <TableRow key={wallet.id}>
                                <TableCell>
                                  <div className="font-medium">{wallet.name || "My Wallet"}</div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center space-x-2">
                                    <span className="font-mono">{truncateAddress(wallet.address)}</span>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 w-6 p-0"
                                      onClick={() => window.open(`https://sepolia.etherscan.io/address/${wallet.address}`, '_blank')}
                                    >
                                      <ExternalLink className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {wallet.isSmartWallet ? "Smart Wallet" : "Regular Wallet"}
                                </TableCell>
                                <TableCell>
                                  {wallet.balance.toFixed(6)} ETH
                                </TableCell>
                                <TableCell className="text-right">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" className="h-8 w-8 p-0">
                                        <span className="sr-only">Open menu</span>
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          className="h-4 w-4"
                                        >
                                          <circle cx="12" cy="12" r="1" />
                                          <circle cx="12" cy="5" r="1" />
                                          <circle cx="12" cy="19" r="1" />
                                        </svg>
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                      <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}`)}>
                                        View Details
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}/send`)}>
                                        <ArrowUpRight className="mr-2 h-4 w-4" />
                                        Send
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => router.push(`/wallet/${wallet.id}/receive`)}>
                                        <ArrowDownLeft className="mr-2 h-4 w-4" />
                                        Receive
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem onClick={() => window.open("https://sepoliafaucet.com/", "_blank")}>
                                        Get Testnet ETH
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                ) : (
                  <AnimatedCard>
                    <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                      <Wallet className="h-10 w-10 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Ethereum Testnet Wallets</h3>
                      <p className="text-muted-foreground mb-6">
                        You don't have any Ethereum Testnet wallets yet. Create your first wallet to get started.
                      </p>
                      <AnimatedButton
                        onClick={() => router.push("/wallet/create")}
                        animationVariant="buttonTap"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Create Ethereum Testnet Wallet
                      </AnimatedButton>
                    </AnimatedCardContent>
                  </AnimatedCard>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
