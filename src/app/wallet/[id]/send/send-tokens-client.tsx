"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { Badge } from "@/components/ui/badge";
import {
  Wallet,
  ArrowUpRight,
  AlertTriangle,
  Loader2,
  Check,
  X,
  Info,
  AlertCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import { truncateAddress } from "@/lib/utils";
import { ValidatedForm } from "@/components/forms/validated-form";
import { sendTokensSchema } from "@/lib/validation";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@/components/ui/alert";

interface SendTokensClientProps {
  userName: string;
  walletId: string;
}

interface WalletData {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  balance: number;
}

interface Token {
  id: string;
  name: string;
  symbol: string;
  tokenAddress: string;
  balance: number;
  decimals: number;
}

type SendFormValues = {
  recipientAddress: string;
  amount: string;
  token: string;
  gasOption: "standard" | "fast" | "instant";
  memo?: string;
};

export default function SendTokensClient({ userName, walletId }: SendTokensClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [tokens, setTokens] = useState<Token[]>([]);
  const [selectedToken, setSelectedToken] = useState<Token | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState(1);
  const [transactionHash, setTransactionHash] = useState<string | null>(null);
  const [estimatedFee, setEstimatedFee] = useState<number | null>(null);
  const [formValues, setFormValues] = useState<SendFormValues | null>(null);
  const [gasOptions, setGasOptions] = useState({
    standard: { price: 0, time: "~5 min" },
    fast: { price: 0, time: "~1 min" },
    instant: { price: 0, time: "~30 sec" },
  });

  useEffect(() => {
    async function fetchWalletAndTokens() {
      try {
        setIsLoading(true);

        // Fetch wallet details
        const walletResponse = await fetch(`/api/wallet/${walletId}`);

        if (!walletResponse.ok) {
          const errorData = await walletResponse.json();
          throw new Error(errorData.error || "Failed to fetch wallet details");
        }

        const walletData = await walletResponse.json();
        setWallet(walletData.wallet);

        // Fetch tokens
        const tokensResponse = await fetch(`/api/wallet/${walletId}/tokens`);

        if (!tokensResponse.ok) {
          const errorData = await tokensResponse.json();
          throw new Error(errorData.error || "Failed to fetch tokens");
        }

        const tokensData = await tokensResponse.json();
        setTokens(tokensData.tokens || []);

        // Fetch gas prices
        const gasPricesResponse = await fetch(`/api/wallet/gas-prices?network=${walletData.wallet.network}&chainId=${walletData.wallet.chainId}`);

        if (gasPricesResponse.ok) {
          const gasPricesData = await gasPricesResponse.json();
          setGasOptions({
            standard: {
              price: gasPricesData.standard,
              time: "~5 min"
            },
            fast: {
              price: gasPricesData.fast,
              time: "~1 min"
            },
            instant: {
              price: gasPricesData.instant,
              time: "~30 sec"
            },
          });
        }
      } catch (error) {
        console.error("Error fetching wallet and tokens:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchWalletAndTokens();
  }, [walletId]);

  // Update selected token when formValues changes
  useEffect(() => {
    if (!formValues) return;

    const tokenValue = formValues.token;

    if (tokenValue === "native") {
      setSelectedToken(null);
    } else {
      const token = tokens.find(t => t.id === tokenValue);
      setSelectedToken(token || null);
    }
  }, [formValues?.token, tokens]);

  // Estimate fee when formValues changes
  useEffect(() => {
    if (!formValues) return;

    const estimateFee = async () => {
      const amount = formValues.amount;
      const tokenValue = formValues.token;
      const gasOption = formValues.gasOption;

      if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
        setEstimatedFee(null);
        return;
      }

      try {
        const response = await fetch(`/api/wallet/${walletId}/estimate-fee`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            amount: parseFloat(amount),
            tokenId: tokenValue === "native" ? null : tokenValue,
            gasOption,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setEstimatedFee(data.fee);
        }
      } catch (error) {
        console.error("Error estimating fee:", error);
      }
    };

    estimateFee();
  }, [formValues?.amount, formValues?.token, formValues?.gasOption, walletId]);

  const onSubmit = async (values: SendFormValues) => {
    // Save form values for use in other components
    setFormValues(values);

    if (step === 1) {
      // Move to confirmation step
      setStep(2);
      return;
    }

    try {
      setIsSending(true);

      const response = await fetch(`/api/wallet/${walletId}/send`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          recipientAddress: values.recipientAddress,
          amount: parseFloat(values.amount),
          tokenId: values.token === "native" ? null : values.token,
          gasOption: values.gasOption,
          memo: values.memo,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send tokens");
      }

      const data = await response.json();
      setTransactionHash(data.transactionHash);
      setStep(3);

      toast({
        title: "Transaction submitted",
        description: "Your transaction has been submitted to the network",
      });
    } catch (error) {
      console.error("Error sending tokens:", error);
      throw new Error(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsSending(false);
    }
  };

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  const handleSetMaxAmount = (setValue: (name: string, value: any) => void) => {
    if (!wallet) return;

    if (selectedToken) {
      setValue("amount", selectedToken.balance.toString());
    } else {
      // For native token, leave a small amount for gas
      const maxAmount = Math.max(0, wallet.balance - 0.01);
      setValue("amount", maxAmount.toString());
    }
  };

  const getNetworkBadge = (network: string, isTestnet: boolean) => {
    const networkColors: Record<string, string> = {
      ethereum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      polygon: isTestnet ? "bg-purple-100 text-purple-800" : "bg-purple-700 text-white",
      arbitrum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      optimism: isTestnet ? "bg-red-100 text-red-800" : "bg-red-700 text-white",
      base: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
    };

    const color = networkColors[network.toLowerCase()] || "bg-gray-100 text-gray-800";

    return (
      <Badge className={color}>
        {network} {isTestnet && "(Testnet)"}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !wallet) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Send Tokens"
                description="Send tokens from your wallet"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Details", href: `/wallet/${walletId}` },
                  { label: "Send", href: `/wallet/${walletId}/send`, isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Wallet</h3>
                <p className="text-muted-foreground mb-6">{error || "Wallet not found"}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Send Tokens"
              description="Send tokens from your wallet"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Details", href: `/wallet/${walletId}` },
                { label: "Send", href: `/wallet/${walletId}/send`, isCurrent: true }
              ]}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>
                    {step === 1 ? "Send Tokens" : step === 2 ? "Confirm Transaction" : "Transaction Submitted"}
                  </AnimatedCardTitle>
                  <AnimatedCardDescription>
                    {step === 1 ? "Enter transaction details" : step === 2 ? "Review and confirm your transaction" : "Your transaction has been submitted"}
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  {step === 1 && (
                    <ValidatedForm
                      schema={sendTokensSchema}
                      defaultValues={{
                        recipientAddress: "",
                        amount: "",
                        token: "native",
                        gasOption: "standard",
                        memo: "",
                      }}
                      onSubmit={onSubmit}
                      className="space-y-6"
                    >
                      {({ control, formState, isSubmitting, formError, setValue, watch }) => (
                        <>
                          {formError && (
                            <Alert variant="destructive">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription>{formError.message}</AlertDescription>
                            </Alert>
                          )}

                          <FormField
                            control={control}
                            name="recipientAddress"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Recipient Address</FormLabel>
                                <FormControl>
                                  <Input placeholder="0x..." {...field} />
                                </FormControl>
                                <FormDescription>
                                  Enter the Ethereum address of the recipient
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={control}
                              name="token"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Token</FormLabel>
                                  <Select
                                    onValueChange={(value) => {
                                      field.onChange(value);
                                      // Update formValues to trigger useEffect
                                      setFormValues({
                                        ...formValues,
                                        token: value,
                                      } as SendFormValues);
                                    }}
                                    defaultValue={field.value}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select token" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="native">
                                        {wallet.network} ({wallet.isTestnet ? "Testnet" : "Mainnet"})
                                      </SelectItem>
                                      {tokens.map((token) => (
                                        <SelectItem key={token.id} value={token.id}>
                                          {token.name} ({token.symbol})
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormDescription>
                                    Select the token you want to send
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={control}
                              name="amount"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Amount</FormLabel>
                                  <div className="flex space-x-2">
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="0.0"
                                        step="any"
                                        {...field}
                                        onChange={(e) => {
                                          field.onChange(e);
                                          // Update formValues to trigger useEffect
                                          setFormValues({
                                            ...formValues,
                                            amount: e.target.value,
                                          } as SendFormValues);
                                        }}
                                      />
                                    </FormControl>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      onClick={() => handleSetMaxAmount(setValue)}
                                    >
                                      Max
                                    </Button>
                                  </div>
                                  <FormDescription>
                                    Available: {selectedToken
                                      ? `${selectedToken.balance} ${selectedToken.symbol}`
                                      : `${wallet.balance} ${wallet.network}`}
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={control}
                            name="gasOption"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Transaction Speed</FormLabel>
                                <div className="grid grid-cols-3 gap-2">
                                  <div
                                    className={`border rounded-md p-3 cursor-pointer ${
                                      field.value === "standard" ? "border-primary bg-primary/5" : "border-input"
                                    }`}
                                    onClick={() => {
                                      field.onChange("standard");
                                      // Update formValues to trigger useEffect
                                      setFormValues({
                                        ...formValues,
                                        gasOption: "standard",
                                      } as SendFormValues);
                                    }}
                                  >
                                    <div className="font-medium">Standard</div>
                                    <div className="text-sm text-muted-foreground">{gasOptions.standard.time}</div>
                                    <div className="text-sm">{gasOptions.standard.price} Gwei</div>
                                  </div>
                                  <div
                                    className={`border rounded-md p-3 cursor-pointer ${
                                      field.value === "fast" ? "border-primary bg-primary/5" : "border-input"
                                    }`}
                                    onClick={() => {
                                      field.onChange("fast");
                                      // Update formValues to trigger useEffect
                                      setFormValues({
                                        ...formValues,
                                        gasOption: "fast",
                                      } as SendFormValues);
                                    }}
                                  >
                                    <div className="font-medium">Fast</div>
                                    <div className="text-sm text-muted-foreground">{gasOptions.fast.time}</div>
                                    <div className="text-sm">{gasOptions.fast.price} Gwei</div>
                                  </div>
                                  <div
                                    className={`border rounded-md p-3 cursor-pointer ${
                                      field.value === "instant" ? "border-primary bg-primary/5" : "border-input"
                                    }`}
                                    onClick={() => {
                                      field.onChange("instant");
                                      // Update formValues to trigger useEffect
                                      setFormValues({
                                        ...formValues,
                                        gasOption: "instant",
                                      } as SendFormValues);
                                    }}
                                  >
                                    <div className="font-medium">Instant</div>
                                    <div className="text-sm text-muted-foreground">{gasOptions.instant.time}</div>
                                    <div className="text-sm">{gasOptions.instant.price} Gwei</div>
                                  </div>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={control}
                            name="memo"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Memo (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="Add a note to this transaction" {...field} />
                                </FormControl>
                                <FormDescription>
                                  This memo is stored locally and not on the blockchain
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="flex justify-end">
                            <AnimatedButton
                              type="submit"
                              animationVariant="buttonTap"
                              disabled={!formState.isValid || isSubmitting}
                            >
                              Review Transaction
                            </AnimatedButton>
                          </div>
                        </>
                      )}
                    </ValidatedForm>
                  )}

                  {step === 2 && (
                    <div className="space-y-6">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Confirm Transaction Details</AlertTitle>
                        <AlertDescription>
                          Please review the transaction details carefully before confirming.
                        </AlertDescription>
                      </Alert>

                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">From</span>
                          <span className="text-sm">{truncateAddress(wallet.address)}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">To</span>
                          <span className="text-sm">{truncateAddress(formValues?.recipientAddress || "")}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Amount</span>
                          <span className="text-sm">
                            {formValues?.amount} {selectedToken ? selectedToken.symbol : wallet.network}
                          </span>
                        </div>
                        <Separator />
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Network</span>
                          <span className="text-sm">
                            {getNetworkBadge(wallet.network, wallet.isTestnet)}
                          </span>
                        </div>
                        <Separator />
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Transaction Speed</span>
                          <span className="text-sm">{formValues?.gasOption}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Estimated Fee</span>
                          <span className="text-sm">
                            {estimatedFee !== null ? `${estimatedFee} ${wallet.network}` : "Calculating..."}
                          </span>
                        </div>
                        {formValues?.memo && (
                          <>
                            <Separator />
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">Memo</span>
                              <span className="text-sm">{formValues.memo}</span>
                            </div>
                          </>
                        )}
                      </div>

                      <div className="flex justify-between">
                        <AnimatedButton
                          type="button"
                          variant="outline"
                          onClick={handleBack}
                          animationVariant="buttonTap"
                        >
                          Back
                        </AnimatedButton>
                        <AnimatedButton
                          type="button"
                          onClick={() => formValues && onSubmit(formValues)}
                          disabled={isSending}
                          animationVariant="buttonTap"
                        >
                          {isSending ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Sending...
                            </>
                          ) : (
                            "Confirm & Send"
                          )}
                        </AnimatedButton>
                      </div>
                    </div>
                  )}

                  {step === 3 && (
                    <div className="flex flex-col items-center justify-center py-6">
                      <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                        <Check className="h-8 w-8 text-green-600" />
                      </div>
                      <h3 className="text-xl font-medium mb-2">Transaction Submitted</h3>
                      <p className="text-muted-foreground text-center mb-6">
                        Your transaction has been submitted to the network and is being processed.
                      </p>

                      {transactionHash && (
                        <div className="w-full mb-6">
                          <div className="flex items-center justify-between bg-muted p-3 rounded-md">
                            <span className="text-sm font-mono">{truncateAddress(transactionHash)}</span>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => navigator.clipboard.writeText(transactionHash)}
                              >
                                Copy
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`https://${wallet.isTestnet ? 'testnet.' : ''}${wallet.network.toLowerCase()}.etherscan.io/tx/${transactionHash}`, '_blank')}
                              >
                                View
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex space-x-4">
                        <AnimatedButton
                          variant="outline"
                          onClick={() => router.push(`/wallet/${walletId}`)}
                          animationVariant="buttonTap"
                        >
                          Back to Wallet
                        </AnimatedButton>
                        <AnimatedButton
                          onClick={() => {
                            setFormValues(null);
                            setStep(1);
                          }}
                          animationVariant="buttonTap"
                        >
                          Send Another
                        </AnimatedButton>
                      </div>
                    </div>
                  )}
                </AnimatedCardContent>
              </AnimatedCard>
            </div>

            <div>
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Wallet Info</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Wallet Address</Label>
                      <div className="mt-1 text-sm font-mono">{truncateAddress(wallet.address)}</div>
                    </div>
                    <div>
                      <Label>Network</Label>
                      <div className="mt-1">{getNetworkBadge(wallet.network, wallet.isTestnet)}</div>
                    </div>
                    <div>
                      <Label>Balance</Label>
                      <div className="mt-1 text-lg font-medium">{wallet.balance} {wallet.network}</div>
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>

              {wallet.isTestnet && (
                <AnimatedCard className="mt-6">
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Testnet Mode</AnimatedCardTitle>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <Alert className="bg-blue-50 border-blue-200">
                      <Info className="h-4 w-4 text-blue-600" />
                      <AlertTitle className="text-blue-600">Testnet Environment</AlertTitle>
                      <AlertDescription className="text-blue-700">
                        This wallet is operating on a testnet. Transactions don't use real funds.
                      </AlertDescription>
                    </Alert>
                    <div className="mt-4">
                      <AnimatedButton
                        variant="outline"
                        className="w-full"
                        onClick={() => window.open(`https://faucet.${wallet.network.toLowerCase()}.io`, '_blank')}
                        animationVariant="buttonTap"
                      >
                        Get Testnet Tokens
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
            </div>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
