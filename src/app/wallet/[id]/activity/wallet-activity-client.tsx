"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  Wallet, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Search, 
  ExternalLink,
  AlertTriangle,
  History,
  Filter,
  Download,
  ArrowUp,
  ArrowDown,
  RefreshCw
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";

interface WalletActivityClientProps {
  userName: string;
  walletId: string;
}

interface Transaction {
  id: string;
  type: string;
  status: string;
  amount: number;
  fee: number;
  transactionHash: string;
  blockNumber: number;
  network: string;
  chainId: number;
  tokenAddress: string | null;
  tokenSymbol: string | null;
  counterpartyAddress: string | null;
  counterpartyName: string | null;
  createdAt: string;
}

interface WalletData {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
}

export default function WalletActivityClient({ userName, walletId }: WalletActivityClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    async function fetchWalletAndTransactions() {
      try {
        setIsLoading(true);
        
        // Fetch wallet details
        const walletResponse = await fetch(`/api/wallet/${walletId}`);
        
        if (!walletResponse.ok) {
          const errorData = await walletResponse.json();
          throw new Error(errorData.error || "Failed to fetch wallet details");
        }
        
        const walletData = await walletResponse.json();
        setWallet(walletData.wallet);
        
        // Fetch transactions
        const queryParams = new URLSearchParams({
          page: currentPage.toString(),
          limit: pageSize.toString(),
          ...(typeFilter !== "all" && { type: typeFilter }),
          ...(statusFilter !== "all" && { status: statusFilter }),
          ...(searchQuery && { search: searchQuery }),
        });
        
        const transactionsResponse = await fetch(`/api/wallet/${walletId}/transactions?${queryParams}`);
        
        if (!transactionsResponse.ok) {
          const errorData = await transactionsResponse.json();
          throw new Error(errorData.error || "Failed to fetch transactions");
        }
        
        const transactionsData = await transactionsResponse.json();
        setTransactions(transactionsData.transactions || []);
        setFilteredTransactions(transactionsData.transactions || []);
        setTotalPages(Math.ceil(transactionsData.totalCount / pageSize));
      } catch (error) {
        console.error("Error fetching wallet and transactions:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchWalletAndTransactions();
  }, [walletId, currentPage, pageSize, typeFilter, statusFilter, searchQuery]);

  const handleSearch = () => {
    setCurrentPage(1);
  };

  const handleReset = () => {
    setSearchQuery("");
    setTypeFilter("all");
    setStatusFilter("all");
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: string) => {
    setPageSize(parseInt(size));
    setCurrentPage(1);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case "DEPOSIT":
        return <ArrowDown className="h-4 w-4 text-green-500" />;
      case "WITHDRAWAL":
        return <ArrowUp className="h-4 w-4 text-red-500" />;
      case "PURCHASE":
        return <ArrowDown className="h-4 w-4 text-green-500" />;
      case "SALE":
        return <ArrowUp className="h-4 w-4 text-red-500" />;
      case "TRANSFER":
        return <ArrowUpRight className="h-4 w-4 text-blue-500" />;
      default:
        return <RefreshCw className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Pending
          </Badge>
        );
      case "FAILED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800">
            {status}
          </Badge>
        );
    }
  };

  const formatAmount = (amount: number, tokenSymbol: string | null) => {
    return `${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    })} ${tokenSymbol || "ETH"}`;
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !wallet) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Wallet Activity"
                description="View transaction history for your wallet"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Details", href: `/wallet/${walletId}` },
                  { label: "Activity", href: `/wallet/${walletId}/activity`, isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Activity</h3>
                <p className="text-muted-foreground mb-6">{error || "Wallet not found"}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Wallet Activity"
              description="View transaction history for your wallet"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Details", href: `/wallet/${walletId}` },
                { label: "Activity", href: `/wallet/${walletId}/activity`, isCurrent: true }
              ]}
            />
            <div className="flex space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <AnimatedButton variant="outline" animationVariant="buttonTap">
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </AnimatedButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    Export as PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search transactions..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Transaction Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="DEPOSIT">Deposit</SelectItem>
                  <SelectItem value="WITHDRAWAL">Withdrawal</SelectItem>
                  <SelectItem value="PURCHASE">Purchase</SelectItem>
                  <SelectItem value="SALE">Sale</SelectItem>
                  <SelectItem value="TRANSFER">Transfer</SelectItem>
                  <SelectItem value="FEE">Fee</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={handleReset}>
                Reset
              </Button>
            </div>
            
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Transaction History</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Recent transactions for this wallet
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                {transactions.length > 0 ? (
                  <>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Type</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Transaction Hash</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {transactions.map((tx) => (
                            <TableRow key={tx.id}>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  {getTransactionTypeIcon(tx.type)}
                                  <span>{tx.type}</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                {format(new Date(tx.createdAt), "MMM d, yyyy HH:mm")}
                              </TableCell>
                              <TableCell>
                                {formatAmount(tx.amount, tx.tokenSymbol)}
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(tx.status)}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-mono">
                                    {tx.transactionHash ? tx.transactionHash.substring(0, 8) + "..." + tx.transactionHash.substring(tx.transactionHash.length - 6) : "N/A"}
                                  </span>
                                  {tx.transactionHash && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 w-6 p-0"
                                      onClick={() => window.open(`https://${wallet.isTestnet ? 'testnet.' : ''}${wallet.network.toLowerCase()}.etherscan.io/tx/${tx.transactionHash}`, '_blank')}
                                    >
                                      <ExternalLink className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => router.push(`/wallet/${walletId}/transactions/${tx.id}`)}
                                >
                                  View Details
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-muted-foreground">
                          Showing
                        </p>
                        <Select
                          value={pageSize.toString()}
                          onValueChange={handlePageSizeChange}
                        >
                          <SelectTrigger className="h-8 w-[70px]">
                            <SelectValue placeholder={pageSize.toString()} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="5">5</SelectItem>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="20">20</SelectItem>
                            <SelectItem value="50">50</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-sm text-muted-foreground">
                          per page
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                        >
                          Previous
                        </Button>
                        <div className="text-sm">
                          Page {currentPage} of {totalPages}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                        >
                          Next
                        </Button>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-6">
                    <History className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No transaction history found</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Transactions will appear here once you start using your wallet
                    </p>
                  </div>
                )}
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
