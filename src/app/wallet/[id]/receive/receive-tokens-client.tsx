"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON>ard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { Badge } from "@/components/ui/badge";
import { 
  Wallet, 
  Copy, 
  AlertTriangle,
  Info,
  Download
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { 
  <PERSON><PERSON>,
  AlertDescription,
  AlertTitle
} from "@/components/ui/alert";

interface ReceiveTokensClientProps {
  userName: string;
  walletId: string;
}

interface WalletData {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  balance: number;
}

export default function ReceiveTokensClient({ userName, walletId }: ReceiveTokensClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("address");
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);

  useEffect(() => {
    async function fetchWalletDetails() {
      try {
        setIsLoading(true);
        
        // Fetch wallet details
        const walletResponse = await fetch(`/api/wallet/${walletId}`);
        
        if (!walletResponse.ok) {
          const errorData = await walletResponse.json();
          throw new Error(errorData.error || "Failed to fetch wallet details");
        }
        
        const walletData = await walletResponse.json();
        setWallet(walletData.wallet);
        
        // Generate QR code
        const qrResponse = await fetch(`/api/wallet/${walletId}/qr-code`);
        
        if (qrResponse.ok) {
          const qrData = await qrResponse.json();
          setQrCodeUrl(qrData.qrCodeUrl);
        }
      } catch (error) {
        console.error("Error fetching wallet details:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchWalletDetails();
  }, [walletId]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Address copied",
      description: "Wallet address copied to clipboard",
    });
  };

  const getNetworkBadge = (network: string, isTestnet: boolean) => {
    const networkColors: Record<string, string> = {
      ethereum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      polygon: isTestnet ? "bg-purple-100 text-purple-800" : "bg-purple-700 text-white",
      arbitrum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      optimism: isTestnet ? "bg-red-100 text-red-800" : "bg-red-700 text-white",
      base: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
    };
    
    const color = networkColors[network.toLowerCase()] || "bg-gray-100 text-gray-800";
    
    return (
      <Badge className={color}>
        {network} {isTestnet && "(Testnet)"}
      </Badge>
    );
  };

  const downloadQrCode = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `wallet-${wallet?.address.substring(0, 8)}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !wallet) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Receive Tokens"
                description="Receive tokens to your wallet"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Details", href: `/wallet/${walletId}` },
                  { label: "Receive", href: `/wallet/${walletId}/receive`, isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Wallet</h3>
                <p className="text-muted-foreground mb-6">{error || "Wallet not found"}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Receive Tokens"
              description="Receive tokens to your wallet"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Details", href: `/wallet/${walletId}` },
                { label: "Receive", href: `/wallet/${walletId}/receive`, isCurrent: true }
              ]}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Receive Tokens</AnimatedCardTitle>
                  <AnimatedCardDescription>
                    Share your wallet address to receive tokens
                  </AnimatedCardDescription>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="w-full mb-6">
                      <TabsTrigger value="address" className="flex-1">Address</TabsTrigger>
                      <TabsTrigger value="qr-code" className="flex-1">QR Code</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="address" className="space-y-6">
                      <div className="flex flex-col items-center">
                        <div className="bg-muted p-6 rounded-lg w-full text-center mb-4">
                          <p className="font-mono text-lg break-all">{wallet.address}</p>
                        </div>
                        <AnimatedButton
                          onClick={() => copyToClipboard(wallet.address)}
                          animationVariant="buttonTap"
                        >
                          <Copy className="mr-2 h-4 w-4" />
                          Copy Address
                        </AnimatedButton>
                      </div>
                      
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Important</AlertTitle>
                        <AlertDescription>
                          Only send {wallet.network} and {wallet.network}-compatible tokens to this address.
                          Sending other types of tokens may result in permanent loss.
                        </AlertDescription>
                      </Alert>
                    </TabsContent>
                    
                    <TabsContent value="qr-code" className="space-y-6">
                      <div className="flex flex-col items-center">
                        {qrCodeUrl ? (
                          <div className="bg-white p-6 rounded-lg mb-4">
                            <img
                              src={qrCodeUrl}
                              alt="Wallet QR Code"
                              className="w-64 h-64"
                            />
                          </div>
                        ) : (
                          <div className="bg-muted p-6 rounded-lg w-64 h-64 flex items-center justify-center mb-4">
                            <p className="text-muted-foreground">QR Code not available</p>
                          </div>
                        )}
                        
                        <div className="flex space-x-4">
                          <AnimatedButton
                            onClick={() => copyToClipboard(wallet.address)}
                            animationVariant="buttonTap"
                          >
                            <Copy className="mr-2 h-4 w-4" />
                            Copy Address
                          </AnimatedButton>
                          
                          {qrCodeUrl && (
                            <AnimatedButton
                              variant="outline"
                              onClick={downloadQrCode}
                              animationVariant="buttonTap"
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download QR
                            </AnimatedButton>
                          )}
                        </div>
                      </div>
                      
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Important</AlertTitle>
                        <AlertDescription>
                          Only send {wallet.network} and {wallet.network}-compatible tokens to this address.
                          Sending other types of tokens may result in permanent loss.
                        </AlertDescription>
                      </Alert>
                    </TabsContent>
                  </Tabs>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>
            
            <div>
              <AnimatedCard>
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Wallet Info</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Wallet Name</Label>
                      <div className="mt-1 text-sm">{wallet.name || "My Wallet"}</div>
                    </div>
                    <div>
                      <Label>Network</Label>
                      <div className="mt-1">{getNetworkBadge(wallet.network, wallet.isTestnet)}</div>
                    </div>
                    <div>
                      <Label>Balance</Label>
                      <div className="mt-1 text-lg font-medium">{wallet.balance} {wallet.network}</div>
                    </div>
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
              
              {wallet.isTestnet && (
                <AnimatedCard className="mt-6">
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Testnet Mode</AnimatedCardTitle>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <Alert className="bg-blue-50 border-blue-200">
                      <Info className="h-4 w-4 text-blue-600" />
                      <AlertTitle className="text-blue-600">Testnet Environment</AlertTitle>
                      <AlertDescription className="text-blue-700">
                        This wallet is operating on a testnet. Transactions don't use real funds.
                      </AlertDescription>
                    </Alert>
                    <div className="mt-4">
                      <AnimatedButton
                        variant="outline"
                        className="w-full"
                        onClick={() => window.open(`https://faucet.${wallet.network.toLowerCase()}.io`, '_blank')}
                        animationVariant="buttonTap"
                      >
                        Get Testnet Tokens
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
              
              <AnimatedCard className="mt-6">
                <AnimatedCardHeader>
                  <AnimatedCardTitle>Supported Tokens</AnimatedCardTitle>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                      {wallet.network} (Native Token)
                    </li>
                    <li className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                      ERC-20 Tokens
                    </li>
                    <li className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                      ERC-721 NFTs
                    </li>
                    <li className="flex items-center">
                      <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                      ERC-1155 Tokens
                    </li>
                  </ul>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
