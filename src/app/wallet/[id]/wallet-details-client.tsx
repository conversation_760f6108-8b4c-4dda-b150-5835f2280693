"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  AnimatedTabs,
  StaggeredListComponent
} from "@/components/ui/animated";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Wallet,
  ArrowUpRight,
  ArrowDownLeft,
  History,
  Copy,
  ExternalLink,
  Shield,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { truncateAddress } from "@/lib/utils";
import { toast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface WalletDetailsClientProps {
  userName: string;
  walletId: string;
}

interface WalletData {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  walletType: string;
  balance: number;
  isSmartWallet: boolean;
  securityScore: number;
  lastSyncedAt: string;
  createdAt: string;
  updatedAt: string;
}

export default function WalletDetailsClient({ userName, walletId }: WalletDetailsClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchWalletDetails() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/wallet/${walletId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch wallet details");
        }

        const data = await response.json();
        setWallet(data.wallet);
      } catch (error) {
        console.error("Error fetching wallet details:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchWalletDetails();
  }, [walletId]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Address copied",
      description: "Wallet address copied to clipboard",
    });
  };

  const getNetworkBadge = (network: string, isTestnet: boolean) => {
    const networkColors: Record<string, string> = {
      ethereum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      polygon: isTestnet ? "bg-purple-100 text-purple-800" : "bg-purple-700 text-white",
      arbitrum: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
      optimism: isTestnet ? "bg-red-100 text-red-800" : "bg-red-700 text-white",
      base: isTestnet ? "bg-blue-100 text-blue-800" : "bg-blue-700 text-white",
    };

    const color = networkColors[network.toLowerCase()] || "bg-gray-100 text-gray-800";

    return (
      <Badge className={color}>
        {network} {isTestnet && "(Testnet)"}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !wallet) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Wallet Details"
                description="View and manage your wallet"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Details", href: `/wallet/${walletId}`, isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Wallet</h3>
                <p className="text-muted-foreground mb-6">{error || "Wallet not found"}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title={wallet.name || `Wallet ${truncateAddress(wallet.address)}`}
              description="View and manage your wallet"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Details", href: `/wallet/${walletId}`, isCurrent: true }
              ]}
            />
            <div className="flex space-x-2">
              <AnimatedButton
                variant="outline"
                onClick={() => router.push(`/wallet/${walletId}/send`)}
                animationVariant="buttonTap"
              >
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Send
              </AnimatedButton>
              <AnimatedButton
                onClick={() => router.push(`/wallet/${walletId}/receive`)}
                animationVariant="buttonTap"
              >
                <ArrowDownLeft className="mr-2 h-4 w-4" />
                Receive
              </AnimatedButton>
            </div>
          </div>

          <div className="space-y-6">
            <AnimatedTabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full mb-4">
                <TabsTrigger value="overview" className="flex-1">Overview</TabsTrigger>
                <TabsTrigger value="tokens" className="flex-1">Tokens</TabsTrigger>
                <TabsTrigger value="nfts" className="flex-1">NFTs</TabsTrigger>
                <TabsTrigger value="activity" className="flex-1">Activity</TabsTrigger>
                <TabsTrigger value="security" className="flex-1">Security</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <StaggeredListComponent className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Wallet Address</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium">{truncateAddress(wallet.address)}</p>
                        <AnimatedButton
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(wallet.address)}
                          animationVariant="buttonTap"
                        >
                          <Copy className="h-4 w-4" />
                        </AnimatedButton>
                        <AnimatedButton
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(`https://${wallet.isTestnet ? 'testnet.' : ''}${wallet.network.toLowerCase()}.etherscan.io/address/${wallet.address}`, '_blank')}
                          animationVariant="buttonTap"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </AnimatedButton>
                      </div>
                      <div className="mt-2">
                        {getNetworkBadge(wallet.network, wallet.isTestnet)}
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Balance</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <p className="text-2xl font-bold">{wallet.balance.toFixed(6)} ETH</p>
                      <p className="text-sm text-muted-foreground">
                        Last updated: {new Date(wallet.lastSyncedAt).toLocaleString()}
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>

                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Wallet Type</AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <p className="text-lg font-medium">
                        {wallet.isSmartWallet ? "Smart Wallet" : "Regular Wallet"}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {wallet.walletType}
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </StaggeredListComponent>

                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Recent Activity</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Recent transactions for this wallet
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="text-center py-6">
                      <History className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No recent transactions</p>
                      <AnimatedButton
                        variant="link"
                        onClick={() => router.push(`/wallet/${walletId}/transactions`)}
                        animationVariant="buttonTap"
                      >
                        View all transactions
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="tokens" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Token Holdings</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Tokens held in this wallet
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="text-center py-6">
                      <p className="text-muted-foreground">No tokens found</p>
                      <AnimatedButton
                        variant="link"
                        onClick={() => router.push(`/wallet/${walletId}/receive`)}
                        animationVariant="buttonTap"
                      >
                        Receive tokens
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="nfts" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>NFT Holdings</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      NFTs held in this wallet
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="text-center py-6">
                      <p className="text-muted-foreground">No NFTs found</p>
                      <AnimatedButton
                        variant="link"
                        onClick={() => router.push(`/wallet/${walletId}/receive`)}
                        animationVariant="buttonTap"
                      >
                        Receive NFTs
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="activity" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Wallet Activity</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Transaction history for this wallet
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="text-center py-6">
                      <History className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No transaction history found</p>
                      <AnimatedButton
                        variant="link"
                        onClick={() => router.push(`/wallet/${walletId}/transactions`)}
                        animationVariant="buttonTap"
                      >
                        View all transactions
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>

              <TabsContent value="security" className="space-y-6">
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Security Settings</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Manage wallet security settings
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Shield className="h-5 w-5 text-muted-foreground" />
                          <span>Security Score</span>
                        </div>
                        <Badge variant={wallet.securityScore > 70 ? "default" : wallet.securityScore > 40 ? "outline" : "destructive"}>
                          {wallet.securityScore}/100
                        </Badge>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Shield className="h-5 w-5 text-muted-foreground" />
                          <span>Two-Factor Authentication</span>
                        </div>
                        <Badge variant="outline" className="bg-red-100 text-red-800">
                          Not Enabled
                        </Badge>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Shield className="h-5 w-5 text-muted-foreground" />
                          <span>Recovery Options</span>
                        </div>
                        <Badge variant="outline" className="bg-red-100 text-red-800">
                          Not Configured
                        </Badge>
                      </div>
                      <div className="pt-4">
                        <AnimatedButton
                          onClick={() => router.push(`/wallet/${walletId}/security`)}
                          animationVariant="buttonTap"
                        >
                          Configure Security Settings
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </TabsContent>
            </AnimatedTabs>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
