"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON>ard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { Badge } from "@/components/ui/badge";
import { 
  Wallet, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Search, 
  ExternalLink,
  AlertTriangle,
  Image
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface NftHoldingsClientProps {
  userName: string;
  walletId: string;
}

interface NFT {
  id: string;
  name: string;
  description: string;
  tokenId: string;
  contractAddress: string;
  imageUrl: string;
  collection: string;
  standard: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
}

interface WalletData {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
}

export default function NftHoldingsClient({ userName, walletId }: NftHoldingsClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [nfts, setNfts] = useState<NFT[]>([]);
  const [filteredNfts, setFilteredNfts] = useState<NFT[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeView, setActiveView] = useState("grid");

  useEffect(() => {
    async function fetchWalletAndNfts() {
      try {
        setIsLoading(true);
        
        // Fetch wallet details
        const walletResponse = await fetch(`/api/wallet/${walletId}`);
        
        if (!walletResponse.ok) {
          const errorData = await walletResponse.json();
          throw new Error(errorData.error || "Failed to fetch wallet details");
        }
        
        const walletData = await walletResponse.json();
        setWallet(walletData.wallet);
        
        // Fetch NFTs
        const nftsResponse = await fetch(`/api/wallet/${walletId}/nfts`);
        
        if (!nftsResponse.ok) {
          const errorData = await nftsResponse.json();
          throw new Error(errorData.error || "Failed to fetch NFTs");
        }
        
        const nftsData = await nftsResponse.json();
        setNfts(nftsData.nfts || []);
        setFilteredNfts(nftsData.nfts || []);
      } catch (error) {
        console.error("Error fetching wallet and NFTs:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchWalletAndNfts();
  }, [walletId]);

  // Filter NFTs based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredNfts(nfts);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = nfts.filter(
      nft => 
        nft.name.toLowerCase().includes(query) || 
        nft.collection.toLowerCase().includes(query) ||
        nft.description.toLowerCase().includes(query)
    );
    
    setFilteredNfts(filtered);
  }, [searchQuery, nfts]);

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !wallet) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="NFT Holdings"
                description="View NFTs in your wallet"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Details", href: `/wallet/${walletId}` },
                  { label: "NFTs", href: `/wallet/${walletId}/nfts`, isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading NFTs</h3>
                <p className="text-muted-foreground mb-6">{error || "Wallet not found"}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="NFT Holdings"
              description="View NFTs in your wallet"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Details", href: `/wallet/${walletId}` },
                { label: "NFTs", href: `/wallet/${walletId}/nfts`, isCurrent: true }
              ]}
            />
            <div className="flex space-x-2">
              <AnimatedButton
                variant="outline"
                onClick={() => router.push(`/wallet/${walletId}/send`)}
                animationVariant="buttonTap"
              >
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Send
              </AnimatedButton>
              <AnimatedButton
                onClick={() => router.push(`/wallet/${walletId}/receive`)}
                animationVariant="buttonTap"
              >
                <ArrowDownLeft className="mr-2 h-4 w-4" />
                Receive
              </AnimatedButton>
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search NFTs..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Tabs value={activeView} onValueChange={setActiveView}>
                <TabsList>
                  <TabsTrigger value="grid">Grid</TabsTrigger>
                  <TabsTrigger value="list">List</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>NFT Holdings</AnimatedCardTitle>
                <AnimatedCardDescription>
                  NFTs held in this wallet
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                {nfts.length > 0 ? (
                  <TabsContent value={activeView} className="mt-0">
                    {activeView === "grid" ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredNfts.map((nft) => (
                          <AnimatedCard key={nft.id} className="overflow-hidden">
                            <div className="aspect-square relative bg-muted">
                              {nft.imageUrl ? (
                                <img
                                  src={nft.imageUrl}
                                  alt={nft.name}
                                  className="object-cover w-full h-full"
                                />
                              ) : (
                                <div className="flex items-center justify-center w-full h-full">
                                  <Image className="h-16 w-16 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                            <AnimatedCardHeader className="pb-2">
                              <AnimatedCardTitle className="text-lg">{nft.name}</AnimatedCardTitle>
                              <p className="text-sm text-muted-foreground">{nft.collection}</p>
                            </AnimatedCardHeader>
                            <AnimatedCardContent>
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium">Token ID</span>
                                <span className="text-sm">{nft.tokenId}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Standard</span>
                                <Badge variant="outline">{nft.standard}</Badge>
                              </div>
                            </AnimatedCardContent>
                          </AnimatedCard>
                        ))}
                      </div>
                    ) : (
                      <div className="rounded-md border">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="h-12 px-4 text-left align-middle font-medium">NFT</th>
                              <th className="h-12 px-4 text-left align-middle font-medium">Collection</th>
                              <th className="h-12 px-4 text-left align-middle font-medium">Token ID</th>
                              <th className="h-12 px-4 text-left align-middle font-medium">Standard</th>
                            </tr>
                          </thead>
                          <tbody>
                            {filteredNfts.map((nft) => (
                              <tr key={nft.id} className="border-b">
                                <td className="p-4 align-middle">
                                  <div className="flex items-center space-x-3">
                                    <div className="h-10 w-10 rounded overflow-hidden bg-muted">
                                      {nft.imageUrl ? (
                                        <img
                                          src={nft.imageUrl}
                                          alt={nft.name}
                                          className="object-cover w-full h-full"
                                        />
                                      ) : (
                                        <div className="flex items-center justify-center w-full h-full">
                                          <Image className="h-5 w-5 text-muted-foreground" />
                                        </div>
                                      )}
                                    </div>
                                    <div>
                                      <div className="font-medium">{nft.name}</div>
                                    </div>
                                  </div>
                                </td>
                                <td className="p-4 align-middle">{nft.collection}</td>
                                <td className="p-4 align-middle">{nft.tokenId}</td>
                                <td className="p-4 align-middle">
                                  <Badge variant="outline">{nft.standard}</Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </TabsContent>
                ) : (
                  <div className="text-center py-6">
                    <Image className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No NFTs found</p>
                    <AnimatedButton
                      variant="link"
                      onClick={() => router.push(`/wallet/${walletId}/receive`)}
                      animationVariant="buttonTap"
                    >
                      Receive NFTs
                    </AnimatedButton>
                  </div>
                )}
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
