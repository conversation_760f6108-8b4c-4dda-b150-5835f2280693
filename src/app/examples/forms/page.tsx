"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { LoginFormExample, RegistrationFormExample, OrganizationFormExample } from "@/components/forms/examples";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

/**
 * Form Examples Page
 * 
 * This page showcases example form implementations using the validation library.
 */
export default function FormExamplesPage() {
  const [activeTab, setActiveTab] = useState("login");

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-5xl mx-auto">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Form Validation Examples</CardTitle>
            <CardDescription>
              These examples demonstrate how to use the validation library to create forms
              with consistent validation and error handling.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              The validation library provides standardized validation schemas, utilities, and hooks
              for form validation. These examples show how to use the library to create forms with
              consistent validation and error handling.
            </p>
            <p className="text-muted-foreground">
              Note: These forms are fully functional but will not actually submit data in this example page.
            </p>
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 w-full mb-8">
            <TabsTrigger value="login">Login Form</TabsTrigger>
            <TabsTrigger value="registration">Registration Form</TabsTrigger>
            <TabsTrigger value="organization">Organization Form</TabsTrigger>
          </TabsList>

          <TabsContent value="login" className="mt-0">
            <div className="flex flex-col items-center">
              <div className="w-full max-w-md">
                <LoginFormExample />
              </div>
              <div className="mt-8 w-full">
                <Card>
                  <CardHeader>
                    <CardTitle>Login Form Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>Email validation with proper error messages</li>
                      <li>Password validation with proper error messages</li>
                      <li>Form-level error handling for authentication errors</li>
                      <li>Loading state handling during form submission</li>
                      <li>Consistent styling and error display</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="registration" className="mt-0">
            <div className="flex flex-col items-center">
              <div className="w-full max-w-md">
                <RegistrationFormExample />
              </div>
              <div className="mt-8 w-full">
                <Card>
                  <CardHeader>
                    <CardTitle>Registration Form Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>Name validation with proper error messages</li>
                      <li>Email validation with proper error messages</li>
                      <li>Password validation with proper error messages</li>
                      <li>Password confirmation validation</li>
                      <li>Form-level error handling for registration errors</li>
                      <li>Loading state handling during form submission</li>
                      <li>Consistent styling and error display</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="organization" className="mt-0">
            <div className="flex flex-col items-center">
              <div className="w-full">
                <OrganizationFormExample />
              </div>
              <div className="mt-8 w-full">
                <Card>
                  <CardHeader>
                    <CardTitle>Organization Form Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>Multi-step form with tabs for better organization</li>
                      <li>Required and optional fields clearly marked</li>
                      <li>Validation for all fields with proper error messages</li>
                      <li>Different input types (text, number, select, textarea)</li>
                      <li>Form-level error handling for API errors</li>
                      <li>Loading state handling during form submission</li>
                      <li>Consistent styling and error display</li>
                      <li>Responsive layout for different screen sizes</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
