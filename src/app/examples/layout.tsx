import { Metadata } from "next";
import { AppHeader } from "@/components/ui/app-header";

export const metadata: Metadata = {
  title: "Examples - Carbonix",
  description: "Example components and features of the Carbonix platform",
};

export default function ExamplesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="flex flex-col gap-6 p-4 min-h-screen">
      <AppHeader title="Examples" description="Example components and features of the Carbonix platform" />
      <section className="w-full flex flex-col gap-2 flex-grow">
        {children}
      </section>
    </main>
  );
}
