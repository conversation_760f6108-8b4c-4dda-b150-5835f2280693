    "use client";

import { PageTransition } from "@/components/ui/animated";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { motion } from "framer-motion";

export default function ContactPage() {
  return (
    <PageTransition animationVariant="fadeIn">
      <div className="bg-white">
        <div className="relative overflow-hidden bg-gradient-to-b from-green-50 to-blue-50 pt-16 pb-24">
          <div className="relative">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mx-auto max-w-2xl lg:mx-0">
                <motion.h1
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  Contact Us
                </motion.h1>
                <motion.p
                  className="mt-6 text-lg leading-8 text-gray-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  We're here to help with any questions about our carbon credit trading platform. Reach out to our team for support, demos, or partnership inquiries.
                </motion.p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form and Info */}
        <div className="relative bg-white">
          <div className="absolute inset-0">
            <div className="absolute inset-y-0 left-0 w-1/2 bg-gray-50"></div>
          </div>
          <div className="relative mx-auto max-w-7xl lg:grid lg:grid-cols-5">
            <motion.div 
              className="bg-gray-50 py-16 px-4 sm:px-6 lg:col-span-2 lg:px-8 xl:pr-12"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="mx-auto max-w-lg">
                <h2 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">Get in touch</h2>
                <p className="mt-3 text-lg leading-6 text-gray-500">
                  Have questions about Carbonix? We're here to help you navigate the world of carbon credits.
                </p>
                <dl className="mt-8 text-base text-gray-500">
                  <div className="mt-6">
                    <dt className="sr-only">Physical address</dt>
                    <dd className="flex">
                      <svg className="h-6 w-6 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z" />
                      </svg>
                      <div className="ml-3">
                        <p>123 Green Street</p>
                        <p>Suite 100</p>
                        <p>San Francisco, CA 94107</p>
                      </div>
                    </dd>
                  </div>
                  <div className="mt-6">
                    <dt className="sr-only">Phone number</dt>
                    <dd className="flex">
                      <svg className="h-6 w-6 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                      </svg>
                      <span className="ml-3">+****************</span>
                    </dd>
                  </div>
                  <div className="mt-6">
                    <dt className="sr-only">Email</dt>
                    <dd className="flex">
                      <svg className="h-6 w-6 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                      </svg>
                      <span className="ml-3"><EMAIL></span>
                    </dd>
                  </div>
                </dl>
                <p className="mt-6 text-base text-gray-500">
                  Looking for careers?{' '}
                  <Link href="/careers" className="font-medium text-green-600 hover:text-green-500">
                    View all job openings
                  </Link>
                </p>
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900">Our Office Hours</h3>
                  <p className="mt-2 text-base text-gray-500">
                    Monday - Friday: 9AM - 5PM PST<br />
                    Saturday - Sunday: Closed
                  </p>
                </div>
              </div>
            </motion.div>
            <motion.div 
              className="bg-white py-16 px-4 sm:px-6 lg:col-span-3 lg:py-24 lg:px-8 xl:pl-12"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="mx-auto max-w-lg lg:max-w-none">
                <form className="grid grid-cols-1 gap-y-6" onSubmit={(e) => e.preventDefault()}>
                  <div>
                    <label htmlFor="full-name" className="block text-sm font-medium text-gray-700">
                      Full name
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="full-name"
                        id="full-name"
                        autoComplete="name"
                        className="block w-full rounded-md border-gray-300 py-3 px-4 shadow-sm focus:border-green-500 focus:ring-green-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                      Company
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="company"
                        id="company"
                        autoComplete="organization"
                        className="block w-full rounded-md border-gray-300 py-3 px-4 shadow-sm focus:border-green-500 focus:ring-green-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Email
                    </label>
                    <div className="mt-1">
                      <input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        className="block w-full rounded-md border-gray-300 py-3 px-4 shadow-sm focus:border-green-500 focus:ring-green-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                      Phone
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="phone"
                        id="phone"
                        autoComplete="tel"
                        className="block w-full rounded-md border-gray-300 py-3 px-4 shadow-sm focus:border-green-500 focus:ring-green-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="inquiry-type" className="block text-sm font-medium text-gray-700">
                      Inquiry Type
                    </label>
                    <div className="mt-1">
                      <select
                        id="inquiry-type"
                        name="inquiry-type"
                        className="block w-full rounded-md border-gray-300 py-3 px-4 shadow-sm focus:border-green-500 focus:ring-green-500"
                      >
                        <option>General Information</option>
                        <option>Platform Demo</option>
                        <option>Partnership Inquiry</option>
                        <option>Technical Support</option>
                        <option>Other</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                      Message
                    </label>
                    <div className="mt-1">
                      <textarea
                        id="message"
                        name="message"
                        rows={5}
                        className="block w-full rounded-md border-gray-300 py-3 px-4 shadow-sm focus:border-green-500 focus:ring-green-500"
                      ></textarea>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="privacy-policy"
                      name="privacy-policy"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                    <label htmlFor="privacy-policy" className="ml-2 block text-sm text-gray-900">
                      By submitting this form, I agree to the{' '}
                      <Link href="/privacy" className="text-green-600 hover:text-green-500">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>
                  <div>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button className="w-full inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Submit
                      </Button>
                    </motion.div>
                  </div>
                </form>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-12 mb-24 bg-gray-50 py-12">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="mx-auto text-center">
              <h2 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">Our Location</h2>
              <p className="mt-3 text-lg leading-6 text-gray-500 mb-8">
                Visit our headquarters in San Francisco
              </p>
            </div>
            {/* Map Placeholder - In a real application, you would integrate a map service like Google Maps */}
            <div className="h-96 w-full rounded-lg bg-gray-200 flex items-center justify-center border border-gray-300">
              <div className="text-center">
                <span className="text-4xl mb-4 block">🗺️</span>
                <p className="text-gray-600">Interactive map would be displayed here</p>
                <p className="text-gray-500 text-sm mt-2">(Using Google Maps or similar service)</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}