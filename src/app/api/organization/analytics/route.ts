import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { format, subDays, subMonths, subYears, startOfMonth, endOfMonth } from "date-fns";

/**
 * GET /api/organization/analytics
 * Get organization analytics data
 */
async function getOrganizationAnalyticsHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access analytics",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to access analytics",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = session.user.organizationId;

    // Get URL parameters
    const url = new URL(req.url);
    const timeRange = url.searchParams.get("timeRange") || "30d";

    // Calculate date range
    let startDate: Date;
    const endDate = new Date();

    switch (timeRange) {
      case "7d":
        startDate = subDays(endDate, 7);
        break;
      case "30d":
        startDate = subDays(endDate, 30);
        break;
      case "90d":
        startDate = subDays(endDate, 90);
        break;
      case "1y":
        startDate = subYears(endDate, 1);
        break;
      case "all":
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate = subDays(endDate, 30);
    }

    // Get carbon credit analytics
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        vintage: true,
        standard: true,
        status: true,
        verificationStatus: true,
        quantity: true,
        availableQuantity: true,
        price: true,
        createdAt: true,
      },
    });

    // Get transaction analytics with related order information
    const transactions = await db.transaction.findMany({
      where: {
        order: {
          OR: [
            { sellerId: organizationId },
            { buyerId: organizationId }
          ]
        },
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        type: true,
        amount: true,
        fee: true,
        status: true,
        createdAt: true,
        order: {
          select: {
            id: true,
            sellerId: true,
            buyerId: true,
            quantity: true
          }
        }
      },
    });

    // Get billing records for revenue analytics
    const billingRecords = await db.billingRecord.findMany({
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        amount: true,
        type: true,
        status: true,
        createdAt: true,
      },
    });

    // Process carbon credit data
    const totalCarbonCredits = carbonCredits.length;
    const verifiedCarbonCredits = carbonCredits.filter(
      (credit) => credit.verificationStatus === "VERIFIED"
    ).length;
    const listedCarbonCredits = carbonCredits.filter(
      (credit) => credit.status === "LISTED"
    ).length;
    const soldCarbonCredits = carbonCredits.filter(
      (credit) => credit.status === "SOLD"
    ).length;
    const retiredCarbonCredits = carbonCredits.filter(
      (credit) => credit.status === "RETIRED"
    ).length;
    const totalVolume = carbonCredits.reduce(
      (sum, credit) => sum + credit.quantity,
      0
    );

    // Group carbon credits by vintage
    const byVintage = carbonCredits.reduce((acc, credit) => {
      const vintage = credit.vintage;
      const existingEntry = acc.find((entry) => entry.vintage === vintage);
      
      if (existingEntry) {
        existingEntry.count += 1;
      } else {
        acc.push({ vintage, count: 1 });
      }
      
      return acc;
    }, [] as { vintage: number; count: number }[]);

    // Group carbon credits by standard
    const byStandard = carbonCredits.reduce((acc, credit) => {
      const standard = credit.standard;
      const existingEntry = acc.find((entry) => entry.standard === standard);
      
      if (existingEntry) {
        existingEntry.count += 1;
      } else {
        acc.push({ standard, count: 1 });
      }
      
      return acc;
    }, [] as { standard: string; count: number }[]);

    // Group carbon credits by status
    const byStatus = carbonCredits.reduce((acc, credit) => {
      const status = credit.status;
      const existingEntry = acc.find((entry) => entry.status === status);
      
      if (existingEntry) {
        existingEntry.count += 1;
      } else {
        acc.push({ status, count: 1 });
      }
      
      return acc;
    }, [] as { status: string; count: number }[]);

    // Process transaction data
    const totalTransactions = transactions.length;
    const transactionVolume = transactions.reduce(
      (sum, tx) => sum + tx.amount,
      0
    );
    const transactionFees = transactions.reduce(
      (sum, tx) => sum + tx.fee,
      0
    );

    // Group transactions by date
    const byDate = transactions.reduce((acc, tx) => {
      const date = format(new Date(tx.createdAt), "yyyy-MM-dd");
      const existingEntry = acc.find((entry) => entry.date === date);
      
      if (existingEntry) {
        existingEntry.count += 1;
        existingEntry.volume += tx.amount;
      } else {
        acc.push({ date, count: 1, volume: tx.amount });
      }
      
      return acc;
    }, [] as { date: string; count: number; volume: number }[]);

    // Group transactions by type
    const byType = transactions.reduce((acc, tx) => {
      const type = tx.type;
      const existingEntry = acc.find((entry) => entry.type === type);
      
      if (existingEntry) {
        existingEntry.count += 1;
        existingEntry.volume += tx.amount;
      } else {
        acc.push({ type, count: 1, volume: tx.amount });
      }
      
      return acc;
    }, [] as { type: string; count: number; volume: number }[]);

    // Process revenue data - only consider transactions where this organization is the seller
    const totalRevenue = transactions
      .filter((tx) => tx.order?.sellerId === organizationId)
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Group revenue by month
    const byMonth = Array.from({ length: 12 }, (_, i) => {
      const date = subMonths(endDate, i);
      const monthStart = startOfMonth(date);
      const monthEnd = endOfMonth(date);
      const monthName = format(date, "MMM yyyy");
      
      const amount = transactions
        .filter(
          (tx) =>
            tx.order?.sellerId === organizationId &&
            new Date(tx.createdAt) >= monthStart &&
            new Date(tx.createdAt) <= monthEnd
        )
        .reduce((sum, tx) => sum + tx.amount, 0);
      
      return { month: monthName, amount };
    }).reverse();

    // Group revenue by source based on transaction type
    const bySource = [
      {
        source: "Sales",
        amount: transactions
          .filter(
            (tx) => tx.order?.sellerId === organizationId && tx.type === "SALE"
          )
          .reduce((sum, tx) => sum + tx.amount, 0),
      },
      {
        source: "Purchases",
        amount: transactions
          .filter(
            (tx) => tx.order?.sellerId === organizationId && tx.type === "PURCHASE"
          )
          .reduce((sum, tx) => sum + tx.amount, 0),
      },
      {
        source: "Fees",
        amount: transactions
          .filter(
            (tx) => tx.order?.sellerId === organizationId && tx.type === "FEE"
          )
          .reduce((sum, tx) => sum + tx.amount, 0),
      },
      {
        source: "Other",
        amount: transactions
          .filter(
            (tx) =>
              tx.order?.sellerId === organizationId &&
              tx.type !== "SALE" &&
              tx.type !== "PURCHASE" &&
              tx.type !== "FEE"
          )
          .reduce((sum, tx) => sum + tx.amount, 0),
      },
    ];

    // Compile analytics data
    const analyticsData = {
      carbonCredits: {
        total: totalCarbonCredits,
        verified: verifiedCarbonCredits,
        listed: listedCarbonCredits,
        sold: soldCarbonCredits,
        retired: retiredCarbonCredits,
        totalVolume,
        byVintage,
        byStandard,
        byStatus,
      },
      transactions: {
        total: totalTransactions,
        volume: transactionVolume,
        fees: transactionFees,
        byDate,
        byType,
      },
      revenue: {
        total: totalRevenue,
        byMonth,
        bySource,
      },
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    logger.error("Error getting organization analytics:", error);
    throw new ApiError(
      "An error occurred while getting organization analytics",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getOrganizationAnalyticsHandler);
