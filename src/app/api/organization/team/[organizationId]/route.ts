import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType, InvitationStatus, UserRole } from "@prisma/client";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for updating a team member
const updateSchema = z.object({
  role: z.enum(["ORGANIZATION_ADMIN", "ORGANIZATION_MEMBER"]),
});

/**
 * PATCH /api/organization/team/[organizationId]
 * Update a team member's role
 */
async function updateTeamMemberHandler(
  req: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update team members",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to update team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  // Check if user has permission to update team members
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to update team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const memberId = params.organizationId;
    const organizationId = session.user.organizationId;

    // Parse and validate request body
    const body = await req.json();
    const { role } = updateSchema.parse(body);

    // Get organization details for notifications
    const organization = await db.organization.findUnique({
      where: { id: session.user.organizationId },
      select: { name: true },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Check if trying to update self
    if (memberId === session.user.id) {
      throw new ApiError(
        "You cannot update your own role",
        ErrorType.FORBIDDEN,
        403
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create query with tenant isolation
    let memberQuery = {
      where: {
        id: memberId,
        organizationId,
      },
    };

    // Apply tenant isolation
    memberQuery = withTenantIsolation(memberQuery, tenantContext);

    // Check if member exists and belongs to the organization
    const member = await db.user.findFirst(memberQuery);

    if (!member) {
      // Check if it's a pending invitation
      let invitationQuery = {
        where: {
          id: memberId,
          organizationId,
          status: InvitationStatus.PENDING,
        },
      };

      // Apply tenant isolation
      invitationQuery = withTenantIsolation(invitationQuery, tenantContext);

      const invitation = await db.invitation.findFirst(invitationQuery);

      if (invitation) {
        // Update invitation role
        const updatedInvitation = await db.invitation.update({
          where: { id: memberId },
          data: {
            role: role === "ORGANIZATION_ADMIN" ? UserRole.ORGANIZATION_ADMIN : UserRole.USER,
          },
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: AuditLogType.USER_UPDATED,
            description: `Invitation role updated for ${invitation.email} from ${invitation.role} to ${role}`,
            userId: session.user.id,
            organizationId,
            metadata: {
              invitationId: invitation.id,
              email: invitation.email,
              previousRole: invitation.role,
              newRole: role,
            },
          },
        });

        logger.info(`Invitation role updated for ${invitation.email} in organization ${organizationId}`);

        return NextResponse.json({
          message: "Invitation role updated successfully",
          invitation: updatedInvitation,
        });
      }

      throw new ApiError("Team member not found", ErrorType.NOT_FOUND, 404);
    }

    // Update member role
    const updatedMember = await db.user.update({
      where: { id: memberId },
      data: {
        role: role === "ORGANIZATION_ADMIN" ? UserRole.ORGANIZATION_ADMIN : UserRole.USER,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.USER_UPDATED,
        description: `User role updated for ${member.name} from ${member.role} to ${role}`,
        userId: session.user.id,
        organizationId,
        metadata: {
          userId: member.id,
          previousRole: member.role,
          newRole: role,
        },
      },
    });

    // Create notification for the updated user
    await db.notification.create({
      data: {
        title: "Role Updated",
        message: `Your role in ${organization.name} has been updated to ${role === "ORGANIZATION_ADMIN" ? "Admin" : "Member"}`,
        type: "SYSTEM",
        userId: memberId,
      },
    });

    logger.info(`User role updated for ${member.name} in organization ${organizationId}`);

    return NextResponse.json({
      message: "Team member role updated successfully",
      member: updatedMember,
    });
  } catch (error) {
    logger.error("Error updating team member:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while updating team member",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/organization/team/[organizationId]
 * Remove a team member from the organization
 */
async function removeTeamMemberHandler(
  req: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to remove team members",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to remove team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  // Check if user has permission to remove team members
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to remove team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const memberId = params.organizationId;
    const organizationId = session.user.organizationId;

    // Get organization details for notifications
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { name: true },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Check if trying to remove self
    if (memberId === session.user.id) {
      throw new ApiError(
        "You cannot remove yourself from the organization",
        ErrorType.FORBIDDEN,
        403
      );
    }

    // Check if member exists and belongs to the organization
    const member = await db.user.findFirst({
      where: {
        id: memberId,
        organizationId,
      },
    });

    if (!member) {
      // Check if it's a pending invitation
      const invitation = await db.invitation.findFirst({
        where: {
          id: memberId,
          organizationId,
        },
      });

      if (invitation) {
        // Delete invitation
        await db.invitation.delete({
          where: { id: memberId },
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: AuditLogType.USER_DELETED,
            description: `Invitation cancelled for ${invitation.email}`,
            userId: session.user.id,
            organizationId,
            metadata: {
              invitationId: invitation.id,
              email: invitation.email,
              role: invitation.role,
            },
          },
        });

        logger.info(`Invitation cancelled for ${invitation.email} in organization ${organizationId}`);

        return NextResponse.json({
          message: "Invitation cancelled successfully",
        });
      }

      throw new ApiError("Team member not found", ErrorType.NOT_FOUND, 404);
    }

    // Store member details for audit log
    const memberDetails = {
      id: member.id,
      name: member.name,
      email: member.email,
      role: member.role,
    };

    // Remove member from organization
    const updatedMember = await db.user.update({
      where: { id: memberId },
      data: {
        organizationId: null,
        role: "USER",
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.USER_DELETED,
        description: `User ${member.name} removed from organization`,
        userId: session.user.id,
        organizationId,
        metadata: memberDetails,
      },
    });

    // Create notification for the removed user
    await db.notification.create({
      data: {
        title: "Removed from Organization",
        message: `You have been removed from ${organization.name}`,
        type: "SYSTEM",
        priority: "HIGH",
        userId: memberId,
      },
    });

    logger.info(`User ${member.name} removed from organization ${organizationId}`);

    return NextResponse.json({
      message: "Team member removed successfully",
    });
  } catch (error) {
    logger.error("Error removing team member:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while removing team member",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling and resource isolation
const wrappedPatchHandler = withErrorHandling(updateTeamMemberHandler);
const wrappedDeleteHandler = withErrorHandling(removeTeamMemberHandler);

export const PATCH = withResourceIsolation('user', 'organizationId')(wrappedPatchHandler);
export const DELETE = withResourceIsolation('user', 'organizationId')(wrappedDeleteHandler);
