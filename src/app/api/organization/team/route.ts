import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { Prisma, InvitationStatus } from "@prisma/client";

/**
 * GET /api/organization/team
 * Get organization team members
 */
async function getTeamMembersHandler() {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access team members",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to access team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = session.user.organizationId;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base queries with tenant isolation
    let membersQuery = {
      where: {
        organizationId,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        lastActive: true,
        createdAt: true,
        image: true,
      },
      orderBy: {
        createdAt: Prisma.SortOrder.asc,
      },
    };

    let invitationsQuery = {
      where: {
        organizationId,
        status: InvitationStatus.PENDING,
      },
      select: {
        id: true,
        email: true,
        role: true,
        createdAt: true,
      },
    };

    // Apply tenant isolation
    membersQuery = withTenantQuery(membersQuery, tenantContext);
    invitationsQuery = withTenantQuery(invitationsQuery, tenantContext);

    // Execute queries
    const members = await db.user.findMany(membersQuery);
    const invitations = await db.invitation.findMany(invitationsQuery);

    // Format invitations as team members
    const pendingMembers = invitations.map((invitation) => ({
      id: invitation.id,
      name: invitation.email, // Use email as name since name field doesn't exist
      email: invitation.email,
      role: invitation.role,
      status: "PENDING",
      lastActive: null,
      createdAt: invitation.createdAt,
      image: null,
    }));

    // Combine active members and pending invitations
    const allMembers = [...members, ...pendingMembers];

    return NextResponse.json({ members: allMembers });
  } catch (error) {
    logger.error("Error getting team members:", error);
    throw new ApiError(
      "An error occurred while getting team members",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap the handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(getTeamMembersHandler);
export const GET = withTenantIsolation(wrappedHandler);
