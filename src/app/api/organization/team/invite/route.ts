import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType, UserRole } from "@prisma/client";
import { sendInvitationEmail } from "@/lib/email";
import { generateInvitationToken } from "@/lib/tokens";

// Schema for inviting a team member
const inviteSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.enum(["ORGANIZATION_ADMIN", "ORGANIZATION_MEMBER"]),
  name: z.string().min(1, "Please enter a name"),
});

/**
 * POST /api/organization/team/invite
 * Invite a team member to the organization
 */
async function inviteTeamMemberHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to invite team members",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to invite team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  // Check if user has permission to invite team members
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to invite team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { email, role, name } = inviteSchema.parse(body);

    const organizationId = session.user.organizationId;

    // Get organization details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Check if user already exists
    const existingUser = await db.user.findFirst({
      where: {
        email,
      },
    });

    if (existingUser && existingUser.organizationId === organizationId) {
      throw new ApiError(
        "User is already a member of this organization",
        ErrorType.CONFLICT,
        409
      );
    }

    // Check if there's a pending invitation
    const existingInvitation = await db.invitation.findFirst({
      where: {
        email,
        organizationId,
        status: "PENDING",
      },
    });

    if (existingInvitation) {
      throw new ApiError(
        "An invitation has already been sent to this email",
        ErrorType.CONFLICT,
        409
      );
    }

    // Generate invitation token
    const token = await generateInvitationToken();

    // Create invitation
    const invitation = await db.invitation.create({
      data: {
        email,
        role: role === "ORGANIZATION_ADMIN" ? UserRole.ORGANIZATION_ADMIN : UserRole.USER,
        token,
        organization: {
          connect: { id: organizationId },
        },
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      },
    });

    // Send invitation email
    await sendInvitationEmail({
      email,
      name,
      inviterName: session.user.name || "A team member",
      organizationName: organization.name,
      invitationLink: `${process.env.NEXT_PUBLIC_APP_URL}/invitation?token=${token}`,
      role: role === "ORGANIZATION_ADMIN" ? "Admin" : "Member",
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.INVITATION_SENT,
        description: `Invitation sent to ${email} for role ${role}`,
        userId: session.user.id,
        organizationId,
        metadata: {
          email,
          role,
          invitationId: invitation.id,
        },
      },
    });

    logger.info(`Invitation sent to ${email} for organization ${organizationId}`);

    return NextResponse.json({
      message: "Invitation sent successfully",
      invitation: {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        createdAt: invitation.createdAt,
      },
    });
  } catch (error) {
    logger.error("Error inviting team member:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while inviting team member",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(inviteTeamMemberHandler);
