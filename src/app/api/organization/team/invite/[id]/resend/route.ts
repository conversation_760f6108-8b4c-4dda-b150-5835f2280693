import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType } from "@prisma/client";
import { sendInvitationEmail } from "@/lib/email";
import { generateInvitationToken } from "@/lib/tokens";

/**
 * POST /api/organization/team/invite/[id]/resend
 * Resend an invitation to a team member
 */
async function resendInvitationHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to resend invitations",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to resend invitations",
      ErrorType.FORBIDDEN,
      403
    );
  }

  // Check if user has permission to resend invitations
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to resend invitations",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const invitationId = params.id;
    const organizationId = session.user.organizationId;

    // Get organization details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Get invitation
    const invitation = await db.invitation.findFirst({
      where: {
        id: invitationId,
        organizationId,
      },
    });

    if (!invitation) {
      throw new ApiError("Invitation not found", ErrorType.NOT_FOUND, 404);
    }

    if (invitation.status !== "PENDING") {
      throw new ApiError(
        "Invitation is no longer pending",
        ErrorType.CONFLICT,
        409
      );
    }

    // Generate new token
    const token = await generateInvitationToken();

    // Update invitation
    const updatedInvitation = await db.invitation.update({
      where: { id: invitationId },
      data: {
        token,
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        updatedAt: new Date(),
      },
    });

    // Send invitation email
    await sendInvitationEmail({
      email: invitation.email,
      name: "Team Member", // Generic name since the Invitation model doesn't have a name field
      inviterName: session.user.name || "A team member",
      organizationName: organization.name,
      invitationLink: `${process.env.NEXT_PUBLIC_APP_URL}/invitation?token=${token}`,
      role: invitation.role === "ORGANIZATION_ADMIN" ? "Admin" : "Member",
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.INVITATION_SENT,
        description: `Invitation resent to ${invitation.email} for role ${invitation.role}`,
        userId: session.user.id,
        organizationId,
        metadata: {
          email: invitation.email,
          role: invitation.role,
          invitationId: invitation.id,
        },
      },
    });

    logger.info(`Invitation resent to ${invitation.email} for organization ${organizationId}`);

    return NextResponse.json({
      message: "Invitation resent successfully",
    });
  } catch (error) {
    logger.error("Error resending invitation:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while resending invitation",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(resendInvitationHandler);
