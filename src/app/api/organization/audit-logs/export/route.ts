import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { format } from "date-fns";

/**
 * GET /api/organization/audit-logs/export
 * Export organization audit logs as CSV
 */
async function exportAuditLogsHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to export audit logs",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to export audit logs",
      ErrorType.FORBIDDEN,
      403
    );
  }

  // Check if user has permission to export audit logs
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to export audit logs",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = session.user.organizationId;

    // Get URL parameters
    const url = new URL(req.url);
    const search = url.searchParams.get("search");
    const type = url.searchParams.get("type");
    const limit = parseInt(url.searchParams.get("limit") || "1000");

    // Build query
    const query: any = {
      organizationId,
    };

    // Add search filter
    if (search) {
      query.OR = [
        { description: { contains: search, mode: "insensitive" } },
        { type: { contains: search, mode: "insensitive" } },
        {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
      ];
    }

    // Add type filter
    if (type && type !== "all") {
      query.type = {
        startsWith: type,
      };
    }

    // Get audit logs
    const auditLogs = await db.auditLog.findMany({
      where: query,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      take: limit,
    });

    // Generate CSV content
    let csvContent = "ID,Type,Description,User,Email,Date,Time\n";

    auditLogs.forEach((log) => {
      const date = new Date(log.createdAt);
      const formattedDate = format(date, "yyyy-MM-dd");
      const formattedTime = format(date, "HH:mm:ss");
      const userName = log.user?.name || "System";
      const userEmail = log.user?.email || "<EMAIL>";

      // Escape fields that might contain commas
      const escapeCsv = (field: string) => {
        if (field.includes(",") || field.includes('"') || field.includes("\n")) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
      };

      csvContent += [
        log.id,
        escapeCsv(log.type),
        escapeCsv(log.description),
        escapeCsv(userName),
        escapeCsv(userEmail),
        formattedDate,
        formattedTime,
      ].join(",") + "\n";
    });

    // Create response with CSV content
    const response = new NextResponse(csvContent, {
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": `attachment; filename="audit-logs-${format(new Date(), "yyyy-MM-dd")}.csv"`,
      },
    });

    return response;
  } catch (error) {
    logger.error("Error exporting audit logs:", error);
    throw new ApiError(
      "An error occurred while exporting audit logs",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(exportAuditLogsHandler);
