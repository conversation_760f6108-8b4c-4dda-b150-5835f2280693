import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * GET /api/organization/audit-logs
 * Get organization audit logs
 */
async function getAuditLogsHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access audit logs",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to access audit logs",
      ErrorType.FORBIDDEN,
      403
    );
  }

  // Check if user has permission to view audit logs
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to access audit logs",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = session.user.organizationId;

    // Get URL parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const search = url.searchParams.get("search");
    const type = url.searchParams.get("type");

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {
      organizationId,
    };

    // Add search filter
    if (search) {
      query.OR = [
        { description: { contains: search, mode: "insensitive" } },
        { type: { contains: search, mode: "insensitive" } },
        {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
      ];
    }

    // Add type filter
    if (type && type !== "all") {
      query.type = {
        startsWith: type,
      };
    }

    // Get audit logs
    const auditLogs = await db.auditLog.findMany({
      where: query,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      skip,
      take: limit,
    });

    // Get total count
    const total = await db.auditLog.count({
      where: query,
    });

    // Format response
    const formattedLogs = auditLogs.map((log) => ({
      id: log.id,
      type: log.type,
      description: log.description,
      userId: log.userId,
      userName: log.user?.name || "System",
      userEmail: log.user?.email || "<EMAIL>",
      createdAt: log.createdAt,
      metadata: log.metadata,
    }));

    return NextResponse.json({
      logs: formattedLogs,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total,
      },
    });
  } catch (error) {
    logger.error("Error getting audit logs:", error);
    throw new ApiError(
      "An error occurred while getting audit logs",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getAuditLogsHandler);
