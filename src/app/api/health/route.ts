import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { alchemy } from "@/lib/blockchain";
import { logger } from "@/lib/logger";
import { withErrorHandling } from "@/lib/error-handler";

// Define interfaces for health check
interface HealthStatus {
  status: string;
  responseTime?: string;
  error?: string;
}

interface HealthChecks {
  database: HealthStatus;
  blockchain: HealthStatus;
  uptime: number;
  timestamp: string;
}

/**
 * Health check endpoint
 *
 * This endpoint checks the health of the application and its dependencies.
 * It returns a 200 OK response if everything is working correctly.
 *
 * @returns {Object} Health status of the application
 */
async function GET() {
  const startTime = Date.now();
  const checks: HealthChecks = {
    database: { status: "unknown" },
    blockchain: { status: "unknown" },
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
  };

  // Check database connection
  try {
    // Simple query to check if the database is responsive
    await db.$queryRaw`SELECT 1`;
    checks.database = {
      status: "healthy",
      responseTime: `${Date.now() - startTime}ms`,
    };
  } catch (error) {
    logger.error("Database health check failed:", error);
    checks.database = {
      status: "unhealthy",
      error: "Database connection failed",
      responseTime: `${Date.now() - startTime}ms`,
    };
  }

  // Check blockchain connection
  try {
    // Simple query to check if the blockchain provider is responsive
    await alchemy.core.getBlockNumber();
    checks.blockchain = {
      status: "healthy",
      responseTime: `${Date.now() - startTime}ms`,
    };
  } catch (error) {
    logger.error("Blockchain health check failed:", error);
    checks.blockchain = {
      status: "unhealthy",
      error: "Blockchain connection failed",
      responseTime: `${Date.now() - startTime}ms`,
    };
  }

  // Determine overall status
  const isHealthy = 
    checks.database.status === "healthy" && 
    checks.blockchain.status === "healthy";

  // Log health check result
  logger.info("Health check completed", {
    healthy: isHealthy,
    responseTime: `${Date.now() - startTime}ms`,
  });

  return NextResponse.json(
    {
      status: isHealthy ? "healthy" : "unhealthy",
      checks,
      environment: process.env.NODE_ENV,
      version: process.env.APP_VERSION || "1.0.0",
      responseTime: `${Date.now() - startTime}ms`,
    },
    { status: isHealthy ? 200 : 503 }
  );
}

const wrappedGET = withErrorHandling(GET);
export { wrappedGET as GET };
