import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { blockchainService } from "@/lib/blockchain";
import { SupportedNetwork } from "@/lib/blockchain/config/networks";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";

/**
 * GET /api/blockchain/transaction-status
 * Get transaction status
 */
async function getTransactionStatusHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to check transaction status",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const txHash = searchParams.get("txHash");
    const network = searchParams.get("network") as SupportedNetwork;
    const isTestnet = searchParams.get("isTestnet") === "true";

    if (!txHash) {
      throw new ApiError(
        "Transaction hash is required",
        ErrorType.VALIDATION,
        400
      );
    }

    if (!network) {
      throw new ApiError(
        "Network is required",
        ErrorType.VALIDATION,
        400
      );
    }

    // Verify the transaction using the existing verifyTransaction method
    const txVerification = await blockchainService.verifyTransaction(txHash);
    
    // Map the verification response to a status response
    const status = {
      status: txVerification.confirmed ? "confirmed" : "pending",
      confirmations: txVerification.receipt?.confirmations || 0,
      blockNumber: txVerification.receipt?.blockNumber,
      gasUsed: txVerification.receipt?.gasUsed?.toString(),
      effectiveGasPrice: txVerification.receipt?.effectiveGasPrice?.toString(),
      success: txVerification.success
    };

    return NextResponse.json(status);
  } catch (error) {
    logger.error("Error getting transaction status:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while getting transaction status",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(getTransactionStatusHandler);

export const GET = withTenantIsolation(wrappedHandler);
