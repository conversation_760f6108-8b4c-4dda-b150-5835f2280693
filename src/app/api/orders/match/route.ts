import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { OrderMatchingEngine } from "@/lib/order-matching-engine";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for matching orders
const matchOrdersSchema = z.object({
  carbonCreditId: z.string().optional(),
});

/**
 * Match orders
 */
async function matchOrdersHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to match orders",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Only platform admins can manually match orders
  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "Only platform administrators can manually match orders",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse request body
    const body = await req.json().catch(() => ({}));
    const { carbonCreditId } = matchOrdersSchema.parse(body);

    let matches;

    if (carbonCreditId) {
      // Match orders for a specific carbon credit
      matches = await OrderMatchingEngine.matchOrders(carbonCreditId);
      logger.info(`Admin ${session.user.id} matched orders for carbon credit ${carbonCreditId}`);
    } else {
      // Match all pending orders
      matches = await OrderMatchingEngine.matchAllOrders();
      logger.info(`Admin ${session.user.id} matched all pending orders`);
    }

    return NextResponse.json({
      matches,
      matchCount: matches.length,
      message: matches.length > 0
        ? `Successfully matched ${matches.length} orders`
        : "No orders were matched",
    });
  } catch (error) {
    logger.error("Error matching orders:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while matching orders",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(matchOrdersHandler);

export const POST = withTenantIsolation(wrappedHandler);
