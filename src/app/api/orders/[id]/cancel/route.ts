import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { OrderMatchingEngine } from "@/lib/order-matching-engine";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

/**
 * Cancel an order
 */
async function cancelOrderHandler(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to cancel an order",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const orderId = params.id;

    // Cancel the order using the matching engine
    const cancelledOrder = await OrderMatchingEngine.cancelOrder(orderId, session.user.id);

    logger.info(`User ${session.user.id} cancelled order ${orderId}`);

    return NextResponse.json({
      order: cancelledOrder,
      message: "Order cancelled successfully",
    });
  } catch (error) {
    logger.error("Error cancelling order:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while cancelling the order",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and resource isolation
const wrappedHandler = withErrorHandling(cancelOrderHandler);

export const POST = withResourceIsolation('order', 'id')(wrappedHandler);
