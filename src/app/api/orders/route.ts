import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { OrderMatchingEngine } from "@/lib/order-matching-engine";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext, withOrderTenantIsolation } from "@/lib/tenant-isolation";

const orderSchema = z.object({
  type: z.enum(["BUY", "SELL"]),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  carbonCreditId: z.string().uuid("Invalid carbon credit ID"),
});

async function createOrderHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to create an order",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to create an order",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    const body = await req.json();
    const { type, quantity, price, carbonCreditId } = orderSchema.parse(body);

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Verify that the carbon credit exists and is accessible to the user
    let carbonCreditQuery: any = {
      where: {
        id: carbonCreditId,
        status: "LISTED",
      },
    };

    // Apply tenant isolation for non-listed credits
    // For listed credits, we don't need to apply tenant isolation as they are publicly available
    if (tenantContext.isAdmin) {
      // Admins can access all carbon credits
      // No additional filters needed
    } else if (!tenantContext.organizationId) {
      // Users without an organization can't access any carbon credits
      logger.warn(`User ${session.user.id} has no organization ID, denying access`);
      carbonCreditQuery.where.id = 'no-access'; // This ensures no results are returned
    } else {
      // Regular users can access carbon credits from their organization
      carbonCreditQuery.where.organizationId = tenantContext.organizationId;
    }

    const carbonCredit = await db.carbonCredit.findFirst(carbonCreditQuery);

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found or not available for trading",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Create the order using the matching engine
    const order = await OrderMatchingEngine.createOrder(
      session.user.id,
      session.user.organizationId,
      type,
      carbonCreditId,
      quantity,
      price
    );

    // Check if the order was matched immediately
    const matchedOrder = await db.order.findUnique({
      where: { id: order.id },
      include: {
        transactions: true,
        carbonCredit: {
          select: {
            name: true,
            standard: true,
            methodology: true,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} created ${type} order for ${quantity} tons of carbon credit ${carbonCreditId}`);

    return NextResponse.json({
      order: matchedOrder,
      message: matchedOrder?.status === "COMPLETED"
        ? "Order created and matched successfully"
        : "Order created successfully",
    });
  } catch (error) {
    logger.error("Error creating order:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while creating the order",
      ErrorType.INTERNAL,
      500
    );
  }
}

async function getOrdersHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view orders",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get("type");
    const status = searchParams.get("status");

    // Build the filter object
    const filter: any = {};

    if (type) {
      filter.type = type;
    }

    if (status) {
      filter.status = status;
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query
    let ordersQuery: any = {
      where: {
        OR: [
          {
            buyerId: session.user.id,
            ...filter,
          },
          {
            sellerId: session.user.id,
            ...filter,
          },
        ],
      },
      include: {
        carbonCredit: {
          select: {
            name: true,
            standard: true,
            methodology: true,
          },
        },
        buyer: {
          select: {
            id: true,
            organizationId: true,
          },
        },
        seller: {
          select: {
            id: true,
            organizationId: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc" as const,
      },
    };

    // Apply tenant isolation using the specialized order tenant isolation function
    ordersQuery = withOrderTenantIsolation(ordersQuery, tenantContext);

    // Get user's orders
    const orders = await db.order.findMany(ordersQuery);

    return NextResponse.json({ orders });
  } catch (error) {
    logger.error("Error fetching orders:", error);

    throw new ApiError(
      "An error occurred while fetching orders",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling and tenant isolation
const wrappedPostHandler = withErrorHandling(createOrderHandler);
const wrappedGetHandler = withErrorHandling(getOrdersHandler);

export const POST = withTenantIsolation(wrappedPostHandler);
export const GET = withTenantIsolation(wrappedGetHandler);
