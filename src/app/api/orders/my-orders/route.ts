import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext, withOrderTenantIsolation } from "@/lib/tenant-isolation";

/**
 * Get user's orders
 */
async function getMyOrdersHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view your orders",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get("type");
    const status = searchParams.get("status");
    const side = searchParams.get("side");

    // Build the filter object
    const filter: any = {};

    if (type) {
      filter.type = type;
    }

    if (status) {
      filter.status = status;
    }

    if (side) {
      filter.side = side;
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query
    let ordersQuery: any = {
      where: {
        OR: [
          {
            buyerId: session.user.id,
            ...filter,
          },
          {
            sellerId: session.user.id,
            ...filter,
          },
        ],
      },
      include: {
        carbonCredit: {
          select: {
            id: true,
            name: true,
            vintage: true,
            standard: true,
            methodology: true,
          },
        },
        buyer: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
        transactions: {
          select: {
            id: true,
            amount: true,
            fee: true,
            status: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc" as const,
      },
    };

    // Apply tenant isolation using the specialized order tenant isolation function
    ordersQuery = withOrderTenantIsolation(ordersQuery, tenantContext);

    // Get user's orders
    const orders = await db.order.findMany(ordersQuery);

    // Transform orders to match the expected format in the client
    const transformedOrders = orders.map(order => {
      // Calculate filled quantity from transactions
      const filledQuantity = order.transactions.reduce(
        (sum, transaction) => sum + (transaction.amount ?? 0),
        0
      );

      // Determine if the current user is the buyer or seller
      const isBuyer = order.buyerId === session.user.id;

      // Set the side based on whether the user is the buyer or seller
      const side = isBuyer ? "BUY" : "SELL";

      return {
        id: order.id,
        type: order.type, // This is BUY or SELL in the schema
        side: side, // Derived from buyer/seller relationship
        status: order.status,
        quantity: order.quantity,
        filledQuantity,
        price: order.price,
        timeInForce: "GTC", // Default value as it's not in the schema
        carbonCredit: {
          id: order.carbonCredit.id,
          name: order.carbonCredit.name,
          vintage: order.carbonCredit.vintage,
          standard: order.carbonCredit.standard,
        },
        listing: {
          id: order.carbonCredit.id,
          title: order.carbonCredit.name,
        },
        createdAt: order.createdAt.toISOString(),
        updatedAt: order.updatedAt.toISOString(),
      };
    });

    logger.info(`User ${session.user.id} fetched their orders`);

    return NextResponse.json({ orders: transformedOrders });
  } catch (error) {
    logger.error("Error fetching user orders:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while fetching your orders",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(getMyOrdersHandler);

export const GET = withTenantIsolation(wrappedHandler);
