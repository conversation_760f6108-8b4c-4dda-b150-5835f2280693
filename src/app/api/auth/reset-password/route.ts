import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { hashPassword } from "@/lib/auth/password";
import { generatePasswordResetToken, verifyPasswordResetToken } from "@/lib/tokens";
import { emailService } from "@/lib/email";
import { logger } from "@/lib/logger";

// Schema for requesting a password reset
const requestResetSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

// Schema for resetting the password
const resetPasswordSchema = z.object({
  token: z.string(),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { email } = requestResetSchema.parse(body);

    // Find the user by email
    const user = await db.user.findUnique({
      where: {
        email,
      },
    });

    // Don't reveal if the user exists or not for security reasons
    if (!user) {
      return NextResponse.json(
        { message: "If your email is registered, you will receive a password reset link" },
        { status: 200 }
      );
    }

    // Generate a password reset token
    const token = await generatePasswordResetToken(user.id);

    // Send the password reset email
    await emailService.sendPasswordResetEmail(user.email, user.name, token);

    return NextResponse.json(
      { message: "If your email is registered, you will receive a password reset link" },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error requesting password reset:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while requesting a password reset" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { token, password } = resetPasswordSchema.parse(body);

    // Verify the token
    const userId = await verifyPasswordResetToken(token);

    if (!userId) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 400 }
      );
    }

    // Hash the new password using our custom function
    const hashedPassword = await hashPassword(password);

    // Update the user's password
    await db.user.update({
      where: {
        id: userId,
      },
      data: {
        password: hashedPassword,
      },
    });

    return NextResponse.json(
      { message: "Password reset successful" },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error resetting password:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while resetting the password" },
      { status: 500 }
    );
  }
}
