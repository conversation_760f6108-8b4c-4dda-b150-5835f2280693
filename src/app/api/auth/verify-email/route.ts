import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { verifyEmailVerificationToken } from "@/lib/tokens";

// Schema for verifying email
const verifyEmailSchema = z.object({
  token: z.string(),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { token } = verifyEmailSchema.parse(body);

    // Verify the token
    const userId = await verifyEmailVerificationToken(token);

    if (!userId) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 400 }
      );
    }

    // Update the user's email verification status
    const user = await db.user.update({
      where: {
        id: userId,
      },
      data: {
        emailVerified: new Date(),
      },
    });

    // Check if user has an organization
    const hasOrganization = !!user.organizationId;

    logger.info(`Email verified for user: ${user.email}`);

    return NextResponse.json(
      {
        message: "<PERSON><PERSON> verified successfully",
        requiresOnboarding: !hasOrganization, // Signal to frontend that onboarding is needed
        redirectUrl: !hasOrganization ? "/onboarding" : "/dashboard"
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error verifying email:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while verifying your email" },
      { status: 500 }
    );
  }
}
