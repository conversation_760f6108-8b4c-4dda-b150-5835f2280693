import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { z } from "zod";
import { onboardingService, OnboardingStep } from "@/lib/onboarding-state";

// Schema for updating onboarding state
const updateStateSchema = z.object({
  currentStep: z.nativeEnum(OnboardingStep).optional(),
  organizationId: z.string().optional(),
  action: z.enum(["complete", "skip"]).optional(),
  step: z.nativeEnum(OnboardingStep).optional(),
});

/**
 * GET /api/onboarding
 * Get current onboarding state for the user
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    const state = await onboardingService.getState(session.user.id);

    if (!state) {
      // Create initial state if it doesn't exist
      await onboardingService.saveState(session.user.id, {
        currentStep: OnboardingStep.ORGANIZATION_DETAILS,
        skippedSteps: [],
        completedSteps: []
      });

      return NextResponse.json({
        currentStep: OnboardingStep.ORGANIZATION_DETAILS,
        skippedSteps: [],
        completedSteps: [],
        isComplete: false
      });
    }

    const isComplete = await onboardingService.isComplete(session.user.id);

    return NextResponse.json({
      ...state,
      isComplete
    });
  } catch (error) {
    logger.error("Error fetching onboarding state:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching onboarding state" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/onboarding
 * Update onboarding state
 */
export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { currentStep, organizationId, action, step } = updateStateSchema.parse(body);

    if (action && step) {
      // Handle complete or skip action
      if (action === "complete") {
        const nextStep = getNextStep(step);
        await onboardingService.completeStep(session.user.id, step, nextStep);
      } else if (action === "skip") {
        const nextStep = getNextStep(step);
        await onboardingService.skipStep(session.user.id, step, nextStep);
      }
    } else {
      // Direct state update
      await onboardingService.saveState(session.user.id, {
        currentStep,
        organizationId
      });
    }

    const updatedState = await onboardingService.getState(session.user.id);
    const isComplete = await onboardingService.isComplete(session.user.id);

    return NextResponse.json({
      ...updatedState,
      isComplete
    });
  } catch (error) {
    logger.error("Error updating onboarding state:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating onboarding state" },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get the next step
 */
function getNextStep(currentStep: OnboardingStep): OnboardingStep {
  switch (currentStep) {
    case OnboardingStep.ORGANIZATION_DETAILS:
      return OnboardingStep.TEAM_INVITATIONS;
    case OnboardingStep.TEAM_INVITATIONS:
      return OnboardingStep.SUBSCRIPTION;
    case OnboardingStep.SUBSCRIPTION:
      return OnboardingStep.WALLET_SETUP;
    case OnboardingStep.WALLET_SETUP:
      return OnboardingStep.VERIFICATION;
    case OnboardingStep.VERIFICATION:
    default:
      return OnboardingStep.COMPLETE;
  }
}
