# Carbonix API Documentation

This document provides an overview of the API endpoints available in the Carbonix platform.

## Authentication

### POST /api/auth/[...nextauth]

NextAuth.js authentication endpoints for handling login, logout, and session management.

### POST /api/register

Register a new user account.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:**
```json
{
  "user": {
    "id": "user_id",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "role": "USER"
  },
  "message": "User created successfully. Please check your email to verify your account."
}
```

### POST /api/auth/verify-email

Verify a user's email address using a token.

**Request Body:**
```json
{
  "token": "verification_token"
}
```

**Response:**
```json
{
  "message": "Email verified successfully"
}
```

### POST /api/auth/reset-password

Request a password reset link.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "If your email is registered, you will receive a password reset link"
}
```

### PUT /api/auth/reset-password

Reset a user's password using a token.

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "NewSecurePassword123!"
}
```

**Response:**
```json
{
  "message": "Password reset successful"
}
```

## Organizations

### POST /api/organizations

Create a new organization.

**Request Body:**
```json
{
  "name": "Green Energy Co.",
  "description": "Renewable energy solutions provider",
  "website": "https://greenenergy.example.com",
  "logo": "base64_encoded_image"
}
```

**Response:**
```json
{
  "organization": {
    "id": "organization_id",
    "name": "Green Energy Co.",
    "description": "Renewable energy solutions provider",
    "website": "https://greenenergy.example.com",
    "logo": "logo_url",
    "status": "PENDING"
  },
  "message": "Organization created successfully"
}
```

### GET /api/organizations

Get a list of organizations.

**Query Parameters:**
- `status` (optional): Filter by organization status (PENDING, ACTIVE, SUSPENDED)

**Response:**
```json
{
  "organizations": [
    {
      "id": "organization_id",
      "name": "Green Energy Co.",
      "description": "Renewable energy solutions provider",
      "website": "https://greenenergy.example.com",
      "logo": "logo_url",
      "status": "ACTIVE"
    }
  ]
}
```

## Projects

### GET /api/projects

Get a list of projects with filtering and pagination.

**Query Parameters:**
- `type` - Filter by project type
- `status` - Filter by project status
- `verificationStatus` - Filter by verification status
- `country` - Filter by country
- `standard` - Filter by standard
- `startDateFrom` - Filter by start date (from)
- `startDateTo` - Filter by start date (to)
- `search` - Search term for project name or description
- `sortBy` - Field to sort by (name, createdAt, startDate, status)
- `sortOrder` - Sort order (asc, desc)
- `page` - Page number
- `limit` - Number of results per page

**Response:**
```json
{
  "projects": [
    {
      "id": "project_id",
      "name": "Renewable Energy Project",
      "description": "Description of the project",
      "type": "RENEWABLE_ENERGY",
      "status": "ACTIVE",
      "verificationStatus": "VERIFIED",
      "organization": {
        "name": "Organization Name"
      },
      "_count": {
        "carbonCredits": 5
      }
    }
  ],
  "filters": {
    "types": ["RENEWABLE_ENERGY", "FORESTRY"],
    "statuses": ["ACTIVE", "PENDING"],
    "countries": ["USA", "Canada"],
    "standards": ["Verra", "Gold Standard"]
  },
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "pages": 5
  }
}
```

### POST /api/projects

Create a new project.

**Request Body:**
```json
{
  "name": "Renewable Energy Project",
  "description": "Description of the project",
  "type": "RENEWABLE_ENERGY",
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2025-01-01T00:00:00.000Z",
  "location": "California, USA",
  "country": "USA",
  "coordinates": "37.7749,-122.4194",
  "area": 1000,
  "externalProjectId": "EXT-123",
  "registryId": "REG-456",
  "standard": "Verra",
  "methodology": "VM0025",
  "methodologyVersion": "1.0",
  "estimatedReductions": 10000,
  "actualReductions": 8000,
  "verifier": "Verification Company",
  "validator": "Validation Company",
  "images": ["https://example.com/image1.jpg"],
  "budget": 500000,
  "roi": 15,
  "sdgs": ["SDG-7", "SDG-13"],
  "tags": ["renewable", "solar"]
}
```

### GET /api/projects/:id

Get details of a specific project.

**Response:**
```json
{
  "project": {
    "id": "project_id",
    "name": "Renewable Energy Project",
    "description": "Description of the project",
    "type": "RENEWABLE_ENERGY",
    "status": "ACTIVE",
    "verificationStatus": "VERIFIED",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2025-01-01T00:00:00.000Z",
    "location": "California, USA",
    "country": "USA",
    "organization": {
      "id": "organization_id",
      "name": "Organization Name"
    },
    "carbonCredits": [
      {
        "id": "carbon_credit_id",
        "name": "Renewable Energy Credit 2023",
        "vintage": 2023,
        "quantity": 1000,
        "availableQuantity": 800,
        "status": "VERIFIED"
      }
    ],
    "documents": [
      {
        "id": "document_id",
        "name": "Project Design Document",
        "type": "PROJECT_DESIGN",
        "url": "https://example.com/document.pdf",
        "status": "VERIFIED"
      }
    ]
  }
}
```

### PATCH /api/projects/:id

Update a project.

**Request Body:**
```json
{
  "name": "Updated Project Name",
  "description": "Updated description",
  "status": "ACTIVE",
  "estimatedReductions": 12000
}
```

### DELETE /api/projects/:id

Delete a project.

### GET /api/projects/:id/documents

Get documents for a project.

### POST /api/projects/:id/documents

Add a document to a project.

**Request Body:**
```json
{
  "name": "Project Design Document",
  "type": "PROJECT_DESIGN",
  "url": "https://example.com/document.pdf",
  "notes": "Initial project design document"
}
```

### GET /api/projects/:id/verification

Get verification history for a project.

### POST /api/projects/:id/verification

Request verification for a project.

**Request Body:**
```json
{
  "notes": "Requesting initial verification",
  "metadata": {
    "additionalInfo": "Additional information"
  }
}
```

### PATCH /api/projects/:id/verification

Update verification status (admin only).

**Request Body:**
```json
{
  "status": "VERIFIED",
  "verifier": "Verification Company",
  "verifierEmail": "<EMAIL>",
  "notes": "Project has been verified"
}
```

### GET /api/projects/:id/carbon-credits

Get carbon credits for a project.

### POST /api/projects/:id/carbon-credits

Create a carbon credit for a project.

**Request Body:**
```json
{
  "name": "Renewable Energy Credit 2023",
  "description": "Carbon credits from renewable energy project",
  "quantity": 1000,
  "price": 15,
  "minPurchaseQuantity": 10,
  "vintage": 2023,
  "standard": "Verra",
  "methodology": "VM0025",
  "location": "California, USA",
  "country": "USA",
  "serialNumber": "VCS-123-456",
  "certificationDate": "2023-06-01T00:00:00.000Z"
}
```

### GET /api/projects/:id/wallets

Get wallets for a project.

### POST /api/projects/:id/wallets

Create a wallet for a project.

**Request Body:**
```json
{
  "name": "Project Tokenization Wallet",
  "network": "polygon",
  "isTestnet": false,
  "purpose": "tokenization",
  "isSmartWallet": true,
  "securitySettings": {
    "recoveryEnabled": true,
    "recoveryType": "MULTISIG",
    "transactionLimitDaily": 10000,
    "transactionLimitPerTx": 1000,
    "requireApprovals": true,
    "approvalThreshold": 2
  }
}
```

### GET /api/projects/:id/financials

Get financial metrics for a project.

### POST /api/projects/:id/financials

Add a financial metric to a project.

**Request Body:**
```json
{
  "metricType": "REVENUE",
  "name": "Q2 2023 Revenue",
  "value": 50000,
  "previousValue": 45000,
  "changePercent": 11.11,
  "currency": "USD",
  "period": "quarterly",
  "startDate": "2023-04-01T00:00:00.000Z",
  "endDate": "2023-06-30T00:00:00.000Z",
  "target": 55000,
  "status": "BELOW_TARGET",
  "notes": "Revenue from carbon credit sales"
}
```

## Carbon Credits

### POST /api/carbon-credits

Create a new carbon credit listing.

**Request Body:**
```json
{
  "name": "Renewable Energy Project",
  "description": "Wind farm in Texas generating clean energy",
  "quantity": 1000,
  "price": 25.5,
  "vintage": 2022,
  "standard": "Verra",
  "methodology": "Renewable Energy",
  "location": "Texas, USA",
  "projectId": "project_id"
}
```

**Response:**
```json
{
  "carbonCredit": {
    "id": "carbon_credit_id",
    "name": "Renewable Energy Project",
    "description": "Wind farm in Texas generating clean energy",
    "quantity": 1000,
    "price": 25.5,
    "vintage": 2022,
    "standard": "Verra",
    "methodology": "Renewable Energy",
    "location": "Texas, USA",
    "status": "PENDING",
    "projectId": "project_id"
  },
  "message": "Carbon credit created successfully"
}
```

### GET /api/carbon-credits

Get a list of carbon credits.

**Query Parameters:**
- `standard` (optional): Filter by standard (e.g., "Verra", "Gold Standard")
- `methodology` (optional): Filter by methodology (e.g., "Renewable Energy", "Forestry")
- `minPrice` (optional): Filter by minimum price
- `maxPrice` (optional): Filter by maximum price
- `vintage` (optional): Filter by vintage year
- `projectId` (optional): Filter by project ID

**Response:**
```json
{
  "carbonCredits": [
    {
      "id": "carbon_credit_id",
      "name": "Renewable Energy Project",
      "description": "Wind farm in Texas generating clean energy",
      "quantity": 1000,
      "price": 25.5,
      "vintage": 2022,
      "standard": "Verra",
      "methodology": "Renewable Energy",
      "location": "Texas, USA",
      "status": "LISTED",
      "organization": {
        "name": "Green Energy Co."
      },
      "project": {
        "id": "project_id",
        "name": "Renewable Energy Project"
      }
    }
  ]
}
```

## Marketplace

### GET /api/marketplace/listings

Get marketplace listings with filtering and pagination.

**Query Parameters:**
- `status` - Filter by listing status
- `pricingStrategy` - Filter by pricing strategy
- `visibility` - Filter by visibility
- `featured` - Filter by featured status
- `minPrice` - Filter by minimum price
- `maxPrice` - Filter by maximum price
- `minQuantity` - Filter by minimum quantity
- `maxQuantity` - Filter by maximum quantity
- `vintage` - Filter by vintage
- `standard` - Filter by standard
- `methodology` - Filter by methodology
- `country` - Filter by country
- `projectType` - Filter by project type
- `organizationId` - Filter by organization
- `search` - Search term
- `sortBy` - Field to sort by
- `sortOrder` - Sort order
- `page` - Page number
- `limit` - Number of results per page

**Response:**
```json
{
  "listings": [
    {
      "id": "listing_id",
      "title": "Renewable Energy Credits 2023",
      "status": "ACTIVE",
      "quantity": 1000,
      "availableQuantity": 800,
      "price": 15,
      "pricingStrategy": "FIXED",
      "carbonCredit": {
        "id": "carbon_credit_id",
        "name": "Renewable Energy Credit 2023",
        "vintage": 2023,
        "standard": "Verra",
        "project": {
          "id": "project_id",
          "name": "Renewable Energy Project",
          "type": "RENEWABLE_ENERGY"
        }
      },
      "organization": {
        "id": "organization_id",
        "name": "Organization Name",
        "logo": "https://example.com/logo.png"
      }
    }
  ],
  "filters": {
    "standards": ["Verra", "Gold Standard"],
    "methodologies": ["VM0025", "VM0015"],
    "countries": ["USA", "Canada"],
    "projectTypes": ["RENEWABLE_ENERGY", "FORESTRY"]
  },
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "pages": 5
  }
}
```

### POST /api/marketplace/listings

Create a new marketplace listing.

**Request Body:**
```json
{
  "title": "Renewable Energy Credits 2023",
  "description": "Carbon credits from renewable energy project",
  "carbonCreditId": "carbon_credit_id",
  "quantity": 1000,
  "minPurchaseQuantity": 10,
  "pricingStrategy": "FIXED",
  "price": 15,
  "visibility": "PUBLIC",
  "featured": false,
  "tags": ["renewable", "solar"]
}
```

### GET /api/marketplace/listings/:id

Get details of a specific marketplace listing.

**Response:**
```json
{
  "listing": {
    "id": "listing_id",
    "title": "Renewable Energy Credits 2023",
    "description": "Carbon credits from renewable energy project",
    "status": "ACTIVE",
    "quantity": 1000,
    "availableQuantity": 800,
    "price": 15,
    "pricingStrategy": "FIXED",
    "visibility": "PUBLIC",
    "featured": false,
    "tags": ["renewable", "solar"],
    "carbonCredit": {
      "id": "carbon_credit_id",
      "name": "Renewable Energy Credit 2023",
      "vintage": 2023,
      "standard": "Verra",
      "project": {
        "id": "project_id",
        "name": "Renewable Energy Project",
        "type": "RENEWABLE_ENERGY",
        "description": "Description of the project",
        "location": "California, USA"
      }
    },
    "organization": {
      "id": "organization_id",
      "name": "Organization Name",
      "logo": "https://example.com/logo.png"
    }
  },
  "isInWatchlist": false
}
```

### PATCH /api/marketplace/listings/:id

Update a marketplace listing.

**Request Body:**
```json
{
  "title": "Updated Listing Title",
  "price": 18,
  "status": "PAUSED"
}
```

### DELETE /api/marketplace/listings/:id

Delete a marketplace listing.

### GET /api/marketplace/watchlist

Get user's watchlists.

**Response:**
```json
{
  "watchlists": [
    {
      "id": "watchlist_id",
      "name": "Renewable Energy Credits",
      "description": "Watchlist for renewable energy carbon credits",
      "_count": {
        "watchlistItems": 5
      }
    }
  ]
}
```

### POST /api/marketplace/watchlist

Create a new watchlist.

**Request Body:**
```json
{
  "name": "Renewable Energy Credits",
  "description": "Watchlist for renewable energy carbon credits",
  "organizationId": "organization_id"
}
```

### GET /api/marketplace/watchlist/:id

Get a specific watchlist with its items.

**Response:**
```json
{
  "watchlist": {
    "id": "watchlist_id",
    "name": "Renewable Energy Credits",
    "description": "Watchlist for renewable energy carbon credits",
    "user": {
      "id": "user_id",
      "name": "John Doe"
    },
    "organization": {
      "id": "organization_id",
      "name": "Organization Name"
    },
    "watchlistItems": [
      {
        "id": "watchlist_item_id",
        "priceAlertEnabled": true,
        "priceAlertThreshold": 20,
        "priceAlertDirection": "BELOW",
        "notes": "Interested in purchasing when price drops below $20",
        "listing": {
          "id": "listing_id",
          "title": "Renewable Energy Credits 2023",
          "carbonCredit": {
            "id": "carbon_credit_id",
            "name": "Renewable Energy Credit 2023",
            "vintage": 2023,
            "standard": "Verra"
          },
          "organization": {
            "id": "organization_id",
            "name": "Organization Name",
            "logo": "https://example.com/logo.png"
          }
        }
      }
    ]
  }
}
```

### PATCH /api/marketplace/watchlist/:id

Update a watchlist.

**Request Body:**
```json
{
  "name": "Updated Watchlist Name",
  "description": "Updated description"
}
```

### DELETE /api/marketplace/watchlist/:id

Delete a watchlist.

### POST /api/marketplace/watchlist/:id/items

Add an item to a watchlist.

**Request Body:**
```json
{
  "listingId": "listing_id",
  "priceAlertEnabled": true,
  "priceAlertThreshold": 20,
  "priceAlertDirection": "BELOW",
  "notes": "Interested in purchasing when price drops below $20"
}
```

### PATCH /api/marketplace/watchlist/:id/items

Update a watchlist item.

**Request Body:**
```json
{
  "itemId": "watchlist_item_id",
  "priceAlertEnabled": true,
  "priceAlertThreshold": 18,
  "priceAlertDirection": "BELOW",
  "notes": "Updated price threshold"
}
```

### DELETE /api/marketplace/watchlist/:id/items

Remove an item from a watchlist.

**Query Parameters:**
- `itemId` - ID of the watchlist item to remove

## Orders

### POST /api/orders

Create a new order (buy or sell).

**Request Body:**
```json
{
  "type": "BUY",
  "quantity": 100,
  "price": 25.5,
  "carbonCreditId": "carbon_credit_id"
}
```

**Response:**
```json
{
  "order": {
    "id": "order_id",
    "type": "BUY",
    "quantity": 100,
    "price": 25.5,
    "status": "PENDING",
    "carbonCreditId": "carbon_credit_id"
  },
  "message": "Order created successfully"
}
```

### GET /api/orders

Get a list of orders.

**Query Parameters:**
- `type` (optional): Filter by order type (BUY, SELL)
- `status` (optional): Filter by order status (PENDING, MATCHED, COMPLETED, CANCELLED)

**Response:**
```json
{
  "orders": [
    {
      "id": "order_id",
      "type": "BUY",
      "quantity": 100,
      "price": 25.5,
      "status": "MATCHED",
      "carbonCredit": {
        "name": "Renewable Energy Project",
        "standard": "Verra",
        "methodology": "Renewable Energy"
      }
    }
  ]
}
```

## Wallet

### GET /api/wallet

Get the user's wallet information.

**Response:**
```json
{
  "wallet": {
    "id": "wallet_id",
    "address": "******************************************",
    "balance": 1000.0,
    "transactions": [
      {
        "id": "transaction_id",
        "amount": 500.0,
        "fee": 5.0,
        "type": "DEPOSIT",
        "status": "COMPLETED",
        "createdAt": "2023-01-15T10:30:00Z"
      }
    ]
  }
}
```

### POST /api/wallet?operation=deposit

Deposit funds into the wallet.

**Request Body:**
```json
{
  "amount": "100.5"
}
```

**Response:**
```json
{
  "wallet": {
    "id": "wallet_id",
    "address": "******************************************",
    "balance": 1100.5
  },
  "transaction": {
    "id": "transaction_id",
    "amount": 100.5,
    "fee": 0,
    "type": "DEPOSIT",
    "status": "COMPLETED"
  },
  "message": "Deposit successful"
}
```

### POST /api/wallet?operation=withdraw

Withdraw funds from the wallet.

**Request Body:**
```json
{
  "amount": "50.25"
}
```

**Response:**
```json
{
  "wallet": {
    "id": "wallet_id",
    "address": "******************************************",
    "balance": 1050.25
  },
  "transaction": {
    "id": "transaction_id",
    "amount": 50.25,
    "fee": 0,
    "type": "WITHDRAWAL",
    "status": "COMPLETED",
    "transactionHash": "******************************************90abcdef1234567890abcdef"
  },
  "message": "Withdrawal successful"
}
```

## Notifications

### GET /api/notifications

Get the user's notifications.

**Response:**
```json
{
  "notifications": [
    {
      "id": "notification_id",
      "title": "Order Matched",
      "message": "Your buy order for 100 tons of Renewable Energy Project has been matched.",
      "read": false,
      "type": "ORDER",
      "createdAt": "2023-03-10T09:15:00Z"
    }
  ]
}
```

### PATCH /api/notifications?id=notification_id

Mark a notification as read.

**Response:**
```json
{
  "message": "Notification marked as read"
}
```

### PATCH /api/notifications?markAllRead=true

Mark all notifications as read.

**Response:**
```json
{
  "message": "All notifications marked as read"
}
```
