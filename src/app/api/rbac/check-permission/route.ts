/**
 * API Route: Check Permission
 *
 * This API route checks if the current user has a permission.
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';
import { db } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    // Get the session
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await db.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, organizationId: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const permission = searchParams.get('permission');
    const resourceType = searchParams.get('resourceType') || undefined;
    const resourceId = searchParams.get('resourceId') || undefined;
    const teamId = searchParams.get('teamId') || undefined;

    if (!permission) {
      return NextResponse.json(
        { error: 'Permission parameter is required' },
        { status: 400 }
      );
    }

    // Build permission context
    const context: PermissionContext = {
      userId: user.id,
      organizationId: user.organizationId || undefined,
      resourceType: resourceType as any,
      resourceId,
      teamId,
    };

    // Check permission
    const granted = await hasPermission(permission, context, {
      logUsage: true,
      action: 'API:check-permission',
      ipAddress: req.headers.get('x-forwarded-for') || req.ip,
      userAgent: req.headers.get('user-agent'),
    });

    return NextResponse.json({ granted });
  } catch (error) {
    console.error('Error checking permission:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
