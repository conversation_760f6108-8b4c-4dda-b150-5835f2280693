/**
 * API Route: User Roles
 *
 * This API route returns all roles for the current user.
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { getUserRoles } from '@/lib/rbac/rbac-service';
import { db } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    // Get the session
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await db.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      );
    }

    // Get user roles
    const roles = await getUserRoles(user.id);

    return NextResponse.json({ roles });
  } catch (error) {
    console.error('Error getting user roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
