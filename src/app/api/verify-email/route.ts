import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { verifyEmailToken } from "@/lib/tokens";

/**
 * GET /api/verify-email
 * Verify a user's email address using a token
 */
export async function GET(req: NextRequest) {
  try {
    const token = req.nextUrl.searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { error: "Missing token" },
        { status: 400 }
      );
    }

    // Verify the token
    const userId = await verifyEmailToken(token);

    if (!userId) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 400 }
      );
    }

    // Update the user's email verification status
    await db.user.update({
      where: { id: userId },
      data: { emailVerified: new Date() },
    });

    return NextResponse.json(
      { message: "Email verified successfully" },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error verifying email", error);
    return NextResponse.json(
      { error: "An error occurred while verifying your email" },
      { status: 500 }
    );
  }
}
