import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { CarbonCreditStatus, VerificationStatus, AuditLogType } from "@prisma/client";
// Define the sort order type
type SortOrder = "asc" | "desc";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

const carbonCreditSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").optional(),
  vintage: z.number().int().min(2000, "Vintage year must be 2000 or later"),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  location: z.string().optional(),
  country: z.string().optional(),
  projectId: z.string().optional(),
  serialNumber: z.string().optional(),
  certificationDate: z.string().optional(),
  expirationDate: z.string().optional(),
  verificationBody: z.string().optional(),
  additionalCertifications: z.array(z.string()).optional(),
  projectType: z.string().optional(),
  projectDescription: z.string().optional(),
  sdgs: z.array(z.number().int().min(1).max(17)).optional(), // Sustainable Development Goals (1-17)
});

// Original POST function wrapped with tenant isolation middleware
const postHandler = async (req: NextRequest) => {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create a carbon credit" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to create a carbon credit" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      name,
      description,
      quantity,
      price,
      minPurchaseQuantity,
      vintage,
      standard,
      methodology,
      location,
      country,
      projectId,
      serialNumber,
      certificationDate,
      expirationDate,
      verificationBody,
      additionalCertifications,
      projectType,
      projectDescription,
      sdgs,
    } = carbonCreditSchema.parse(body);

    // Check if the organization is verified
    const organization = await db.organization.findUnique({
      where: {
        id: session.user.organizationId,
      },
      select: {
        status: true,
        verificationStatus: true,
      },
    });

    if (organization?.status !== "ACTIVE") {
      return NextResponse.json(
        { error: "Your organization must be active to create carbon credits" },
        { status: 403 }
      );
    }

    // Create the carbon credit with all provided fields
    const carbonCredit = await db.carbonCredit.create({
      data: {
        name,
        description,
        quantity,
        availableQuantity: quantity, // Initially, all credits are available
        price,
        minPurchaseQuantity,
        vintage,
        standard,
        methodology,
        location,
        country,
        projectId,
        serialNumber,
        certificationDate: certificationDate ? new Date(certificationDate) : undefined,
        expirationDate: expirationDate ? new Date(expirationDate) : undefined,
        verificationBody,
        metadata: {
          additionalCertifications: additionalCertifications || [],
          sdgs: sdgs || [],
          projectType, // Move this to metadata
          projectDescription, // Add projectDescription to metadata too
        },
        status: CarbonCreditStatus.PENDING, // Initial status is PENDING
        verificationStatus: VerificationStatus.PENDING,
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: session.user.organizationId,
          },
        },
      },
    });

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: AuditLogType.CARBON_CREDIT_CREATED,
        description: `Carbon credit ${name} created`,
        userId: session.user.id,
        organizationId: session.user.organizationId,
        metadata: {
          carbonCreditId: carbonCredit.id,
          carbonCreditName: name,
          quantity,
          vintage,
          standard,
          methodology,
        },
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        title: "Carbon Credit Created",
        message: `Your carbon credit "${carbonCredit.name}" has been created and is pending verification.`,
        type: "CREDIT",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: session.user.organizationId,
          },
        },
        actionUrl: `/dashboard/carbon-credits/${carbonCredit.id}`,
        actionLabel: "View Carbon Credit",
      },
    });

    // Notify platform admins about the new carbon credit
    const admins = await db.user.findMany({
      where: { role: UserRole.ADMIN },
    });

    for (const admin of admins) {
      await db.notification.create({
        data: {
          title: "New Carbon Credit",
          message: `A new carbon credit ${name} has been created and needs verification.`,
          type: "CREDIT",
          priority: "NORMAL",
          user: {
            connect: {
              id: admin.id,
            },
          },
          actionUrl: `/admin/carbon-credits/${carbonCredit.id}/verification`,
          actionLabel: "Verify Carbon Credit",
        },
      });
    }

    logger.info(`User ${session.user.id} created carbon credit ${carbonCredit.id}`);

    return NextResponse.json(
      { carbonCredit, message: "Carbon credit created successfully" },
      { status: 201 }
    );
  } catch (error) {
    logger.error("Error creating carbon credit:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the carbon credit" },
      { status: 500 }
    );
  }
}

// Original GET function wrapped with tenant isolation middleware
const getHandler = async (req: NextRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status");

    // Only require authentication for non-public endpoints
    if (status && status !== "LISTED") {
      const session = await auth();

      if (!session?.user) {
        return NextResponse.json(
          { error: "You must be logged in to view non-public carbon credits" },
          { status: 401 }
        );
      }
    }

    // Get additional filter parameters
    const standard = searchParams.get("standard");
    const methodology = searchParams.get("methodology");
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const vintage = searchParams.get("vintage");

    // Build the filter object
    const filter: any = {};

    // Only show LISTED credits by default for public marketplace
    if (status) {
      filter.status = status;
    } else {
      filter.status = CarbonCreditStatus.LISTED;
    }

    if (standard) {
      filter.standard = standard;
    }

    if (methodology) {
      filter.methodology = methodology;
    }

    if (vintage) {
      filter.vintage = parseInt(vintage);
    }

    if (minPrice || maxPrice) {
      filter.price = {};

      if (minPrice) {
        filter.price.gte = parseFloat(minPrice);
      }

      if (maxPrice) {
        filter.price.lte = parseFloat(maxPrice);
      }
    }

    // Check if there are any carbon credits in the database
    const creditCount = await db.carbonCredit.count();

    if (creditCount === 0) {
      // If no carbon credits exist, return empty data with default filters
      return NextResponse.json({
        carbonCredits: [],
        filters: {
          standards: [],
          methodologies: [],
          vintages: [],
        },
        total: 0,
        message: "No carbon credits available in the marketplace yet."
      });
    }

    // Get carbon credits with more detailed information
    // Apply tenant isolation if user is authenticated
    let query = {
      where: filter,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            verificationStatus: true,
          },
        },
        _count: {
          select: {
            orders: true, // Simplify this to only count all orders
          },
        },
      },
      orderBy: {
        createdAt: "desc" as SortOrder,
      },
    };

    // Apply tenant isolation for non-public queries
    const session = await auth();
    if (session?.user && status && status !== "LISTED") {
      const tenantContext = await getTenantContext(session.user.id);
      query = withTenantQuery(query, tenantContext);
    }

    const carbonCredits = await db.carbonCredit.findMany(query);

    // Get unique standards and methodologies for filtering options
    const standards = await db.carbonCredit.findMany({
      where: { status: CarbonCreditStatus.LISTED },
      select: { standard: true },
      distinct: ["standard"],
    });

    const methodologies = await db.carbonCredit.findMany({
      where: { status: CarbonCreditStatus.LISTED },
      select: { methodology: true },
      distinct: ["methodology"],
    });

    const vintages = await db.carbonCredit.findMany({
      where: { status: CarbonCreditStatus.LISTED },
      select: { vintage: true },
      distinct: ["vintage"],
      orderBy: { vintage: "desc" },
    });

    return NextResponse.json({
      carbonCredits,
      filters: {
        standards: standards.map(s => s.standard),
        methodologies: methodologies.map(m => m.methodology),
        vintages: vintages.map(v => v.vintage),
      },
      total: carbonCredits.length,
    });
  } catch (error) {
    console.error("Error fetching carbon credits:", error);
    logger.error("Error fetching carbon credits:", error);

    // Return more detailed error information for debugging
    return NextResponse.json(
      {
        error: "An error occurred while fetching carbon credits",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const POST = withTenantIsolation(postHandler);

// For GET, we don't use tenant isolation middleware directly
// because we need to handle public endpoints without authentication
export async function GET(req: NextRequest) {
  return getHandler(req);
}
