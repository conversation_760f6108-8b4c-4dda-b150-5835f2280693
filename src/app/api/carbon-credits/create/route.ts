import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { CarbonCreditStatus, VerificationStatus } from "@prisma/client";
// TokenizationStatus is not defined in the schema yet

// Schema for creating a carbon credit
const createCarbonCreditSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  vintage: z.number().int().positive("Vintage year must be positive"),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  location: z.string().optional(),
  country: z.string().optional(),
  projectId: z.string().optional(),
  serialNumber: z.string().optional(),
  projectType: z.string().optional(),
  projectDescription: z.string().optional(),
  certificationDate: z.string().optional(),
  expirationDate: z.string().optional(),
  verificationBody: z.string().optional(),
  additionalCertifications: z.array(z.string()).optional(),
  sdgs: z.array(z.string()).optional(),
  minPurchaseQuantity: z.number().int().positive().default(1),
});

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create carbon credits" },
        { status: 401 }
      );
    }

    // Check if user has an organization
    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to create carbon credits" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = createCarbonCreditSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    const {
      name,
      description,
      quantity,
      price,
      vintage,
      standard,
      methodology,
      location,
      country,
      projectId,
      serialNumber,
      projectType,
      projectDescription,
      certificationDate,
      expirationDate,
      verificationBody,
      additionalCertifications,
      sdgs,
      minPurchaseQuantity,
    } = validationResult.data;

    // Create the carbon credit with all provided fields
    const carbonCredit = await db.carbonCredit.create({
      data: {
        name,
        description,
        quantity,
        availableQuantity: quantity, // Initially, all credits are available
        price,
        minPurchaseQuantity,
        vintage,
        standard,
        methodology,
        location,
        country,
        projectId,
        serialNumber,
        certificationDate: certificationDate ? new Date(certificationDate) : undefined,
        expirationDate: expirationDate ? new Date(expirationDate) : undefined,
        verificationBody,
        // Remove additionalCertifications as it doesn't exist in the model
        metadata: {
          sdgs: sdgs || [],
          additionalCertifications: additionalCertifications || [],
        },
        status: CarbonCreditStatus.PENDING, // Initial status is PENDING
        verificationStatus: VerificationStatus.PENDING,
        // Removed tokenizationStatus as it doesn't exist in the model
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: session.user.organizationId,
          },
        },
      },
    });

    logger.info(`Carbon credit created: ${carbonCredit.id} by user ${session.user.id}`);

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: "CARBON_CREDIT_CREATED",
        description: `Carbon credit "${name}" created`,
        userId: session.user.id,
        organizationId: session.user.organizationId,
        metadata: {
          carbonCreditId: carbonCredit.id,
          quantity,
          price,
          vintage,
          standard,
        },
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        title: "Carbon Credit Created",
        message: `Your carbon credit "${name}" has been created successfully.`,
        type: "SYSTEM",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: session.user.organizationId,
          },
        },
        actionUrl: `/dashboard/carbon-credits/${carbonCredit.id}`,
        actionLabel: "View Carbon Credit",
      },
    });

    return NextResponse.json(
      { 
        carbonCredit, 
        message: "Carbon credit created successfully" 
      },
      { status: 201 }
    );
  } catch (error) {
    logger.error("Error creating carbon credit:", error);

    return NextResponse.json(
      { error: "An error occurred while creating the carbon credit" },
      { status: 500 }
    );
  }
}
