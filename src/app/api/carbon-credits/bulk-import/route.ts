import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { z } from "zod";
import { logger } from "@/lib/logger";

// Schema for a single carbon credit in bulk import
const carbonCreditSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  vintage: z.number().int().positive("Vintage year must be positive"),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  location: z.string().optional(),
  country: z.string().optional(),
  projectId: z.string().optional(),
  serialNumber: z.string().optional(),
  verificationBody: z.string().optional(),
});

// Schema for bulk import request
const bulkImportSchema = z.object({
  carbonCredits: z.array(carbonCreditSchema).min(1, "At least one carbon credit is required"),
});

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to import carbon credits" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to import carbon credits" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { carbonCredits } = bulkImportSchema.parse(body);

    // Check if the organization is verified
    const organization = await db.organization.findUnique({
      where: {
        id: session.user.organizationId,
      },
      select: {
        status: true,
        verificationStatus: true,
      },
    });

    if (organization?.status !== "ACTIVE") {
      return NextResponse.json(
        { error: "Your organization must be active to import carbon credits" },
        { status: 403 }
      );
    }

    // Create carbon credits
    const createdCredits = await Promise.all(
      carbonCredits.map(async (credit) => {
        return db.carbonCredit.create({
          data: {
            name: credit.name,
            description: credit.description || "",
            quantity: credit.quantity,
            availableQuantity: credit.quantity, // Initially, all credits are available
            retiredQuantity: 0,
            price: credit.price,
            vintage: credit.vintage,
            standard: credit.standard,
            methodology: credit.methodology,
            location: credit.location,
            country: credit.country,
            projectId: credit.projectId,
            serialNumber: credit.serialNumber,
            verificationBody: credit.verificationBody,
            status: "PENDING",
            verificationStatus: "PENDING",
            user: {
              connect: {
                id: session.user.id,
              },
            },
            organization: {
              connect: {
                id: session.user.organizationId,
              },
            },
          },
        });
      })
    );

    logger.info(`Bulk imported ${createdCredits.length} carbon credits`);

    return NextResponse.json({
      success: true,
      imported: createdCredits.length,
      credits: createdCredits.map((credit) => ({
        id: credit.id,
        name: credit.name,
      })),
    });
  } catch (error) {
    console.error("Error bulk importing carbon credits:", error);
    logger.error("Error bulk importing carbon credits:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: "An error occurred while importing carbon credits",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
