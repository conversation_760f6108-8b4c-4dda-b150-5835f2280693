import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";

export async function GET(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view carbon credits" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to view carbon credits" },
        { status: 403 }
      );
    }

    // Get carbon credits for the organization
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        organizationId: session.user.organizationId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ carbonCredits });
  } catch (error) {
    logger.error("Error fetching organization carbon credits:", error);
    
    return NextResponse.json(
      { error: "An error occurred while fetching carbon credits" },
      { status: 500 }
    );
  }
}
