import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Get carbon credit metadata for tokenized carbon credits
 * This endpoint is public and does not require authentication
 * It is used by blockchain explorers and marketplaces to display token metadata
 */
async function getMetadataHandler(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    // Get the carbon credit ID from the URL
    const carbonCreditId = (await context.params).id;

    // Get the carbon credit with minimal fields first
    const carbonCredit = await db.carbonCredit.findUnique({
      where: {
        id: carbonCreditId,
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Get the base URL for the API
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || req.nextUrl.origin;

    // Create the metadata object
    const metadata = {
      name: carbonCredit.name,
      description: carbonCredit.description,
      image: (carbonCredit.images && carbonCredit.images.length > 0)
        ? carbonCredit.images[0]
        : `${baseUrl}/api/carbon-credits/${carbonCreditId}/image`,
      external_url: `${baseUrl}/dashboard/carbon-credits/${carbonCreditId}`,
      attributes: [
        {
          trait_type: "Standard",
          value: carbonCredit.standard,
        },
        {
          trait_type: "Methodology",
          value: carbonCredit.methodology,
        },
        {
          trait_type: "Vintage",
          value: carbonCredit.vintage,
        },
        {
          trait_type: "Quantity",
          value: carbonCredit.quantity,
          display_type: "number",
        },
        {
          trait_type: "Price",
          value: carbonCredit.price,
          display_type: "number",
        },
        {
          trait_type: "Status",
          value: carbonCredit.status,
        },
      ],
      properties: {
        project: {
          id: carbonCredit.projectId || carbonCredit.id,
          name: carbonCredit.name,
          location: carbonCredit.location || "Unknown",
          description: carbonCredit.description || "",
        },
        // Get organization data separately
        ...(await (async () => {
          // Safely handle organization data
          if (carbonCredit.organizationId) {
            try {
              const organization = await db.organization.findUnique({
                where: { id: carbonCredit.organizationId },
                select: { name: true, logo: true, website: true }
              });
              
              if (organization) {
                return {
                  issuer: {
                    name: organization.name,
                    logo: organization.logo,
                    website: organization.website,
                  }
                };
              }
            } catch (err) {
              logger.error("Error fetching organization:", err);
            }
          }
          return { issuer: { name: "Unknown Issuer" } };
        })()),
        // Safely handle token data
        tokenId: carbonCredit.tokenId || null,
        contractAddress: carbonCredit.contractAddress || null,
        chainId: carbonCredit.chainId || null,
      },
    };

    // Log the request
    logger.info(`Metadata requested for carbon credit ${carbonCreditId}`);

    // Return the metadata
    return NextResponse.json(metadata);
  } catch (error) {
    logger.error("Error getting carbon credit metadata:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to get carbon credit metadata",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getMetadataHandler);
