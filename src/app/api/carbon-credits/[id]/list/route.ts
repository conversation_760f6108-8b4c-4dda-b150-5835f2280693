import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { CarbonCreditStatus, VerificationStatus, AuditLogType } from "@prisma/client";

// Schema for listing a carbon credit
const listingSchema = z.object({
  price: z.number().positive("Price must be positive").optional(),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").optional(),
});

/**
 * POST /api/carbon-credits/[id]/list
 * List a carbon credit on the marketplace
 */
export async function POST(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to list carbon credits" },
        { status: 401 }
      );
    }

    const carbonCreditId = params.id;
    const body = await req.json();
    const { price, minPurchaseQuantity } = listingSchema.parse(body);

    // Check if carbon credit exists
    const carbonCredit = await db.carbonCredit.findUnique({
      where: {
        id: carbonCreditId,
      },
      include: {
        organization: true,
      },
    });

    if (!carbonCredit) {
      return NextResponse.json(
        { error: "Carbon credit not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to list this carbon credit
    const isAdmin = session.user.role === UserRole.ADMIN;
    const isOrgAdmin = session.user.role === UserRole.ORGANIZATION_ADMIN && session.user.organizationId === carbonCredit.organizationId;
    const isOwner = session.user.id === carbonCredit.userId;

    if (!isAdmin && !isOrgAdmin && !isOwner) {
      return NextResponse.json(
        { error: "You do not have permission to list this carbon credit" },
        { status: 403 }
      );
    }

    // Check if carbon credit is verified
    if (carbonCredit.verificationStatus !== VerificationStatus.VERIFIED) {
      return NextResponse.json(
        { error: "Carbon credit must be verified before it can be listed" },
        { status: 400 }
      );
    }

    // Check if carbon credit is already listed
    if (carbonCredit.status === CarbonCreditStatus.LISTED) {
      return NextResponse.json(
        { error: "Carbon credit is already listed" },
        { status: 400 }
      );
    }

    // Update carbon credit
    const updatedCarbonCredit = await db.carbonCredit.update({
      where: { id: carbonCreditId },
      data: {
        status: CarbonCreditStatus.LISTED,
        listingDate: new Date(),
        ...(price && { price }),
        ...(minPurchaseQuantity && { minPurchaseQuantity }),
      },
    });

    // If price was updated, add to price history
    if (price && price !== carbonCredit.price) {
      await db.carbonCreditPrice.create({
        data: {
          price,
          reason: "Price update on listing",
          carbonCredit: { connect: { id: carbonCreditId } },
        },
      });
    }

    // Calculate listing fee
    const listingFee = carbonCredit.price * carbonCredit.availableQuantity * carbonCredit.organization.listingFeeRate;

    // Create a billing record for the listing fee
    await db.billingRecord.create({
      data: {
        amount: listingFee,
        description: `Listing fee for ${carbonCredit.name}`,
        type: "LISTING_FEE",
        status: "PENDING",
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
        organization: { connect: { id: carbonCredit.organizationId } },
      },
    });

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: AuditLogType.CARBON_CREDIT_UPDATED,
        description: `Carbon credit ${carbonCredit.name} listed on marketplace`,
        userId: session.user.id,
        organizationId: carbonCredit.organizationId,
        metadata: {
          carbonCreditId,
          carbonCreditName: carbonCredit.name,
          price: price || carbonCredit.price,
          previousStatus: carbonCredit.status,
          newStatus: CarbonCreditStatus.LISTED,
        },
      },
    });

    // Create a notification for the carbon credit owner
    await db.notification.create({
      data: {
        title: "Carbon Credit Listed",
        message: `Your carbon credit "${carbonCredit.name}" has been listed on the marketplace.`,
        type: "CREDIT",
        user: {
          connect: {
            id: carbonCredit.userId,
          },
        },
        organization: {
          connect: {
            id: carbonCredit.organizationId,
          },
        },
        actionUrl: `/dashboard/carbon-credits/${carbonCreditId}`,
        actionLabel: "View Carbon Credit",
      },
    });

    // Notify users who might be interested in this carbon credit
    // For simplicity, we'll notify all users for now
    // In a production environment, this would be more targeted based on user preferences
    const users = await db.user.findMany({
      where: {
        organizationId: {
          not: carbonCredit.organizationId, // Don't notify users from the same organization
        },
      },
      take: 10, // Limit to 10 users for now
    });

    for (const user of users) {
      await db.notification.create({
        data: {
          title: "New Carbon Credit Listing",
          message: `${carbonCredit.name} is now available on the marketplace.`,
          type: "MARKETPLACE",
          user: {
            connect: {
              id: user.id,
            },
          },
          actionUrl: `/marketplace/${carbonCreditId}`,
          actionLabel: "View Listing",
        },
      });
    }

    logger.info(`Carbon credit ${carbonCreditId} listed by user ${session.user.id}`);

    return NextResponse.json({
      carbonCredit: updatedCarbonCredit,
      message: "Carbon credit listed successfully",
      listingFee,
    });
  } catch (error) {
    logger.error(`Error listing carbon credit:`, error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while listing the carbon credit" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/carbon-credits/[id]/list
 * Remove a carbon credit from the marketplace
 */
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to delist carbon credits" },
        { status: 401 }
      );
    }

    const carbonCreditId = params.id;

    // Check if carbon credit exists
    const carbonCredit = await db.carbonCredit.findUnique({
      where: {
        id: carbonCreditId,
      },
      include: {
        organization: true,
      },
    });

    if (!carbonCredit) {
      return NextResponse.json(
        { error: "Carbon credit not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to delist this carbon credit
    const isAdmin = session.user.role === UserRole.ADMIN;
    const isOrgAdmin = session.user.role === UserRole.ORGANIZATION_ADMIN && session.user.organizationId === carbonCredit.organizationId;
    const isOwner = session.user.id === carbonCredit.userId;

    if (!isAdmin && !isOrgAdmin && !isOwner) {
      return NextResponse.json(
        { error: "You do not have permission to delist this carbon credit" },
        { status: 403 }
      );
    }

    // Check if carbon credit is listed
    if (carbonCredit.status !== CarbonCreditStatus.LISTED) {
      return NextResponse.json(
        { error: "Carbon credit is not currently listed" },
        { status: 400 }
      );
    }

    // Update carbon credit
    const updatedCarbonCredit = await db.carbonCredit.update({
      where: { id: carbonCreditId },
      data: {
        status: CarbonCreditStatus.VERIFIED, // Return to verified status
        listingDate: null,
      },
    });

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: AuditLogType.CARBON_CREDIT_UPDATED,
        description: `Carbon credit ${carbonCredit.name} removed from marketplace`,
        userId: session.user.id,
        organizationId: carbonCredit.organizationId,
        metadata: {
          carbonCreditId,
          carbonCreditName: carbonCredit.name,
          previousStatus: carbonCredit.status,
          newStatus: CarbonCreditStatus.VERIFIED,
        },
      },
    });

    // Create a notification for the carbon credit owner
    await db.notification.create({
      data: {
        title: "Carbon Credit Delisted",
        message: `Your carbon credit "${carbonCredit.name}" has been removed from the marketplace.`,
        type: "CREDIT",
        user: {
          connect: {
            id: carbonCredit.userId,
          },
        },
        organization: {
          connect: {
            id: carbonCredit.organizationId,
          },
        },
        actionUrl: `/dashboard/carbon-credits/${carbonCreditId}`,
        actionLabel: "View Carbon Credit",
      },
    });

    logger.info(`Carbon credit ${carbonCreditId} delisted by user ${session.user.id}`);

    return NextResponse.json({
      carbonCredit: updatedCarbonCredit,
      message: "Carbon credit removed from marketplace successfully",
    });
  } catch (error) {
    logger.error(`Error delisting carbon credit:`, error);

    return NextResponse.json(
      { error: "An error occurred while delisting the carbon credit" },
      { status: 500 }
    );
  }
}
