import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for updating carbon credit
const updateCarbonCreditSchema = z.object({
  name: z.string().min(2).optional(),
  description: z.string().optional(),
  quantity: z.number().positive().optional(),
  price: z.number().positive().optional(),
  vintage: z.number().int().min(2000).optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  location: z.string().optional(),
  status: z.enum(["PENDING", "VERIFIED", "LISTED", "SOLD", "RETIRED"]).optional(),
});

// Original GET handler
async function getHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view carbon credits" },
        { status: 401 }
      );
    }

    // Get the carbon credit with tenant isolation
    const tenantContext = await getTenantContext(session.user.id);

    // Create the base query
    let query = {
      where: {
        id: params.id,
      },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      query = withTenantIsolation(query, tenantContext);
    }

    const carbonCredit = await db.carbonCredit.findFirst(query);

    if (!carbonCredit) {
      return NextResponse.json(
        { error: "Carbon credit not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ carbonCredit });
  } catch (error) {
    logger.error(`Error fetching carbon credit ${params.id}:`, error);

    return NextResponse.json(
      { error: "An error occurred while fetching the carbon credit" },
      { status: 500 }
    );
  }
}

// Enhanced PATCH handler with change tracking
async function patchHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update carbon credits" },
        { status: 401 }
      );
    }

    // Check if the carbon credit exists and belongs to the user's organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: params.id,
        organizationId: session.user.organizationId,
      },
    });

    if (!carbonCredit) {
      return NextResponse.json(
        { error: "Carbon credit not found or you don't have permission to update it" },
        { status: 404 }
      );
    }

    // Parse and validate the request body
    const body = await req.json();
    const validatedData = updateCarbonCreditSchema.safeParse(body);

    if (!validatedData.success) {
      return NextResponse.json(
        { error: validatedData.error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    // Check if status transition is valid
    if (validatedData.data.status && validatedData.data.status !== carbonCredit.status) {
      // Define valid transitions
      const validTransitions: Record<string, string[]> = {
        "PENDING": ["VERIFIED"],
        "VERIFIED": ["LISTED", "RETIRED"],
        "LISTED": ["VERIFIED", "RETIRED"],
        "RETIRED": [],
      };

      // Check if the transition is valid
      if (!validTransitions[carbonCredit.status]?.includes(validatedData.data.status)) {
        return NextResponse.json(
          { error: `Invalid status transition from ${carbonCredit.status} to ${validatedData.data.status}` },
          { status: 400 }
        );
      }
    }

    // Track changes for history
    const changes: Record<string, { from: any; to: any }> = {};

    Object.keys(validatedData.data).forEach(key => {
      if (validatedData.data[key] !== (carbonCredit as any)[key]) {
        changes[key] = {
          from: (carbonCredit as any)[key],
          to: validatedData.data[key],
        };
      }
    });

    // Update the carbon credit
    const updatedCarbonCredit = await db.carbonCredit.update({
      where: {
        id: params.id,
      },
      data: validatedData.data,
    });

    // Create a change history record
    // In a real implementation, you would have a separate table for this
    // This is a placeholder for the actual implementation
    try {
      // Example of how you might implement this with a real table
      // await db.carbonCreditChangeHistory.create({
      //   data: {
      //     carbonCreditId: params.id,
      //     userId: session.user.id,
      //     changes: JSON.stringify(changes),
      //     timestamp: new Date(),
      //   },
      // });

      // For now, just log the changes
      logger.info(`Carbon credit ${params.id} updated by user ${session.user.id}`);
      logger.info("Changes:", JSON.stringify(changes));
    } catch (historyError) {
      // Log the error but don't fail the update
      logger.error("Failed to record change history:", historyError);
    }

    // Create a notification for the user
    await db.notification.create({
      data: {
        title: "Carbon Credit Updated",
        message: `Your carbon credit "${updatedCarbonCredit.name}" has been updated.`,
        type: "CREDIT",
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });

    return NextResponse.json({
      carbonCredit: updatedCarbonCredit,
      message: "Carbon credit updated successfully",
      changes: Object.keys(changes).length > 0 ? changes : null,
    });
  } catch (error) {
    logger.error(`Error updating carbon credit ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating the carbon credit" },
      { status: 500 }
    );
  }
}

// Original DELETE handler
async function deleteHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete carbon credits" },
        { status: 401 }
      );
    }

    // Check if the carbon credit exists and belongs to the user's organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: params.id,
        organizationId: session.user.organizationId,
      },
    });

    if (!carbonCredit) {
      return NextResponse.json(
        { error: "Carbon credit not found or you don't have permission to delete it" },
        { status: 404 }
      );
    }

    // Check if the carbon credit has associated orders
    const orders = await db.order.findMany({
      where: {
        carbonCreditId: params.id,
      },
    });

    if (orders.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete carbon credit with associated orders" },
        { status: 400 }
      );
    }

    // Delete the carbon credit
    await db.carbonCredit.delete({
      where: {
        id: params.id,
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        title: "Carbon Credit Deleted",
        message: `Your carbon credit "${carbonCredit.name}" has been deleted.`,
        type: "CREDIT",
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Carbon credit deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting carbon credit ${params.id}:`, error);

    return NextResponse.json(
      { error: "An error occurred while deleting the carbon credit" },
      { status: 500 }
    );
  }
}

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('carbon_credit', 'id')(getHandler);
export const PATCH = withResourceIsolation('carbon_credit', 'id')(patchHandler);
