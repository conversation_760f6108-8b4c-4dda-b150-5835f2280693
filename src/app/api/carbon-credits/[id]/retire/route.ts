import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { retireCarbonCredit } from "@/lib/blockchain";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";

// Schema for retirement request
const retirementSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  reason: z.string().min(1, "Reason is required"),
  beneficiary: z.string().optional(),
});

/**
 * Retire a tokenized carbon credit
 */
async function retireHandler(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to retire a carbon credit",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to retire a carbon credit",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { amount, reason, beneficiary } = retirementSchema.parse(body);

    // Get the carbon credit ID from the URL
    const carbonCreditId = params.id;

    // Check if the carbon credit exists and belongs to the organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: carbonCreditId,
        organizationId: session.user.organizationId,
        status: "VERIFIED", // Only verified carbon credits can be retired
      },
      include: {
        organization: {
          include: {
            wallets: true,
          },
        },
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found, does not belong to your organization, or is not tokenized",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the organization has any wallets
    if (!carbonCredit.organization.wallets || carbonCredit.organization.wallets.length === 0) {
      throw new ApiError(
        "Organization does not have a wallet",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the amount is available
    if (amount > carbonCredit.availableQuantity) {
      throw new ApiError(
        `Not enough quantity available. Available: ${carbonCredit.availableQuantity} tons`,
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the token ID is available
    if (!carbonCredit.tokenId) {
      throw new ApiError(
        "Carbon credit does not have a token ID",
        ErrorType.VALIDATION,
        400
      );
    }

    // Retire the carbon credit
    // Use the first wallet in the organization's wallets array
    const wallet = carbonCredit.organization.wallets[0];
    
    const retirementResult = await retireCarbonCredit(
      wallet.address,
      parseInt(carbonCredit.tokenId),
      amount,
      wallet.encryptedKey,
      wallet.network as SupportedNetwork, // Use the wallet's network instead
      wallet.isTestnet // Use the wallet's testnet flag
    );

    // Create a retirement record using our proper Retirement model
    // Note: In a real scenario, we would run `prisma generate` to update the client types
    // This is a temporary type assertion until that happens
    const retirement = await db.retirement.create({
      data: {
        amount,
        reason,
        beneficiary,
        tokenId: carbonCredit.tokenId,
        network: wallet.network,
        chainId: wallet.chainId,
        contractAddress: retirementResult.contractAddress,
        transactionHash: retirementResult.transactionHash,
        status: retirementResult.status === "success" ? "COMPLETED" : "FAILED",
        carbonCredit: {
          connect: { id: carbonCreditId },
        },
        organization: {
          connect: { id: session.user.organizationId },
        },
        user: {
          connect: { id: session.user.id },
        },
      },
    });

    // Update carbon credit available quantity if retirement was successful
    if (retirementResult.status === "success") {
      // Note: In a real scenario, we would run `prisma generate` to update the client types
      await (db.carbonCredit.update)({
        where: { id: carbonCreditId },
        data: {
          availableQuantity: {
            decrement: amount,
          },
          retiredQuantity: {
            increment: amount,
          },
          retirementDate: new Date(),
          retirementReason: reason || "Voluntary retirement",
          retirementBeneficiary: beneficiary || "Not specified",
          status: "RETIRED",
        },
      });
    }

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "CARBON_CREDIT_RETIRED",
        description: `${amount} tons of carbon credit ${carbonCredit.name} retired`,
        user: {
          connect: { id: session.user.id },
        },
        organization: {
          connect: { id: session.user.organizationId },
        },
        metadata: {
          carbonCreditId,
          tokenId: carbonCredit.tokenId,
          amount,
          reason,
          beneficiary,
          transactionHash: retirementResult.transactionHash,
          status: retirementResult.status,
        },
      },
    });

    logger.info(`Carbon credit ${carbonCreditId} retired by user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: "Carbon credit retired successfully",
      transactionHash: retirementResult.transactionHash,
      blockExplorerUrl: `${wallet.network === SupportedNetwork.ETHEREUM ? 'https://etherscan.io' :
        wallet.network === SupportedNetwork.POLYGON ? 'https://polygonscan.com' :
        wallet.network === SupportedNetwork.ARBITRUM ? 'https://arbiscan.io' :
        wallet.network === SupportedNetwork.OPTIMISM ? 'https://optimistic.etherscan.io' :
        'https://basescan.org'}/tx/${retirementResult.transactionHash}`,
    });
  } catch (error) {
    logger.error("Error retiring carbon credit:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to retire carbon credit",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(retireHandler);
