import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { VerificationService } from "@/lib/verification";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";

// Schema for tokenization request
const tokenizationSchema = z.object({
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]).default("polygon"),
  useTestnet: z.boolean().default(true),
  metadata: z.record(z.any()).optional(),
});

/**
 * Tokenize a carbon credit
 */
async function tokenizeHandler(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to tokenize a carbon credit",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to tokenize a carbon credit",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json().catch(() => ({}));
    const { network, useTestnet, metadata } = tokenizationSchema.parse(body);

    // Get the carbon credit ID from the URL
    const carbonCreditId = params.id;

    // Check if the carbon credit exists and belongs to the organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: carbonCreditId,
        organizationId: session.user.organizationId,
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found or does not belong to your organization",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the carbon credit is verified
    if (carbonCredit.status !== "VERIFIED") {
      throw new ApiError(
        "Carbon credit must be verified before it can be tokenized",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the carbon credit is already tokenized by looking at the token data and status
    if (carbonCredit.tokenId || carbonCredit.contractAddress) {
      throw new ApiError(
        "Carbon credit is already tokenized",
        ErrorType.VALIDATION,
        400
      );
    }

    // Request tokenization
    const verificationService = new VerificationService();
    const result = await verificationService.requestTokenization({
      carbonCreditId,
      network: network as SupportedNetwork,
      useTestnet,
      userId: session.user.id,
      organizationId: session.user.organizationId,
      metadata,
    });

    logger.info(`Carbon credit ${carbonCreditId} tokenized by user ${session.user.id}`);

    return NextResponse.json({
      tokenization: result.tokenization,
      message: "Carbon credit tokenized successfully",
      blockExplorerUrl: `${result.tokenizationResult.network === SupportedNetwork.ETHEREUM ? 'https://etherscan.io' :
        result.tokenizationResult.network === SupportedNetwork.POLYGON ? 'https://polygonscan.com' :
        result.tokenizationResult.network === SupportedNetwork.ARBITRUM ? 'https://arbiscan.io' :
        result.tokenizationResult.network === SupportedNetwork.OPTIMISM ? 'https://optimistic.etherscan.io' :
        'https://basescan.org'}/tx/${result.tokenizationResult.transactionHash}`,
    });
  } catch (error) {
    logger.error("Error tokenizing carbon credit:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to tokenize carbon credit",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(tokenizeHandler);
