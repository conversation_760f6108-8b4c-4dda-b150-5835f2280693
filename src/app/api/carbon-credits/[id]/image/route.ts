import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Get carbon credit image for tokenized carbon credits
 * This endpoint is public and does not require authentication
 * It is used by blockchain explorers and marketplaces to display token images
 */
async function getImageHandler(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    // Get the carbon credit ID from the URL
    const carbonCreditId = (await context.params).id;

    // Check if the carbon credit exists
    const carbonCredit = await db.carbonCredit.findUnique({
      where: {
        id: carbonCreditId,
      },
      select: {
        name: true,
        images: true,
        standard: true,
        organization: {
          select: {
            name: true,
            logo: true,
          },
        },
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // If the carbon credit has images, redirect to the first one
    if (carbonCredit.images && carbonCredit.images.length > 0) {
      return NextResponse.redirect(carbonCredit.images[0]);
    }

    // If the organization has a logo, redirect to it
    if (carbonCredit.organization.logo) {
      return NextResponse.redirect(carbonCredit.organization.logo);
    }

    // Otherwise, generate a placeholder SVG image
    const svg = generatePlaceholderSVG(
      carbonCredit.name,
      carbonCredit.standard,
      carbonCredit.organization.name
    );

    // Log the request
    logger.info(`Image requested for carbon credit ${carbonCreditId}`);

    // Return the SVG image
    return new NextResponse(svg, {
      headers: {
        "Content-Type": "image/svg+xml",
        "Cache-Control": "public, max-age=86400",
      },
    });
  } catch (error) {
    logger.error("Error getting carbon credit image:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Generate a default error SVG
    const errorSvg = generateErrorSVG();

    return new NextResponse(errorSvg, {
      headers: {
        "Content-Type": "image/svg+xml",
        "Cache-Control": "public, max-age=3600",
      },
    });
  }
}

/**
 * Generate a placeholder SVG image for a carbon credit
 * @param name Carbon credit name
 * @param standard Carbon credit standard
 * @param organization Organization name
 * @returns SVG image as a string
 */
function generatePlaceholderSVG(name: string, standard: string, organization: string): string {
  // Generate a deterministic color based on the name
  const hash = name.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const hue = hash % 360;
  const saturation = 70;
  const lightness = 50;
  const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  const textColor = lightness > 50 ? "#000000" : "#FFFFFF";

  // Truncate long text
  const truncatedName = name.length > 20 ? name.substring(0, 17) + "..." : name;
  const truncatedStandard = standard.length > 15 ? standard.substring(0, 12) + "..." : standard;
  const truncatedOrganization = organization.length > 20 ? organization.substring(0, 17) + "..." : organization;

  // Generate the SVG
  return `<svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
    <rect width="512" height="512" fill="${color}" />
    <circle cx="256" cy="180" r="80" fill="white" fill-opacity="0.2" />
    <text x="256" y="280" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">${truncatedName}</text>
    <text x="256" y="320" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">${truncatedStandard}</text>
    <text x="256" y="360" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle">by ${truncatedOrganization}</text>
    <text x="256" y="420" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle">Carbon Credit Token</text>
    <text x="256" y="450" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">Carbonix Platform</text>
  </svg>`;
}

/**
 * Generate an error SVG image
 * @returns SVG image as a string
 */
function generateErrorSVG(): string {
  return `<svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
    <rect width="512" height="512" fill="#FF5555" />
    <circle cx="256" cy="180" r="80" fill="white" fill-opacity="0.2" />
    <text x="256" y="280" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">Error</text>
    <text x="256" y="320" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">Image Not Available</text>
    <text x="256" y="420" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle">Carbon Credit Token</text>
    <text x="256" y="450" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">Carbonix Platform</text>
  </svg>`;
}

export const GET = withErrorHandling(getImageHandler);
