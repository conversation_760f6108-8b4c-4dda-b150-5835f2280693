import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

/**
 * Get transaction history for a carbon credit
 */
async function getTransactionsHandler(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view transaction history",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get the carbon credit ID from the URL
    const carbonCreditId = params.id;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let carbonCreditQuery = {
      where: {
        id: carbonCreditId,
      },
      select: {
        id: true,
        organizationId: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      carbonCreditQuery = withTenantIsolation(carbonCreditQuery, tenantContext);
    }

    // Check if the carbon credit exists
    const carbonCredit = await db.carbonCredit.findFirst(carbonCreditQuery);

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the user has access to the carbon credit
    // Allow access if the user is an admin or belongs to the organization that owns the carbon credit
    const hasAccess =
      session.user.role === "ADMIN" ||
      session.user.organizationId === carbonCredit.organizationId;

    if (!hasAccess) {
      throw new ApiError(
        "You do not have permission to view this carbon credit's transaction history",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Get all transactions for the carbon credit
    // Note: In a real scenario, we would run `prisma generate` to update the client types
    const tokenizations = await db.tokenization.findMany({
      where: {
        carbonCreditId,
      },
      select: {
        id: true,
        tokenId: true,
        amount: true,
        network: true,
        contractAddress: true,
        transactionHash: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Note: In a real scenario, we would run `prisma generate` to update the client types
    const transfers = await db.tokenTransfer.findMany({
      where: {
        carbonCreditId,
      },
      select: {
        id: true,
        fromAddress: true,
        toAddress: true,
        amount: true,
        tokenId: true,
        network: true,
        contractAddress: true,
        transactionHash: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const retirements = await db.retirement.findMany({
      where: {
        carbonCreditId,
      },
      select: {
        id: true,
        amount: true,
        reason: true,
        beneficiary: true,
        tokenId: true,
        network: true,
        contractAddress: true,
        transactionHash: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Format the transactions
    const formattedTokenizations = tokenizations.map((tokenization) => ({
      id: tokenization.id,
      type: "TOKENIZATION",
      amount: tokenization.amount,
      tokenId: tokenization.tokenId,
      network: tokenization.network,
      contractAddress: tokenization.contractAddress,
      transactionHash: tokenization.transactionHash,
      status: tokenization.status,
      createdAt: tokenization.createdAt,
      updatedAt: tokenization.updatedAt,
    }));

    const formattedTransfers = transfers.map((transfer) => ({
      id: transfer.id,
      type: "TRANSFER",
      amount: transfer.amount,
      tokenId: transfer.tokenId,
      fromAddress: transfer.fromAddress,
      toAddress: transfer.toAddress,
      network: transfer.network,
      contractAddress: transfer.contractAddress,
      transactionHash: transfer.transactionHash,
      status: transfer.status,
      createdAt: transfer.createdAt,
      updatedAt: transfer.updatedAt,
    }));

    const formattedRetirements = retirements.map((retirement) => ({
      id: retirement.id,
      type: "RETIREMENT",
      amount: retirement.amount,
      tokenId: retirement.tokenId,
      reason: retirement.reason,
      beneficiary: retirement.beneficiary,
      network: retirement.network,
      contractAddress: retirement.contractAddress,
      transactionHash: retirement.transactionHash,
      status: retirement.status,
      createdAt: retirement.createdAt,
      updatedAt: retirement.updatedAt,
    }));

    // Combine all transactions and sort by creation date (newest first)
    const allTransactions = [
      ...formattedTokenizations,
      ...formattedTransfers,
      ...formattedRetirements,
    ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    logger.info(`Retrieved transaction history for carbon credit ${carbonCreditId}`);

    return NextResponse.json({
      transactions: allTransactions,
    });
  } catch (error) {
    logger.error("Error getting transaction history:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to get transaction history",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and resource isolation
const wrappedHandler = withErrorHandling(getTransactionsHandler);

export const GET = withResourceIsolation('carbon_credit', 'id')(wrappedHandler);
