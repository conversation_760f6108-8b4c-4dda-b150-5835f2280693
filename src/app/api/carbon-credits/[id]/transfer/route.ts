import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { transferCarbonCredit } from "@/lib/blockchain";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";

// Schema for transfer request
const transferSchema = z.object({
  toAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, "Invalid Ethereum address"),
  amount: z.number().positive("Amount must be positive"),
});

/**
 * Transfer a tokenized carbon credit
 */
async function transferHandler(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to transfer a carbon credit",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to transfer a carbon credit",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { toAddress, amount } = transferSchema.parse(body);

    // Get the carbon credit ID from the URL
    const carbonCreditId = params.id;

    // Check if the carbon credit exists and belongs to the organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: carbonCreditId,
        organizationId: session.user.organizationId,
        status: "TOKENIZED", // Only tokenized carbon credits can be transferred
      },
      include: {
        organization: {
          include: {
            wallets: true,
          },
        },
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found, does not belong to your organization, or is not tokenized",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the organization has a wallet
    if (!carbonCredit.organization.wallets || carbonCredit.organization.wallets.length === 0) {
      throw new ApiError(
        "Organization does not have a wallet",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the amount is available
    if (amount > carbonCredit.availableQuantity) {
      throw new ApiError(
        `Not enough quantity available. Available: ${carbonCredit.availableQuantity} tons`,
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the token ID is available
    if (!carbonCredit.tokenId) {
      throw new ApiError(
        "Carbon credit does not have a token ID",
        ErrorType.VALIDATION,
        400
      );
    }

    // Transfer the carbon credit
    const transferResult = await transferCarbonCredit(
      carbonCredit.organization.wallets[0].address,
      toAddress,
      parseInt((carbonCredit as any).tokenId),
      amount,
      carbonCredit.organization.wallets[0].encryptedKey,
      (carbonCredit as any).tokenNetwork as SupportedNetwork,
      true // Use testnet for now
    );

    // Create a transfer record
    // Note: In a real scenario, we would run `prisma generate` to update the client types
    const transfer = await (db as any).tokenTransfer.create({
      data: {
        fromAddress: carbonCredit.organization.wallets[0].address,
        toAddress,
        amount,
        tokenId: (carbonCredit as any).tokenId,
        network: (carbonCredit as any).tokenNetwork,
        chainId: transferResult.chainId,
        contractAddress: transferResult.contractAddress,
        transactionHash: transferResult.transactionHash,
        status: transferResult.status === "success" ? "COMPLETED" : "FAILED",
        carbonCredit: {
          connect: { id: carbonCreditId },
        },
        organization: {
          connect: { id: session.user.organizationId },
        },
        user: {
          connect: { id: session.user.id },
        },
      },
    });

    // Update carbon credit available quantity if transfer was successful
    if (transferResult.status === "success") {
      await db.carbonCredit.update({
        where: { id: carbonCreditId },
        data: {
          availableQuantity: {
            decrement: amount,
          },
        },
      });
    }

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "CARBON_CREDIT_RETIRED" as any, // Using a known type with type assertion for now
        description: `${amount} tons of carbon credit ${carbonCredit.name} transferred to ${toAddress}`,
        user: {
          connect: { id: session.user.id },
        },
        organization: {
          connect: { id: session.user.organizationId },
        },
        metadata: {
          carbonCreditId,
          tokenId: carbonCredit.tokenId,
          fromAddress: carbonCredit.organization.wallets[0].address,
          toAddress,
          amount,
          transactionHash: transferResult.transactionHash,
          status: transferResult.status,
        },
      },
    });

    logger.info(`Carbon credit ${carbonCreditId} transferred by user ${session.user.id}`);

    return NextResponse.json({
      transfer,
      message: "Carbon credit transferred successfully",
      blockExplorerUrl: `${((carbonCredit as any).tokenNetwork === SupportedNetwork.ETHEREUM ? 'https://etherscan.io' :
        (carbonCredit as any).tokenNetwork === SupportedNetwork.POLYGON ? 'https://polygonscan.com' :
        (carbonCredit as any).tokenNetwork === SupportedNetwork.ARBITRUM ? 'https://arbiscan.io' :
        (carbonCredit as any).tokenNetwork === SupportedNetwork.OPTIMISM ? 'https://optimistic.etherscan.io' :
        'https://basescan.org')}/tx/${transferResult.transactionHash}`,
    });
  } catch (error) {
    logger.error("Error transferring carbon credit:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to transfer carbon credit",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(transferHandler);
