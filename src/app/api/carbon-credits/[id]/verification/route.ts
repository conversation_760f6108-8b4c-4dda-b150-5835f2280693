import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { verificationService, VerificationType } from "@/lib/verification";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

// Schema for requesting verification
const requestVerificationSchema = z.object({
  type: z.enum(["INITIAL", "ANNUAL", "METHODOLOGY", "OWNERSHIP", "RETIREMENT"]),
  documents: z.array(z.string().url("Invalid document URL")),
  notes: z.string().optional(),
});

/**
 * GET /api/carbon-credits/[id]/verification
 * Get verifications for a carbon credit
 */
// Original GET handler
async function getHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const carbonCreditId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the carbon credit exists and belongs to the user's organization
    let carbonCreditQuery = {
      where: {
        id: carbonCreditId,
      },
      select: {
        id: true,
        organizationId: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      carbonCreditQuery = withTenantIsolation(carbonCreditQuery, tenantContext);
    }

    const carbonCredit = await db.carbonCredit.findFirst(carbonCreditQuery);

    if (!carbonCredit) {
      logger.warn(`User ${session.user.id} attempted to access verifications for carbon credit ${carbonCreditId} that doesn't exist or they don't have access to`);
      return NextResponse.json(
        { error: "Carbon credit not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Get verifications
    const verifications = await verificationService.getVerifications(carbonCreditId);

    return NextResponse.json({ verifications });
  } catch (error) {
    logger.error("Error getting verifications:", error);
    return NextResponse.json(
      { error: "Failed to get verifications" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/carbon-credits/[id]/verification
 * Request verification for a carbon credit
 */
// Original POST handler
async function postHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const carbonCreditId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the carbon credit exists and belongs to the user's organization
    let carbonCreditQuery = {
      where: {
        id: carbonCreditId,
      },
      select: {
        id: true,
        organizationId: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      carbonCreditQuery = withTenantIsolation(carbonCreditQuery, tenantContext);
    }

    const carbonCredit = await db.carbonCredit.findFirst(carbonCreditQuery);

    if (!carbonCredit) {
      logger.warn(`User ${session.user.id} attempted to request verification for carbon credit ${carbonCreditId} that doesn't exist or they don't have access to`);
      return NextResponse.json(
        { error: "Carbon credit not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = requestVerificationSchema.parse(body);

    // Request verification
    const verification = await verificationService.requestVerification({
      carbonCreditId,
      type: validatedData.type as VerificationType,
      documents: validatedData.documents,
      notes: validatedData.notes,
      userId: session.user.id,
      organizationId: session.user.organizationId,
    });

    return NextResponse.json({
      verification,
      message: "Verification requested successfully",
    });
  } catch (error) {
    logger.error("Error requesting verification:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to request verification" },
      { status: 500 }
    );
  }
}

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('carbon_credit', 'id')(getHandler);
