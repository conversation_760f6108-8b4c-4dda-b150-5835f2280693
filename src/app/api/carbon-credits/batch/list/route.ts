import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { CarbonCreditStatus, VerificationStatus, AuditLogType } from "@prisma/client";

// Schema for batch listing
const batchListingSchema = z.object({
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  price: z.number().positive("Price must be positive"),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").optional(),
});

/**
 * POST /api/carbon-credits/batch/list
 * List multiple carbon credits at once
 */
async function batchListHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to list carbon credits",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Check if user has permission to list carbon credits
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to list carbon credits",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { carbonCreditIds, price, minPurchaseQuantity } = batchListingSchema.parse(body);

    // Get organization ID for filtering
    const organizationId = session.user.organizationId;

    if (!organizationId && !isAdmin) {
      throw new ApiError(
        "You must be part of an organization to list carbon credits",
        ErrorType.FORBIDDEN,
        403
      );
    }

    // Process results
    const results = {
      processed: 0,
      skipped: 0,
      errors: [] as string[],
    };

    // Process each carbon credit
    for (const carbonCreditId of carbonCreditIds) {
      try {
        // Find the carbon credit
        const carbonCredit = await db.carbonCredit.findFirst({
          where: {
            id: carbonCreditId,
            ...(isAdmin ? {} : { organizationId }),
          },
          include: {
            organization: true,
          },
        });

        if (!carbonCredit) {
          results.errors.push(`Carbon credit not found: ${carbonCreditId}`);
          results.skipped++;
          continue;
        }

        // Check if carbon credit is already listed
        if (carbonCredit.status === CarbonCreditStatus.LISTED) {
          results.errors.push(`Carbon credit already listed: ${carbonCredit.name}`);
          results.skipped++;
          continue;
        }

        // Check if carbon credit is verified
        if (carbonCredit.verificationStatus !== VerificationStatus.VERIFIED) {
          results.errors.push(`Carbon credit not verified: ${carbonCredit.name}`);
          results.skipped++;
          continue;
        }

        // Update carbon credit
        await db.carbonCredit.update({
          where: { id: carbonCredit.id },
          data: {
            status: CarbonCreditStatus.LISTED,
            price,
            minPurchaseQuantity,
            listingDate: new Date(),
          },
        });

        // Calculate listing fee
        const listingFee = price * carbonCredit.availableQuantity * carbonCredit.organization.listingFeeRate;

        // Create a billing record for the listing fee
        await db.billingRecord.create({
          data: {
            amount: listingFee,
            description: `Listing fee for ${carbonCredit.name}`,
            type: "LISTING_FEE",
            status: "PENDING",
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Due in 7 days
            organization: { connect: { id: carbonCredit.organizationId } },
          },
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: AuditLogType.CARBON_CREDIT_LISTED,
            description: `Carbon credit ${carbonCredit.name} listed via batch operation`,
            userId: session.user.id,
            organizationId: carbonCredit.organizationId,
            metadata: {
              carbonCreditId: carbonCredit.id,
              price,
              minPurchaseQuantity,
              listingFee,
            },
          },
        });

        // Create notification for the carbon credit owner
        await db.notification.create({
          data: {
            title: "Carbon Credit Listed",
            message: `Your carbon credit ${carbonCredit.name} has been listed on the marketplace.`,
            type: "CREDIT",
            user: {
              connect: {
                id: carbonCredit.userId,
              },
            },
            actionUrl: `/dashboard/carbon-credits/${carbonCredit.id}`,
            actionLabel: "View Carbon Credit",
          },
        });

        results.processed++;
      } catch (error) {
        logger.error(`Error listing carbon credit:`, error);
        results.errors.push(`Error listing: ${carbonCreditId} - ${error instanceof Error ? error.message : "Unknown error"}`);
        results.skipped++;
      }
    }

    logger.info(`Batch listing operation completed: ${results.processed} processed, ${results.skipped} skipped`);

    return NextResponse.json({
      ...results,
      message: `${results.processed} carbon credits listed successfully`,
    });
  } catch (error) {
    logger.error("Error processing batch listing:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while listing carbon credits",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(batchListHandler);
