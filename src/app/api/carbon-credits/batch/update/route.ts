import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType } from "@prisma/client";

// Schema for batch updating
const batchUpdateSchema = z.object({
  carbonCreditIds: z.array(z.string()).min(1, "Select at least one carbon credit"),
  price: z.number().positive("Price must be positive").optional(),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").optional(),
  description: z.string().optional(),
});

/**
 * POST /api/carbon-credits/batch/update
 * Update multiple carbon credits at once
 */
async function batchUpdateHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update carbon credits",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Check if user has permission to update carbon credits
  const isAdmin = session.user.role === "ADMIN";
  const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to update carbon credits",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { carbonCreditIds, price, minPurchaseQuantity, description } = batchUpdateSchema.parse(body);

    // Check if at least one field is provided for update
    if (price === undefined && minPurchaseQuantity === undefined && description === undefined) {
      throw new ApiError(
        "At least one field must be provided for update",
        ErrorType.VALIDATION,
        400
      );
    }

    // Get organization ID for filtering
    const organizationId = session.user.organizationId;

    if (!organizationId && !isAdmin) {
      throw new ApiError(
        "You must be part of an organization to update carbon credits",
        ErrorType.FORBIDDEN,
        403
      );
    }

    // Process results
    const results = {
      processed: 0,
      skipped: 0,
      errors: [] as string[],
    };

    // Process each carbon credit
    for (const carbonCreditId of carbonCreditIds) {
      try {
        // Find the carbon credit
        const carbonCredit = await db.carbonCredit.findFirst({
          where: {
            id: carbonCreditId,
            ...(isAdmin ? {} : { organizationId }),
          },
        });

        if (!carbonCredit) {
          results.errors.push(`Carbon credit not found: ${carbonCreditId}`);
          results.skipped++;
          continue;
        }

        // Prepare update data
        const updateData: any = {};
        const updatedFields: string[] = [];

        if (price !== undefined) {
          updateData.price = price;
          updatedFields.push("price");

          // If the carbon credit is listed, add to price history
          if (carbonCredit.status === "LISTED" && price !== carbonCredit.price) {
            await db.carbonCreditPrice.create({
              data: {
                price,
                reason: "Batch update",
                carbonCredit: { connect: { id: carbonCreditId } },
              },
            });
          }
        }

        if (minPurchaseQuantity !== undefined) {
          updateData.minPurchaseQuantity = minPurchaseQuantity;
          updatedFields.push("minPurchaseQuantity");
        }

        if (description !== undefined) {
          updateData.description = description;
          updatedFields.push("description");
        }

        // Update carbon credit
        await db.carbonCredit.update({
          where: { id: carbonCredit.id },
          data: updateData,
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: AuditLogType.CARBON_CREDIT_UPDATED,
            description: `Carbon credit ${carbonCredit.name} updated via batch operation`,
            userId: session.user.id,
            organizationId: carbonCredit.organizationId,
            metadata: {
              carbonCreditId: carbonCredit.id,
              updatedFields,
              oldValues: {
                price: carbonCredit.price,
                minPurchaseQuantity: carbonCredit.minPurchaseQuantity,
                description: carbonCredit.description,
              },
              newValues: updateData,
            },
          },
        });

        // Create notification for the carbon credit owner
        if (carbonCredit.userId !== session.user.id) {
          await db.notification.create({
            data: {
              title: "Carbon Credit Updated",
              message: `Your carbon credit ${carbonCredit.name} has been updated.`,
              type: "CREDIT",
              user: {
                connect: {
                  id: carbonCredit.userId,
                },
              },
              actionUrl: `/dashboard/carbon-credits/${carbonCredit.id}`,
              actionLabel: "View Carbon Credit",
            },
          });
        }

        results.processed++;
      } catch (error) {
        logger.error(`Error updating carbon credit:`, error);
        results.errors.push(`Error updating: ${carbonCreditId} - ${error instanceof Error ? error.message : "Unknown error"}`);
        results.skipped++;
      }
    }

    logger.info(`Batch update operation completed: ${results.processed} processed, ${results.skipped} skipped`);

    return NextResponse.json({
      ...results,
      message: `${results.processed} carbon credits updated successfully`,
    });
  } catch (error) {
    logger.error("Error processing batch update:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while updating carbon credits",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(batchUpdateHandler);
