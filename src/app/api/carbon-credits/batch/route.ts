import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";
import { VerificationService, VerificationType } from "@/lib/verification";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

// Schema for batch operations
const batchOperationSchema = z.object({
  operation: z.enum(["verify", "list", "retire", "update-price", "update-status", "update-metadata"]),
  carbonCreditIds: z.array(z.string().min(1)).min(1, "At least one carbon credit ID is required"),
  data: z.record(z.any()).optional(),
});

// Verification schema
const batchVerificationSchema = z.object({
  type: z.enum(["INITIAL", "ANNUAL", "METHODOLOGY", "OWNERSHIP", "RETIREMENT", "TOKENIZATION", "BATCH"]),
  documents: z.array(z.string().url("Invalid document URL")),
  notes: z.string().optional(),
});

// Listing schema
const batchListingSchema = z.object({
  listingDate: z.string().datetime().optional(),
});

// Retirement schema
const batchRetirementSchema = z.object({
  reason: z.string().min(1, "Retirement reason is required"),
  beneficiary: z.string().optional(),
  retirementDate: z.string().datetime().optional(),
});

// Price update schema
const batchPriceUpdateSchema = z.object({
  price: z.number().positive("Price must be positive"),
  reason: z.string().optional(),
});

// Status update schema
const batchStatusUpdateSchema = z.object({
  status: z.enum(["PENDING", "VERIFIED", "LISTED", "RETIRED"]),
  reason: z.string().optional(),
});

// Metadata update schema
const batchMetadataUpdateSchema = z.object({
  standard: z.string().optional(),
  methodology: z.string().optional(),
  vintage: z.number().int().min(2000).optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});

/**
 * Perform batch operations on carbon credits
 */
async function batchOperationHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to perform batch operations",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to perform batch operations",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { operation, carbonCreditIds, data } = batchOperationSchema.parse(body);

    // Check if all carbon credits exist and belong to the organization
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        id: { in: carbonCreditIds },
        organizationId: session.user.organizationId,
      },
    });

    if (carbonCredits.length !== carbonCreditIds.length) {
      throw new ApiError(
        "One or more carbon credits not found or do not belong to your organization",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Perform the requested operation
    switch (operation) {
      case "verify": {
        // Validate verification data
        const verificationData = batchVerificationSchema.parse(data);

        // Request batch verification
        const verificationService = new VerificationService();
        const verifications = await verificationService.requestBatchVerification({
          carbonCreditIds,
          type: verificationData.type as VerificationType,
          documents: verificationData.documents,
          notes: verificationData.notes,
          userId: session.user.id,
          organizationId: session.user.organizationId,
        });

        return NextResponse.json({
          message: `Batch verification requested for ${carbonCreditIds.length} carbon credits`,
          verifications,
        });
      }

      case "list": {
        // Validate listing data
        const listingData = batchListingSchema.parse(data);

        // Update carbon credits to listed status
        const listingDate = listingData.listingDate ? new Date(listingData.listingDate) : new Date();

        const updatedCredits = await db.carbonCredit.updateMany({
          where: {
            id: { in: carbonCreditIds },
            status: "VERIFIED", // Only verified credits can be listed
          },
          data: {
            status: "LISTED",
            listingDate,
          },
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_LISTED" as any, // Using a known type with type assertion
            description: `${updatedCredits.count} carbon credits listed`,
            user: {
              connect: { id: session.user.id },
            },
            organization: {
              connect: { id: session.user.organizationId },
            },
            metadata: {
              carbonCreditIds,
              listingDate,
            },
          },
        });

        return NextResponse.json({
          message: `${updatedCredits.count} carbon credits listed successfully`,
          updatedCount: updatedCredits.count,
        });
      }

      case "retire": {
        // Validate retirement data
        const retirementData = batchRetirementSchema.parse(data);

        // Update carbon credits to retired status
        const retirementDate = retirementData.retirementDate ? new Date(retirementData.retirementDate) : new Date();

        const updatedCredits = await db.carbonCredit.updateMany({
          where: {
            id: { in: carbonCreditIds },
            status: { in: ["VERIFIED", "LISTED"] }, // Only verified or listed credits can be retired
          },
          data: {
            status: "RETIRED",
            retirementDate,
            retirementReason: retirementData.reason,
            retirementBeneficiary: retirementData.beneficiary,
          },
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_RETIRED" as any, // Using a known type with type assertion
            description: `${updatedCredits.count} carbon credits retired`,
            user: {
              connect: { id: session.user.id },
            },
            organization: {
              connect: { id: session.user.organizationId },
            },
            metadata: {
              carbonCreditIds,
              retirementDate,
              reason: retirementData.reason,
              beneficiary: retirementData.beneficiary,
            },
          },
        });

        return NextResponse.json({
          message: `${updatedCredits.count} carbon credits retired successfully`,
          updatedCount: updatedCredits.count,
        });
      }

      case "update-price": {
        // Validate price update data
        const priceData = batchPriceUpdateSchema.parse(data);

        // Update carbon credit prices
        const updatedCredits = await db.carbonCredit.updateMany({
          where: {
            id: { in: carbonCreditIds },
          },
          data: {
            price: priceData.price,
          },
        });

        // Record price history for each carbon credit
        for (const creditId of carbonCreditIds) {
          await db.carbonCreditPrice.create({
            data: {
              price: priceData.price,
              reason: priceData.reason,
              carbonCredit: {
                connect: { id: creditId },
              },
            },
          });
        }

        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_LISTED" as any, // Using a known type with type assertion
            description: `Price updated for ${updatedCredits.count} carbon credits`,
            user: {
              connect: { id: session.user.id },
            },
            organization: {
              connect: { id: session.user.organizationId },
            },
            metadata: {
              carbonCreditIds,
              price: priceData.price,
              reason: priceData.reason,
            },
          },
        });

        return NextResponse.json({
          message: `Price updated for ${updatedCredits.count} carbon credits`,
          updatedCount: updatedCredits.count,
        });
      }

      case "update-status": {
        // Validate status update data
        const statusData = batchStatusUpdateSchema.parse(data);

        // Check if status transitions are valid
        for (const credit of carbonCredits) {
          // Define valid transitions
          const validTransitions: Record<string, string[]> = {
            "PENDING": ["VERIFIED"],
            "VERIFIED": ["LISTED", "RETIRED"],
            "LISTED": ["VERIFIED", "RETIRED"],
            "RETIRED": [],
          };

          // Check if the transition is valid
          if (!validTransitions[credit.status]?.includes(statusData.status)) {
            throw new ApiError(
              `Invalid status transition from ${credit.status} to ${statusData.status} for credit ${credit.id}`,
              ErrorType.VALIDATION,
              400
            );
          }
        }

        // Update carbon credit statuses
        const updatedCredits = await db.carbonCredit.updateMany({
          where: {
            id: { in: carbonCreditIds },
          },
          data: {
            status: statusData.status,
          },
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_STATUS_UPDATED" as any,
            description: `Status updated to ${statusData.status} for ${updatedCredits.count} carbon credits`,
            user: {
              connect: { id: session.user.id },
            },
            organization: {
              connect: { id: session.user.organizationId },
            },
            metadata: {
              carbonCreditIds,
              status: statusData.status,
              reason: statusData.reason,
            },
          },
        });

        return NextResponse.json({
          message: `Status updated to ${statusData.status} for ${updatedCredits.count} carbon credits`,
          updatedCount: updatedCredits.count,
        });
      }

      case "update-metadata": {
        // Validate metadata update data
        const metadataData = batchMetadataUpdateSchema.parse(data);

        // Prepare update data (only include fields that are provided)
        const updateData: any = {};
        if (metadataData.standard !== undefined) updateData.standard = metadataData.standard;
        if (metadataData.methodology !== undefined) updateData.methodology = metadataData.methodology;
        if (metadataData.vintage !== undefined) updateData.vintage = metadataData.vintage;
        if (metadataData.location !== undefined) updateData.location = metadataData.location;

        // Update carbon credit metadata
        const updatedCredits = await db.carbonCredit.updateMany({
          where: {
            id: { in: carbonCreditIds },
          },
          data: updateData,
        });

        // Create audit log
        await db.auditLog.create({
          data: {
            type: "CARBON_CREDIT_METADATA_UPDATED" as any,
            description: `Metadata updated for ${updatedCredits.count} carbon credits`,
            user: {
              connect: { id: session.user.id },
            },
            organization: {
              connect: { id: session.user.organizationId },
            },
            metadata: {
              carbonCreditIds,
              updatedFields: Object.keys(updateData),
              ...updateData,
              notes: metadataData.notes,
            },
          },
        });

        return NextResponse.json({
          message: `Metadata updated for ${updatedCredits.count} carbon credits`,
          updatedCount: updatedCredits.count,
          updatedFields: Object.keys(updateData),
        });
      }

      default:
        throw new ApiError(
          "Invalid operation",
          ErrorType.VALIDATION,
          400
        );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    logger.error("Error performing batch operation:", error);
    throw new ApiError(
      "Failed to perform batch operation",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(batchOperationHandler);
