import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { AuditLogType, NotificationType } from "@prisma/client";
import { notificationService, NotificationChannel } from "@/lib/notifications";

/**
 * POST /api/organizations/verification/request
 * Request organization verification
 */
export async function POST() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to request verification" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to request verification" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        documents: true,
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Check if organization is already verified
    if (organization.verificationStatus === "VERIFIED") {
      return NextResponse.json(
        { error: "Organization is already verified" },
        { status: 400 }
      );
    }

    // Check if organization is already in review
    if (organization.verificationStatus === "IN_REVIEW") {
      return NextResponse.json(
        { error: "Verification is already in review" },
        { status: 400 }
      );
    }

    // Check if organization has uploaded required documents
    if (organization.documents.length === 0) {
      return NextResponse.json(
        { error: "Please upload required documents before requesting verification" },
        { status: 400 }
      );
    }

    // Update organization verification status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        verificationStatus: "IN_REVIEW",
      },
    });

    // Create audit log
    await auditService.log(
      "VERIFICATION_REQUESTED",
      `Verification requested for organization ${organization.name}`,
      {
        organizationId,
        documentCount: organization.documents.length,
      },
      session.user.id,
      organizationId
    );

    // Notify admins about the verification request
    const admins = await db.user.findMany({
      where: {
        role: "ADMIN",
      },
    });

    for (const admin of admins) {
      await notificationService.createNotification(
        admin.id,
        "Verification Request",
        `Organization ${organization.name} has requested verification`,
        NotificationType.VERIFICATION_REQUESTED,
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          organizationId,
          organizationName: organization.name,
        },
        `/admin/organizations/${organizationId}/verification`,
        "Review Request"
      );
    }

    // Notify organization members about the verification request
    const organizationMembers = await db.user.findMany({
      where: {
        organizationId,
        role: "ORGANIZATION_ADMIN",
        id: { not: session.user.id },
      },
    });

    for (const member of organizationMembers) {
      await notificationService.createNotification(
        member.id,
        "Verification Requested",
        `Your organization's verification has been submitted for review`,
        "VERIFICATION",
        ["IN_APP"],
        {
          organizationId,
        },
        "/dashboard/organization/verification",
        "View Status"
      );
    }

    return NextResponse.json({
      message: "Verification requested successfully",
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error requesting verification:", error);

    return NextResponse.json(
      { error: "An error occurred while requesting verification" },
      { status: 500 }
    );
  }
}
