import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { AuditLogType } from "@prisma/client";
import { deleteFile } from "@/lib/storage";

/**
 * DELETE /api/organizations/verification/documents/[id]
 * Delete a verification document
 */
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete documents" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to delete documents" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;
    const documentId = params.id;

    // Check if document exists and belongs to the organization
    const document = await db.document.findFirst({
      where: {
        id: documentId,
        organizationId,
      },
    });

    if (!document) {
      return NextResponse.json(
        { error: "Document not found" },
        { status: 404 }
      );
    }

    // Check if organization is in a state where documents can be deleted
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    if (organization.verificationStatus === "IN_REVIEW" && document.status !== "PENDING") {
      return NextResponse.json(
        { error: "Cannot delete documents while verification is in review" },
        { status: 400 }
      );
    }

    // Delete file from storage if URL exists
    if (document.url) {
      try {
        // Extract file path from URL
        // For local storage, the URL might be relative (/uploads/...) or absolute (http://...)
        let filePath = document.url;

        // If it's an absolute URL, extract the path
        if (filePath.startsWith('http')) {
          const url = new URL(filePath);
          filePath = url.pathname;
        }

        // Remove /uploads/ prefix if present
        filePath = filePath.replace(/^\/uploads\//, '');

        // Delete the file
        const deleteResult = await deleteFile(filePath);

        if (!deleteResult.success) {
          logger.warn(`Failed to delete file ${filePath}: ${deleteResult.error}`);
        }
      } catch (error) {
        logger.error("Error deleting file from storage:", error);
        // Continue with document deletion even if file deletion fails
      }
    }

    // Delete document
    await db.document.delete({
      where: { id: documentId },
    });

    // Create audit log
    await auditService.log(
      AuditLogType.VERIFICATION_REJECTED,
      `Document deleted: ${document.name} (${document.type})`,
      {
        documentId,
        documentType: document.type,
        documentName: document.name,
      },
      session.user.id,
      organizationId
    );

    return NextResponse.json({
      message: "Document deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting document:", error);

    return NextResponse.json(
      { error: "An error occurred while deleting document" },
      { status: 500 }
    );
  }
}
