import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { uploadFile, generateUniqueFileName } from "@/lib/storage";
import { DocumentType, VerificationStatus, AuditLogType } from "@prisma/client";

/**
 * POST /api/organizations/verification/documents
 * Upload a verification document
 */
export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to upload documents" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to upload documents" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Check if organization is in a state where documents can be uploaded
    if (organization.verificationStatus === VerificationStatus.VERIFIED) {
      return NextResponse.json(
        { error: "Cannot upload documents while verification is in review" },
        { status: 400 }
      );
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as DocumentType;
    const name = formData.get("name") as string;

    if (!file || !type || !name) {
      return NextResponse.json(
        { error: "Missing required fields: file, type, or name" },
        { status: 400 }
      );
    }

    // Validate document type
    const validTypes = Object.values(DocumentType);
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: "Invalid document type", validTypes },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds the maximum limit of 5MB" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["application/pdf", "image/jpeg", "image/png", "image/jpg"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Allowed types: PDF, JPEG, PNG" },
        { status: 400 }
      );
    }

    // Check if document of this type already exists
    const existingDocument = await db.document.findFirst({
      where: {
        organizationId,
        type,
      },
    });

    if (existingDocument) {
      return NextResponse.json(
        { error: `A document of type ${type} already exists` },
        { status: 400 }
      );
    }

    // Upload file to storage
    const fileBuffer = await file.arrayBuffer();
    const fileName = generateUniqueFileName(file.name, `${organizationId}/${type.toLowerCase()}`);

    const uploadResult = await uploadFile(
      Buffer.from(fileBuffer),
      fileName,
      file.type,
      {
        originalName: file.name,
        organizationId,
        documentType: type,
        uploadedBy: session.user.id,
      }
    );

    if (!uploadResult.success) {
      throw new Error("Failed to upload file to storage");
    }

    // Create document record
    const document = await db.document.create({
      data: {
        name,
        type,
        url: uploadResult.url,
        status: "PENDING",
        organization: {
          connect: { id: organizationId },
        },
      },
    });

    // Update organization verification status if it's not already verified
    await db.organization.update({
      where: { id: organizationId },
      data: {
        verificationStatus: VerificationStatus.VERIFIED,
      },
    });

    // Create audit log
    await auditService.log(
      AuditLogType.DOCUMENT_UPLOADED,
      `Document uploaded: ${name} (${type})`,
      {
        documentId: document.id,
        documentType: type,
        documentName: name,
      },
      session.user.id,
      organizationId
    );

    return NextResponse.json({
      message: "Document uploaded successfully",
      document,
    });
  } catch (error) {
    logger.error("Error uploading document:", error);

    return NextResponse.json(
      { error: "An error occurred while uploading document" },
      { status: 500 }
    );
  }
}
