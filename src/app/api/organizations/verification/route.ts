import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

/**
 * GET /api/organizations/verification
 * Get organization verification details
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access verification details" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to access verification details" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;

    // Get organization with verification details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Get verification documents
    const documents = await db.document.findMany({
      where: { organizationId },
      orderBy: { createdAt: "desc" },
    });

    // Get verification notes
    const verificationNotes = await db.verificationNote.findMany({
      where: { organizationId },
      orderBy: { createdAt: "desc" },
      include: {
        admin: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      organization,
      documents,
      verificationNotes,
    });
  } catch (error) {
    logger.error("Error fetching organization verification details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching verification details" },
      { status: 500 }
    );
  }
}
