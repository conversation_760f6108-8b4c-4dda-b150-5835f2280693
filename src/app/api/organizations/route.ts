import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { OrganizationStatus, VerificationStatus, SubscriptionPlan, AuditLogType } from "@prisma/client";
import { onboardingEmailService } from "@/lib/email/onboarding-emails";

const organizationSchema = z.object({
  // Essential fields (required)
  name: z.string().min(2, "Organization name must be at least 2 characters"),
  industry: z.string().min(2, "Industry is required"),
  size: z.enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"]),
  country: z.string().min(2, "Country is required"),

  // Optional fields that can be filled later
  description: z.string().optional(),
  website: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  logo: z.string().optional(),
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  phoneNumber: z.string().optional(),
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  primaryContact: z.string().optional(),
  primaryContactEmail: z.string().email("Please enter a valid email").optional(),
  primaryContactPhone: z.string().optional(),
});

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create an organization" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const {
      name,
      description,
      website,
      logo,
      legalName,
      registrationNumber,
      taxId,
      country,
      address,
      city,
      state,
      postalCode,
      phoneNumber,
      industry,
      size,
      foundedYear,
      primaryContact,
      primaryContactEmail,
      primaryContactPhone
    } = organizationSchema.parse(body);

    // Check if user already has an organization
    const existingUser = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        organization: true,
      },
    });

    if (existingUser?.organization) {
      return NextResponse.json(
        { error: "You already have an organization" },
        { status: 409 }
      );
    }

    // Create the organization with all provided fields
    const organization = await db.organization.create({
      data: {
        name,
        description,
        website,
        logo,
        status: OrganizationStatus.PENDING,
        verificationStatus: VerificationStatus.PENDING,
        legalName,
        registrationNumber,
        taxId,
        country,
        address,
        city,
        state,
        postalCode,
        phoneNumber,
        industry,
        size,
        foundedYear,
        primaryContact,
        primaryContactEmail,
        primaryContactPhone,
        users: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });

    logger.info(`Organization ${organization.id} created by user ${session.user.id}`);

    // Update user role to ORGANIZATION_ADMIN
    await db.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        role: UserRole.ORGANIZATION_ADMIN,
      },
    });

    // Create a free subscription for the organization
    await db.subscription.create({
      data: {
        plan: SubscriptionPlan.FREE,
        organization: {
          connect: {
            id: organization.id,
          },
        },
      },
    });

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: AuditLogType.ORGANIZATION_CREATED,
        description: `Organization ${name} created`,
        userId: session.user.id,
        organizationId: organization.id,
        metadata: {
          organizationId: organization.id,
          organizationName: name,
        },
      },
    });

    // Create a notification for the user to complete their profile
    await db.notification.create({
      data: {
        title: "Organization Created",
        message: `Your organization ${name} has been created successfully. Complete your organization profile to get the most out of our platform.`,
        type: "SYSTEM",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: organization.id,
          },
        },
        actionUrl: `/settings/organization?organizationId=${organization.id}`,
        actionLabel: "Complete Profile",
      },
    });

    // Notify platform admins about the new organization
    const admins = await db.user.findMany({
      where: { role: UserRole.ADMIN },
    });

    for (const admin of admins) {
      await db.notification.create({
        data: {
          title: "New Organization",
          message: `A new organization ${name} has been created and needs verification.`,
          type: "SYSTEM",
          priority: "HIGH",
          user: {
            connect: {
              id: admin.id,
            },
          },
          organization: {
            connect: {
              id: organization.id,
            },
          },
          actionUrl: `/admin/organizations/${organization.id}`,
          actionLabel: "Review Organization",
        },
      });
    }

    // Send welcome email
    try {
      await onboardingEmailService.sendWelcomeEmail(
        session.user.email || '',
        session.user.name || 'User',
        organization.name
      );
    } catch (emailError) {
      logger.error("Error sending welcome email:", emailError);
      // Continue even if email fails
    }

    return NextResponse.json(
      { organization, message: "Organization created successfully" },
      { status: 201 }
    );
  } catch (error) {
    logger.error("Error creating organization:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the organization" },
      { status: 500 }
    );
  }
}
