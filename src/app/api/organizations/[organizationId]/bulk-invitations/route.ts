import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType, UserRole } from "@prisma/client";
import { sendInvitationEmail } from "@/lib/email";
import { generateInvitationToken } from "@/lib/tokens";
import { simpleEmailSchema } from "@/lib/validation/schemas";

// Schema for bulk invitations
const bulkInviteSchema = z.object({
  emails: z.array(simpleEmailSchema).min(1, "At least one email is required"),
  role: z.enum(["USER", "ADMIN", "FINANCE", "COMPLIANCE"]),
  message: z.string().optional(),
});

/**
 * POST /api/organizations/[organizationId]/bulk-invitations
 * Send invitations to multiple team members
 */
async function bulkInviteHandler(
  req: Request,
  { params }: { params: { organizationId: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to invite team members",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const organizationId = params.organizationId;

  // Check if user has permission to invite team members
  const userMembership = await db.organizationMember.findFirst({
    where: {
      userId: session.user.id,
      organizationId,
    },
  });

  if (!userMembership) {
    throw new ApiError(
      "You are not a member of this organization",
      ErrorType.FORBIDDEN,
      403
    );
  }

  const isAdmin = userMembership.role === "ADMIN";
  const isOrgAdmin = userMembership.role === "ORGANIZATION_ADMIN";

  if (!isAdmin && !isOrgAdmin) {
    throw new ApiError(
      "You do not have permission to invite team members",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const body = await req.json();
    const { emails, role, message } = bulkInviteSchema.parse(body);

    // Get organization details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new ApiError(
        "Organization not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Process each email
    const results = await Promise.allSettled(
      emails.map(async (email) => {
        try {
          // Check if user already exists and is a member
          const existingUser = await db.user.findFirst({
            where: { email },
            include: {
              organizationMembers: {
                where: { organizationId },
              },
            },
          });

          if (existingUser && existingUser.organizationMembers.length > 0) {
            return {
              email,
              status: "failed",
              reason: "User is already a member of this organization",
            };
          }

          // Check if there's a pending invitation
          const existingInvitation = await db.invitation.findFirst({
            where: {
              email,
              organizationId,
              status: "PENDING",
            },
          });

          if (existingInvitation) {
            return {
              email,
              status: "failed",
              reason: "An invitation has already been sent to this email",
            };
          }

          // Generate invitation token
          const token = await generateInvitationToken();

          // Map role string to UserRole enum
          let userRole: UserRole;
          switch (role) {
            case "ADMIN":
              userRole = UserRole.ADMIN;
              break;
            case "FINANCE":
              userRole = UserRole.FINANCE;
              break;
            case "COMPLIANCE":
              userRole = UserRole.COMPLIANCE;
              break;
            default:
              userRole = UserRole.USER;
          }

          // Create invitation
          const invitation = await db.invitation.create({
            data: {
              email,
              role: userRole,
              token,
              organization: {
                connect: { id: organizationId },
              },
              expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
            },
          });

          // Send invitation email
          await sendInvitationEmail({
            email,
            name: email.split("@")[0], // Use part of email as name
            inviterName: session.user.name || "A team member",
            organizationName: organization.name,
            invitationLink: `${process.env.NEXT_PUBLIC_APP_URL}/invitation?token=${token}`,
            role: role,
            message: message,
          });

          // Create audit log
          await db.auditLog.create({
            data: {
              type: AuditLogType.INVITATION_SENT,
              description: `Invitation sent to ${email} for role ${role}`,
              userId: session.user.id,
              organizationId,
              metadata: {
                email,
                role,
                invitationId: invitation.id,
              },
            },
          });

          return {
            email,
            status: "success",
            invitation: {
              id: invitation.id,
              email: invitation.email,
              role: invitation.role,
              createdAt: invitation.createdAt,
            },
          };
        } catch (error) {
          logger.error(`Error inviting ${email}:`, error);
          return {
            email,
            status: "failed",
            reason: error instanceof Error ? error.message : "Unknown error",
          };
        }
      })
    );

    // Count successful and failed invitations
    const successful = results.filter(
      (result) => result.status === "fulfilled" && result.value.status === "success"
    ).length;

    const failed = results.filter(
      (result) => result.status === "rejected" || (result.status === "fulfilled" && result.value.status === "failed")
    ).length;

    // Get successful invitations
    const invitations = results
      .filter(
        (result): result is PromiseFulfilledResult<{ status: string; invitation?: any }> =>
          result.status === "fulfilled" && result.value.status === "success"
      )
      .map((result) => result.value.invitation);

    logger.info(`Bulk invitation: ${successful} successful, ${failed} failed for organization ${organizationId}`);

    return NextResponse.json({
      message: `${successful} invitations sent successfully${failed > 0 ? `, ${failed} failed` : ""}`,
      successful,
      failed,
      invitations,
    });
  } catch (error) {
    logger.error("Error processing bulk invitations:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while processing invitations",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(bulkInviteHandler);
