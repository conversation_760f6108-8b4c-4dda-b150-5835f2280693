import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { emailService } from "@/lib/email";
import { generateInvitationToken, generateLegacyInvitationToken } from "@/lib/tokens";
import { logger } from "@/lib/logger";

const inviteeSchema = z.object({
  email: z.string().email(),
  role: z.enum(["ORGANIZATION_ADMIN", "USER"]),
});

const inviteSchema = z.object({
  invitees: z.array(inviteeSchema),
});

export async function POST(
  req: Request,
  { params }: { params: { organizationId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to invite team members" },
        { status: 401 }
      );
    }

    // Check if user has permission to invite members to this organization
    const organization = await db.organization.findUnique({
      where: {
        id: params.organizationId,
      },
      include: {
        users: {
          where: {
            id: session.user.id,
            role: "ORGANIZATION_ADMIN",
          },
        },
      },
    });

    if (!organization || organization.users.length === 0) {
      return NextResponse.json(
        { error: "You don't have permission to invite members to this organization" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { invitees } = inviteSchema.parse(body);

    // Process each invitation
    const results = await Promise.all(
      invitees.map(async (invitee) => {
        try {
          // Check if user already exists
          const existingUser = await db.user.findUnique({
            where: {
              email: invitee.email,
            },
          });

          if (existingUser) {
            // If user exists, check if they're already in the organization
            if (existingUser.organizationId === params.organizationId) {
              return {
                email: invitee.email,
                status: "already_member",
                message: "User is already a member of this organization",
              };
            }

            // If user exists but is in another organization, send invitation
            // In a real app, you might want to handle this differently
            const token = await generateLegacyInvitationToken(existingUser.id, params.organizationId, invitee.role);
            await emailService.sendInvitationEmail(
              invitee.email,
              organization.name,
              session.user.name || "An organization admin",
              token
            );

            return {
              email: invitee.email,
              status: "invited_existing",
              message: "Invitation sent to existing user",
            };
          }

          // Create a new user with pending status
          const newUser = await db.user.create({
            data: {
              email: invitee.email,
              role: invitee.role,
              // Don't set organizationId yet - will be set when they accept
            },
          });

          // Generate invitation token
          const token = await generateLegacyInvitationToken(newUser.id, params.organizationId, invitee.role);

          // Send invitation email
          await emailService.sendInvitationEmail(
            invitee.email,
            organization.name,
            session.user.name || "An organization admin",
            token
          );

          // Create a notification for the inviter
          await db.notification.create({
            data: {
              title: "Invitation Sent",
              message: `You've invited ${invitee.email} to join your organization.`,
              type: "SYSTEM",
              user: {
                connect: {
                  id: session.user.id,
                },
              },
            },
          });

          return {
            email: invitee.email,
            status: "invited_new",
            message: "Invitation sent to new user",
          };
        } catch (error) {
          logger.error(`Error inviting ${invitee.email}:`, error);
          return {
            email: invitee.email,
            status: "error",
            message: error instanceof Error ? error.message : "Unknown error",
          };
        }
      })
    );

    return NextResponse.json({
      results,
      message: "Invitations processed",
    });
  } catch (error) {
    logger.error("Error processing invitations:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while processing invitations" },
      { status: 500 }
    );
  }
}
