import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { UserRole as AuthUserRole } from "@/lib/authorization";
import { AuditLogType, UserRole } from "@prisma/client";

const invitationSchema = z.object({
  email: z.string().email("Please enter a valid email"),
  role: z.enum(["USER", "ADMIN", "FINANCE", "COMPLIANCE"]),
});

export async function POST(
  req: Request,
  { params }: { params: { organizationId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to send invitations" },
        { status: 401 }
      );
    }

    // Check if user has permission to send invitations
    const userOrganization = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        role: true,
        organizationId: true,
      },
    });

    if (
      !userOrganization ||
      userOrganization.organizationId !== params.organizationId ||
      (userOrganization.role !== AuthUserRole.ORGANIZATION_ADMIN &&
        userOrganization.role !== AuthUserRole.ADMIN)
    ) {
      return NextResponse.json(
        { error: "You don't have permission to send invitations for this organization" },
        { status: 403 }
      );
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: {
        id: params.organizationId,
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { email, role } = invitationSchema.parse(body);

    // Check if user with email already exists in the organization
    const existingUser = await db.user.findFirst({
      where: {
        email,
        organizationId: params.organizationId,
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User is already part of this organization" },
        { status: 409 }
      );
    }

    // Check if an invitation has already been sent
    const existingInvitation = await db.invitation.findFirst({
      where: {
        email,
        organizationId: params.organizationId,
        status: "PENDING",
      },
    });

    if (existingInvitation) {
      return NextResponse.json(
        { error: "An invitation has already been sent to this email" },
        { status: 409 }
      );
    }

    // Generate a unique token for the invitation
    const token = crypto.randomUUID();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

    // Map the role string to the appropriate UserRole enum value
    let userRole: UserRole;
    switch (role) {
      case "ADMIN":
        userRole = UserRole.ORGANIZATION_ADMIN;
        break;
      case "FINANCE":
      case "COMPLIANCE":
      case "USER":
      default:
        userRole = UserRole.USER;
        break;
    }

    // Create the invitation
    const invitation = await db.invitation.create({
      data: {
        email,
        role: userRole,
        token,
        expires: expiresAt, // Using expires instead of expiresAt based on previous fixes
        organization: {
          connect: {
            id: params.organizationId,
          },
        },
        // Remove invitedBy field as it doesn't exist in the model
      },
    });

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: AuditLogType.INVITATION_SENT,
        description: `Invitation sent to ${email} for role ${role}`,
        userId: session.user.id,
        organizationId: params.organizationId,
        metadata: {
          email,
          role,
          invitationId: invitation.id,
        },
      },
    });

    // Send invitation email - simplified here, would typically use email service
    try {
      // This would be replaced with your actual email sending code
      logger.info(`Sending invitation email to ${email} for organization ${organization.name}`);

      // Example: await emailService.sendInvitation(...);
    } catch (emailError) {
      logger.error("Error sending invitation email:", emailError);
    }

    return NextResponse.json(
      { invitation, message: "Invitation sent successfully" },
      { status: 201 }
    );
  } catch (error) {
    logger.error("Error sending invitation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while sending the invitation" },
      { status: 500 }
    );
  }
}
