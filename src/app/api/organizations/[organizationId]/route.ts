import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withOrganizationIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

/**
 * GET /api/organizations/[organizationId]
 * Get organization details
 */
async function getHandler(
  req: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access organization details" },
        { status: 401 }
      );
    }

    // Get the organization ID from params
    const { organizationId } = params;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Note: The tenant isolation middleware already checks if the user can access this organization
    // This is just a fallback check
    if (!tenantContext.isAdmin && tenantContext.organizationId !== organizationId) {
      logger.warn(`User ${session.user.id} attempted to access organization ${organizationId} they don't belong to`);
      return NextResponse.json(
        { error: "You do not have permission to view this organization" },
        { status: 403 }
      );
    }

    // Get organization details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        subscription: true,
        documents: {
          orderBy: { createdAt: "desc" },
        },
        wallets: {
          select: {
            id: true,
            address: true,
            network: true,
            chainId: true,
            isTestnet: true,
            balance: true,
            lastSyncedAt: true,
          },
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      organization,
    });
  } catch (error) {
    logger.error("Error fetching organization details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching organization details" },
      { status: 500 }
    );
  }
}

// Export the handler with organization isolation middleware
export const GET = withOrganizationIsolation('organizationId')(getHandler);
