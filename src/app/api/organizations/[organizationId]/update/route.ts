import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { AuditLogType } from "@prisma/client";

const updateOrganizationSchema = z.object({
  name: z.string().min(2, "Organization name must be at least 2 characters").optional(),
  description: z.string().optional(),
  website: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  country: z.string().min(2, "Country is required").optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  phoneNumber: z.string().optional(),
  industry: z.string().min(2, "Industry is required").optional(),
  size: z.enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"]).optional(),
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  primaryContact: z.string().optional(),
  primaryContactEmail: z.string().email("Please enter a valid email").optional(),
  primaryContactPhone: z.string().optional(),
});

export async function PATCH(
  req: Request,
  context: { params: Promise<{ organizationId: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update an organization" },
        { status: 401 }
      );
    }

    const organizationId = (await context.params).organizationId;

    // Check if the user belongs to the organization or is an admin
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        role: true,
        organizationId: true,
      },
    });

    const isAdmin = user?.role === "ADMIN";
    const isOrgAdmin = user?.role === "ORGANIZATION_ADMIN";
    const belongsToOrg = user?.organizationId === organizationId;

    if (!isAdmin && !(isOrgAdmin && belongsToOrg)) {
      return NextResponse.json(
        { error: "You do not have permission to update this organization" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const validatedData = updateOrganizationSchema.parse(body);

    // Update organization
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: validatedData,
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.ORGANIZATION_UPDATED,
        description: `Organization ${updatedOrganization.name} updated`,
        userId: session.user.id,
        organizationId,
        metadata: {
          organizationId,
          organizationName: updatedOrganization.name,
          updatedFields: Object.keys(validatedData),
        },
      },
    });

    return NextResponse.json({
      organization: updatedOrganization,
      message: "Organization updated successfully",
    });
  } catch (error) {
    logger.error("Error updating organization:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating the organization" },
      { status: 500 }
    );
  }
}
