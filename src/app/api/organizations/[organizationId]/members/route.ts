import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

export async function GET(
  req: Request,
  { params }: { params: { organizationId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view team members" },
        { status: 401 }
      );
    }

    // Check if user has access to this organization
    const userOrganization = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        organizationId: true,
      },
    });

    if (userOrganization?.organizationId !== params.organizationId) {
      return NextResponse.json(
        { error: "You don't have access to this organization" },
        { status: 403 }
      );
    }

    // Get all members of the organization
    const members = await db.user.findMany({
      where: {
        organizationId: params.organizationId,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json({ members });
  } catch (error) {
    logger.error("Error fetching team members:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching team members" },
      { status: 500 }
    );
  }
}
