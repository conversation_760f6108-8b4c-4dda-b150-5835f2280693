import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";

const updateRoleSchema = z.object({
  role: z.enum(["ORGANIZATION_ADMIN", "USER"]),
});

export async function DELETE(
  req: Request,
  { params }: { params: { organizationId: string; userId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to remove team members" },
        { status: 401 }
      );
    }

    // Check if user is an admin of this organization
    const userOrganization = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        organizationId: true,
        role: true,
      },
    });

    if (
      userOrganization?.organizationId !== params.organizationId ||
      (userOrganization?.role !== "ORGANIZATION_ADMIN" && userOrganization?.role !== "ADMIN")
    ) {
      return NextResponse.json(
        { error: "You don't have permission to remove team members" },
        { status: 403 }
      );
    }

    // Check if the user to remove exists and is part of the organization
    const userToRemove = await db.user.findUnique({
      where: {
        id: params.userId,
        organizationId: params.organizationId,
      },
    });

    if (!userToRemove) {
      return NextResponse.json(
        { error: "User not found in this organization" },
        { status: 404 }
      );
    }

    // Don't allow removing yourself
    if (userToRemove.id === session.user.id) {
      return NextResponse.json(
        { error: "You cannot remove yourself from the organization" },
        { status: 400 }
      );
    }

    // Remove the user from the organization
    await db.user.update({
      where: {
        id: params.userId,
      },
      data: {
        organizationId: null,
        role: "USER", // Reset role to USER
      },
    });

    // Create a notification for the removed user
    await db.notification.create({
      data: {
        title: "Organization Membership",
        message: "You have been removed from the organization.",
        type: "SYSTEM",
        user: {
          connect: {
            id: params.userId,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} removed user ${params.userId} from organization ${params.organizationId}`);

    return NextResponse.json({
      message: "Team member removed successfully",
    });
  } catch (error) {
    logger.error("Error removing team member:", error);
    return NextResponse.json(
      { error: "An error occurred while removing the team member" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { organizationId: string; userId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update team members" },
        { status: 401 }
      );
    }

    // Check if user is an admin of this organization
    const userOrganization = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        organizationId: true,
        role: true,
      },
    });

    if (
      userOrganization?.organizationId !== params.organizationId ||
      (userOrganization?.role !== "ORGANIZATION_ADMIN" && userOrganization?.role !== "ADMIN")
    ) {
      return NextResponse.json(
        { error: "You don't have permission to update team members" },
        { status: 403 }
      );
    }

    // Check if the user to update exists and is part of the organization
    const userToUpdate = await db.user.findUnique({
      where: {
        id: params.userId,
        organizationId: params.organizationId,
      },
    });

    if (!userToUpdate) {
      return NextResponse.json(
        { error: "User not found in this organization" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { role } = updateRoleSchema.parse(body);

    // Update the user's role
    await db.user.update({
      where: {
        id: params.userId,
      },
      data: {
        role,
      },
    });

    // Create a notification for the updated user
    await db.notification.create({
      data: {
        title: "Role Updated",
        message: `Your role has been updated to ${role === "ORGANIZATION_ADMIN" ? "Admin" : "Member"}.`,
        type: "SYSTEM",
        user: {
          connect: {
            id: params.userId,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} updated role of user ${params.userId} to ${role} in organization ${params.organizationId}`);

    return NextResponse.json({
      message: "Team member role updated successfully",
    });
  } catch (error) {
    logger.error("Error updating team member role:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating the team member role" },
      { status: 500 }
    );
  }
}
