import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

// Schema for organization draft
const organizationDraftSchema = z.object({
  // Basic Information
  name: z.string().min(2, "Organization name must be at least 2 characters").optional(),
  industry: z.string().optional(),
  size: z.enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"]).optional(),
  description: z.string().optional(),

  // Contact Information
  website: z.string().url("Please enter a valid URL").or(z.string().length(0)).optional(),
  phoneNumber: z.string().optional(),
  email: z.string().email("Please enter a valid email address").optional(),

  // Legal Information
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),

  // Address Information
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),

  // Metadata
  currentStep: z.number().int().min(1).max(4).optional(),
  draftId: z.string().optional().nullable(), // For updating existing drafts, can be null
});

/**
 * POST /api/organizations/draft
 * Save organization draft
 */
export async function POST(req: Request) {
  try {
    // Get the authenticated session
    const session = await getAuthenticatedSession();
    if (!session) {
      return NextResponse.json(
        { error: "You must be logged in to save a draft" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = organizationDraftSchema.parse(body);

    try {
      // Check if draftId is provided in the request
      if (validatedData.draftId) {
        logger.info("Draft ID provided in request:", { draftId: validatedData.draftId });
      } else {
        logger.info("No draft ID provided in request, will look up by user ID");
      }

      // Check if user already has a draft
      const existingDraft = await db.organizationDraft.findFirst({
        where: {
          userId: session.user.id,
        },
      });

      let draft;

      // Remove draftId from validatedData as it's not a field in the database model
      const { draftId, ...dataToSave } = validatedData;

      if (existingDraft) {
        // Update existing draft
        draft = await db.organizationDraft.update({
          where: {
            id: existingDraft.id,
          },
          data: {
            ...dataToSave,
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new draft
        draft = await db.organizationDraft.create({
          data: {
            ...dataToSave,
            userId: session.user.id,
          },
        });
      }

      logger.info(`Organization draft saved for user ${session.user.id}`);

      return NextResponse.json({
        message: "Draft saved successfully",
        draft,
      });
    } catch (prismaError: any) {
      // Check if the error is because the table doesn't exist
      if (prismaError.code === 'P2021') {
        logger.warn("OrganizationDraft table does not exist yet:", {
          code: prismaError.code,
          message: prismaError.message
        });

        return NextResponse.json(
          {
            message: "Organization draft feature not yet available",
            details: "The database schema needs to be updated to support organization drafts."
          },
          { status: 503 } // Service Unavailable
        );
      }

      // For other Prisma errors, rethrow
      throw prismaError;
    }
  } catch (error) {
    logger.error("Error saving organization draft:", {
      error,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      errorStack: error instanceof Error ? error.stack : undefined
    });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: "An error occurred while saving the draft",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/organizations/draft
 * Get organization draft for current user
 */
export async function GET() {
  try {
    // Get the authenticated session
    const session = await getAuthenticatedSession();
    if (!session) {
      return NextResponse.json(
        { error: "You must be logged in to retrieve a draft" },
        { status: 401 }
      );
    }

    // Get draft for current user
    return await getDraftForUser(session.user.id);
  } catch (error) {
    // Improved error logging with more details
    logger.error("Error retrieving organization draft:", {
      error,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      errorStack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      {
        error: "An error occurred while retrieving the draft",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get authenticated session
 */
async function getAuthenticatedSession() {
  try {
    const session = await auth();
    logger.info("Auth session retrieved:", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id ?? 'none'
    });

    if (!session?.user) {
      logger.warn("No user in session when retrieving organization draft");
      return null;
    }

    if (!session.user.id) {
      logger.warn("User ID missing in session:", { user: session.user });
      throw new Error("User ID is missing");
    }

    return session;
  } catch (authError) {
    logger.error("Error retrieving auth session:", authError);
    throw authError;
  }
}

/**
 * Helper function to get draft for a user
 */
async function getDraftForUser(userId: string) {
  logger.info("Attempting to find organization draft for user:", { userId });

  try {
    // Log db object to verify it's defined
    logger.info("Database connection:", {
      isDefined: !!db,
      hasOrganizationDraft: !!db?.organizationDraft
    });

    try {
      const draft = await db.organizationDraft.findFirst({
        where: { userId },
      });

      if (!draft) {
        logger.info("No organization draft found for user:", { userId });
        return NextResponse.json(
          { message: "No draft found" },
          { status: 404 }
        );
      }

      logger.info("Organization draft found for user:", {
        userId,
        draftId: draft.id
      });

      return NextResponse.json({ draft });
    } catch (prismaError: any) {
      // Check if the error is because the table doesn't exist
      if (prismaError.code === 'P2021') {
        logger.warn("OrganizationDraft table does not exist yet:", {
          code: prismaError.code,
          message: prismaError.message
        });

        return NextResponse.json(
          { message: "No draft found - organization draft feature not yet available" },
          { status: 404 }
        );
      }

      // For other Prisma errors, rethrow
      throw prismaError;
    }
  } catch (dbError) {
    logger.error("Database error when retrieving organization draft:", {
      error: dbError,
      errorMessage: dbError instanceof Error ? dbError.message : "Unknown error",
      userId
    });

    throw dbError;
  }
}

/**
 * DELETE /api/organizations/draft
 * Delete organization draft for current user
 */
export async function DELETE() {
  try {
    // Get the authenticated session
    const session = await getAuthenticatedSession();
    if (!session) {
      return NextResponse.json(
        { error: "You must be logged in to delete a draft" },
        { status: 401 }
      );
    }

    try {
      // Delete draft for current user
      await db.organizationDraft.deleteMany({
        where: {
          userId: session.user.id,
        },
      });

      logger.info(`Organization draft deleted for user ${session.user.id}`);

      return NextResponse.json({
        message: "Draft deleted successfully",
      });
    } catch (prismaError: any) {
      // Check if the error is because the table doesn't exist
      if (prismaError.code === 'P2021') {
        logger.warn("OrganizationDraft table does not exist yet:", {
          code: prismaError.code,
          message: prismaError.message
        });

        return NextResponse.json(
          { message: "No draft to delete - organization draft feature not yet available" },
          { status: 200 } // Return 200 OK since there's nothing to delete anyway
        );
      }

      // For other Prisma errors, rethrow
      throw prismaError;
    }
  } catch (error) {
    logger.error("Error deleting organization draft:", {
      error,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      errorStack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      {
        error: "An error occurred while deleting the draft",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
