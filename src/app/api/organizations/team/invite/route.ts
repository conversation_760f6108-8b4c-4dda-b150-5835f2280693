import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { NotificationType } from "@prisma/client";
import { NotificationChannel } from "@/lib/notifications";
import { notificationService } from "@/lib/notifications";
import { auditService } from "@/lib/audit";
import { generateInvitationToken } from "@/lib/tokens";

// Schema for invitation
const invitationSchema = z.object({
  email: z.string().email(),
  role: z.enum(["ORGANIZATION_ADMIN", "USER"]),
});

/**
 * POST /api/organizations/team/invite
 * Invite a user to join the organization
 */
export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to invite users" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to invite users" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;

    // Check if user has permission to invite users
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to invite users" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { email, role } = invitationSchema.parse(body);

    // Check if user already exists in the organization
    const existingUser = await db.user.findFirst({
      where: {
        email,
        organizationId,
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User is already a member of this organization" },
        { status: 400 }
      );
    }

    // Check if there's already a pending invitation for this email
    const existingInvitation = await db.invitation.findFirst({
      where: {
        email,
        organizationId,
        status: "PENDING",
      },
    });

    if (existingInvitation) {
      return NextResponse.json(
        { error: "An invitation has already been sent to this email" },
        { status: 400 }
      );
    }

    // Get organization details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Generate invitation token
    const token = await generateInvitationToken();

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create invitation
    const invitation = await db.invitation.create({
      data: {
        email,
        role,
        token,
        expires: expiresAt,
        organization: {
          connect: { id: organizationId },
        },
      },
    });

    // Create audit log
    await auditService.log(
      "INVITATION_SENT",
      `Invitation sent to ${email} for role ${role}`,
      {
        invitationId: invitation.id,
        email,
        role,
      },
      session.user.id,
      organizationId
    );

    // Send invitation email
    // This would typically be handled by a separate service
    // For now, we'll just log it
    logger.info(`Invitation email sent to ${email} for organization ${organization.name}`);

    // Check if the invited user already exists in the system
    const existingUserInSystem = await db.user.findUnique({
      where: { email },
    });

    if (existingUserInSystem) {
      // Send notification to existing user
      await notificationService.createNotification(
        existingUserInSystem.id,
        "Organization Invitation",
        `You have been invited to join ${organization.name} as ${role === "ORGANIZATION_ADMIN" ? "an administrator" : "a member"}.`,
        NotificationType.SYSTEM,
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          invitationId: invitation.id,
          organizationId,
          organizationName: organization.name,
          role,
        },
        `/invitations/${invitation.token}`,
        "View Invitation"
      );
    }

    return NextResponse.json({
      message: "Invitation sent successfully",
      invitation,
    });
  } catch (error) {
    logger.error("Error inviting user:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while inviting user" },
      { status: 500 }
    );
  }
}
