import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { InvitationStatus, AuditLogType, NotificationType, NotificationPriority } from "@prisma/client";

/**
 * DELETE /api/organizations/team/invite/[id]
 * Cancel an invitation
 */
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to cancel invitations" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to cancel invitations" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;
    const invitationId = params.id;

    // Check if user has permission to cancel invitations
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to cancel invitations" },
        { status: 403 }
      );
    }

    // Check if invitation exists and belongs to the organization
    const invitation = await db.invitation.findFirst({
      where: {
        id: invitationId,
        organizationId,
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    // Cancel invitation
    const cancelledInvitation = await db.invitation.update({
      where: { id: invitationId },
      data: {
        status: InvitationStatus.DECLINED,
      },
    });

    // Create audit log
    await auditService.log(
      AuditLogType.USER_UPDATED,
      `Invitation cancelled for ${invitation.email}`,
      {
        invitationId,
        email: invitation.email,
        role: invitation.role,
      },
      session.user.id,
      organizationId
    );

    return NextResponse.json({
      message: "Invitation cancelled successfully",
      invitation: cancelledInvitation,
    });
  } catch (error) {
    logger.error("Error cancelling invitation:", error);

    return NextResponse.json(
      { error: "An error occurred while cancelling invitation" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/organizations/team/invite/[id]/resend
 * Resend an invitation
 */
export async function POST(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to resend invitations" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to resend invitations" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;
    const invitationId = params.id;

    // Check if user has permission to resend invitations
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to resend invitations" },
        { status: 403 }
      );
    }

    // Check if invitation exists and belongs to the organization
    const invitation = await db.invitation.findFirst({
      where: {
        id: invitationId,
        organizationId,
        status: InvitationStatus.PENDING,
      },
      include: {
        organization: true,
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invitation not found or not pending" },
        { status: 404 }
      );
    }

    // Set new expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Update invitation
    const updatedInvitation = await db.invitation.update({
      where: { id: invitationId },
      data: {
        expires: expiresAt,
        updatedAt: new Date(),
      },
    });

    // Create audit log
    await auditService.log(
      AuditLogType.USER_UPDATED,
      `Invitation resent to ${invitation.email}`,
      {
        invitationId,
        email: invitation.email,
        role: invitation.role,
      },
      session.user.id,
      organizationId
    );

    // Send invitation email
    // This would typically be handled by a separate service
    // For now, we'll just log it
    logger.info(`Invitation email resent to ${invitation.email} for organization ${invitation.organization.name}`);

    // Check if the invited user already exists in the system
    const existingUser = await db.user.findUnique({
      where: { email: invitation.email },
    });

    if (existingUser) {
      // Send notification to existing user
      await db.notification.create({
        data: {
          title: "Organization Invitation",
          message: `You have been invited to join ${invitation.organization.name} as ${invitation.role === "ORGANIZATION_ADMIN" ? "an administrator" : "a member"}.`,
          type: NotificationType.SYSTEM,
          priority: NotificationPriority.HIGH,
          user: {
            connect: { id: existingUser.id },
          },
          actionUrl: `/invitations/${invitation.token}`,
          actionLabel: "View Invitation",
          metadata: {
            invitationId,
            organizationId,
            organizationName: invitation.organization.name,
            role: invitation.role,
          },
        },
      });
    }

    return NextResponse.json({
      message: "Invitation resent successfully",
      invitation: updatedInvitation,
    });
  } catch (error) {
    logger.error("Error resending invitation:", error);

    return NextResponse.json(
      { error: "An error occurred while resending invitation" },
      { status: 500 }
    );
  }
}
