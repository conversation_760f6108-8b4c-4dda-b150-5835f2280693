import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { auditService } from "@/lib/audit";

// Schema for role update
const roleUpdateSchema = z.object({
  role: z.enum(["ORGANIZATION_ADMIN", "USER"]),
});

/**
 * PATCH /api/organizations/team/[organizationId]/role
 * Update a team member's role
 */
export async function PATCH(
  req: Request,
  { params }: { params: { organizationId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update team member roles" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to update team member roles" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;
    const userId = params.organizationId;

    // Check if user has permission to update roles
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to update team member roles" },
        { status: 403 }
      );
    }

    // Prevent changing own role
    if (userId === session.user.id) {
      return NextResponse.json(
        { error: "You cannot change your own role" },
        { status: 400 }
      );
    }

    const body = await req.json();
    const { role } = roleUpdateSchema.parse(body);

    // Check if user exists and belongs to the organization
    const user = await db.user.findFirst({
      where: {
        id: userId,
        organizationId,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in this organization" },
        { status: 404 }
      );
    }

    // Update user role
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: {
        role,
      },
    });

    // Create audit log
    await auditService.log(
      "USER_UPDATED",
      `User ${user.name || user.email} role updated to ${role}`,
      {
        userId,
        previousRole: user.role,
        newRole: role,
      },
      session.user.id,
      organizationId
    );

    // Notify user about role change
    await notificationService.createNotification(
      userId,
      "Role Updated",
      `Your role in the organization has been updated to ${role === "ORGANIZATION_ADMIN" ? "Organization Admin" : "Regular User"}.`,
      "SYSTEM",
      [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
      {
        previousRole: user.role,
        newRole: role,
      }
    );

    // If user is being promoted to ORGANIZATION_ADMIN, notify other admins in the organization
    if (role === "ORGANIZATION_ADMIN") {
      const otherAdmins = await db.user.findMany({
        where: {
          organizationId,
          role: "ORGANIZATION_ADMIN",
          id: { not: userId },
        },
      });

      for (const admin of otherAdmins) {
        if (admin.id !== session.user.id) {
          await notificationService.createNotification(
            admin.id,
            "New Organization Admin",
            `${user.name || user.email} has been promoted to Organization Admin.`,
            "SYSTEM",
            [NotificationChannel.IN_APP],
            {
              userId,
              userName: user.name || user.email,
            }
          );
        }
      }
    }

    return NextResponse.json({
      message: "User role updated successfully",
      user: updatedUser,
    });
  } catch (error) {
    logger.error("Error updating user role:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating user role" },
      { status: 500 }
    );
  }
}
