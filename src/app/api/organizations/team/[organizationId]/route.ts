import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { auditService } from "@/lib/audit";
import { AuditLogType } from "@prisma/client";

/**
 * GET /api/organizations/team/[organizationId]
 * Get team member details
 */
export async function GET(
  req: Request,
  context: { params: Promise<{ organizationId: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view team member details" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to view team member details" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;
    const userId = (await context.params).organizationId;

    // Check if user has permission to view team member details
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin && userId !== session.user.id) {
      return NextResponse.json(
        { error: "You do not have permission to view this team member's details" },
        { status: 403 }
      );
    }

    // Get user details
    const user = await db.user.findFirst({
      where: {
        id: userId,
        organizationId,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in this organization" },
        { status: 404 }
      );
    }

    // Get user's activity
    const auditLogs = await db.auditLog.findMany({
      where: {
        userId,
        organizationId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    // Get user's carbon credits
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        userId,
        organizationId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    // Get user's transactions through orders
    const transactions = await db.transaction.findMany({
      where: {
        order: {
          OR: [
            { buyerId: userId },
            { sellerId: userId },
          ],
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
      include: {
        order: {
          include: {
            carbonCredit: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      user,
      auditLogs,
      carbonCredits,
      transactions,
    });
  } catch (error) {
    logger.error("Error fetching team member details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching team member details" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/organizations/team/[organizationId]
 * Remove a team member from the organization
 */
export async function DELETE(
  req: Request,
  { params }: { params: { organizationId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to remove team members" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to remove team members" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;
    const userId = params.organizationId;

    // Check if user has permission to remove team members
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to remove team members" },
        { status: 403 }
      );
    }

    // Prevent removing self
    if (userId === session.user.id) {
      return NextResponse.json(
        { error: "You cannot remove yourself from the organization" },
        { status: 400 }
      );
    }

    // Check if user exists and belongs to the organization
    const user = await db.user.findFirst({
      where: {
        id: userId,
        organizationId,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in this organization" },
        { status: 404 }
      );
    }

    // Remove user from organization
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: {
        organizationId: null,
        role: "USER", // Reset role to regular user
      },
    });

    // Create audit log
    await auditService.log(
      AuditLogType.USER_DELETED,
      `User ${user.name || user.email} removed from organization`,
      {
        userId,
        userName: user.name || user.email,
        userEmail: user.email,
      },
      session.user.id,
      organizationId
    );

    // Notify user about removal
    await notificationService.createNotification(
      userId,
      "Removed from Organization",
      `You have been removed from the organization.`,
      "SYSTEM",
      [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
      {
        organizationId,
      }
    );

    return NextResponse.json({
      message: "User removed from organization successfully",
      user: updatedUser,
    });
  } catch (error) {
    logger.error("Error removing team member:", error);

    return NextResponse.json(
      { error: "An error occurred while removing team member" },
      { status: 500 }
    );
  }
}
