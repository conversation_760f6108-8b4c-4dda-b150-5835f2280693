import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

/**
 * GET /api/organizations/team
 * Get team members and invitations for the current user's organization
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access team members" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to access team members" },
        { status: 403 }
      );
    }

    const organizationId = session.user.organizationId;

    // Check if user has permission to view team members
    const isAdmin = session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to access team members" },
        { status: 403 }
      );
    }

    // Get team members
    const members = await db.user.findMany({
      where: {
        organizationId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get invitations
    const invitations = await db.invitation.findMany({
      where: {
        organizationId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      members,
      invitations,
    });
  } catch (error) {
    logger.error("Error fetching team members:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching team members" },
      { status: 500 }
    );
  }
}
