import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { AuditLogType, VerificationStatus } from "@prisma/client";
import { onboardingEmailService } from "@/lib/email/onboarding-emails";

// Schema for document verification
const documentSchema = z.object({
  name: z.string().min(2, "Document name is required"),
  type: z.enum([
    "BUSINESS_REGISTRATION",
    "TAX_CERTIFICATE",
    "IDENTITY_PROOF",
    "ADDRESS_PROOF",
    "BANK_STATEMENT",
    "OTHER",
  ]),
  url: z.string().url("Please enter a valid URL"),
  notes: z.string().optional(),
});

// Schema for organization verification
const verificationSchema = z.object({
  organizationId: z.string(),
  documents: z.array(documentSchema).min(1, "At least one document is required"),
});

/**
 * POST /api/organizations/verify
 * Submit organization verification documents
 */
export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to verify an organization" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { organizationId, documents } = verificationSchema.parse(body);

    // Check if the user belongs to the organization
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        organization: true,
      },
    });

    if (!user?.organization || user.organization.id !== organizationId) {
      return NextResponse.json(
        { error: "You do not have permission to verify this organization" },
        { status: 403 }
      );
    }

    // Store the documents
    for (const document of documents) {
      await db.document.create({
        data: {
          name: document.name,
          type: document.type,
          url: document.url,
          notes: document.notes,
          status: VerificationStatus.PENDING,
          organization: {
            connect: {
              id: organizationId,
            },
          },
          uploadedBy: {
            connect: {
              id: session.user.id,
            },
          },
        },
      });
    }

    // Update organization verification status
    await db.organization.update({
      where: {
        id: organizationId,
      },
      data: {
        verificationStatus: VerificationStatus.IN_REVIEW,
      },
    });

    // Create an audit log entry
    await db.auditLog.create({
      data: {
        type: AuditLogType.DOCUMENT_UPLOADED,
        description: `Verification documents uploaded for organization ${user.organization.name}`,
        userId: session.user.id,
        organizationId: organizationId,
        metadata: {
          documentCount: documents.length,
          documentTypes: documents.map(doc => doc.type),
        },
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        title: "Verification Submitted",
        message: `Your verification documents for ${user.organization.name} have been submitted and are under review.`,
        type: "SYSTEM",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });

    // Notify platform admins about the verification request
    const admins = await db.user.findMany({
      where: { role: "ADMIN" },
    });

    for (const admin of admins) {
      await db.notification.create({
        data: {
          title: "Verification Request",
          message: `${user.organization.name} has submitted verification documents for review.`,
          type: "SYSTEM",
          priority: "HIGH",
          user: {
            connect: {
              id: admin.id,
            },
          },
          organization: {
            connect: {
              id: organizationId,
            },
          },
          actionUrl: `/admin/organizations/${organizationId}/verification`,
          actionLabel: "Review Documents",
        },
      });
    }

    // Send verification submitted email
    try {
      await onboardingEmailService.sendVerificationSubmittedEmail(
        session.user.email || '',
        session.user.name || 'User',
        user.organization.name
      );
    } catch (emailError) {
      logger.error("Error sending verification submitted email:", emailError);
      // Continue even if email fails
    }

    return NextResponse.json({
      message: "Verification documents submitted successfully",
      status: "IN_REVIEW",
    });
  } catch (error) {
    logger.error("Error submitting verification documents:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while submitting verification documents" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/organizations/verify
 * Get verification status and documents for an organization
 */
export async function GET(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to check verification status" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    // Check if the user belongs to the organization or is an admin
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        role: true,
        organizationId: true,
      },
    });

    const isAdmin = user?.role === "ADMIN";
    const belongsToOrg = user?.organizationId === organizationId;

    if (!isAdmin && !belongsToOrg) {
      return NextResponse.json(
        { error: "You do not have permission to view this organization's verification status" },
        { status: 403 }
      );
    }

    // Get organization verification status and documents
    const organization = await db.organization.findUnique({
      where: {
        id: organizationId,
      },
      select: {
        id: true,
        name: true,
        verificationStatus: true,
        documents: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      organization,
    });
  } catch (error) {
    logger.error("Error getting verification status:", error);

    return NextResponse.json(
      { error: "An error occurred while getting verification status" },
      { status: 500 }
    );
  }
}
