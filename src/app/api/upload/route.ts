import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { db } from "@/lib/db";
import { uploadFile, generateUniqueFileName } from "@/lib/storage";
import path from "path";

/**
 * Upload a file
 *
 * @returns The uploaded file URL
 */
async function uploadFileHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to upload files",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const organizationId = formData.get("organizationId") as string;
    const documentType = formData.get("documentType") as string;

    if (!file) {
      throw new ApiError(
        "No file provided",
        ErrorType.VALIDATION,
        400
      );
    }

    if (!organizationId) {
      throw new ApiError(
        "Organization ID is required",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the organization exists and belongs to the user
    const organization = await db.organization.findFirst({
      where: {
        id: organizationId,
        users: {
          some: {
            id: session.user.id,
          },
        },
      },
    });

    if (!organization) {
      throw new ApiError(
        "Organization not found or you don't have permission to upload files for this organization",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Generate a unique filename with organization ID as prefix
    const fileExtension = path.extname(file.name);
    const fileName = generateUniqueFileName(file.name, organizationId);

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Upload the file using our storage system
    const uploadResult = await uploadFile(
      buffer,
      fileName,
      file.type,
      {
        originalName: file.name,
        organizationId,
        documentType,
        uploadedBy: session.user.id,
      }
    );

    if (!uploadResult.success) {
      throw new ApiError(
        uploadResult.error || "Failed to upload file",
        ErrorType.INTERNAL,
        500
      );
    }

    // Get the URL for the file
    const fileUrl = uploadResult.url;

    // Create a document record in the database
    const document = await db.document.create({
      data: {
        name: file.name,
        type: documentType as any,
        url: fileUrl,
        status: "PENDING",
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });

    logger.info(`File uploaded: ${fileUrl} for organization ${organizationId}`);

    // Create an audit log
    await db.auditLog.create({
      data: {
        type: "DOCUMENT_UPLOADED",
        description: `Document ${file.name} uploaded for organization ${organization.name}`,
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: organizationId,
          },
        },
        metadata: {
          documentId: document.id,
          documentName: file.name,
          documentType: documentType,
        },
      },
    });

    return NextResponse.json({
      url: fileUrl,
      documentId: document.id,
      message: "File uploaded successfully",
    });
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    logger.error("Error uploading file:", error);
    throw new ApiError(
      "Failed to upload file",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(uploadFileHandler);
