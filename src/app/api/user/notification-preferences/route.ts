import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType } from "@prisma/client";

// Schema for notification preferences
const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean().default(true),
  inAppNotifications: z.boolean().default(true),
  
  // System notifications
  systemEmailEnabled: z.boolean().default(true),
  systemInAppEnabled: z.boolean().default(true),
  
  // Transaction notifications
  transactionEmailEnabled: z.boolean().default(true),
  transactionInAppEnabled: z.boolean().default(true),
  
  // Credit notifications
  creditEmailEnabled: z.boolean().default(true),
  creditInAppEnabled: z.boolean().default(true),
  
  // Billing notifications
  billingEmailEnabled: z.boolean().default(true),
  billingInAppEnabled: z.boolean().default(true),
  
  // Document notifications
  documentEmailEnabled: z.boolean().default(true),
  documentInAppEnabled: z.boolean().default(true),
  
  // Security notifications
  securityEmailEnabled: z.boolean().default(true),
  securityInAppEnabled: z.boolean().default(true),
  
  // Marketing notifications
  marketingEmailEnabled: z.boolean().default(false),
  marketingInAppEnabled: z.boolean().default(false),
});

/**
 * GET /api/user/notification-preferences
 * Get user notification preferences
 */
async function getNotificationPreferencesHandler() {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access notification preferences",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const userId = session.user.id;
    
    // Get user notification preferences
    const preferences = await db.notificationPreferences.findUnique({
      where: { userId },
    });
    
    // If no preferences exist, create default preferences
    if (!preferences) {
      const defaultPreferences = await db.notificationPreferences.create({
        data: {
          userId,
          emailNotifications: true,
          inAppNotifications: true,
          systemEmailEnabled: true,
          systemInAppEnabled: true,
          transactionEmailEnabled: true,
          transactionInAppEnabled: true,
          creditEmailEnabled: true,
          creditInAppEnabled: true,
          billingEmailEnabled: true,
          billingInAppEnabled: true,
          documentEmailEnabled: true,
          documentInAppEnabled: true,
          securityEmailEnabled: true,
          securityInAppEnabled: true,
          marketingEmailEnabled: false,
          marketingInAppEnabled: false,
        },
      });
      
      return NextResponse.json({ preferences: defaultPreferences });
    }
    
    return NextResponse.json({ preferences });
  } catch (error) {
    logger.error("Error getting notification preferences:", error);
    throw new ApiError(
      "An error occurred while getting notification preferences",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PUT /api/user/notification-preferences
 * Update user notification preferences
 */
async function updateNotificationPreferencesHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update notification preferences",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const userId = session.user.id;
    
    // Parse and validate request body
    const body = await req.json();
    const validatedData = notificationPreferencesSchema.parse(body);
    
    // Get existing preferences
    const existingPreferences = await db.notificationPreferences.findUnique({
      where: { userId },
    });
    
    // Update or create preferences
    const updatedPreferences = await db.notificationPreferences.upsert({
      where: { userId },
      update: validatedData,
      create: {
        userId,
        ...validatedData,
      },
    });
    
    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.NOTIFICATION_PREFERENCES_UPDATED,
        description: "Notification preferences updated",
        userId,
        metadata: {
          previousPreferences: existingPreferences || "No previous preferences",
          newPreferences: updatedPreferences,
        },
      },
    });
    
    logger.info(`Notification preferences updated for user ${userId}`);
    
    return NextResponse.json({
      message: "Notification preferences updated successfully",
      preferences: updatedPreferences,
    });
  } catch (error) {
    logger.error("Error updating notification preferences:", error);
    
    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }
    
    throw new ApiError(
      "An error occurred while updating notification preferences",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getNotificationPreferencesHandler);
export const PUT = withErrorHandling(updateNotificationPreferencesHandler);
