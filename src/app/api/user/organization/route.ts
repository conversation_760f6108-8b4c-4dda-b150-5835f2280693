import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

/**
 * GET /api/user/organization
 * Get the current user's organization
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get user with organization
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        organization: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      organization: user.organization || null,
    });
  } catch (error) {
    logger.error("Error fetching user organization:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching user organization" },
      { status: 500 }
    );
  }
}
