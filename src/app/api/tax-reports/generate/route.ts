import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceManager } from "@/lib/compliance";

/**
 * POST /api/tax-reports/generate
 * Generate a tax report
 */
async function generateTaxReportHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to generate a tax report",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const body = await req.json();
    const { year, quarter, format = "pdf" } = body;

    // Validate request
    if (!year) {
      throw new ApiError("Year is required", ErrorType.VALIDATION, 400);
    }

    if (quarter && (quarter < 1 || quarter > 4)) {
      throw new ApiError("Quarter must be between 1 and 4", ErrorType.VALIDATION, 400);
    }

    if (format && !["pdf", "csv", "xlsx"].includes(format)) {
      throw new ApiError("Format must be pdf, csv, or xlsx", ErrorType.VALIDATION, 400);
    }

    // Generate tax report
    const reportUrl = await ComplianceManager.generateTaxReport({
      userId: session.user.id,
      organizationId: session.user.organizationId,
      year,
      quarter,
      format: format as "pdf" | "csv" | "xlsx",
    });

    // Extract report ID from URL
    const reportId = reportUrl.split("/").pop()?.split("?")[0];

    return NextResponse.json({ reportUrl, reportId });
  } catch (error) {
    logger.error("Error generating tax report:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while generating tax report",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(generateTaxReportHandler);
