import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { db } from "@/lib/db";
import { ComplianceAuditService } from "@/lib/audit/compliance-audit";

/**
 * GET /api/tax-reports/[id]/download
 * Download a tax report
 */
async function downloadTaxReportHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to download a tax report",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const { id } = params;

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const format = searchParams.get("format") || "pdf";

    // Get tax report
    const report = await db.taxReport.findUnique({
      where: { id },
    });

    if (!report) {
      throw new ApiError("Tax report not found", ErrorType.NOT_FOUND, 404);
    }

    // Check if user is authorized to download this report
    if (
      report.userId !== session.user.id &&
      report.organizationId !== session.user.organizationId &&
      session.user.role !== "ADMIN"
    ) {
      throw new ApiError(
        "You are not authorized to download this tax report",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // In a real implementation, this would generate and return the actual file
    // For now, we'll simulate a download by returning JSON data

    // Get report data
    const reportData = report.data;

    // Set appropriate headers based on format
    let headers: HeadersInit = {};
    let fileName = `tax-report-${report.year}${report.quarter ? `-q${report.quarter}` : ""}.${format}`;

    switch (format) {
      case "pdf":
        headers = {
          "Content-Type": "application/pdf",
          "Content-Disposition": `attachment; filename="${fileName}"`,
        };
        break;
      case "csv":
        headers = {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="${fileName}"`,
        };
        break;
      case "xlsx":
        headers = {
          "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Disposition": `attachment; filename="${fileName}"`,
        };
        break;
      default:
        headers = {
          "Content-Type": "application/json",
        };
    }

    // Log the tax report download in the audit trail
    await ComplianceAuditService.logTaxReportDownload({
      userId: session.user.id,
      organizationId: session.user.organizationId,
      reportId: id,
      year: report.year,
      quarter: report.quarter || undefined,
      format: format,
      ipAddress: req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
    });

    // For demonstration purposes, we'll return JSON data
    // In a real implementation, this would return the actual file
    return NextResponse.json(
      {
        reportData,
        message: `This is a simulated download. In a real implementation, this would return a ${format.toUpperCase()} file.`,
      },
      { headers }
    );
  } catch (error) {
    logger.error("Error downloading tax report:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while downloading tax report",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(downloadTaxReportHandler);
