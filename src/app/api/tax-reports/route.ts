import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceManager } from "@/lib/compliance";

/**
 * GET /api/tax-reports
 * Get tax reports for the current user or organization
 */
async function getTaxReportsHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to get tax reports",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get tax reports for the current user or organization
    const reports = await ComplianceManager.getTaxReports(
      session.user.id,
      session.user.organizationId
    );

    return NextResponse.json({ reports });
  } catch (error) {
    logger.error("Error getting tax reports:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while getting tax reports",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getTaxReportsHandler);
