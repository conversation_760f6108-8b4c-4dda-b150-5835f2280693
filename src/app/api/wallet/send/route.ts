import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";

import { getBlockchainClient } from "@/lib/blockchain-client";
import { GasOptimizer, GasStrategy } from "@/lib/gas-optimizer";
import { validateRequest } from "@/lib/validation/utils";
import { ethereumAddressSchema, amountSchema } from "@/lib/validation/schemas";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

// Schema for sending native currency
const sendSchema = z.object({
  walletId: z.string().min(1, "Wallet ID is required"),
  to: ethereumAddressSchema,
  amount: amountSchema,
  gasStrategy: z.enum([
    GasStrategy.SLOW,
    GasStrategy.AVERAGE,
    GasStrategy.FAST,
    GasStrategy.CUSTOM,
  ]).default(GasStrategy.AVERAGE),
  maxFeePerGas: z.string().optional(),
  maxPriorityFeePerGas: z.string().optional(),
  gasPrice: z.string().optional(),
  gasLimit: z.number().optional(),
});

/**
 * Send native currency from the user's wallet
 *
 * @returns Transaction details
 */
async function sendNativeHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to send funds",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Validate request body
  const body = await req.json();
  const validationResult = validateRequest(sendSchema, body);

  if (validationResult.error) {
    throw new ApiError(
      validationResult.error,
      ErrorType.VALIDATION,
      400
    );
  }

  const {
    walletId,
    to,
    amount,
    gasStrategy,
    maxFeePerGas,
    maxPriorityFeePerGas,
    gasPrice,
    gasLimit
  } = validationResult.data;

  const amountValue = parseFloat(amount);

  // Get user's wallet
  const wallet = await db.wallet.findFirst({
    where: {
      id: walletId,
      userId: session.user.id,
    },
  });

  if (!wallet) {
    throw new ApiError(
      "Wallet not found",
      ErrorType.NOT_FOUND,
      404
    );
  }

  if (!wallet.encryptedKey) {
    throw new ApiError(
      "Wallet does not have an encrypted key",
      ErrorType.VALIDATION,
      400
    );
  }

  // Check if there's enough balance
  if (wallet.balance < amountValue) {
    throw new ApiError(
      "Insufficient balance",
      ErrorType.VALIDATION,
      400
    );
  }

  try {
    // Get blockchain client for the wallet's network
    const blockchainClient = getBlockchainClient(wallet.network as any);

    // Get gas optimizer
    const gasOptimizer = new GasOptimizer(wallet.network as any);

    // Get optimized gas parameters
    const gasOptions = await gasOptimizer.getOptimizedGasParams({
      strategy: gasStrategy,
      maxFeePerGas,
      maxPriorityFeePerGas,
      gasPrice,
      gasLimit,
    });

    // Create transaction record with PENDING status
    const transaction = await db.transaction.create({
      data: {
        amount: amountValue,
        fee: 0, // Will be updated after the transaction
        type: "WITHDRAWAL",
        status: "PENDING",
        network: wallet.network,
        chainId: wallet.chainId,
        wallet: {
          connect: {
            id: wallet.id,
          },
        },
      },
    });

    logger.info(`Sending ${amount} from ${wallet.address} to ${to} on ${wallet.network}`);

    // Send transaction
    const result = await blockchainClient.sendTransaction(
      wallet.encryptedKey,
      to,
      amount,
      gasOptions
    );

    // Calculate the fee
    let fee = 0;
    if (result.gasUsed && result.effectiveGasPrice) {
      const gasUsed = BigInt(result.gasUsed);
      const effectiveGasPrice = BigInt(result.effectiveGasPrice);
      const feeWei = gasUsed * effectiveGasPrice;
      fee = parseFloat(ethers.formatEther(feeWei));
    }

    // Update transaction with hash, status, and fee
    const updatedTransaction = await db.transaction.update({
      where: {
        id: transaction.id,
      },
      data: {
        status: "COMPLETED",
        transactionHash: result.hash,
        blockNumber: result.blockNumber,
        fee,
        gasUsed: result.gasUsed ? parseInt(result.gasUsed) : undefined,
        gasPrice: gasOptions.gasPrice ? parseFloat(gasOptions.gasPrice) : undefined,
        maxFeePerGas: gasOptions.maxFeePerGas ? parseFloat(gasOptions.maxFeePerGas) : undefined,
        maxPriorityFeePerGas: gasOptions.maxPriorityFeePerGas ? parseFloat(gasOptions.maxPriorityFeePerGas) : undefined,
        updatedAt: new Date(),
      },
    });

    // Update wallet balance
    await db.wallet.update({
      where: {
        id: wallet.id,
      },
      data: {
        balance: {
          decrement: amountValue + fee,
        },
        lastSyncedAt: new Date(),
      },
    });

    // Create notification
    await db.notification.create({
      data: {
        title: "Transaction Successful",
        message: `${amount} has been sent to ${to} on ${wallet.network}`,
        type: "TRANSACTION",
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });

    logger.info(`Transaction successful: ${result.hash} on ${wallet.network}`);

    return NextResponse.json({
      transaction: {
        id: updatedTransaction.id,
        hash: result.hash,
        from: wallet.address,
        to,
        amount: amountValue,
        fee,
        status: "COMPLETED",
        network: wallet.network,
        chainId: wallet.chainId,
        blockNumber: result.blockNumber,
      },
      message: "Transaction successful",
    });
  } catch (error) {
    logger.error(`Error sending transaction for user ${session.user.id}:`, error);
    throw new ApiError(
      "Failed to send transaction",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * Get gas price estimates
 *
 * @returns Gas price estimates
 */
async function getNativeGasPricesHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to get gas prices",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const { searchParams } = new URL(req.url);
  const network = searchParams.get("network") || DEFAULT_NETWORK;

  try {
    // Get blockchain client for the specified network
    const blockchainClient = getBlockchainClient(network as any);

    // Get gas prices
    const gasPrices = await blockchainClient.getGasPrices();

    // Get gas optimizer
    const gasOptimizer = new GasOptimizer(network as any);

    // Get gas price history
    const gasPriceHistory = await gasOptimizer.getGasPriceHistory(5);

    logger.info(`Retrieved gas prices for ${network}`);

    return NextResponse.json({
      gasPrices,
      gasPriceHistory,
      timestamp: new Date(),
      network,
    });
  } catch (error) {
    logger.error(`Error getting gas prices for ${network}:`, error);
    throw new ApiError(
      "Failed to get gas prices",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getNativeGasPricesHandler);
export const POST = withErrorHandling(sendNativeHandler);
