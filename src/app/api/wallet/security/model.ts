/**
 * Wallet security settings model
 */
export interface WalletSecuritySettings {
  twoFactorEnabled?: boolean;
  twoFactorType?: string;
  whitelistedAddresses?: string[];
  blacklistedAddresses?: string[];
  delayedWithdrawals?: boolean;
  withdrawalDelayHours?: number;
  notificationsEnabled?: boolean;
  autoLockEnabled?: boolean;
  autoLockTimeoutMinutes?: number;
  spendingNotifications?: boolean;
  unusualActivityDetection?: boolean;
  securityReviewFrequency?: number;
}

/**
 * Wallet recovery settings model
 */
export interface WalletRecoverySettings {
  recoveryEnabled: boolean;
  recoveryType: "SOCIAL_RECOVERY" | "SEED_PHRASE" | "HARDWARE_BACKUP" | "MULTI_SIG" | "GUARDIAN";
  recoveryData?: any;
}

/**
 * Wallet transaction limits model
 */
export interface WalletTransactionLimits {
  transactionLimitDaily?: number;
  transactionLimitPerTx?: number;
  requireApprovals?: boolean;
  approvalThreshold?: number;
}

/**
 * Wallet security response model
 */
export interface WalletSecurityResponse {
  security: {
    twoFactorEnabled?: boolean;
    twoFactorType?: string;
    whitelistedAddresses?: string[];
    blacklistedAddresses?: string[];
    delayedWithdrawals?: boolean;
    withdrawalDelayHours?: number;
    notificationsEnabled?: boolean;
    autoLockEnabled?: boolean;
    autoLockTimeoutMinutes?: number;
    spendingNotifications?: boolean;
    unusualActivityDetection?: boolean;
    securityReviewFrequency?: number;
    securityScore: number;
    recoveryEnabled: boolean;
    recoveryType?: string;
    transactionLimitDaily?: number;
    transactionLimitPerTx?: number;
    requireApprovals?: boolean;
    approvalThreshold?: number;
  };
  accessControls: {
    id: string;
    userId: string;
    accessLevel: string;
    canApprove: boolean;
    canInitiate: boolean;
    canView: boolean;
    customLimits?: any;
    expiresAt?: Date;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
}
