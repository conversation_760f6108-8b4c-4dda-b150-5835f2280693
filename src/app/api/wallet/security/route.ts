import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { AuditLogType } from "@prisma/client";

// Schema for updating wallet security settings
const securitySettingsSchema = z.object({
  twoFactorEnabled: z.boolean().optional(),
  twoFactorType: z.string().optional(),
  whitelistedAddresses: z.array(z.string()).optional(),
  blacklistedAddresses: z.array(z.string()).optional(),
  delayedWithdrawals: z.boolean().optional(),
  withdrawalDelayHours: z.number().int().min(0).optional(),
  notificationsEnabled: z.boolean().optional(),
  autoLockEnabled: z.boolean().optional(),
  autoLockTimeoutMinutes: z.number().int().min(1).optional(),
  spendingNotifications: z.boolean().optional(),
  unusualActivityDetection: z.boolean().optional(),
  securityReviewFrequency: z.number().int().min(0).optional(),
});

// Schema for recovery settings
const recoverySettingsSchema = z.object({
  recoveryEnabled: z.boolean(),
  recoveryType: z.enum(["SOCIAL_RECOVERY", "SEED_PHRASE", "HARDWARE_BACKUP", "MULTI_SIG", "GUARDIAN"]),
  recoveryData: z.record(z.any()).optional(),
});

// Schema for transaction limits
const transactionLimitsSchema = z.object({
  transactionLimitDaily: z.number().min(0).optional(),
  transactionLimitPerTx: z.number().min(0).optional(),
  requireApprovals: z.boolean().optional(),
  approvalThreshold: z.number().int().min(1).optional(),
});

// GET handler for wallet security settings
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access wallet security settings" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
      include: {
        securitySettings: true,
        accessControls: true,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet with security settings
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    // Calculate security score based on enabled security features
    const securityScore = calculateSecurityScore(wallet);

    // Update security score if it has changed
    if (wallet.securityScore !== securityScore) {
      await db.wallet.update({
        where: { id: wallet.id },
        data: { securityScore },
      });
    }

    return NextResponse.json({
      security: {
        ...wallet.securitySettings,
        securityScore,
        recoveryEnabled: wallet.recoveryEnabled,
        recoveryType: wallet.recoveryType,
        transactionLimitDaily: wallet.transactionLimitDaily,
        transactionLimitPerTx: wallet.transactionLimitPerTx,
        requireApprovals: wallet.requireApprovals,
        approvalThreshold: wallet.approvalThreshold,
      },
      accessControls: wallet.accessControls,
    });
  } catch (error) {
    logger.error("Error fetching wallet security settings:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching wallet security settings" },
      { status: 500 }
    );
  }
}

// POST handler for updating wallet security settings
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update wallet security settings" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");
    const settingType = searchParams.get("type") || "general";

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
      include: {
        securitySettings: true,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    const body = await req.json();

    // Process different types of security settings
    if (settingType === "general") {
      const validatedData = securitySettingsSchema.parse(body);

      // Create or update security settings
      const securitySettings = await db.walletSecuritySetting.upsert({
        where: {
          walletId: wallet.id,
        },
        create: {
          ...validatedData,
          wallet: {
            connect: {
              id: wallet.id,
            },
          },
        },
        update: validatedData,
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: AuditLogType.WALLET_SECURITY_UPDATED,
          description: "Wallet security settings updated",
          userId: session.user.id,
          metadata: {
            walletId: wallet.id,
            settings: validatedData,
          },
        },
      });

      return NextResponse.json({
        securitySettings,
        message: "Security settings updated successfully",
      });
    } else if (settingType === "recovery") {
      const validatedData = recoverySettingsSchema.parse(body);

      // Update wallet recovery settings
      const updatedWallet = await db.wallet.update({
        where: { id: wallet.id },
        data: {
          recoveryEnabled: validatedData.recoveryEnabled,
          recoveryType: validatedData.recoveryType,
          recoveryData: validatedData.recoveryData,
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: validatedData.recoveryEnabled
            ? AuditLogType.WALLET_RECOVERY_ENABLED
            : AuditLogType.WALLET_RECOVERY_DISABLED,
          description: validatedData.recoveryEnabled
            ? `Wallet recovery enabled (${validatedData.recoveryType})`
            : "Wallet recovery disabled",
          userId: session.user.id,
          metadata: {
            walletId: wallet.id,
            recoveryType: validatedData.recoveryType,
          },
        },
      });

      return NextResponse.json({
        wallet: {
          id: updatedWallet.id,
          recoveryEnabled: updatedWallet.recoveryEnabled,
          recoveryType: updatedWallet.recoveryType,
        },
        message: "Recovery settings updated successfully",
      });
    } else if (settingType === "limits") {
      const validatedData = transactionLimitsSchema.parse(body);

      // Update wallet transaction limits
      const updatedWallet = await db.wallet.update({
        where: { id: wallet.id },
        data: {
          transactionLimitDaily: validatedData.transactionLimitDaily,
          transactionLimitPerTx: validatedData.transactionLimitPerTx,
          requireApprovals: validatedData.requireApprovals,
          approvalThreshold: validatedData.approvalThreshold,
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: AuditLogType.WALLET_LIMITS_UPDATED,
          description: "Wallet transaction limits updated",
          userId: session.user.id,
          metadata: {
            walletId: wallet.id,
            limits: validatedData,
          },
        },
      });

      return NextResponse.json({
        wallet: {
          id: updatedWallet.id,
          transactionLimitDaily: updatedWallet.transactionLimitDaily,
          transactionLimitPerTx: updatedWallet.transactionLimitPerTx,
          requireApprovals: updatedWallet.requireApprovals,
          approvalThreshold: updatedWallet.approvalThreshold,
        },
        message: "Transaction limits updated successfully",
      });
    } else {
      return NextResponse.json(
        { error: "Invalid setting type" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Error updating wallet security settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating wallet security settings" },
      { status: 500 }
    );
  }
}

// Helper function to calculate security score based on enabled security features
function calculateSecurityScore(wallet: any): number {
  let score = 0;
  const securitySettings = wallet.securitySettings;

  // Base score for having a wallet
  score += 10;

  // Recovery settings
  if (wallet.recoveryEnabled) {
    score += 20;
  }

  if (!securitySettings) {
    return score;
  }

  // Two-factor authentication
  if (securitySettings.twoFactorEnabled) {
    score += 25;
  }

  // Whitelisted addresses
  if (securitySettings.whitelistedAddresses && securitySettings.whitelistedAddresses.length > 0) {
    score += 10;
  }

  // Delayed withdrawals
  if (securitySettings.delayedWithdrawals) {
    score += 15;
  }

  // Auto-lock
  if (securitySettings.autoLockEnabled) {
    score += 10;
  }

  // Notifications
  if (securitySettings.notificationsEnabled) {
    score += 5;
  }

  // Spending notifications
  if (securitySettings.spendingNotifications) {
    score += 5;
  }

  // Unusual activity detection
  if (securitySettings.unusualActivityDetection) {
    score += 10;
  }

  // Transaction limits
  if (wallet.transactionLimitDaily || wallet.transactionLimitPerTx) {
    score += 10;
  }

  // Approval requirements
  if (wallet.requireApprovals) {
    score += 15;
  }

  // Cap the score at 100
  return Math.min(score, 100);
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
