import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Get all wallets for the authenticated user
 *
 * @returns List of wallets
 */
async function listWalletsHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access wallet information",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const { searchParams } = new URL(req.url);
  const network = searchParams.get("network");
  const isTestnet = searchParams.get("isTestnet");
  const isSmartWallet = searchParams.get("isSmartWallet");

  try {
    // Build filter
    const filter: any = {
      userId: session.user.id,
    };

    if (network) {
      filter.network = network;
    }

    if (isTestnet !== null) {
      filter.isTestnet = isTestnet === "true";
    }

    if (isSmartWallet !== null) {
      filter.isSmartWallet = isSmartWallet === "true";
    }

    // Get user's wallets
    const wallets = await db.wallet.findMany({
      where: filter,
      orderBy: [
        { network: "asc" },
        { createdAt: "desc" },
      ],
    });

    logger.info(`Retrieved ${wallets.length} wallets for user ${session.user.id}`);

    return NextResponse.json({
      wallets: wallets.map(wallet => ({
        id: wallet.id,
        address: wallet.address,
        network: wallet.network,
        chainId: wallet.chainId,
        isTestnet: wallet.isTestnet,
        isSmartWallet: wallet.isSmartWallet,
        balance: wallet.balance,
        createdAt: wallet.createdAt,
      })),
    });
  } catch (error) {
    logger.error(`Error retrieving wallets for user ${session.user.id}:`, error);
    throw new ApiError(
      "Failed to retrieve wallets",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(listWalletsHandler);
