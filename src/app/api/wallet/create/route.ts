import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { AuditLogType } from "@prisma/client";
import { ethers } from "ethers";
import { onboardingEmailService } from "@/lib/email/onboarding-emails";
import { withOrganizationIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

const walletCreateSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  walletType: z.enum(["SMART_WALLET", "REGULAR_WALLET"]),
  network: z.enum(["ETHEREUM", "POLYGON", "ARBITRUM", "OPTIMISM", "BASE"]),
  securityLevel: z.enum(["STANDARD", "HIGH"]),
  testMode: z.boolean(),
});

// Original POST handler
async function postHandler(req: NextRequest, { params }: { params: { organizationId: string } }) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create a wallet" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { organizationId, walletType, network, securityLevel, testMode } = walletCreateSchema.parse(body);

    logger.info(`Creating wallet for organization ${organizationId}`);
    logger.info(`User ID: ${session.user.id}`);
    logger.info(`Wallet type: ${walletType}`);
    logger.info(`Network: ${network}`);
    logger.info(`Security level: ${securityLevel}`);
    logger.info(`Test mode: ${testMode}`);

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user has permission to create a wallet for this organization
    // Note: The tenant isolation middleware already checks if the user can access this organization
    // This is just a fallback check
    if (!tenantContext.isAdmin && tenantContext.organizationId !== organizationId) {
      logger.warn(`User ${session.user.id} attempted to create a wallet for organization ${organizationId} they don't belong to`);
      return NextResponse.json(
        { error: "You do not have permission to create a wallet for this organization" },
        { status: 403 }
      );
    }

    // Check if organization already has a wallet with tenant isolation
    let walletQuery = {
      where: {
        organizationId,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantIsolation(walletQuery, tenantContext);

    const existingWallet = await db.wallet.findFirst(walletQuery);

    if (existingWallet) {
      return NextResponse.json(
        { error: "This organization already has a wallet" },
        { status: 409 }
      );
    }

    try {
      // Generate wallet address based on type
      let walletAddress = "";
      let metadata = {};

      if (walletType === "REGULAR_WALLET") {
        // Create a standard Ethereum wallet
        const wallet = ethers.Wallet.createRandom();
        walletAddress = wallet.address;
        metadata = {
          publicKey: wallet.publicKey,
          encryptedPrivateKey: "DEMO_ENCRYPTED_KEY", // Would be encrypted in production
        };
        logger.info(`Created regular wallet with address: ${walletAddress}`);
      } else {
        // Create a smart wallet
        const ownerWallet = ethers.Wallet.createRandom();
        walletAddress = `0x${Array(40).fill(0).map(() =>
          Math.floor(Math.random() * 16).toString(16)).join('')}`;
        metadata = {
          ownerAddress: ownerWallet.address,
          securityLevel,
          factoryAddress: "0xSimulatedFactoryAddress",
          implementationAddress: "0xSimulatedImplementationAddress",
        };
        logger.info(`Created smart wallet with address: ${walletAddress}`);
      }

      // Create wallet in database
      const wallet = await db.wallet.create({
        data: {
          address: walletAddress,
          network: network,
          isTestnet: testMode,
          isSmartWallet: walletType === "SMART_WALLET",
          balance: 0,
          chainId: testMode ? 80001 : 137, // Mumbai testnet or Polygon mainnet
          organization: {
            connect: {
              id: organizationId,
            },
          },
          user: {
            connect: {
              id: session.user.id,
            },
          },
          // Add smart wallet specific fields if applicable
          ...(walletType === "SMART_WALLET" && {
            ownerAddress: metadata.ownerAddress,
            factoryAddress: metadata.factoryAddress,
            implementationAddress: metadata.implementationAddress,
          }),
        },
      });

      logger.info(`Wallet created successfully: ${wallet.id}`);

      // Create an audit log entry
      await db.auditLog.create({
        data: {
          type: AuditLogType.WALLET_CREATED,
          description: `${walletType} wallet created on ${network} ${testMode ? "Testnet" : "Mainnet"}`,
          userId: session.user.id,
          organizationId,
          metadata: {
            walletId: wallet.id,
            walletAddress: wallet.address,
            walletType,
            network,
            testMode,
          },
        },
      });

      // Create a notification for the user
      await db.notification.create({
        data: {
          title: "Wallet Created",
          message: `Your organization wallet has been created successfully on ${network} ${testMode ? "Testnet" : "Mainnet"}.`,
          type: "SYSTEM",
          user: {
            connect: {
              id: session.user.id,
            },
          },
          organization: {
            connect: {
              id: organizationId,
            },
          },
          actionUrl: `/wallet`,
          actionLabel: "View Wallet",
        },
      });

      // Get organization details for email
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
        select: { name: true }
      });

      // Send wallet creation email
      try {
        if (organization) {
          await onboardingEmailService.sendWalletCreatedEmail(
            session.user.email || '',
            session.user.name || 'User',
            organization.name,
            wallet.address,
            network,
            testMode
          );
        }
      } catch (emailError) {
        logger.error("Error sending wallet creation email:", emailError);
        // Continue even if email fails
      }

      return NextResponse.json(
        { wallet, message: "Wallet created successfully" },
        { status: 201 }
      );
    } catch (error) {
      logger.error(`Error creating wallet: ${error instanceof Error ? error.message : 'Unknown error'}`);
      if (error instanceof Error && error.stack) {
        logger.error(`Stack trace: ${error.stack}`);
      }

      return NextResponse.json(
        { error: "Failed to create wallet" },
        { status: 500 }
      );
    }
  } catch (error) {
    logger.error(`Error in wallet creation API: ${error instanceof Error ? error.message : 'Unknown error'}`);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the wallet" },
      { status: 500 }
    );
  }
}

// Export the handler with organization isolation middleware
export const POST = withOrganizationIsolation('organizationId')(postHandler);
