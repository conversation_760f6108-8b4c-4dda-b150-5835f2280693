import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for creating a wallet audit log
const createAuditLogSchema = z.object({
  action: z.string().min(1, "Action is required"),
  details: z.record(z.any()).optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

// GET handler for wallet audit logs
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access wallet audit logs" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 50;
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0;
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const action = searchParams.get("action");

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    // Build filter for audit logs
    const filter: any = {
      walletId,
    };

    if (action) {
      filter.action = action;
    }

    if (startDate || endDate) {
      filter.timestamp = {};
      
      if (startDate) {
        filter.timestamp.gte = new Date(startDate);
      }
      
      if (endDate) {
        filter.timestamp.lte = new Date(endDate);
      }
    }

    // Get audit logs
    const auditLogs = await db.walletAuditLog.findMany({
      where: filter,
      orderBy: {
        timestamp: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get total count
    const totalCount = await db.walletAuditLog.count({
      where: filter,
    });

    return NextResponse.json({
      auditLogs,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + auditLogs.length < totalCount,
      },
    });
  } catch (error) {
    logger.error("Error fetching wallet audit logs:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching wallet audit logs" },
      { status: 500 }
    );
  }
}

// POST handler for creating a wallet audit log
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create a wallet audit log" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { action, details, ipAddress, userAgent } = createAuditLogSchema.parse(body);

    // Create audit log
    const auditLog = await db.walletAuditLog.create({
      data: {
        wallet: {
          connect: {
            id: wallet.id,
          },
        },
        action,
        userId: session.user.id,
        ipAddress,
        userAgent,
        details,
      },
    });

    return NextResponse.json({
      auditLog,
      message: "Audit log created successfully",
    });
  } catch (error) {
    logger.error("Error creating wallet audit log:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the wallet audit log" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
