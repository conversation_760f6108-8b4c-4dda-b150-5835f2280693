/**
 * Wallet audit log model
 */
export interface WalletAuditLog {
  action: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Wallet audit log response model
 */
export interface WalletAuditLogResponse {
  id: string;
  walletId: string;
  action: string;
  userId: string;
  ipAddress?: string;
  userAgent?: string;
  details?: any;
  timestamp: Date;
}

/**
 * Wallet audit logs response
 */
export interface WalletAuditLogsResponse {
  auditLogs: WalletAuditLogResponse[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
