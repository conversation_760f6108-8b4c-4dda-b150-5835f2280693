import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { getPortfolio, getTokenTransfers } from "@/lib/smart-wallet";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

/**
 * Get the portfolio for the authenticated user's wallet
 *
 * @returns The user's portfolio including tokens and NFTs
 */
async function getPortfolioHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access portfolio information",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const { searchParams } = new URL(req.url);
  const walletId = searchParams.get("walletId");
  const includeTransfers = searchParams.get("includeTransfers") === "true";
  const transferLimit = parseInt(searchParams.get("transferLimit") || "20", 10);

  // Get tenant context
  const tenantContext = await getTenantContext(session.user.id);

  // Get user's wallet with tenant isolation
  let wallet;
  if (walletId) {
    // Create base query
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantIsolation(walletQuery, tenantContext);
    wallet = await db.wallet.findFirst(walletQuery);
  } else {
    // Create base query
    let walletQuery = {
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantIsolation(walletQuery, tenantContext);
    wallet = await db.wallet.findFirst(walletQuery);
  }

  if (!wallet) {
    throw new ApiError(
      "Wallet not found",
      ErrorType.NOT_FOUND,
      404
    );
  }

  try {
    // Get portfolio data from blockchain
    const portfolio = await getPortfolio(wallet.address);

    // Optional: Get token transfers
    let transfers = [];
    if (includeTransfers) {
      transfers = await getTokenTransfers(wallet.address, transferLimit);
    }

    // Update wallet balance in database
    await db.wallet.update({
      where: {
        id: wallet.id,
      },
      data: {
        balance: parseFloat(portfolio.nativeBalance.eth),
        lastSyncedAt: new Date(),
      },
    });

    // Update or create token records
    for (const token of portfolio.tokens) {
      await db.token.upsert({
        where: {
          contractAddress_walletId: {
            contractAddress: token.contractAddress,
            walletId: wallet.id,
          },
        },
        update: {
          name: token.name || null,
          symbol: token.symbol || null,
          decimals: token.decimals || null,
          balance: token.formattedBalance,
          lastUpdated: new Date(),
        },
        create: {
          contractAddress: token.contractAddress,
          name: token.name || null,
          symbol: token.symbol || null,
          decimals: token.decimals || null,
          balance: token.formattedBalance,
          wallet: {
            connect: {
              id: wallet.id,
            },
          },
        },
      });
    }

    // Update or create NFT records
    for (const nft of portfolio.nfts) {
      await db.nFT.upsert({
        where: {
          contractAddress_tokenId_walletId: {
            contractAddress: nft.contractAddress,
            tokenId: nft.tokenId,
            walletId: wallet.id,
          },
        },
        update: {
          name: nft.name || null,
          description: nft.description || null,
          tokenType: nft.tokenType || null,
          metadata: nft.media ? { media: nft.media } : null,
          lastUpdated: new Date(),
        },
        create: {
          contractAddress: nft.contractAddress,
          tokenId: nft.tokenId,
          name: nft.name || null,
          description: nft.description || null,
          tokenType: nft.tokenType || null,
          metadata: nft.media ? { media: nft.media } : null,
          wallet: {
            connect: {
              id: wallet.id,
            },
          },
        },
      });
    }

    logger.info(`Retrieved portfolio for user ${session.user.id}, wallet ${wallet.address}`);

    return NextResponse.json({
      wallet: {
        id: wallet.id,
        address: wallet.address,
        isSmartWallet: wallet.isSmartWallet,
        balance: parseFloat(portfolio.nativeBalance.eth),
      },
      portfolio: {
        nativeBalance: portfolio.nativeBalance,
        tokens: portfolio.tokens,
        nfts: portfolio.nfts,
        ...(includeTransfers && { transfers }),
      },
      lastUpdated: new Date(),
    });
  } catch (error) {
    logger.error(`Error retrieving portfolio for user ${session.user.id}:`, error);
    throw new ApiError(
      "Failed to retrieve portfolio",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and resource isolation
const wrappedHandler = withErrorHandling(getPortfolioHandler);

// Use resource isolation for wallet resources
export const GET = withResourceIsolation('wallet', 'walletId')(wrappedHandler);
