import { WalletAccessLevel } from "@prisma/client";

/**
 * Wallet access grant model
 */
export interface WalletAccessGrant {
  userId: string;
  accessLevel: WalletAccessLevel;
  canApprove?: boolean;
  canInitiate?: boolean;
  canView?: boolean;
  customLimits?: any;
  expiresAt?: string;
}

/**
 * Wallet access revoke model
 */
export interface WalletAccessRevoke {
  accessId: string;
}

/**
 * Wallet access update model
 */
export interface WalletAccessUpdate {
  accessId: string;
  accessLevel?: WalletAccessLevel;
  canApprove?: boolean;
  canInitiate?: boolean;
  canView?: boolean;
  customLimits?: any;
  expiresAt?: string;
}

/**
 * Wallet access control response model
 */
export interface WalletAccessControlResponse {
  id: string;
  walletId: string;
  userId: string;
  accessLevel: WalletAccessLevel;
  canApprove: boolean;
  canInitiate: boolean;
  canView: boolean;
  customLimits?: any;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

/**
 * Wallet access controls response
 */
export interface WalletAccessControlsResponse {
  accessControls: WalletAccessControlResponse[];
}
