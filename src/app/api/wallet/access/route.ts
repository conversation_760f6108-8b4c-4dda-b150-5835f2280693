import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { AuditLogType, WalletAccessLevel } from "@prisma/client";

// Schema for granting wallet access
const grantAccessSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  accessLevel: z.enum([
    WalletAccessLevel.ADMIN,
    WalletAccessLevel.MANAGER,
    WalletAccessLevel.APPROVER,
    WalletAccessLevel.VIEWER,
  ]),
  canApprove: z.boolean().optional(),
  canInitiate: z.boolean().optional(),
  canView: z.boolean().default(true),
  customLimits: z.record(z.any()).optional(),
  expiresAt: z.string().optional(), // ISO date string
});

// Schema for revoking wallet access
const revokeAccessSchema = z.object({
  accessId: z.string().min(1, "Access ID is required"),
});

// Schema for updating wallet access
const updateAccessSchema = z.object({
  accessId: z.string().min(1, "Access ID is required"),
  accessLevel: z.enum([
    WalletAccessLevel.ADMIN,
    WalletAccessLevel.MANAGER,
    WalletAccessLevel.APPROVER,
    WalletAccessLevel.VIEWER,
  ]).optional(),
  canApprove: z.boolean().optional(),
  canInitiate: z.boolean().optional(),
  canView: z.boolean().optional(),
  customLimits: z.record(z.any()).optional(),
  expiresAt: z.string().optional(), // ISO date string
});

// GET handler for wallet access controls
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access wallet access controls" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
      include: {
        accessControls: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet with access controls
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      accessControls: wallet.accessControls,
    });
  } catch (error) {
    logger.error("Error fetching wallet access controls:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching wallet access controls" },
      { status: 500 }
    );
  }
}

// POST handler for managing wallet access
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to manage wallet access" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");
    const action = searchParams.get("action") || "grant";

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    const body = await req.json();

    // Process different actions
    if (action === "grant") {
      const validatedData = grantAccessSchema.parse(body);

      // Check if the user exists
      const user = await db.user.findUnique({
        where: { id: validatedData.userId },
      });

      if (!user) {
        return NextResponse.json(
          { error: "User not found" },
          { status: 404 }
        );
      }

      // Check if access already exists
      const existingAccess = await db.walletAccessControl.findUnique({
        where: {
          walletId_userId: {
            walletId: wallet.id,
            userId: validatedData.userId,
          },
        },
      });

      if (existingAccess) {
        return NextResponse.json(
          { error: "User already has access to this wallet" },
          { status: 400 }
        );
      }

      // Create access control
      const accessControl = await db.walletAccessControl.create({
        data: {
          wallet: {
            connect: {
              id: wallet.id,
            },
          },
          userId: validatedData.userId,
          accessLevel: validatedData.accessLevel,
          canApprove: validatedData.canApprove ?? (validatedData.accessLevel === WalletAccessLevel.ADMIN || validatedData.accessLevel === WalletAccessLevel.APPROVER),
          canInitiate: validatedData.canInitiate ?? (validatedData.accessLevel === WalletAccessLevel.ADMIN || validatedData.accessLevel === WalletAccessLevel.MANAGER),
          canView: validatedData.canView,
          customLimits: validatedData.customLimits,
          expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : null,
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: AuditLogType.WALLET_ACCESS_GRANTED,
          description: `Wallet access granted to user ${user.name} (${validatedData.accessLevel})`,
          userId: session.user.id,
          metadata: {
            walletId: wallet.id,
            targetUserId: validatedData.userId,
            accessLevel: validatedData.accessLevel,
          },
        },
      });

      // Create notification for the user
      await db.notification.create({
        data: {
          title: "Wallet Access Granted",
          message: `You have been granted ${validatedData.accessLevel} access to a wallet`,
          type: "WALLET",
          user: {
            connect: {
              id: validatedData.userId,
            },
          },
          actionUrl: `/wallet/${wallet.id}`,
          actionLabel: "View Wallet",
        },
      });

      return NextResponse.json({
        accessControl,
        message: "Access granted successfully",
      });
    } else if (action === "revoke") {
      const validatedData = revokeAccessSchema.parse(body);

      // Check if access exists
      const accessControl = await db.walletAccessControl.findUnique({
        where: {
          id: validatedData.accessId,
          walletId: wallet.id,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!accessControl) {
        return NextResponse.json(
          { error: "Access control not found" },
          { status: 404 }
        );
      }

      // Delete access control
      await db.walletAccessControl.delete({
        where: {
          id: validatedData.accessId,
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: AuditLogType.WALLET_ACCESS_REVOKED,
          description: `Wallet access revoked from user ${accessControl.user.name}`,
          userId: session.user.id,
          metadata: {
            walletId: wallet.id,
            targetUserId: accessControl.userId,
            accessLevel: accessControl.accessLevel,
          },
        },
      });

      // Create notification for the user
      await db.notification.create({
        data: {
          title: "Wallet Access Revoked",
          message: "Your access to a wallet has been revoked",
          type: "WALLET",
          user: {
            connect: {
              id: accessControl.userId,
            },
          },
        },
      });

      return NextResponse.json({
        message: "Access revoked successfully",
      });
    } else if (action === "update") {
      const validatedData = updateAccessSchema.parse(body);

      // Check if access exists
      const accessControl = await db.walletAccessControl.findUnique({
        where: {
          id: validatedData.accessId,
          walletId: wallet.id,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!accessControl) {
        return NextResponse.json(
          { error: "Access control not found" },
          { status: 404 }
        );
      }

      // Update access control
      const updatedAccessControl = await db.walletAccessControl.update({
        where: {
          id: validatedData.accessId,
        },
        data: {
          accessLevel: validatedData.accessLevel,
          canApprove: validatedData.canApprove,
          canInitiate: validatedData.canInitiate,
          canView: validatedData.canView,
          customLimits: validatedData.customLimits,
          expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined,
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: AuditLogType.WALLET_ACCESS_GRANTED,
          description: `Wallet access updated for user ${accessControl.user.name}`,
          userId: session.user.id,
          metadata: {
            walletId: wallet.id,
            targetUserId: accessControl.userId,
            accessLevel: validatedData.accessLevel || accessControl.accessLevel,
          },
        },
      });

      // Create notification for the user
      await db.notification.create({
        data: {
          title: "Wallet Access Updated",
          message: "Your access to a wallet has been updated",
          type: "WALLET",
          user: {
            connect: {
              id: accessControl.userId,
            },
          },
          actionUrl: `/wallet/${wallet.id}`,
          actionLabel: "View Wallet",
        },
      });

      return NextResponse.json({
        accessControl: updatedAccessControl,
        message: "Access updated successfully",
      });
    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Error managing wallet access:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while managing wallet access" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
