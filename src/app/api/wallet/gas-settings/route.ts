import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { AuditLogType } from "@prisma/client";

// Schema for updating gas settings
const gasSettingsSchema = z.object({
  defaultGasPrice: z.number().min(0).optional(),
  maxGasPrice: z.number().min(0).optional(),
  defaultMaxPriorityFee: z.number().min(0).optional(),
  defaultMaxFeePerGas: z.number().min(0).optional(),
  gasLimitMultiplier: z.number().min(1).optional(),
  optimizationEnabled: z.boolean().optional(),
  alertThreshold: z.number().min(0).optional(),
  alertEnabled: z.boolean().optional(),
});

// GET handler for gas settings
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access gas settings" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
      include: {
        gasSettings: true,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet with gas settings
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      gasSettings: wallet.gasSettings || {
        defaultGasPrice: null,
        maxGasPrice: null,
        defaultMaxPriorityFee: null,
        defaultMaxFeePerGas: null,
        gasLimitMultiplier: 1.1,
        optimizationEnabled: true,
        alertThreshold: null,
        alertEnabled: false,
      },
    });
  } catch (error) {
    logger.error("Error fetching gas settings:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching gas settings" },
      { status: 500 }
    );
  }
}

// POST handler for updating gas settings
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update gas settings" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");

    if (!walletId) {
      return NextResponse.json(
        { error: "Wallet ID is required" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: walletId,
        userId: session.user.id,
      },
      include: {
        gasSettings: true,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get wallet
    const wallet = await db.wallet.findFirst(walletQuery);

    if (!wallet) {
      return NextResponse.json(
        { error: "Wallet not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const validatedData = gasSettingsSchema.parse(body);

    // Create or update gas settings
    const gasSettings = await db.gasSetting.upsert({
      where: {
        walletId: wallet.id,
      },
      create: {
        ...validatedData,
        wallet: {
          connect: {
            id: wallet.id,
          },
        },
      },
      update: validatedData,
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.GAS_SETTINGS_UPDATED,
        description: "Gas settings updated",
        userId: session.user.id,
        metadata: {
          walletId: wallet.id,
          settings: validatedData,
        },
      },
    });

    return NextResponse.json({
      gasSettings,
      message: "Gas settings updated successfully",
    });
  } catch (error) {
    logger.error("Error updating gas settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating gas settings" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
