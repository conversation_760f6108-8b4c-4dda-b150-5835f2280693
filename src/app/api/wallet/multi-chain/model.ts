import { SupportedNetwork } from "@/lib/blockchain-config";

/**
 * Create wallet request model
 */
export interface CreateWalletRequest {
  network: SupportedNetwork;
  isTestnet?: boolean;
  name?: string;
  isSmartWallet?: boolean;
}

/**
 * Wallet response model
 */
export interface WalletResponse {
  id: string;
  address: string;
  name?: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
  isSmartWallet: boolean;
  balance: number;
  lastSyncedAt?: Date;
}

/**
 * Network summary model
 */
export interface NetworkSummary {
  network: string;
  totalBalance: number;
  walletCount: number;
  testnetCount: number;
  mainnetCount: number;
  wallets: WalletResponse[];
}

/**
 * Multi-chain wallets response
 */
export interface MultiChainWalletsResponse {
  networks: NetworkSummary[];
  totalWallets: number;
}
