import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { createWallet, getWalletBalance } from "@/lib/blockchain";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";

// Schema for creating a new wallet
const createWalletSchema = z.object({
  network: z.enum([
    SupportedNetwork.ETHEREUM,
    SupportedNetwork.POLYGON,
    SupportedNetwork.ARBITRUM,
    SupportedNetwork.OPTIMISM,
    SupportedNetwork.BASE,
  ]),
  isTestnet: z.boolean().default(true),
  name: z.string().optional(),
  isSmartWallet: z.boolean().default(false),
});

// GET handler for multi-chain wallets
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access wallet information" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const network = searchParams.get("network");
    const includeTestnets = searchParams.get("includeTestnets") === "true";

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletsQuery = {
      where: {
        userId: session.user.id,
        ...(network ? { network } : {}),
        ...(includeTestnets ? {} : { isTestnet: false }),
      },
      include: {
        transactions: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
        tokens: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    };

    // Apply tenant isolation
    walletsQuery = withTenantQuery(walletsQuery, tenantContext);

    // Get user's wallets
    const wallets = await db.wallet.findMany(walletsQuery);

    // Group wallets by network
    const walletsByNetwork = wallets.reduce((acc: any, wallet) => {
      if (!acc[wallet.network]) {
        acc[wallet.network] = [];
      }
      acc[wallet.network].push(wallet);
      return acc;
    }, {});

    // Get network summaries
    const networkSummaries = Object.entries(walletsByNetwork).map(([network, networkWallets]: [string, any[]]) => {
      const totalBalance = networkWallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      const walletCount = networkWallets.length;
      const testnetCount = networkWallets.filter(wallet => wallet.isTestnet).length;
      const mainnetCount = walletCount - testnetCount;

      return {
        network,
        totalBalance,
        walletCount,
        testnetCount,
        mainnetCount,
        wallets: networkWallets.map(wallet => ({
          id: wallet.id,
          address: wallet.address,
          name: wallet.name,
          balance: wallet.balance,
          isTestnet: wallet.isTestnet,
          isSmartWallet: wallet.isSmartWallet,
          chainId: wallet.chainId,
          lastSyncedAt: wallet.lastSyncedAt,
        })),
      };
    });

    return NextResponse.json({
      networks: networkSummaries,
      totalWallets: wallets.length,
    });
  } catch (error) {
    logger.error("Error fetching multi-chain wallets:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching wallet information" },
      { status: 500 }
    );
  }
}

// POST handler for creating a new wallet
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create a wallet" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { network, isTestnet, name, isSmartWallet } = createWalletSchema.parse(body);

    // Get network configuration
    const networkConfig = getNetworkConfig(network, isTestnet);

    // Create a new wallet using the blockchain integration
    const { address, encryptedKey } = await createWallet();

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create wallet in database
    const wallet = await db.wallet.create({
      data: {
        name,
        address,
        encryptedKey,
        network,
        chainId: networkConfig.chainId,
        isTestnet,
        isSmartWallet,
        balance: 0,
        lastSyncedAt: new Date(),
        user: {
          connect: {
            id: session.user.id,
          },
        },
        ...(session.user.organizationId ? {
          organization: {
            connect: {
              id: session.user.organizationId,
            },
          },
        } : {}),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "WALLET_CREATED",
        description: `Created new ${network} ${isTestnet ? 'testnet' : 'mainnet'} wallet`,
        userId: session.user.id,
        organizationId: session.user.organizationId,
        metadata: {
          walletId: wallet.id,
          network,
          isTestnet,
          isSmartWallet,
        },
      },
    });

    // Create notification
    await db.notification.create({
      data: {
        title: "Wallet Created",
        message: `Your new ${network} ${isTestnet ? 'testnet' : 'mainnet'} wallet has been created.`,
        type: "WALLET",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        actionUrl: `/wallet/${wallet.id}`,
        actionLabel: "View Wallet",
      },
    });

    logger.info(`Created new ${network} wallet for user ${session.user.id}`);

    return NextResponse.json({
      wallet: {
        id: wallet.id,
        address: wallet.address,
        name: wallet.name,
        network: wallet.network,
        chainId: wallet.chainId,
        isTestnet: wallet.isTestnet,
        isSmartWallet: wallet.isSmartWallet,
        balance: wallet.balance,
      },
      message: "Wallet created successfully",
    });
  } catch (error) {
    logger.error("Error creating wallet:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the wallet" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
