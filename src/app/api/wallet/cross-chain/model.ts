import { BridgeStatus } from "@prisma/client";
import { SupportedNetwork } from "@/lib/blockchain-config";

/**
 * Bridge transaction request model
 */
export interface BridgeTransactionRequest {
  sourceWalletId: string;
  destinationAddress: string;
  destinationNetwork: SupportedNetwork;
  destinationChainId: number;
  tokenAddress?: string;
  tokenSymbol?: string;
  amount: string;
  bridgeProvider: string;
}

/**
 * Bridge transaction response model
 */
export interface BridgeTransactionResponse {
  id: string;
  sourceWalletId: string;
  sourceNetwork: string;
  sourceChainId: number;
  destinationAddress: string;
  destinationNetwork: string;
  destinationChainId: number;
  tokenAddress?: string;
  tokenSymbol?: string;
  amount: string;
  status: BridgeStatus;
  bridgeProvider: string;
  sourceTxHash?: string;
  destinationTxHash?: string;
  estimatedTimeMinutes: number;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
  sourceWallet: {
    id: string;
    address: string;
    network: string;
    chainId: number;
  };
}

/**
 * Bridge transactions response
 */
export interface BridgeTransactionsResponse {
  bridgeTransactions: BridgeTransactionResponse[];
  count: number;
}
