import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";
import { BridgeStatus } from "@prisma/client";

// Schema for initiating a bridge transaction
const bridgeTransactionSchema = z.object({
  sourceWalletId: z.string().min(1, "Source wallet ID is required"),
  destinationAddress: z.string().min(1, "Destination address is required"),
  destinationNetwork: z.enum([
    SupportedNetwork.ETHEREUM,
    SupportedNetwork.POLYGON,
    SupportedNetwork.ARBITRUM,
    SupportedNetwork.OPTIMISM,
    SupportedNetwork.BASE,
  ]),
  destinationChainId: z.number().int().positive(),
  tokenAddress: z.string().optional(), // Optional for native token transfers
  tokenSymbol: z.string().optional(),
  amount: z.string().min(1, "Amount is required"),
  bridgeProvider: z.string().min(1, "Bridge provider is required"),
});

// GET handler for bridge transactions
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access bridge transactions" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const walletId = searchParams.get("walletId");
    const status = searchParams.get("status");
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 10;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let bridgeQuery: any = {
      where: {
        sourceWallet: {
          userId: session.user.id,
        },
        ...(walletId ? { sourceWalletId: walletId } : {}),
        ...(status ? { status } : {}),
      },
      include: {
        sourceWallet: {
          select: {
            id: true,
            address: true,
            network: true,
            chainId: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    };

    // Apply tenant isolation
    bridgeQuery = withTenantQuery(bridgeQuery, tenantContext);

    // Get bridge transactions
    const bridgeTransactions = await db.bridgeTransaction.findMany(bridgeQuery);

    return NextResponse.json({
      bridgeTransactions,
      count: bridgeTransactions.length,
    });
  } catch (error) {
    logger.error("Error fetching bridge transactions:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching bridge transactions" },
      { status: 500 }
    );
  }
}

// POST handler for initiating a bridge transaction
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to initiate a bridge transaction" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const {
      sourceWalletId,
      destinationAddress,
      destinationNetwork,
      destinationChainId,
      tokenAddress,
      tokenSymbol,
      amount,
      bridgeProvider,
    } = bridgeTransactionSchema.parse(body);

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        id: sourceWalletId,
        userId: session.user.id,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get source wallet
    const sourceWallet = await db.wallet.findFirst(walletQuery);

    if (!sourceWallet) {
      return NextResponse.json(
        { error: "Source wallet not found" },
        { status: 404 }
      );
    }

    // Check if the wallet has sufficient balance
    if (!tokenAddress && parseFloat(amount) > sourceWallet.balance) {
      return NextResponse.json(
        { error: "Insufficient balance" },
        { status: 400 }
      );
    }

    // TODO: In a real implementation, we would check token balances for ERC-20 tokens

    // Create bridge transaction
    const bridgeTransaction = await db.bridgeTransaction.create({
      data: {
        sourceWallet: {
          connect: {
            id: sourceWalletId,
          },
        },
        sourceNetwork: sourceWallet.network,
        sourceChainId: sourceWallet.chainId,
        destinationAddress,
        destinationNetwork,
        destinationChainId,
        tokenAddress,
        tokenSymbol,
        amount,
        status: BridgeStatus.PENDING,
        bridgeProvider,
        estimatedTimeMinutes: getBridgeEstimatedTime(sourceWallet.network, destinationNetwork, bridgeProvider),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "BRIDGE_TRANSACTION_INITIATED",
        description: `Initiated bridge transaction from ${sourceWallet.network} to ${destinationNetwork}`,
        userId: session.user.id,
        metadata: {
          bridgeTransactionId: bridgeTransaction.id,
          sourceWalletId,
          destinationNetwork,
          amount,
          bridgeProvider,
        },
      },
    });

    // Create notification
    await db.notification.create({
      data: {
        title: "Bridge Transaction Initiated",
        message: `Your bridge transaction of ${amount} ${tokenSymbol || 'ETH'} from ${sourceWallet.network} to ${destinationNetwork} has been initiated.`,
        type: "TRANSACTION",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        actionUrl: `/wallet/cross-chain/status?id=${bridgeTransaction.id}`,
        actionLabel: "View Status",
      },
    });

    // In a real implementation, we would initiate the actual bridge transaction here
    // For now, we'll simulate the process by updating the status after a delay
    simulateBridgeTransaction(bridgeTransaction.id, session.user.id);

    logger.info(`Initiated bridge transaction ${bridgeTransaction.id} for user ${session.user.id}`);

    return NextResponse.json({
      bridgeTransaction,
      message: "Bridge transaction initiated successfully",
    });
  } catch (error) {
    logger.error("Error initiating bridge transaction:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while initiating the bridge transaction" },
      { status: 500 }
    );
  }
}

// Helper function to get estimated time for bridge transaction
function getBridgeEstimatedTime(sourceNetwork: string, destinationNetwork: string, bridgeProvider: string): number {
  // These are example values - in a real implementation, these would be based on actual bridge providers
  const estimatedTimes: Record<string, Record<string, number>> = {
    "Connext": {
      "default": 15,
      "ethereum_polygon": 10,
      "polygon_ethereum": 20,
      "ethereum_arbitrum": 5,
      "arbitrum_ethereum": 15,
    },
    "Hop": {
      "default": 20,
      "ethereum_polygon": 15,
      "polygon_ethereum": 25,
      "ethereum_optimism": 10,
      "optimism_ethereum": 20,
    },
    "Across": {
      "default": 25,
      "ethereum_polygon": 20,
      "polygon_ethereum": 30,
      "ethereum_base": 15,
      "base_ethereum": 25,
    },
  };

  const networkPair = `${sourceNetwork.toLowerCase()}_${destinationNetwork.toLowerCase()}`;
  const providerTimes = estimatedTimes[bridgeProvider] || { default: 30 };
  
  return providerTimes[networkPair] || providerTimes.default;
}

// Helper function to simulate bridge transaction process
async function simulateBridgeTransaction(bridgeTransactionId: string, userId: string) {
  try {
    // Simulate transaction initiation (after 5 seconds)
    setTimeout(async () => {
      await db.bridgeTransaction.update({
        where: { id: bridgeTransactionId },
        data: {
          status: BridgeStatus.INITIATED,
          sourceTxHash: `0x${Math.random().toString(16).substring(2, 66)}`,
        },
      });

      // Create notification
      await db.notification.create({
        data: {
          title: "Bridge Transaction Started",
          message: "Your bridge transaction has been initiated on the source network.",
          type: "TRANSACTION",
          userId,
          actionUrl: `/wallet/cross-chain/status?id=${bridgeTransactionId}`,
          actionLabel: "View Status",
        },
      });

      // Simulate in-progress status (after 15 seconds)
      setTimeout(async () => {
        await db.bridgeTransaction.update({
          where: { id: bridgeTransactionId },
          data: {
            status: BridgeStatus.IN_PROGRESS,
          },
        });

        // Simulate completion (after 30 seconds)
        setTimeout(async () => {
          // 90% chance of success, 10% chance of failure
          const isSuccessful = Math.random() < 0.9;

          if (isSuccessful) {
            await db.bridgeTransaction.update({
              where: { id: bridgeTransactionId },
              data: {
                status: BridgeStatus.COMPLETED,
                destinationTxHash: `0x${Math.random().toString(16).substring(2, 66)}`,
              },
            });

            // Create notification
            await db.notification.create({
              data: {
                title: "Bridge Transaction Completed",
                message: "Your bridge transaction has been completed successfully.",
                type: "TRANSACTION",
                userId,
                actionUrl: `/wallet/cross-chain/status?id=${bridgeTransactionId}`,
                actionLabel: "View Details",
              },
            });

            // Create audit log
            await db.auditLog.create({
              data: {
                type: "BRIDGE_TRANSACTION_COMPLETED",
                description: "Bridge transaction completed successfully",
                userId,
                metadata: {
                  bridgeTransactionId,
                },
              },
            });
          } else {
            await db.bridgeTransaction.update({
              where: { id: bridgeTransactionId },
              data: {
                status: BridgeStatus.FAILED,
                errorMessage: "Transaction failed on destination network",
              },
            });

            // Create notification
            await db.notification.create({
              data: {
                title: "Bridge Transaction Failed",
                message: "Your bridge transaction has failed. Please check the details.",
                type: "TRANSACTION",
                priority: "HIGH",
                userId,
                actionUrl: `/wallet/cross-chain/status?id=${bridgeTransactionId}`,
                actionLabel: "View Details",
              },
            });

            // Create audit log
            await db.auditLog.create({
              data: {
                type: "BRIDGE_TRANSACTION_FAILED",
                description: "Bridge transaction failed",
                userId,
                metadata: {
                  bridgeTransactionId,
                  errorMessage: "Transaction failed on destination network",
                },
              },
            });
          }
        }, 30000);
      }, 15000);
    }, 5000);
  } catch (error) {
    logger.error(`Error simulating bridge transaction ${bridgeTransactionId}:`, error);
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
