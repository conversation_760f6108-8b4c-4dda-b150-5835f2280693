import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { createWallet, fundWalletWithTestTokens } from "@/lib/blockchain/wallet-manager";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for creating a wallet
const createWalletSchema = z.object({
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]).default("ethereum"),
  useTestnet: z.boolean().default(true),
  isSmartWallet: z.boolean().default(true),
  organizationId: z.string().optional(),
  securityLevel: z.enum(["standard", "high", "custom"]).optional().default("standard"),
});

/**
 * Create a new wallet for the authenticated user
 *
 * @returns The created wallet
 */
async function createWalletHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to create a wallet",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Parse and validate request body
  const body = await req.json().catch(() => ({}));
  const { network, useTestnet, isSmartWallet, organizationId, securityLevel } = createWalletSchema.parse(body);

  logger.info(`Creating ${isSmartWallet ? "smart" : "standard"} wallet with ${securityLevel} security for user ${session.user.id}`);

  // Check if user already has a wallet on this network
  const existingWallet = await db.wallet.findFirst({
    where: {
      userId: session.user.id,
      network: network,
      isTestnet: useTestnet,
    },
  });

  if (existingWallet) {
    throw new ApiError(
      `You already have a wallet on ${network} ${useTestnet ? "testnet" : "mainnet"}`,
      ErrorType.CONFLICT,
      409
    );
  }

  try {
    // Determine if this is an organization wallet
    let targetOrganizationId = organizationId;

    // If no organization ID was provided but the user belongs to an organization,
    // check if they have permission to create an organization wallet
    if (!targetOrganizationId && session.user.organizationId) {
      const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" || session.user.role === "ADMIN";

      if (isOrgAdmin) {
        targetOrganizationId = session.user.organizationId;
      }
    }

    // Create the wallet
    const wallet = await createWallet(
      session.user.id,
      targetOrganizationId || null,
      isSmartWallet,
      network as SupportedNetwork,
      useTestnet,
      securityLevel
    );

    // If using testnet, fund the wallet with test tokens
    let fundingTxHash: string | undefined;
    if (useTestnet) {
      try {
        fundingTxHash = await fundWalletWithTestTokens(wallet.id);
      } catch (fundingError) {
        logger.error(`Error funding wallet with test tokens:`, fundingError);
        // Don't fail the request if funding fails
      }
    }

    // Create a notification
    await db.notification.create({
      data: {
        title: `${isSmartWallet ? "Smart" : "Standard"} Wallet Created`,
        message: `Your ${isSmartWallet ? "smart" : "standard"} wallet has been created successfully. Address: ${wallet.address}`,
        type: "SYSTEM",
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });

    // Create an audit log
    await db.auditLog.create({
      data: {
        type: "WALLET_CREATED",
        description: `${isSmartWallet ? "Smart" : "Standard"} wallet created on ${network} ${useTestnet ? "testnet" : "mainnet"}`,
        userId: session.user.id,
        organizationId: targetOrganizationId || undefined,
        metadata: {
          walletId: wallet.id,
          walletAddress: wallet.address,
          network,
          isTestnet,
          isSmartWallet,
        },
      },
    });

    logger.info(`${isSmartWallet ? "Smart" : "Standard"} wallet created for user ${session.user.id}: ${wallet.address}`);

    return NextResponse.json({
      wallet: {
        id: wallet.id,
        address: wallet.address,
        ownerAddress: wallet.ownerAddress,
        isSmartWallet: wallet.isSmartWallet,
        network: wallet.network,
        chainId: wallet.chainId,
        isTestnet: wallet.isTestnet,
        balance: wallet.balance,
        createdAt: wallet.createdAt,
      },
      fundingTxHash,
      message: `${isSmartWallet ? "Smart" : "Standard"} wallet created successfully`,
    }, { status: 201 });
  } catch (error) {
    logger.error(`Error creating wallet for user ${session.user.id}:`, error);
    throw new ApiError(
      "Failed to create wallet",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * Get the authenticated user's wallets
 *
 * @returns The user's wallets
 */
async function getWalletsHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access wallet information",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Get URL parameters
  const url = new URL(req.url);
  const networkParam = url.searchParams.get("network");
  const testnetParam = url.searchParams.get("testnet");
  const organizationParam = url.searchParams.get("organization");
  const smartWalletParam = url.searchParams.get("smartWallet");

  // Build query
  const query: any = {};

  // Filter by user or organization
  if (organizationParam === "true" && session.user.organizationId) {
    // Check if user has permission to view organization wallets
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" || session.user.role === "ADMIN";
    if (!isOrgAdmin) {
      throw new ApiError(
        "You do not have permission to view organization wallets",
        ErrorType.FORBIDDEN,
        403
      );
    }
    query.organizationId = session.user.organizationId;
  } else {
    query.userId = session.user.id;
  }

  // Filter by network
  if (networkParam) {
    query.network = networkParam;
  }

  // Filter by testnet
  if (testnetParam !== null) {
    query.isTestnet = testnetParam === "true";
  }

  // Filter by smart wallet
  if (smartWalletParam !== null) {
    query.isSmartWallet = smartWalletParam === "true";
  }

  // Get tenant context
  const tenantContext = await getTenantContext(session.user.id);

  // Create base query with tenant isolation
  let walletsQuery = {
    where: query,
    orderBy: {
      createdAt: "desc",
    },
    include: {
      tokens: true,
      nfts: true,
    },
  };

  // Apply tenant isolation if not an admin
  if (!tenantContext.isAdmin) {
    walletsQuery = withTenantQuery(walletsQuery, tenantContext);
  }

  // Get wallets
  const wallets = await db.wallet.findMany(walletsQuery);

  logger.info(`Retrieved ${wallets.length} wallets for user ${session.user.id}`);

  return NextResponse.json({ wallets });
}

// Wrap handlers with error handling and tenant isolation
const wrappedGetHandler = withErrorHandling(getWalletsHandler);
const wrappedPostHandler = withErrorHandling(createWalletHandler);

export const GET = withTenantIsolation(wrappedGetHandler);
export const POST = withTenantIsolation(wrappedPostHandler);
