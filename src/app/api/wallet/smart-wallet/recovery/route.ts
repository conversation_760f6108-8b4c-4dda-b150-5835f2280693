import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";
import { 
  initiateRecovery, 
  approveRecovery, 
  executeRecovery 
} from "@/lib/blockchain/smart-wallet";

// Schema for initiating recovery
const initiateRecoverySchema = z.object({
  walletId: z.string(),
  recoveryInitiator: z.string(),
  description: z.string().optional(),
});

// Schema for approving recovery
const approveRecoverySchema = z.object({
  recoveryId: z.string(),
  guardianAddress: z.string(),
  notes: z.string().optional(),
});

// Schema for executing recovery
const executeRecoverySchema = z.object({
  recoveryId: z.string(),
  newOwnerAddress: z.string(),
});

/**
 * POST /api/wallet/smart-wallet/recovery
 * Initiate, approve, or execute a wallet recovery
 */
async function handleRecovery(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to manage wallet recovery",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Parse and validate request body
  const body = await req.json().catch(() => ({}));
  const action = req.nextUrl.searchParams.get("action") || "initiate";

  try {
    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    switch (action) {
      case "initiate": {
        const { walletId, recoveryInitiator, description } = initiateRecoverySchema.parse(body);

        // Check if wallet exists and belongs to the user's organization
        const wallet = await db.wallet.findUnique({
          where: { id: walletId },
          include: {
            user: true,
            organization: true,
          },
        });

        if (!wallet) {
          throw new ApiError("Wallet not found", ErrorType.NOT_FOUND, 404);
        }

        // Check if user has permission to initiate recovery
        const isOwner = wallet.userId === session.user.id;
        const isSameOrg = wallet.organizationId === tenantContext.organizationId;
        const isAdmin = tenantContext.isAdmin || session.user.role === "ORGANIZATION_ADMIN";

        if (!isOwner && !(isSameOrg && isAdmin)) {
          throw new ApiError(
            "You do not have permission to initiate recovery for this wallet",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Initiate recovery
        const recovery = await initiateRecovery(walletId, recoveryInitiator);

        // Update description if provided
        if (description) {
          await db.walletRecovery.update({
            where: { id: recovery.id },
            data: { description },
          });
        }

        logger.info(`Recovery initiated for wallet ${walletId} by user ${session.user.id}`);

        return NextResponse.json({
          success: true,
          recovery: {
            id: recovery.id,
            status: recovery.status,
            initiator: recovery.initiator,
            timelock: recovery.timelock,
            createdAt: recovery.createdAt,
          },
          message: "Recovery initiated successfully",
        });
      }

      case "approve": {
        const { recoveryId, guardianAddress, notes } = approveRecoverySchema.parse(body);

        // Check if recovery exists
        const recovery = await db.walletRecovery.findUnique({
          where: { id: recoveryId },
          include: {
            wallet: {
              include: {
                user: true,
                organization: true,
              },
            },
          },
        });

        if (!recovery) {
          throw new ApiError("Recovery request not found", ErrorType.NOT_FOUND, 404);
        }

        // Check if user has permission to approve recovery
        const isOwner = recovery.wallet.userId === session.user.id;
        const isSameOrg = recovery.wallet.organizationId === tenantContext.organizationId;
        const isAdmin = tenantContext.isAdmin || session.user.role === "ORGANIZATION_ADMIN";

        if (!isOwner && !(isSameOrg && isAdmin)) {
          throw new ApiError(
            "You do not have permission to approve recovery for this wallet",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Approve recovery
        const result = await approveRecovery(recoveryId, guardianAddress);

        // Add notes if provided
        if (notes && result.isComplete) {
          await db.walletRecoveryApproval.updateMany({
            where: { 
              recoveryId,
              guardian: {
                address: guardianAddress
              }
            },
            data: { notes },
          });
        }

        logger.info(`Recovery ${recoveryId} approved by guardian ${guardianAddress}`);

        return NextResponse.json({
          success: true,
          result,
          message: result.isComplete 
            ? "Recovery approved and ready for execution" 
            : `Recovery approved (${result.approvals}/${result.requiredApprovals} approvals)`,
        });
      }

      case "execute": {
        const { recoveryId, newOwnerAddress } = executeRecoverySchema.parse(body);

        // Check if recovery exists
        const recovery = await db.walletRecovery.findUnique({
          where: { id: recoveryId },
          include: {
            wallet: {
              include: {
                user: true,
                organization: true,
              },
            },
          },
        });

        if (!recovery) {
          throw new ApiError("Recovery request not found", ErrorType.NOT_FOUND, 404);
        }

        // Check if user has permission to execute recovery
        const isOwner = recovery.wallet.userId === session.user.id;
        const isSameOrg = recovery.wallet.organizationId === tenantContext.organizationId;
        const isAdmin = tenantContext.isAdmin || session.user.role === "ORGANIZATION_ADMIN";

        if (!isOwner && !(isSameOrg && isAdmin)) {
          throw new ApiError(
            "You do not have permission to execute recovery for this wallet",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Execute recovery
        const updatedWallet = await executeRecovery(recoveryId, newOwnerAddress);

        logger.info(`Recovery ${recoveryId} executed with new owner ${newOwnerAddress}`);

        return NextResponse.json({
          success: true,
          wallet: {
            id: updatedWallet.id,
            address: updatedWallet.address,
            ownerAddress: updatedWallet.ownerAddress,
          },
          message: "Recovery executed successfully",
        });
      }

      default:
        throw new ApiError(`Invalid action: ${action}`, ErrorType.VALIDATION, 400);
    }
  } catch (error) {
    logger.error(`Error handling wallet recovery (${action}):`, error);
    throw error;
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(handleRecovery);

export const POST = withTenantIsolation(wrappedHandler);
