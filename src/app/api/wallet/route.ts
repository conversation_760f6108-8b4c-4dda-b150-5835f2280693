import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { createWallet, getWalletBalance, sendTransaction, getTransactionHistory } from "@/lib/blockchain";
import { validateRequest } from "@/lib/validation/utils";
import { amountSchema } from "@/lib/validation/schemas";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

const depositSchema = z.object({
  amount: amountSchema,
});

const withdrawSchema = z.object({
  amount: amountSchema,
});

const transactionHistorySchema = z.object({
  limit: z.number().int().positive().default(10).optional(),
});

// Original GET handler
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access wallet information" },
        { status: 401 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        userId: session.user.id,
      },
      include: {
        transactions: {
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get user's wallet
    let wallet = await db.wallet.findFirst(walletQuery);

    // If wallet doesn't exist, create one
    if (!wallet) {
      logger.info(`Creating new wallet for user ${session.user.id}`);

      // Create a new wallet using the blockchain integration
      const { address, encryptedKey } = await createWallet();

      wallet = await db.wallet.create({
        data: {
          address,
          encryptedKey, // Store the encrypted private key
          balance: 0,
          network: "ethereum", // Default to Ethereum network
          chainId: 11155111, // Default to Sepolia testnet
          isTestnet: true, // Default to testnet
          user: {
            connect: {
              id: session.user.id,
            },
          },
        },
        include: {
          transactions: {
            orderBy: {
              createdAt: "desc",
            },
            take: 10,
          },
        },
      });
    }

    return NextResponse.json({ wallet });
  } catch (error) {
    console.error("Error fetching wallet:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching wallet information" },
      { status: 500 }
    );
  }
}

// Original POST handler
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to perform wallet operations" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const operation = searchParams.get("operation");

    if (!operation || !["deposit", "withdraw"].includes(operation)) {
      return NextResponse.json(
        { error: "Invalid operation" },
        { status: 400 }
      );
    }

    const body = await req.json();

    // Validate the request body
    let validationResult;
    if (operation === "deposit") {
      validationResult = validateRequest(depositSchema, body);
    } else {
      validationResult = validateRequest(withdrawSchema, body);
    }

    if (validationResult.error) {
      return NextResponse.json(
        { error: validationResult.error },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let walletQuery = {
      where: {
        userId: session.user.id,
      },
    };

    // Apply tenant isolation
    walletQuery = withTenantQuery(walletQuery, tenantContext);

    // Get user's wallet
    let wallet = await db.wallet.findFirst(walletQuery);

    // If wallet doesn't exist, create one
    if (!wallet) {
      logger.info(`Creating new wallet for user ${session.user.id}`);

      // Create a new wallet using the blockchain integration
      const { address, encryptedKey } = await createWallet();

      wallet = await db.wallet.create({
        data: {
          address,
          encryptedKey,
          balance: 0,
          network: "ethereum", // Default to Ethereum network
          chainId: 11155111, // Default to Sepolia testnet
          isTestnet: true, // Default to testnet
          lastSyncedAt: new Date(),
          user: {
            connect: {
              id: session.user.id,
            },
          },
        },
      });
    }

    // Sync wallet balance with blockchain
    try {
      const blockchainBalance = await getWalletBalance(wallet.address);

      // Update wallet balance if it's different from the blockchain
      if (parseFloat(blockchainBalance.eth) !== wallet.balance) {
        wallet = await db.wallet.update({
          where: { id: wallet.id },
          data: {
            balance: parseFloat(blockchainBalance.eth),
            lastSyncedAt: new Date(),
          },
        });

        logger.info(`Updated wallet balance for user ${session.user.id} to ${wallet.balance} ETH`);
      }
    } catch (error) {
      logger.error(`Failed to sync wallet balance for user ${session.user.id}:`, error);
      // Continue with the operation even if balance sync fails
    }

    if (operation === "deposit") {
      const { amount } = validationResult.data;
      const amountValue = parseFloat(amount);

      logger.info(`Processing deposit of ${amountValue} ETH for user ${session.user.id}`);

      // In a real application, this would involve a payment processor or blockchain transaction
      // For this example, we'll simulate a deposit by creating a transaction record

      // Create deposit transaction with PENDING status
      const transaction = await db.transaction.create({
        data: {
          amount: amountValue,
          fee: 0,
          type: "DEPOSIT",
          status: "PENDING", // Start as pending until confirmed
          wallet: {
            connect: {
              id: wallet.id,
            },
          },
        },
      });

      try {
        // Simulate blockchain confirmation
        // In a real application, this would be a real blockchain transaction
        // that would be monitored for confirmation

        // Update transaction status to COMPLETED
        await db.transaction.update({
          where: { id: transaction.id },
          data: { status: "COMPLETED" },
        });

        // Update wallet balance
        wallet = await db.wallet.update({
          where: { id: wallet.id },
          data: {
            balance: { increment: amountValue },
            lastSyncedAt: new Date(),
          },
        });

        // Create notification
        await db.notification.create({
          data: {
            title: "Deposit Successful",
            message: `${amountValue.toFixed(6)} ETH has been deposited to your wallet.`,
            type: "TRANSACTION",
            user: { connect: { id: session.user.id } },
          },
        });

        logger.info(`Deposit of ${amountValue} ETH completed for user ${session.user.id}`);

        return NextResponse.json({
          wallet,
          transaction: { ...transaction, status: "COMPLETED" },
          message: "Deposit successful",
        });
      } catch (error) {
        logger.error(`Deposit failed for user ${session.user.id}:`, error);

        // Update transaction status to FAILED
        await db.transaction.update({
          where: { id: transaction.id },
          data: { status: "FAILED" },
        });

        return NextResponse.json(
          { error: "Deposit failed. Please try again." },
          { status: 500 }
        );
      }
    } else if (operation === "withdraw") {
      const { amount } = validationResult.data;
      const amountValue = parseFloat(amount);

      logger.info(`Processing withdrawal of ${amountValue} ETH for user ${session.user.id}`);

      // Check if there's enough balance
      if (wallet.balance < amountValue) {
        logger.warn(`Insufficient balance for withdrawal: ${wallet.balance} < ${amountValue}`);
        return NextResponse.json(
          { error: "Insufficient balance" },
          { status: 400 }
        );
      }

      // Create withdrawal transaction with PENDING status
      const transaction = await db.transaction.create({
        data: {
          amount: amountValue,
          fee: 0,
          type: "WITHDRAWAL",
          status: "PENDING", // Start as pending until confirmed
          wallet: {
            connect: {
              id: wallet.id,
            },
          },
        },
      });

      try {
        // In a real application, this would be a real blockchain transaction
        // using the wallet's private key to send funds to another address
        if (wallet.encryptedKey) {
          // This would be a real transaction in production
          // const result = await sendTransaction(
          //   wallet.encryptedKey,
          //   "0xDestinationAddress", // This would be provided by the user
          //   amount
          // );

          // Simulate a successful transaction
          const txHash = `0x${Math.random().toString(16).substring(2, 66)}`;

          // Update transaction status to COMPLETED
          await db.transaction.update({
            where: { id: transaction.id },
            data: {
              status: "COMPLETED",
              transactionHash: txHash,
            },
          });

          // Update wallet balance
          wallet = await db.wallet.update({
            where: { id: wallet.id },
            data: {
              balance: { decrement: amountValue },
              lastSyncedAt: new Date(),
            },
          });

          // Create notification
          await db.notification.create({
            data: {
              title: "Withdrawal Successful",
              message: `${amountValue.toFixed(6)} ETH has been withdrawn from your wallet.`,
              type: "TRANSACTION",
              user: { connect: { id: session.user.id } },
            },
          });

          logger.info(`Withdrawal of ${amountValue} ETH completed for user ${session.user.id}`);

          return NextResponse.json({
            wallet,
            transaction: { ...transaction, status: "COMPLETED", transactionHash: txHash },
            message: "Withdrawal successful",
          });
        } else {
          throw new Error("Wallet encrypted key not found");
        }
      } catch (error) {
        logger.error(`Withdrawal failed for user ${session.user.id}:`, error);

        // Update transaction status to FAILED
        await db.transaction.update({
          where: { id: transaction.id },
          data: { status: "FAILED" },
        });

        return NextResponse.json(
          { error: "Withdrawal failed. Please try again." },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error("Error performing wallet operation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while performing the wallet operation" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);