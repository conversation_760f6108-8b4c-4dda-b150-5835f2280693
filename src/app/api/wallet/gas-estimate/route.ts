import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";
import { estimateTransactionCost } from "@/lib/blockchain";

// Schema for gas estimation
const gasEstimateSchema = z.object({
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]).default("ethereum"),
  useTestnet: z.boolean().default(true),
  to: z.string().min(1, "Recipient address is required"),
  data: z.string().optional(),
  value: z.string().optional(),
});

/**
 * POST /api/wallet/gas-estimate
 * Estimate gas cost for a transaction
 */
async function estimateGasHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to estimate gas costs",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json().catch(() => ({}));
    const { network, useTestnet, to, data, value } = gasEstimateSchema.parse(body);

    // Estimate gas cost
    const gasEstimate = await estimateTransactionCost(
      network as SupportedNetwork,
      useTestnet,
      {
        to,
        data: data || "0x",
        value,
      }
    );

    logger.info(`Gas estimation for user ${session.user.id} on ${network}: ${gasEstimate.costInEther} ETH`);

    return NextResponse.json({
      gasEstimate,
      message: "Gas cost estimated successfully",
    });
  } catch (error) {
    logger.error(`Error estimating gas for user ${session.user.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "Failed to estimate gas cost",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(estimateGasHandler);
