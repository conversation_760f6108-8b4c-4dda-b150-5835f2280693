import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { sendTokens, getGasPrices } from "@/lib/smart-wallet";
import { validateRequest } from "@/lib/validation/utils";
import { ethereumAddressSchema, amountSchema } from "@/lib/validation/schemas";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for sending tokens
const sendTokensSchema = z.object({
  walletId: z.string().min(1, "Wallet ID is required"),
  to: ethereumAddressSchema,
  tokenAddress: z.string().min(1, "Token address is required"),
  amount: amountSchema,
  gasOption: z.enum(["slow", "average", "fast"]).optional(),
});

/**
 * Send tokens from the user's wallet
 *
 * @returns Transaction details
 */
async function sendTokensHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to send tokens",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Validate request body
  const body = await req.json();
  const validationResult = validateRequest(sendTokensSchema, body);

  if (validationResult.error) {
    throw new ApiError(
      validationResult.error,
      ErrorType.VALIDATION,
      400
    );
  }

  const { walletId, to, tokenAddress, amount, gasOption } = validationResult.data;
  const amountValue = parseFloat(amount);

  // Get tenant context
  const tenantContext = await getTenantContext(session.user.id);

  // Create base query with tenant isolation
  let walletQuery = {
    where: {
      id: walletId,
      userId: session.user.id,
    },
  };

  // Apply tenant isolation
  walletQuery = withTenantIsolation(walletQuery, tenantContext);

  // Get user's wallet
  const wallet = await db.wallet.findFirst(walletQuery);

  if (!wallet) {
    throw new ApiError(
      "Wallet not found",
      ErrorType.NOT_FOUND,
      404
    );
  }

  if (!wallet.encryptedKey) {
    throw new ApiError(
      "Wallet does not have an encrypted key",
      ErrorType.VALIDATION,
      400
    );
  }

  // Check if sending ETH and if there's enough balance
  if (tokenAddress === 'ETH' && wallet.balance < amountValue) {
    throw new ApiError(
      "Insufficient balance",
      ErrorType.VALIDATION,
      400
    );
  }

  // For ERC-20 tokens, we would check the token balance here
  // This is simplified for the example

  try {
    // Create transaction record with PENDING status
    const transaction = await db.transaction.create({
      data: {
        amount: amountValue,
        fee: 0, // Will be updated after the transaction
        type: "WITHDRAWAL",
        status: "PENDING",
        wallet: {
          connect: {
            id: wallet.id,
          },
        },
      },
    });

    logger.info(`Sending ${amount} ${tokenAddress} from ${wallet.address} to ${to}`);

    // Send tokens
    const result = await sendTokens(
      wallet.encryptedKey,
      wallet.address,
      to,
      tokenAddress,
      amount,
      wallet.network as any, // Cast to SupportedNetwork
      wallet.isTestnet
    );

    // Update transaction with hash and status
    const updatedTransaction = await db.transaction.update({
      where: {
        id: transaction.id,
      },
      data: {
        status: "COMPLETED",
        transactionHash: result.hash,
        network: wallet.network,
        chainId: wallet.chainId,
        tokenAddress: tokenAddress !== 'ETH' ? tokenAddress : null,
        tokenSymbol: tokenAddress === 'ETH' ? 'ETH' : null,
        updatedAt: new Date(),
      },
    });

    // If sending ETH, update wallet balance
    if (tokenAddress === 'ETH') {
      await db.wallet.update({
        where: {
          id: wallet.id,
        },
        data: {
          balance: {
            decrement: amountValue,
          },
          lastSyncedAt: new Date(),
        },
      });
    }

    // Create notification
    await db.notification.create({
      data: {
        title: "Token Transfer Successful",
        message: `${amount} ${tokenAddress === 'ETH' ? 'ETH' : tokenAddress} has been sent to ${to}`,
        type: "TRANSACTION",
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });

    logger.info(`Token transfer successful: ${result.hash}`);

    return NextResponse.json({
      transaction: {
        id: updatedTransaction.id,
        hash: result.hash,
        from: wallet.address,
        to,
        tokenAddress,
        amount,
        status: "COMPLETED",
        network: wallet.network,
        chainId: wallet.chainId,
        blockNumber: result.blockNumber,
      },
      message: "Token transfer successful",
    });
  } catch (error) {
    logger.error(`Error sending tokens for user ${session.user.id}:`, error);
    throw new ApiError(
      "Failed to send tokens",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * Get gas price estimates
 *
 * @returns Gas price estimates
 */
async function getGasPricesHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to get gas prices",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const gasPrices = await getGasPrices();

    logger.info(`Retrieved gas prices for user ${session.user.id}`);

    return NextResponse.json({
      gasPrices,
      timestamp: new Date(),
    });
  } catch (error) {
    logger.error(`Error getting gas prices for user ${session.user.id}:`, error);
    throw new ApiError(
      "Failed to get gas prices",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling and resource isolation
const wrappedGetHandler = withErrorHandling(getGasPricesHandler);
const wrappedPostHandler = withErrorHandling(sendTokensHandler);

// Use tenant isolation for GET and resource isolation for POST
export const GET = withTenantIsolation(wrappedGetHandler);
export const POST = withResourceIsolation('wallet', 'walletId')(wrappedPostHandler);
