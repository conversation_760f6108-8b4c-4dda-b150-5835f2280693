import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { verifyInvitationToken } from "@/lib/tokens";
import { logger } from "@/lib/logger";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const token = searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { error: "Token is required" },
        { status: 400 }
      );
    }

    // Verify the invitation token
    const invitationData = await verifyInvitationToken(token);

    if (!invitationData) {
      return NextResponse.json(
        { error: "Invalid or expired invitation token" },
        { status: 400 }
      );
    }

    // Get the organization name
    const organization = await db.organization.findUnique({
      where: {
        id: invitationData.organizationId,
      },
      select: {
        name: true,
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Get the user's email from invitation data
    const user = await db.user.findUnique({
      where: {
        email: invitationData.email,
      },
      select: {
        email: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Return the invitation data
    return NextResponse.json({
      organizationName: organization.name,
      role: invitationData.role,
      email: user.email,
    });
  } catch (error) {
    logger.error("Error verifying invitation token:", error);
    return NextResponse.json(
      { error: "An error occurred while verifying the invitation token" },
      { status: 500 }
    );
  }
}
