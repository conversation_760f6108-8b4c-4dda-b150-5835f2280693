import { NextResponse } from "next/server";
import { z } from "zod";
import { hash } from "bcryptjs";
import { db } from "@/lib/db";
import { verifyInvitationToken } from "@/lib/tokens";
import { logger } from "@/lib/logger";

const acceptInvitationSchema = z.object({
  token: z.string(),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { token, password } = acceptInvitationSchema.parse(body);

    // Verify the invitation token
    const invitationData = await verifyInvitationToken(token);

    if (!invitationData) {
      return NextResponse.json(
        { error: "Invalid or expired invitation token" },
        { status: 400 }
      );
    }

    // Get the user by email from the invitation
    const user = await db.user.findUnique({
      where: {
        email: invitationData.email,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get the organization
    const organization = await db.organization.findUnique({
      where: {
        id: invitationData.organizationId,
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Hash the password
    const hashedPassword = await hash(password, 10);

    // Update the user with the password and organization
    const updatedUser = await db.user.update({
      where: {
        id: user.id, // Use the id from the user we found by email
      },
      data: {
        password: hashedPassword,
        role: invitationData.role,
        emailVerified: new Date(), // Mark email as verified
        organization: {
          connect: {
            id: invitationData.organizationId,
          },
        },
      },
    });

    // Create a notification for the user
    await db.notification.create({
      data: {
        title: "Welcome to the Organization",
        message: `You have successfully joined ${organization.name}.`,
        type: "SYSTEM",
        user: {
          connect: {
            id: updatedUser.id,
          },
        },
      },
    });

    // Create a notification for the organization admins
    const orgAdmins = await db.user.findMany({
      where: {
        organizationId: organization.id,
        role: "ORGANIZATION_ADMIN",
        id: {
          not: updatedUser.id, // Don't notify the user who just joined
        },
      },
    });

    // Notify all organization admins
    await Promise.all(
      orgAdmins.map((admin) =>
        db.notification.create({
          data: {
            title: "New Team Member",
            message: `${user.email} has joined your organization.`,
            type: "SYSTEM",
            user: {
              connect: {
                id: admin.id,
              },
            },
          },
        })
      )
    );

    logger.info(`User ${updatedUser.id} accepted invitation to organization ${organization.id}`);

    return NextResponse.json({
      message: "Invitation accepted successfully",
    });
  } catch (error) {
    logger.error("Error accepting invitation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while accepting the invitation" },
      { status: 500 }
    );
  }
}
