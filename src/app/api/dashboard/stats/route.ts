/**
 * Dashboard Stats API
 * 
 * This API provides statistics for the dashboard with proper tenant isolation.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

/**
 * GET /api/dashboard/stats
 * Get dashboard statistics with tenant isolation
 */
async function getDashboardStatsHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access dashboard statistics" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to access dashboard statistics" },
        { status: 403 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);
    const organizationId = session.user.organizationId;

    // Create base queries with tenant isolation
    let carbonCreditsQuery = {
      where: {
        organizationId,
      },
    };

    let walletsQuery = {
      where: {
        organizationId,
      },
    };

    let teamMembersQuery = {
      where: {
        organizationId,
      },
    };

    // For transactions, use orderId relation to check organization membership
    let transactionsQuery = {
      where: {
        wallet: {
          organizationId: organizationId,
        }
      },
    };

    // Apply tenant isolation
    carbonCreditsQuery = withTenantQuery(carbonCreditsQuery, tenantContext);
    walletsQuery = withTenantQuery(walletsQuery, tenantContext);
    teamMembersQuery = withTenantQuery(teamMembersQuery, tenantContext);
    transactionsQuery = withTenantQuery(transactionsQuery, tenantContext);

    // Execute queries in parallel
    const [
      carbonCreditsCount,
      walletsCount,
      teamMembersCount,
      transactionsCount,
      listedCreditsCount,
      verifiedCreditsCount,
      pendingCreditsCount,
    ] = await Promise.all([
      db.carbonCredit.count(carbonCreditsQuery),
      db.wallet.count(walletsQuery),
      db.user.count(teamMembersQuery),
      db.transaction.count(transactionsQuery),
      db.carbonCredit.count({
        where: {
          ...carbonCreditsQuery.where,
          status: "LISTED",
        },
      }),
      db.carbonCredit.count({
        where: {
          ...carbonCreditsQuery.where,
          verificationStatus: "VERIFIED",
        },
      }),
      db.carbonCredit.count({
        where: {
          ...carbonCreditsQuery.where,
          verificationStatus: "PENDING",
        },
      }),
    ]);

    // Get recent transactions with tenant isolation
    let recentTransactionsQuery = {
      where: {
        wallet: {
          organizationId: organizationId,
        }
      },
      orderBy: {
        createdAt: "desc" as const,
      },
      take: 5,
      include: {
        wallet: true,
        order: {
          include: {
            carbonCredit: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    };

    recentTransactionsQuery = withTenantQuery(recentTransactionsQuery, tenantContext);
    const recentTransactions = await db.transaction.findMany(recentTransactionsQuery);

    return NextResponse.json({
      stats: {
        carbonCredits: carbonCreditsCount || 0,
        wallets: walletsCount || 0,
        teamMembers: teamMembersCount || 0,
        transactions: transactionsCount || 0,
        listedCredits: listedCreditsCount,
        verifiedCredits: verifiedCreditsCount,
        pendingCredits: pendingCreditsCount,
      },
      recentTransactions,
    });
  } catch (error) {
    logger.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching dashboard statistics" },
      { status: 500 }
    );
  }
}

// Export the handler with tenant isolation middleware
export const GET = withTenantIsolation(getDashboardStatsHandler);
