import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";

// Schema for creating a project
const projectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  coordinates: z.string().optional(),
  area: z.number().positive().optional(),
  externalProjectId: z.string().optional(),
  registryId: z.string().optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  methodologyVersion: z.string().optional(),
  estimatedReductions: z.number().positive().optional(),
  actualReductions: z.number().positive().optional(),
  verifier: z.string().optional(),
  validator: z.string().optional(),
  images: z.array(z.string().url()).optional(),
  budget: z.number().positive().optional(),
  roi: z.number().optional(),
  sdgs: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for filtering projects
const projectFilterSchema = z.object({
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]).optional(),
  status: z.enum([
    "PENDING",
    "ACTIVE",
    "COMPLETED",
    "SUSPENDED",
    "CANCELLED"
  ]).optional(),
  verificationStatus: z.enum([
    "PENDING",
    "VERIFIED",
    "REJECTED",
    "EXPIRED"
  ]).optional(),
  country: z.string().optional(),
  standard: z.string().optional(),
  startDateFrom: z.string().datetime().optional(),
  startDateTo: z.string().datetime().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["name", "createdAt", "startDate", "status"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
  page: z.number().int().positive().optional(),
  limit: z.number().int().positive().max(100).optional(),
});

/**
 * POST /api/projects
 * Create a new project
 */
async function createProjectHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create a project",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "You must be part of an organization to create a project",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Parse request body
    const body = await req.json();
    const projectData = projectSchema.parse(body);

    // Check if the organization is verified
    const organization = await db.organization.findUnique({
      where: {
        id: session.user.organizationId,
      },
      select: {
        status: true,
        verificationStatus: true,
      },
    });

    if (organization?.status !== "ACTIVE") {
      throw new ApiError(
        "Your organization must be active to create projects",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Create the project
    const project = await db.project.create({
      data: {
        ...projectData,
        status: "PENDING",
        verificationStatus: "PENDING",
        organization: {
          connect: {
            id: session.user.organizationId,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} created project ${project.id}`);

    return NextResponse.json({
      project,
      message: "Project created successfully",
    });
  } catch (error) {
    logger.error("Error creating project:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid project data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while creating the project",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/projects
 * Get projects with filtering and pagination
 */
async function getProjectsHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view projects",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams: Record<string, any> = {};

    url.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });

    // Convert numeric parameters
    if (queryParams.page) queryParams.page = parseInt(queryParams.page);
    if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);

    // Parse and validate filters
    const filters = projectFilterSchema.parse(queryParams);

    // Build the where clause for filtering
    const where: any = {};

    if (filters.type) where.type = filters.type;
    if (filters.status) where.status = filters.status;
    if (filters.verificationStatus) where.verificationStatus = filters.verificationStatus;
    if (filters.country) where.country = filters.country;
    if (filters.standard) where.standard = filters.standard;

    if (filters.startDateFrom || filters.startDateTo) {
      where.startDate = {};
      if (filters.startDateFrom) where.startDate.gte = new Date(filters.startDateFrom);
      if (filters.startDateTo) where.startDate.lte = new Date(filters.startDateTo);
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    // Create base query
    let projectsQuery: any = {
      where,
      include: {
        organization: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            carbonCredits: true,
          },
        },
      },
      orderBy: {},
    };

    // Add sorting
    if (filters.sortBy) {
      projectsQuery.orderBy[filters.sortBy] = filters.sortOrder || 'desc';
    } else {
      projectsQuery.orderBy.createdAt = 'desc';
    }

    // Add pagination
    if (filters.page && filters.limit) {
      projectsQuery.skip = (filters.page - 1) * filters.limit;
      projectsQuery.take = filters.limit;
    } else {
      projectsQuery.take = filters.limit || 10;
    }

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectsQuery = withTenantIsolation(projectsQuery, tenantContext);
    }

    // Get projects count for pagination
    const totalCount = await db.project.count({
      where: projectsQuery.where,
    });

    // Get projects
    const projects = await db.project.findMany(projectsQuery);

    // Get unique filter options for dropdowns
    const [types, statuses, countries, standards] = await Promise.all([
      db.project.groupBy({
        by: ['type'],
        where: tenantContext.isAdmin ? {} : { organizationId: session.user.organizationId },
      }),
      db.project.groupBy({
        by: ['status'],
        where: tenantContext.isAdmin ? {} : { organizationId: session.user.organizationId },
      }),
      db.project.groupBy({
        by: ['country'],
        where: tenantContext.isAdmin ? {} : { organizationId: session.user.organizationId },
      }),
      db.project.groupBy({
        by: ['standard'],
        where: tenantContext.isAdmin ? {} : { organizationId: session.user.organizationId },
      }),
    ]);

    return NextResponse.json({
      projects,
      filters: {
        types: types.map(t => t.type),
        statuses: statuses.map(s => s.status),
        countries: countries.map(c => c.country).filter(Boolean),
        standards: standards.map(s => s.standard).filter(Boolean),
      },
      pagination: {
        total: totalCount,
        page: filters.page || 1,
        limit: filters.limit || 10,
        pages: Math.ceil(totalCount / (filters.limit || 10)),
      },
    });
  } catch (error) {
    logger.error("Error fetching projects:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid filter parameters",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while fetching projects",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(createProjectHandler);
const wrappedGetHandler = withErrorHandling(getProjectsHandler);

// Export the handlers with tenant isolation middleware
export const POST = withTenantIsolation(wrappedPostHandler);
export const GET = withTenantIsolation(wrappedGetHandler);
