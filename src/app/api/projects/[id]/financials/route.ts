import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for creating a financial metric
const financialMetricSchema = z.object({
  metricType: z.enum([
    "REVENUE",
    "EXPENSE",
    "PROFIT",
    "INVESTMENT",
    "ROI",
    "CARBON_VALUE",
    "TRANSACTION_VOLUME",
    "ASSET_VALUE",
    "CUSTOM"
  ]),
  name: z.string().min(1, "Metric name is required"),
  value: z.number(),
  previousValue: z.number().optional(),
  changePercent: z.number().optional(),
  currency: z.string().default("USD"),
  period: z.string().min(1, "Period is required"),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  target: z.number().optional(),
  status: z.enum(["ABOVE_TARGET", "ON_TARGET", "BELOW_TARGET", "NO_TARGET"]).optional(),
  notes: z.string().optional(),
});

/**
 * POST /api/projects/[id]/financials
 * Add a financial metric to a project
 */
async function addFinancialMetricHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to add financial metrics",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const metricData = financialMetricSchema.parse(body);

    // Create the financial metric
    const metric = await db.projectFinancialMetric.create({
      data: {
        ...metricData,
        project: {
          connect: {
            id: params.id,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} added financial metric ${metric.id} to project ${params.id}`);

    return NextResponse.json({
      metric,
      message: "Financial metric added successfully",
    });
  } catch (error) {
    logger.error(`Error adding financial metric to project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid financial metric data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while adding the financial metric",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/projects/[id]/financials
 * Get financial metrics for a project
 */
async function getFinancialMetricsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view financial metrics",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const metricType = url.searchParams.get('metricType');
    const period = url.searchParams.get('period');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit')!) : 20;

    // Build the where clause for filtering
    const where: any = {
      projectId: params.id,
    };

    if (metricType) where.metricType = metricType;
    if (period) where.period = period;
    
    if (startDate || endDate) {
      where.startDate = {};
      if (startDate) where.startDate.gte = new Date(startDate);
      if (endDate) where.endDate = { lte: new Date(endDate) };
    }

    // Get financial metrics
    const metrics = await db.projectFinancialMetric.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    // Get summary statistics
    const summary = await db.projectFinancialMetric.groupBy({
      by: ['metricType'],
      where: {
        projectId: params.id,
      },
      _avg: {
        value: true,
        changePercent: true,
      },
      _sum: {
        value: true,
      },
      _count: true,
    });

    return NextResponse.json({
      metrics,
      summary,
    });
  } catch (error) {
    logger.error(`Error fetching financial metrics for project ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching financial metrics",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(addFinancialMetricHandler);
const wrappedGetHandler = withErrorHandling(getFinancialMetricsHandler);

// Export the handlers with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedPostHandler);
export const GET = withResourceIsolation('project', 'id')(wrappedGetHandler);
