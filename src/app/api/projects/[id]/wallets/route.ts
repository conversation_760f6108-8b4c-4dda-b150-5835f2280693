import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";
import { createWallet } from "@/lib/blockchain";
import { SupportedNetwork } from "@/lib/blockchain-config";

// Schema for creating a project wallet
const createWalletSchema = z.object({
  name: z.string().min(1, "Wallet name is required"),
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]).default("polygon"),
  isTestnet: z.boolean().default(true),
  purpose: z.string().optional(),
  isSmartWallet: z.boolean().default(true),
  securitySettings: z.object({
    recoveryEnabled: z.boolean().optional(),
    recoveryType: z.enum(["EMAIL", "SOCIAL", "MULTISIG"]).optional(),
    transactionLimitDaily: z.number().positive().optional(),
    transactionLimitPerTx: z.number().positive().optional(),
    requireApprovals: z.boolean().optional(),
    approvalThreshold: z.number().int().positive().optional(),
  }).optional(),
});

/**
 * POST /api/projects/[id]/wallets
 * Create a new wallet for a project
 */
async function createProjectWalletHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create a project wallet",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const walletData = createWalletSchema.parse(body);

    // Create the wallet using blockchain service
    const { address, chainId, encryptedKey, smartAccountAddress, factoryAddress, implementationAddress } = 
      await createWallet(walletData.network as SupportedNetwork, walletData.isTestnet, walletData.isSmartWallet);

    // Create the wallet in the database
    const wallet = await db.wallet.create({
      data: {
        name: walletData.name,
        address,
        network: walletData.network,
        chainId,
        isTestnet: walletData.isTestnet,
        walletType: "PROJECT",
        purpose: walletData.purpose,
        encryptedKey,
        isSmartWallet: walletData.isSmartWallet,
        smartAccountAddress,
        factoryAddress,
        implementationAddress,
        recoveryEnabled: walletData.securitySettings?.recoveryEnabled || false,
        recoveryType: walletData.securitySettings?.recoveryType,
        transactionLimitDaily: walletData.securitySettings?.transactionLimitDaily,
        transactionLimitPerTx: walletData.securitySettings?.transactionLimitPerTx,
        requireApprovals: walletData.securitySettings?.requireApprovals || false,
        approvalThreshold: walletData.securitySettings?.approvalThreshold,
        user: session.user.id ? {
          connect: {
            id: session.user.id,
          },
        } : undefined,
        organization: session.user.organizationId ? {
          connect: {
            id: session.user.organizationId,
          },
        } : undefined,
        project: {
          connect: {
            id: params.id,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} created wallet ${wallet.id} for project ${params.id}`);

    return NextResponse.json({
      wallet,
      message: "Project wallet created successfully",
    });
  } catch (error) {
    logger.error(`Error creating wallet for project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid wallet data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while creating the project wallet",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/projects/[id]/wallets
 * Get all wallets for a project
 */
async function getProjectWalletsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view project wallets",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Get all wallets for the project
    const wallets = await db.wallet.findMany({
      where: {
        projectId: params.id,
      },
      select: {
        id: true,
        name: true,
        address: true,
        network: true,
        chainId: true,
        isTestnet: true,
        walletType: true,
        purpose: true,
        isSmartWallet: true,
        smartAccountAddress: true,
        balance: true,
        lastSyncedAt: true,
        securityScore: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({ wallets });
  } catch (error) {
    logger.error(`Error fetching wallets for project ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching project wallets",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(createProjectWalletHandler);
const wrappedGetHandler = withErrorHandling(getProjectWalletsHandler);

// Export the handlers with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedPostHandler);
export const GET = withResourceIsolation('project', 'id')(wrappedGetHandler);
