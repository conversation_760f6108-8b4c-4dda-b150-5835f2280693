import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation } from "@/lib/tenant-isolation";

// Schema for linking/unlinking carbon credits
const linkCarbonCreditsSchema = z.object({
  toLink: z.array(z.string()),
  toUnlink: z.array(z.string()),
});

/**
 * POST /api/projects/[id]/carbon-credits/link
 * Link or unlink carbon credits to/from a project
 */
async function linkCarbonCreditsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to manage project carbon credits",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Check if the project exists
    const project = await db.project.findUnique({
      where: {
        id: params.id,
      },
      include: {
        carbonCredits: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!project) {
      throw new ApiError(
        "Project not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const { toLink, toUnlink } = linkCarbonCreditsSchema.parse(body);

    // Process unlinking first
    if (toUnlink.length > 0) {
      await db.carbonCredit.updateMany({
        where: {
          id: {
            in: toUnlink,
          },
          projectId: params.id,
        },
        data: {
          projectId: null,
        },
      });

      logger.info(`User ${session.user.id} unlinked ${toUnlink.length} carbon credits from project ${params.id}`);
    }

    // Process linking
    if (toLink.length > 0) {
      await db.carbonCredit.updateMany({
        where: {
          id: {
            in: toLink,
          },
          projectId: null, // Only link credits that aren't already linked to a project
        },
        data: {
          projectId: params.id,
        },
      });

      logger.info(`User ${session.user.id} linked ${toLink.length} carbon credits to project ${params.id}`);
    }

    return NextResponse.json({
      message: "Carbon credits updated successfully",
      linked: toLink.length,
      unlinked: toUnlink.length,
    });
  } catch (error) {
    logger.error(`Error linking carbon credits to project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid request data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while updating project carbon credits",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling
const wrappedHandler = withErrorHandling(linkCarbonCreditsHandler);

// Export the handler with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedHandler);
