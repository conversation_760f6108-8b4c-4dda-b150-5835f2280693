import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for creating a carbon credit
const carbonCreditSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  minPurchaseQuantity: z.number().positive().optional(),
  vintage: z.number().int().min(1900).max(new Date().getFullYear()),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  location: z.string().optional(),
  country: z.string().optional(),
  serialNumber: z.string().optional(),
  certificationDate: z.string().datetime().optional(),
  expirationDate: z.string().datetime().optional(),
  verificationBody: z.string().optional(),
  additionalCertifications: z.array(z.string()).optional(),
  images: z.array(z.string().url()).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * POST /api/projects/[id]/carbon-credits
 * Create a new carbon credit for a project
 */
async function createCarbonCreditHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create carbon credits",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the project is verified
    if (project.verificationStatus !== "VERIFIED") {
      throw new ApiError(
        "Project must be verified before creating carbon credits",
        ErrorType.VALIDATION,
        400
      );
    }

    // Parse request body
    const body = await req.json();
    const creditData = carbonCreditSchema.parse(body);

    // Create the carbon credit
    const carbonCredit = await db.carbonCredit.create({
      data: {
        ...creditData,
        availableQuantity: creditData.quantity,
        retiredQuantity: 0,
        status: "PENDING",
        verificationStatus: "PENDING",
        externalProjectId: project.externalProjectId,
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: session.user.organizationId!,
          },
        },
        project: {
          connect: {
            id: params.id,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} created carbon credit ${carbonCredit.id} for project ${params.id}`);

    return NextResponse.json({
      carbonCredit,
      message: "Carbon credit created successfully",
    });
  } catch (error) {
    logger.error(`Error creating carbon credit for project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid carbon credit data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while creating the carbon credit",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/projects/[id]/carbon-credits
 * Get all carbon credits for a project
 */
async function getCarbonCreditsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view carbon credits",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const status = url.searchParams.get('status');
    const vintage = url.searchParams.get('vintage') ? parseInt(url.searchParams.get('vintage')!) : null;
    const standard = url.searchParams.get('standard');
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';
    const page = url.searchParams.get('page') ? parseInt(url.searchParams.get('page')!) : 1;
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit')!) : 10;

    // Build the where clause for filtering
    const where: any = {
      projectId: params.id,
    };

    if (status) where.status = status;
    if (vintage) where.vintage = vintage;
    if (standard) where.standard = standard;

    // Create base query
    const creditsQuery = {
      where,
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip: (page - 1) * limit,
      take: limit,
    };

    // Get carbon credits count for pagination
    const totalCount = await db.carbonCredit.count({
      where,
    });

    // Get carbon credits
    const carbonCredits = await db.carbonCredit.findMany(creditsQuery);

    // Get unique filter options for dropdowns
    const [standards, vintages, statuses] = await Promise.all([
      db.carbonCredit.groupBy({
        by: ['standard'],
        where: { projectId: params.id },
      }),
      db.carbonCredit.groupBy({
        by: ['vintage'],
        where: { projectId: params.id },
      }),
      db.carbonCredit.groupBy({
        by: ['status'],
        where: { projectId: params.id },
      }),
    ]);

    return NextResponse.json({
      carbonCredits,
      filters: {
        standards: standards.map(s => s.standard),
        vintages: vintages.map(v => v.vintage),
        statuses: statuses.map(s => s.status),
      },
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    logger.error(`Error fetching carbon credits for project ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching carbon credits",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(createCarbonCreditHandler);
const wrappedGetHandler = withErrorHandling(getCarbonCreditsHandler);

// Export the handlers with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedPostHandler);
export const GET = withResourceIsolation('project', 'id')(wrappedGetHandler);
