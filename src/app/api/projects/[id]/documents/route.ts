import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for creating a project document
const projectDocumentSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  type: z.enum([
    "PROJECT_DESIGN",
    "METHODOLOGY",
    "BASELINE_ASSESSMENT",
    "MONITORING_PLAN",
    "VALIDATION_REPORT",
    "VERIFICATION_REPORT",
    "LEGAL_DOCUMENT",
    "STAKEHOLDER_CONSULTATION",
    "ENVIRONMENTAL_IMPACT",
    "SOCIAL_IMPACT",
    "FINANCIAL_DOCUMENT",
    "OTHER"
  ]),
  url: z.string().url("Invalid document URL"),
  notes: z.string().optional(),
});

/**
 * POST /api/projects/[id]/documents
 * Add a document to a project
 */
async function addDocumentHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to add project documents",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const documentData = projectDocumentSchema.parse(body);

    // Create the document
    const document = await db.projectDocument.create({
      data: {
        ...documentData,
        status: "PENDING",
        project: {
          connect: {
            id: params.id,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} added document ${document.id} to project ${params.id}`);

    return NextResponse.json({
      document,
      message: "Document added successfully",
    });
  } catch (error) {
    logger.error(`Error adding document to project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid document data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while adding the document",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/projects/[id]/documents
 * Get all documents for a project
 */
async function getDocumentsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view project documents",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Get all documents for the project
    const documents = await db.projectDocument.findMany({
      where: {
        projectId: params.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({ documents });
  } catch (error) {
    logger.error(`Error fetching documents for project ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching project documents",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(addDocumentHandler);
const wrappedGetHandler = withErrorHandling(getDocumentsHandler);

// Export the handlers with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedPostHandler);
export const GET = withResourceIsolation('project', 'id')(wrappedGetHandler);
