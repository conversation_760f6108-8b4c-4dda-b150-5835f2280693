import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for requesting verification
const verificationRequestSchema = z.object({
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for updating verification status (admin only)
const updateVerificationSchema = z.object({
  status: z.enum(["PENDING", "VERIFIED", "REJECTED", "EXPIRED"]),
  verifier: z.string().optional(),
  verifierEmail: z.string().email().optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * POST /api/projects/[id]/verification
 * Request verification for a project
 */
async function requestVerificationHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to request project verification",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
      include: {
        documents: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the project has required documents
    if (project.documents.length === 0) {
      throw new ApiError(
        "Project must have at least one document before requesting verification",
        ErrorType.VALIDATION,
        400
      );
    }

    // Parse request body
    const body = await req.json();
    const verificationData = verificationRequestSchema.parse(body);

    // Create verification record
    const verification = await db.projectVerification.create({
      data: {
        status: "PENDING",
        notes: verificationData.notes,
        metadata: verificationData.metadata,
        project: {
          connect: {
            id: params.id,
          },
        },
      },
    });

    // Update project verification status
    await db.project.update({
      where: {
        id: params.id,
      },
      data: {
        verificationStatus: "PENDING",
      },
    });

    logger.info(`User ${session.user.id} requested verification for project ${params.id}`);

    return NextResponse.json({
      verification,
      message: "Verification requested successfully",
    });
  } catch (error) {
    logger.error(`Error requesting verification for project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid verification data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while requesting verification",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/projects/[id]/verification
 * Get verification history for a project
 */
async function getVerificationHistoryHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view project verification history",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantIsolation(projectQuery, tenantContext);
    }

    const project = await db.project.findUnique(projectQuery);

    if (!project) {
      throw new ApiError(
        "Project not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Get verification history
    const verificationHistory = await db.projectVerification.findMany({
      where: {
        projectId: params.id,
      },
      orderBy: {
        timestamp: 'desc',
      },
    });

    return NextResponse.json({ verificationHistory });
  } catch (error) {
    logger.error(`Error fetching verification history for project ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching verification history",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PATCH /api/projects/[id]/verification
 * Update verification status (admin only)
 */
async function updateVerificationStatusHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update verification status",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Check if user is an admin
    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Only administrators can update verification status",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Check if the project exists
    const project = await db.project.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!project) {
      throw new ApiError(
        "Project not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const updateData = updateVerificationSchema.parse(body);

    // Create new verification record
    const verification = await db.projectVerification.create({
      data: {
        status: updateData.status,
        verifier: updateData.verifier,
        verifierEmail: updateData.verifierEmail,
        notes: updateData.notes,
        metadata: updateData.metadata,
        project: {
          connect: {
            id: params.id,
          },
        },
      },
    });

    // Update project verification status
    await db.project.update({
      where: {
        id: params.id,
      },
      data: {
        verificationStatus: updateData.status,
      },
    });

    logger.info(`Admin ${session.user.id} updated verification status for project ${params.id} to ${updateData.status}`);

    return NextResponse.json({
      verification,
      message: "Verification status updated successfully",
    });
  } catch (error) {
    logger.error(`Error updating verification status for project ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid verification data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while updating verification status",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(requestVerificationHandler);
const wrappedGetHandler = withErrorHandling(getVerificationHistoryHandler);
const wrappedPatchHandler = withErrorHandling(updateVerificationStatusHandler);

// Export the handlers with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedPostHandler);
export const GET = withResourceIsolation('project', 'id')(wrappedGetHandler);
export const PATCH = withResourceIsolation('project', 'id')(wrappedPatchHandler);
