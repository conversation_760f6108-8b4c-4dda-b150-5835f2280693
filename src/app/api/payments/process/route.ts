import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { paymentService } from "@/lib/payment";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

// Schema for processing a payment
const processPaymentSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().min(3, "Currency code is required"),
  description: z.string().min(1, "Description is required"),
  metadata: z.record(z.any()).optional(),
  paymentMethodId: z.string().optional(),
});

/**
 * POST /api/payments/process
 * Process a payment
 */
// Original POST handler
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Ensure the organization ID matches the tenant context
    if (!tenantContext.isAdmin && session.user.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to process payment with mismatched organization ID`);
      return NextResponse.json(
        { error: "Organization mismatch" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = processPaymentSchema.parse(body);

    // Process payment
    const paymentResult = await paymentService.processPayment({
      ...validatedData,
      userId: session.user.id,
      organizationId: session.user.organizationId,
    });

    return NextResponse.json({
      payment: paymentResult,
      message: paymentResult.success
        ? "Payment processed successfully"
        : `Payment failed: ${paymentResult.errorMessage}`,
    });
  } catch (error) {
    logger.error("Error processing payment:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to process payment" },
      { status: 500 }
    );
  }
}

// Export the handler with tenant isolation middleware
export const POST = withTenantIsolation(postHandler);
