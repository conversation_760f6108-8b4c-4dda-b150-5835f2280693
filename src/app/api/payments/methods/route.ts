import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { paymentService, PaymentMethodType } from "@/lib/payment";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

// Schema for creating a payment method
const createPaymentMethodSchema = z.object({
  type: z.enum(["CREDIT_CARD", "BANK_TRANSFER", "CRYPTO", "INVOICE"]),
  details: z.record(z.any()),
  isDefault: z.boolean().optional(),
});

/**
 * GET /api/payments/methods
 * Get payment methods for the current user/organization
 */
// Original GET handler
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Ensure the organization ID matches the tenant context
    if (!tenantContext.isAdmin && session.user.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to access payment methods with mismatched organization ID`);
      return NextResponse.json(
        { error: "Organization mismatch" },
        { status: 403 }
      );
    }

    // Get payment methods
    const paymentMethods = await paymentService.getPaymentMethods(
      session.user.id,
      session.user.organizationId
    );

    return NextResponse.json({ paymentMethods });
  } catch (error) {
    logger.error("Error getting payment methods:", error);
    return NextResponse.json(
      { error: "Failed to get payment methods" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payments/methods
 * Create a new payment method
 */
// Original POST handler
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Ensure the organization ID matches the tenant context
    if (!tenantContext.isAdmin && session.user.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to create payment method with mismatched organization ID`);
      return NextResponse.json(
        { error: "Organization mismatch" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = createPaymentMethodSchema.parse(body);

    // Create payment method
    const paymentMethod = await paymentService.createPaymentMethod(
      session.user.id,
      session.user.organizationId,
      validatedData.type as PaymentMethodType,
      validatedData.details,
      validatedData.isDefault || false
    );

    return NextResponse.json({
      paymentMethod,
      message: "Payment method created successfully",
    });
  } catch (error) {
    logger.error("Error creating payment method:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create payment method" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);