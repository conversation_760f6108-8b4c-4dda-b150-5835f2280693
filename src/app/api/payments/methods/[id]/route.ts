import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { paymentService } from "@/lib/payment";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

/**
 * DELETE /api/payments/methods/[id]
 * Delete a payment method
 */
// Original DELETE handler
async function deleteHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paymentMethodId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Ensure the organization ID matches the tenant context
    if (!tenantContext.isAdmin && session.user.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to delete payment method with mismatched organization ID`);
      return NextResponse.json(
        { error: "Organization mismatch" },
        { status: 403 }
      );
    }

    // Check if payment method exists and belongs to the user's organization
    const paymentMethod = await db.paymentMethod.findUnique({
      where: { id: paymentMethodId },
      select: { id: true, organizationId: true },
    });

    if (!paymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Check if the payment method belongs to the user's organization
    if (!tenantContext.isAdmin && paymentMethod.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to delete payment method ${paymentMethodId} from another organization`);
      return NextResponse.json(
        { error: "You do not have permission to delete this payment method" },
        { status: 403 }
      );
    }

    // Delete payment method
    await paymentService.deletePaymentMethod(
      paymentMethodId,
      session.user.id,
      session.user.organizationId
    );

    return NextResponse.json({
      message: "Payment method deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting payment method:", error);
    return NextResponse.json(
      { error: "Failed to delete payment method" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/payments/methods/[id]
 * Set a payment method as default
 */
// Original PATCH handler
async function patchHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paymentMethodId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Ensure the organization ID matches the tenant context
    if (!tenantContext.isAdmin && session.user.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to update payment method with mismatched organization ID`);
      return NextResponse.json(
        { error: "Organization mismatch" },
        { status: 403 }
      );
    }

    // Check if payment method exists and belongs to the user's organization
    const existingPaymentMethod = await db.paymentMethod.findUnique({
      where: { id: paymentMethodId },
      select: { id: true, organizationId: true },
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Check if the payment method belongs to the user's organization
    if (!tenantContext.isAdmin && existingPaymentMethod.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to update payment method ${paymentMethodId} from another organization`);
      return NextResponse.json(
        { error: "You do not have permission to update this payment method" },
        { status: 403 }
      );
    }

    // Set payment method as default
    const paymentMethod = await paymentService.setDefaultPaymentMethod(
      paymentMethodId,
      session.user.id,
      session.user.organizationId
    );

    return NextResponse.json({
      paymentMethod,
      message: "Payment method set as default",
    });
  } catch (error) {
    logger.error("Error setting default payment method:", error);
    return NextResponse.json(
      { error: "Failed to set default payment method" },
      { status: 500 }
    );
  }
}

// Export the handlers with resource isolation middleware
export const DELETE = withResourceIsolation('payment_method', 'id')(deleteHandler);
export const PATCH = withResourceIsolation('payment_method', 'id')(patchHandler);