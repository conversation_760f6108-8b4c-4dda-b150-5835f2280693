import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { paymentService } from "@/lib/payment";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

/**
 * GET /api/payments/history
 * Get payment history for the current user/organization
 */
// Original GET handler
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "User must be part of an organization" },
        { status: 400 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Ensure the organization ID matches the tenant context
    if (!tenantContext.isAdmin && session.user.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to access payment history with mismatched organization ID`);
      return NextResponse.json(
        { error: "Organization mismatch" },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Get payment history
    const paymentHistory = await paymentService.getPaymentHistory(
      session.user.id,
      session.user.organizationId,
      limit,
      offset
    );

    return NextResponse.json(paymentHistory);
  } catch (error) {
    logger.error("Error getting payment history:", error);
    return NextResponse.json(
      { error: "Failed to get payment history" },
      { status: 500 }
    );
  }
}

// Export the handler with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
