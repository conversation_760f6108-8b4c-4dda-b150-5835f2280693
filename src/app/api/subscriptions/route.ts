import { NextRequest, NextResponse } from "next/server";
import { subscriptionService, SUBSCRIPTION_PLANS } from "@/lib/subscription";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { z } from "zod";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { onboardingEmailService } from "@/lib/email/onboarding-emails";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for creating a subscription
const createSubscriptionSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  plan: z.enum(["FREE", "BASIC", "PREMIUM", "ENTERPRISE"]),
});

/**
 * GET /api/subscriptions
 * Get all subscription plans or user's subscriptions
 */
async function getSubscriptionsHandler(req: NextRequest) {
  const session = await auth();

  // Get tenant context after session is available
  const tenantContext = await getTenantContext(session?.user?.id);

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view subscriptions",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Check if we're requesting plans or user subscriptions
  const url = new URL(req.url);
  const plansOnly = url.searchParams.get('plans') === 'true';

  if (plansOnly) {
    // Return all subscription plans
    return NextResponse.json(SUBSCRIPTION_PLANS);
  }

  try {
    // Get all organizations the user belongs to
    const organizations = await db.organization.findMany({
      where: {
        users: {
          some: {
            id: session.user.id,
          },
        },
      },
      include: {
        subscription: true,
      },
    });

    // Extract subscriptions
    const subscriptions = organizations
      .filter((org) => org.subscription)
      .map((org) => {
        // We've already filtered out null subscriptions
        const subscription = org.subscription;
        return {
          id: subscription.id,
          plan: subscription.plan,
          status: subscription.status,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          organizationId: org.id,
          organizationName: org.name,
        };
      });

    logger.info(`Retrieved ${subscriptions.length} subscriptions for user ${session.user.id}`);

    return NextResponse.json({
      subscriptions,
    });
  } catch (error) {
    logger.error("Error getting subscriptions:", error);
    throw new ApiError(
      "Failed to get subscriptions",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * POST /api/subscriptions
 * Create a new subscription for an organization
 */
async function createSubscriptionHandler(req: NextRequest) {
  const session = await auth();

  // Get tenant context after session is available
  const tenantContext = await getTenantContext(session?.user?.id);

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to create a subscription",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { organizationId, plan } = createSubscriptionSchema.parse(body);

    // Check if the organization exists and belongs to the user
    let organizationQuery = {
      where: {
        id: organizationId,
        users: {
          some: {
            id: session.user.id,
          },
        },
      },
      include: {
        subscription: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      organizationQuery = withTenantQuery(organizationQuery, tenantContext);
    }

    const organization = await db.organization.findFirst(organizationQuery);

    if (!organization) {
      throw new ApiError(
        "Organization not found or you don't have permission to create a subscription for this organization",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if user is an admin or organization admin for non-onboarding scenarios
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";
    const isOnboarding = !organization.subscription; // If no subscription exists, assume onboarding

    if (!isOnboarding && !isAdmin && !isOrgAdmin) {
      throw new ApiError(
        "Forbidden: Admin access required to change subscription",
        ErrorType.FORBIDDEN,
        403
      );
    }

    // If the organization already has a subscription, update it
    if (organization.subscription) {
      const updatedSubscription = await db.subscription.update({
        where: {
          id: organization.subscription.id,
        },
        data: {
          plan: plan,
          startDate: new Date(),
          status: "ACTIVE",
        },
      });

      // Create an audit log
      await db.auditLog.create({
        data: {
          type: "SUBSCRIPTION_UPDATED",
          description: `${plan} subscription updated for organization ${organization.name}`,
          user: {
            connect: {
              id: session.user.id,
            },
          },
          organization: {
            connect: {
              id: organizationId,
            },
          },
          metadata: {
            subscriptionId: updatedSubscription.id,
            plan: plan,
            previousPlan: organization.subscription.plan,
          },
        },
      });

      logger.info(`Subscription updated for organization ${organizationId}: ${plan}`);

      return NextResponse.json({
        subscription: updatedSubscription,
        message: "Subscription updated successfully",
      });
    }

    // Create a new subscription
    const subscription = await subscriptionService.createSubscription(
      organizationId,
      plan,
      session.user.id
    );

    // Create a notification
    await db.notification.create({
      data: {
        title: "Subscription Created",
        message: `Your ${plan} subscription has been created successfully.`,
        type: "SUBSCRIPTION",
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: organizationId,
          },
        },
      },
    });

    // Get organization details for email
    const organizationDetails = await db.organization.findUnique({
      where: { id: organizationId },
      select: { name: true }
    });

    // Send subscription confirmation email
    try {
      if (organizationDetails) {
        await onboardingEmailService.sendSubscriptionConfirmationEmail(
          session.user.email || '',
          session.user.name || 'User',
          organizationDetails.name,
          plan
        );
      }
    } catch (emailError) {
      logger.error("Error sending subscription confirmation email:", emailError);
      // Continue even if email fails
    }

    logger.info(`Subscription created for organization ${organizationId}: ${plan}`);

    return NextResponse.json({
      subscription,
      message: "Subscription created successfully",
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    logger.error("Error creating subscription:", error);
    throw new ApiError(
      "Failed to create subscription",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling and tenant isolation
const wrappedGetHandler = withErrorHandling(getSubscriptionsHandler);
const wrappedPostHandler = withErrorHandling(createSubscriptionHandler);

export const GET = withTenantIsolation(wrappedGetHandler);
export const POST = withTenantIsolation(wrappedPostHandler);
