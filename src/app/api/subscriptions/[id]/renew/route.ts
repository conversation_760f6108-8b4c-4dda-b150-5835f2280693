import { NextRequest, NextResponse } from "next/server";
import { subscriptionService } from "@/lib/subscription";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

/**
 * POST /api/subscriptions/[id]/renew
 * Renew subscription
 */
// Original POST handler
async function postHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subscriptionId = params.id;

    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get subscription
    const subscription = await db.subscription.findUnique({
      where: { id: subscriptionId },
      include: { organization: true },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Check if user is an admin or organization admin
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" &&
                      session.user.organizationId === subscription.organizationId;

    if (!isAdmin && !isOrgAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Renew subscription
    const renewedSubscription = await subscriptionService.renewSubscription(
      subscriptionId,
      session.user.id
    );

    return NextResponse.json(renewedSubscription);
  } catch (error) {
    logger.error("Error renewing subscription:", error);
    return NextResponse.json(
      { error: "Failed to renew subscription" },
      { status: 500 }
    );
  }
}

// Export the handler with resource isolation middleware
export const POST = withResourceIsolation('subscription', 'id')(postHandler);
