import { NextRequest, NextResponse } from "next/server";
import { subscriptionService } from "@/lib/subscription";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { db } from "@/lib/db";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

/**
 * GET /api/subscriptions/[id]
 * Get subscription details
 */
// Original GET handler
async function getHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subscriptionId = params.id;

    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get subscription
    const subscription = await db.subscription.findUnique({
      where: { id: subscriptionId },
      include: { organization: true },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Check if user is an admin or belongs to the organization
    const isAdmin = session.user.role === "ADMIN";
    const isOrgMember = session.user.organizationId === subscription.organizationId;

    if (!isAdmin && !isOrgMember) {
      return NextResponse.json(
        { error: "Forbidden: You don't have access to this subscription" },
        { status: 403 }
      );
    }

    // Get subscription details with plan features
    const subscriptionDetails = await subscriptionService.getSubscriptionDetails(subscriptionId);

    return NextResponse.json(subscriptionDetails);
  } catch (error) {
    logger.error("Error getting subscription details:", error);
    return NextResponse.json(
      { error: "Failed to get subscription details" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/subscriptions/[id]
 * Update subscription
 */
// Original PUT handler
async function putHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subscriptionId = params.id;

    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get subscription
    const subscription = await db.subscription.findUnique({
      where: { id: subscriptionId },
      include: { organization: true },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Check if user is an admin or organization admin
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" &&
                      session.user.organizationId === subscription.organizationId;

    if (!isAdmin && !isOrgAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { plan } = body;

    if (!plan) {
      return NextResponse.json(
        { error: "Plan is required" },
        { status: 400 }
      );
    }

    // Update subscription
    const updatedSubscription = await subscriptionService.updateSubscription(
      subscriptionId,
      plan,
      session.user.id
    );

    return NextResponse.json(updatedSubscription);
  } catch (error) {
    logger.error("Error updating subscription:", error);
    return NextResponse.json(
      { error: "Failed to update subscription" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/subscriptions/[id]
 * Cancel subscription
 */
// Original DELETE handler
async function deleteHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const subscriptionId = params.id;

    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get subscription
    const subscription = await db.subscription.findUnique({
      where: { id: subscriptionId },
      include: { organization: true },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Check if user is an admin or organization admin
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" &&
                      session.user.organizationId === subscription.organizationId;

    if (!isAdmin && !isOrgAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Cancel subscription
    const cancelledSubscription = await subscriptionService.cancelSubscription(
      subscriptionId,
      session.user.id
    );

    return NextResponse.json(cancelledSubscription);
  } catch (error) {
    logger.error("Error cancelling subscription:", error);
    return NextResponse.json(
      { error: "Failed to cancel subscription" },
      { status: 500 }
    );
  }
}

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('subscription', 'id')(getHandler);
export const PUT = withResourceIsolation('subscription', 'id')(putHandler);
