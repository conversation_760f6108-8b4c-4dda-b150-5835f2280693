import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for updating a marketplace listing
const updateListingSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional().nullable(),
  status: z.enum(["PENDING", "ACTIVE", "PAUSED", "SOLD", "EXPIRED", "CANCELLED"]).optional(),
  quantity: z.number().positive("Quantity must be positive").optional(),
  availableQuantity: z.number().positive("Available quantity must be positive").optional(),
  minPurchaseQuantity: z.number().positive().optional().nullable(),
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"]).optional(),
  price: z.number().positive("Price must be positive").optional().nullable(),
  auctionEndTime: z.string().datetime().optional().nullable(),
  auctionReservePrice: z.number().positive().optional().nullable(),
  auctionMinIncrement: z.number().positive().optional().nullable(),
  dynamicPricingRules: z.record(z.any()).optional().nullable(),
  tieredPricingRules: z.record(z.any()).optional().nullable(),
  visibility: z.enum(["PUBLIC", "PRIVATE", "INVITE_ONLY"]).optional(),
  featured: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional().nullable(),
});

/**
 * GET /api/marketplace/listings/[id]
 * Get a specific marketplace listing by ID
 */
async function getListingHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view marketplace listing details",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get the listing with related data
    const listing = await db.marketplaceListing.findUnique({
      where: {
        id: params.id,
      },
      include: {
        carbonCredit: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                type: true,
                description: true,
                location: true,
                country: true,
                standard: true,
                methodology: true,
                methodologyVersion: true,
                images: true,
              },
            },
            documents: true,
            complianceDocuments: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
            website: true,
            description: true,
            verificationStatus: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orders: {
          where: {
            status: {
              in: ["PENDING", "MATCHED"],
            },
          },
          select: {
            id: true,
            type: true,
            quantity: true,
            price: true,
            status: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        _count: {
          select: {
            orders: true,
            watchlistItems: true,
          },
        },
      },
    });

    if (!listing) {
      throw new ApiError(
        "Marketplace listing not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the listing is in the user's watchlist
    const watchlistItem = await db.watchlistItem.findFirst({
      where: {
        listingId: params.id,
        watchlist: {
          userId: session.user.id,
        },
      },
    });

    return NextResponse.json({
      listing,
      isInWatchlist: !!watchlistItem,
    });
  } catch (error) {
    logger.error(`Error fetching marketplace listing ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching the marketplace listing",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PATCH /api/marketplace/listings/[id]
 * Update a specific marketplace listing
 */
async function updateListingHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update a marketplace listing",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the listing exists and user has access
    let listingQuery: any = {
      where: {
        id: params.id,
      },
      include: {
        carbonCredit: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      listingQuery = withTenantIsolation(listingQuery, tenantContext);
    }

    const existingListing = await db.marketplaceListing.findUnique(listingQuery);

    if (!existingListing) {
      throw new ApiError(
        "Marketplace listing not found or you don't have permission to update it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const updateData = updateListingSchema.parse(body);

    // Validate pricing strategy data
    if (updateData.pricingStrategy === "FIXED" && updateData.price === undefined && !existingListing.price) {
      throw new ApiError(
        "Price is required for fixed pricing strategy",
        ErrorType.VALIDATION,
        400
      );
    }

    if (updateData.pricingStrategy === "AUCTION" && 
        !existingListing.auctionEndTime && !updateData.auctionEndTime) {
      throw new ApiError(
        "Auction end time is required for auction pricing strategy",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the quantity is available
    if (updateData.quantity !== undefined && 
        updateData.quantity > (existingListing.quantity - existingListing.availableQuantity + existingListing.carbonCredit.availableQuantity)) {
      throw new ApiError(
        "Updated quantity exceeds available carbon credit quantity",
        ErrorType.VALIDATION,
        400
      );
    }

    // Update the marketplace listing
    const updatedListing = await db.marketplaceListing.update({
      where: {
        id: params.id,
      },
      data: updateData,
    });

    logger.info(`User ${session.user.id} updated marketplace listing ${params.id}`);

    return NextResponse.json({
      listing: updatedListing,
      message: "Marketplace listing updated successfully",
    });
  } catch (error) {
    logger.error(`Error updating marketplace listing ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid listing data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while updating the marketplace listing",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/marketplace/listings/[id]
 * Delete a specific marketplace listing
 */
async function deleteListingHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to delete a marketplace listing",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the listing exists and user has access
    let listingQuery: any = {
      where: {
        id: params.id,
      },
      include: {
        carbonCredit: true,
        orders: {
          where: {
            status: {
              in: ["PENDING", "MATCHED"],
            },
          },
        },
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      listingQuery = withTenantIsolation(listingQuery, tenantContext);
    }

    const existingListing = await db.marketplaceListing.findUnique(listingQuery);

    if (!existingListing) {
      throw new ApiError(
        "Marketplace listing not found or you don't have permission to delete it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if there are active orders
    if (existingListing.orders.length > 0) {
      throw new ApiError(
        "Cannot delete a marketplace listing with active orders",
        ErrorType.VALIDATION,
        400
      );
    }

    // Delete the marketplace listing
    await db.marketplaceListing.delete({
      where: {
        id: params.id,
      },
    });

    // Update carbon credit status if this was the only listing
    const otherListings = await db.marketplaceListing.findMany({
      where: {
        carbonCreditId: existingListing.carbonCreditId,
        id: {
          not: params.id,
        },
      },
    });

    if (otherListings.length === 0) {
      await db.carbonCredit.update({
        where: {
          id: existingListing.carbonCreditId,
        },
        data: {
          status: "VERIFIED", // Revert to VERIFIED status
          listingDate: null,
        },
      });
    }

    logger.info(`User ${session.user.id} deleted marketplace listing ${params.id}`);

    return NextResponse.json({
      message: "Marketplace listing deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting marketplace listing ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while deleting the marketplace listing",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedGetHandler = withErrorHandling(getListingHandler);
const wrappedPatchHandler = withErrorHandling(updateListingHandler);
const wrappedDeleteHandler = withErrorHandling(deleteListingHandler);

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('marketplace_listing', 'id')(wrappedGetHandler);
export const PATCH = withResourceIsolation('marketplace_listing', 'id')(wrappedPatchHandler);
export const DELETE = withResourceIsolation('marketplace_listing', 'id')(wrappedDeleteHandler);
