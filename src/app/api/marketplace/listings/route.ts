import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";

// Schema for creating a marketplace listing
const createListingSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  carbonCreditId: z.string().uuid("Invalid carbon credit ID"),
  quantity: z.number().positive("Quantity must be positive"),
  minPurchaseQuantity: z.number().positive().optional(),
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"]).default("FIXED"),
  price: z.number().positive("Price must be positive").optional(),
  auctionEndTime: z.string().datetime().optional(),
  auctionReservePrice: z.number().positive().optional(),
  auctionMinIncrement: z.number().positive().optional(),
  dynamicPricingRules: z.record(z.any()).optional(),
  tieredPricingRules: z.record(z.any()).optional(),
  visibility: z.enum(["PUBLIC", "PRIVATE", "INVITE_ONLY"]).default("PUBLIC"),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for filtering marketplace listings
const listingFilterSchema = z.object({
  status: z.enum(["PENDING", "ACTIVE", "PAUSED", "SOLD", "EXPIRED", "CANCELLED"]).optional(),
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"]).optional(),
  visibility: z.enum(["PUBLIC", "PRIVATE", "INVITE_ONLY"]).optional(),
  featured: z.boolean().optional(),
  minPrice: z.number().positive().optional(),
  maxPrice: z.number().positive().optional(),
  minQuantity: z.number().positive().optional(),
  maxQuantity: z.number().positive().optional(),
  vintage: z.number().int().positive().optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  country: z.string().optional(),
  projectType: z.string().optional(),
  organizationId: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["price", "quantity", "createdAt", "updatedAt", "title"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
  page: z.number().int().positive().optional(),
  limit: z.number().int().positive().max(100).optional(),
});

/**
 * POST /api/marketplace/listings
 * Create a new marketplace listing
 */
async function createListingHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create a marketplace listing",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "You must be part of an organization to create a marketplace listing",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Parse request body
    const body = await req.json();
    const listingData = createListingSchema.parse(body);

    // Validate pricing strategy data
    if (listingData.pricingStrategy === "FIXED" && !listingData.price) {
      throw new ApiError(
        "Price is required for fixed pricing strategy",
        ErrorType.VALIDATION,
        400
      );
    }

    if (listingData.pricingStrategy === "AUCTION" &&
        (!listingData.auctionEndTime || !listingData.auctionReservePrice)) {
      throw new ApiError(
        "Auction end time and reserve price are required for auction pricing strategy",
        ErrorType.VALIDATION,
        400
      );
    }

    if (listingData.pricingStrategy === "DYNAMIC" && !listingData.dynamicPricingRules) {
      throw new ApiError(
        "Dynamic pricing rules are required for dynamic pricing strategy",
        ErrorType.VALIDATION,
        400
      );
    }

    if (listingData.pricingStrategy === "TIERED" && !listingData.tieredPricingRules) {
      throw new ApiError(
        "Tiered pricing rules are required for tiered pricing strategy",
        ErrorType.VALIDATION,
        400
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the carbon credit exists and user has access
    let carbonCreditQuery: any = {
      where: {
        id: listingData.carbonCreditId,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      carbonCreditQuery = withTenantIsolation(carbonCreditQuery, tenantContext);
    }

    const carbonCredit = await db.carbonCredit.findUnique(carbonCreditQuery);

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the carbon credit is verified
    if (carbonCredit.verificationStatus !== "VERIFIED") {
      throw new ApiError(
        "Carbon credit must be verified before listing on the marketplace",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the quantity is available
    if (listingData.quantity > carbonCredit.availableQuantity) {
      throw new ApiError(
        "Listing quantity exceeds available carbon credit quantity",
        ErrorType.VALIDATION,
        400
      );
    }

    // Create the marketplace listing
    const listing = await db.marketplaceListing.create({
      data: {
        title: listingData.title,
        description: listingData.description,
        status: "PENDING",
        quantity: listingData.quantity,
        availableQuantity: listingData.quantity,
        minPurchaseQuantity: listingData.minPurchaseQuantity,
        pricingStrategy: listingData.pricingStrategy,
        price: listingData.price,
        auctionEndTime: listingData.auctionEndTime ? new Date(listingData.auctionEndTime) : undefined,
        auctionReservePrice: listingData.auctionReservePrice,
        auctionMinIncrement: listingData.auctionMinIncrement,
        dynamicPricingRules: listingData.dynamicPricingRules,
        tieredPricingRules: listingData.tieredPricingRules,
        visibility: listingData.visibility,
        featured: listingData.featured,
        tags: listingData.tags || [],
        metadata: listingData.metadata,
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: {
          connect: {
            id: session.user.organizationId,
          },
        },
        carbonCredit: {
          connect: {
            id: listingData.carbonCreditId,
          },
        },
      },
    });

    // Update carbon credit status to LISTED
    await db.carbonCredit.update({
      where: {
        id: listingData.carbonCreditId,
      },
      data: {
        status: "LISTED",
        listingDate: new Date(),
      },
    });

    logger.info(`User ${session.user.id} created marketplace listing ${listing.id}`);

    return NextResponse.json({
      listing,
      message: "Marketplace listing created successfully",
    });
  } catch (error) {
    logger.error("Error creating marketplace listing:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid listing data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while creating the marketplace listing",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/marketplace/listings
 * Get marketplace listings with filtering and pagination
 */
async function getListingsHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view marketplace listings",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams: Record<string, any> = {};

    url.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });

    // Convert numeric parameters
    if (queryParams.page) queryParams.page = parseInt(queryParams.page);
    if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);
    if (queryParams.minPrice) queryParams.minPrice = parseFloat(queryParams.minPrice);
    if (queryParams.maxPrice) queryParams.maxPrice = parseFloat(queryParams.maxPrice);
    if (queryParams.minQuantity) queryParams.minQuantity = parseFloat(queryParams.minQuantity);
    if (queryParams.maxQuantity) queryParams.maxQuantity = parseFloat(queryParams.maxQuantity);
    if (queryParams.vintage) queryParams.vintage = parseInt(queryParams.vintage);
    if (queryParams.featured) queryParams.featured = queryParams.featured === 'true';

    // Parse and validate filters
    const filters = listingFilterSchema.parse(queryParams);

    // Build the where clause for filtering
    const where: any = {
      // By default, only show active listings
      status: filters.status || "ACTIVE",
    };

    if (filters.pricingStrategy) where.pricingStrategy = filters.pricingStrategy;
    if (filters.visibility) where.visibility = filters.visibility;
    if (filters.featured !== undefined) where.featured = filters.featured;

    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      where.price = {};
      if (filters.minPrice !== undefined) where.price.gte = filters.minPrice;
      if (filters.maxPrice !== undefined) where.price.lte = filters.maxPrice;
    }

    if (filters.minQuantity !== undefined || filters.maxQuantity !== undefined) {
      where.availableQuantity = {};
      if (filters.minQuantity !== undefined) where.availableQuantity.gte = filters.minQuantity;
      if (filters.maxQuantity !== undefined) where.availableQuantity.lte = filters.maxQuantity;
    }

    if (filters.organizationId) where.organizationId = filters.organizationId;

    // Add carbon credit specific filters
    if (filters.vintage || filters.standard || filters.methodology || filters.country || filters.projectType) {
      where.carbonCredit = {};

      if (filters.vintage) where.carbonCredit.vintage = filters.vintage;
      if (filters.standard) where.carbonCredit.standard = filters.standard;
      if (filters.methodology) where.carbonCredit.methodology = filters.methodology;
      if (filters.country) where.carbonCredit.country = filters.country;

      if (filters.projectType) {
        where.carbonCredit.project = {
          type: filters.projectType,
        };
      }
    }

    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { carbonCredit: { name: { contains: filters.search, mode: 'insensitive' } } },
        { carbonCredit: { project: { name: { contains: filters.search, mode: 'insensitive' } } } },
        { organization: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }

    // Create base query
    let listingsQuery: any = {
      where,
      include: {
        carbonCredit: {
          select: {
            id: true,
            name: true,
            vintage: true,
            standard: true,
            methodology: true,
            country: true,
            project: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            orders: true,
            watchlistItems: true,
          },
        },
      },
      orderBy: {},
    };

    // Add sorting
    if (filters.sortBy) {
      listingsQuery.orderBy[filters.sortBy] = filters.sortOrder || 'desc';
    } else {
      listingsQuery.orderBy.createdAt = 'desc';
    }

    // Add pagination
    if (filters.page && filters.limit) {
      listingsQuery.skip = (filters.page - 1) * filters.limit;
      listingsQuery.take = filters.limit;
    } else {
      listingsQuery.take = filters.limit || 10;
    }

    // Get listings count for pagination
    const totalCount = await db.marketplaceListing.count({
      where: listingsQuery.where,
    });

    // Get listings
    const listings = await db.marketplaceListing.findMany(listingsQuery);

    // Get unique filter options for dropdowns
    const [standards, methodologies, countries, projectTypes] = await Promise.all([
      db.carbonCredit.groupBy({
        by: ['standard'],
        where: {
          marketplaceListings: {
            some: {
              status: "ACTIVE",
            },
          },
        },
      }),
      db.carbonCredit.groupBy({
        by: ['methodology'],
        where: {
          marketplaceListings: {
            some: {
              status: "ACTIVE",
            },
          },
        },
      }),
      db.carbonCredit.groupBy({
        by: ['country'],
        where: {
          marketplaceListings: {
            some: {
              status: "ACTIVE",
            },
          },
        },
      }),
      db.project.groupBy({
        by: ['type'],
        where: {
          carbonCredits: {
            some: {
              marketplaceListings: {
                some: {
                  status: "ACTIVE",
                },
              },
            },
          },
        },
      }),
    ]);

    return NextResponse.json({
      listings,
      filters: {
        standards: standards.map(s => s.standard).filter(Boolean),
        methodologies: methodologies.map(m => m.methodology).filter(Boolean),
        countries: countries.map(c => c.country).filter(Boolean),
        projectTypes: projectTypes.map(p => p.type),
      },
      pagination: {
        total: totalCount,
        page: filters.page || 1,
        limit: filters.limit || 10,
        pages: Math.ceil(totalCount / (filters.limit || 10)),
      },
    });
  } catch (error) {
    logger.error("Error fetching marketplace listings:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid filter parameters",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while fetching marketplace listings",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(createListingHandler);
const wrappedGetHandler = withErrorHandling(getListingsHandler);

// Export the handlers with tenant isolation middleware
export const POST = withTenantIsolation(wrappedPostHandler);
export const GET = withTenantIsolation(wrappedGetHandler);
