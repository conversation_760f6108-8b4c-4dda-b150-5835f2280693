import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";

// Schema for creating a watchlist
const createWatchlistSchema = z.object({
  name: z.string().min(1, "Watchlist name is required"),
  description: z.string().optional(),
  organizationId: z.string().optional(),
});

/**
 * POST /api/marketplace/watchlist
 * Create a new watchlist
 */
async function createWatchlistHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create a watchlist",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Parse request body
    const body = await req.json();
    const watchlistData = createWatchlistSchema.parse(body);

    // If organizationId is provided, check if user belongs to that organization
    if (watchlistData.organizationId && watchlistData.organizationId !== session.user.organizationId) {
      throw new ApiError(
        "You can only create watchlists for your own organization",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Create the watchlist
    const watchlist = await db.marketplaceWatchlist.create({
      data: {
        name: watchlistData.name,
        description: watchlistData.description,
        user: {
          connect: {
            id: session.user.id,
          },
        },
        organization: watchlistData.organizationId ? {
          connect: {
            id: watchlistData.organizationId,
          },
        } : undefined,
      },
    });

    logger.info(`User ${session.user.id} created watchlist ${watchlist.id}`);

    return NextResponse.json({
      watchlist,
      message: "Watchlist created successfully",
    });
  } catch (error) {
    logger.error("Error creating watchlist:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid watchlist data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while creating the watchlist",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * GET /api/marketplace/watchlist
 * Get user's watchlists
 */
async function getWatchlistsHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view watchlists",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query
    let watchlistsQuery: any = {
      where: {
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
      include: {
        _count: {
          select: {
            watchlistItems: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    };

    // Get watchlists
    const watchlists = await db.marketplaceWatchlist.findMany(watchlistsQuery);

    return NextResponse.json({ watchlists });
  } catch (error) {
    logger.error("Error fetching watchlists:", error);

    throw new ApiError(
      "An error occurred while fetching watchlists",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(createWatchlistHandler);
const wrappedGetHandler = withErrorHandling(getWatchlistsHandler);

// Export the handlers with tenant isolation middleware
export const POST = withTenantIsolation(wrappedPostHandler);
export const GET = withTenantIsolation(wrappedGetHandler);
