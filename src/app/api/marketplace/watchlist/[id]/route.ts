import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for updating a watchlist
const updateWatchlistSchema = z.object({
  name: z.string().min(1, "Watchlist name is required").optional(),
  description: z.string().optional().nullable(),
});

/**
 * GET /api/marketplace/watchlist/[id]
 * Get a specific watchlist with its items
 */
async function getWatchlistHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view watchlist details",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the watchlist exists and user has access
    let watchlistQuery: any = {
      where: {
        id: params.id,
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
      include: {
        watchlistItems: {
          include: {
            listing: {
              include: {
                carbonCredit: {
                  select: {
                    id: true,
                    name: true,
                    vintage: true,
                    standard: true,
                    methodology: true,
                    project: {
                      select: {
                        id: true,
                        name: true,
                        type: true,
                      },
                    },
                  },
                },
                organization: {
                  select: {
                    id: true,
                    name: true,
                    logo: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    };

    const watchlist = await db.marketplaceWatchlist.findUnique(watchlistQuery);

    if (!watchlist) {
      throw new ApiError(
        "Watchlist not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    return NextResponse.json({ watchlist });
  } catch (error) {
    logger.error(`Error fetching watchlist ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while fetching the watchlist",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PATCH /api/marketplace/watchlist/[id]
 * Update a specific watchlist
 */
async function updateWatchlistHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update a watchlist",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the watchlist exists and user has access
    let watchlistQuery: any = {
      where: {
        id: params.id,
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
    };

    const existingWatchlist = await db.marketplaceWatchlist.findUnique(watchlistQuery);

    if (!existingWatchlist) {
      throw new ApiError(
        "Watchlist not found or you don't have permission to update it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const updateData = updateWatchlistSchema.parse(body);

    // Update the watchlist
    const updatedWatchlist = await db.marketplaceWatchlist.update({
      where: {
        id: params.id,
      },
      data: updateData,
    });

    logger.info(`User ${session.user.id} updated watchlist ${params.id}`);

    return NextResponse.json({
      watchlist: updatedWatchlist,
      message: "Watchlist updated successfully",
    });
  } catch (error) {
    logger.error(`Error updating watchlist ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid watchlist data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while updating the watchlist",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/marketplace/watchlist/[id]
 * Delete a specific watchlist
 */
async function deleteWatchlistHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to delete a watchlist",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the watchlist exists and user has access
    let watchlistQuery: any = {
      where: {
        id: params.id,
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
    };

    const existingWatchlist = await db.marketplaceWatchlist.findUnique(watchlistQuery);

    if (!existingWatchlist) {
      throw new ApiError(
        "Watchlist not found or you don't have permission to delete it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Delete the watchlist (cascade will delete watchlist items)
    await db.marketplaceWatchlist.delete({
      where: {
        id: params.id,
      },
    });

    logger.info(`User ${session.user.id} deleted watchlist ${params.id}`);

    return NextResponse.json({
      message: "Watchlist deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting watchlist ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while deleting the watchlist",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedGetHandler = withErrorHandling(getWatchlistHandler);
const wrappedPatchHandler = withErrorHandling(updateWatchlistHandler);
const wrappedDeleteHandler = withErrorHandling(deleteWatchlistHandler);

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('marketplace_watchlist', 'id')(wrappedGetHandler);
export const PATCH = withResourceIsolation('marketplace_watchlist', 'id')(wrappedPatchHandler);
export const DELETE = withResourceIsolation('marketplace_watchlist', 'id')(wrappedDeleteHandler);
