import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for adding an item to a watchlist
const addWatchlistItemSchema = z.object({
  listingId: z.string().uuid("Invalid listing ID"),
  priceAlertEnabled: z.boolean().default(false),
  priceAlertThreshold: z.number().positive().optional(),
  priceAlertDirection: z.enum(["ABOVE", "BELOW", "BOTH"]).optional(),
  notes: z.string().optional(),
});

// Schema for updating a watchlist item
const updateWatchlistItemSchema = z.object({
  itemId: z.string().uuid("Invalid item ID"),
  priceAlertEnabled: z.boolean().optional(),
  priceAlertThreshold: z.number().positive().optional().nullable(),
  priceAlertDirection: z.enum(["ABOVE", "BELOW", "BOTH"]).optional().nullable(),
  notes: z.string().optional().nullable(),
});

/**
 * POST /api/marketplace/watchlist/[id]/items
 * Add an item to a watchlist
 */
async function addWatchlistItemHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to add items to a watchlist",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the watchlist exists and user has access
    let watchlistQuery: any = {
      where: {
        id: params.id,
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
    };

    const watchlist = await db.marketplaceWatchlist.findUnique(watchlistQuery);

    if (!watchlist) {
      throw new ApiError(
        "Watchlist not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const itemData = addWatchlistItemSchema.parse(body);

    // Check if the listing exists
    const listing = await db.marketplaceListing.findUnique({
      where: {
        id: itemData.listingId,
      },
    });

    if (!listing) {
      throw new ApiError(
        "Marketplace listing not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the item already exists in the watchlist
    const existingItem = await db.watchlistItem.findFirst({
      where: {
        watchlistId: params.id,
        listingId: itemData.listingId,
      },
    });

    if (existingItem) {
      throw new ApiError(
        "This listing is already in the watchlist",
        ErrorType.VALIDATION,
        400
      );
    }

    // Validate price alert data
    if (itemData.priceAlertEnabled && (!itemData.priceAlertThreshold || !itemData.priceAlertDirection)) {
      throw new ApiError(
        "Price alert threshold and direction are required when price alerts are enabled",
        ErrorType.VALIDATION,
        400
      );
    }

    // Create the watchlist item
    const watchlistItem = await db.watchlistItem.create({
      data: {
        priceAlertEnabled: itemData.priceAlertEnabled,
        priceAlertThreshold: itemData.priceAlertEnabled ? itemData.priceAlertThreshold : undefined,
        priceAlertDirection: itemData.priceAlertEnabled ? itemData.priceAlertDirection : undefined,
        notes: itemData.notes,
        watchlist: {
          connect: {
            id: params.id,
          },
        },
        listing: {
          connect: {
            id: itemData.listingId,
          },
        },
      },
    });

    logger.info(`User ${session.user.id} added item ${watchlistItem.id} to watchlist ${params.id}`);

    return NextResponse.json({
      watchlistItem,
      message: "Item added to watchlist successfully",
    });
  } catch (error) {
    logger.error(`Error adding item to watchlist ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid watchlist item data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while adding the item to the watchlist",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PATCH /api/marketplace/watchlist/[id]/items
 * Update a watchlist item
 */
async function updateWatchlistItemHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update watchlist items",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the watchlist exists and user has access
    let watchlistQuery: any = {
      where: {
        id: params.id,
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
    };

    const watchlist = await db.marketplaceWatchlist.findUnique(watchlistQuery);

    if (!watchlist) {
      throw new ApiError(
        "Watchlist not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const updateData = updateWatchlistItemSchema.parse(body);

    // Check if the item exists in the watchlist
    const existingItem = await db.watchlistItem.findFirst({
      where: {
        id: updateData.itemId,
        watchlistId: params.id,
      },
    });

    if (!existingItem) {
      throw new ApiError(
        "Watchlist item not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Validate price alert data
    if (updateData.priceAlertEnabled === true && 
        (!updateData.priceAlertThreshold || !updateData.priceAlertDirection) && 
        (!existingItem.priceAlertThreshold || !existingItem.priceAlertDirection)) {
      throw new ApiError(
        "Price alert threshold and direction are required when price alerts are enabled",
        ErrorType.VALIDATION,
        400
      );
    }

    // Update the watchlist item
    const updatedItem = await db.watchlistItem.update({
      where: {
        id: updateData.itemId,
      },
      data: {
        priceAlertEnabled: updateData.priceAlertEnabled,
        priceAlertThreshold: updateData.priceAlertThreshold,
        priceAlertDirection: updateData.priceAlertDirection,
        notes: updateData.notes,
      },
    });

    logger.info(`User ${session.user.id} updated item ${updateData.itemId} in watchlist ${params.id}`);

    return NextResponse.json({
      watchlistItem: updatedItem,
      message: "Watchlist item updated successfully",
    });
  } catch (error) {
    logger.error(`Error updating item in watchlist ${params.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid watchlist item data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while updating the watchlist item",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/marketplace/watchlist/[id]/items
 * Remove an item from a watchlist
 */
async function deleteWatchlistItemHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to remove items from a watchlist",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the watchlist exists and user has access
    let watchlistQuery: any = {
      where: {
        id: params.id,
        OR: [
          { userId: session.user.id },
          { organizationId: session.user.organizationId },
        ],
      },
    };

    const watchlist = await db.marketplaceWatchlist.findUnique(watchlistQuery);

    if (!watchlist) {
      throw new ApiError(
        "Watchlist not found or you don't have permission to access it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse URL parameters
    const url = new URL(req.url);
    const itemId = url.searchParams.get('itemId');

    if (!itemId) {
      throw new ApiError(
        "Item ID is required",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the item exists in the watchlist
    const existingItem = await db.watchlistItem.findFirst({
      where: {
        id: itemId,
        watchlistId: params.id,
      },
    });

    if (!existingItem) {
      throw new ApiError(
        "Watchlist item not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Delete the watchlist item
    await db.watchlistItem.delete({
      where: {
        id: itemId,
      },
    });

    logger.info(`User ${session.user.id} removed item ${itemId} from watchlist ${params.id}`);

    return NextResponse.json({
      message: "Item removed from watchlist successfully",
    });
  } catch (error) {
    logger.error(`Error removing item from watchlist ${params.id}:`, error);

    throw new ApiError(
      "An error occurred while removing the item from the watchlist",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedPostHandler = withErrorHandling(addWatchlistItemHandler);
const wrappedPatchHandler = withErrorHandling(updateWatchlistItemHandler);
const wrappedDeleteHandler = withErrorHandling(deleteWatchlistItemHandler);

// Export the handlers with resource isolation middleware
export const POST = withResourceIsolation('marketplace_watchlist', 'id')(wrappedPostHandler);
export const PATCH = withResourceIsolation('marketplace_watchlist', 'id')(wrappedPatchHandler);
export const DELETE = withResourceIsolation('marketplace_watchlist', 'id')(wrappedDeleteHandler);
