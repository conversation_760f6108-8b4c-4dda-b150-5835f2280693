import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceManager } from "@/lib/compliance";
import { db } from "@/lib/db";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";

/**
 * GET /api/compliance/kyc/status
 * Get KYC status for the current user
 */
async function getKycStatusHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to check KYC status",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get("userId") || session.user.id;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user is authorized to check KYC status for this user
    if (userId !== session.user.id && !tenantContext.isAdmin) {
      // Check if the target user belongs to the same organization
      const targetUser = await db.user.findUnique({
        where: { id: userId },
        select: { organizationId: true },
      });

      if (!targetUser || targetUser.organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to check KYC status for user ${userId} from another organization`);
        throw new ApiError(
          "You are not authorized to check KYC status for this user",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Get KYC status
    const status = await ComplianceManager.getKycStatus(userId);

    return NextResponse.json(status);
  } catch (error) {
    logger.error("Error getting KYC status:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while getting KYC status",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(getKycStatusHandler);

export const GET = withTenantIsolation(wrappedHandler);
