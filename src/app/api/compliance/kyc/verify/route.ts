import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceManager } from "@/lib/compliance";
import { KycLevel, ComplianceDocumentType, ComplianceStatus } from "@/lib/compliance/types";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

/**
 * POST /api/compliance/kyc/verify
 * Submit KYC verification
 */
async function verifyKycHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to submit KYC verification",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const body = await req.json();
    const { userId, organizationId, level, documents } = body;

    // Validate request
    if (!userId) {
      throw new ApiError("User ID is required", ErrorType.VALIDATION, 400);
    }

    if (!organizationId) {
      throw new ApiError("Organization ID is required", ErrorType.VALIDATION, 400);
    }

    if (!level || !Object.values(KycLevel).includes(level)) {
      throw new ApiError("Valid KYC level is required", ErrorType.VALIDATION, 400);
    }

    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      throw new ApiError("Documents are required", ErrorType.VALIDATION, 400);
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user is authorized to submit KYC for this user and organization
    if (session.user.id !== userId && !tenantContext.isAdmin) {
      // Check if the target user belongs to the same organization
      const targetUser = await db.user.findUnique({
        where: { id: userId },
        select: { organizationId: true },
      });

      if (!targetUser || targetUser.organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to submit KYC verification for user ${userId} from another organization`);
        throw new ApiError(
          "You are not authorized to submit KYC verification for this user",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Check if organization belongs to the user's organization
    if (organizationId && organizationId !== tenantContext.organizationId && !tenantContext.isAdmin) {
      logger.warn(`User ${session.user.id} attempted to submit KYC verification for organization ${organizationId} they don't belong to`);
      throw new ApiError(
        "You are not authorized to submit KYC verification for this organization",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Validate documents
    for (const doc of documents) {
      if (!doc.type || !doc.name || !doc.url) {
        throw new ApiError(
          "Each document must have type, name, and url",
          ErrorType.VALIDATION,
          400
        );
      }
    }

    // Submit KYC verification
    const result = await ComplianceManager.performKycVerification(
      userId,
      organizationId,
      level as KycLevel,
      documents.map(doc => ({
        type: doc.type as ComplianceDocumentType,
        name: doc.name,
        url: doc.url,
        status: ComplianceStatus.APPROVED,
      }))
    );

    return NextResponse.json(result);
  } catch (error) {
    logger.error("Error submitting KYC verification:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while submitting KYC verification",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(verifyKycHandler);

export const POST = withTenantIsolation(wrappedHandler);
