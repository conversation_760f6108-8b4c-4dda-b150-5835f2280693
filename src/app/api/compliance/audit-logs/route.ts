import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceAuditService } from "@/lib/audit/compliance-audit";
import { AuditLogType } from "@prisma/client";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

/**
 * GET /api/compliance/audit-logs
 * Get compliance audit logs
 */
async function getComplianceAuditLogsHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view compliance audit logs",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get("userId") || undefined;
    const organizationId = searchParams.get("organizationId") || undefined;
    const startDateStr = searchParams.get("startDate") || undefined;
    const endDateStr = searchParams.get("endDate") || undefined;
    const typesParam = searchParams.getAll("types") || [];
    const limitStr = searchParams.get("limit") || "50";
    const offsetStr = searchParams.get("offset") || "0";
    const search = searchParams.get("search") || undefined;
    const pageStr = searchParams.get("page") || "1";

    // Parse parameters
    const limit = parseInt(limitStr);
    const page = parseInt(pageStr);
    const offset = (page - 1) * limit || parseInt(offsetStr);
    const startDate = startDateStr ? new Date(startDateStr) : undefined;
    const endDate = endDateStr ? new Date(endDateStr) : undefined;

    // Validate types
    const types = typesParam.length > 0
      ? typesParam.filter(type => Object.values(AuditLogType).includes(type as AuditLogType)) as AuditLogType[]
      : undefined;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user is authorized to view these logs
    const isAdmin = tenantContext.isAdmin;
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

    // Regular users can only view their own logs
    if (!isAdmin && !isOrgAdmin) {
      if (userId && userId !== session.user.id) {
        // Check if the target user belongs to the same organization
        const targetUser = await db.user.findUnique({
          where: { id: userId },
          select: { organizationId: true },
        });

        if (!targetUser || targetUser.organizationId !== tenantContext.organizationId) {
          logger.warn(`User ${session.user.id} attempted to view audit logs for user ${userId} from another organization`);
          throw new ApiError(
            "You are not authorized to view audit logs for this user",
            ErrorType.AUTHORIZATION,
            403
          );
        }
      }

      if (organizationId && organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to view audit logs for organization ${organizationId} they don't belong to`);
        throw new ApiError(
          "You are not authorized to view audit logs for this organization",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Organization admins can only view logs for their organization
    if (isOrgAdmin && !isAdmin) {
      if (organizationId && organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to view audit logs for organization ${organizationId} they don't belong to`);
        throw new ApiError(
          "You are not authorized to view audit logs for this organization",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Set effective user ID and organization ID
    const effectiveUserId = userId || ((!isAdmin && !isOrgAdmin) ? session.user.id : undefined);
    const effectiveOrgId = organizationId || (isOrgAdmin && !isAdmin ? session.user.organizationId : undefined);

    // Get compliance audit logs
    const result = await ComplianceAuditService.getComplianceAuditLogs({
      userId: effectiveUserId,
      organizationId: effectiveOrgId,
      startDate,
      endDate,
      types,
      limit,
      offset,
    });

    return NextResponse.json(result);
  } catch (error) {
    logger.error("Error getting compliance audit logs:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while getting compliance audit logs",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(getComplianceAuditLogsHandler);

export const GET = withTenantIsolation(wrappedHandler);
