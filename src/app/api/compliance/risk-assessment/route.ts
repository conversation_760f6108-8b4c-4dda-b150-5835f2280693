import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";
import { KycService } from "@/lib/compliance/kyc";
import { ComplianceRiskLevel } from "@/lib/compliance/types";

// Schema for risk assessment request
const riskAssessmentSchema = z.object({
  userId: z.string().optional(),
  organizationId: z.string().optional(),
});

// Schema for sanctions and PEP check request
const sanctionsCheckSchema = z.object({
  userId: z.string().optional(),
  organizationId: z.string().optional(),
});

/**
 * POST /api/compliance/risk-assessment
 * Perform risk assessment for a user or organization
 */
async function handleRiskAssessment(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to perform risk assessment",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  // Parse and validate request body
  const body = await req.json().catch(() => ({}));
  const action = req.nextUrl.searchParams.get("action") || "assess";

  try {
    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user has permission to perform risk assessment
    const isAdmin = tenantContext.isAdmin || session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";
    const isComplianceOfficer = session.user.role === "COMPLIANCE_OFFICER";

    if (!isAdmin && !isComplianceOfficer) {
      throw new ApiError(
        "You do not have permission to perform risk assessment",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    switch (action) {
      case "assess": {
        const { userId, organizationId } = riskAssessmentSchema.parse(body);

        // Use the authenticated user's ID if not provided
        const targetUserId = userId || session.user.id;
        
        // Use the tenant's organization ID if not provided
        const targetOrgId = organizationId || tenantContext.organizationId;

        // Check if user has permission to assess the target user
        if (targetUserId !== session.user.id && !isAdmin && !isComplianceOfficer) {
          throw new ApiError(
            "You do not have permission to perform risk assessment for this user",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Check if user has permission to assess the target organization
        if (targetOrgId !== tenantContext.organizationId && !isAdmin && !isComplianceOfficer) {
          throw new ApiError(
            "You do not have permission to perform risk assessment for this organization",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Perform risk assessment
        const result = await KycService.performRiskAssessment(targetUserId, targetOrgId);

        logger.info(`Risk assessment performed for user ${targetUserId}, organization ${targetOrgId}: ${result.riskLevel} (${result.riskScore})`);

        return NextResponse.json({
          success: true,
          result,
          message: `Risk assessment completed with ${result.riskLevel} risk level`,
        });
      }

      case "sanctions": {
        const { userId, organizationId } = sanctionsCheckSchema.parse(body);

        // Use the authenticated user's ID if not provided
        const targetUserId = userId || session.user.id;
        
        // Use the tenant's organization ID if not provided
        const targetOrgId = organizationId || tenantContext.organizationId;

        // Check if user has permission to check the target user
        if (targetUserId !== session.user.id && !isAdmin && !isComplianceOfficer) {
          throw new ApiError(
            "You do not have permission to perform sanctions check for this user",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Check if user has permission to check the target organization
        if (targetOrgId !== tenantContext.organizationId && !isAdmin && !isComplianceOfficer) {
          throw new ApiError(
            "You do not have permission to perform sanctions check for this organization",
            ErrorType.AUTHORIZATION,
            403
          );
        }

        // Perform sanctions and PEP check
        const result = await KycService.verifySanctionsAndPep(targetUserId, targetOrgId);

        logger.info(`Sanctions and PEP check performed for user ${targetUserId}, organization ${targetOrgId}: ${result.passed ? 'Passed' : 'Failed'} with ${result.matches.length} matches`);

        return NextResponse.json({
          success: true,
          result,
          message: result.passed 
            ? "No significant sanctions or PEP matches found" 
            : `Found ${result.matches.length} potential matches in sanctions or PEP lists`,
        });
      }

      default:
        throw new ApiError(`Invalid action: ${action}`, ErrorType.VALIDATION, 400);
    }
  } catch (error) {
    logger.error(`Error handling risk assessment (${action}):`, error);
    throw error;
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(handleRiskAssessment);

export const POST = withTenantIsolation(wrappedHandler);

/**
 * GET /api/compliance/risk-assessment
 * Get risk assessment summary for a user or organization
 */
async function getRiskAssessmentSummary(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view risk assessment data",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Get query parameters
    const userId = req.nextUrl.searchParams.get("userId") || session.user.id;
    const organizationId = req.nextUrl.searchParams.get("organizationId") || tenantContext.organizationId;

    // Check if user has permission to view the target user's risk assessment
    const isAdmin = tenantContext.isAdmin || session.user.role === "ADMIN" || session.user.role === "ORGANIZATION_ADMIN";
    const isComplianceOfficer = session.user.role === "COMPLIANCE_OFFICER";
    const isSelf = userId === session.user.id;

    if (!isSelf && !isAdmin && !isComplianceOfficer) {
      throw new ApiError(
        "You do not have permission to view risk assessment data for this user",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Get the latest risk assessment
    const latestRiskAssessment = await db.complianceCheck.findFirst({
      where: {
        userId,
        organizationId,
        type: "RISK_ASSESSMENT",
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get the latest sanctions check
    const latestSanctionsCheck = await db.complianceCheck.findFirst({
      where: {
        userId,
        organizationId,
        type: "SANCTIONS_PEP",
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get overall risk level
    let overallRiskLevel = ComplianceRiskLevel.LOW;
    
    if (latestRiskAssessment?.riskLevel === ComplianceRiskLevel.HIGH || 
        latestSanctionsCheck?.riskLevel === ComplianceRiskLevel.HIGH) {
      overallRiskLevel = ComplianceRiskLevel.HIGH;
    } else if (latestRiskAssessment?.riskLevel === ComplianceRiskLevel.MEDIUM || 
               latestSanctionsCheck?.riskLevel === ComplianceRiskLevel.MEDIUM) {
      overallRiskLevel = ComplianceRiskLevel.MEDIUM;
    }

    return NextResponse.json({
      success: true,
      riskAssessment: latestRiskAssessment ? {
        riskLevel: latestRiskAssessment.riskLevel,
        details: latestRiskAssessment.details,
        createdAt: latestRiskAssessment.createdAt,
      } : null,
      sanctionsCheck: latestSanctionsCheck ? {
        result: latestSanctionsCheck.result,
        details: latestSanctionsCheck.details,
        createdAt: latestSanctionsCheck.createdAt,
      } : null,
      overallRiskLevel,
    });
  } catch (error) {
    logger.error(`Error getting risk assessment summary:`, error);
    throw error;
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedGetHandler = withErrorHandling(getRiskAssessmentSummary);

export const GET = withTenantIsolation(wrappedGetHandler);
