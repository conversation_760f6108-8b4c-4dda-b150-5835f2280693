/**
 * Compliance report data model
 */
export interface ComplianceReportData {
  reportType: string;
  name: string;
  description?: string;
  startDate: string;
  endDate: string;
  filters?: any;
  format?: string;
}

/**
 * Compliance report response model
 */
export interface ComplianceReportResponse {
  id: string;
  name: string;
  description?: string;
  type: string;
  format: string;
  startDate: Date;
  endDate: Date;
  filters?: any;
  data?: any;
  url?: string;
  generatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  organizationId?: string;
}

/**
 * KYC verification summary
 */
export interface KycVerificationSummary {
  total: number;
  approved: number;
  pending: number;
  rejected: number;
  expired: number;
  requiresUpdate: number;
}

/**
 * AML check summary
 */
export interface AmlCheckSummary {
  total: number;
  approved: number;
  pending: number;
  rejected: number;
  lowRisk: number;
  mediumRisk: number;
  highRisk: number;
  criticalRisk: number;
  alertCount: number;
}

/**
 * Carbon credit verification summary
 */
export interface CarbonCreditVerificationSummary {
  total: number;
  verified: number;
  pending: number;
  rejected: number;
  pendingUpdate: number;
}

/**
 * Audit log summary
 */
export interface AuditLogSummary {
  total: number;
  uniqueUsers: number;
  typeCounts: {
    type: string;
    count: number;
  }[];
}

/**
 * Compliance report data by type
 */
export interface ComplianceReportData {
  kycVerifications?: any[];
  amlChecks?: any[];
  carbonCreditVerifications?: any[];
  auditLogs?: any[];
  logsByType?: Record<string, any[]>;
  summary: KycVerificationSummary | AmlCheckSummary | CarbonCreditVerificationSummary | AuditLogSummary;
}
