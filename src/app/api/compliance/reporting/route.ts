import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for generating a compliance report
const generateReportSchema = z.object({
  reportType: z.enum([
    "KYC",
    "AML",
    "VERIFICATION",
    "AUDIT",
    "CUSTOM",
  ]),
  name: z.string().min(1, "Report name is required"),
  description: z.string().optional(),
  startDate: z.string(), // ISO date string
  endDate: z.string(), // ISO date string
  filters: z.record(z.any()).optional(),
  format: z.enum(["PDF", "CSV", "JSON"]).default("PDF"),
});

// GET handler for compliance reports
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access compliance reports" },
        { status: 401 }
      );
    }

    // Check if user has compliance officer or admin role
    const userRoles = await db.userRole.findMany({
      where: {
        userId: session.user.id,
        role: {
          name: {
            in: ["ADMIN", "PLATFORM_ADMIN", "COMPLIANCE_OFFICER"],
          },
        },
      },
    });

    const isComplianceOfficer = userRoles.length > 0;
    const isOrganizationAdmin = session.user.organizationId && (await db.userRole.findFirst({
      where: {
        userId: session.user.id,
        role: {
          name: "ORGANIZATION_ADMIN",
        },
      },
    }));

    if (!isComplianceOfficer && !isOrganizationAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to access compliance reports" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(req.url);
    const reportType = searchParams.get("reportType");
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20;
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Build filter
    const filter: any = {};

    if (reportType) {
      filter.type = reportType;
    }

    // For organization admins, only show reports for their organization
    if (isOrganizationAdmin && !isComplianceOfficer) {
      filter.organizationId = session.user.organizationId;
    }

    // Apply tenant isolation
    let reportsQuery = {
      where: filter,
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    };

    reportsQuery = withTenantQuery(reportsQuery, tenantContext);

    // Get reports
    const reports = await db.complianceReport.findMany(reportsQuery);

    // Get total count
    const totalCount = await db.complianceReport.count({
      where: filter,
    });

    return NextResponse.json({
      reports,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + reports.length < totalCount,
      },
    });
  } catch (error) {
    logger.error("Error fetching compliance reports:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching compliance reports" },
      { status: 500 }
    );
  }
}

// POST handler for generating a compliance report
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to generate a compliance report" },
        { status: 401 }
      );
    }

    // Check if user has compliance officer or admin role
    const userRoles = await db.userRole.findMany({
      where: {
        userId: session.user.id,
        role: {
          name: {
            in: ["ADMIN", "PLATFORM_ADMIN", "COMPLIANCE_OFFICER", "ORGANIZATION_ADMIN"],
          },
        },
      },
    });

    if (userRoles.length === 0) {
      return NextResponse.json(
        { error: "You do not have permission to generate compliance reports" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { reportType, name, description, startDate, endDate, filters, format } = generateReportSchema.parse(body);

    // Generate report data based on report type
    let reportData: any = {};

    if (reportType === "KYC") {
      // Get KYC verification data
      const kycVerifications = await db.kycVerification.findMany({
        where: {
          ...(session.user.organizationId ? { organizationId: session.user.organizationId } : {}),
          updatedAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
          ...(filters?.status ? { status: filters.status } : {}),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          documents: true,
          verificationHistory: true,
        },
      });

      reportData = {
        kycVerifications,
        summary: {
          total: kycVerifications.length,
          approved: kycVerifications.filter(v => v.status === "APPROVED").length,
          pending: kycVerifications.filter(v => v.status === "PENDING").length,
          rejected: kycVerifications.filter(v => v.status === "REJECTED").length,
          expired: kycVerifications.filter(v => v.status === "EXPIRED").length,
          requiresUpdate: kycVerifications.filter(v => v.status === "REQUIRES_UPDATE").length,
        },
      };
    } else if (reportType === "AML") {
      // Get AML check data
      const amlChecks = await db.amlCheck.findMany({
        where: {
          ...(session.user.organizationId ? { organizationId: session.user.organizationId } : {}),
          updatedAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
          ...(filters?.status ? { status: filters.status } : {}),
          ...(filters?.riskLevel ? { riskLevel: filters.riskLevel } : {}),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          amlAlerts: true,
        },
      });

      reportData = {
        amlChecks,
        summary: {
          total: amlChecks.length,
          approved: amlChecks.filter(c => c.status === "APPROVED").length,
          pending: amlChecks.filter(c => c.status === "PENDING").length,
          rejected: amlChecks.filter(c => c.status === "REJECTED").length,
          lowRisk: amlChecks.filter(c => c.riskLevel === "LOW").length,
          mediumRisk: amlChecks.filter(c => c.riskLevel === "MEDIUM").length,
          highRisk: amlChecks.filter(c => c.riskLevel === "HIGH").length,
          criticalRisk: amlChecks.filter(c => c.riskLevel === "CRITICAL").length,
          alertCount: amlChecks.reduce((sum, check) => sum + check.amlAlerts.length, 0),
        },
      };
    } else if (reportType === "VERIFICATION") {
      // Get carbon credit verification data
      const carbonCreditVerifications = await db.carbonCreditVerification.findMany({
        where: {
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
          ...(filters?.status ? { status: filters.status } : {}),
        },
        include: {
          carbonCredit: {
            select: {
              id: true,
              name: true,
              standard: true,
              vintage: true,
              quantity: true,
              status: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
              complianceDocuments: true,
            },
          },
        },
      });

      reportData = {
        carbonCreditVerifications,
        summary: {
          total: carbonCreditVerifications.length,
          verified: carbonCreditVerifications.filter(v => v.status === "VERIFIED").length,
          pending: carbonCreditVerifications.filter(v => v.status === "PENDING").length,
          rejected: carbonCreditVerifications.filter(v => v.status === "REJECTED").length,
          pendingUpdate: carbonCreditVerifications.filter(v => v.status === "PENDING_UPDATE").length,
        },
      };
    } else if (reportType === "AUDIT") {
      // Get audit log data
      const auditLogs = await db.auditLog.findMany({
        where: {
          ...(session.user.organizationId ? { organizationId: session.user.organizationId } : {}),
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
          ...(filters?.type ? { type: filters.type } : {}),
          ...(filters?.userId ? { userId: filters.userId } : {}),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Group audit logs by type
      const logsByType = auditLogs.reduce((acc: any, log) => {
        if (!acc[log.type]) {
          acc[log.type] = [];
        }
        acc[log.type].push(log);
        return acc;
      }, {});

      reportData = {
        auditLogs,
        logsByType,
        summary: {
          total: auditLogs.length,
          uniqueUsers: new Set(auditLogs.map(log => log.userId)).size,
          typeCounts: Object.entries(logsByType).map(([type, logs]) => ({
            type,
            count: (logs as any[]).length,
          })),
        },
      };
    } else if (reportType === "CUSTOM") {
      // Custom report logic based on filters
      reportData = {
        filters,
        // Additional custom data would be generated here based on specific requirements
      };
    }

    // Create report record
    const report = await db.complianceReport.create({
      data: {
        name,
        description,
        type: reportType,
        format,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        filters,
        data: reportData,
        generatedBy: session.user.id,
        ...(session.user.organizationId ? {
          organization: {
            connect: {
              id: session.user.organizationId,
            },
          },
        } : {}),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "COMPLIANCE_REPORT_GENERATED",
        description: `Generated ${reportType} compliance report: ${name}`,
        userId: session.user.id,
        organizationId: session.user.organizationId,
        metadata: {
          reportId: report.id,
          reportType,
          startDate,
          endDate,
        },
      },
    });

    return NextResponse.json({
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        format: report.format,
        createdAt: report.createdAt,
        // Include summary data for immediate display
        summary: reportData.summary,
      },
      message: "Compliance report generated successfully",
    });
  } catch (error) {
    logger.error("Error generating compliance report:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while generating the compliance report" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
