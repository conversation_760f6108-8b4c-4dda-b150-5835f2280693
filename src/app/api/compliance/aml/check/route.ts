import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceManager } from "@/lib/compliance";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

/**
 * POST /api/compliance/aml/check
 * Perform AML check
 */
async function performAmlCheckHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to perform AML check",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const body = await req.json();
    const { userId, organizationId, walletAddress, transactionHash, amount } = body;

    // Validate request
    if (!userId && !organizationId && !walletAddress && !transactionHash) {
      throw new ApiError(
        "At least one of userId, organizationId, walletAddress, or transactionHash is required",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if user is authorized to perform AML check
    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user is authorized to perform AML check for this user
    if (userId && userId !== session.user.id && !tenantContext.isAdmin) {
      // Check if the target user belongs to the same organization
      const targetUser = await db.user.findUnique({
        where: { id: userId },
        select: { organizationId: true },
      });

      if (!targetUser || targetUser.organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to perform AML check for user ${userId} from another organization`);
        throw new ApiError(
          "You are not authorized to perform AML check for this user",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Check if organization belongs to the user's organization
    if (organizationId && organizationId !== tenantContext.organizationId && !tenantContext.isAdmin) {
      logger.warn(`User ${session.user.id} attempted to perform AML check for organization ${organizationId} they don't belong to`);
      throw new ApiError(
        "You are not authorized to perform AML check for this organization",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Perform AML check
    const result = await ComplianceManager.performAmlCheck({
      userId,
      organizationId,
      walletAddress,
      transactionHash,
      amount,
    });

    return NextResponse.json(result);
  } catch (error) {
    logger.error("Error performing AML check:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while performing AML check",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(performAmlCheckHandler);

export const POST = withTenantIsolation(wrappedHandler);
