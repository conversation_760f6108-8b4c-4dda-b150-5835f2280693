import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { ComplianceManager } from "@/lib/compliance";
import { withTenantIsolation, getTenantContext } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

/**
 * GET /api/compliance/aml/status
 * Get AML status for the current user or organization
 */
async function getAmlStatusHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to check AML status",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get("userId") || undefined;
    const organizationId = searchParams.get("organizationId") || undefined;

    // If no parameters are provided, use the current user's ID and organization ID
    const effectiveUserId = userId || session.user.id;
    const effectiveOrganizationId = organizationId || session.user.organizationId;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user is authorized to check AML status
    if (effectiveUserId !== session.user.id && !tenantContext.isAdmin) {
      // Check if the target user belongs to the same organization
      const targetUser = await db.user.findUnique({
        where: { id: effectiveUserId },
        select: { organizationId: true },
      });

      if (!targetUser || targetUser.organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to check AML status for user ${effectiveUserId} from another organization`);
        throw new ApiError(
          "You are not authorized to check AML status for this user",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Check if organization belongs to the user's organization
    if (effectiveOrganizationId && effectiveOrganizationId !== tenantContext.organizationId && !tenantContext.isAdmin) {
      logger.warn(`User ${session.user.id} attempted to check AML status for organization ${effectiveOrganizationId} they don't belong to`);
      throw new ApiError(
        "You are not authorized to check AML status for this organization",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Get AML status
    const status = await ComplianceManager.getAmlStatus(effectiveUserId, effectiveOrganizationId);

    return NextResponse.json(status);
  } catch (error) {
    logger.error("Error getting AML status:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while getting AML status",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling and tenant isolation
const wrappedHandler = withErrorHandling(getAmlStatusHandler);

export const GET = withTenantIsolation(wrappedHandler);
