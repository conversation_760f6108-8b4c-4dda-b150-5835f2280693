import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { ComplianceDocumentType, ComplianceStatus } from "@prisma/client";

// Schema for submitting a carbon credit for verification
const submitVerificationSchema = z.object({
  carbonCreditId: z.string().min(1, "Carbon credit ID is required"),
  documents: z.array(
    z.object({
      type: z.enum([
        ComplianceDocumentType.PROJECT_DESCRIPTION,
        ComplianceDocumentType.METHODOLOGY,
        ComplianceDocumentType.VERIFICATION_REPORT,
        ComplianceDocumentType.VALIDATION_REPORT,
        ComplianceDocumentType.REGISTRY_CERTIFICATE,
        ComplianceDocumentType.MONITORING_REPORT,
        ComplianceDocumentType.OTHER,
      ]),
      name: z.string().min(1, "Document name is required"),
      url: z.string().url("Valid URL is required"),
      notes: z.string().optional(),
    })
  ).min(1, "At least one document is required"),
  notes: z.string().optional(),
});

// Schema for verifying a carbon credit
const verifyCreditsSchema = z.object({
  carbonCreditId: z.string().min(1, "Carbon credit ID is required"),
  status: z.enum([
    ComplianceStatus.APPROVED,
    ComplianceStatus.REJECTED,
    ComplianceStatus.REQUIRES_UPDATE,
  ]),
  notes: z.string().optional(),
  rejectionReason: z.string().optional(),
});

// GET handler for carbon credit verifications
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access carbon credit verifications" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const carbonCreditId = searchParams.get("carbonCreditId");
    const status = searchParams.get("status") as ComplianceStatus | null;
    const isAdmin = searchParams.get("isAdmin") === "true";
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20;
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if user is an admin for admin-only queries
    if (isAdmin) {
      const userRoles = await db.userRole.findMany({
        where: {
          userId: session.user.id,
          role: {
            name: {
              in: ["ADMIN", "PLATFORM_ADMIN", "COMPLIANCE_OFFICER"],
            },
          },
        },
      });

      if (userRoles.length === 0) {
        return NextResponse.json(
          { error: "You do not have permission to access admin verification data" },
          { status: 403 }
        );
      }
    }

    // Build filter
    let filter: any = {};

    if (carbonCreditId) {
      filter.carbonCreditId = carbonCreditId;
    } else if (isAdmin) {
      // Admin can see all pending verifications
      if (status) {
        filter.status = status;
      }
    } else {
      // Regular users can only see their own carbon credits
      filter.carbonCredit = {
        userId: session.user.id,
      };

      if (status) {
        filter.status = status;
      }
    }

    // Apply tenant isolation
    let documentsQuery = {
      where: {
        ...filter,
        type: {
          in: [
            ComplianceDocumentType.PROJECT_DESCRIPTION,
            ComplianceDocumentType.METHODOLOGY,
            ComplianceDocumentType.VERIFICATION_REPORT,
            ComplianceDocumentType.VALIDATION_REPORT,
            ComplianceDocumentType.REGISTRY_CERTIFICATE,
            ComplianceDocumentType.MONITORING_REPORT,
          ],
        },
      },
      include: {
        carbonCredit: {
          select: {
            id: true,
            name: true,
            standard: true,
            vintage: true,
            quantity: true,
            status: true,
            userId: true,
            organizationId: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    };

    documentsQuery = withTenantQuery(documentsQuery, tenantContext);

    // Get verification documents
    const documents = await db.complianceDocument.findMany(documentsQuery);

    // Group documents by carbon credit
    const verificationsByCredit = documents.reduce((acc: any, doc) => {
      if (!acc[doc.carbonCreditId!]) {
        acc[doc.carbonCreditId!] = {
          carbonCredit: doc.carbonCredit,
          documents: [],
          status: null,
        };
      }

      acc[doc.carbonCreditId!].documents.push(doc);

      // Use the most recent document status as the overall status
      if (!acc[doc.carbonCreditId!].status || new Date(doc.updatedAt) > new Date(acc[doc.carbonCreditId!].updatedAt)) {
        acc[doc.carbonCreditId!].status = doc.status;
        acc[doc.carbonCreditId!].updatedAt = doc.updatedAt;
      }

      return acc;
    }, {});

    // Convert to array
    const verifications = Object.values(verificationsByCredit);

    // Get total count
    const totalCount = Object.keys(verificationsByCredit).length;

    return NextResponse.json({
      verifications,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + verifications.length < totalCount,
      },
    });
  } catch (error) {
    logger.error("Error fetching carbon credit verifications:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching carbon credit verifications" },
      { status: 500 }
    );
  }
}

// POST handler for carbon credit verification operations
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to perform carbon credit verification operations" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const action = searchParams.get("action") || "submit";

    const body = await req.json();

    if (action === "submit") {
      const { carbonCreditId, documents, notes } = submitVerificationSchema.parse(body);

      // Get tenant context
      const tenantContext = await getTenantContext(session.user.id);

      // Check if the carbon credit exists and belongs to the user
      let creditQuery = {
        where: {
          id: carbonCreditId,
          userId: session.user.id,
        },
      };

      creditQuery = withTenantQuery(creditQuery, tenantContext);
      const carbonCredit = await db.carbonCredit.findFirst(creditQuery);

      if (!carbonCredit) {
        return NextResponse.json(
          { error: "Carbon credit not found or you do not have permission to submit it for verification" },
          { status: 404 }
        );
      }

      // Create compliance documents for the carbon credit
      const createdDocuments = await Promise.all(
        documents.map(async (doc) => {
          return db.complianceDocument.create({
            data: {
              type: doc.type,
              name: doc.name,
              url: doc.url,
              status: ComplianceStatus.PENDING,
              notes: doc.notes,
              carbonCredit: {
                connect: {
                  id: carbonCreditId,
                },
              },
              user: {
                connect: {
                  id: session.user.id,
                },
              },
              ...(session.user.organizationId ? {
                organization: {
                  connect: {
                    id: session.user.organizationId,
                  },
                },
              } : {}),
            },
          });
        })
      );

      // Create verification history entry
      await db.carbonCreditVerification.create({
        data: {
          status: "PENDING",
          notes: notes || "Submitted for verification",
          carbonCredit: {
            connect: {
              id: carbonCreditId,
            },
          },
          verifier: null,
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: "VERIFICATION_REQUESTED",
          description: `Carbon credit ${carbonCreditId} submitted for verification`,
          userId: session.user.id,
          organizationId: session.user.organizationId,
          metadata: {
            carbonCreditId,
            documentCount: documents.length,
          },
        },
      });

      // Create notification for compliance officers
      const complianceOfficers = await db.userRole.findMany({
        where: {
          role: {
            name: "COMPLIANCE_OFFICER",
          },
        },
        select: {
          userId: true,
        },
      });

      for (const officer of complianceOfficers) {
        await db.notification.create({
          data: {
            title: "New Verification Request",
            message: `A new carbon credit has been submitted for verification`,
            type: "COMPLIANCE",
            priority: "NORMAL",
            user: {
              connect: {
                id: officer.userId,
              },
            },
            actionUrl: `/compliance/carbon-verification/pending`,
            actionLabel: "Review Request",
          },
        });
      }

      return NextResponse.json({
        documents: createdDocuments,
        message: "Carbon credit submitted for verification successfully",
      });
    } else if (action === "verify") {
      const { carbonCreditId, status, notes, rejectionReason } = verifyCreditsSchema.parse(body);

      // Check if user is a compliance officer or admin
      const userRoles = await db.userRole.findMany({
        where: {
          userId: session.user.id,
          role: {
            name: {
              in: ["ADMIN", "PLATFORM_ADMIN", "COMPLIANCE_OFFICER"],
            },
          },
        },
      });

      if (userRoles.length === 0) {
        return NextResponse.json(
          { error: "You do not have permission to verify carbon credits" },
          { status: 403 }
        );
      }

      // Get the carbon credit
      const carbonCredit = await db.carbonCredit.findUnique({
        where: {
          id: carbonCreditId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!carbonCredit) {
        return NextResponse.json(
          { error: "Carbon credit not found" },
          { status: 404 }
        );
      }

      // Update all compliance documents for this carbon credit
      await db.complianceDocument.updateMany({
        where: {
          carbonCreditId,
        },
        data: {
          status,
          verifier: session.user.id,
          verificationDate: new Date(),
          rejectionReason: status === ComplianceStatus.REJECTED ? rejectionReason : null,
        },
      });

      // Create verification history entry
      await db.carbonCreditVerification.create({
        data: {
          status: status === ComplianceStatus.APPROVED ? "VERIFIED" :
                 status === ComplianceStatus.REJECTED ? "REJECTED" : "PENDING_UPDATE",
          notes: notes || `Verification ${status.toLowerCase()}`,
          carbonCredit: {
            connect: {
              id: carbonCreditId,
            },
          },
          verifier: session.user.id,
        },
      });

      // Update carbon credit status if approved
      if (status === ComplianceStatus.APPROVED) {
        await db.carbonCredit.update({
          where: {
            id: carbonCreditId,
          },
          data: {
            status: "VERIFIED",
          },
        });
      }

      // Create audit log
      await db.auditLog.create({
        data: {
          type: status === ComplianceStatus.APPROVED ? "VERIFICATION_APPROVED" :
                status === ComplianceStatus.REJECTED ? "VERIFICATION_REJECTED" : "VERIFICATION_UPDATE_REQUESTED",
          description: `Carbon credit ${carbonCreditId} verification ${status.toLowerCase()}`,
          userId: session.user.id,
          organizationId: session.user.organizationId,
          metadata: {
            carbonCreditId,
            status,
            notes,
            rejectionReason,
          },
        },
      });

      // Create notification for the carbon credit owner
      await db.notification.create({
        data: {
          title: `Verification ${status === ComplianceStatus.APPROVED ? "Approved" :
                  status === ComplianceStatus.REJECTED ? "Rejected" : "Requires Update"}`,
          message: status === ComplianceStatus.APPROVED ?
                  "Your carbon credit has been verified successfully" :
                  status === ComplianceStatus.REJECTED ?
                  "Your carbon credit verification has been rejected" :
                  "Your carbon credit verification requires additional information",
          type: "COMPLIANCE",
          priority: status === ComplianceStatus.APPROVED ? "NORMAL" : "HIGH",
          user: {
            connect: {
              id: carbonCredit.userId,
            },
          },
          actionUrl: `/carbon-credits/${carbonCreditId}`,
          actionLabel: "View Carbon Credit",
        },
      });

      return NextResponse.json({
        status,
        message: `Carbon credit verification ${status.toLowerCase()} successfully`,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Error performing carbon credit verification operation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while performing the carbon credit verification operation" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
