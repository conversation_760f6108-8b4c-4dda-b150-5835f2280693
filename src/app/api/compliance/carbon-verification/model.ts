import { ComplianceDocumentType, ComplianceStatus } from "@prisma/client";

/**
 * Carbon credit verification document model
 */
export interface VerificationDocument {
  type: ComplianceDocumentType;
  name: string;
  url: string;
  notes?: string;
}

/**
 * Carbon credit verification submission model
 */
export interface VerificationSubmission {
  carbonCreditId: string;
  documents: VerificationDocument[];
  notes?: string;
}

/**
 * Carbon credit verification response model
 */
export interface VerificationResponse {
  carbonCredit: {
    id: string;
    name: string;
    standard: string;
    vintage: string;
    quantity: number;
    status: string;
    userId: string;
    organizationId?: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
    organization?: {
      id: string;
      name: string;
    };
  };
  documents: {
    id: string;
    type: ComplianceDocumentType;
    name: string;
    url: string;
    status: ComplianceStatus;
    notes?: string;
    verifier?: string;
    verificationDate?: Date;
    rejectionReason?: string;
    createdAt: Date;
    updatedAt: Date;
  }[];
  status: ComplianceStatus;
  updatedAt: Date;
}

/**
 * Carbon credit verification status update model
 */
export interface VerificationStatusUpdate {
  carbonCreditId: string;
  status: ComplianceStatus;
  notes?: string;
  rejectionReason?: string;
}

/**
 * Verification pagination response
 */
export interface VerificationPaginationResponse {
  verifications: VerificationResponse[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
