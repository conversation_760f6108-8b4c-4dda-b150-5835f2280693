import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";

import { logger } from "@/lib/logger";
import { verificationService } from "@/lib/verification";

/**
 * GET /api/verifications/pending
 * Get pending verifications for admin review
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user is an admin
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");
    
    // Get pending verifications
    const pendingVerifications = await verificationService.getPendingVerifications(
      limit,
      offset
    );
    
    return NextResponse.json(pendingVerifications);
  } catch (error) {
    logger.error("Error getting pending verifications:", error);
    return NextResponse.json(
      { error: "Failed to get pending verifications" },
      { status: 500 }
    );
  }
}
