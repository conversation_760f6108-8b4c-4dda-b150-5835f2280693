import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { logger } from "@/lib/logger";
import { verificationService, VerificationStatus } from "@/lib/verification";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";
import { db } from "@/lib/db";

// Schema for updating verification status
const updateVerificationSchema = z.object({
  status: z.enum(["PENDING", "IN_PROGRESS", "APPROVED", "REJECTED"]),
  notes: z.string().optional(),
});

// Schema for adding document
const addDocumentSchema = z.object({
  documentUrl: z.string().url("Invalid document URL"),
});

/**
 * GET /api/verifications/[id]
 * Get verification details
 */
// Original GET handler
async function getHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const verificationId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Get verification details with tenant check
    const verification = await verificationService.getVerificationDetails(verificationId);

    // Check if user has access to this verification
    if (!tenantContext.isAdmin && verification.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to access verification ${verificationId} from another organization`);
      return NextResponse.json(
        { error: "You do not have permission to view this verification" },
        { status: 403 }
      );
    }

    return NextResponse.json({ verification });
  } catch (error) {
    logger.error("Error getting verification details:", error);
    return NextResponse.json(
      { error: "Failed to get verification details" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/verifications/[id]
 * Update verification status
 */
// Original PATCH handler
async function patchHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const verificationId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = updateVerificationSchema.parse(body);

    // Update verification status
    const verification = await verificationService.updateVerificationStatus(
      verificationId,
      validatedData.status as VerificationStatus,
      validatedData.notes || "",
      session.user.id
    );

    return NextResponse.json({
      verification,
      message: `Verification ${validatedData.status.toLowerCase()} successfully`,
    });
  } catch (error) {
    logger.error("Error updating verification status:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update verification status" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/verifications/[id]
 * Add document to verification
 */
// Original POST handler
async function postHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const verificationId = params.id;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = addDocumentSchema.parse(body);

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Get verification details to check ownership
    const existingVerification = await verificationService.getVerificationDetails(verificationId);

    // Check if user has access to this verification
    if (!tenantContext.isAdmin && existingVerification.organizationId !== tenantContext.organizationId) {
      logger.warn(`User ${session.user.id} attempted to add document to verification ${verificationId} from another organization`);
      return NextResponse.json(
        { error: "You do not have permission to modify this verification" },
        { status: 403 }
      );
    }

    // Add document to verification
    const verification = await verificationService.addDocument(
      verificationId,
      validatedData.documentUrl,
      session.user.id
    );

    return NextResponse.json({
      verification,
      message: "Document added to verification successfully",
    });
  } catch (error) {
    logger.error("Error adding document to verification:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to add document to verification" },
      { status: 500 }
    );
  }
}

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('verification', 'id')(getHandler);
export const PATCH = withResourceIsolation('verification', 'id')(patchHandler);
