import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

/**
 * GET /api/admin/users/[id]
 * Get user details
 */
export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access user details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access user details" },
        { status: 403 }
      );
    }

    const userId = (await context.params).id;

    // Get user with related data
    const user = await db.user.findUnique({
      where: { id: userId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            status: true,
            verificationStatus: true,
          },
        },
        // These relations are not defined in the schema
        // accounts: true,
        // sessions: {
        //   orderBy: {
        //     expires: "desc",
        //   },
        //   take: 5,
        // },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get audit logs for the user
    const auditLogs = await db.auditLog.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    // Get carbon credits created by the user
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    // Get transactions involving the user
    const transactions = await db.transaction.findMany({
      where: {
        order: {
          OR: [
            { buyerId: userId },
            { sellerId: userId },
          ],
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
      include: {
        order: {
          include: {
            carbonCredit: {
              select: {
                id: true,
                name: true,
              },
            },
            buyer: {
              select: {
                id: true,
                name: true,
              }
            },
            seller: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
      },
    });

    // Get login history
    const loginHistory = await db.auditLog.findMany({
      where: {
        userId,
        type: {
          in: ["LOGIN_SUCCESS", "LOGIN_FAILED"],
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    return NextResponse.json({
      user,
      auditLogs,
      carbonCredits,
      transactions,
      loginHistory,
    });
  } catch (error) {
    logger.error("Error fetching user details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching user details" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/users/[id]
 * Update user details
 */
export async function PATCH(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update user details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update user details" },
        { status: 403 }
      );
    }

    const userId = (await context.params).id;
    const body = await req.json();

    // Check if user exists
    const user = await db.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Prevent changing email to an existing one
    if (body.email && body.email !== user.email) {
      const existingUser = await db.user.findUnique({
        where: { email: body.email },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: "Email already in use" },
          { status: 400 }
        );
      }
    }

    // Update user
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: {
        ...body,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "USER_UPDATED",
        description: `User ${user.name || user.email} updated by admin`,
        user: {
          connect: { id: session.user.id },
        },
        organization: user.organizationId
          ? { connect: { id: user.organizationId } }
          : undefined,
        metadata: {
          updatedFields: Object.keys(body),
        },
      },
    });

    return NextResponse.json({
      message: "User updated successfully",
      user: updatedUser,
    });
  } catch (error) {
    logger.error("Error updating user:", error);

    return NextResponse.json(
      { error: "An error occurred while updating user" },
      { status: 500 }
    );
  }
}
