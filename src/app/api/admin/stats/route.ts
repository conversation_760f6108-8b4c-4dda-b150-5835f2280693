import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access admin statistics" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access admin statistics" },
        { status: 403 }
      );
    }

    // Get total users count
    const totalUsers = await db.user.count();

    // Get total organizations count
    const totalOrganizations = await db.organization.count();

    // Get total carbon credits count
    const totalCarbonCredits = await db.carbonCredit.count();

    // Get total transactions count
    const totalTransactions = await db.transaction.count();

    // Get total transaction volume
    const transactionVolumeResult = await db.transaction.aggregate({
      _sum: {
        amount: true,
      },
    });

    const transactionVolume = transactionVolumeResult._sum.amount || 0;

    // Get pending verifications count
    const pendingVerifications = await db.organization.count({
      where: {
        verificationStatus: {
          in: ["PENDING", "IN_REVIEW"],
        },
      },
    });

    // Get active subscriptions count
    const activeSubscriptions = await db.subscription.count({
      where: {
        status: "ACTIVE",
      },
    });

    // Calculate platform revenue (sum of all billing records)
    const platformRevenueResult = await db.billingRecord.aggregate({
      where: {
        status: "PAID",
      },
      _sum: {
        amount: true,
      },
    });

    const platformRevenue = platformRevenueResult._sum.amount || 0;

    // Get user growth data (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const userGrowthData = await db.user.groupBy({
      by: ["createdAt"],
      where: {
        createdAt: {
          gte: sixMonthsAgo,
        },
      },
      _count: {
        id: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Format user growth data
    const userGrowth = userGrowthData.map((item: any) => ({
      date: item.createdAt.toISOString().split("T")[0],
      count: item._count.id,
    }));

    // Get transaction growth data (last 6 months)
    const transactionGrowthData = await db.transaction.groupBy({
      by: ["createdAt"],
      where: {
        createdAt: {
          gte: sixMonthsAgo,
        },
      },
      _sum: {
        amount: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Format transaction growth data
    const transactionGrowth = transactionGrowthData.map((item: any) => ({
      date: item.createdAt.toISOString().split("T")[0],
      volume: item._sum.amount || 0,
    }));

    // Get recent transactions
    const recentTransactions = await db.transaction.findMany({
      take: 5,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        order: {
          include: {
            carbonCredit: {
              select: {
                name: true,
              },
            },
            buyer: {
              select: {
                name: true,
              },
            },
            seller: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    // Get recent organizations
    const recentOrganizations = await db.organization.findMany({
      take: 5,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        _count: {
          select: {
            users: true,
            carbonCredits: true,
          },
        },
      },
    });

    // Get pending organizations
    const pendingOrganizations = await db.organization.findMany({
      where: {
        status: "PENDING",
      },
      take: 5,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    // Get verification requests
    const verificationRequests = await db.organization.findMany({
      where: {
        verificationStatus: {
          in: ["PENDING", "IN_REVIEW"],
        },
      },
      take: 5,
      orderBy: {
        updatedAt: "desc",
      },
      include: {
        _count: {
          select: {
            documents: true,
          },
        },
      },
    });

    // Format verification requests
    const formattedVerificationRequests = verificationRequests.map((org: any) => ({
      id: org.id,
      organization: {
        id: org.id,
        name: org.name,
        legalName: org.legalName,
        registrationNumber: org.registrationNumber,
        country: org.country,
      },
      status: org.verificationStatus,
      documentCount: org._count.documents,
      createdAt: org.createdAt,
      updatedAt: org.updatedAt,
    }));

    return NextResponse.json({
      totalUsers,
      totalOrganizations,
      totalCarbonCredits,
      totalTransactions,
      transactionVolume,
      pendingVerifications,
      activeSubscriptions,
      platformRevenue,
      userGrowth,
      transactionGrowth,
      recentTransactions,
      recentOrganizations,
      pendingOrganizations,
      verificationRequests: formattedVerificationRequests,
    });
  } catch (error) {
    logger.error("Error fetching admin statistics:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching admin statistics" },
      { status: 500 }
    );
  }
}
