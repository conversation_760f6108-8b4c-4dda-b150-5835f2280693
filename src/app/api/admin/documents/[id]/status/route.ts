import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { auditService } from "@/lib/audit";

// Schema for document status update
const documentStatusUpdateSchema = z.object({
  status: z.enum(["PENDING", "APPROVED", "REJECTED"]),
  notes: z.string().optional(),
});

/**
 * PATCH /api/admin/documents/[id]/status
 * Update document status
 */
export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update document status" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update document status" },
        { status: 403 }
      );
    }

    const documentId = params.id;
    const body = await req.json();
    const { status, notes } = documentStatusUpdateSchema.parse(body);

    // Check if document exists
    const document = await db.document.findUnique({
      where: { id: documentId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            users: {
              where: {
                role: "ORGANIZATION_ADMIN",
              },
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!document) {
      return NextResponse.json(
        { error: "Document not found" },
        { status: 404 }
      );
    }

    // Update document status
    const updatedDocument = await db.document.update({
      where: { id: documentId },
      data: {
        status,
        notes,
      },
    });

    // Create audit log
    await auditService.log(
      status === "APPROVED" ? "DOCUMENT_VERIFIED" : "VERIFICATION_REJECTED",
      `Document ${document.name} status updated to ${status}`,
      {
        documentId,
        documentName: document.name,
        documentType: document.type,
        organizationId: document.organizationId,
        previousStatus: document.status,
        newStatus: status,
        notes,
      },
      session.user.id,
      document.organizationId
    );

    // Notify organization admins
    for (const admin of document.organization.users) {
      await notificationService.createNotification(
        admin.id,
        "Document Status Updated",
        `Your document "${document.name}" has been ${status.toLowerCase()}.`,
        "VERIFICATION",
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          documentId,
          documentName: document.name,
          documentType: document.type,
          status,
          notes,
        },
        "/dashboard/organization/verification",
        "View Details"
      );
    }

    // Check if all documents are approved and update organization verification status
    if (status === "APPROVED") {
      const allDocuments = await db.document.findMany({
        where: {
          organizationId: document.organizationId,
        },
      });

      const allApproved = allDocuments.every((doc) => doc.status === "APPROVED");

      if (allApproved && allDocuments.length > 0) {
        // Update organization verification status to VERIFIED
        await db.organization.update({
          where: { id: document.organizationId },
          data: {
            verificationStatus: "VERIFIED",
            status: "ACTIVE",
          },
        });

        // Notify organization admins about verification approval
        for (const admin of document.organization.users) {
          await notificationService.createNotification(
            admin.id,
            "Organization Verified",
            `Congratulations! Your organization "${document.organization.name}" has been verified.`,
            "VERIFICATION",
            [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
            {
              organizationId: document.organizationId,
              verificationStatus: "VERIFIED",
            },
            "/dashboard/organization/verification",
            "View Details"
          );
        }

        // Create audit log for organization verification
        await auditService.log(
          "VERIFICATION_APPROVED",
          `Organization ${document.organization.name} has been verified`,
          {
            organizationId: document.organizationId,
            verificationStatus: "VERIFIED",
          },
          session.user.id,
          document.organizationId
        );
      }
    }

    return NextResponse.json({
      message: `Document status updated to ${status}`,
      document: updatedDocument,
    });
  } catch (error) {
    logger.error("Error updating document status:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating document status" },
      { status: 500 }
    );
  }
}
