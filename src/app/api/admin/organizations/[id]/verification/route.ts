import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { auditService } from "@/lib/audit";
import { AuditLogType } from "@prisma/client";

// Schema for verification status update
const verificationUpdateSchema = z.object({
  verificationStatus: z.enum(["PENDING", "IN_REVIEW", "VERIFIED", "REJECTED"]),
  notes: z.string().optional(),
});

/**
 * PATCH /api/admin/organizations/[id]/verification
 * Update organization verification status
 */
export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update verification status" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update verification status" },
        { status: 403 }
      );
    }

    const organizationId = params.id;
    const body = await req.json();
    const { verificationStatus, notes } = verificationUpdateSchema.parse(body);

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          where: {
            role: "ORGANIZATION_ADMIN",
          },
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Update organization verification status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        verificationStatus,
        ...(verificationStatus === "VERIFIED" && { status: "ACTIVE" }),
      },
    });

    // Create audit log
    await auditService.log(
      verificationStatus === "VERIFIED" ? AuditLogType.VERIFICATION_APPROVED :
      verificationStatus === "REJECTED" ? AuditLogType.VERIFICATION_REJECTED :
      AuditLogType.ORGANIZATION_UPDATED,
      `Organization ${organization.name} verification status updated to ${verificationStatus}`,
      {
        organizationId,
        previousStatus: organization.verificationStatus,
        newStatus: verificationStatus,
        notes,
      },
      session.user.id
    );

    // Note: verificationNote model does not exist in the schema
    // Store notes in auditLog metadata instead
    if (notes) {
      // Log additional audit event for the notes
      await auditService.log(
        AuditLogType.VERIFICATION_APPROVED,
        `Verification note added for organization ${organization.name}`,
        {
          notes,
          status: verificationStatus,
          organizationId,
        },
        session.user.id
      );
    }

    // Notify organization admins
    for (const admin of organization.users) {
      await notificationService.createNotification(
        admin.id,
        "Verification Status Updated",
        `Your organization's verification status has been updated to ${verificationStatus}.`,
        "VERIFICATION",
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          organizationId,
          verificationStatus,
          notes,
        },
        "/dashboard/organization/verification",
        "View Details"
      );
    }

    return NextResponse.json({
      message: `Organization verification status updated to ${verificationStatus}`,
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error updating organization verification status:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating verification status" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/organizations/[id]/verification
 * Get organization verification details
 */
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view verification details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to view verification details" },
        { status: 403 }
      );
    }

    const organizationId = params.id;

    // Get organization with verification details
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        documents: {
          orderBy: {
            createdAt: "desc",
          },
        },
        // Note: verificationNotes field does not exist on Organization model
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      organization,
    });
  } catch (error) {
    logger.error("Error fetching organization verification details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching verification details" },
      { status: 500 }
    );
  }
}
