import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

/**
 * GET /api/admin/organizations/[id]
 * Get organization details
 */
export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access organization details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access organization details" },
        { status: 403 }
      );
    }

    const organizationId = (await context.params).id;

    // Get organization with related data
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          orderBy: {
            createdAt: "desc",
          },
        },
        carbonCredits: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
        documents: {
          orderBy: {
            createdAt: "desc",
          },
        },
        subscription: true,
        wallets: true,
        _count: {
          select: {
            users: true,
            carbonCredits: true,
            documents: true,
          },
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Get audit logs for the organization
    const auditLogs = await db.auditLog.findMany({
      where: {
        organizationId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Get transactions for the organization
    const transactions = await db.transaction.findMany({
      where: {
        order: {
          OR: [
            {
              buyer: {
                organizationId,
              },
            },
            {
              seller: {
                organizationId,
              },
            },
          ],
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
      include: {
        order: {
          include: {
            carbonCredit: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      organization,
      auditLogs,
      transactions,
    });
  } catch (error) {
    logger.error("Error fetching organization details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching organization details" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/organizations/[id]
 * Update organization details
 */
export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update organization details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update organization details" },
        { status: 403 }
      );
    }

    const organizationId = params.id;
    const body = await req.json();

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Update organization
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        ...body,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "ORGANIZATION_UPDATED",
        description: `Organization ${organization.name} updated by admin`,
        user: {
          connect: { id: session.user.id },
        },
        organization: {
          connect: { id: organizationId },
        },
        metadata: {
          updatedFields: Object.keys(body),
        },
      },
    });

    return NextResponse.json({
      message: "Organization updated successfully",
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error updating organization:", error);

    return NextResponse.json(
      { error: "An error occurred while updating organization" },
      { status: 500 }
    );
  }
}
