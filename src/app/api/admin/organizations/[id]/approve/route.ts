import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { OrganizationStatus, VerificationStatus, AuditLogType } from "@prisma/client";

/**
 * POST /api/admin/organizations/[id]/approve
 * Approve an organization
 */
async function approveOrganizationHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to approve organizations",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to approve organizations",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    const organizationId = params.id;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Update organization status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        status: OrganizationStatus.ACTIVE,
        verificationStatus: VerificationStatus.VERIFIED,
      },
    });

    // Update all pending documents to approved
    await db.document.updateMany({
      where: {
        organizationId,
        status: "PENDING",
      },
      data: {
        status: "APPROVED",
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.VERIFICATION_APPROVED,
        description: `Organization ${organization.name} approved by admin`,
        userId: session.user.id,
        organizationId,
        metadata: {
          previousStatus: organization.status,
          previousVerificationStatus: organization.verificationStatus,
          newStatus: OrganizationStatus.ACTIVE,
          newVerificationStatus: VerificationStatus.VERIFIED,
        },
      },
    });

    // Send notification to all organization users
    for (const user of organization.users) {
      await db.notification.create({
        data: {
          title: "Organization Approved",
          message: `Your organization ${organization.name} has been approved and verified.`,
          type: "SYSTEM",
          priority: "HIGH",
          userId: user.id,
          actionUrl: "/dashboard/organization/settings",
          actionLabel: "View Organization",
        },
      });
    }

    logger.info(`Organization ${organizationId} approved by admin ${session.user.id}`);

    return NextResponse.json({
      message: "Organization approved successfully",
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error approving organization:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while approving the organization",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(approveOrganizationHandler);
