import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType } from "@prisma/client";

// Schema for organization fee settings
const organizationFeeSchema = z.object({
  listingFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  transactionFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  subscriptionFee: z
    .number()
    .min(0, "Fee must be at least 0"),
  effectiveFrom: z.date(),
  notes: z.string().optional(),
});

/**
 * GET /api/admin/organizations/[id]/fees
 * Get organization fee settings
 */
async function getOrganizationFeesHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access organization fees",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to access organization fees",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = params.id;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Get fee history
    const feeHistory = await db.organizationFeeHistory.findMany({
      where: { organizationId },
      orderBy: {
        effectiveFrom: "desc",
      },
    });

    return NextResponse.json({
      organization: {
        id: organization.id,
        name: organization.name,
        listingFeeRate: organization.listingFeeRate,
        transactionFeeRate: organization.transactionFeeRate,
        subscriptionFee: organization.subscriptionFee,
      },
      feeHistory,
    });
  } catch (error) {
    logger.error("Error getting organization fees:", error);
    throw new ApiError(
      "An error occurred while getting organization fees",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * POST /api/admin/organizations/[id]/fees
 * Update organization fee settings
 */
async function updateOrganizationFeesHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update organization fees",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to update organization fees",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = params.id;

    // Parse and validate request body
    const body = await req.json();
    const { listingFeeRate, transactionFeeRate, subscriptionFee, effectiveFrom, notes } = 
      organizationFeeSchema.parse(body);

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Create fee history record
    await db.organizationFeeHistory.create({
      data: {
        organizationId,
        listingFeeRate,
        transactionFeeRate,
        subscriptionFee,
        effectiveFrom,
        notes,
      },
    });

    // Update organization
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        listingFeeRate,
        transactionFeeRate,
        subscriptionFee,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.ORGANIZATION_FEES_UPDATED,
        description: `Fees updated for organization ${organization.name}`,
        userId: session.user.id,
        organizationId,
        metadata: {
          previousFees: {
            listingFeeRate: organization.listingFeeRate,
            transactionFeeRate: organization.transactionFeeRate,
            subscriptionFee: organization.subscriptionFee,
          },
          newFees: {
            listingFeeRate,
            transactionFeeRate,
            subscriptionFee,
          },
          effectiveFrom,
          notes,
        },
      },
    });

    // Create notification for organization admins
    const orgAdmins = await db.user.findMany({
      where: {
        organizationId,
        role: "ORGANIZATION_ADMIN",
      },
    });

    for (const admin of orgAdmins) {
      await db.notification.create({
        data: {
          title: "Fee Structure Updated",
          message: `Your organization's fee structure has been updated. The changes will take effect on ${effectiveFrom.toLocaleDateString()}.`,
          type: "BILLING",
          priority: "HIGH",
          userId: admin.id,
          actionUrl: "/dashboard/organization/billing",
          actionLabel: "View Billing",
        },
      });
    }

    logger.info(`Fees updated for organization ${organizationId} by admin ${session.user.id}`);

    return NextResponse.json({
      message: "Organization fees updated successfully",
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error updating organization fees:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while updating organization fees",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getOrganizationFeesHandler);
export const POST = withErrorHandling(updateOrganizationFeesHandler);
