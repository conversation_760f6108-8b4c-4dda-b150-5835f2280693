import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { OrganizationStatus, VerificationStatus, AuditLogType } from "@prisma/client";

// Schema for rejection reason
const rejectionSchema = z.object({
  reason: z.string().min(1, "Rejection reason is required"),
});

/**
 * POST /api/admin/organizations/[id]/reject
 * Reject an organization
 */
async function rejectOrganizationHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to reject organizations",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to reject organizations",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    const organizationId = params.id;

    // Parse and validate request body
    const body = await req.json();
    const { reason } = rejectionSchema.parse(body);

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
    });

    if (!organization) {
      throw new ApiError("Organization not found", ErrorType.NOT_FOUND, 404);
    }

    // Update organization status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        status: OrganizationStatus.INACTIVE,
        verificationStatus: VerificationStatus.REJECTED,
      },
    });

    // Update all pending documents to rejected
    await db.document.updateMany({
      where: {
        organizationId,
        status: "PENDING",
      },
      data: {
        status: "REJECTED",
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.VERIFICATION_REJECTED,
        description: `Organization ${organization.name} rejected by admin`,
        userId: session.user.id,
        organizationId,
        metadata: {
          previousStatus: organization.status,
          previousVerificationStatus: organization.verificationStatus,
          newStatus: OrganizationStatus.INACTIVE,
          newVerificationStatus: VerificationStatus.REJECTED,
          reason,
        },
      },
    });

    // Send notification to all organization users
    for (const user of organization.users) {
      await db.notification.create({
        data: {
          title: "Organization Verification Rejected",
          message: `Your organization ${organization.name} verification has been rejected. Reason: ${reason}`,
          type: "SYSTEM",
          priority: "HIGH",
          userId: user.id,
          actionUrl: "/dashboard/organization/verification",
          actionLabel: "Update Verification",
        },
      });
    }

    logger.info(`Organization ${organizationId} rejected by admin ${session.user.id}`);

    return NextResponse.json({
      message: "Organization rejected successfully",
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error rejecting organization:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while rejecting the organization",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(rejectOrganizationHandler);
