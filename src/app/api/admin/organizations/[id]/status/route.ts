import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { auditService } from "@/lib/audit";

// Schema for status update
const statusUpdateSchema = z.object({
  status: z.enum(["PENDING", "ACTIVE", "SUSPENDED", "INACTIVE"]),
  reason: z.string().optional(),
});

/**
 * PATCH /api/admin/organizations/[id]/status
 * Update organization status
 */
export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update organization status" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update organization status" },
        { status: 403 }
      );
    }

    const organizationId = params.id;
    const body = await req.json();
    const { status, reason } = statusUpdateSchema.parse(body);

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          where: {
            role: "ORGANIZATION_ADMIN",
          },
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Update organization status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        status,
      },
    });

    // Create audit log
    await auditService.log(
      "ORGANIZATION_UPDATED",
      `Organization ${organization.name} status updated to ${status}`,
      {
        organizationId,
        previousStatus: organization.status,
        newStatus: status,
        reason,
      },
      session.user.id
    );

    // Notify organization admins
    for (const admin of organization.users) {
      await notificationService.createNotification(
        admin.id,
        "Organization Status Updated",
        `Your organization's status has been updated to ${status}.${reason ? ` Reason: ${reason}` : ""}`,
        "SYSTEM",
        [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
        {
          organizationId,
          status,
          reason,
        },
        "/dashboard/organization",
        "View Organization"
      );
    }

    // If organization is suspended, cancel all pending orders
    if (status === "SUSPENDED") {
      // Get all pending orders for the organization
      const pendingOrders = await db.order.findMany({
        where: {
          status: "PENDING",
          OR: [
            {
              buyer: {
                organizationId,
              },
            },
            {
              seller: {
                organizationId,
              },
            },
          ],
        },
      });

      // Cancel each order
      for (const order of pendingOrders) {
        await db.order.update({
          where: { id: order.id },
          data: {
            status: "CANCELLED",
            updatedAt: new Date(),
          },
        });

        // Create audit log for each cancelled order
        await auditService.log(
          "ORDER_CANCELLED",
          `Order ${order.id} cancelled due to organization suspension`,
          {
            orderId: order.id,
            organizationId,
            reason: "Organization suspended",
          },
          session.user.id,
          organizationId
        );
      }

      // Notify about cancelled orders if any
      if (pendingOrders.length > 0) {
        for (const admin of organization.users) {
          await notificationService.createNotification(
            admin.id,
            "Orders Cancelled",
            `${pendingOrders.length} pending orders have been cancelled due to your organization's suspension.`,
            "ORDER",
            [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
            {
              organizationId,
              cancelledOrders: pendingOrders.length,
            },
            "/dashboard/orders",
            "View Orders"
          );
        }
      }
    }

    return NextResponse.json({
      message: `Organization status updated to ${status}`,
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error updating organization status:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating organization status" },
      { status: 500 }
    );
  }
}
