import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { auditService } from "@/lib/audit";
import { AuditLogType } from "@prisma/client";

// Schema for platform settings
const platformSettingsSchema = z.object({
  settings: z.object({
    // Fee settings
    transactionFeePercent: z.number().min(0).max(100),
    listingFeeAmount: z.number().min(0),
    minimumTransactionAmount: z.number().min(0),
    maximumTransactionAmount: z.number().min(0),
    
    // Verification settings
    requireVerificationForTrading: z.boolean(),
    autoApproveVerification: z.boolean(),
    verificationDocumentTypes: z.array(z.string()),
    
    // Email settings
    emailNotificationsEnabled: z.boolean(),
    adminEmailAddress: z.string().email(),
    supportEmailAddress: z.string().email(),
    
    // Platform settings
    maintenanceMode: z.boolean(),
    allowNewRegistrations: z.boolean(),
    allowNewListings: z.boolean(),
    platformName: z.string(),
    platformUrl: z.string().url(),
    
    // Blockchain settings
    defaultNetwork: z.string(),
    useTestnet: z.boolean(),
    gasLimit: z.number().min(0),
    
    // Terms and policies
    termsOfServiceUrl: z.string().url(),
    privacyPolicyUrl: z.string().url(),
  }),
});

/**
 * GET /api/admin/settings
 * Get platform settings
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access platform settings" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access platform settings" },
        { status: 403 }
      );
    }

    // Get platform settings
    const settings = await db.platformSettings.findFirst();

    if (!settings) {
      // Create default settings if none exist
      const defaultSettings = {
        transactionFeePercent: 1.5,
        listingFeeAmount: 10,
        minimumTransactionAmount: 50,
        maximumTransactionAmount: 1000000,
        requireVerificationForTrading: true,
        autoApproveVerification: false,
        verificationDocumentTypes: ["BUSINESS_REGISTRATION", "TAX_CERTIFICATE", "IDENTITY_PROOF"],
        emailNotificationsEnabled: true,
        adminEmailAddress: "<EMAIL>",
        supportEmailAddress: "<EMAIL>",
        maintenanceMode: false,
        allowNewRegistrations: true,
        allowNewListings: true,
        platformName: "Carbonix",
        platformUrl: "https://carbonexchange.com",
        defaultNetwork: "polygon",
        useTestnet: true,
        gasLimit: 500000,
        termsOfServiceUrl: "https://carbonexchange.com/terms",
        privacyPolicyUrl: "https://carbonexchange.com/privacy",
      };

      await db.platformSettings.create({
        data: defaultSettings,
      });

      return NextResponse.json({ settings: defaultSettings });
    }

    return NextResponse.json({ settings });
  } catch (error) {
    logger.error("Error fetching platform settings:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching platform settings" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/settings
 * Update platform settings
 */
export async function PUT(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update platform settings" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update platform settings" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { settings } = platformSettingsSchema.parse(body);

    // Get current settings
    const currentSettings = await db.platformSettings.findFirst();

    if (!currentSettings) {
      // Create settings if none exist
      await db.platformSettings.create({
        data: settings,
      });
    } else {
      // Update existing settings
      await db.platformSettings.update({
        where: { id: currentSettings.id },
        data: settings,
      });
    }

    // Create audit log
    await auditService.log(
      AuditLogType.SETTINGS_UPDATED,
      "Platform settings updated",
      {
        updatedFields: Object.keys(settings),
      },
      session.user.id
    );

    return NextResponse.json({
      message: "Platform settings updated successfully",
      settings,
    });
  } catch (error) {
    logger.error("Error updating platform settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating platform settings" },
      { status: 500 }
    );
  }
}
