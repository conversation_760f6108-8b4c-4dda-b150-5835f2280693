import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { AuditLogType } from "@prisma/client";

// Schema for platform fee settings
const platformFeeSchema = z.object({
  defaultListingFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  defaultTransactionFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  defaultSubscriptionFee: z
    .number()
    .min(0, "Fee must be at least 0"),
});

/**
 * GET /api/admin/settings/fees
 * Get platform fee settings
 */
async function getPlatformFeeSettingsHandler() {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access fee settings",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to access fee settings",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Get platform settings
    const settings = await db.platformSettings.findFirst();

    if (!settings) {
      // Create default settings if they don't exist
      const defaultSettings = await db.platformSettings.create({
        data: {
          defaultListingFeeRate: 2.5,
          defaultTransactionFeeRate: 1.5,
          defaultSubscriptionFee: 99,
        },
      });

      return NextResponse.json({ settings: defaultSettings });
    }

    return NextResponse.json({ settings });
  } catch (error) {
    logger.error("Error getting platform fee settings:", error);
    throw new ApiError(
      "An error occurred while getting platform fee settings",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PUT /api/admin/settings/fees
 * Update platform fee settings
 */
async function updatePlatformFeeSettingsHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update fee settings",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to update fee settings",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { defaultListingFeeRate, defaultTransactionFeeRate, defaultSubscriptionFee } = 
      platformFeeSchema.parse(body);

    // Get current settings
    const currentSettings = await db.platformSettings.findFirst();

    // Update or create settings
    const updatedSettings = await db.platformSettings.upsert({
      where: { id: currentSettings?.id || "default" },
      update: {
        defaultListingFeeRate,
        defaultTransactionFeeRate,
        defaultSubscriptionFee,
      },
      create: {
        defaultListingFeeRate,
        defaultTransactionFeeRate,
        defaultSubscriptionFee,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: AuditLogType.SETTINGS_UPDATED,
        description: "Platform fee settings updated",
        userId: session.user.id,
        metadata: {
          previousSettings: currentSettings || "No previous settings",
          newSettings: {
            defaultListingFeeRate,
            defaultTransactionFeeRate,
            defaultSubscriptionFee,
          },
        },
      },
    });

    logger.info(`Platform fee settings updated by admin ${session.user.id}`);

    return NextResponse.json({
      message: "Platform fee settings updated successfully",
      settings: updatedSettings,
    });
  } catch (error) {
    logger.error("Error updating platform fee settings:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while updating platform fee settings",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getPlatformFeeSettingsHandler);
export const PUT = withErrorHandling(updatePlatformFeeSettingsHandler);
