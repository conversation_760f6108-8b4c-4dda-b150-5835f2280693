import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * GET /api/admin/settings/fees/history
 * Get fee change history
 */
async function getFeeHistoryHandler(req: Request) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access fee history",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (session.user.role !== "ADMIN") {
    throw new ApiError(
      "You do not have permission to access fee history",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Get URL parameters
    const url = new URL(req.url);
    const organizationId = url.searchParams.get("organizationId");
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    // Build query
    const query: any = {};
    if (organizationId) {
      query.organizationId = organizationId;
    }

    // Get fee history
    const feeHistory = await db.organizationFeeHistory.findMany({
      where: query,
      orderBy: {
        effectiveFrom: "desc",
      },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
      },
      skip: offset,
      take: limit,
    });

    // Format response
    const formattedHistory = feeHistory.map((history) => ({
      id: history.id,
      organizationId: history.organizationId,
      organizationName: history.organization.name,
      listingFeeRate: history.listingFeeRate,
      transactionFeeRate: history.transactionFeeRate,
      subscriptionFee: history.subscriptionFee,
      effectiveFrom: history.effectiveFrom,
      createdAt: history.createdAt,
      notes: history.notes,
    }));

    // Get total count
    const total = await db.organizationFeeHistory.count({
      where: query,
    });

    return NextResponse.json({
      history: formattedHistory,
      pagination: {
        total,
        limit,
        offset,
        hasMore: total > offset + limit,
      },
    });
  } catch (error) {
    logger.error("Error getting fee history:", error);
    throw new ApiError(
      "An error occurred while getting fee history",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getFeeHistoryHandler);
