import { NextRequest, NextResponse } from "next/server";
import { analyticsService } from "@/lib/analytics";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

/**
 * GET /api/analytics/market
 * Get market trends and analytics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get period from query parameters (default to 30 days)
    const searchParams = req.nextUrl.searchParams;
    const period = parseInt(searchParams.get("period") || "30");

    // Get market trends
    const trends = await analyticsService.getMarketTrends(period);

    return NextResponse.json(trends);
  } catch (error) {
    logger.error("Error in market analytics API:", error);
    return NextResponse.json(
      { error: "Failed to get market analytics" },
      { status: 500 }
    );
  }
}
