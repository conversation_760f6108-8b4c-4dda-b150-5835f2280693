import { NextRequest, NextResponse } from "next/server";
import { analyticsService } from "@/lib/analytics";
import { auth } from "@/lib/auth";

import { logger } from "@/lib/logger";

/**
 * GET /api/analytics/platform
 * Get platform analytics for admin users
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user is an admin
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }
    
    // Get platform statistics
    const stats = await analyticsService.getPlatformStats();
    
    return NextResponse.json(stats);
  } catch (error) {
    logger.error("Error in platform analytics API:", error);
    return NextResponse.json(
      { error: "Failed to get platform analytics" },
      { status: 500 }
    );
  }
}
