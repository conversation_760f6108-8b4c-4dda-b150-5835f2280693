import { NextRequest, NextResponse } from "next/server";
import { analyticsService } from "@/lib/analytics";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

/**
 * GET /api/analytics/audit
 * Get audit trail records
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin or organization admin
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin && !isOrgAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const organizationId = searchParams.get("organizationId") || undefined;
    const userId = searchParams.get("userId") || undefined;
    const type = searchParams.get("type") || undefined;
    const limit = parseInt(searchParams.get("limit") || "100");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Parse dates if provided
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (searchParams.has("startDate")) {
      startDate = new Date(searchParams.get("startDate")!);
    }

    if (searchParams.has("endDate")) {
      endDate = new Date(searchParams.get("endDate")!);
    }

    // If organization admin, restrict to their organization
    const filters: any = {
      limit,
      offset,
      type,
      startDate,
      endDate,
    };

    if (isOrgAdmin && !isAdmin) {
      filters.organizationId = session.user.organizationId;
    } else if (organizationId) {
      filters.organizationId = organizationId;
    }

    if (userId) {
      filters.userId = userId;
    }

    // Get audit trail
    const auditTrail = await analyticsService.getAuditTrail(filters);

    return NextResponse.json(auditTrail);
  } catch (error) {
    logger.error("Error in audit trail API:", error);
    return NextResponse.json(
      { error: "Failed to get audit trail" },
      { status: 500 }
    );
  }
}
