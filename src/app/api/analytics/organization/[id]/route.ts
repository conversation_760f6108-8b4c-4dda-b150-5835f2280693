import { NextRequest, NextResponse } from "next/server";
import { analyticsService } from "@/lib/analytics";
import { auth } from "@/lib/auth";

import { logger } from "@/lib/logger";
import { db } from "@/lib/db";

/**
 * GET /api/analytics/organization/[id]
 * Get organization analytics
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const organizationId = params.id;
    
    // Check authentication
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user is an admin or belongs to the organization
    const isAdmin = session.user.role === "ADMIN";
    const isOrgMember = session.user.organizationId === organizationId;
    const isOrgAdmin = isOrgMember && session.user.role === "ORGANIZATION_ADMIN";
    
    if (!isAdmin && !isOrgMember) {
      return NextResponse.json(
        { error: "Forbidden: You don't have access to this organization's analytics" },
        { status: 403 }
      );
    }
    
    // Get organization statistics
    const stats = await analyticsService.getOrganizationStats(organizationId);
    
    return NextResponse.json(stats);
  } catch (error) {
    logger.error("Error in organization analytics API:", error);
    return NextResponse.json(
      { error: "Failed to get organization analytics" },
      { status: 500 }
    );
  }
}
