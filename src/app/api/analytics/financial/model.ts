import { FinancialMetricType, MetricStatus } from "@prisma/client";

/**
 * Financial metric data model
 */
export interface FinancialMetricData {
  metricType: FinancialMetricType;
  name: string;
  value: number;
  previousValue?: number;
  currency?: string;
  period: string;
  startDate: string;
  endDate: string;
  target?: number;
  notes?: string;
}

/**
 * Financial metric response model
 */
export interface FinancialMetricResponse {
  id: string;
  metricType: FinancialMetricType;
  name: string;
  value: number;
  previousValue?: number;
  changePercent?: number;
  currency: string;
  period: string;
  startDate: Date;
  endDate: Date;
  target?: number;
  status?: MetricStatus;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
}

/**
 * Financial report data model
 */
export interface FinancialReportData {
  reportType: string;
  name: string;
  description?: string;
  period: string;
  startDate: string;
  endDate: string;
  data?: any;
}

/**
 * Financial report response model
 */
export interface FinancialReportResponse {
  id: string;
  reportType: string;
  name: string;
  description?: string;
  period: string;
  startDate: Date;
  endDate: Date;
  data?: any;
  status: string;
  generatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
}

/**
 * Period comparison data model
 */
export interface PeriodComparisonData {
  comparisonType: string;
  name: string;
  description?: string;
  period1Start: string;
  period1End: string;
  period2Start: string;
  period2End: string;
  metrics: any;
}

/**
 * Period comparison response model
 */
export interface PeriodComparisonResponse {
  id: string;
  comparisonType: string;
  name: string;
  description?: string;
  period1Start: Date;
  period1End: Date;
  period2Start: Date;
  period2End: Date;
  metrics: any;
  changePercent?: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
}

/**
 * Financial dashboard response model
 */
export interface FinancialDashboardResponse {
  keyMetrics: FinancialMetricResponse[];
  recentMetrics: FinancialMetricResponse[];
  recentReports: FinancialReportResponse[];
  recentComparisons: PeriodComparisonResponse[];
}
