import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { FinancialMetricType, MetricStatus } from "@prisma/client";

// Schema for creating a financial metric
const createMetricSchema = z.object({
  metricType: z.enum([
    FinancialMetricType.TRANSACTION_VOLUME,
    FinancialMetricType.REVENUE,
    FinancialMetricType.EXPENSE,
    FinancialMetricType.WALLET_BALANCE,
    FinancialMetricType.CARBON_ASSET_VALUE,
    FinancialMetricType.PROFIT_LOSS,
    FinancialMetricType.FEE_REVENUE,
    FinancialMetricType.TRADING_VOLUME,
    FinancialMetricType.AVERAGE_TRANSACTION_SIZE,
    FinancialMetricType.RETIREMENT_VOLUME,
    FinancialMetricType.TOKENIZATION_VOLUME,
  ]),
  name: z.string().min(1, "Name is required"),
  value: z.number(),
  previousValue: z.number().optional(),
  currency: z.string().default("USD"),
  period: z.string().min(1, "Period is required"),
  startDate: z.string(), // ISO date string
  endDate: z.string(), // ISO date string
  target: z.number().optional(),
  notes: z.string().optional(),
});

// Schema for creating a financial report
const createReportSchema = z.object({
  reportType: z.enum([
    "TRANSACTION_SUMMARY",
    "REVENUE_REPORT",
    "EXPENSE_REPORT",
    "ASSET_VALUATION",
    "PROFIT_LOSS",
    "BALANCE_SHEET",
    "CASH_FLOW",
    "TAX_SUMMARY",
    "TRADING_ACTIVITY",
    "CUSTOM",
  ]),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  period: z.string().min(1, "Period is required"),
  startDate: z.string(), // ISO date string
  endDate: z.string(), // ISO date string
  data: z.record(z.any()).optional(),
});

// Schema for creating a period comparison
const createComparisonSchema = z.object({
  comparisonType: z.enum([
    "MONTH_OVER_MONTH",
    "QUARTER_OVER_QUARTER",
    "YEAR_OVER_YEAR",
    "CUSTOM",
  ]),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  period1Start: z.string(), // ISO date string
  period1End: z.string(), // ISO date string
  period2Start: z.string(), // ISO date string
  period2End: z.string(), // ISO date string
  metrics: z.record(z.any()),
});

// GET handler for financial analytics
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access financial analytics" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to access financial analytics" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(req.url);
    const type = searchParams.get("type") || "metrics";
    const period = searchParams.get("period");
    const metricType = searchParams.get("metricType");
    const reportType = searchParams.get("reportType");
    const comparisonType = searchParams.get("comparisonType");
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 10;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    if (type === "metrics") {
      // Build filter for metrics
      const filter: any = {
        organizationId: session.user.organizationId,
      };

      if (metricType) {
        filter.metricType = metricType;
      }

      if (period) {
        filter.period = period;
      }

      // Apply tenant isolation
      let metricsQuery = {
        where: filter,
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
      };

      metricsQuery = withTenantQuery(metricsQuery, tenantContext);

      // Get metrics
      const metrics = await db.financialMetric.findMany(metricsQuery);

      return NextResponse.json({
        metrics,
        count: metrics.length,
      });
    } else if (type === "reports") {
      // Build filter for reports
      const filter: any = {
        organizationId: session.user.organizationId,
      };

      if (reportType) {
        filter.reportType = reportType;
      }

      if (period) {
        filter.period = period;
      }

      // Apply tenant isolation
      let reportsQuery = {
        where: filter,
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
      };

      reportsQuery = withTenantQuery(reportsQuery, tenantContext);

      // Get reports
      const reports = await db.financialReport.findMany(reportsQuery);

      return NextResponse.json({
        reports,
        count: reports.length,
      });
    } else if (type === "comparisons") {
      // Build filter for comparisons
      const filter: any = {
        organizationId: session.user.organizationId,
      };

      if (comparisonType) {
        filter.comparisonType = comparisonType;
      }

      // Apply tenant isolation
      let comparisonsQuery = {
        where: filter,
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
      };

      comparisonsQuery = withTenantQuery(comparisonsQuery, tenantContext);

      // Get comparisons
      const comparisons = await db.periodComparison.findMany(comparisonsQuery);

      return NextResponse.json({
        comparisons,
        count: comparisons.length,
      });
    } else if (type === "dashboard") {
      // Get summary data for dashboard
      
      // Get recent metrics
      let metricsQuery = {
        where: {
          organizationId: session.user.organizationId,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      };

      metricsQuery = withTenantQuery(metricsQuery, tenantContext);
      const recentMetrics = await db.financialMetric.findMany(metricsQuery);

      // Get recent reports
      let reportsQuery = {
        where: {
          organizationId: session.user.organizationId,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 3,
      };

      reportsQuery = withTenantQuery(reportsQuery, tenantContext);
      const recentReports = await db.financialReport.findMany(reportsQuery);

      // Get recent comparisons
      let comparisonsQuery = {
        where: {
          organizationId: session.user.organizationId,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 3,
      };

      comparisonsQuery = withTenantQuery(comparisonsQuery, tenantContext);
      const recentComparisons = await db.periodComparison.findMany(comparisonsQuery);

      // Get key metrics
      const keyMetricsQuery = {
        where: {
          organizationId: session.user.organizationId,
          metricType: {
            in: [
              FinancialMetricType.TRANSACTION_VOLUME,
              FinancialMetricType.REVENUE,
              FinancialMetricType.WALLET_BALANCE,
              FinancialMetricType.CARBON_ASSET_VALUE,
            ],
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        distinct: ["metricType"],
        take: 4,
      };

      const keyMetrics = await db.financialMetric.findMany(keyMetricsQuery);

      return NextResponse.json({
        keyMetrics,
        recentMetrics,
        recentReports,
        recentComparisons,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid type" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Error fetching financial analytics:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching financial analytics" },
      { status: 500 }
    );
  }
}

// POST handler for creating financial analytics data
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create financial analytics data" },
        { status: 401 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to create financial analytics data" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(req.url);
    const type = searchParams.get("type") || "metric";

    const body = await req.json();

    if (type === "metric") {
      const validatedData = createMetricSchema.parse(body);

      // Calculate change percentage if previous value is provided
      let changePercent = null;
      if (validatedData.previousValue !== undefined && validatedData.previousValue !== 0) {
        changePercent = ((validatedData.value - validatedData.previousValue) / Math.abs(validatedData.previousValue)) * 100;
      }

      // Determine status if target is provided
      let status = null;
      if (validatedData.target !== undefined) {
        const percentOfTarget = (validatedData.value / validatedData.target) * 100;
        
        if (percentOfTarget >= 100) {
          status = MetricStatus.ABOVE_TARGET;
        } else if (percentOfTarget >= 90) {
          status = MetricStatus.ON_TARGET;
        } else if (percentOfTarget >= 75) {
          status = MetricStatus.BELOW_TARGET;
        } else {
          status = MetricStatus.CRITICAL;
        }
      }

      // Create metric
      const metric = await db.financialMetric.create({
        data: {
          ...validatedData,
          changePercent,
          status,
          startDate: new Date(validatedData.startDate),
          endDate: new Date(validatedData.endDate),
          organization: {
            connect: {
              id: session.user.organizationId,
            },
          },
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: "FINANCIAL_METRIC_CREATED",
          description: `Created financial metric: ${validatedData.name}`,
          userId: session.user.id,
          organizationId: session.user.organizationId,
          metadata: {
            metricId: metric.id,
            metricType: validatedData.metricType,
            value: validatedData.value,
          },
        },
      });

      return NextResponse.json({
        metric,
        message: "Financial metric created successfully",
      });
    } else if (type === "report") {
      const validatedData = createReportSchema.parse(body);

      // Create report
      const report = await db.financialReport.create({
        data: {
          ...validatedData,
          startDate: new Date(validatedData.startDate),
          endDate: new Date(validatedData.endDate),
          status: "DRAFT",
          generatedBy: session.user.id,
          organization: {
            connect: {
              id: session.user.organizationId,
            },
          },
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: "FINANCIAL_REPORT_GENERATED",
          description: `Generated financial report: ${validatedData.name}`,
          userId: session.user.id,
          organizationId: session.user.organizationId,
          metadata: {
            reportId: report.id,
            reportType: validatedData.reportType,
          },
        },
      });

      return NextResponse.json({
        report,
        message: "Financial report created successfully",
      });
    } else if (type === "comparison") {
      const validatedData = createComparisonSchema.parse(body);

      // Calculate overall change percentage
      let changePercent = null;
      if (validatedData.metrics && typeof validatedData.metrics === "object") {
        const metrics = Object.values(validatedData.metrics);
        if (metrics.length > 0) {
          const changes = metrics
            .filter((m: any) => m.changePercent !== undefined)
            .map((m: any) => m.changePercent);
          
          if (changes.length > 0) {
            changePercent = changes.reduce((sum: number, val: number) => sum + val, 0) / changes.length;
          }
        }
      }

      // Create comparison
      const comparison = await db.periodComparison.create({
        data: {
          ...validatedData,
          period1Start: new Date(validatedData.period1Start),
          period1End: new Date(validatedData.period1End),
          period2Start: new Date(validatedData.period2Start),
          period2End: new Date(validatedData.period2End),
          changePercent,
          createdBy: session.user.id,
          organization: {
            connect: {
              id: session.user.organizationId,
            },
          },
        },
      });

      // Create audit log
      await db.auditLog.create({
        data: {
          type: "PERIOD_COMPARISON_CREATED",
          description: `Created period comparison: ${validatedData.name}`,
          userId: session.user.id,
          organizationId: session.user.organizationId,
          metadata: {
            comparisonId: comparison.id,
            comparisonType: validatedData.comparisonType,
          },
        },
      });

      return NextResponse.json({
        comparison,
        message: "Period comparison created successfully",
      });
    } else {
      return NextResponse.json(
        { error: "Invalid type" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Error creating financial analytics data:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating financial analytics data" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
