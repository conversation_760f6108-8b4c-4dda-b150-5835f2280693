import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { AuditStatus } from "@prisma/client";

// Schema for auditing a transaction
const auditTransactionSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
  status: z.enum([
    AuditStatus.PENDING,
    AuditStatus.VERIFIED,
    AuditStatus.FLAGGED,
    AuditStatus.RECONCILED,
    AuditStatus.NEEDS_REVIEW,
  ]),
  notes: z.string().optional(),
  flagged: z.boolean().optional(),
  flagReason: z.string().optional(),
  reconciled: z.boolean().optional(),
  documentUrls: z.array(z.string()).optional(),
});

// GET handler for transaction audits
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access transaction audits" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const transactionId = searchParams.get("transactionId");
    const status = searchParams.get("status");
    const flagged = searchParams.get("flagged") === "true";
    const reconciled = searchParams.get("reconciled") === "true";
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20;
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Build filter
    const filter: any = {};

    if (transactionId) {
      filter.transactionId = transactionId;
    } else {
      // If no specific transaction ID, only show transactions for the user's organization
      if (session.user.organizationId) {
        filter.transaction = {
          wallet: {
            organizationId: session.user.organizationId,
          },
        };
      } else {
        // For individual users, only show their own transactions
        filter.transaction = {
          wallet: {
            userId: session.user.id,
          },
        };
      }
    }

    if (status) {
      filter.status = status;
    }

    if (searchParams.has("flagged")) {
      filter.flagged = flagged;
    }

    if (searchParams.has("reconciled")) {
      filter.reconciled = reconciled;
    }

    // Apply tenant isolation
    let auditQuery = {
      where: filter,
      include: {
        transaction: {
          include: {
            wallet: {
              select: {
                id: true,
                address: true,
                network: true,
                userId: true,
                organizationId: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    };

    auditQuery = withTenantQuery(auditQuery, tenantContext);

    // Get transaction audits
    const audits = await db.transactionAudit.findMany(auditQuery);

    // Get total count
    const totalCount = await db.transactionAudit.count({
      where: filter,
    });

    return NextResponse.json({
      audits,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + audits.length < totalCount,
      },
    });
  } catch (error) {
    logger.error("Error fetching transaction audits:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching transaction audits" },
      { status: 500 }
    );
  }
}

// POST handler for auditing a transaction
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to audit a transaction" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const {
      transactionId,
      status,
      notes,
      flagged,
      flagReason,
      reconciled,
      documentUrls,
    } = auditTransactionSchema.parse(body);

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the transaction exists and belongs to the user's organization
    let transactionQuery = {
      where: {
        id: transactionId,
      },
      include: {
        wallet: {
          select: {
            userId: true,
            organizationId: true,
          },
        },
      },
    };

    transactionQuery = withTenantQuery(transactionQuery, tenantContext);
    const transaction = await db.transaction.findFirst(transactionQuery);

    if (!transaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      );
    }

    // Check if the user has permission to audit this transaction
    const isOwnTransaction = transaction.wallet.userId === session.user.id;
    const isOrgTransaction = session.user.organizationId && transaction.wallet.organizationId === session.user.organizationId;
    
    if (!isOwnTransaction && !isOrgTransaction) {
      return NextResponse.json(
        { error: "You do not have permission to audit this transaction" },
        { status: 403 }
      );
    }

    // Check if an audit already exists for this transaction
    const existingAudit = await db.transactionAudit.findFirst({
      where: {
        transactionId,
      },
    });

    let audit;
    if (existingAudit) {
      // Update existing audit
      audit = await db.transactionAudit.update({
        where: {
          id: existingAudit.id,
        },
        data: {
          status,
          notes,
          flagged: flagged ?? existingAudit.flagged,
          flagReason: flagReason ?? existingAudit.flagReason,
          flaggedBy: flagged ? session.user.id : existingAudit.flaggedBy,
          flaggedAt: flagged ? new Date() : existingAudit.flaggedAt,
          reconciled: reconciled ?? existingAudit.reconciled,
          reconciledBy: reconciled ? session.user.id : existingAudit.reconciledBy,
          reconciledAt: reconciled ? new Date() : existingAudit.reconciledAt,
          verifiedBy: status === AuditStatus.VERIFIED ? session.user.id : existingAudit.verifiedBy,
          verifiedAt: status === AuditStatus.VERIFIED ? new Date() : existingAudit.verifiedAt,
          documentUrls: documentUrls ?? existingAudit.documentUrls,
        },
      });
    } else {
      // Create new audit
      audit = await db.transactionAudit.create({
        data: {
          transaction: {
            connect: {
              id: transactionId,
            },
          },
          status,
          notes,
          flagged: flagged ?? false,
          flagReason,
          flaggedBy: flagged ? session.user.id : null,
          flaggedAt: flagged ? new Date() : null,
          reconciled: reconciled ?? false,
          reconciledBy: reconciled ? session.user.id : null,
          reconciledAt: reconciled ? new Date() : null,
          verifiedBy: status === AuditStatus.VERIFIED ? session.user.id : null,
          verifiedAt: status === AuditStatus.VERIFIED ? new Date() : null,
          documentUrls: documentUrls ?? [],
        },
      });
    }

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "TRANSACTION_AUDITED",
        description: `Transaction ${transactionId} audited with status ${status}`,
        userId: session.user.id,
        organizationId: session.user.organizationId,
        metadata: {
          transactionId,
          auditId: audit.id,
          status,
          flagged,
          reconciled,
        },
      },
    });

    // Create notification for transaction owner if different from auditor
    if (transaction.wallet.userId !== session.user.id) {
      await db.notification.create({
        data: {
          title: "Transaction Audited",
          message: `Your transaction has been audited with status: ${status}`,
          type: "TRANSACTION",
          priority: flagged ? "HIGH" : "NORMAL",
          user: {
            connect: {
              id: transaction.wallet.userId,
            },
          },
          actionUrl: `/wallet/transactions/${transactionId}`,
          actionLabel: "View Transaction",
        },
      });
    }

    return NextResponse.json({
      audit,
      message: "Transaction audited successfully",
    });
  } catch (error) {
    logger.error("Error auditing transaction:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while auditing the transaction" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
