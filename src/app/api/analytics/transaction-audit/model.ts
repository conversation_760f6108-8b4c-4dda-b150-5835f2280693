import { AuditStatus } from "@prisma/client";

/**
 * Transaction audit data model
 */
export interface TransactionAuditData {
  transactionId: string;
  status: AuditStatus;
  notes?: string;
  flagged?: boolean;
  flagReason?: string;
  reconciled?: boolean;
  documentUrls?: string[];
}

/**
 * Transaction audit response model
 */
export interface TransactionAuditResponse {
  id: string;
  transactionId: string;
  status: AuditStatus;
  notes?: string;
  flagged: boolean;
  flagReason?: string;
  flaggedBy?: string;
  flaggedAt?: Date;
  reconciled: boolean;
  reconciledBy?: string;
  reconciledAt?: Date;
  verifiedBy?: string;
  verifiedAt?: Date;
  documentUrls: string[];
  createdAt: Date;
  updatedAt: Date;
  transaction?: {
    id: string;
    amount: number;
    fee: number;
    type: string;
    status: string;
    transactionHash?: string;
    wallet: {
      id: string;
      address: string;
      network: string;
      userId: string;
      organizationId?: string;
    };
  };
}

/**
 * Transaction audit pagination response
 */
export interface TransactionAuditPaginationResponse {
  audits: TransactionAuditResponse[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
