import { AssetType, ValuationMethod } from "@prisma/client";

/**
 * Asset valuation data model
 */
export interface AssetValuationData {
  assetType: AssetType;
  assetId: string;
  valuationMethod: ValuationMethod;
  valueAmount: number;
  valueCurrency?: string;
  previousValue?: number;
  valuationNotes?: string;
  dataSource?: string;
  confidence?: number;
  transactionId?: string;
}

/**
 * Asset valuation response model
 */
export interface AssetValuationResponse {
  id: string;
  assetType: AssetType;
  assetId: string;
  valuationMethod: ValuationMethod;
  valueAmount: number;
  valueCurrency: string;
  previousValue?: number;
  changePercentage?: number;
  valuationDate: Date;
  valuationNotes?: string;
  dataSource?: string;
  confidence?: number;
  approvedBy?: string;
  approvedAt?: Date;
  transactionId?: string;
  carbonCreditId?: string;
  createdAt: Date;
  updatedAt: Date;
  transaction?: {
    id: string;
    amount: number;
    type: string;
    status: string;
    wallet: {
      id: string;
      address: string;
      network: string;
    };
  };
}

/**
 * Asset valuation pagination response
 */
export interface AssetValuationPaginationResponse {
  valuations: AssetValuationResponse[];
  assetDetails?: any;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
