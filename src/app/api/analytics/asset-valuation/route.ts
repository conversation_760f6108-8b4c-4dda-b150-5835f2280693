import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";
import { AssetType, ValuationMethod } from "@prisma/client";

// Schema for creating an asset valuation
const createValuationSchema = z.object({
  assetType: z.enum([
    AssetType.CARBON_CREDIT,
    AssetType.TOKEN,
    AssetType.NFT,
    AssetType.WALLET,
    AssetType.PORTFOLIO,
  ]),
  assetId: z.string().min(1, "Asset ID is required"),
  valuationMethod: z.enum([
    ValuationMethod.MARKET_PRICE,
    ValuationMethod.HISTORICAL_COST,
    ValuationMethod.FAIR_VALUE,
    ValuationMethod.DISCOUNTED_CASH_FLOW,
    ValuationMethod.COMPARABLE_SALES,
    ValuationMethod.WEIGHTED_AVERAGE,
    ValuationMethod.MANUAL,
  ]),
  valueAmount: z.number().positive("Value amount must be positive"),
  valueCurrency: z.string().default("USD"),
  previousValue: z.number().optional(),
  valuationNotes: z.string().optional(),
  dataSource: z.string().optional(),
  confidence: z.number().int().min(0).max(100).optional(),
  transactionId: z.string().optional(),
});

// GET handler for asset valuations
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access asset valuations" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const assetType = searchParams.get("assetType") as AssetType | null;
    const assetId = searchParams.get("assetId");
    const valuationMethod = searchParams.get("valuationMethod") as ValuationMethod | null;
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20;
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0;

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Build filter
    const filter: any = {};

    if (assetType) {
      filter.assetType = assetType;
    }

    if (assetId) {
      filter.assetId = assetId;
    }

    if (valuationMethod) {
      filter.valuationMethod = valuationMethod;
    }

    if (startDate || endDate) {
      filter.valuationDate = {};
      
      if (startDate) {
        filter.valuationDate.gte = new Date(startDate);
      }
      
      if (endDate) {
        filter.valuationDate.lte = new Date(endDate);
      }
    }

    // Apply tenant isolation based on asset type
    if (assetType === AssetType.CARBON_CREDIT) {
      // For carbon credits, check if they belong to the user's organization
      filter.OR = [
        {
          carbonCredit: {
            organizationId: session.user.organizationId,
          },
        },
        {
          carbonCredit: {
            status: "LISTED", // Public carbon credits
          },
        },
      ];
    } else if (assetType === AssetType.WALLET || assetType === AssetType.TOKEN) {
      // For wallets and tokens, check if they belong to the user
      if (session.user.organizationId) {
        filter.OR = [
          {
            transaction: {
              wallet: {
                userId: session.user.id,
              },
            },
          },
          {
            transaction: {
              wallet: {
                organizationId: session.user.organizationId,
              },
            },
          },
        ];
      } else {
        filter.transaction = {
          wallet: {
            userId: session.user.id,
          },
        };
      }
    }

    // Apply tenant isolation
    let valuationQuery = {
      where: filter,
      include: {
        transaction: {
          select: {
            id: true,
            amount: true,
            type: true,
            status: true,
            wallet: {
              select: {
                id: true,
                address: true,
                network: true,
              },
            },
          },
        },
      },
      orderBy: {
        valuationDate: "desc",
      },
      skip: offset,
      take: limit,
    };

    valuationQuery = withTenantQuery(valuationQuery, tenantContext);

    // Get asset valuations
    const valuations = await db.assetValuation.findMany(valuationQuery);

    // Get total count
    const totalCount = await db.assetValuation.count({
      where: filter,
    });

    // Get asset details based on asset type
    let assetDetails = null;
    if (assetId && assetType) {
      if (assetType === AssetType.CARBON_CREDIT) {
        const carbonCredit = await db.carbonCredit.findUnique({
          where: { id: assetId },
          select: {
            id: true,
            name: true,
            standard: true,
            vintage: true,
            quantity: true,
            availableQuantity: true,
            price: true,
          },
        });
        assetDetails = carbonCredit;
      } else if (assetType === AssetType.WALLET) {
        const wallet = await db.wallet.findUnique({
          where: { id: assetId },
          select: {
            id: true,
            address: true,
            network: true,
            balance: true,
          },
        });
        assetDetails = wallet;
      } else if (assetType === AssetType.TOKEN) {
        const token = await db.token.findUnique({
          where: { id: assetId },
          select: {
            id: true,
            address: true,
            symbol: true,
            name: true,
            balance: true,
          },
        });
        assetDetails = token;
      }
    }

    return NextResponse.json({
      valuations,
      assetDetails,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + valuations.length < totalCount,
      },
    });
  } catch (error) {
    logger.error("Error fetching asset valuations:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching asset valuations" },
      { status: 500 }
    );
  }
}

// POST handler for creating an asset valuation
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create an asset valuation" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = createValuationSchema.parse(body);

    // Calculate change percentage if previous value is provided
    let changePercentage = null;
    if (validatedData.previousValue !== undefined && validatedData.previousValue !== 0) {
      changePercentage = ((validatedData.valueAmount - validatedData.previousValue) / Math.abs(validatedData.previousValue)) * 100;
    }

    // Verify asset exists and user has access to it
    if (validatedData.assetType === AssetType.CARBON_CREDIT) {
      const carbonCredit = await db.carbonCredit.findUnique({
        where: { id: validatedData.assetId },
        select: {
          id: true,
          userId: true,
          organizationId: true,
          status: true,
        },
      });

      if (!carbonCredit) {
        return NextResponse.json(
          { error: "Carbon credit not found" },
          { status: 404 }
        );
      }

      // Check if user has access to this carbon credit
      const isOwner = carbonCredit.userId === session.user.id;
      const isOrgMember = session.user.organizationId && carbonCredit.organizationId === session.user.organizationId;
      const isPublic = carbonCredit.status === "LISTED";

      if (!isOwner && !isOrgMember && !isPublic) {
        return NextResponse.json(
          { error: "You do not have permission to value this asset" },
          { status: 403 }
        );
      }
    } else if (validatedData.assetType === AssetType.WALLET) {
      const wallet = await db.wallet.findUnique({
        where: { id: validatedData.assetId },
        select: {
          id: true,
          userId: true,
          organizationId: true,
        },
      });

      if (!wallet) {
        return NextResponse.json(
          { error: "Wallet not found" },
          { status: 404 }
        );
      }

      // Check if user has access to this wallet
      const isOwner = wallet.userId === session.user.id;
      const isOrgMember = session.user.organizationId && wallet.organizationId === session.user.organizationId;

      if (!isOwner && !isOrgMember) {
        return NextResponse.json(
          { error: "You do not have permission to value this asset" },
          { status: 403 }
        );
      }
    } else if (validatedData.assetType === AssetType.TOKEN) {
      const token = await db.token.findUnique({
        where: { id: validatedData.assetId },
        select: {
          id: true,
          wallet: {
            select: {
              userId: true,
              organizationId: true,
            },
          },
        },
      });

      if (!token) {
        return NextResponse.json(
          { error: "Token not found" },
          { status: 404 }
        );
      }

      // Check if user has access to this token
      const isOwner = token.wallet.userId === session.user.id;
      const isOrgMember = session.user.organizationId && token.wallet.organizationId === session.user.organizationId;

      if (!isOwner && !isOrgMember) {
        return NextResponse.json(
          { error: "You do not have permission to value this asset" },
          { status: 403 }
        );
      }
    }

    // If transaction ID is provided, verify it exists and user has access to it
    if (validatedData.transactionId) {
      const transaction = await db.transaction.findUnique({
        where: { id: validatedData.transactionId },
        select: {
          id: true,
          wallet: {
            select: {
              userId: true,
              organizationId: true,
            },
          },
        },
      });

      if (!transaction) {
        return NextResponse.json(
          { error: "Transaction not found" },
          { status: 404 }
        );
      }

      // Check if user has access to this transaction
      const isOwner = transaction.wallet.userId === session.user.id;
      const isOrgMember = session.user.organizationId && transaction.wallet.organizationId === session.user.organizationId;

      if (!isOwner && !isOrgMember) {
        return NextResponse.json(
          { error: "You do not have permission to associate this transaction" },
          { status: 403 }
        );
      }
    }

    // Create asset valuation
    const valuation = await db.assetValuation.create({
      data: {
        ...validatedData,
        changePercentage,
        approvedBy: session.user.id,
        approvedAt: new Date(),
        ...(validatedData.transactionId ? {
          transaction: {
            connect: {
              id: validatedData.transactionId,
            },
          },
        } : {}),
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "ASSET_VALUATION_CREATED",
        description: `Created ${validatedData.assetType} valuation for asset ${validatedData.assetId}`,
        userId: session.user.id,
        organizationId: session.user.organizationId,
        metadata: {
          valuationId: valuation.id,
          assetType: validatedData.assetType,
          assetId: validatedData.assetId,
          valueAmount: validatedData.valueAmount,
          valuationMethod: validatedData.valuationMethod,
        },
      },
    });

    return NextResponse.json({
      valuation,
      message: "Asset valuation created successfully",
    });
  } catch (error) {
    logger.error("Error creating asset valuation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the asset valuation" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
