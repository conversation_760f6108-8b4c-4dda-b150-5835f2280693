import { NextRequest, NextResponse } from "next/server";
import { analyticsService } from "@/lib/analytics";
import { auth } from "@/lib/auth";

import { logger } from "@/lib/logger";

/**
 * GET /api/analytics/user/[id]
 * Get user activity analytics
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const userId = (await context.params).id;

    // Check authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is requesting their own data or is an admin
    const isOwnData = session.user.id === userId;
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" &&
                      session.user.organizationId === session.user.organizationId;

    if (!isOwnData && !isAdmin && !isOrgAdmin) {
      return NextResponse.json(
        { error: "Forbidden: You don't have access to this user's analytics" },
        { status: 403 }
      );
    }

    // Get user activity
    const activity = await analyticsService.getUserActivity(userId);

    return NextResponse.json(activity);
  } catch (error) {
    logger.error("Error in user analytics API:", error);
    return NextResponse.json(
      { error: "Failed to get user analytics" },
      { status: 500 }
    );
  }
}
