import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";

// Original DELETE handler
async function deleteHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete notifications" },
        { status: 401 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query with tenant isolation
    let notificationQuery = {
      where: {
        id: params.id,
      },
    };

    // Apply tenant isolation
    notificationQuery = withTenantIsolation(notificationQuery, tenantContext);

    // Find the notification
    const notification = await db.notification.findFirst(notificationQuery);

    if (!notification) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if the notification belongs to the user
    if (notification.userId !== session.user.id) {
      return NextResponse.json(
        { error: "You do not have permission to delete this notification" },
        { status: 403 }
      );
    }

    // Delete the notification
    await db.notification.delete({
      where: {
        id: params.id,
      },
    });

    logger.info(`User ${session.user.id} deleted notification ${params.id}`);

    return NextResponse.json({
      message: "Notification deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting notification ${params.id}:`, error);

    return NextResponse.json(
      { error: "An error occurred while deleting the notification" },
      { status: 500 }
    );
  }
}

// Export the handler with resource isolation middleware
export const DELETE = withResourceIsolation('notification', 'id')(deleteHandler);
