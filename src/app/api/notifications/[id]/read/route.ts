import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update notifications" },
        { status: 401 }
      );
    }

    // Find the notification
    const notification = await db.notification.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!notification) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if the notification belongs to the user
    if (notification.userId !== session.user.id) {
      return NextResponse.json(
        { error: "You do not have permission to update this notification" },
        { status: 403 }
      );
    }

    // Mark the notification as read
    await db.notification.update({
      where: {
        id: params.id,
      },
      data: {
        read: true,
      },
    });

    logger.info(`User ${session.user.id} marked notification ${params.id} as read`);

    return NextResponse.json({
      message: "Notification marked as read",
    });
  } catch (error) {
    logger.error(`Error marking notification ${params.id} as read:`, error);
    
    return NextResponse.json(
      { error: "An error occurred while marking the notification as read" },
      { status: 500 }
    );
  }
}
