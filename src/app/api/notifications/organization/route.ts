import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";

import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";

// Schema for creating organization notifications
const createOrgNotificationSchema = z.object({
  title: z.string().min(1, "Title is required"),
  message: z.string().min(1, "Message is required"),
  organizationId: z.string().min(1, "Organization ID is required"),
  channels: z.array(z.enum(["in_app", "email", "sms", "webhook"])).optional(),
});

/**
 * POST /api/notifications/organization
 * Create notifications for all users in an organization
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create notifications" },
        { status: 401 }
      );
    }

    // Only admins and organization admins can create organization-wide notifications
    const isAdmin = session.user.role === "ADMIN";
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN";

    if (!isAdmin && !isOrgAdmin) {
      return NextResponse.json(
        { error: "You do not have permission to create organization notifications" },
        { status: 403 }
      );
    }

    // Parse and validate the request body
    const body = await req.json();
    const validatedData = createOrgNotificationSchema.parse(body);

    // If organization admin, check if they belong to the organization
    if (isOrgAdmin && session.user.organizationId !== validatedData.organizationId) {
      return NextResponse.json(
        { error: "You can only create notifications for your own organization" },
        { status: 403 }
      );
    }

    // Convert channels to NotificationChannel enum
    const channels = validatedData.channels?.map(
      (channel) => NotificationChannel[channel.toUpperCase() as keyof typeof NotificationChannel]
    ) || [NotificationChannel.IN_APP];

    // Create notifications for all users in the organization
    const count = await notificationService.notifyOrganization(
      validatedData.organizationId,
      validatedData.title,
      validatedData.message,
      channels
    );

    logger.info(
      `User ${session.user.id} created ${count} notifications for organization ${validatedData.organizationId}`
    );

    return NextResponse.json({
      message: `${count} notifications created successfully`,
      count,
    });
  } catch (error) {
    logger.error("Error creating organization notifications:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating organization notifications" },
      { status: 500 }
    );
  }
}
