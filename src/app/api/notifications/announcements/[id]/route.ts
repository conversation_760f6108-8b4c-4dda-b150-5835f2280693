import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { NotificationType } from "@prisma/client";

/**
 * GET /api/notifications/announcements/[id]
 * Get a specific system notification used as an announcement
 *
 * NOTE: This route is a placeholder implementation using Notification model
 * as there's no dedicated Announcement model in the schema yet.
 */
async function getAnnouncementHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view announcements",
        ErrorType.AUTHENTICATION,
        401
      );
    }
    
    const notificationId = params.id;
    
    // Get notification
    const notification = await db.notification.findUnique({
      where: {
        id: notificationId,
        type: NotificationType.SYSTEM
      },
    });
    
    if (!notification) {
      throw new ApiError(
        "Announcement not found",
        ErrorType.NOT_FOUND,
        404
      );
    }
    
    // Check if notification belongs to user or user is admin
    if (notification.userId !== session.user.id && session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to view this announcement",
        ErrorType.FORBIDDEN,
        403
      );
    }
    
    return NextResponse.json({ announcement: notification });
  } catch (error) {
    logger.error(`Error getting announcement ${params.id}:`, error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while getting announcement",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PATCH /api/notifications/announcements/[id]
 * Update a specific notification (admin only)
 */
async function updateAnnouncementHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update announcements",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to update announcements",
        ErrorType.FORBIDDEN,
        403
      );
    }

    const notificationId = params.id;
    const body = await req.json();
    
    // Check if notification exists
    const existingNotification = await db.notification.findUnique({
      where: {
        id: notificationId,
        type: NotificationType.SYSTEM
      },
    });
    
    if (!existingNotification) {
      throw new ApiError(
        "Announcement not found",
        ErrorType.NOT_FOUND,
        404
      );
    }
    
    // Update notification
    const updatedNotification = await db.notification.update({
      where: { id: notificationId },
      data: {
        ...(body.title !== undefined && { title: body.title }),
        ...(body.message !== undefined && { message: body.message }),
        ...(body.priority !== undefined && { priority: body.priority }),
        ...(body.actionUrl !== undefined && { actionUrl: body.actionUrl }),
        ...(body.actionLabel !== undefined && { actionLabel: body.actionLabel })
      },
    });
    
    logger.info(`Announcement updated: ${notificationId} by user ${session.user.id}`);
    
    return NextResponse.json({
      message: "Announcement updated successfully",
      announcement: updatedNotification,
    });
  } catch (error) {
    logger.error(`Error updating announcement ${params.id}:`, error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while updating announcement",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/notifications/announcements/[id]
 * Delete a specific notification (admin only)
 */
async function deleteAnnouncementHandler(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to delete announcements",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to delete announcements",
        ErrorType.FORBIDDEN,
        403
      );
    }

    const notificationId = params.id;
    
    // Check if notification exists
    const existingNotification = await db.notification.findUnique({
      where: {
        id: notificationId,
        type: NotificationType.SYSTEM
      },
    });
    
    if (!existingNotification) {
      throw new ApiError(
        "Announcement not found",
        ErrorType.NOT_FOUND,
        404
      );
    }
    
    // Delete notification
    await db.notification.delete({
      where: { id: notificationId },
    });
    
    logger.info(`Announcement deleted: ${notificationId} by user ${session.user.id}`);
    
    return NextResponse.json({
      message: "Announcement deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting announcement ${params.id}:`, error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while deleting announcement",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getAnnouncementHandler);
export const PATCH = withErrorHandling(updateAnnouncementHandler);
export const DELETE = withErrorHandling(deleteAnnouncementHandler);
