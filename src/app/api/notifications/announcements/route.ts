import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { NotificationType } from "@prisma/client";

/**
 * GET /api/notifications/announcements
 * Get system notifications for the user
 *
 * NOTE: This is a simplified implementation using the Notification model
 * since there's no dedicated Announcement model in the schema yet.
 */
async function getAnnouncementsHandler(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to view announcements",
        ErrorType.AUTHENTICATION,
        401
      );
    }
    
    // Get system notifications for the current user
    const notifications = await db.notification.findMany({
      where: {
        OR: [
          // System-wide notifications
          {
            type: NotificationType.SYSTEM,
            organizationId: null
          },
          // Organization-specific notifications if user belongs to an organization
          ...(session.user.organizationId ?
            [{
              type: NotificationType.SYSTEM,
              organizationId: session.user.organizationId
            }] :
            []
          )
        ]
      },
      orderBy: [
        { priority: "desc" },
        { createdAt: "desc" },
      ],
    });
    
    return NextResponse.json({ announcements: notifications });
  } catch (error) {
    logger.error("Error getting announcements:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while getting announcements",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * POST /api/notifications/announcements
 * Create a new system notification as an announcement (admin only)
 */
async function createAnnouncementHandler(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create announcements",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to create announcements",
        ErrorType.FORBIDDEN,
        403
      );
    }

    const body = await req.json();
    
    // Validate required fields
    if (!body.title || !body.message) {
      throw new ApiError(
        "Title and message are required",
        ErrorType.VALIDATION,
        400
      );
    }
    
    // Create a system notification
    const notification = await db.notification.create({
      data: {
        title: body.title,
        message: body.message,
        type: NotificationType.SYSTEM,
        priority: body.priority || "NORMAL",
        actionUrl: body.actionUrl,
        actionLabel: body.actionLabel,
        // If targeting a specific organization
        ...(body.organizationId ? {
          organization: {
            connect: { id: body.organizationId }
          }
        } : {}),
        // Connect to the admin user
        user: {
          connect: { id: session.user.id }
        }
      },
    });
    
    logger.info(`Announcement created: ${notification.id} by user ${session.user.id}`);
    
    return NextResponse.json({
      message: "Announcement created successfully",
      announcement: notification,
    });
  } catch (error) {
    logger.error("Error creating announcement:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while creating announcement",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getAnnouncementsHandler);
export const POST = withErrorHandling(createAnnouncementHandler);
