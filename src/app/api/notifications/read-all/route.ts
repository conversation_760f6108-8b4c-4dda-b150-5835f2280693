import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";

export async function PATCH(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update notifications" },
        { status: 401 }
      );
    }

    // Mark all notifications as read
    await db.notification.updateMany({
      where: {
        userId: session.user.id,
        read: false,
      },
      data: {
        read: true,
      },
    });

    logger.info(`User ${session.user.id} marked all notifications as read`);

    return NextResponse.json({
      message: "All notifications marked as read",
    });
  } catch (error) {
    logger.error("Error marking all notifications as read:", error);
    
    return NextResponse.json(
      { error: "An error occurred while marking all notifications as read" },
      { status: 500 }
    );
  }
}
