import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";
import { gasEstimationService } from "@/lib/gas-estimation";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";

// Schema for retirement gas estimation request
const retirementGasEstimationSchema = z.object({
  carbonCreditId: z.string().uuid("Invalid carbon credit ID"),
  amount: z.number().positive("Amount must be positive"),
});

/**
 * Estimate gas for retiring a carbon credit
 */
async function retireGasHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to estimate gas",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to estimate gas",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { carbonCreditId, amount } = retirementGasEstimationSchema.parse(body);

    // Check if the carbon credit exists and belongs to the organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: carbonCreditId,
        organizationId: session.user.organizationId,
        status: "TOKENIZED", // Only tokenized carbon credits can be retired
      },
      include: {
        organization: {
          include: {
            wallets: true, // Fix: use 'wallets' (plural) instead of 'wallet'
          },
        },
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found, does not belong to your organization, or is not tokenized",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the organization has wallets
    if (!carbonCredit.organization.wallets || carbonCredit.organization.wallets.length === 0) {
      throw new ApiError(
        "Organization does not have any wallets",
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the amount is available
    if (amount > (carbonCredit.availableQuantity || carbonCredit.quantity)) {
      throw new ApiError(
        `Not enough quantity available. Available: ${carbonCredit.availableQuantity || carbonCredit.quantity} tons`,
        ErrorType.VALIDATION,
        400
      );
    }

    // Check if the token ID is available
    if (!carbonCredit.tokenId) {
      throw new ApiError(
        "Carbon credit does not have a token ID",
        ErrorType.VALIDATION,
        400
      );
    }

    // Get properties we need
    const network = carbonCredit.contractAddress ? new URL(carbonCredit.contractAddress).hostname.split('.')[0] : 'ethereum';
    
    // Estimate gas for retirement
    const gasEstimation = await gasEstimationService.estimateRetirementGas(
      carbonCredit.organization.wallets[0].address, // Use the first wallet
      parseInt(carbonCredit.tokenId as string),
      amount,
      network as SupportedNetwork,
      true // Use testnet for now
    );

    logger.info(`Gas estimation for retiring carbon credit ${carbonCreditId}: ${JSON.stringify(gasEstimation)}`);

    return NextResponse.json({
      gasEstimation,
    });
  } catch (error) {
    logger.error("Error estimating retirement gas:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to estimate retirement gas",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(retireGasHandler);
