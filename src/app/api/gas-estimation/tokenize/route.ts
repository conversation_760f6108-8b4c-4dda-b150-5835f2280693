import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";

import { logger } from "@/lib/logger";
import { gasEstimationService } from "@/lib/gas-estimation";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { SupportedNetwork } from "@/lib/blockchain-config";

// Schema for tokenization gas estimation request
const tokenizationGasEstimationSchema = z.object({
  carbonCreditId: z.string().uuid("Invalid carbon credit ID"),
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]).default("polygon"),
  useTestnet: z.boolean().default(true),
});

/**
 * Estimate gas for tokenizing a carbon credit
 */
async function tokenizeGasHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to estimate gas",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to estimate gas",
      ErrorType.FORBIDDEN,
      403
    );
  }

  try {
    // Parse and validate request body
    const body = await req.json();
    const { carbonCreditId, network, useTestnet } = tokenizationGasEstimationSchema.parse(body);

    // Check if the carbon credit exists and belongs to the organization
    const carbonCredit = await db.carbonCredit.findFirst({
      where: {
        id: carbonCreditId,
        organizationId: session.user.organizationId,
      },
      include: {
        organization: {
          include: {
            wallets: true, // Fix: use 'wallets' (plural) instead of 'wallet'
          },
        },
      },
    });

    if (!carbonCredit) {
      throw new ApiError(
        "Carbon credit not found or does not belong to your organization",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the organization has wallets
    if (!carbonCredit.organization.wallets || carbonCredit.organization.wallets.length === 0) {
      throw new ApiError(
        "Organization does not have any wallets",
        ErrorType.VALIDATION,
        400
      );
    }

    // Generate a token ID based on the carbon credit ID
    // This is a simple hash function to generate a unique token ID
    const tokenId = parseInt(carbonCreditId.substring(0, 8), 16) % 1000000000;

    // Create metadata for the token
    const metadata = {
      projectId: carbonCredit.projectId || carbonCredit.id,
      vintage: carbonCredit.vintage,
      standard: carbonCredit.standard,
      methodology: carbonCredit.methodology,
    };

    // Estimate gas for tokenization
    const gasEstimation = await gasEstimationService.estimateTokenizationGas(
      tokenId,
      carbonCredit.quantity,
      carbonCredit.organization.wallets[0].address, // Use the first wallet
      metadata,
      network as SupportedNetwork,
      useTestnet
    );

    logger.info(`Gas estimation for tokenizing carbon credit ${carbonCreditId}: ${JSON.stringify(gasEstimation)}`);

    return NextResponse.json({
      gasEstimation,
    });
  } catch (error) {
    logger.error("Error estimating tokenization gas:", error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        error.errors.map((e) => e.message).join(", "),
        ErrorType.VALIDATION,
        400
      );
    }

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to estimate tokenization gas",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(tokenizeGasHandler);
