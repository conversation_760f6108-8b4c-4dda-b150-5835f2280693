"use client";

import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/animations/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { CustomReportBuilder } from "@/components/analytics/custom-report-builder";

export default function CustomReportsPage() {
  const { data: session } = useSession();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <PageHeaderWithBreadcrumb
            title="Custom Reports"
            description="Create and manage custom reports for your organization"
            breadcrumbItems={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Analytics", href: "/analytics" },
              { label: "Custom Reports", href: "/analytics/reports", isCurrent: true }
            ]}
          />
          
          <div className="mt-6">
            <CustomReportBuilder 
              userId={session?.user?.id} 
              organizationId={session?.user?.organizationId} 
            />
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
