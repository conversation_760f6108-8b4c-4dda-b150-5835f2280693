"use client";

import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/animations/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { InsightsGenerator } from "@/components/analytics/insights-generator";

export default function InsightsPage() {
  const { data: session } = useSession();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <PageHeaderWithBreadcrumb
            title="AI Insights"
            description="AI-powered insights and recommendations based on your data"
            breadcrumbItems={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Analytics", href: "/analytics" },
              { label: "AI Insights", href: "/analytics/insights", isCurrent: true }
            ]}
          />
          
          <div className="mt-6">
            <InsightsGenerator 
              userId={session?.user?.id} 
              organizationId={session?.user?.organizationId} 
            />
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
