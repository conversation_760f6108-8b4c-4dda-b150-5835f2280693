"use client";

import { useSession } from "next-auth/react";
import { PageTransition } from "@/components/animations/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { EnhancedDashboard } from "@/components/analytics/enhanced-dashboard";

export default function EnhancedAnalyticsPage() {
  const { data: session } = useSession();

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <PageHeaderWithBreadcrumb
            title="Enhanced Analytics"
            description="Advanced analytics and custom reporting tools"
            breadcrumbItems={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Analytics", href: "/analytics" },
              { label: "Enhanced Analytics", href: "/analytics/enhanced", isCurrent: true }
            ]}
          />
          
          <div className="mt-6">
            <EnhancedDashboard 
              userId={session?.user?.id} 
              organizationId={session?.user?.organizationId} 
            />
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
