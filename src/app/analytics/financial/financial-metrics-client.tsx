"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  Loader2, 
  AlertTriangle,
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Wallet,
  Leaf,
  Calendar,
  Download,
  RefreshCw
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface FinancialMetricsClientProps {
  userName: string;
}

interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  walletBalance: number;
  carbonAssetValue: number;
  revenueGrowth: number;
  expenseGrowth: number;
  profitGrowth: number;
  revenueByMonth: { month: string; revenue: number }[];
  expensesByMonth: { month: string; expenses: number }[];
  profitByMonth: { month: string; profit: number }[];
  revenueBySource: { source: string; amount: number; percentage: number }[];
  expensesByCategory: { category: string; amount: number; percentage: number }[];
}

export default function FinancialMetricsClient({ userName }: FinancialMetricsClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState("30d");
  const [activeTab, setActiveTab] = useState("overview");
  const [financialMetrics, setFinancialMetrics] = useState<FinancialMetrics | null>(null);

  useEffect(() => {
    fetchFinancialMetrics();
  }, [timeframe]);

  async function fetchFinancialMetrics() {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/analytics/financial/metrics?timeframe=${timeframe}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch financial metrics");
      }
      
      const data = await response.json();
      setFinancialMetrics(data.metrics);
    } catch (error) {
      console.error("Error fetching financial metrics:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  }

  async function refreshData() {
    try {
      setIsRefreshing(true);
      await fetchFinancialMetrics();
    } finally {
      setIsRefreshing(false);
    }
  }

  const getGrowthIndicator = (growth: number) => {
    if (growth > 0) {
      return (
        <div className="flex items-center text-green-600">
          <ArrowUpRight className="mr-1 h-4 w-4" />
          <span>+{growth}%</span>
        </div>
      );
    } else if (growth < 0) {
      return (
        <div className="flex items-center text-red-600">
          <ArrowDownRight className="mr-1 h-4 w-4" />
          <span>{growth}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-muted-foreground">
          <span>0%</span>
        </div>
      );
    }
  };

  // Mock data for charts
  const mockRevenueByMonth = [
    { month: "Jan", revenue: 12500 },
    { month: "Feb", revenue: 14200 },
    { month: "Mar", revenue: 16800 },
    { month: "Apr", revenue: 15300 },
    { month: "May", revenue: 17500 },
    { month: "Jun", revenue: 19200 },
  ];

  const mockExpensesByMonth = [
    { month: "Jan", expenses: 8200 },
    { month: "Feb", expenses: 8500 },
    { month: "Mar", expenses: 9100 },
    { month: "Apr", expenses: 8800 },
    { month: "May", expenses: 9300 },
    { month: "Jun", expenses: 9800 },
  ];

  const mockProfitByMonth = [
    { month: "Jan", profit: 4300 },
    { month: "Feb", profit: 5700 },
    { month: "Mar", profit: 7700 },
    { month: "Apr", profit: 6500 },
    { month: "May", profit: 8200 },
    { month: "Jun", profit: 9400 },
  ];

  const mockRevenueBySource = [
    { source: "Carbon Credit Sales", amount: 45000, percentage: 60 },
    { source: "Transaction Fees", amount: 15000, percentage: 20 },
    { source: "Subscription Fees", amount: 9000, percentage: 12 },
    { source: "Verification Services", amount: 6000, percentage: 8 },
  ];

  const mockExpensesByCategory = [
    { category: "Platform Operations", amount: 25000, percentage: 45 },
    { category: "Marketing", amount: 12000, percentage: 22 },
    { category: "Development", amount: 10000, percentage: 18 },
    { category: "Administrative", amount: 8000, percentage: 15 },
  ];

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Financial Metrics"
              description="Analyze your financial performance and trends"
              breadcrumbItems={[
                { label: "Analytics", href: "/analytics" },
                { label: "Financial", href: "/analytics/financial", isCurrent: true }
              ]}
            />
            
            <div className="mt-4 flex space-x-2 sm:mt-0">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="icon" onClick={refreshData} disabled={isRefreshing}>
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
              <TabsTrigger value="expenses">Expenses</TabsTrigger>
              <TabsTrigger value="profit">Profit</TabsTrigger>
              <TabsTrigger value="assets">Assets</TabsTrigger>
            </TabsList>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-lg">Loading financial metrics...</span>
              </div>
            ) : error ? (
              <AnimatedCard className="border-destructive/50 bg-destructive/10">
                <AnimatedCardContent className="flex items-center py-6">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                  <p className="ml-2 text-destructive">{error}</p>
                </AnimatedCardContent>
              </AnimatedCard>
            ) : (
              <>
                <TabsContent value="overview" className="space-y-4">
                  <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Total Revenue
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${financialMetrics?.totalRevenue.toLocaleString() || mockRevenueByMonth.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <DollarSign className="h-5 w-5 text-muted-foreground" />
                            {financialMetrics ? getGrowthIndicator(financialMetrics.revenueGrowth) : getGrowthIndicator(8.5)}
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Total Expenses
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${financialMetrics?.totalExpenses.toLocaleString() || mockExpensesByMonth.reduce((sum, item) => sum + item.expenses, 0).toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <TrendingUp className="h-5 w-5 text-muted-foreground" />
                            {financialMetrics ? getGrowthIndicator(financialMetrics.expenseGrowth) : getGrowthIndicator(3.2)}
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Net Profit
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${financialMetrics?.netProfit.toLocaleString() || mockProfitByMonth.reduce((sum, item) => sum + item.profit, 0).toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <TrendingUp className="h-5 w-5 text-muted-foreground" />
                            {financialMetrics ? getGrowthIndicator(financialMetrics.profitGrowth) : getGrowthIndicator(12.8)}
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Profit Margin
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            {financialMetrics 
                              ? ((financialMetrics.netProfit / financialMetrics.totalRevenue) * 100).toFixed(1)
                              : ((mockProfitByMonth.reduce((sum, item) => sum + item.profit, 0) / 
                                 mockRevenueByMonth.reduce((sum, item) => sum + item.revenue, 0)) * 100).toFixed(1)}%
                          </div>
                          <PieChart className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </StaggeredList>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Revenue vs Expenses</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Monthly comparison of revenue and expenses
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <BarChart3 className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Revenue vs Expenses chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Profit Trend</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Monthly profit trend analysis
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Profit trend chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </div>
                </TabsContent>
                
                <TabsContent value="revenue" className="space-y-4">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Revenue Breakdown</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Analysis of revenue by source
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <PieChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Revenue breakdown chart will appear here</p>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="mb-4 text-sm font-medium">Revenue Sources</h3>
                          <div className="space-y-4">
                            {(financialMetrics?.revenueBySource || mockRevenueBySource).map((item, index) => (
                              <div key={index} className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium">{item.source}</span>
                                  <span className="text-sm">${item.amount.toLocaleString()}</span>
                                </div>
                                <div className="h-2 w-full rounded-full bg-muted">
                                  <div 
                                    className="h-2 rounded-full bg-primary" 
                                    style={{ width: `${item.percentage}%` }}
                                  />
                                </div>
                                <div className="flex justify-end">
                                  <span className="text-xs text-muted-foreground">{item.percentage}%</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Revenue Trend</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Monthly revenue trend analysis
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Revenue trend chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </TabsContent>
                
                <TabsContent value="expenses" className="space-y-4">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Expense Breakdown</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Analysis of expenses by category
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <PieChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Expense breakdown chart will appear here</p>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="mb-4 text-sm font-medium">Expense Categories</h3>
                          <div className="space-y-4">
                            {(financialMetrics?.expensesByCategory || mockExpensesByCategory).map((item, index) => (
                              <div key={index} className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium">{item.category}</span>
                                  <span className="text-sm">${item.amount.toLocaleString()}</span>
                                </div>
                                <div className="h-2 w-full rounded-full bg-muted">
                                  <div 
                                    className="h-2 rounded-full bg-destructive" 
                                    style={{ width: `${item.percentage}%` }}
                                  />
                                </div>
                                <div className="flex justify-end">
                                  <span className="text-xs text-muted-foreground">{item.percentage}%</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Expense Trend</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Monthly expense trend analysis
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Expense trend chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </TabsContent>
                
                <TabsContent value="profit" className="space-y-4">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Profit Analysis</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Detailed profit analysis over time
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Profit analysis chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Profit Margin</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Profit margin trend over time
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Profit margin chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Profit by Project</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Profit distribution across projects
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <BarChart3 className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Profit by project chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </div>
                </TabsContent>
                
                <TabsContent value="assets" className="space-y-4">
                  <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Wallet Balance
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${financialMetrics?.walletBalance.toLocaleString() || "125,000"}
                          </div>
                          <Wallet className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Carbon Asset Value
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${financialMetrics?.carbonAssetValue.toLocaleString() || "350,000"}
                          </div>
                          <Leaf className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Total Assets
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${(financialMetrics ? 
                              (financialMetrics.walletBalance + financialMetrics.carbonAssetValue) : 
                              475000).toLocaleString()}
                          </div>
                          <DollarSign className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </StaggeredList>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Asset Allocation</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Distribution of assets by type
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <PieChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Asset allocation chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Asset Value Trend</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Historical trend of asset values
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Asset value trend chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </TabsContent>
              </>
            )}
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
