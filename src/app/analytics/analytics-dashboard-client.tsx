"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import {
  ArrowUpRight,
  ArrowDownRight,
  Loader2,
  AlertTriangle,
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  // DollarSign,
  Wallet,
  Leaf,
  Calendar,
  ArrowRight,
  FileText,
  Sparkles
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { RupeeIcon } from "@/components/ui/icons/rupee-icon";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

interface AnalyticsDashboardClientProps {
  userName: string;
}

interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  walletBalance: number;
  carbonAssetValue: number;
  revenueGrowth: number;
  expenseGrowth: number;
  profitGrowth: number;
}

interface TradingMetrics {
  totalOrders: number;
  totalVolume: number;
  averageOrderSize: number;
  successRate: number;
  buyOrders: number;
  sellOrders: number;
  pendingOrders: number;
  completedOrders: number;
}

interface CarbonMetrics {
  totalCredits: number;
  totalRetired: number;
  totalListed: number;
  totalSold: number;
  averagePrice: number;
  priceChange: number;
  vintageDistribution: Record<string, number>;
  standardDistribution: Record<string, number>;
}

export default function AnalyticsDashboardClient({ userName }: AnalyticsDashboardClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState("30d");
  const [financialMetrics, setFinancialMetrics] = useState<FinancialMetrics | null>(null);
  const [tradingMetrics, setTradingMetrics] = useState<TradingMetrics | null>(null);
  const [carbonMetrics, setCarbonMetrics] = useState<CarbonMetrics | null>(null);

  useEffect(() => {
    async function fetchAnalyticsData() {
      try {
        setIsLoading(true);

        // Fetch financial metrics
        const financialResponse = await fetch(`/api/analytics/financial?timeframe=${timeframe}`);

        if (!financialResponse.ok) {
          const errorData = await financialResponse.json();
          throw new Error(errorData.error || "Failed to fetch financial metrics");
        }

        const financialData = await financialResponse.json();
        setFinancialMetrics(financialData.metrics);

        // Fetch trading metrics
        const tradingResponse = await fetch(`/api/analytics/trading?timeframe=${timeframe}`);

        if (!tradingResponse.ok) {
          const errorData = await tradingResponse.json();
          throw new Error(errorData.error || "Failed to fetch trading metrics");
        }

        const tradingData = await tradingResponse.json();
        setTradingMetrics(tradingData.metrics);

        // Fetch carbon metrics
        const carbonResponse = await fetch(`/api/analytics/carbon?timeframe=${timeframe}`);

        if (!carbonResponse.ok) {
          const errorData = await carbonResponse.json();
          throw new Error(errorData.error || "Failed to fetch carbon metrics");
        }

        const carbonData = await carbonResponse.json();
        setCarbonMetrics(carbonData.metrics);
      } catch (error) {
        console.error("Error fetching analytics data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchAnalyticsData();
  }, [timeframe]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(value);
  };

  const getGrowthBadge = (value: number) => {
    if (value > 0) {
      return (
        <Badge className="bg-green-100 text-green-800 flex items-center">
          <TrendingUp className="h-3 w-3 mr-1" />
          +{value.toFixed(1)}%
        </Badge>
      );
    } else if (value < 0) {
      return (
        <Badge className="bg-red-100 text-red-800 flex items-center">
          <TrendingDown className="h-3 w-3 mr-1" />
          {value.toFixed(1)}%
        </Badge>
      );
    } else {
      return (
        <Badge className="bg-gray-100 text-gray-800">
          0%
        </Badge>
      );
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading analytics data...</p>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <AlertTriangle className="h-10 w-10 text-destructive mb-4" />
              <h3 className="text-lg font-medium mb-2">Error Loading Analytics</h3>
              <p className="text-muted-foreground mb-6">{error}</p>
              <AnimatedButton onClick={() => router.push("/dashboard")} animationVariant="buttonTap">
                Return to Dashboard
              </AnimatedButton>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Analytics Dashboard"
              description="View and analyze your carbon trading performance"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Analytics", href: "/analytics", isCurrent: true }
              ]}
            />
            <div>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                  <SelectItem value="90d">Last 90 Days</SelectItem>
                  <SelectItem value="1y">Last Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-6">
            {/* Financial Overview */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Financial Overview</h2>
                <AnimatedButton
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push("/analytics/financial")}
                  animationVariant="buttonTap"
                >
                  View Details
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </div>

              <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Revenue</p>
                        <p className="text-2xl font-bold">{formatCurrency(financialMetrics?.totalRevenue || 0)}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                        <RupeeIcon className="h-6 w-6 text-green-700" />
                      </div>
                    </div>
                    <div className="mt-2">
                      {getGrowthBadge(financialMetrics?.revenueGrowth || 0)}
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Expenses</p>
                        <p className="text-2xl font-bold">{formatCurrency(financialMetrics?.totalExpenses || 0)}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                        <ArrowDownRight className="h-6 w-6 text-red-700" />
                      </div>
                    </div>
                    <div className="mt-2">
                      {getGrowthBadge(-1 * (financialMetrics?.expenseGrowth || 0))}
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Net Profit</p>
                        <p className="text-2xl font-bold">{formatCurrency(financialMetrics?.netProfit || 0)}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <BarChart3 className="h-6 w-6 text-blue-700" />
                      </div>
                    </div>
                    <div className="mt-2">
                      {getGrowthBadge(financialMetrics?.profitGrowth || 0)}
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Carbon Asset Value</p>
                        <p className="text-2xl font-bold">{formatCurrency(financialMetrics?.carbonAssetValue || 0)}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                        <Leaf className="h-6 w-6 text-green-700" />
                      </div>
                    </div>
                    <div className="mt-2">
                      <Badge className="bg-blue-100 text-blue-800">
                        {carbonMetrics?.totalCredits.toLocaleString()} Credits
                      </Badge>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </StaggeredList>
            </div>

            {/* Trading Activity */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Trading Activity</h2>
                <AnimatedButton
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push("/analytics/trading")}
                  animationVariant="buttonTap"
                >
                  View Details
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <AnimatedCard className="md:col-span-2">
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Order Summary</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Overview of your trading activity
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="h-[200px] flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                        <p>Trading volume chart will appear here</p>
                      </div>
                    </div>

                    <Separator className="my-4" />

                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Orders</p>
                        <p className="text-xl font-bold">{tradingMetrics?.totalOrders}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Total Volume</p>
                        <p className="text-xl font-bold">{formatCurrency(tradingMetrics?.totalVolume || 0)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Avg. Order Size</p>
                        <p className="text-xl font-bold">{formatCurrency(tradingMetrics?.averageOrderSize || 0)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Success Rate</p>
                        <p className="text-xl font-bold">{tradingMetrics?.successRate}%</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Order Distribution</AnimatedCardTitle>
                  </AnimatedCardHeader>
                  <AnimatedCardContent>
                    <div className="h-[200px] flex items-center justify-center mb-4">
                      <div className="text-center text-muted-foreground">
                        <PieChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                        <p>Order distribution chart will appear here</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm">Buy Orders</span>
                        </div>
                        <span className="text-sm font-medium">{tradingMetrics?.buyOrders}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                          <span className="text-sm">Sell Orders</span>
                        </div>
                        <span className="text-sm font-medium">{tradingMetrics?.sellOrders}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="h-3 w-3 rounded-full bg-blue-500 mr-2"></div>
                          <span className="text-sm">Completed</span>
                        </div>
                        <span className="text-sm font-medium">{tradingMetrics?.completedOrders}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                          <span className="text-sm">Pending</span>
                        </div>
                        <span className="text-sm font-medium">{tradingMetrics?.pendingOrders}</span>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </div>
            </div>

            {/* Carbon Credits */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Carbon Credits</h2>
                <AnimatedButton
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push("/analytics/carbon")}
                  animationVariant="buttonTap"
                >
                  View Details
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </div>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Credits</p>
                        <p className="text-2xl font-bold">{carbonMetrics?.totalCredits.toLocaleString()}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                        <Leaf className="h-6 w-6 text-green-700" />
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Listed Credits</p>
                        <p className="text-2xl font-bold">{carbonMetrics?.totalListed.toLocaleString()}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <ArrowUpRight className="h-6 w-6 text-blue-700" />
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Sold Credits</p>
                        <p className="text-2xl font-bold">{carbonMetrics?.totalSold.toLocaleString()}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                        <RupeeIcon className="h-6 w-6 text-yellow-700" />
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Retired Credits</p>
                        <p className="text-2xl font-bold">{carbonMetrics?.totalRetired.toLocaleString()}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <Calendar className="h-6 w-6 text-purple-700" />
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Quick Links</h2>

              <div className="grid gap-4 md:grid-cols-3">
                <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/financial")}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <BarChart3 className="h-6 w-6 text-blue-700" />
                      </div>
                      <div>
                        <h3 className="font-medium">Financial Metrics</h3>
                        <p className="text-sm text-muted-foreground">Revenue, expenses, and profit analysis</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/trading")}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                        <LineChart className="h-6 w-6 text-green-700" />
                      </div>
                      <div>
                        <h3 className="font-medium">Trading Activity</h3>
                        <p className="text-sm text-muted-foreground">Order analytics and market trends</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/transaction-audit")}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                        <Wallet className="h-6 w-6 text-yellow-700" />
                      </div>
                      <div>
                        <h3 className="font-medium">Transaction Audit</h3>
                        <p className="text-sm text-muted-foreground">Detailed transaction history and audit</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </div>
            </div>

            {/* Enhanced Analytics */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Enhanced Analytics</h2>
                <Badge variant="outline" className="text-xs">New</Badge>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/enhanced")}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <BarChart3 className="h-6 w-6 text-blue-700" />
                      </div>
                      <div>
                        <h3 className="font-medium">Enhanced Dashboard</h3>
                        <p className="text-sm text-muted-foreground">Interactive dashboard with customizable widgets</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/reports")}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <FileText className="h-6 w-6 text-purple-700" />
                      </div>
                      <div>
                        <h3 className="font-medium">Custom Reports</h3>
                        <p className="text-sm text-muted-foreground">Create and manage custom reports</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>

                <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/insights")}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center">
                        <TrendingUp className="h-6 w-6 text-amber-700" />
                      </div>
                      <div>
                        <h3 className="font-medium">AI Insights</h3>
                        <p className="text-sm text-muted-foreground">AI-powered insights and recommendations</p>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              </div>
            </div>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
