"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON><PERSON>, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  Loader2, 
  AlertTriangle,
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Wallet,
  Leaf,
  Calendar,
  ArrowRight
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface AnalyticsDashboardClientProps {
  userName: string;
}

interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  walletBalance: number;
  carbonAssetValue: number;
  revenueGrowth: number;
  expenseGrowth: number;
  profitGrowth: number;
}

interface TradingMetrics {
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  averageOrderSize: number;
  orderVolume: number;
  orderGrowth: number;
  topSellingCredits: { name: string; volume: number; growth: number }[];
}

interface CarbonMetrics {
  totalEmissionsReduced: number;
  totalCreditsRetired: number;
  emissionsGrowth: number;
  retirementGrowth: number;
  impactByProject: { name: string; impact: number; percentage: number }[];
}

export default function AnalyticsDashboardClient({ userName }: AnalyticsDashboardClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState("30d");
  const [financialMetrics, setFinancialMetrics] = useState<FinancialMetrics | null>(null);
  const [tradingMetrics, setTradingMetrics] = useState<TradingMetrics | null>(null);
  const [carbonMetrics, setCarbonMetrics] = useState<CarbonMetrics | null>(null);

  useEffect(() => {
    async function fetchAnalyticsData() {
      try {
        setIsLoading(true);
        
        // Fetch financial metrics
        const financialResponse = await fetch(`/api/analytics/financial?timeframe=${timeframe}`);
        
        if (!financialResponse.ok) {
          const errorData = await financialResponse.json();
          throw new Error(errorData.error || "Failed to fetch financial metrics");
        }
        
        const financialData = await financialResponse.json();
        setFinancialMetrics(financialData.metrics);
        
        // Fetch trading metrics
        const tradingResponse = await fetch(`/api/analytics/trading?timeframe=${timeframe}`);
        
        if (!tradingResponse.ok) {
          const errorData = await tradingResponse.json();
          throw new Error(errorData.error || "Failed to fetch trading metrics");
        }
        
        const tradingData = await tradingResponse.json();
        setTradingMetrics(tradingData.metrics);
        
        // Fetch carbon metrics
        const carbonResponse = await fetch(`/api/analytics/carbon?timeframe=${timeframe}`);
        
        if (!carbonResponse.ok) {
          const errorData = await carbonResponse.json();
          throw new Error(errorData.error || "Failed to fetch carbon metrics");
        }
        
        const carbonData = await carbonResponse.json();
        setCarbonMetrics(carbonData.metrics);
      } catch (error) {
        console.error("Error fetching analytics data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchAnalyticsData();
  }, [timeframe]);

  const getGrowthIndicator = (growth: number) => {
    if (growth > 0) {
      return (
        <div className="flex items-center text-green-600">
          <ArrowUpRight className="mr-1 h-4 w-4" />
          <span>+{growth}%</span>
        </div>
      );
    } else if (growth < 0) {
      return (
        <div className="flex items-center text-red-600">
          <ArrowDownRight className="mr-1 h-4 w-4" />
          <span>{growth}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-muted-foreground">
          <span>0%</span>
        </div>
      );
    }
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Analytics Dashboard"
              description="Track and analyze your carbon trading performance"
              breadcrumbItems={[
                { label: "Analytics", href: "/analytics" },
                { label: "Dashboard", href: "/analytics/dashboard", isCurrent: true }
              ]}
            />
            
            <div className="mt-4 sm:mt-0">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading analytics data...</span>
            </div>
          ) : error ? (
            <AnimatedCard className="border-destructive/50 bg-destructive/10">
              <AnimatedCardContent className="flex items-center py-6">
                <AlertTriangle className="h-6 w-6 text-destructive" />
                <p className="ml-2 text-destructive">{error}</p>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <div className="space-y-8">
              <div>
                <h2 className="mb-4 text-xl font-semibold">Financial Overview</h2>
                <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Total Revenue
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          ${financialMetrics?.totalRevenue.toLocaleString() || "0"}
                        </div>
                        <div className="flex items-center">
                          <DollarSign className="h-5 w-5 text-muted-foreground" />
                          {financialMetrics && getGrowthIndicator(financialMetrics.revenueGrowth)}
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Net Profit
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          ${financialMetrics?.netProfit.toLocaleString() || "0"}
                        </div>
                        <div className="flex items-center">
                          <TrendingUp className="h-5 w-5 text-muted-foreground" />
                          {financialMetrics && getGrowthIndicator(financialMetrics.profitGrowth)}
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Wallet Balance
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          ${financialMetrics?.walletBalance.toLocaleString() || "0"}
                        </div>
                        <Wallet className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Carbon Asset Value
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          ${financialMetrics?.carbonAssetValue.toLocaleString() || "0"}
                        </div>
                        <Leaf className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </StaggeredList>
              </div>
              
              <div>
                <h2 className="mb-4 text-xl font-semibold">Trading Activity</h2>
                <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Total Orders
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          {tradingMetrics?.totalOrders.toLocaleString() || "0"}
                        </div>
                        <div className="flex items-center">
                          <BarChart3 className="h-5 w-5 text-muted-foreground" />
                          {tradingMetrics && getGrowthIndicator(tradingMetrics.orderGrowth)}
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Completed Orders
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          {tradingMetrics?.completedOrders.toLocaleString() || "0"}
                        </div>
                        <LineChart className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Pending Orders
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          {tradingMetrics?.pendingOrders.toLocaleString() || "0"}
                        </div>
                        <PieChart className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Order Volume
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          ${tradingMetrics?.orderVolume.toLocaleString() || "0"}
                        </div>
                        <TrendingUp className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </StaggeredList>
              </div>
              
              <div>
                <h2 className="mb-4 text-xl font-semibold">Carbon Impact</h2>
                <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Emissions Reduced
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          {carbonMetrics?.totalEmissionsReduced.toLocaleString() || "0"} tCO₂e
                        </div>
                        <div className="flex items-center">
                          <Leaf className="h-5 w-5 text-muted-foreground" />
                          {carbonMetrics && getGrowthIndicator(carbonMetrics.emissionsGrowth)}
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader className="pb-2">
                      <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                        Credits Retired
                      </AnimatedCardTitle>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                          {carbonMetrics?.totalCreditsRetired.toLocaleString() || "0"}
                        </div>
                        <div className="flex items-center">
                          <TrendingDown className="h-5 w-5 text-muted-foreground" />
                          {carbonMetrics && getGrowthIndicator(carbonMetrics.retirementGrowth)}
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </StaggeredList>
              </div>
              
              <div>
                <h2 className="mb-4 text-xl font-semibold">Analytics Modules</h2>
                <div className="grid gap-4 md:grid-cols-3">
                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/financial")}>
                    <AnimatedCardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                          <BarChart3 className="h-6 w-6 text-blue-700" />
                        </div>
                        <div>
                          <h3 className="font-medium">Financial Metrics</h3>
                          <p className="text-sm text-muted-foreground">Revenue, expenses, and profit analysis</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/trading")}>
                    <AnimatedCardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                          <LineChart className="h-6 w-6 text-green-700" />
                        </div>
                        <div>
                          <h3 className="font-medium">Trading Activity</h3>
                          <p className="text-sm text-muted-foreground">Order analytics and market trends</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/transaction-audit")}>
                    <AnimatedCardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                          <Wallet className="h-6 w-6 text-yellow-700" />
                        </div>
                        <div>
                          <h3 className="font-medium">Transaction Audit</h3>
                          <p className="text-sm text-muted-foreground">Detailed transaction history and audit</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </div>
              </div>
            </div>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
