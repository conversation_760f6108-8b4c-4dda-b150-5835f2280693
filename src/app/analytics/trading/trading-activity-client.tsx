"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  Loader2, 
  AlertTriangle,
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Wallet,
  Calendar,
  Download,
  RefreshCw,
  ShoppingCart,
  CheckCircle,
  Clock,
  Filter
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TradingActivityClientProps {
  userName: string;
}

interface TradingMetrics {
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  cancelledOrders: number;
  orderVolume: number;
  averageOrderSize: number;
  orderGrowth: number;
  volumeGrowth: number;
  ordersByType: { type: string; count: number; percentage: number }[];
  ordersByStatus: { status: string; count: number; percentage: number }[];
  volumeByMonth: { month: string; volume: number }[];
  ordersByMonth: { month: string; orders: number }[];
  topSellingCredits: { name: string; volume: number; orders: number; growth: number }[];
}

interface Order {
  id: string;
  type: 'BUY' | 'SELL';
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  creditName: string;
  quantity: number;
  price: number;
  total: number;
  date: string;
}

export default function TradingActivityClient({ userName }: TradingActivityClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState("30d");
  const [activeTab, setActiveTab] = useState("overview");
  const [tradingMetrics, setTradingMetrics] = useState<TradingMetrics | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderType, setOrderType] = useState<string>("all");

  useEffect(() => {
    fetchTradingMetrics();
  }, [timeframe]);

  async function fetchTradingMetrics() {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/analytics/trading/metrics?timeframe=${timeframe}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch trading metrics");
      }
      
      const data = await response.json();
      setTradingMetrics(data.metrics);
      
      // Fetch recent orders
      const ordersResponse = await fetch(`/api/analytics/trading/orders?timeframe=${timeframe}`);
      
      if (!ordersResponse.ok) {
        const errorData = await ordersResponse.json();
        throw new Error(errorData.error || "Failed to fetch orders");
      }
      
      const ordersData = await ordersResponse.json();
      setOrders(ordersData.orders);
    } catch (error) {
      console.error("Error fetching trading metrics:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  }

  async function refreshData() {
    try {
      setIsRefreshing(true);
      await fetchTradingMetrics();
    } finally {
      setIsRefreshing(false);
    }
  }

  const getGrowthIndicator = (growth: number) => {
    if (growth > 0) {
      return (
        <div className="flex items-center text-green-600">
          <ArrowUpRight className="mr-1 h-4 w-4" />
          <span>+{growth}%</span>
        </div>
      );
    } else if (growth < 0) {
      return (
        <div className="flex items-center text-red-600">
          <ArrowDownRight className="mr-1 h-4 w-4" />
          <span>{growth}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-muted-foreground">
          <span>0%</span>
        </div>
      );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge variant="success">Completed</Badge>;
      case 'PENDING':
        return <Badge variant="warning">Pending</Badge>;
      case 'CANCELLED':
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'BUY':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Buy</Badge>;
      case 'SELL':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Sell</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Mock data for charts and tables
  const mockTradingMetrics: TradingMetrics = {
    totalOrders: 156,
    completedOrders: 124,
    pendingOrders: 18,
    cancelledOrders: 14,
    orderVolume: 285000,
    averageOrderSize: 1827,
    orderGrowth: 12.5,
    volumeGrowth: 18.2,
    ordersByType: [
      { type: "BUY", count: 92, percentage: 59 },
      { type: "SELL", count: 64, percentage: 41 }
    ],
    ordersByStatus: [
      { status: "COMPLETED", count: 124, percentage: 79 },
      { status: "PENDING", count: 18, percentage: 12 },
      { status: "CANCELLED", count: 14, percentage: 9 }
    ],
    volumeByMonth: [
      { month: "Jan", volume: 42000 },
      { month: "Feb", volume: 38000 },
      { month: "Mar", volume: 45000 },
      { month: "Apr", volume: 48000 },
      { month: "May", volume: 52000 },
      { month: "Jun", volume: 60000 }
    ],
    ordersByMonth: [
      { month: "Jan", orders: 22 },
      { month: "Feb", orders: 18 },
      { month: "Mar", orders: 24 },
      { month: "Apr", orders: 28 },
      { month: "May", orders: 30 },
      { month: "Jun", orders: 34 }
    ],
    topSellingCredits: [
      { name: "Rainforest Protection - 2023", volume: 85000, orders: 42, growth: 15.2 },
      { name: "Solar Energy - 2023", volume: 65000, orders: 35, growth: 12.8 },
      { name: "Wind Farm - 2022", volume: 48000, orders: 28, growth: 8.5 },
      { name: "Methane Capture - 2023", volume: 42000, orders: 22, growth: 10.2 },
      { name: "Reforestation - 2022", volume: 35000, orders: 18, growth: 5.8 }
    ]
  };

  const mockOrders: Order[] = [
    { id: "ORD-001", type: "BUY", status: "COMPLETED", creditName: "Rainforest Protection - 2023", quantity: 100, price: 25, total: 2500, date: "2023-06-15" },
    { id: "ORD-002", type: "SELL", status: "COMPLETED", creditName: "Solar Energy - 2023", quantity: 75, price: 30, total: 2250, date: "2023-06-12" },
    { id: "ORD-003", type: "BUY", status: "PENDING", creditName: "Wind Farm - 2022", quantity: 50, price: 28, total: 1400, date: "2023-06-10" },
    { id: "ORD-004", type: "SELL", status: "COMPLETED", creditName: "Methane Capture - 2023", quantity: 120, price: 22, total: 2640, date: "2023-06-08" },
    { id: "ORD-005", type: "BUY", status: "CANCELLED", creditName: "Reforestation - 2022", quantity: 80, price: 26, total: 2080, date: "2023-06-05" },
    { id: "ORD-006", type: "BUY", status: "COMPLETED", creditName: "Solar Energy - 2023", quantity: 90, price: 30, total: 2700, date: "2023-06-03" },
    { id: "ORD-007", type: "SELL", status: "COMPLETED", creditName: "Rainforest Protection - 2023", quantity: 110, price: 25, total: 2750, date: "2023-06-01" },
    { id: "ORD-008", type: "BUY", status: "PENDING", creditName: "Wind Farm - 2022", quantity: 60, price: 28, total: 1680, date: "2023-05-28" },
    { id: "ORD-009", type: "SELL", status: "COMPLETED", creditName: "Methane Capture - 2023", quantity: 100, price: 22, total: 2200, date: "2023-05-25" },
    { id: "ORD-010", type: "BUY", status: "COMPLETED", creditName: "Reforestation - 2022", quantity: 70, price: 26, total: 1820, date: "2023-05-22" }
  ];

  const filteredOrders = orderType === "all" 
    ? (orders.length > 0 ? orders : mockOrders) 
    : (orders.length > 0 ? orders : mockOrders).filter(order => order.type === orderType.toUpperCase());

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Trading Activity"
              description="Analyze your trading patterns and performance"
              breadcrumbItems={[
                { label: "Analytics", href: "/analytics" },
                { label: "Trading", href: "/analytics/trading", isCurrent: true }
              ]}
            />
            
            <div className="mt-4 flex space-x-2 sm:mt-0">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="icon" onClick={refreshData} disabled={isRefreshing}>
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="trends">Market Trends</TabsTrigger>
              <TabsTrigger value="credits">Top Credits</TabsTrigger>
            </TabsList>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-lg">Loading trading metrics...</span>
              </div>
            ) : error ? (
              <AnimatedCard className="border-destructive/50 bg-destructive/10">
                <AnimatedCardContent className="flex items-center py-6">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                  <p className="ml-2 text-destructive">{error}</p>
                </AnimatedCardContent>
              </AnimatedCard>
            ) : (
              <>
                <TabsContent value="overview" className="space-y-4">
                  <StaggeredList className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Total Orders
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            {tradingMetrics?.totalOrders.toLocaleString() || mockTradingMetrics.totalOrders.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <ShoppingCart className="h-5 w-5 text-muted-foreground" />
                            {tradingMetrics ? getGrowthIndicator(tradingMetrics.orderGrowth) : getGrowthIndicator(mockTradingMetrics.orderGrowth)}
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Completed Orders
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            {tradingMetrics?.completedOrders.toLocaleString() || mockTradingMetrics.completedOrders.toLocaleString()}
                          </div>
                          <CheckCircle className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Order Volume
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${tradingMetrics?.orderVolume.toLocaleString() || mockTradingMetrics.orderVolume.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <DollarSign className="h-5 w-5 text-muted-foreground" />
                            {tradingMetrics ? getGrowthIndicator(tradingMetrics.volumeGrowth) : getGrowthIndicator(mockTradingMetrics.volumeGrowth)}
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader className="pb-2">
                        <AnimatedCardTitle className="text-sm font-medium text-muted-foreground">
                          Average Order Size
                        </AnimatedCardTitle>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold">
                            ${tradingMetrics?.averageOrderSize.toLocaleString() || mockTradingMetrics.averageOrderSize.toLocaleString()}
                          </div>
                          <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </StaggeredList>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Orders by Type</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Distribution of buy and sell orders
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <PieChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Orders by type chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Orders by Status</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Distribution of order statuses
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <PieChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Orders by status chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </div>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Trading Volume Trend</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Monthly trading volume analysis
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <BarChart3 className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Trading volume trend chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </TabsContent>
                
                <TabsContent value="orders" className="space-y-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <h3 className="text-lg font-medium">Recent Orders</h3>
                    <div className="mt-2 sm:mt-0">
                      <Select value={orderType} onValueChange={setOrderType}>
                        <SelectTrigger className="w-[150px]">
                          <Filter className="mr-2 h-4 w-4" />
                          <SelectValue placeholder="Filter by type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Orders</SelectItem>
                          <SelectItem value="buy">Buy Orders</SelectItem>
                          <SelectItem value="sell">Sell Orders</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <AnimatedCard>
                    <AnimatedCardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Order ID</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Credit</TableHead>
                            <TableHead>Quantity</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Total</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredOrders.map((order) => (
                            <TableRow key={order.id}>
                              <TableCell className="font-medium">{order.id}</TableCell>
                              <TableCell>{getTypeBadge(order.type)}</TableCell>
                              <TableCell>{order.creditName}</TableCell>
                              <TableCell>{order.quantity.toLocaleString()}</TableCell>
                              <TableCell>${order.price.toLocaleString()}</TableCell>
                              <TableCell>${order.total.toLocaleString()}</TableCell>
                              <TableCell>{order.date}</TableCell>
                              <TableCell>{getStatusBadge(order.status)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <div className="flex justify-center">
                    <Button variant="outline">
                      View All Orders
                    </Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="trends" className="space-y-4">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Market Trends</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Analysis of market trends and patterns
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Market trends chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Price Trends</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Historical price trends for carbon credits
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Price trends chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                    
                    <AnimatedCard>
                      <AnimatedCardHeader>
                        <AnimatedCardTitle>Volume Trends</AnimatedCardTitle>
                        <AnimatedCardDescription>
                          Historical volume trends for carbon credits
                        </AnimatedCardDescription>
                      </AnimatedCardHeader>
                      <AnimatedCardContent>
                        <div className="h-[300px] flex items-center justify-center">
                          <div className="text-center text-muted-foreground">
                            <BarChart3 className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                            <p>Volume trends chart will appear here</p>
                          </div>
                        </div>
                      </AnimatedCardContent>
                    </AnimatedCard>
                  </div>
                </TabsContent>
                
                <TabsContent value="credits" className="space-y-4">
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Top Selling Carbon Credits</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Most popular carbon credits by trading volume
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Credit Name</TableHead>
                            <TableHead>Volume</TableHead>
                            <TableHead>Orders</TableHead>
                            <TableHead>Growth</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {(tradingMetrics?.topSellingCredits || mockTradingMetrics.topSellingCredits).map((credit, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">{credit.name}</TableCell>
                              <TableCell>${credit.volume.toLocaleString()}</TableCell>
                              <TableCell>{credit.orders}</TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  {getGrowthIndicator(credit.growth)}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </AnimatedCardContent>
                  </AnimatedCard>
                  
                  <AnimatedCard>
                    <AnimatedCardHeader>
                      <AnimatedCardTitle>Credit Performance Comparison</AnimatedCardTitle>
                      <AnimatedCardDescription>
                        Comparative analysis of top carbon credits
                      </AnimatedCardDescription>
                    </AnimatedCardHeader>
                    <AnimatedCardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <BarChart3 className="h-16 w-16 mx-auto mb-2 text-muted-foreground" />
                          <p>Credit performance comparison chart will appear here</p>
                        </div>
                      </div>
                    </AnimatedCardContent>
                  </AnimatedCard>
                </TabsContent>
              </>
            )}
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
