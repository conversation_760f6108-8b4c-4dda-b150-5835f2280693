"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Loader2, 
  AlertTriangle,
  Calendar,
  Download,
  RefreshCw,
  Filter,
  Search,
  FileText,
  ArrowUpDown,
  Eye,
  Wallet,
  CreditCard,
  BarChart3,
  AlertCircle
} from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Select, 
  SelectContent, 
  SelectI<PERSON>, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";

interface TransactionAuditClientProps {
  userName: string;
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  fee: number;
  status: string;
  date: string;
  wallet: string;
  network: string;
  description: string;
  hash?: string;
}

export default function TransactionAuditClient({ userName }: TransactionAuditClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState("30d");
  const [activeTab, setActiveTab] = useState("all");
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [transactionType, setTransactionType] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");

  useEffect(() => {
    fetchTransactions();
  }, [timeframe, transactionType]);

  async function fetchTransactions() {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/analytics/transactions?timeframe=${timeframe}&type=${transactionType}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch transactions");
      }
      
      const data = await response.json();
      setTransactions(data.transactions);
    } catch (error) {
      console.error("Error fetching transactions:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  }

  async function refreshData() {
    try {
      setIsRefreshing(true);
      await fetchTransactions();
    } finally {
      setIsRefreshing(false);
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
      case 'CONFIRMED':
      case 'SUCCESS':
        return <Badge variant="success">Completed</Badge>;
      case 'PENDING':
      case 'PROCESSING':
        return <Badge variant="warning">Pending</Badge>;
      case 'FAILED':
      case 'REJECTED':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type.toUpperCase()) {
      case 'DEPOSIT':
      case 'RECEIVE':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Deposit</Badge>;
      case 'WITHDRAWAL':
      case 'SEND':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Withdrawal</Badge>;
      case 'SWAP':
      case 'EXCHANGE':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">Swap</Badge>;
      case 'FEE':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">Fee</Badge>;
      case 'PURCHASE':
      case 'BUY':
        return <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">Purchase</Badge>;
      case 'SALE':
      case 'SELL':
        return <Badge variant="outline" className="bg-pink-100 text-pink-800 border-pink-200">Sale</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Mock transactions data
  const mockTransactions: Transaction[] = [
    { id: "TX-001", type: "DEPOSIT", amount: 5000, fee: 2.5, status: "COMPLETED", date: "2023-06-15", wallet: "Main Wallet", network: "Ethereum", description: "Deposit to wallet" },
    { id: "TX-002", type: "WITHDRAWAL", amount: 1200, fee: 1.8, status: "COMPLETED", date: "2023-06-12", wallet: "Main Wallet", network: "Ethereum", description: "Withdrawal to external wallet", hash: "0x1234...5678" },
    { id: "TX-003", type: "PURCHASE", amount: 3500, fee: 5.25, status: "COMPLETED", date: "2023-06-10", wallet: "Trading Wallet", network: "Polygon", description: "Purchase of carbon credits" },
    { id: "TX-004", type: "SALE", amount: 2800, fee: 4.2, status: "COMPLETED", date: "2023-06-08", wallet: "Trading Wallet", network: "Polygon", description: "Sale of carbon credits" },
    { id: "TX-005", type: "FEE", amount: 25, fee: 0, status: "COMPLETED", date: "2023-06-05", wallet: "Main Wallet", network: "Ethereum", description: "Platform usage fee" },
    { id: "TX-006", type: "SWAP", amount: 1500, fee: 3.75, status: "PENDING", date: "2023-06-03", wallet: "Main Wallet", network: "Polygon", description: "Swap ETH to MATIC" },
    { id: "TX-007", type: "DEPOSIT", amount: 2000, fee: 1.0, status: "COMPLETED", date: "2023-06-01", wallet: "Main Wallet", network: "Ethereum", description: "Deposit to wallet" },
    { id: "TX-008", type: "WITHDRAWAL", amount: 800, fee: 1.2, status: "FAILED", date: "2023-05-28", wallet: "Main Wallet", network: "Ethereum", description: "Withdrawal failed - insufficient funds" },
    { id: "TX-009", type: "PURCHASE", amount: 4200, fee: 6.3, status: "COMPLETED", date: "2023-05-25", wallet: "Trading Wallet", network: "Polygon", description: "Purchase of carbon credits" },
    { id: "TX-010", type: "SALE", amount: 3600, fee: 5.4, status: "COMPLETED", date: "2023-05-22", wallet: "Trading Wallet", network: "Polygon", description: "Sale of carbon credits" }
  ];

  // Filter and sort transactions
  const filteredTransactions = (transactions.length > 0 ? transactions : mockTransactions)
    .filter(tx => {
      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          tx.id.toLowerCase().includes(query) ||
          tx.description.toLowerCase().includes(query) ||
          tx.wallet.toLowerCase().includes(query) ||
          tx.network.toLowerCase().includes(query) ||
          (tx.hash && tx.hash.toLowerCase().includes(query))
        );
      }
      return true;
    })
    .filter(tx => {
      // Filter by transaction type
      if (activeTab !== "all") {
        return tx.type.toUpperCase() === activeTab.toUpperCase();
      }
      return true;
    })
    .sort((a, b) => {
      // Sort transactions
      if (sortBy === "date") {
        return sortOrder === "asc" 
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortBy === "amount") {
        return sortOrder === "asc" ? a.amount - b.amount : b.amount - a.amount;
      } else if (sortBy === "fee") {
        return sortOrder === "asc" ? a.fee - b.fee : b.fee - a.fee;
      }
      return 0;
    });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <PageHeaderWithBreadcrumb
              title="Transaction Audit"
              description="Review and audit your transaction history"
              breadcrumbItems={[
                { label: "Analytics", href: "/analytics" },
                { label: "Transaction Audit", href: "/analytics/transaction-audit", isCurrent: true }
              ]}
            />
            
            <div className="mt-4 flex space-x-2 sm:mt-0">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="icon" onClick={refreshData} disabled={isRefreshing}>
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search transactions..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex space-x-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[150px]">
                  <ArrowUpDown className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="amount">Amount</SelectItem>
                  <SelectItem value="fee">Fee</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="icon" onClick={toggleSortOrder}>
                {sortOrder === "asc" ? "↑" : "↓"}
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="flex flex-wrap">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="deposit">Deposits</TabsTrigger>
              <TabsTrigger value="withdrawal">Withdrawals</TabsTrigger>
              <TabsTrigger value="purchase">Purchases</TabsTrigger>
              <TabsTrigger value="sale">Sales</TabsTrigger>
              <TabsTrigger value="swap">Swaps</TabsTrigger>
              <TabsTrigger value="fee">Fees</TabsTrigger>
            </TabsList>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-lg">Loading transactions...</span>
              </div>
            ) : error ? (
              <AnimatedCard className="border-destructive/50 bg-destructive/10">
                <AnimatedCardContent className="flex items-center py-6">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                  <p className="ml-2 text-destructive">{error}</p>
                </AnimatedCardContent>
              </AnimatedCard>
            ) : (
              <TabsContent value={activeTab} className="space-y-4">
                {filteredTransactions.length === 0 ? (
                  <AnimatedCard>
                    <AnimatedCardContent className="flex flex-col items-center justify-center py-12">
                      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No transactions found</h3>
                      <p className="text-muted-foreground text-center max-w-md">
                        No transactions match your current filters. Try changing your search criteria or timeframe.
                      </p>
                    </AnimatedCardContent>
                  </AnimatedCard>
                ) : (
                  <AnimatedCard>
                    <AnimatedCardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Transaction ID</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Fee</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Wallet</TableHead>
                            <TableHead>Network</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredTransactions.map((tx) => (
                            <TableRow key={tx.id}>
                              <TableCell className="font-medium">{tx.id}</TableCell>
                              <TableCell>{getTypeBadge(tx.type)}</TableCell>
                              <TableCell>${tx.amount.toLocaleString()}</TableCell>
                              <TableCell>${tx.fee.toLocaleString()}</TableCell>
                              <TableCell>{tx.date}</TableCell>
                              <TableCell>{tx.wallet}</TableCell>
                              <TableCell>{tx.network}</TableCell>
                              <TableCell>{getStatusBadge(tx.status)}</TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="icon" onClick={() => router.push(`/analytics/transaction-audit/details/${tx.id}`)}>
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </AnimatedCardContent>
                  </AnimatedCard>
                )}
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredTransactions.length} transactions
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" disabled>Previous</Button>
                    <Button variant="outline" size="sm" disabled>Next</Button>
                  </div>
                </div>
              </TabsContent>
            )}
          </Tabs>
          
          <div className="mt-8 grid gap-4 md:grid-cols-3">
            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/transaction-audit/flagged")}>
              <AnimatedCardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                    <AlertCircle className="h-6 w-6 text-red-700" />
                  </div>
                  <div>
                    <h3 className="font-medium">Flagged Transactions</h3>
                    <p className="text-sm text-muted-foreground">Review potentially suspicious transactions</p>
                  </div>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
            
            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/analytics/transaction-audit/reports")}>
              <AnimatedCardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <BarChart3 className="h-6 w-6 text-blue-700" />
                  </div>
                  <div>
                    <h3 className="font-medium">Audit Reports</h3>
                    <p className="text-sm text-muted-foreground">Generate and view transaction audit reports</p>
                  </div>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
            
            <AnimatedCard className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => router.push("/wallet/transactions")}>
              <AnimatedCardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                    <Wallet className="h-6 w-6 text-green-700" />
                  </div>
                  <div>
                    <h3 className="font-medium">Wallet Transactions</h3>
                    <p className="text-sm text-muted-foreground">View detailed wallet transaction history</p>
                  </div>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
