#!/usr/bin/env node

// Manually load environment variables from .env file
const fs = require('fs');
const path = require('path');

try {
  const envFile = fs.readFileSync(path.join(__dirname, '.env'), 'utf8');
  const envVars = envFile.split('\n').filter(line => line.includes('='));
  envVars.forEach(line => {
    const [key, ...valueParts] = line.split('=');
    const value = valueParts.join('=').replace(/^"/, '').replace(/"$/, '');
    process.env[key] = value;
  });
} catch (error) {
  console.log('⚠️  Could not load .env file:', error.message);
}

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function setupInitialData() {
  const prisma = new PrismaClient();
  
  console.log('🚀 Setting up initial data...');
  
  try {
    // Check if we already have users
    const existingUsers = await prisma.user.count();
    
    if (existingUsers > 0) {
      console.log(`✅ Database already has ${existingUsers} users. Setup complete.`);
      return;
    }
    
    // Create a test organization
    console.log('📋 Creating test organization...');
    const organization = await prisma.organization.create({
      data: {
        name: 'Test Carbon Trading Company',
        description: 'A test organization for carbon credit trading',
        website: 'https://example.com',
        status: 'ACTIVE',
        verificationStatus: 'VERIFIED',
        country: 'United States',
        industry: 'Environmental Services',
        size: 'MEDIUM',
        foundedYear: 2020,
      },
    });
    
    console.log(`✅ Created organization: ${organization.name} (${organization.id})`);
    
    // Create a test admin user
    console.log('👤 Creating test admin user...');
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const adminUser = await prisma.user.create({
      data: {
        name: 'Test Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ORGANIZATION_ADMIN',
        emailVerified: new Date(),
        organizationId: organization.id,
      },
    });
    
    console.log(`✅ Created admin user: ${adminUser.email} (${adminUser.id})`);
    
    // Create a regular test user
    console.log('👤 Creating test user...');
    const regularUser = await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'USER',
        emailVerified: new Date(),
        organizationId: organization.id,
      },
    });
    
    console.log(`✅ Created regular user: ${regularUser.email} (${regularUser.id})`);
    
    console.log('\n🎉 Initial data setup completed successfully!');
    console.log('\n📝 Login credentials:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   User:  <EMAIL> / password123');
    
  } catch (error) {
    console.error('❌ Error setting up initial data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the setup
setupInitialData().catch(console.error);
