FROM node:22-alpine AS base

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package.json
COPY package.json ./

# Copy pnpm lock file if it exists
COPY pnpm-lock.yaml* ./

# Copy scripts directory for development
COPY scripts/ ./scripts/
RUN chmod +x ./scripts/*.sh

# Install dependencies
RUN pnpm install --frozen-lockfile

# Install PostgreSQL client for database migrations
RUN apk add --no-cache postgresql-client

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED 1

# Generate Prisma client
RUN pnpm prisma generate

# Build the application
RUN pnpm build

# Create uploads directory
RUN mkdir -p public/uploads

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Install PostgreSQL client for database migrations in production
RUN apk add --no-cache postgresql-client

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Copy Prisma schema and migration files
COPY --from=builder /app/prisma ./prisma

# Copy scripts directory
COPY --from=builder /app/scripts ./scripts
RUN chmod +x scripts/*.sh scripts/*.js

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

# Use a startup script to run migrations before starting the app
CMD ["/app/scripts/start.sh"]
