const express = require('express');
const router = express.Router();
const carbonCreditController = require('../../controller/carbonCredit');
const { verifyAuthToken } = require('../../middleware/auth');

/**
 * @swagger
 * components:
 *   schemas:
 *     CarbonCredit:
 *       type: object
 *       required:
 *         - name
 *         - quantity
 *         - price
 *         - vintage
 *         - standard
 *         - methodology
 *         - projectId
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the carbon credit
 *         name:
 *           type: string
 *           description: Name of the carbon credit
 *         description:
 *           type: string
 *           description: Description of the carbon credit
 *         quantity:
 *           type: integer
 *           minimum: 1
 *           description: Total quantity of carbon credits
 *         availableQuantity:
 *           type: integer
 *           minimum: 0
 *           description: Available quantity for purchase
 *         price:
 *           type: number
 *           minimum: 0
 *           description: Price per carbon credit
 *         vintage:
 *           type: integer
 *           minimum: 1990
 *           description: Vintage year of the carbon credit
 *         standard:
 *           type: string
 *           enum: [VCS, CDM, GOLD_STANDARD, CAR, ACR, OTHER]
 *           description: Carbon credit standard
 *         methodology:
 *           type: string
 *           description: Methodology used for carbon credit generation
 *         status:
 *           type: string
 *           enum: [PENDING, ACTIVE, SOLD, RETIRED, CANCELLED]
 *           description: Current status of the carbon credit
 *         verificationStatus:
 *           type: string
 *           enum: [PENDING, IN_REVIEW, VERIFIED, REJECTED]
 *           description: Verification status
 *         projectId:
 *           type: string
 *           description: ID of the associated project
 *         organizationId:
 *           type: string
 *           description: ID of the organization that owns the credit
 *         userId:
 *           type: string
 *           description: ID of the user who created the credit
 */

/**
 * @swagger
 * /api/v1/carbon-credits:
 *   get:
 *     summary: Get carbon credits with filtering and pagination
 *     tags: [Carbon Credits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, ACTIVE, SOLD, RETIRED, CANCELLED]
 *         description: Filter by status
 *       - in: query
 *         name: vintage
 *         schema:
 *           type: integer
 *         description: Filter by vintage year
 *       - in: query
 *         name: standard
 *         schema:
 *           type: string
 *           enum: [VCS, CDM, GOLD_STANDARD, CAR, ACR, OTHER]
 *         description: Filter by standard
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *     responses:
 *       200:
 *         description: List of carbon credits
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     carbonCredits:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/CarbonCredit'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 */
router.get('/', verifyToken, carbonCreditController.getCarbonCredits);

/**
 * @swagger
 * /api/v1/carbon-credits:
 *   post:
 *     summary: Create a new carbon credit
 *     tags: [Carbon Credits]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - quantity
 *               - price
 *               - vintage
 *               - standard
 *               - methodology
 *               - projectId
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *               description:
 *                 type: string
 *                 maxLength: 1000
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *               price:
 *                 type: number
 *                 minimum: 0
 *               vintage:
 *                 type: integer
 *                 minimum: 1990
 *               standard:
 *                 type: string
 *                 enum: [VCS, CDM, GOLD_STANDARD, CAR, ACR, OTHER]
 *               methodology:
 *                 type: string
 *               projectId:
 *                 type: string
 *               minPurchaseQuantity:
 *                 type: integer
 *                 minimum: 1
 *               maxPurchaseQuantity:
 *                 type: integer
 *                 minimum: 1
 *     responses:
 *       201:
 *         description: Carbon credit created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Organization required
 */
router.post('/', verifyToken, requireOrganization, carbonCreditController.createCarbonCredit);

/**
 * @swagger
 * /api/v1/carbon-credits/{id}/purchase:
 *   post:
 *     summary: Purchase carbon credits
 *     tags: [Carbon Credits]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Carbon credit ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *               paymentMethod:
 *                 type: string
 *                 enum: [CREDIT_CARD, BANK_TRANSFER, CRYPTO, WALLET, OTHER]
 *                 default: CREDIT_CARD
 *     responses:
 *       200:
 *         description: Purchase successful
 *       400:
 *         description: Validation error or insufficient quantity
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Carbon credit not found
 */
router.post('/:id/purchase', verifyToken, carbonCreditController.purchaseCarbonCredit);

/**
 * @swagger
 * /api/v1/carbon-credits/retire:
 *   post:
 *     summary: Retire multiple carbon credits
 *     tags: [Carbon Credits]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - carbonCreditIds
 *               - retirementReason
 *             properties:
 *               carbonCreditIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 minItems: 1
 *               retirementReason:
 *                 type: string
 *               retirementBeneficiary:
 *                 type: string
 *     responses:
 *       200:
 *         description: Carbon credits retired successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/retire', verifyToken, carbonCreditController.retireCarbonCredits);

module.exports = router;
