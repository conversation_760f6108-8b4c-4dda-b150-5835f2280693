#!/usr/bin/env node

require('dotenv').config();
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

const logger = require('./src/utils/logger');

/**
 * Run Sequelize CLI commands with proper error handling
 */
async function runSequelizeCommand(command, description) {
  console.log(`\n🔄 ${description}...`);
  
  try {
    const { stdout, stderr } = await execAsync(`npx sequelize-cli ${command}`);
    
    if (stdout) {
      console.log(stdout);
    }
    
    if (stderr && !stderr.includes('Loaded configuration file')) {
      console.warn('⚠️  Warning:', stderr);
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
    
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    if (error.stdout) console.log('STDOUT:', error.stdout);
    if (error.stderr) console.log('STDERR:', error.stderr);
    return false;
  }
}

/**
 * Check migration status
 */
async function checkMigrationStatus() {
  console.log('\n📊 Checking migration status...');
  
  try {
    const { stdout } = await execAsync('npx sequelize-cli db:migrate:status');
    console.log(stdout);
    return true;
  } catch (error) {
    console.log('ℹ️  No migrations table found or no migrations run yet');
    return false;
  }
}

/**
 * Run all pending migrations
 */
async function runMigrations() {
  const success = await runSequelizeCommand(
    'db:migrate',
    'Running database migrations'
  );
  
  if (success) {
    await checkMigrationStatus();
  }
  
  return success;
}

/**
 * Rollback last migration
 */
async function rollbackMigration() {
  const success = await runSequelizeCommand(
    'db:migrate:undo',
    'Rolling back last migration'
  );
  
  if (success) {
    await checkMigrationStatus();
  }
  
  return success;
}

/**
 * Rollback all migrations
 */
async function rollbackAllMigrations() {
  const success = await runSequelizeCommand(
    'db:migrate:undo:all',
    'Rolling back all migrations'
  );
  
  if (success) {
    await checkMigrationStatus();
  }
  
  return success;
}

/**
 * Create a new migration
 */
async function createMigration(name) {
  if (!name) {
    console.error('❌ Migration name is required');
    return false;
  }
  
  return await runSequelizeCommand(
    `migration:generate --name ${name}`,
    `Creating new migration: ${name}`
  );
}

/**
 * Run seeders
 */
async function runSeeders() {
  return await runSequelizeCommand(
    'db:seed:all',
    'Running database seeders'
  );
}

/**
 * Undo all seeders
 */
async function undoSeeders() {
  return await runSequelizeCommand(
    'db:seed:undo:all',
    'Undoing all database seeders'
  );
}

/**
 * Fresh database setup (drop, create, migrate, seed)
 */
async function freshSetup() {
  console.log('\n🆕 Setting up fresh database...');
  
  // Note: Be careful with this in production!
  const steps = [
    () => runSequelizeCommand('db:drop', 'Dropping database'),
    () => runSequelizeCommand('db:create', 'Creating database'),
    () => runMigrations(),
    () => runSeeders()
  ];
  
  for (const step of steps) {
    const success = await step();
    if (!success) {
      console.error('❌ Fresh setup failed');
      return false;
    }
  }
  
  console.log('✅ Fresh database setup completed');
  return true;
}

/**
 * Display help information
 */
function showHelp() {
  console.log(`
🗄️  Database Migration Manager

Usage: node migrate.js <command>

Commands:
  status              Check migration status
  migrate             Run all pending migrations
  rollback            Rollback last migration
  rollback:all        Rollback all migrations
  create <name>       Create a new migration file
  seed                Run all seeders
  seed:undo           Undo all seeders
  fresh               Fresh setup (drop, create, migrate, seed)
  help                Show this help message

Examples:
  node migrate.js status
  node migrate.js migrate
  node migrate.js create add-user-preferences
  node migrate.js rollback
  node migrate.js fresh

Environment Variables:
  NODE_ENV            Environment (development, test, production)
  DB_HOST             Database host
  DB_PORT             Database port
  DB_NAME             Database name
  DB_USER             Database username
  DB_PASSWORD         Database password
`);
}

/**
 * Main function to handle command line arguments
 */
async function main() {
  const command = process.argv[2];
  const arg = process.argv[3];
  
  console.log('🗄️  CarbonX Database Migration Manager');
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Database: ${process.env.DB_NAME || 'carbonix'}`);
  
  switch (command) {
    case 'status':
      await checkMigrationStatus();
      break;
      
    case 'migrate':
      await runMigrations();
      break;
      
    case 'rollback':
      await rollbackMigration();
      break;
      
    case 'rollback:all':
      await rollbackAllMigrations();
      break;
      
    case 'create':
      await createMigration(arg);
      break;
      
    case 'seed':
      await runSeeders();
      break;
      
    case 'seed:undo':
      await undoSeeders();
      break;
      
    case 'fresh':
      await freshSetup();
      break;
      
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
      
    default:
      console.error(`❌ Unknown command: ${command}`);
      showHelp();
      process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runMigrations,
  rollbackMigration,
  rollbackAllMigrations,
  createMigration,
  runSeeders,
  undoSeeders,
  freshSetup,
  checkMigrationStatus
};
