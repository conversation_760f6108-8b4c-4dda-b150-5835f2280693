#!/usr/bin/env node

require('dotenv').config();
const { User, Organization, AuditLog } = require('./src/models');
const { withTransaction, withRetryTransaction } = require('./src/utils/transaction');
const logger = require('./src/utils/logger');

/**
 * Test transaction rollback behavior
 */
async function testTransactionRollback() {
  console.log('\n🧪 Testing Transaction Rollback...');
  
  try {
    await withTransaction(async (transaction) => {
      // Create a test organization
      const org = await Organization.create({
        name: 'Test Rollback Org',
        status: 'PENDING',
        verificationStatus: 'PENDING'
      }, { transaction });
      
      console.log(`✅ Created organization: ${org.id}`);
      
      // Create a user
      const user = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        organizationId: org.id
      }, { transaction });
      
      console.log(`✅ Created user: ${user.id}`);
      
      // Intentionally cause an error to trigger rollback
      throw new Error('Intentional error to test rollback');
    });
  } catch (error) {
    console.log(`❌ Transaction failed as expected: ${error.message}`);
    
    // Verify that nothing was created (rollback worked)
    const org = await Organization.findOne({ where: { name: 'Test Rollback Org' } });
    const user = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!org && !user) {
      console.log('✅ Rollback successful - no data was persisted');
    } else {
      console.log('❌ Rollback failed - data was persisted');
    }
  }
}

/**
 * Test transaction commit behavior
 */
async function testTransactionCommit() {
  console.log('\n🧪 Testing Transaction Commit...');
  
  try {
    const result = await withTransaction(async (transaction) => {
      // Create a test organization
      const org = await Organization.create({
        name: 'Test Commit Org',
        status: 'ACTIVE',
        verificationStatus: 'VERIFIED'
      }, { transaction });
      
      console.log(`✅ Created organization: ${org.id}`);
      
      // Create a user
      const user = await User.create({
        name: 'Commit Test User',
        email: '<EMAIL>',
        password: 'password123',
        organizationId: org.id
      }, { transaction });
      
      console.log(`✅ Created user: ${user.id}`);
      
      // Create audit log
      await AuditLog.create({
        type: 'USER_CREATED',
        description: 'Test user created during transaction test',
        userId: user.id,
        organizationId: org.id
      }, { transaction });
      
      console.log(`✅ Created audit log`);
      
      return { org, user };
    });
    
    console.log('✅ Transaction committed successfully');
    
    // Verify that data was persisted (commit worked)
    const org = await Organization.findByPk(result.org.id);
    const user = await User.findByPk(result.user.id);
    const auditLog = await AuditLog.findOne({ where: { userId: result.user.id } });
    
    if (org && user && auditLog) {
      console.log('✅ Commit successful - all data was persisted');
      
      // Clean up test data
      await auditLog.destroy();
      await user.destroy();
      await org.destroy();
      console.log('🧹 Test data cleaned up');
    } else {
      console.log('❌ Commit failed - data was not persisted');
    }
    
  } catch (error) {
    console.log(`❌ Unexpected error: ${error.message}`);
  }
}

/**
 * Test retry transaction with deadlock simulation
 */
async function testRetryTransaction() {
  console.log('\n🧪 Testing Retry Transaction...');
  
  let attemptCount = 0;
  
  try {
    const result = await withRetryTransaction(async (transaction) => {
      attemptCount++;
      console.log(`🔄 Attempt ${attemptCount}`);
      
      // Simulate failure on first two attempts
      if (attemptCount < 3) {
        const error = new Error('Simulated deadlock');
        error.name = 'SequelizeDeadlockError';
        throw error;
      }
      
      // Success on third attempt
      const org = await Organization.create({
        name: 'Retry Test Org',
        status: 'ACTIVE'
      }, { transaction });
      
      return org;
    }, {
      maxRetries: 3,
      baseDelay: 50
    });
    
    console.log(`✅ Retry transaction succeeded after ${attemptCount} attempts`);
    
    // Clean up
    await result.destroy();
    console.log('🧹 Test data cleaned up');
    
  } catch (error) {
    console.log(`❌ Retry transaction failed: ${error.message}`);
  }
}

/**
 * Test concurrent transactions
 */
async function testConcurrentTransactions() {
  console.log('\n🧪 Testing Concurrent Transactions...');
  
  // Create a test organization first
  const org = await Organization.create({
    name: 'Concurrent Test Org',
    status: 'ACTIVE'
  });
  
  const promises = [];
  
  // Create 5 concurrent transactions
  for (let i = 1; i <= 5; i++) {
    const promise = withTransaction(async (transaction) => {
      const user = await User.create({
        name: `Concurrent User ${i}`,
        email: `concurrent-${i}@example.com`,
        password: 'password123',
        organizationId: org.id
      }, { transaction });
      
      // Simulate some processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
      
      return user;
    });
    
    promises.push(promise);
  }
  
  try {
    const results = await Promise.all(promises);
    console.log(`✅ All ${results.length} concurrent transactions completed successfully`);
    
    // Clean up
    for (const user of results) {
      await user.destroy();
    }
    await org.destroy();
    console.log('🧹 Test data cleaned up');
    
  } catch (error) {
    console.log(`❌ Concurrent transaction test failed: ${error.message}`);
  }
}

/**
 * Run all transaction tests
 */
async function runTransactionTests() {
  console.log('🚀 Starting Transaction Tests...');
  
  try {
    await testTransactionRollback();
    await testTransactionCommit();
    await testRetryTransaction();
    await testConcurrentTransactions();
    
    console.log('\n🎉 All transaction tests completed!');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTransactionTests();
}

module.exports = {
  testTransactionRollback,
  testTransactionCommit,
  testRetryTransaction,
  testConcurrentTransactions
};
