{"name": "carbonx-backend", "version": "1.0.0", "description": "Carbon Credit Trading Platform Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:transactions": "node test-transactions.js", "sync": "node sync-database.js", "sync:safe": "node sync-database.js safe", "sync:force": "node sync-database.js force", "sync:alter": "node sync-database.js alter", "sync:dev": "node sync-database.js dev", "migrate": "node migrate.js migrate", "migrate:status": "node migrate.js status", "migrate:rollback": "node migrate.js rollback", "migrate:rollback:all": "node migrate.js rollback:all", "migrate:create": "node migrate.js create", "migrate:fresh": "node migrate.js fresh", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:seed": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo:all", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop"}, "keywords": ["carbon-credits", "trading", "blockchain", "sustainability", "api"], "author": "CarbonX Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.4", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.3", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}