{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_size\" AS ENUM(''SMALL'', ''MEDIUM'', ''LARGE'', ''ENTERPRISE''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_User_role\" AS ENUM(''ADMIN'', ''ORGANIZATION_ADMIN'', ''USER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_type\" AS ENUM(''RENEWABLE_ENERGY'', ''FORESTRY'', ''METHANE_REDUCTION'', ''ENERGY_EFFICIENCY'', ''WASTE_MANAGEMENT'', ''AGRICULTURE'', ''TRANSPORTATION'', ''INDUSTRIAL'', ''OTHER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''COMPLETED'', ''SUSPENDED'', ''CANCELLED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_CarbonCredit_status\" AS ENUM(''PENDING'', ''VERIFIED'', ''LISTED'', ''SOLD'', ''RETIRED'', ''TOKENIZED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_CarbonCredit_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Order_type\" AS ENUM(''BUY'', ''SELL''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Order_status\" AS ENUM(''PENDING'', ''MATCHED'', ''COMPLETED'', ''CANCELLED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Wallet_walletType\" AS ENUM(''GENERAL'', ''PROJECT'', ''TOKENIZATION'', ''TRADING'', ''RETIREMENT'', ''CUSTODY''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE UNIQUE INDEX \"wallet_address_network_chain_id\" ON \"Wallet\" (\"address\", \"network\", \"chainId\")\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Transaction_type\" AS ENUM(''DEPOSIT'', ''WITHDRAWAL'', ''PURCHASE'', ''SALE'', ''FEE'', ''BRIDGE'', ''SWAP'', ''TRANSFER'', ''RETIREMENT'', ''TOKENIZATION''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Transaction_status\" AS ENUM(''PENDING'', ''COMPLETED'', ''FAILED'', ''CANCELLED'', ''REVERTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Notification_type\" AS ENUM(''SYSTEM'', ''ORDER'', ''TRANSACTION'', ''CREDIT'', ''VERIFICATION'', ''SUBSCRIPTION'', ''PAYMENT'', ''BILLING'', ''TEAM'', ''SECURITY'', ''MARKETPLACE'', ''WALLET''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Notification_priority\" AS ENUM(''LOW'', ''NORMAL'', ''HIGH'', ''URGENT''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_AuditLog_type\" AS ENUM(''LOGIN_SUCCESS'', ''LOGIN_FAILED'', ''LOGOUT'', ''USER_CREATED'', ''USER_UPDATED'', ''USER_DELETED'', ''ORGANIZATION_CREATED'', ''ORGANIZATION_UPDATED'', ''PROJECT_CREATED'', ''PROJECT_UPDATED'', ''CREDIT_CREATED'', ''CREDIT_UPDATED'', ''ORDER_CREATED'', ''ORDER_UPDATED'', ''TRANSACTION_CREATED'', ''WALLET_CREATED'', ''WALLET_UPDATED'', ''PERMISSION_GRANTED'', ''PERMISSION_REVOKED'', ''SYSTEM_CONFIG_CHANGED'', ''DATA_EXPORT'', ''DATA_IMPORT'', ''SECURITY_EVENT'', ''OTHER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_type\" ON \"AuditLog\" (\"type\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_user_id\" ON \"AuditLog\" (\"userId\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_organization_id\" ON \"AuditLog\" (\"organizationId\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_created_at\" ON \"AuditLog\" (\"createdAt\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:23:34:2334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:53:34 +0000] \"GET /health HTTP/1.1\" 200 150 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:23:34:2334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:53:41 +0000] \"GET /api HTTP/1.1\" 200 420 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:23:41:2341"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"email\", \"name\", \"password\", \"role\", \"emailVerified\", \"jobTitle\", \"departmentName\", \"phoneNumber\", \"profileImage\", \"bio\", \"lastLoginAt\", \"twoFactorEnabled\", \"preferences\", \"organizationId\", \"departmentId\", \"divisionId\", \"createdAt\", \"updatedAt\" FROM \"User\" AS \"User\" WHERE \"User\".\"email\" = '<EMAIL>';\u001b[39m","timestamp":"2025-06-25 12:23:52:2352"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mClient error: User with this email already exists\u001b[39m","method":"POST","statusCode":409,"timestamp":"2025-06-25 12:23:52:2352","url":"/api/auth/register"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:53:52 +0000] \"POST /api/auth/register HTTP/1.1\" 409 289 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:23:52:2352"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"email\", \"User\".\"name\", \"User\".\"password\", \"User\".\"role\", \"User\".\"emailVerified\", \"User\".\"jobTitle\", \"User\".\"departmentName\", \"User\".\"phoneNumber\", \"User\".\"profileImage\", \"User\".\"bio\", \"User\".\"lastLoginAt\", \"User\".\"twoFactorEnabled\", \"User\".\"preferences\", \"User\".\"organizationId\", \"User\".\"departmentId\", \"User\".\"divisionId\", \"User\".\"createdAt\", \"User\".\"updatedAt\", \"organization\".\"id\" AS \"organization.id\", \"organization\".\"name\" AS \"organization.name\", \"organization\".\"status\" AS \"organization.status\", \"organization\".\"verificationStatus\" AS \"organization.verificationStatus\" FROM \"User\" AS \"User\" LEFT OUTER JOIN \"Organization\" AS \"organization\" ON \"User\".\"organizationId\" = \"organization\".\"id\" WHERE \"User\".\"email\" = '<EMAIL>';\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"User\" SET \"lastLoginAt\"=$1,\"updatedAt\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase error: column \"details\" of relation \"AuditLog\" does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_target.c","length":128,"line":"1066","name":"error","parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"position":"51","routine":"checkInsertTargets","severity":"ERROR","sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";"},"parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"parent":{"code":"42703","file":"parse_target.c","length":128,"line":"1066","name":"error","parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"position":"51","routine":"checkInsertTargets","severity":"ERROR","sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";"},"sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";","stack":"Error\n    at Query.run (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/query.js:50:25)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.insert (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)\n    at async model.save (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:2490:35)\n    at async AuditLog.create (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:1362:12)\n    at async AuditLog.logEvent (/Users/<USER>/Documents/carbonx/carbonx/backend/src/models/AuditLog.js:92:12)\n    at async /Users/<USER>/Documents/carbonx/carbonx/backend/src/routes/auth.js:175:3","timestamp":"2025-06-25 12:24:01:241"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mServer error: Database operation failed\u001b[39m","method":"POST","stack":"ApiError: Database operation failed\n    at handleSequelizeError (/Users/<USER>/Documents/carbonx/carbonx/backend/src/middleware/errorHandler.js:93:12)\n    at errorHandler (/Users/<USER>/Documents/carbonx/carbonx/backend/src/middleware/errorHandler.js:120:11)\n    at Layer.handle_error (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-06-25 12:24:01:241","url":"/api/auth/login","userAgent":"curl/8.7.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:54:01 +0000] \"POST /api/auth/login HTTP/1.1\" 500 1255 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-25 12:37:19:3719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:55:3755"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:55:3755"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:37:55:3755"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-25 12:37:59:3759"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-25 13:08:13:813"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:13:19:1319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:07:43:19 +0000] \"GET /health HTTP/1.1\" 200 149 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 13:13:19:1319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
