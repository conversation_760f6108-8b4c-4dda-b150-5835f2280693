{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_size\" AS ENUM(''SMALL'', ''MEDIUM'', ''LARGE'', ''ENTERPRISE''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_User_role\" AS ENUM(''ADMIN'', ''ORGANIZATION_ADMIN'', ''USER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_type\" AS ENUM(''RENEWABLE_ENERGY'', ''FORESTRY'', ''METHANE_REDUCTION'', ''ENERGY_EFFICIENCY'', ''WASTE_MANAGEMENT'', ''AGRICULTURE'', ''TRANSPORTATION'', ''INDUSTRIAL'', ''OTHER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''COMPLETED'', ''SUSPENDED'', ''CANCELLED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_CarbonCredit_status\" AS ENUM(''PENDING'', ''VERIFIED'', ''LISTED'', ''SOLD'', ''RETIRED'', ''TOKENIZED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_CarbonCredit_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Order_type\" AS ENUM(''BUY'', ''SELL''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Order_status\" AS ENUM(''PENDING'', ''MATCHED'', ''COMPLETED'', ''CANCELLED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Wallet_walletType\" AS ENUM(''GENERAL'', ''PROJECT'', ''TOKENIZATION'', ''TRADING'', ''RETIREMENT'', ''CUSTODY''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE UNIQUE INDEX \"wallet_address_network_chain_id\" ON \"Wallet\" (\"address\", \"network\", \"chainId\")\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Transaction_type\" AS ENUM(''DEPOSIT'', ''WITHDRAWAL'', ''PURCHASE'', ''SALE'', ''FEE'', ''BRIDGE'', ''SWAP'', ''TRANSFER'', ''RETIREMENT'', ''TOKENIZATION''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Transaction_status\" AS ENUM(''PENDING'', ''COMPLETED'', ''FAILED'', ''CANCELLED'', ''REVERTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Notification_type\" AS ENUM(''SYSTEM'', ''ORDER'', ''TRANSACTION'', ''CREDIT'', ''VERIFICATION'', ''SUBSCRIPTION'', ''PAYMENT'', ''BILLING'', ''TEAM'', ''SECURITY'', ''MARKETPLACE'', ''WALLET''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Notification_priority\" AS ENUM(''LOW'', ''NORMAL'', ''HIGH'', ''URGENT''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_AuditLog_type\" AS ENUM(''LOGIN_SUCCESS'', ''LOGIN_FAILED'', ''LOGOUT'', ''USER_CREATED'', ''USER_UPDATED'', ''USER_DELETED'', ''ORGANIZATION_CREATED'', ''ORGANIZATION_UPDATED'', ''PROJECT_CREATED'', ''PROJECT_UPDATED'', ''CREDIT_CREATED'', ''CREDIT_UPDATED'', ''ORDER_CREATED'', ''ORDER_UPDATED'', ''TRANSACTION_CREATED'', ''WALLET_CREATED'', ''WALLET_UPDATED'', ''PERMISSION_GRANTED'', ''PERMISSION_REVOKED'', ''SYSTEM_CONFIG_CHANGED'', ''DATA_EXPORT'', ''DATA_IMPORT'', ''SECURITY_EVENT'', ''OTHER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_type\" ON \"AuditLog\" (\"type\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_user_id\" ON \"AuditLog\" (\"userId\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_organization_id\" ON \"AuditLog\" (\"organizationId\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_created_at\" ON \"AuditLog\" (\"createdAt\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
