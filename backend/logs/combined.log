{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_size\" AS ENUM(''SMALL'', ''MEDIUM'', ''LARGE'', ''ENTERPRISE''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_User_role\" AS ENUM(''ADMIN'', ''ORGANIZATION_ADMIN'', ''USER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_type\" AS ENUM(''RENEWABLE_ENERGY'', ''FORESTRY'', ''METHANE_REDUCTION'', ''ENERGY_EFFICIENCY'', ''WASTE_MANAGEMENT'', ''AGRICULTURE'', ''TRANSPORTATION'', ''INDUSTRIAL'', ''OTHER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''COMPLETED'', ''SUSPENDED'', ''CANCELLED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Project_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_CarbonCredit_status\" AS ENUM(''PENDING'', ''VERIFIED'', ''LISTED'', ''SOLD'', ''RETIRED'', ''TOKENIZED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_CarbonCredit_verificationStatus\" AS ENUM(''PENDING'', ''IN_REVIEW'', ''VERIFIED'', ''REJECTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:14:2214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Order_type\" AS ENUM(''BUY'', ''SELL''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Order_status\" AS ENUM(''PENDING'', ''MATCHED'', ''COMPLETED'', ''CANCELLED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Wallet_walletType\" AS ENUM(''GENERAL'', ''PROJECT'', ''TOKENIZATION'', ''TRADING'', ''RETIREMENT'', ''CUSTODY''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:15:2215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE UNIQUE INDEX \"wallet_address_network_chain_id\" ON \"Wallet\" (\"address\", \"network\", \"chainId\")\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Transaction_type\" AS ENUM(''DEPOSIT'', ''WITHDRAWAL'', ''PURCHASE'', ''SALE'', ''FEE'', ''BRIDGE'', ''SWAP'', ''TRANSFER'', ''RETIREMENT'', ''TOKENIZATION''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Transaction_status\" AS ENUM(''PENDING'', ''COMPLETED'', ''FAILED'', ''CANCELLED'', ''REVERTED''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:22:16:2216"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Notification_type\" AS ENUM(''SYSTEM'', ''ORDER'', ''TRANSACTION'', ''CREDIT'', ''VERIFICATION'', ''SUBSCRIPTION'', ''PAYMENT'', ''BILLING'', ''TEAM'', ''SECURITY'', ''MARKETPLACE'', ''WALLET''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_Notification_priority\" AS ENUM(''LOW'', ''NORMAL'', ''HIGH'', ''URGENT''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DO 'BEGIN CREATE TYPE \"public\".\"enum_AuditLog_type\" AS ENUM(''LOGIN_SUCCESS'', ''LOGIN_FAILED'', ''LOGOUT'', ''USER_CREATED'', ''USER_UPDATED'', ''USER_DELETED'', ''ORGANIZATION_CREATED'', ''ORGANIZATION_UPDATED'', ''PROJECT_CREATED'', ''PROJECT_UPDATED'', ''CREDIT_CREATED'', ''CREDIT_UPDATED'', ''ORDER_CREATED'', ''ORDER_UPDATED'', ''TRANSACTION_CREATED'', ''WALLET_CREATED'', ''WALLET_UPDATED'', ''PERMISSION_GRANTED'', ''PERMISSION_REVOKED'', ''SYSTEM_CONFIG_CHANGED'', ''DATA_EXPORT'', ''DATA_IMPORT'', ''SECURITY_EVENT'', ''OTHER''); EXCEPTION WHEN duplicate_object THEN null; END';\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): WITH ranges AS (  SELECT pg_range.rngtypid, pg_type.typname AS rngtypname,         pg_type.typarray AS rngtyparray, pg_range.rngsubtype    FROM pg_range LEFT OUTER JOIN pg_type ON pg_type.oid = pg_range.rngtypid)SELECT pg_type.typname, pg_type.typtype, pg_type.oid, pg_type.typarray,       ranges.rngtypname, ranges.rngtypid, ranges.rngtyparray  FROM pg_type LEFT OUTER JOIN ranges ON pg_type.oid = ranges.rngsubtype WHERE (pg_type.typtype IN('b', 'e'));\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:22:17:2217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_type\" ON \"AuditLog\" (\"type\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_user_id\" ON \"AuditLog\" (\"userId\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_organization_id\" ON \"AuditLog\" (\"organizationId\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): CREATE INDEX \"audit_log_created_at\" ON \"AuditLog\" (\"createdAt\")\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:07:237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:08:238"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:23:09:239"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 12:23:10:2310"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:23:34:2334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:53:34 +0000] \"GET /health HTTP/1.1\" 200 150 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:23:34:2334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:53:41 +0000] \"GET /api HTTP/1.1\" 200 420 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:23:41:2341"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"email\", \"name\", \"password\", \"role\", \"emailVerified\", \"jobTitle\", \"departmentName\", \"phoneNumber\", \"profileImage\", \"bio\", \"lastLoginAt\", \"twoFactorEnabled\", \"preferences\", \"organizationId\", \"departmentId\", \"divisionId\", \"createdAt\", \"updatedAt\" FROM \"User\" AS \"User\" WHERE \"User\".\"email\" = '<EMAIL>';\u001b[39m","timestamp":"2025-06-25 12:23:52:2352"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mClient error: User with this email already exists\u001b[39m","method":"POST","statusCode":409,"timestamp":"2025-06-25 12:23:52:2352","url":"/api/auth/register"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:53:52 +0000] \"POST /api/auth/register HTTP/1.1\" 409 289 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:23:52:2352"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"email\", \"User\".\"name\", \"User\".\"password\", \"User\".\"role\", \"User\".\"emailVerified\", \"User\".\"jobTitle\", \"User\".\"departmentName\", \"User\".\"phoneNumber\", \"User\".\"profileImage\", \"User\".\"bio\", \"User\".\"lastLoginAt\", \"User\".\"twoFactorEnabled\", \"User\".\"preferences\", \"User\".\"organizationId\", \"User\".\"departmentId\", \"User\".\"divisionId\", \"User\".\"createdAt\", \"User\".\"updatedAt\", \"organization\".\"id\" AS \"organization.id\", \"organization\".\"name\" AS \"organization.name\", \"organization\".\"status\" AS \"organization.status\", \"organization\".\"verificationStatus\" AS \"organization.verificationStatus\" FROM \"User\" AS \"User\" LEFT OUTER JOIN \"Organization\" AS \"organization\" ON \"User\".\"organizationId\" = \"organization\".\"id\" WHERE \"User\".\"email\" = '<EMAIL>';\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"User\" SET \"lastLoginAt\"=$1,\"updatedAt\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase error: column \"details\" of relation \"AuditLog\" does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_target.c","length":128,"line":"1066","name":"error","parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"position":"51","routine":"checkInsertTargets","severity":"ERROR","sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";"},"parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"parent":{"code":"42703","file":"parse_target.c","length":128,"line":"1066","name":"error","parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"position":"51","routine":"checkInsertTargets","severity":"ERROR","sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";"},"sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";","stack":"Error\n    at Query.run (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/query.js:50:25)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.insert (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)\n    at async model.save (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:2490:35)\n    at async AuditLog.create (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:1362:12)\n    at async AuditLog.logEvent (/Users/<USER>/Documents/carbonx/carbonx/backend/src/models/AuditLog.js:92:12)\n    at async /Users/<USER>/Documents/carbonx/carbonx/backend/src/routes/auth.js:175:3","timestamp":"2025-06-25 12:24:01:241"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mServer error: Database operation failed\u001b[39m","method":"POST","stack":"ApiError: Database operation failed\n    at handleSequelizeError (/Users/<USER>/Documents/carbonx/carbonx/backend/src/middleware/errorHandler.js:93:12)\n    at errorHandler (/Users/<USER>/Documents/carbonx/carbonx/backend/src/middleware/errorHandler.js:120:11)\n    at Layer.handle_error (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-06-25 12:24:01:241","url":"/api/auth/login","userAgent":"curl/8.7.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:06:54:01 +0000] \"POST /api/auth/login HTTP/1.1\" 500 1255 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 12:24:01:241"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-25 12:37:19:3719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:43:3743"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:44:3744"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:45:3745"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:37:46:3746"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:37:52:3752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:37:53:3753"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:37:54:3754"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:37:55:3755"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:37:55:3755"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:37:55:3755"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-25 12:37:59:3759"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 12:38:06:386"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:07:387"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 12:38:08:388"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-25 13:08:13:813"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:50:1250"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:51:1251"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:12:52:1252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:12:53:1253"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:13:19:1319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [25/Jun/2025:07:43:19 +0000] \"GET /health HTTP/1.1\" 200 149 \"-\" \"curl/8.7.1\"\u001b[39m","timestamp":"2025-06-25 13:13:19:1319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:07:157"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:15:08:158"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:15:09:159"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:34:1534"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:15:35:1535"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:15:36:1536"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:15:37:1537"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:00:160"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:16:01:161"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:16:02:162"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:15:1615"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:16:1616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:16:17:1617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:16:18:1618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:16:18:1618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:16:18:1618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:16:18:1618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:16:18:1618"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:32:34:3234"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:32:34:3234"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:32:34:3234"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:34:3234"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:32:36:3236"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:33:03:333"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:33:03:333"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:33:03:333"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:03:333"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:03:333"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:04:334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:05:335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:33:06:336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 13:33:17:3317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:18:3318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 13:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:0vjg40pse] Executing (dbd04e18-622e-45d3-91a1-b17f888bc8dd): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:0vjg40pse] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (dbd04e18-622e-45d3-91a1-b17f888bc8dd): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (dbd04e18-622e-45d3-91a1-b17f888bc8dd): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:0vjg40pse] Executing (dbd04e18-622e-45d3-91a1-b17f888bc8dd): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:0vjg40pse] Transaction rolled back due to error: Intentional error to test rollback\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"error":"Intentional error to test rollback","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:0vjg40pse] Transaction failed:\u001b[39m","stack":"Error: Intentional error to test rollback\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:36:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:20)\n    at async testTransactionRollback (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:15:5)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:216:5)","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"name\", \"description\", \"website\", \"logo\", \"status\", \"verificationStatus\", \"legalName\", \"registrationNumber\", \"taxId\", \"country\", \"address\", \"city\", \"state\", \"postalCode\", \"phoneNumber\", \"industry\", \"size\", \"foundedYear\", \"primaryContact\", \"primaryContactEmail\", \"primaryContactPhone\", \"transactionFeeRate\", \"listingFeeRate\", \"subscriptionFee\", \"createdAt\", \"updatedAt\" FROM \"Organization\" AS \"Organization\" WHERE \"Organization\".\"name\" = 'Test Rollback Org' LIMIT 1;\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"email\", \"name\", \"password\", \"role\", \"emailVerified\", \"jobTitle\", \"departmentName\", \"phoneNumber\", \"profileImage\", \"bio\", \"lastLoginAt\", \"twoFactorEnabled\", \"preferences\", \"organizationId\", \"departmentId\", \"divisionId\", \"createdAt\", \"updatedAt\" FROM \"User\" AS \"User\" WHERE \"User\".\"email\" = '<EMAIL>';\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:fmg6jk3v2] Executing (62d98354-1827-4800-a068-4de0f857f846): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:fmg6jk3v2] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (62d98354-1827-4800-a068-4de0f857f846): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (62d98354-1827-4800-a068-4de0f857f846): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:32:5632"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (62d98354-1827-4800-a068-4de0f857f846): INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:fmg6jk3v2] Executing (62d98354-1827-4800-a068-4de0f857f846): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:fmg6jk3v2] Transaction rolled back due to error: column \"updatedAt\" of relation \"AuditLog\" does not exist\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"error":"column \"updatedAt\" of relation \"AuditLog\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:fmg6jk3v2] Transaction failed:\u001b[39m","stack":"Error\n    at Query.run (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/query.js:50:25)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.insert (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)\n    at async model.save (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:2490:35)\n    at async AuditLog.create (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:1362:12)\n    at async /Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:81:7\n    at async withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:20)\n    at async testTransactionCommit (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:60:20)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:217:5)","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:oyj8kyn96] Executing (a480d967-d7e5-46eb-87de-54b7a12b1964): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:oyj8kyn96] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:oyj8kyn96] Executing (a480d967-d7e5-46eb-87de-54b7a12b1964): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:oyj8kyn96] Transaction rolled back due to error: Simulated deadlock\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"error":"Simulated deadlock","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:oyj8kyn96] Transaction failed:\u001b[39m","stack":"SequelizeDeadlockError: Simulated deadlock\n    at withRetryTransaction.maxRetries (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:132:23)\n    at withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async withRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:101:14)\n    at async testRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:126:20)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:218:5)","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mTransaction attempt 1 failed, retrying in 105.4141560440797ms:\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:1v42xuw14] Executing (ad201fc3-cf4a-49be-8c63-40e8b66b1cbe): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:1v42xuw14] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:1v42xuw14] Executing (ad201fc3-cf4a-49be-8c63-40e8b66b1cbe): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:1v42xuw14] Transaction rolled back due to error: Simulated deadlock\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"error":"Simulated deadlock","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:1v42xuw14] Transaction failed:\u001b[39m","stack":"SequelizeDeadlockError: Simulated deadlock\n    at withRetryTransaction.maxRetries (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:132:23)\n    at withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async withRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:101:14)\n    at async testRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:126:20)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:218:5)","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mTransaction attempt 2 failed, retrying in 110.66008339830782ms:\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:lrhgjvpsu] Executing (c504b403-026d-4f30-bee7-60963252e092): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:lrhgjvpsu] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (c504b403-026d-4f30-bee7-60963252e092): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:lrhgjvpsu] Executing (c504b403-026d-4f30-bee7-60963252e092): COMMIT;\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:lrhgjvpsu] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"Organization\" WHERE \"id\" = '8b4673056c48c214490f2f8e'\u001b[39m","timestamp":"2025-06-25 15:56:33:5633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:1o68ny5ej] Executing (5e68a17a-88da-440a-80e9-e998f8a6980d): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:1o68ny5ej] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (5e68a17a-88da-440a-80e9-e998f8a6980d): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:1o68ny5ej] Executing (5e68a17a-88da-440a-80e9-e998f8a6980d): COMMIT;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:1o68ny5ej] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:1e3aqmn5b] Executing (af7f93e7-9b4d-4819-9859-62e00765479e): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:97mn05a61] Executing (e79ae0e4-e93c-491e-8a73-11022e259100): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:1e3aqmn5b] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:hqyhtq7ml] Executing (dae232cf-0749-4d98-8689-450cf8f797a4): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:97mn05a61] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:33vt6c50x] Executing (07e34753-9351-4dc7-859c-cdf886f0c666): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (af7f93e7-9b4d-4819-9859-62e00765479e): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:hqyhtq7ml] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:33vt6c50x] Transaction started\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (e79ae0e4-e93c-491e-8a73-11022e259100): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (dae232cf-0749-4d98-8689-450cf8f797a4): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (07e34753-9351-4dc7-859c-cdf886f0c666): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:1e3aqmn5b] Executing (af7f93e7-9b4d-4819-9859-62e00765479e): COMMIT;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:33vt6c50x] Executing (07e34753-9351-4dc7-859c-cdf886f0c666): COMMIT;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:hqyhtq7ml] Executing (dae232cf-0749-4d98-8689-450cf8f797a4): COMMIT;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:97mn05a61] Executing (e79ae0e4-e93c-491e-8a73-11022e259100): COMMIT;\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:1e3aqmn5b] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 15:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:33vt6c50x] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:hqyhtq7ml] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:97mn05a61] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = '622a7cd0a6ca1e1dfb077f91'\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = 'c6afddf0dbf08af8ea4837fa'\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = 'aeee90651924f134e5d9b526'\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = '3bb2a021c2e3da7ae1a8ff1d'\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = '81735b449a6884a0461bce8e'\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"Organization\" WHERE \"id\" = 'e53a6009666acb75231ca84c'\u001b[39m","timestamp":"2025-06-25 15:56:35:5635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:23:5723"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 15:57:24:5724"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 15:57:25:5725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 15:57:31:5731"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 15:57:31:5731"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 15:57:31:5731"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 15:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:33:5733"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:34:5734"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 15:57:34:5734"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 15:57:34:5734"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 15:57:34:5734"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 15:57:34:5734"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 15:57:34:5734"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 15:57:49:5749"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:50:5750"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:51:5751"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 15:57:52:5752"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:56:056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:57:057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 16:00:58:058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 16:01:30:130"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:31:131"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:32:132"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 16:01:33:133"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Organization' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_User_role' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'User' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:44:144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Project'\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Project_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Project' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'CarbonCredit'\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_CarbonCredit_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'CarbonCredit' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Order'\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Order_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Order' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Wallet'\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Wallet_walletType' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Wallet' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:45:145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Transaction'\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Transaction_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Transaction' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Notification'\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Notification_priority' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'Notification' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'AuditLog'\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_AuditLog_type' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'AuditLog' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase models synchronized\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5001\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:5001/api\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API Documentation: http://localhost:5001/api/docs\u001b[39m","timestamp":"2025-06-25 16:01:46:146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-06-25 16:04:57:457"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organization'\u001b[39m","timestamp":"2025-06-25 16:04:57:457"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_status' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:04:57:457"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_verificationStatus' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT t.typname enum_name, array_agg(e.enumlabel ORDER BY enumsortorder) enum_value FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace WHERE n.nspname = 'public' AND t.typname='enum_Organization_size' GROUP BY 1\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT pk.constraint_type as \"Constraint\",c.column_name as \"Field\", c.column_default as \"Default\",c.is_nullable as \"Null\", (CASE WHEN c.udt_name = 'hstore' THEN c.udt_name ELSE c.data_type END) || (CASE WHEN c.character_maximum_length IS NOT NULL THEN '(' || c.character_maximum_length || ')' ELSE '' END) as \"Type\", (SELECT array_agg(e.enumlabel) FROM pg_catalog.pg_type t JOIN pg_catalog.pg_enum e ON t.oid=e.enumtypid WHERE t.typname=c.udt_name) AS \"special\", (SELECT pgd.description FROM pg_catalog.pg_statio_all_tables AS st INNER JOIN pg_catalog.pg_description pgd on (pgd.objoid=st.relid) WHERE c.ordinal_position=pgd.objsubid AND c.table_name=st.relname) AS \"Comment\" FROM information_schema.columns c LEFT JOIN (SELECT tc.table_schema, tc.table_name, cu.column_name, tc.constraint_type FROM information_schema.TABLE_CONSTRAINTS tc JOIN information_schema.KEY_COLUMN_USAGE  cu ON tc.table_schema=cu.table_schema and tc.table_name=cu.table_name and tc.constraint_name=cu.constraint_name and tc.constraint_type='PRIMARY KEY') pk ON pk.table_schema=c.table_schema AND pk.table_name=c.table_name AND pk.column_name=c.column_name WHERE c.table_name = 'Organization' AND c.table_schema = 'public'\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT DISTINCT tc.constraint_name as constraint_name, tc.constraint_schema as constraint_schema, tc.constraint_catalog as constraint_catalog, tc.table_name as table_name,tc.table_schema as table_schema,tc.table_catalog as table_catalog,tc.initially_deferred as initially_deferred,tc.is_deferrable as is_deferrable,kcu.column_name as column_name,ccu.table_schema  AS referenced_table_schema,ccu.table_catalog  AS referenced_table_catalog,ccu.table_name  AS referenced_table_name,ccu.column_name AS referenced_column_name FROM information_schema.table_constraints AS tc JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name WHERE constraint_type = 'FOREIGN KEY' AND tc.table_name = 'Organization' AND tc.table_catalog = 'carbonix'\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): ALTER TABLE \"Organization\" ALTER COLUMN \"name\" SET NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"name\" DROP DEFAULT;ALTER TABLE \"Organization\" ALTER COLUMN \"name\" TYPE VARCHAR(255);\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): ALTER TABLE \"Organization\" ALTER COLUMN \"description\" DROP NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"description\" DROP DEFAULT;ALTER TABLE \"Organization\" ALTER COLUMN \"description\" TYPE TEXT;\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): ALTER TABLE \"Organization\" ALTER COLUMN \"website\" DROP NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"website\" DROP DEFAULT;ALTER TABLE \"Organization\" ALTER COLUMN \"website\" TYPE VARCHAR(255);\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): ALTER TABLE \"Organization\" ALTER COLUMN \"logo\" DROP NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"logo\" DROP DEFAULT;ALTER TABLE \"Organization\" ALTER COLUMN \"logo\" TYPE VARCHAR(255);\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET DEFAULT 'PENDING';DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"Organization\" ALTER COLUMN \"status\" TYPE \"public\".\"enum_Organization_status\" USING (\"status\"::\"public\".\"enum_Organization_status\");\u001b[39m","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase sync error: cannot cast type \"OrganizationStatus\" to \"enum_Organization_status\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42846","file":"parse_expr.c","length":139,"line":"2697","name":"error","position":"408","routine":"transformTypeCast","severity":"ERROR","sql":"ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET DEFAULT 'PENDING';DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"Organization\" ALTER COLUMN \"status\" TYPE \"public\".\"enum_Organization_status\" USING (\"status\"::\"public\".\"enum_Organization_status\");"},"parameters":{},"parent":{"code":"42846","file":"parse_expr.c","length":139,"line":"2697","name":"error","position":"408","routine":"transformTypeCast","severity":"ERROR","sql":"ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET DEFAULT 'PENDING';DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"Organization\" ALTER COLUMN \"status\" TYPE \"public\".\"enum_Organization_status\" USING (\"status\"::\"public\".\"enum_Organization_status\");"},"sql":"ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET NOT NULL;ALTER TABLE \"Organization\" ALTER COLUMN \"status\" SET DEFAULT 'PENDING';DO 'BEGIN CREATE TYPE \"public\".\"enum_Organization_status\" AS ENUM(''PENDING'', ''ACTIVE'', ''SUSPENDED'', ''INACTIVE''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"Organization\" ALTER COLUMN \"status\" TYPE \"public\".\"enum_Organization_status\" USING (\"status\"::\"public\".\"enum_Organization_status\");","stack":"Error\n    at Query.run (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/query.js:50:25)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Organization.sync (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:984:11)\n    at async Sequelize.sync (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:377:9)\n    at async syncDatabase (/Users/<USER>/Documents/carbonx/carbonx/backend/sync-database.js:38:5)\n    at async alterSyncDatabase (/Users/<USER>/Documents/carbonx/carbonx/backend/sync-database.js:92:10)\n    at async main (/Users/<USER>/Documents/carbonx/carbonx/backend/sync-database.js:160:7)","timestamp":"2025-06-25 16:04:58:458"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:5x8ypqjpz] Executing (14fd2149-90af-4319-802a-4d63875a83e8): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:5x8ypqjpz] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (14fd2149-90af-4319-802a-4d63875a83e8): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (14fd2149-90af-4319-802a-4d63875a83e8): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:5x8ypqjpz] Executing (14fd2149-90af-4319-802a-4d63875a83e8): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:5x8ypqjpz] Transaction rolled back due to error: Intentional error to test rollback\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"error":"Intentional error to test rollback","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:5x8ypqjpz] Transaction failed:\u001b[39m","stack":"Error: Intentional error to test rollback\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:36:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:20)\n    at async testTransactionRollback (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:15:5)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:216:5)","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"name\", \"description\", \"website\", \"logo\", \"status\", \"verificationStatus\", \"legalName\", \"registrationNumber\", \"taxId\", \"country\", \"address\", \"city\", \"state\", \"postalCode\", \"phoneNumber\", \"industry\", \"size\", \"foundedYear\", \"primaryContact\", \"primaryContactEmail\", \"primaryContactPhone\", \"transactionFeeRate\", \"listingFeeRate\", \"subscriptionFee\", \"createdAt\", \"updatedAt\" FROM \"Organization\" AS \"Organization\" WHERE \"Organization\".\"name\" = 'Test Rollback Org' LIMIT 1;\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"email\", \"name\", \"password\", \"role\", \"emailVerified\", \"jobTitle\", \"departmentName\", \"phoneNumber\", \"profileImage\", \"bio\", \"lastLoginAt\", \"twoFactorEnabled\", \"preferences\", \"organizationId\", \"departmentId\", \"divisionId\", \"createdAt\", \"updatedAt\" FROM \"User\" AS \"User\" WHERE \"User\".\"email\" = '<EMAIL>';\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:vaf52ewfz] Executing (abe0bc11-1111-4712-8b37-866799f1ce56): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:vaf52ewfz] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (abe0bc11-1111-4712-8b37-866799f1ce56): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:08:58"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (abe0bc11-1111-4712-8b37-866799f1ce56): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (abe0bc11-1111-4712-8b37-866799f1ce56): INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:vaf52ewfz] Executing (abe0bc11-1111-4712-8b37-866799f1ce56): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:vaf52ewfz] Transaction rolled back due to error: column \"updatedAt\" of relation \"AuditLog\" does not exist\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"error":"column \"updatedAt\" of relation \"AuditLog\" does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:vaf52ewfz] Transaction failed:\u001b[39m","stack":"Error\n    at Query.run (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/query.js:50:25)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.insert (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)\n    at async model.save (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:2490:35)\n    at async AuditLog.create (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:1362:12)\n    at async /Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:81:7\n    at async withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:20)\n    at async testTransactionCommit (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:60:20)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:217:5)","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:5ufgrhntr] Executing (2015ba68-a30f-4712-8989-04f21cf18d27): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:5ufgrhntr] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:5ufgrhntr] Executing (2015ba68-a30f-4712-8989-04f21cf18d27): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:5ufgrhntr] Transaction rolled back due to error: Simulated deadlock\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"error":"Simulated deadlock","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:5ufgrhntr] Transaction failed:\u001b[39m","stack":"SequelizeDeadlockError: Simulated deadlock\n    at withRetryTransaction.maxRetries (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:132:23)\n    at withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async withRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:101:14)\n    at async testRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:126:20)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:218:5)","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mTransaction attempt 1 failed, retrying in 114.47446728666783ms:\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:fj340cpn5] Executing (caecafae-dcc0-4d1b-9a50-910cece72363): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:fj340cpn5] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:fj340cpn5] Executing (caecafae-dcc0-4d1b-9a50-910cece72363): ROLLBACK;\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[TX:fj340cpn5] Transaction rolled back due to error: Simulated deadlock\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"error":"Simulated deadlock","level":"\u001b[31merror\u001b[39m","message":"\u001b[31m[TX:fj340cpn5] Transaction failed:\u001b[39m","stack":"SequelizeDeadlockError: Simulated deadlock\n    at withRetryTransaction.maxRetries (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:132:23)\n    at withTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:22:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async withRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/src/utils/transaction.js:101:14)\n    at async testRetryTransaction (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:126:20)\n    at async runTransactionTests (/Users/<USER>/Documents/carbonx/carbonx/backend/test-transactions.js:218:5)","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mTransaction attempt 2 failed, retrying in 133.47333217159297ms:\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:0kt2v56vh] Executing (897830d3-3bf1-4907-8be1-595e5b10108c): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:09:59"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:0kt2v56vh] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (897830d3-3bf1-4907-8be1-595e5b10108c): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:0kt2v56vh] Executing (897830d3-3bf1-4907-8be1-595e5b10108c): COMMIT;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:0kt2v56vh] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"Organization\" WHERE \"id\" = '210fb7c6d4ca50deeb342e01'\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): INSERT INTO \"Organization\" (\"id\",\"name\",\"status\",\"verificationStatus\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"name\",\"description\",\"website\",\"logo\",\"status\",\"verificationStatus\",\"legalName\",\"registrationNumber\",\"taxId\",\"country\",\"address\",\"city\",\"state\",\"postalCode\",\"phoneNumber\",\"industry\",\"size\",\"foundedYear\",\"primaryContact\",\"primaryContactEmail\",\"primaryContactPhone\",\"transactionFeeRate\",\"listingFeeRate\",\"subscriptionFee\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:6c5lsro4s] Executing (67b4bb66-1784-4db4-85bf-5670a023893a): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:6c5lsro4s] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (67b4bb66-1784-4db4-85bf-5670a023893a): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:6c5lsro4s] Executing (67b4bb66-1784-4db4-85bf-5670a023893a): COMMIT;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:6c5lsro4s] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:x21vhfzpo] Executing (3ba09c2a-8b90-4480-a486-2622c84c18a2): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:nu5whmp6f] Executing (13cf9679-5cb1-4cb9-82ad-45a4ca379544): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:6odt9ey8j] Executing (36160928-6493-4c98-a9bf-737ba32c1850): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:adytlm5wp] Executing (54d0f25a-ac28-4541-94dd-827901999b19): START TRANSACTION;\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:x21vhfzpo] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:nu5whmp6f] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:6odt9ey8j] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:adytlm5wp] Transaction started\u001b[39m","timestamp":"2025-06-25 16:05:10:510"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (3ba09c2a-8b90-4480-a486-2622c84c18a2): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (13cf9679-5cb1-4cb9-82ad-45a4ca379544): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (36160928-6493-4c98-a9bf-737ba32c1850): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (54d0f25a-ac28-4541-94dd-827901999b19): INSERT INTO \"User\" (\"id\",\"email\",\"name\",\"password\",\"role\",\"twoFactorEnabled\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING \"id\",\"email\",\"name\",\"password\",\"role\",\"emailVerified\",\"jobTitle\",\"departmentName\",\"phoneNumber\",\"profileImage\",\"bio\",\"lastLoginAt\",\"twoFactorEnabled\",\"preferences\",\"organizationId\",\"departmentId\",\"divisionId\",\"createdAt\",\"updatedAt\";\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:x21vhfzpo] Executing (3ba09c2a-8b90-4480-a486-2622c84c18a2): COMMIT;\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:nu5whmp6f] Executing (13cf9679-5cb1-4cb9-82ad-45a4ca379544): COMMIT;\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:6odt9ey8j] Executing (36160928-6493-4c98-a9bf-737ba32c1850): COMMIT;\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37m[TX:adytlm5wp] Executing (54d0f25a-ac28-4541-94dd-827901999b19): COMMIT;\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:x21vhfzpo] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:nu5whmp6f] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:6odt9ey8j] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[TX:adytlm5wp] Transaction committed successfully\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = 'a32c376bea6a1da31841ae1a'\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = '832f450814b63d04d678e859'\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = '552f49c403537c4d56938855'\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = 'e994eaa6b6a7b8270ef33c69'\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"User\" WHERE \"id\" = '704eced3974bb0b52e7e47bc'\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): DELETE FROM \"Organization\" WHERE \"id\" = '12c831a6d5f1ad3f7f08e5a9'\u001b[39m","timestamp":"2025-06-25 16:05:11:511"}
