{"address": "::", "code": "EADDRINUSE", "errno": -48, "level": "\u001b[31<PERSON><PERSON>r\u001b[39m", "message": "\u001b[31mUncaught Exception: listen EADDRINUSE: address already in use :::5000\u001b[39m", "port": 5000, "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (/Users/<USER>/Documents/carbonx/carbonx/backend/src/server.js:108:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)", "syscall": "listen", "timestamp": "2025-06-25 12:22:18:2218"}