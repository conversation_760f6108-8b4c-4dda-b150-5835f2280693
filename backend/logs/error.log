{"address":"::","code":"EADDRINUSE","errno":-48,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUncaught Exception: listen EADDRINUSE: address already in use :::5000\u001b[39m","port":5000,"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (/Users/<USER>/Documents/carbonx/carbonx/backend/src/server.js:108:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-25 12:22:18:2218"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase error: column \"details\" of relation \"AuditLog\" does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_target.c","length":128,"line":"1066","name":"error","parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"position":"51","routine":"checkInsertTargets","severity":"ERROR","sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";"},"parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"parent":{"code":"42703","file":"parse_target.c","length":128,"line":"1066","name":"error","parameters":["fb225e07acf84aa460771e18","LOGIN_SUCCESS","User logged in: <EMAIL>","{\"userId\":\"cmc8wztuu0002uxnypepa7iew\"}","::1","curl/8.7.1","cmc8wztuu0002uxnypepa7iew","cmc8wztnk0000uxnyt44i81e0","2025-06-25 06:54:01.287 +00:00","2025-06-25 06:54:01.287 +00:00"],"position":"51","routine":"checkInsertTargets","severity":"ERROR","sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";"},"sql":"INSERT INTO \"AuditLog\" (\"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING \"id\",\"type\",\"description\",\"details\",\"ipAddress\",\"userAgent\",\"userId\",\"organizationId\",\"createdAt\",\"updatedAt\";","stack":"Error\n    at Query.run (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/query.js:50:25)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.insert (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)\n    at async model.save (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:2490:35)\n    at async AuditLog.create (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/sequelize/lib/model.js:1362:12)\n    at async AuditLog.logEvent (/Users/<USER>/Documents/carbonx/carbonx/backend/src/models/AuditLog.js:92:12)\n    at async /Users/<USER>/Documents/carbonx/carbonx/backend/src/routes/auth.js:175:3","timestamp":"2025-06-25 12:24:01:241"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mServer error: Database operation failed\u001b[39m","method":"POST","stack":"ApiError: Database operation failed\n    at handleSequelizeError (/Users/<USER>/Documents/carbonx/carbonx/backend/src/middleware/errorHandler.js:93:12)\n    at errorHandler (/Users/<USER>/Documents/carbonx/carbonx/backend/src/middleware/errorHandler.js:120:11)\n    at Layer.handle_error (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/router/index.js:286:9","timestamp":"2025-06-25 12:24:01:241","url":"/api/auth/login","userAgent":"curl/8.7.1"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUncaught Exception: listen EADDRINUSE: address already in use :::5001\u001b[39m","port":5001,"stack":"Error: listen EADDRINUSE: address already in use :::5001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (/Users/<USER>/Documents/carbonx/carbonx/backend/src/server.js:108:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-25 12:37:46:3746"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUncaught Exception: listen EADDRINUSE: address already in use :::5001\u001b[39m","port":5001,"stack":"Error: listen EADDRINUSE: address already in use :::5001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Function.listen (/Users/<USER>/Documents/carbonx/carbonx/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (/Users/<USER>/Documents/carbonx/carbonx/backend/src/server.js:108:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-25 12:37:55:3755"}
