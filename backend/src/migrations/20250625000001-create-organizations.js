'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Organization table
    await queryInterface.createTable('Organization', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      website: {
        type: Sequelize.STRING,
        allowNull: true
      },
      logo: {
        type: Sequelize.STRING,
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE'),
        allowNull: false,
        defaultValue: 'PENDING'
      },
      verificationStatus: {
        type: Sequelize.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED'),
        allowNull: false,
        defaultValue: 'PENDING'
      },
      legalName: {
        type: Sequelize.STRING,
        allowNull: true
      },
      registrationNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      taxId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      city: {
        type: Sequelize.STRING,
        allowNull: true
      },
      state: {
        type: Sequelize.STRING,
        allowNull: true
      },
      postalCode: {
        type: Sequelize.STRING,
        allowNull: true
      },
      phoneNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      industry: {
        type: Sequelize.STRING,
        allowNull: true
      },
      size: {
        type: Sequelize.ENUM('SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'),
        allowNull: true
      },
      foundedYear: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      primaryContact: {
        type: Sequelize.STRING,
        allowNull: true
      },
      primaryContactEmail: {
        type: Sequelize.STRING,
        allowNull: true
      },
      primaryContactPhone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      transactionFeeRate: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0.01
      },
      listingFeeRate: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0.005
      },
      subscriptionFee: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0.0
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('Organization', ['status']);
    await queryInterface.addIndex('Organization', ['verificationStatus']);
    await queryInterface.addIndex('Organization', ['country']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Organization');
  }
};
