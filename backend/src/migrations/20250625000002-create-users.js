'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create User table
    await queryInterface.createTable('User', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      password: {
        type: Sequelize.STRING,
        allowNull: true
      },
      role: {
        type: Sequelize.ENUM('ADMIN', 'ORGANIZATION_ADMIN', 'USER'),
        allowNull: false,
        defaultValue: 'USER'
      },
      emailVerified: {
        type: Sequelize.DATE,
        allowNull: true
      },
      jobTitle: {
        type: Sequelize.STRING,
        allowNull: true
      },
      departmentName: {
        type: Sequelize.STRING,
        allowNull: true
      },
      phoneNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      profileImage: {
        type: Sequelize.STRING,
        allowNull: true
      },
      bio: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      lastLoginAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      twoFactorEnabled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      preferences: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      organizationId: {
        type: Sequelize.STRING,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      departmentId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      divisionId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('User', ['email']);
    await queryInterface.addIndex('User', ['role']);
    await queryInterface.addIndex('User', ['organizationId']);
    await queryInterface.addIndex('User', ['emailVerified']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('User');
  }
};
