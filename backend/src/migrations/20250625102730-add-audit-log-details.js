'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add missing columns to AuditLog table to match our model
    await queryInterface.addColumn('AuditLog', 'details', {
      type: Sequelize.JSONB,
      allowNull: true
    });

    await queryInterface.addColumn('AuditLog', 'ipAddress', {
      type: Sequelize.STRING,
      allowNull: true
    });

    await queryInterface.addColumn('AuditLog', 'userAgent', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    // Add updatedAt column if it doesn't exist
    const tableDescription = await queryInterface.describeTable('AuditLog');
    if (!tableDescription.updatedAt) {
      await queryInterface.addColumn('AuditLog', 'updatedAt', {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      });
    }
  },

  async down (queryInterface, Sequelize) {
    // Remove the columns we added
    await queryInterface.removeColumn('AuditLog', 'details');
    await queryInterface.removeColumn('AuditLog', 'ipAddress');
    await queryInterface.removeColumn('AuditLog', 'userAgent');

    // Only remove updatedAt if we added it
    const tableDescription = await queryInterface.describeTable('AuditLog');
    if (tableDescription.updatedAt) {
      await queryInterface.removeColumn('AuditLog', 'updatedAt');
    }
  }
};
