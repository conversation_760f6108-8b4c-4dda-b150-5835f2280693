const express = require('express');
const Joi = require('joi');
const { Op } = require('sequelize');
const { CarbonCredit, Project, User, Organization, Order, AuditLog } = require('../models');
const { authenticate, requireOrganization } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, ConflictError } = require('../middleware/errorHandler');
const { withTransaction, withBatchTransaction, withRetryTransaction } = require('../utils/transaction');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createCarbonCreditSchema = Joi.object({
  name: Joi.string().min(2).max(200).required(),
  description: Joi.string().max(1000).optional(),
  quantity: Joi.number().positive().required(),
  price: Joi.number().positive().required(),
  vintage: Joi.number().integer().min(1990).max(new Date().getFullYear() + 10).required(),
  standard: Joi.string().required(),
  methodology: Joi.string().required(),
  projectId: Joi.string().required(),
  minPurchaseQuantity: Joi.number().positive().optional()
});

const purchaseCarbonCreditSchema = Joi.object({
  quantity: Joi.number().positive().required(),
  price: Joi.number().positive().required()
});

/**
 * @route GET /api/carbon-credits
 * @desc Get carbon credits with filtering and pagination
 * @access Private
 */
router.get('/', authenticate, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, vintage, standard, minPrice, maxPrice } = req.query;

  // Build where clause
  const where = {};
  if (status) where.status = status;
  if (vintage) where.vintage = vintage;
  if (standard) where.standard = standard;
  if (minPrice || maxPrice) {
    where.price = {};
    if (minPrice) where.price[Op.gte] = parseFloat(minPrice);
    if (maxPrice) where.price[Op.lte] = parseFloat(maxPrice);
  }

  const offset = (page - 1) * limit;

  const { rows: carbonCredits, count } = await CarbonCredit.findAndCountAll({
    where,
    include: [
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'name', 'type', 'location', 'country']
      },
      {
        model: Organization,
        as: 'organization',
        attributes: ['id', 'name']
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [['createdAt', 'DESC']]
  });

  res.json({
    carbonCredits,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      pages: Math.ceil(count / limit)
    }
  });
}));

/**
 * @route POST /api/carbon-credits
 * @desc Create a new carbon credit
 * @access Private (Organization required)
 */
router.post('/', authenticate, requireOrganization, asyncHandler(async (req, res) => {
  // Validate request body
  const { error, value } = createCarbonCreditSchema.validate(req.body);
  if (error) {
    throw new ValidationError('Validation failed', error.details);
  }

  const result = await withTransaction(async (transaction) => {
    // Verify project exists and belongs to user's organization
    const project = await Project.findOne({
      where: {
        id: value.projectId,
        organizationId: req.user.organizationId
      },
      transaction
    });

    if (!project) {
      throw new NotFoundError('Project not found or access denied');
    }

    // Create carbon credit
    const carbonCredit = await CarbonCredit.create({
      ...value,
      availableQuantity: value.quantity,
      userId: req.user.id,
      organizationId: req.user.organizationId,
      status: 'PENDING',
      verificationStatus: 'PENDING',
      listingDate: new Date()
    }, { transaction });

    // Log creation
    await AuditLog.create({
      type: 'CREDIT_CREATED',
      description: `Carbon credit created: ${carbonCredit.name}`,
      details: {
        carbonCreditId: carbonCredit.id,
        projectId: project.id,
        quantity: carbonCredit.quantity
      },
      userId: req.user.id,
      organizationId: req.user.organizationId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    }, { transaction });

    return { carbonCredit, project };
  });

  logger.info(`Carbon credit created: ${result.carbonCredit.name} by user ${req.user.email}`);

  res.status(201).json({
    message: 'Carbon credit created successfully',
    carbonCredit: result.carbonCredit
  });
}));

/**
 * @route POST /api/carbon-credits/:id/purchase
 * @desc Purchase carbon credits (atomic transaction)
 * @access Private
 */
router.post('/:id/purchase', authenticate, asyncHandler(async (req, res) => {
  // Validate request body
  const { error, value } = purchaseCarbonCreditSchema.validate(req.body);
  if (error) {
    throw new ValidationError('Validation failed', error.details);
  }

  const { quantity, price } = value;
  const carbonCreditId = req.params.id;

  // Use retry transaction for purchase (handles concurrent purchases)
  const result = await withRetryTransaction(async (transaction) => {
    // Get carbon credit with lock
    const carbonCredit = await CarbonCredit.findByPk(carbonCreditId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        }
      ],
      lock: transaction.LOCK.UPDATE, // Row-level lock
      transaction
    });

    if (!carbonCredit) {
      throw new NotFoundError('Carbon credit not found');
    }

    // Validate purchase
    if (!carbonCredit.canPurchase(quantity)) {
      throw new ConflictError('Insufficient quantity available or minimum purchase requirement not met');
    }

    if (carbonCredit.userId === req.user.id) {
      throw new ConflictError('Cannot purchase your own carbon credits');
    }

    // Create order
    const order = await Order.create({
      type: 'BUY',
      quantity,
      price,
      buyerId: req.user.id,
      sellerId: carbonCredit.userId,
      carbonCreditId: carbonCredit.id,
      status: 'COMPLETED'
    }, { transaction });

    // Update carbon credit availability
    await carbonCredit.update({
      availableQuantity: carbonCredit.availableQuantity - quantity
    }, { transaction });

    // Log purchase
    await AuditLog.create({
      type: 'CREDIT_PURCHASED',
      description: `Carbon credit purchased: ${quantity} units of ${carbonCredit.name}`,
      details: {
        orderId: order.id,
        carbonCreditId: carbonCredit.id,
        quantity,
        price,
        totalAmount: quantity * price
      },
      userId: req.user.id,
      organizationId: req.user.organizationId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    }, { transaction });

    return { order, carbonCredit };
  }, {
    maxRetries: 3, // Retry up to 3 times for deadlocks
    baseDelay: 100 // Start with 100ms delay
  });

  logger.info(`Carbon credit purchased: ${quantity} units by user ${req.user.email}`);

  res.json({
    message: 'Carbon credit purchased successfully',
    order: result.order,
    carbonCredit: {
      id: result.carbonCredit.id,
      name: result.carbonCredit.name,
      availableQuantity: result.carbonCredit.availableQuantity
    }
  });
}));

/**
 * @route POST /api/carbon-credits/batch-retire
 * @desc Retire multiple carbon credits in a single transaction
 * @access Private
 */
router.post('/batch-retire', authenticate, asyncHandler(async (req, res) => {
  const { carbonCreditIds, retirementReason, retirementBeneficiary } = req.body;

  if (!Array.isArray(carbonCreditIds) || carbonCreditIds.length === 0) {
    throw new ValidationError('carbonCreditIds must be a non-empty array');
  }

  // Use batch transaction for retiring multiple credits
  const operations = carbonCreditIds.map(creditId => async (transaction) => {
    const carbonCredit = await CarbonCredit.findOne({
      where: {
        id: creditId,
        userId: req.user.id // Only allow retiring own credits
      },
      transaction
    });

    if (!carbonCredit) {
      throw new NotFoundError(`Carbon credit ${creditId} not found or access denied`);
    }

    if (carbonCredit.status === 'RETIRED') {
      throw new ConflictError(`Carbon credit ${creditId} is already retired`);
    }

    // Retire the credit
    await carbonCredit.update({
      status: 'RETIRED',
      retirementDate: new Date(),
      retirementReason,
      retirementBeneficiary
    }, { transaction });

    // Log retirement
    await AuditLog.create({
      type: 'CREDIT_RETIRED',
      description: `Carbon credit retired: ${carbonCredit.name}`,
      details: {
        carbonCreditId: carbonCredit.id,
        retirementReason,
        retirementBeneficiary
      },
      userId: req.user.id,
      organizationId: req.user.organizationId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    }, { transaction });

    return carbonCredit;
  });

  const retiredCredits = await withBatchTransaction(operations);

  logger.info(`Batch retirement completed: ${retiredCredits.length} credits by user ${req.user.email}`);

  res.json({
    message: 'Carbon credits retired successfully',
    retiredCredits: retiredCredits.map(credit => ({
      id: credit.id,
      name: credit.name,
      status: credit.status,
      retirementDate: credit.retirementDate
    }))
  });
}));

module.exports = router;
