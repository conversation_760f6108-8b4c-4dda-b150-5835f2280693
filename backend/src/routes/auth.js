const express = require('express');
const Joi = require('joi');
const { User, Organization, AuditLog } = require('../models');
const { generateTokens, verifyRefreshToken } = require('../utils/jwt');
const { authenticate } = require('../middleware/auth');
const { asyncHandler, ValidationError, AuthenticationError, ConflictError } = require('../middleware/errorHandler');
const { withTransaction, withRetryTransaction } = require('../utils/transaction');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const registerSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).max(128).required(),
  organizationName: Joi.string().min(2).max(100).optional(),
  organizationId: Joi.string().optional()
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required()
});

/**
 * @route POST /api/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post('/register', asyncHandler(async (req, res) => {
  // Validate request body
  const { error, value } = registerSchema.validate(req.body);
  if (error) {
    throw new ValidationError('Validation failed', error.details);
  }

  const { name, email, password, organizationName, organizationId } = value;

  // Use transaction helper for atomic operations
  const result = await withRetryTransaction(async (transaction) => {
    // Check if user already exists
    const existingUser = await User.findOne({
      where: { email },
      transaction
    });
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    let organization = null;

    // Handle organization
    if (organizationId) {
      // Join existing organization
      organization = await Organization.findByPk(organizationId, { transaction });
      if (!organization) {
        throw new ValidationError('Organization not found');
      }
    } else if (organizationName) {
      // Create new organization
      organization = await Organization.create({
        name: organizationName,
        status: 'PENDING',
        verificationStatus: 'PENDING'
      }, { transaction });
    }

    // Create user
    const user = await User.create({
      name,
      email,
      password,
      role: organization ? 'ORGANIZATION_ADMIN' : 'USER',
      organizationId: organization?.id || null
    }, { transaction });

    // Log registration
    await AuditLog.create({
      type: 'USER_CREATED',
      description: `User registered: ${email}`,
      details: { userId: user.id, organizationId: organization?.id },
      userId: user.id,
      organizationId: organization?.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    }, { transaction });

    return { user, organization };
  });

  // Generate tokens (after successful transaction)
  const tokens = generateTokens(result.user);

  logger.info(`User registered: ${email}`);

  res.status(201).json({
    message: 'User registered successfully',
    user: {
      id: result.user.id,
      name: result.user.name,
      email: result.user.email,
      role: result.user.role,
      organizationId: result.user.organizationId,
      emailVerified: result.user.emailVerified
    },
    organization: result.organization ? {
      id: result.organization.id,
      name: result.organization.name,
      status: result.organization.status,
      verificationStatus: result.organization.verificationStatus
    } : null,
    tokens
  });
}));

/**
 * @route POST /api/auth/login
 * @desc Login user
 * @access Public
 */
router.post('/login', asyncHandler(async (req, res) => {
  // Validate request body
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    throw new ValidationError('Validation failed', error.details);
  }

  const { email, password } = value;

  // Use transaction for login operations
  const transaction = await sequelize.transaction();

  try {
    // Find user with organization
    const user = await User.findOne({
      where: { email },
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'status', 'verificationStatus']
        }
      ],
      transaction
    });

    if (!user) {
      // Log failed login attempt
      await AuditLog.create({
        type: 'LOGIN_FAILED',
        description: `Failed login attempt: ${email} (user not found)`,
        details: { email },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }, { transaction });

      await transaction.commit();
      throw new AuthenticationError('Invalid email or password');
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      // Log failed login attempt
      await AuditLog.create({
        type: 'LOGIN_FAILED',
        description: `Failed login attempt: ${email} (invalid password)`,
        details: { userId: user.id },
        userId: user.id,
        organizationId: user.organizationId,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }, { transaction });

      await transaction.commit();
      throw new AuthenticationError('Invalid email or password');
    }

    // Update last login
    await user.update({ lastLoginAt: new Date() }, { transaction });

    // Log successful login
    await AuditLog.create({
      type: 'LOGIN_SUCCESS',
      description: `User logged in: ${email}`,
      details: { userId: user.id },
      userId: user.id,
      organizationId: user.organizationId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    }, { transaction });

    // Commit transaction
    await transaction.commit();

    // Generate tokens (after successful commit)
    const tokens = generateTokens(user);

    logger.info(`User logged in: ${email}`);

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        organizationId: user.organizationId,
        emailVerified: user.emailVerified,
        lastLoginAt: user.lastLoginAt
      },
      organization: user.organization ? {
        id: user.organization.id,
        name: user.organization.name,
        status: user.organization.status,
        verificationStatus: user.organization.verificationStatus
      } : null,
      tokens
    });

  } catch (error) {
    // Rollback transaction on any error
    await transaction.rollback();
    throw error;
  }
}));

/**
 * @route POST /api/auth/refresh
 * @desc Refresh access token
 * @access Public
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  // Validate request body
  const { error, value } = refreshTokenSchema.validate(req.body);
  if (error) {
    throw new ValidationError('Validation failed', error.details);
  }

  const { refreshToken } = value;

  // Verify refresh token
  const decoded = verifyRefreshToken(refreshToken);

  // Find user
  const user = await User.findByPk(decoded.id, {
    include: [
      {
        model: Organization,
        as: 'organization',
        attributes: ['id', 'name', 'status', 'verificationStatus']
      }
    ]
  });

  if (!user) {
    throw new AuthenticationError('User not found');
  }

  // Generate new tokens
  const tokens = generateTokens(user);

  res.json({
    message: 'Token refreshed successfully',
    tokens
  });
}));

/**
 * @route POST /api/auth/logout
 * @desc Logout user
 * @access Private
 */
router.post('/logout', authenticate, asyncHandler(async (req, res) => {
  // Use transaction for logout operations
  const transaction = await sequelize.transaction();

  try {
    // Log logout
    await AuditLog.create({
      type: 'LOGOUT',
      description: `User logged out: ${req.user.email}`,
      details: { userId: req.user.id },
      userId: req.user.id,
      organizationId: req.user.organizationId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    }, { transaction });

    // Commit transaction
    await transaction.commit();

    logger.info(`User logged out: ${req.user.email}`);

    res.json({
      message: 'Logout successful'
    });

  } catch (error) {
    // Rollback transaction on any error
    await transaction.rollback();
    throw error;
  }
}));

/**
 * @route GET /api/auth/me
 * @desc Get current user
 * @access Private
 */
router.get('/me', authenticate, asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    include: [
      {
        model: Organization,
        as: 'organization',
        attributes: ['id', 'name', 'status', 'verificationStatus', 'logo', 'website']
      }
    ]
  });

  res.json({
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      organizationId: user.organizationId,
      emailVerified: user.emailVerified,
      jobTitle: user.jobTitle,
      departmentName: user.departmentName,
      phoneNumber: user.phoneNumber,
      profileImage: user.profileImage,
      bio: user.bio,
      lastLoginAt: user.lastLoginAt,
      twoFactorEnabled: user.twoFactorEnabled,
      preferences: user.preferences,
      createdAt: user.createdAt
    },
    organization: user.organization
  });
}));

module.exports = router;
