#!/usr/bin/env node

require('dotenv').config();
const { sequelize } = require('./src/models');
const logger = require('./src/utils/logger');

/**
 * Sync database using Sequelize .sync()
 * This creates/updates tables based on model definitions
 */
async function syncDatabase(options = {}) {
  const {
    force = false,      // Drop tables if they exist
    alter = false,      // Alter tables to match models
    logging = true      // Enable logging
  } = options;

  console.log('🗄️  CarbonX Database Sync');
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Database: ${process.env.DB_NAME || 'carbonix'}`);
  
  if (force) {
    console.log('⚠️  WARNING: force=true will DROP ALL TABLES!');
  }
  
  if (alter) {
    console.log('🔧 alter=true will modify existing tables to match models');
  }

  try {
    console.log('\n🔄 Starting database sync...');
    
    // Test connection first
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Sync all models
    await sequelize.sync({ 
      force, 
      alter, 
      logging: logging ? (sql) => logger.debug(sql) : false 
    });

    console.log('✅ Database sync completed successfully');
    
    // Show table information
    const queryInterface = sequelize.getQueryInterface();
    const tables = await queryInterface.showAllTables();
    
    console.log(`\n📋 Tables created/updated: ${tables.length}`);
    tables.forEach(table => console.log(`  - ${table}`));

    return true;

  } catch (error) {
    console.error('❌ Database sync failed:', error.message);
    logger.error('Database sync error:', error);
    return false;
  } finally {
    await sequelize.close();
  }
}

/**
 * Safe sync - only creates missing tables, doesn't alter existing ones
 */
async function safeSyncDatabase() {
  console.log('\n🛡️  Running SAFE database sync...');
  return await syncDatabase({ force: false, alter: false });
}

/**
 * Force sync - drops and recreates all tables (DANGEROUS!)
 */
async function forceSyncDatabase() {
  console.log('\n💥 Running FORCE database sync (will drop all tables)...');
  
  // Add confirmation in production
  if (process.env.NODE_ENV === 'production') {
    console.error('❌ Force sync is disabled in production for safety');
    return false;
  }
  
  return await syncDatabase({ force: true, alter: false });
}

/**
 * Alter sync - modifies existing tables to match models
 */
async function alterSyncDatabase() {
  console.log('\n🔧 Running ALTER database sync...');
  return await syncDatabase({ force: false, alter: true });
}

/**
 * Development sync - safe sync with detailed logging
 */
async function devSyncDatabase() {
  console.log('\n🚀 Running DEVELOPMENT database sync...');
  return await syncDatabase({ 
    force: false, 
    alter: process.env.NODE_ENV === 'development', 
    logging: true 
  });
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
🗄️  Database Sync Manager

Usage: node sync-database.js <command>

Commands:
  safe                Safe sync - only creates missing tables
  force               Force sync - drops and recreates all tables (DANGEROUS!)
  alter               Alter sync - modifies existing tables to match models
  dev                 Development sync - safe with auto-alter in dev mode
  help                Show this help message

Examples:
  node sync-database.js safe
  node sync-database.js alter
  node sync-database.js dev

⚠️  Important Notes:
  - 'force' will DELETE ALL DATA in your tables
  - 'alter' may cause data loss if column types change
  - Use 'safe' for production environments
  - Use 'dev' for development with auto-schema updates

Environment Variables:
  NODE_ENV            Environment (development, test, production)
  DB_HOST             Database host
  DB_PORT             Database port
  DB_NAME             Database name
  DB_USER             Database username
  DB_PASSWORD         Database password
`);
}

/**
 * Main function to handle command line arguments
 */
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'safe':
      await safeSyncDatabase();
      break;
      
    case 'force':
      await forceSyncDatabase();
      break;
      
    case 'alter':
      await alterSyncDatabase();
      break;
      
    case 'dev':
      await devSyncDatabase();
      break;
      
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
      
    default:
      if (!command) {
        // Default to dev sync if no command provided
        await devSyncDatabase();
      } else {
        console.error(`❌ Unknown command: ${command}`);
        showHelp();
        process.exit(1);
      }
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Sync script failed:', error);
    process.exit(1);
  });
}

module.exports = {
  syncDatabase,
  safeSyncDatabase,
  forceSyncDatabase,
  alterSyncDatabase,
  devSyncDatabase
};
