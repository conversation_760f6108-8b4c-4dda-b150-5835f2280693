services:
  app-dev:
    build:
      context: .
      target: deps
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=**********************************/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=root
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=2825c02c6e162bb89551c9fae64e2395cd08d7806bf28e535ededc833beee3f1
      - EMAIL_SERVER=smtp.example.com
      - EMAIL_FROM=<EMAIL>
      - SMTP_HOST=smtp.example.com
      - SMTP_PORT=587
      - SMTP_USER=<EMAIL>
      - SMTP_PASSWORD=your-smtp-password
      - SMTP_FROM=<EMAIL>
      - ALCHEMY_API_KEY=your-alchemy-api-key
      - ALCHEMY_NETWORK=eth-sepolia
      - ETHEREUM_NETWORK=sepolia
      - POLYGON_NETWORK=mumbai
      - OPTIMISM_NETWORK=optimism-sepolia
      - ARBITRUM_NETWORK=arbitrum-sepolia
      - BASE_NETWORK=base-sepolia
      - NODE_ENV=development
      - WALLET_ENCRYPTION_KEY=dev-wallet-encryption-key
    volumes:
      - ./:/app
      - /app/.next
    command: /app/scripts/start-dev.sh

  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=**********************************/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-root}
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-2825c02c6e162bb89551c9fae64e2395cd08d7806bf28e535ededc833beee3f1}
      - EMAIL_SERVER=${EMAIL_SERVER:-smtp.example.com}
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}
      - SMTP_HOST=${SMTP_HOST:-smtp.example.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-<EMAIL>}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-your-smtp-password}
      - SMTP_FROM=${SMTP_FROM:-<EMAIL>}
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY:-your-alchemy-api-key}
      - ALCHEMY_NETWORK=${ALCHEMY_NETWORK:-eth-sepolia}
      - ETHEREUM_NETWORK=${ETHEREUM_NETWORK:-mainnet}
      - POLYGON_NETWORK=${POLYGON_NETWORK:-polygon}
      - OPTIMISM_NETWORK=${OPTIMISM_NETWORK:-optimism}
      - ARBITRUM_NETWORK=${ARBITRUM_NETWORK:-arbitrum}
      - BASE_NETWORK=${BASE_NETWORK:-base}
      - NODE_ENV=production
      - WALLET_ENCRYPTION_KEY=${WALLET_ENCRYPTION_KEY:-prod-wallet-encryption-key}
    volumes:
      - uploads_data:/app/public/uploads
    command: /app/scripts/start.sh

  db-init:
    build:
      context: .
      target: deps
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=**********************************/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=root
      - NODE_ENV=development
    volumes:
      - ./:/app
      - /app/node_modules
    command: /bin/sh -c "cd /app && chmod +x /app/scripts/docker-init-db.sh && /app/scripts/docker-init-db.sh"

  db:
    image: postgres:16
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=root
      - POSTGRES_DB=carbon_exchange
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  uploads_data:

networks:
  app_network:
    driver: bridge
