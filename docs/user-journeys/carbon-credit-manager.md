# Carbon Credit Manager User Journey

This document outlines the key journeys for Carbon Credit Managers on the Carbonix platform.

## Role Overview

The Carbon Credit Manager is responsible for managing projects that generate carbon credits, listing and managing those credits on the platform, and facilitating their tokenization and trading on the marketplace. This role typically belongs to sustainability officers, environmental specialists, or dedicated carbon asset managers within the enterprise.

## Permissions

Carbon Credit Managers have the following key permissions:
- Create, read, update, and delete projects and carbon credits
- Manage project portfolios across the organization
- List and unlist carbon credits on the marketplace
- Tokenize carbon credits on the blockchain
- Verify carbon credit documentation
- View carbon credit analytics and history

## 1. Project and Carbon Credit Management Journey

The journey of managing projects that generate carbon credits and adding those credits to the platform.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Projects]
    Navigate --> ProjectAction{Project Action}

    ProjectAction -->|Create New Project| CreateProject[Create New Project]
    ProjectAction -->|Manage Existing Project| SelectProject[Select Existing Project]

    CreateProject --> ProjectDetails[Enter Project Details]
    ProjectDetails --> ProjectLocation[Enter Project Location]
    ProjectLocation --> ProjectMethodology[Select Project Methodology]
    ProjectMethodology --> ProjectStandard[Select Verification Standard]
    ProjectStandard --> UploadProjectDocs[Upload Project Documents]
    UploadProjectDocs --> SaveProject[Save Project]

    SelectProject --> ViewProject[View Project Details]
    ViewProject --> ProjectOptions{Project Options}

    ProjectOptions -->|Edit Project| EditProject[Edit Project Details]
    ProjectOptions -->|Generate Credits| GenerateCredits[Generate Carbon Credits]
    ProjectOptions -->|View Credits| ViewProjectCredits[View Project Credits]

    EditProject --> SaveProject

    GenerateCredits --> CreditDetails[Enter Credit Details]
    CreditDetails --> VintageYear[Select Vintage Year]
    VintageYear --> CreditQuantity[Enter Credit Quantity]
    CreditQuantity --> SerialNumbers[Enter Serial Numbers]
    SerialNumbers --> UploadCreditDocs[Upload Credit Documents]
    UploadCreditDocs --> VerificationStatus{Verification Status}

    VerificationStatus -->|Self-Verified| SetPricing[Set Pricing Information]
    VerificationStatus -->|Needs Verification| SubmitVerification[Submit for Verification]

    SubmitVerification --> WaitVerification{Verification Status}
    WaitVerification -->|Approved| SetPricing
    WaitVerification -->|Rejected| ReviseInfo[Revise Information]
    ReviseInfo --> SubmitVerification

    SetPricing --> ListingOptions{Listing Options}
    ListingOptions -->|List on Marketplace| ListMarketplace[List on Marketplace]
    ListingOptions -->|Save for Later| SaveCredits[Save Credits]

    ListMarketplace --> MarketplaceSettings[Configure Marketplace Settings]
    MarketplaceSettings --> PreviewListing[Preview Listing]
    PreviewListing --> ConfirmListing[Confirm Listing]
    ConfirmListing --> Listed[Listed on Marketplace]

    SaveCredits --> Complete([Management Complete])
    Listed --> TokenizeOption{Tokenize Now?}
    TokenizeOption -->|Yes| InitiateTokenization[Initiate Tokenization]
    TokenizeOption -->|No| Complete

    InitiateTokenization --> Complete
    SaveProject --> Complete
    ViewProjectCredits --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style ProjectAction fill:#FFC107,stroke:#FFA000,color:black
    style ProjectOptions fill:#FFC107,stroke:#FFA000,color:black
    style VerificationStatus fill:#FFC107,stroke:#FFA000,color:black
    style WaitVerification fill:#FFC107,stroke:#FFA000,color:black
    style ListingOptions fill:#FFC107,stroke:#FFA000,color:black
    style TokenizeOption fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Projects**: Access the projects section
2. **Project Action**: Choose to create a new project or manage an existing one
3. **Create New Project Flow**:
   - Enter project details (name, description, etc.)
   - Enter project location information
   - Select project methodology
   - Select verification standard (e.g., Verra, Gold Standard)
   - Upload project documents
   - Save project
4. **Manage Existing Project Flow**:
   - Select a project from the organization's portfolio
   - View project details
   - Choose project options:
     - Edit project details
     - Generate carbon credits from the project
     - View existing credits associated with the project
5. **Generate Carbon Credits Flow**:
   - Enter credit details
   - Select vintage year
   - Enter credit quantity
   - Enter serial numbers
   - Upload credit documentation
   - Choose verification path:
     - Self-verified credits (for internal use)
     - Submit for third-party verification
6. **Verification Process** (if needed):
   - Submit for verification
   - Wait for verification status
   - Address any rejection reasons if necessary
7. **Set Pricing Information**: Define pricing strategy and parameters
8. **Listing Options**:
   - List on marketplace immediately
   - Save credits for later listing
9. **Marketplace Listing Flow** (if listing):
   - Configure marketplace settings
   - Preview listing
   - Confirm listing
   - Credits become available for trading
10. **Tokenization Option**: Choose whether to tokenize the credits now or later

## 2. Carbon Credit Tokenization Journey

The process of converting traditional carbon credits into blockchain tokens.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Carbon Credits]
    Navigate --> SelectCredit[Select Carbon Credit]
    SelectCredit --> ViewDetails[View Credit Details]
    ViewDetails --> InitiateTokenize[Initiate Tokenization]
    InitiateTokenize --> SelectNetwork[Select Blockchain Network]

    SelectNetwork --> NetworkType{Network Type}
    NetworkType -->|Mainnet| ConfirmMainnet[Confirm Mainnet Selection]
    NetworkType -->|Testnet| SelectTestnet[Select Testnet]

    ConfirmMainnet --> WalletSelection[Select Destination Wallet]
    SelectTestnet --> WalletSelection

    WalletSelection --> ReviewDetails[Review Tokenization Details]
    ReviewDetails --> ConfirmTokenize[Confirm Tokenization]
    ConfirmTokenize --> ProcessingStatus{Processing Status}

    ProcessingStatus -->|Success| TokenCreated[Token Created Successfully]
    ProcessingStatus -->|Failed| ErrorHandling[View Error Details]

    ErrorHandling --> RetryOptions{Retry Options}
    RetryOptions -->|Adjust Parameters| SelectNetwork
    RetryOptions -->|Cancel| Cancelled[Tokenization Cancelled]

    TokenCreated --> ViewToken[View Token Details]
    ViewToken --> ManageToken[Manage Token]
    ManageToken --> Complete([Tokenization Complete])
    Cancelled --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style NetworkType fill:#FFC107,stroke:#FFA000,color:black
    style ProcessingStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryOptions fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Carbon Credits**: Access the carbon credits section
2. **Select Carbon Credit**: Choose the credit to tokenize
3. **View Credit Details**: Review credit information
4. **Initiate Tokenization**: Start the tokenization process
5. **Select Blockchain Network**: Choose the network (Ethereum, Polygon, etc.)
6. **Network Type**: Select between mainnet (production) or testnet (testing)
7. **Select Destination Wallet**: Choose where the tokens will be stored
8. **Review Tokenization Details**: Confirm all parameters
9. **Confirm Tokenization**: Approve the transaction
10. **Processing Status**: Monitor the tokenization process
    - If successful, view and manage the created token
    - If failed, review error details and retry with adjusted parameters

## 3. Marketplace and Trading Journey

The process of managing carbon credit listings on the marketplace and facilitating trading.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Marketplace]
    Navigate --> ViewOptions{View Options}

    ViewOptions -->|My Listings| MyListings[View My Listings]
    ViewOptions -->|All Listings| AllListings[View All Listings]
    ViewOptions -->|Market Analytics| MarketAnalytics[View Market Analytics]

    MyListings --> FilterMyListings[Filter My Listings]
    FilterMyListings --> SelectListing[Select Listing]

    AllListings --> FilterAllListings[Filter All Listings]
    FilterAllListings --> BrowseListings[Browse Listings]
    BrowseListings --> ViewListingDetails[View Listing Details]

    SelectListing --> ListingAction{Listing Action}

    ListingAction -->|View Details| ViewDetails[View Detailed Information]
    ListingAction -->|Update Price| UpdatePrice[Update Pricing]
    ListingAction -->|Update Status| UpdateStatus[Change Listing Status]
    ListingAction -->|View Orders| ViewOrders[View Associated Orders]
    ListingAction -->|View History| ViewHistory[View Listing History]
    ListingAction -->|Delist| DelistCredit[Delist from Marketplace]

    UpdatePrice --> PriceStrategy{Price Strategy}
    PriceStrategy -->|Fixed Price| SetFixedPrice[Set Fixed Price]
    PriceStrategy -->|Auction| ConfigureAuction[Configure Auction Parameters]
    PriceStrategy -->|Dynamic Pricing| SetDynamicRules[Set Dynamic Pricing Rules]

    SetFixedPrice --> SavePriceChanges[Save Price Changes]
    ConfigureAuction --> SavePriceChanges
    SetDynamicRules --> SavePriceChanges

    SavePriceChanges --> RecordPriceHistory[Record Price History]
    UpdateStatus --> NotifyBuyers[Notify Potential Buyers]
    DelistCredit --> ConfirmDelist[Confirm Delisting]

    ViewOrders --> OrderAction{Order Action}
    OrderAction -->|Accept Order| AcceptOrder[Accept Buy Order]
    OrderAction -->|Reject Order| RejectOrder[Reject Buy Order]
    OrderAction -->|Counter Offer| CounterOffer[Make Counter Offer]

    AcceptOrder --> ProcessTransaction[Process Transaction]
    RejectOrder --> ProvideReason[Provide Rejection Reason]
    CounterOffer --> SendCounterOffer[Send Counter Offer]

    ProcessTransaction --> TransactionComplete[Transaction Complete]
    ProvideReason --> NotifyBuyer[Notify Buyer]
    SendCounterOffer --> WaitResponse[Wait for Response]

    ViewDetails --> Complete([Management Complete])
    RecordPriceHistory --> Complete
    NotifyBuyers --> Complete
    ConfirmDelist --> Complete
    ViewHistory --> Complete
    TransactionComplete --> Complete
    NotifyBuyer --> Complete
    WaitResponse --> Complete
    MarketAnalytics --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style ViewOptions fill:#FFC107,stroke:#FFA000,color:black
    style ListingAction fill:#FFC107,stroke:#FFA000,color:black
    style PriceStrategy fill:#FFC107,stroke:#FFA000,color:black
    style OrderAction fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Marketplace**: Access the marketplace section
2. **View Options**: Choose what to view in the marketplace
   - My Listings: View organization's own listings
   - All Listings: View all marketplace listings
   - Market Analytics: View market trends and analytics
3. **Filter Listings**: Narrow down by project type, vintage, price, etc.
4. **Select Listing**: Choose a specific listing to manage (for own listings)
5. **Listing Action**: Choose from various management options:
   - View Details: See all listing information
   - Update Price: Change the pricing strategy
   - Update Status: Change listing status (active, paused, etc.)
   - View Orders: See buy orders for this listing
   - View History: See the listing's history (price changes, transactions, etc.)
   - Delist: Remove from marketplace
6. **Price Strategy Options**:
   - Fixed Price: Set a specific price per credit
   - Auction: Configure auction parameters
   - Dynamic Pricing: Set rules for automatic price adjustments
7. **Order Management**:
   - Accept Order: Approve a buy order
   - Reject Order: Decline a buy order
   - Counter Offer: Propose alternative terms
8. **Transaction Processing**: Complete the sale process
9. **Notification**: Inform relevant parties of changes

## 4. Project and Credit Portfolio Analytics Journey

Analyzing the performance and impact of projects and carbon credits across the organization's portfolio.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Portfolio Analytics]
    Navigate --> ViewDashboard[View Analytics Dashboard]
    ViewDashboard --> AnalyticsType{Analytics Type}

    AnalyticsType -->|Project Analytics| ProjectAnalytics[View Project Analytics]
    AnalyticsType -->|Credit Analytics| CreditAnalytics[View Credit Analytics]
    AnalyticsType -->|Market Analytics| MarketAnalytics[View Market Analytics]
    AnalyticsType -->|Impact Analytics| ImpactAnalytics[View Impact Analytics]

    ProjectAnalytics --> ProjectMetrics{Project Metrics}
    ProjectMetrics -->|Performance| ProjectPerformance[View Project Performance]
    ProjectMetrics -->|Generation Rate| GenerationRate[View Credit Generation Rate]
    ProjectMetrics -->|Comparison| ProjectComparison[Compare Projects]

    CreditAnalytics --> CreditMetrics{Credit Metrics}
    CreditMetrics -->|Trading Volume| TradingVolume[View Trading Volume]
    CreditMetrics -->|Price Trends| PriceTrends[View Price Trends]
    CreditMetrics -->|Vintage Analysis| VintageAnalysis[Analyze by Vintage]
    CreditMetrics -->|Standard Analysis| StandardAnalysis[Analyze by Standard]

    MarketAnalytics --> MarketMetrics{Market Metrics}
    MarketMetrics -->|Demand Trends| DemandTrends[View Demand Trends]
    MarketMetrics -->|Buyer Demographics| Demographics[View Buyer Demographics]
    MarketMetrics -->|Competitor Analysis| CompetitorAnalysis[View Competitor Analysis]
    MarketMetrics -->|Market Share| MarketShare[View Market Share]

    ImpactAnalytics --> ImpactMetrics{Impact Metrics}
    ImpactMetrics -->|Emissions Reduction| EmissionsReduction[View Emissions Reduction]
    ImpactMetrics -->|SDG Impact| SDGImpact[View SDG Impact]
    ImpactMetrics -->|Geographic Impact| GeographicImpact[View Geographic Impact]

    ProjectPerformance --> ApplyFilters[Apply Filters & Date Range]
    GenerationRate --> ApplyFilters
    ProjectComparison --> ApplyFilters
    TradingVolume --> ApplyFilters
    PriceTrends --> ApplyFilters
    VintageAnalysis --> ApplyFilters
    StandardAnalysis --> ApplyFilters
    DemandTrends --> ApplyFilters
    Demographics --> ApplyFilters
    CompetitorAnalysis --> ApplyFilters
    MarketShare --> ApplyFilters
    EmissionsReduction --> ApplyFilters
    SDGImpact --> ApplyFilters
    GeographicImpact --> ApplyFilters

    ApplyFilters --> ViewDetailed[View Detailed Analysis]
    ViewDetailed --> VisualizationOptions{Visualization Options}

    VisualizationOptions -->|Charts| ViewCharts[View as Charts]
    VisualizationOptions -->|Tables| ViewTables[View as Tables]
    VisualizationOptions -->|Maps| ViewMaps[View as Maps]
    VisualizationOptions -->|Dashboard| ViewDashboard[View as Dashboard]

    ViewCharts --> ExportOption{Export Data?}
    ViewTables --> ExportOption
    ViewMaps --> ExportOption
    ViewDashboard --> ExportOption

    ExportOption -->|Yes| ExportFormat[Select Export Format]
    ExportOption -->|No| Complete([Analysis Complete])

    ExportFormat --> DownloadReport[Download Report]
    DownloadReport --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style AnalyticsType fill:#FFC107,stroke:#FFA000,color:black
    style ProjectMetrics fill:#FFC107,stroke:#FFA000,color:black
    style CreditMetrics fill:#FFC107,stroke:#FFA000,color:black
    style MarketMetrics fill:#FFC107,stroke:#FFA000,color:black
    style ImpactMetrics fill:#FFC107,stroke:#FFA000,color:black
    style VisualizationOptions fill:#FFC107,stroke:#FFA000,color:black
    style ExportOption fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Portfolio Analytics**: Access the analytics section
2. **View Analytics Dashboard**: See high-level metrics across the portfolio
3. **Select Analytics Type**: Choose a specific area to explore:
   - Project Analytics: Analyze project performance and generation
   - Credit Analytics: Analyze credit performance and trends
   - Market Analytics: Analyze market dynamics and competition
   - Impact Analytics: Analyze environmental and social impact
4. **Select Specific Metrics**: Choose detailed metrics within each category:
   - Project Metrics: Performance, generation rate, comparisons
   - Credit Metrics: Trading volume, price trends, vintage analysis
   - Market Metrics: Demand trends, buyer demographics, competition
   - Impact Metrics: Emissions reduction, SDG impact, geographic impact
5. **Apply Filters**: Narrow down by date range, project type, vintage, etc.
6. **View Detailed Analysis**: Explore specific data points and trends
7. **Visualization Options**: Choose how to view the data (charts, tables, maps, dashboards)
8. **Export Data (Optional)**: Download reports for further analysis or sharing

## 5. Credit Retirement and Impact Reporting Journey

The process of retiring carbon credits and reporting on environmental impact.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Carbon Credits]
    Navigate --> ViewCredits[View Available Credits]
    ViewCredits --> SelectCredits[Select Credits to Retire]
    SelectCredits --> InitiateRetirement[Initiate Retirement Process]

    InitiateRetirement --> RetirementPurpose{Retirement Purpose}
    RetirementPurpose -->|Corporate Offsetting| CorporateOffset[Corporate Carbon Offsetting]
    RetirementPurpose -->|Client Offsetting| ClientOffset[Client Carbon Offsetting]
    RetirementPurpose -->|Product Offsetting| ProductOffset[Product Carbon Offsetting]
    RetirementPurpose -->|Event Offsetting| EventOffset[Event Carbon Offsetting]

    CorporateOffset --> EnterDetails[Enter Retirement Details]
    ClientOffset --> EnterClientInfo[Enter Client Information]
    ProductOffset --> EnterProductInfo[Enter Product Information]
    EventOffset --> EnterEventInfo[Enter Event Information]

    EnterClientInfo --> EnterDetails
    EnterProductInfo --> EnterDetails
    EnterEventInfo --> EnterDetails

    EnterDetails --> SpecifyQuantity[Specify Quantity to Retire]
    SpecifyQuantity --> EnterBeneficiary[Enter Beneficiary Information]
    EnterBeneficiary --> ReviewDetails[Review Retirement Details]
    ReviewDetails --> ConfirmRetirement[Confirm Retirement]

    ConfirmRetirement --> ProcessingStatus{Processing Status}
    ProcessingStatus -->|Success| RetirementComplete[Retirement Complete]
    ProcessingStatus -->|Failed| ErrorHandling[View Error Details]

    ErrorHandling --> RetryOptions{Retry Options}
    RetryOptions -->|Adjust Parameters| EnterDetails
    RetryOptions -->|Cancel| Cancelled[Retirement Cancelled]

    RetirementComplete --> ViewCertificate[View Retirement Certificate]
    ViewCertificate --> GenerateReport[Generate Impact Report]

    GenerateReport --> ReportType{Report Type}
    ReportType -->|Basic Report| BasicReport[Generate Basic Report]
    ReportType -->|Detailed Report| DetailedReport[Generate Detailed Report]
    ReportType -->|Custom Report| CustomReport[Configure Custom Report]

    BasicReport --> ShareOptions{Share Options}
    DetailedReport --> ShareOptions
    CustomReport --> ShareOptions

    ShareOptions -->|Download| DownloadReport[Download Report]
    ShareOptions -->|Email| EmailReport[Email Report]
    ShareOptions -->|Generate URL| GenerateURL[Generate Shareable URL]

    DownloadReport --> Complete([Process Complete])
    EmailReport --> Complete
    GenerateURL --> Complete
    Cancelled --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style RetirementPurpose fill:#FFC107,stroke:#FFA000,color:black
    style ProcessingStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryOptions fill:#FFC107,stroke:#FFA000,color:black
    style ReportType fill:#FFC107,stroke:#FFA000,color:black
    style ShareOptions fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Carbon Credits**: Access the carbon credits section
2. **View Available Credits**: See credits available for retirement
3. **Select Credits to Retire**: Choose specific credits for retirement
4. **Initiate Retirement Process**: Start the retirement workflow
5. **Select Retirement Purpose**:
   - Corporate Offsetting: For the organization's own carbon footprint
   - Client Offsetting: On behalf of a client
   - Product Offsetting: For a specific product
   - Event Offsetting: For a specific event
6. **Enter Retirement Details**: Provide necessary information based on purpose
7. **Specify Quantity**: Enter the amount of credits to retire
8. **Enter Beneficiary**: Specify who benefits from the retirement
9. **Review Retirement Details**: Verify all information
10. **Confirm Retirement**: Approve the retirement transaction
11. **Processing Status**: Monitor the retirement process
12. **View Retirement Certificate**: Access the official certificate
13. **Generate Impact Report**: Create a report showing environmental impact
14. **Select Report Type**: Choose basic, detailed, or custom report
15. **Share Report**: Download, email, or generate a shareable URL

## Success Metrics

The Carbon Credit Manager journey is successful when:

1. Projects are properly documented and managed in the system
2. Carbon credits are accurately generated from projects with complete information
3. Credits are successfully listed and traded on the marketplace
4. Tokenization processes complete successfully
5. Credits are properly managed throughout their lifecycle
6. Retirement processes are completed correctly with proper documentation
7. Analytics provide actionable insights for project management, pricing, and strategy
8. Environmental impact is clearly tracked and reported

## Common Challenges and Solutions

| Challenge | Solution |
|-----------|----------|
| Complex project documentation | Project templates and documentation guides |
| Verification requirements | Document templates and verification checklists |
| Managing multiple projects | Project portfolio management tools |
| Tokenization failures | Network status indicators and guided troubleshooting |
| Price optimization | Market analytics and competitive pricing tools |
| Managing multiple credit types | Batch operations and filtering tools |
| Tracking environmental impact | Standardized impact calculation methods |
| Marketplace competition | Market intelligence and pricing strategy tools |
