# Wallet Manager User Journey

This document outlines the key journeys for Wallet Managers on the Carbonix platform.

## Role Overview

The Wallet Manager is responsible for creating, managing, and monitoring blockchain wallets for the organization, supporting project-based carbon credit tokenization, and facilitating marketplace trading. This role typically belongs to finance team members, blockchain specialists, or dedicated treasury managers within the enterprise.

## Permissions

Wallet Managers have the following key permissions:
- Create, read, update, and delete wallets
- Configure project-specific wallet settings
- Transfer tokens between wallets
- Tokenize carbon credits from projects
- Facilitate marketplace transactions
- View transaction history
- Monitor wallet balances and activity
- Manage wallet security settings
- Set up trading automation rules

## 1. Wallet Creation Journey

The process of setting up new blockchain wallets for the organization.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Wallet Management]
    Navigate --> CreateNew[Create New Wallet]
    CreateNew --> SelectType{Select Wallet Type}

    SelectType -->|EOA Wallet| EOAWallet[Create EOA Wallet]
    SelectType -->|Smart Wallet| SmartWallet[Create Smart Wallet]

    EOAWallet --> SelectNetwork[Select Blockchain Network]
    SmartWallet --> SelectNetwork

    SelectNetwork --> NetworkType{Network Type}
    NetworkType -->|Mainnet| ConfirmMainnet[Confirm Mainnet Selection]
    NetworkType -->|Testnet| SelectTestnet[Select Testnet]

    ConfirmMainnet --> SecuritySetup[Configure Security Settings]
    SelectTestnet --> SecuritySetup

    SecuritySetup --> BackupOptions[Set Backup & Recovery Options]
    BackupOptions --> ReviewDetails[Review Wallet Details]
    ReviewDetails --> CreateWallet[Create Wallet]
    CreateWallet --> ProcessingStatus{Processing Status}

    ProcessingStatus -->|Success| WalletCreated[Wallet Created Successfully]
    ProcessingStatus -->|Failed| ErrorHandling[View Error Details]

    ErrorHandling --> RetryOptions{Retry Options}
    RetryOptions -->|Adjust Parameters| SelectNetwork
    RetryOptions -->|Cancel| Cancelled[Creation Cancelled]

    WalletCreated --> SecureBackup[Secure Backup Information]
    SecureBackup --> LabelWallet[Label Wallet for Organization Use]
    LabelWallet --> AssignPermissions[Assign User Permissions]
    AssignPermissions --> Complete([Wallet Setup Complete])
    Cancelled --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style SelectType fill:#FFC107,stroke:#FFA000,color:black
    style NetworkType fill:#FFC107,stroke:#FFA000,color:black
    style ProcessingStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryOptions fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Wallet Management**: Access the wallet section
2. **Create New Wallet**: Initiate the wallet creation process
3. **Select Wallet Type**:
   - EOA Wallet: Standard Externally Owned Account
   - Smart Wallet: Contract-based wallet with advanced features
4. **Select Blockchain Network**: Choose the network (Ethereum, Polygon, etc.)
5. **Network Type**: Select between mainnet (production) or testnet (testing)
6. **Configure Security Settings**: Set up security parameters
7. **Set Backup & Recovery Options**: Configure recovery methods
8. **Review Wallet Details**: Confirm all parameters
9. **Create Wallet**: Generate the new wallet
10. **Processing Status**: Monitor the creation process
    - If successful, proceed with setup
    - If failed, review error details and retry
11. **Secure Backup Information**: Safely store recovery information
12. **Label Wallet**: Add descriptive information for organization use
13. **Assign User Permissions**: Configure who can access the wallet

## 2. Project-Based Token Management Journey

Managing tokens and transactions within the organization's wallets, with project context.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Wallet Dashboard]
    Navigate --> WalletView{Wallet View}

    WalletView -->|Project View| ProjectWallets[View Project Wallets]
    WalletView -->|Standard View| StandardWallets[View All Wallets]

    ProjectWallets --> SelectProject[Select Project]
    SelectProject --> ViewProjectWallets[View Project-Specific Wallets]
    ViewProjectWallets --> SelectProjectWallet[Select Project Wallet]

    StandardWallets --> SelectWallet[Select Wallet]
    SelectProjectWallet --> ViewBalance[View Wallet Balance]
    SelectWallet --> ViewBalance

    ViewBalance --> TokenContext{Token Context}
    TokenContext -->|Project Tokens| ViewProjectTokens[View Project Tokens]
    TokenContext -->|All Tokens| ViewAllTokens[View All Tokens]

    ViewProjectTokens --> Action{Select Action}
    ViewAllTokens --> Action

    Action -->|Send Tokens| InitiateSend[Initiate Send Transaction]
    Action -->|Receive Tokens| GenerateReceive[Generate Receive Address/QR]
    Action -->|View Portfolio| ViewPortfolio[View Token Portfolio]
    Action -->|View NFTs| ViewNFTs[View NFT Collection]
    Action -->|View History| ViewHistory[View Transaction History]
    Action -->|Marketplace Actions| MarketplaceActions[Marketplace Actions]

    MarketplaceActions --> MarketplaceAction{Marketplace Action}
    MarketplaceAction -->|List Tokens| ListTokens[List Tokens on Marketplace]
    MarketplaceAction -->|View Orders| ViewOrders[View Buy/Sell Orders]
    MarketplaceAction -->|Process Order| ProcessOrder[Process Marketplace Order]

    ListTokens --> ConfigureListing[Configure Listing Parameters]
    ConfigureListing --> ConfirmListing[Confirm Listing]
    ConfirmListing --> ListingComplete[Listing Complete]

    ViewOrders --> FilterOrders[Filter Orders]
    FilterOrders --> SelectOrder[Select Order]
    SelectOrder --> OrderAction{Order Action}
    OrderAction -->|Accept| AcceptOrder[Accept Order]
    OrderAction -->|Reject| RejectOrder[Reject Order]
    OrderAction -->|Counter| CounterOffer[Make Counter Offer]

    AcceptOrder --> ProcessTransaction[Process Transaction]
    RejectOrder --> NotifyBuyer[Notify Buyer]
    CounterOffer --> WaitResponse[Wait for Response]

    InitiateSend --> TokenType{Token Type}
    TokenType -->|Project Token| SelectProjectToken[Select Project Token]
    TokenType -->|Other Token| SelectOtherToken[Select Other Token]

    SelectProjectToken --> EnterDetails[Enter Recipient & Amount]
    SelectOtherToken --> EnterDetails
    EnterDetails --> ReviewFees[Review Gas Fees]
    ReviewFees --> ConfirmSend[Confirm Transaction]
    ConfirmSend --> ProcessingStatus{Processing Status}

    ProcessingStatus -->|Success| TransactionComplete[Transaction Complete]
    ProcessingStatus -->|Failed| ErrorHandling[View Error Details]

    ErrorHandling --> RetryOptions{Retry Options}
    RetryOptions -->|Adjust Parameters| ReviewFees
    RetryOptions -->|Cancel| Cancelled[Transaction Cancelled]

    GenerateReceive --> ShareAddress[Share Address Information]
    ViewPortfolio --> FilterTokens[Filter & Sort Tokens]
    ViewNFTs --> NFTDetails[View NFT Details]
    ViewHistory --> FilterHistory[Filter Transaction History]

    TransactionComplete --> RecordTransaction[Record in Transaction Log]
    ShareAddress --> WaitForFunds[Wait for Incoming Funds]
    FilterTokens --> TokenDetails[View Token Details]
    NFTDetails --> Complete([Management Complete])
    FilterHistory --> ExportOption{Export History?}

    ProcessTransaction --> TransactionComplete
    NotifyBuyer --> Complete
    WaitResponse --> Complete
    ListingComplete --> Complete

    RecordTransaction --> Complete
    WaitForFunds --> FundsReceived[Funds Received]
    TokenDetails --> Complete
    Cancelled --> Complete

    ExportOption -->|Yes| ExportHistory[Export Transaction History]
    ExportOption -->|No| Complete

    FundsReceived --> Complete
    ExportHistory --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style WalletView fill:#FFC107,stroke:#FFA000,color:black
    style TokenContext fill:#FFC107,stroke:#FFA000,color:black
    style Action fill:#FFC107,stroke:#FFA000,color:black
    style MarketplaceAction fill:#FFC107,stroke:#FFA000,color:black
    style OrderAction fill:#FFC107,stroke:#FFA000,color:black
    style TokenType fill:#FFC107,stroke:#FFA000,color:black
    style ProcessingStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryOptions fill:#FFC107,stroke:#FFA000,color:black
    style ExportOption fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Wallet Dashboard**: Access the wallet section
2. **Select Wallet View**:
   - Project View: See wallets organized by projects
   - Standard View: See all wallets without project context
3. **Project View Flow**:
   - Select a specific project
   - View wallets associated with that project
   - Select a project-specific wallet
4. **Standard View Flow**:
   - Select any wallet from the complete list
5. **Token Context**:
   - Project Tokens: View tokens associated with specific projects
   - All Tokens: View all tokens regardless of project
6. **Select Action**: Choose from various management options:
   - Send Tokens: Transfer tokens to another wallet
   - Receive Tokens: Generate address for receiving tokens
   - View Portfolio: See all tokens in the wallet
   - View NFTs: See all NFTs in the wallet
   - View History: See transaction history
   - Marketplace Actions: Perform marketplace-related actions
7. **Marketplace Actions Flow**:
   - List Tokens: List tokens on the marketplace
   - View Orders: See buy/sell orders for your tokens
   - Process Order: Accept, reject, or counter marketplace orders
8. **Send Token Flow**:
   - Select token type (project token or other token)
   - Select specific token to send
   - Enter recipient and amount
   - Review gas fees
   - Confirm transaction
   - Monitor processing status
   - Record transaction in log
9. **Receive Token Flow**:
   - Generate and share address
   - Wait for incoming funds
   - Funds received notification
10. **Portfolio Management Flow**:
    - Filter and sort tokens
    - View token details
11. **Transaction History Flow**:
    - Filter transaction history
    - Export history (optional)

## 3. Multi-Chain Wallet Management Journey

Managing wallets across multiple blockchain networks.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Multi-Chain Dashboard]
    Navigate --> ViewOverview[View Multi-Chain Overview]
    ViewOverview --> SelectNetwork{Select Network}

    SelectNetwork -->|Ethereum| ETHWallets[View Ethereum Wallets]
    SelectNetwork -->|Polygon| POLYWallets[View Polygon Wallets]
    SelectNetwork -->|Arbitrum| ARBWallets[View Arbitrum Wallets]
    SelectNetwork -->|Optimism| OPTWallets[View Optimism Wallets]
    SelectNetwork -->|Base| BASEWallets[View Base Wallets]

    ETHWallets --> SelectWallet[Select Wallet]
    POLYWallets --> SelectWallet
    ARBWallets --> SelectWallet
    OPTWallets --> SelectWallet
    BASEWallets --> SelectWallet

    SelectWallet --> Action{Select Action}

    Action -->|Network-Specific Actions| NetworkActions[Perform Network-Specific Actions]
    Action -->|Cross-Chain Transfer| InitiateBridge[Initiate Bridge Transfer]
    Action -->|Gas Management| ManageGas[Manage Gas Settings]

    NetworkActions --> Complete([Management Complete])

    InitiateBridge --> SelectSource[Select Source Network]
    SelectSource --> SelectDestination[Select Destination Network]
    SelectDestination --> SelectAsset[Select Asset to Bridge]
    SelectAsset --> ReviewBridgeFees[Review Bridge Fees]
    ReviewBridgeFees --> ConfirmBridge[Confirm Bridge Transaction]
    ConfirmBridge --> BridgeStatus{Bridge Status}

    BridgeStatus -->|Initiated| WaitConfirmation[Wait for Confirmations]
    BridgeStatus -->|Failed| BridgeError[View Bridge Error]

    WaitConfirmation --> DestinationStatus{Destination Status}
    DestinationStatus -->|Completed| BridgeComplete[Bridge Complete]
    DestinationStatus -->|Pending| ContinueWaiting[Continue Waiting]
    DestinationStatus -->|Failed| RecoveryOptions[View Recovery Options]

    BridgeComplete --> Complete
    BridgeError --> RetryBridge{Retry Bridge?}
    ContinueWaiting --> CheckAgain[Check Again Later]
    RecoveryOptions --> AttemptRecovery[Attempt Recovery]

    RetryBridge -->|Yes| SelectSource
    RetryBridge -->|No| Complete
    CheckAgain --> Complete
    AttemptRecovery --> Complete

    ManageGas --> GasAction{Gas Action}
    GasAction -->|Set Default Gas| SetDefaultGas[Set Default Gas Settings]
    GasAction -->|Gas Price Alerts| ConfigureAlerts[Configure Gas Price Alerts]
    GasAction -->|Gas Optimization| OptimizeGas[Enable Gas Optimization]

    SetDefaultGas --> Complete
    ConfigureAlerts --> Complete
    OptimizeGas --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style SelectNetwork fill:#FFC107,stroke:#FFA000,color:black
    style Action fill:#FFC107,stroke:#FFA000,color:black
    style BridgeStatus fill:#FFC107,stroke:#FFA000,color:black
    style DestinationStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryBridge fill:#FFC107,stroke:#FFA000,color:black
    style GasAction fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Multi-Chain Dashboard**: Access the multi-chain section
2. **View Multi-Chain Overview**: See all networks and wallets
3. **Select Network**: Choose a specific blockchain network
4. **View Network Wallets**: See wallets on the selected network
5. **Select Wallet**: Choose a specific wallet to manage
6. **Select Action**: Choose from various management options:
   - Network-Specific Actions: Actions unique to the selected network
   - Cross-Chain Transfer: Move assets between networks
   - Gas Management: Configure gas settings
7. **Cross-Chain Transfer Flow**:
   - Select source and destination networks
   - Select asset to bridge
   - Review bridge fees
   - Confirm bridge transaction
   - Monitor bridge status
   - Handle completion or recovery if needed
8. **Gas Management Flow**:
   - Set default gas settings
   - Configure gas price alerts
   - Enable gas optimization features

## 4. Wallet Security and Compliance Journey

Managing wallet security and ensuring compliance with regulations.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Wallet Security]
    Navigate --> ViewStatus[View Security Status]
    ViewStatus --> Action{Select Action}

    Action -->|Access Control| ManageAccess[Manage Access Control]
    Action -->|Security Settings| SecuritySettings[Configure Security Settings]
    Action -->|Audit Logs| ViewAudit[View Wallet Audit Logs]
    Action -->|Compliance Check| RunCompliance[Run Compliance Check]

    ManageAccess --> AccessAction{Access Action}
    AccessAction -->|Add User| AddUser[Add User Access]
    AccessAction -->|Remove User| RemoveUser[Remove User Access]
    AccessAction -->|Modify Permissions| ModifyPermissions[Modify User Permissions]

    SecuritySettings --> SecurityAction{Security Action}
    SecurityAction -->|Update Recovery| UpdateRecovery[Update Recovery Options]
    SecurityAction -->|Configure 2FA| Configure2FA[Configure Two-Factor Auth]
    SecurityAction -->|Transaction Limits| SetLimits[Set Transaction Limits]
    SecurityAction -->|Approval Rules| SetApprovals[Set Approval Rules]

    ViewAudit --> FilterAudit[Filter Audit Logs]
    FilterAudit --> ExportAudit[Export Audit Logs]

    RunCompliance --> ComplianceAction{Compliance Action}
    ComplianceAction -->|AML Check| RunAML[Run AML Check]
    ComplianceAction -->|Transaction Screening| ScreenTx[Screen Transactions]
    ComplianceAction -->|Generate Report| GenerateReport[Generate Compliance Report]

    AddUser --> Complete([Security Management Complete])
    RemoveUser --> Complete
    ModifyPermissions --> Complete
    UpdateRecovery --> Complete
    Configure2FA --> Complete
    SetLimits --> Complete
    SetApprovals --> Complete
    ExportAudit --> Complete
    RunAML --> Complete
    ScreenTx --> Complete
    GenerateReport --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style Action fill:#FFC107,stroke:#FFA000,color:black
    style AccessAction fill:#FFC107,stroke:#FFA000,color:black
    style SecurityAction fill:#FFC107,stroke:#FFA000,color:black
    style ComplianceAction fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Wallet Security**: Access the security section
2. **View Security Status**: See overall security health
3. **Select Action**: Choose from various security options:
   - Access Control: Manage user access to wallets
   - Security Settings: Configure security parameters
   - Audit Logs: View wallet activity logs
   - Compliance Check: Run regulatory compliance checks
4. **Access Control Flow**:
   - Add, remove, or modify user access
5. **Security Settings Flow**:
   - Update recovery options
   - Configure two-factor authentication
   - Set transaction limits
   - Set approval rules for transactions
6. **Audit Logs Flow**:
   - Filter and export audit logs
7. **Compliance Check Flow**:
   - Run AML (Anti-Money Laundering) checks
   - Screen transactions for compliance
   - Generate compliance reports

## 5. Carbon Credit Tokenization Journey

The process of tokenizing carbon credits from projects onto the blockchain.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Tokenization Dashboard]
    Navigate --> ViewProjects[View Projects with Credits]
    ViewProjects --> SelectProject[Select Project]
    SelectProject --> ViewCredits[View Available Carbon Credits]
    ViewCredits --> SelectCredits[Select Credits to Tokenize]

    SelectCredits --> TokenizationMethod{Tokenization Method}
    TokenizationMethod -->|Standard Tokenization| StandardTokenization[Standard Tokenization]
    TokenizationMethod -->|Batch Tokenization| BatchTokenization[Batch Tokenization]
    TokenizationMethod -->|Custom Tokenization| CustomTokenization[Custom Tokenization]

    StandardTokenization --> SelectNetwork[Select Blockchain Network]
    BatchTokenization --> BatchParameters[Configure Batch Parameters]
    CustomTokenization --> CustomParameters[Configure Custom Parameters]

    BatchParameters --> SelectNetwork
    CustomParameters --> SelectNetwork

    SelectNetwork --> NetworkType{Network Type}
    NetworkType -->|Mainnet| ConfirmMainnet[Confirm Mainnet Selection]
    NetworkType -->|Testnet| SelectTestnet[Select Testnet]

    ConfirmMainnet --> SelectWallet[Select Destination Wallet]
    SelectTestnet --> SelectWallet

    SelectWallet --> TokenStandard{Token Standard}
    TokenStandard -->|ERC-20| ConfigureERC20[Configure ERC-20 Parameters]
    TokenStandard -->|ERC-1155| ConfigureERC1155[Configure ERC-1155 Parameters]
    TokenStandard -->|Custom| ConfigureCustom[Configure Custom Parameters]

    ConfigureERC20 --> TokenMetadata[Configure Token Metadata]
    ConfigureERC1155 --> TokenMetadata
    ConfigureCustom --> TokenMetadata

    TokenMetadata --> ReviewDetails[Review Tokenization Details]
    ReviewDetails --> EstimateGas[Estimate Gas Costs]
    EstimateGas --> ConfirmTokenization[Confirm Tokenization]

    ConfirmTokenization --> ProcessingStatus{Processing Status}
    ProcessingStatus -->|Success| TokenCreated[Tokens Created Successfully]
    ProcessingStatus -->|Failed| ErrorHandling[View Error Details]

    ErrorHandling --> RetryOptions{Retry Options}
    RetryOptions -->|Adjust Parameters| SelectNetwork
    RetryOptions -->|Cancel| Cancelled[Tokenization Cancelled]

    TokenCreated --> UpdateRegistry[Update Carbon Credit Registry]
    UpdateRegistry --> ViewTokens[View Tokenized Credits]
    ViewTokens --> MarketplaceOption{List on Marketplace?}

    MarketplaceOption -->|Yes| InitiateListing[Initiate Marketplace Listing]
    MarketplaceOption -->|No| Complete([Tokenization Complete])

    InitiateListing --> ConfigureListing[Configure Listing Parameters]
    ConfigureListing --> ConfirmListing[Confirm Listing]
    ConfirmListing --> ListingComplete[Listing Complete]

    ListingComplete --> Complete
    Cancelled --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style TokenizationMethod fill:#FFC107,stroke:#FFA000,color:black
    style NetworkType fill:#FFC107,stroke:#FFA000,color:black
    style TokenStandard fill:#FFC107,stroke:#FFA000,color:black
    style ProcessingStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryOptions fill:#FFC107,stroke:#FFA000,color:black
    style MarketplaceOption fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Tokenization Dashboard**: Access the tokenization section
2. **View Projects with Credits**: See projects with available carbon credits
3. **Select Project**: Choose a specific project
4. **View Available Carbon Credits**: See credits available for tokenization
5. **Select Credits to Tokenize**: Choose which credits to tokenize
6. **Tokenization Method**:
   - Standard Tokenization: Tokenize credits individually
   - Batch Tokenization: Tokenize multiple credits at once
   - Custom Tokenization: Use custom tokenization parameters
7. **Select Blockchain Network**: Choose the network (Ethereum, Polygon, etc.)
8. **Network Type**: Select between mainnet (production) or testnet (testing)
9. **Select Destination Wallet**: Choose where the tokens will be stored
10. **Token Standard**: Select the token standard to use:
    - ERC-20: Fungible token standard
    - ERC-1155: Multi-token standard
    - Custom: Custom token implementation
11. **Configure Token Metadata**: Set up token metadata and properties
12. **Review Tokenization Details**: Verify all parameters
13. **Estimate Gas Costs**: Calculate transaction fees
14. **Confirm Tokenization**: Approve the tokenization transaction
15. **Processing Status**: Monitor the tokenization process
16. **Update Registry**: Update the carbon credit registry with tokenization status
17. **View Tokenized Credits**: See the newly created tokens
18. **Marketplace Option**: Choose whether to list tokens on the marketplace
19. **Listing Flow** (if listing on marketplace):
    - Configure listing parameters
    - Confirm listing
    - Complete listing process

## Success Metrics

The Wallet Manager journey is successful when:

1. Wallets are securely created and properly configured for projects
2. Carbon credits are successfully tokenized from projects
3. Tokens are efficiently managed across multiple chains
4. Marketplace transactions are processed smoothly
5. Transactions complete successfully with optimized gas fees
6. Security measures are properly implemented
7. Project-specific wallet configurations are optimized
8. Compliance requirements are fully satisfied

## Common Challenges and Solutions

| Challenge | Solution |
|-----------|----------|
| Complex wallet setup | Guided wallet creation wizard with project templates |
| Project-specific wallet configuration | Project wallet templates and configuration guides |
| Tokenization failures | Network status indicators and guided troubleshooting |
| Marketplace integration | Automated marketplace connectors and trading APIs |
| Gas fee optimization | Automated gas price suggestions and alerts |
| Cross-chain transfers | Bridge status monitoring and recovery options |
| Security configuration | Security health score and best practice guides |
| Compliance management | Automated compliance checks and reporting |
| Multi-project token management | Project portfolio view and batch operations |
