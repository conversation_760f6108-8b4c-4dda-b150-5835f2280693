# Project and Marketplace Updates to User Journeys

This document outlines the updates made to the user journey documentation to better reflect the relationship between organizations, projects, and carbon credits, as well as the marketplace trading dynamics.

## Overview of Changes

The user journey documentation has been updated to incorporate:

1. **Project-Based Structure**: Organizations can have multiple projects generating carbon credits
2. **Marketplace Trading Dynamics**: Enhanced trading flows for carbon credits on the marketplace
3. **Portfolio Management**: Added project portfolio management for Enterprise Administrators
4. **Marketplace Strategy**: Added marketplace strategy management for optimizing trading

## Key Updates by Role

### Carbon Credit Manager

The Carbon Credit Manager role has been significantly updated to reflect the project-based structure:

1. **Role Definition**: Updated to emphasize managing projects that generate carbon credits
2. **Project and Carbon Credit Management Journey**: Replaced the simple carbon credit listing journey with a comprehensive project management flow:
   - Create and manage projects
   - Generate carbon credits from projects
   - Manage verification and documentation
   - Configure listing options

3. **Marketplace and Trading Journey**: Enhanced to include:
   - Multiple pricing strategies (fixed, auction, dynamic)
   - Order management
   - Buyer interaction

4. **Portfolio Analytics Journey**: Expanded to include:
   - Project analytics
   - Credit analytics
   - Market analytics
   - Impact analytics

5. **Credit Retirement and Impact Reporting Journey**: Added a dedicated journey for:
   - Different retirement purposes (corporate, client, product, event)
   - Impact reporting and sharing

### Enterprise Administrator

The Enterprise Administrator role has been updated to include project portfolio oversight and marketplace strategy:

1. **Role Definition**: Updated to include project portfolio oversight and marketplace strategy management
2. **Project Portfolio Management Journey**: Added a new journey for:
   - Creating and managing projects
   - Assigning project teams
   - Setting project milestones
   - Analyzing project performance
   - Defining portfolio strategy

3. **Organization Analytics Journey**: Enhanced to include:
   - Project analytics
   - Marketplace analytics

4. **Marketplace Strategy Journey**: Added a new journey for:
   - Market analysis
   - Pricing strategy
   - Listing strategy
   - Trading rules
   - Buyer management

## Project-Based Structure

The updated user journeys now reflect a hierarchical structure:

```
Organization
├── Projects
│   ├── Project 1 (e.g., Forestry Project)
│   │   ├── Carbon Credits (Vintage 2022)
│   │   ├── Carbon Credits (Vintage 2023)
│   │   └── Carbon Credits (Vintage 2024)
│   ├── Project 2 (e.g., Renewable Energy Project)
│   │   ├── Carbon Credits (Vintage 2022)
│   │   └── Carbon Credits (Vintage 2023)
│   └── Project 3 (e.g., Methane Reduction Project)
│       └── Carbon Credits (Vintage 2023)
└── Marketplace Listings
    ├── Listing 1 (from Project 1, Vintage 2022)
    ├── Listing 2 (from Project 1, Vintage 2023)
    └── Listing 3 (from Project 2, Vintage 2023)
```

## Marketplace Trading Dynamics

The updated user journeys now reflect more sophisticated marketplace dynamics:

1. **Multiple Pricing Strategies**:
   - Fixed Price: Set a specific price per credit
   - Auction: Configure auction parameters
   - Dynamic Pricing: Set rules for automatic price adjustments
   - Tiered Pricing: Different prices based on volume or buyer tier

2. **Order Management**:
   - Accept orders
   - Reject orders
   - Make counter offers
   - Process transactions

3. **Buyer Relationship Management**:
   - Preferred buyers
   - Buyer tiers
   - Buyer blacklisting
   - Buyer analytics

4. **Listing Strategies**:
   - Batch listing
   - Continuous listing
   - Scheduled listing
   - Exclusive listing

## Impact on Other Roles

While we've focused on updating the Carbon Credit Manager and Enterprise Administrator roles, these changes have implications for other roles:

1. **Wallet Manager**: Will need to interact with the project-based structure when tokenizing credits
2. **Compliance Officer**: Will need to verify projects as well as individual carbon credits
3. **Finance Manager**: Will need to analyze financial performance at both project and credit levels
4. **Regular User**: Will interact with the marketplace to purchase credits from various projects
5. **Platform Administrator**: Will need to oversee the project verification process

## Next Steps

To fully implement these changes across the platform, consider:

1. Updating the frontend sitemap to include project management pages
2. Ensuring the database schema supports the project-based structure
3. Updating API endpoints to handle project-related operations
4. Enhancing the marketplace UI to support the new trading dynamics
5. Updating other user journey documents to reflect these changes
