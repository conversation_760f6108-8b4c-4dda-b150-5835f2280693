# Finance Manager User Journey

This document outlines the key journeys for Finance Managers on the Carbonix platform.

## Role Overview

The Finance Manager is responsible for overseeing the financial aspects of carbon projects and credit trading, including project financial tracking, transaction monitoring, portfolio valuation, financial reporting, and tax compliance. This role typically belongs to finance team members, accountants, or financial controllers within the enterprise.

## Permissions

Finance Managers have the following key permissions:
- View and manage project financial data
- Track project development costs and returns
- View carbon credits and their financial details
- Monitor marketplace financial activities
- View wallet balances and transaction history
- Perform portfolio valuation and analysis
- Access financial reports and analytics
- View audit logs for financial activities
- Generate tax reports
- Configure financial settings

## 1. Project Financial Management Journey

The process of managing financial aspects of carbon projects.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Finance Dashboard]
    Navigate --> SelectProjectFinance[Select Project Finance]
    SelectProjectFinance --> ViewProjects[View Project Portfolio]
    ViewProjects --> SelectProject[Select Project]
    SelectProject --> ViewProjectFinancials[View Project Financial Dashboard]

    ViewProjectFinancials --> FinancialAction{Financial Action}

    FinancialAction -->|Budget Management| BudgetManagement[Manage Project Budget]
    FinancialAction -->|Cost Tracking| CostTracking[Track Project Costs]
    FinancialAction -->|Revenue Tracking| RevenueTracking[Track Project Revenue]
    FinancialAction -->|ROI Analysis| ROIAnalysis[Analyze Project ROI]
    FinancialAction -->|Financial Reporting| FinancialReporting[Generate Financial Reports]

    BudgetManagement --> BudgetAction{Budget Action}
    BudgetAction -->|Create Budget| CreateBudget[Create Project Budget]
    BudgetAction -->|Update Budget| UpdateBudget[Update Project Budget]
    BudgetAction -->|Review Budget| ReviewBudget[Review Budget Performance]

    CostTracking --> CostAction{Cost Action}
    CostAction -->|Record Costs| RecordCosts[Record Project Costs]
    CostAction -->|Categorize Costs| CategorizeCosts[Categorize Cost Items]
    CostAction -->|Analyze Costs| AnalyzeCosts[Analyze Cost Breakdown]

    RevenueTracking --> RevenueAction{Revenue Action}
    RevenueAction -->|Record Revenue| RecordRevenue[Record Project Revenue]
    RevenueAction -->|Credit Sales| CreditSales[Track Credit Sales Revenue]
    RevenueAction -->|Other Revenue| OtherRevenue[Track Other Revenue]

    ROIAnalysis --> ROIAction{ROI Action}
    ROIAction -->|Calculate ROI| CalculateROI[Calculate Project ROI]
    ROIAction -->|Forecast ROI| ForecastROI[Forecast Future ROI]
    ROIAction -->|Compare ROI| CompareROI[Compare with Other Projects]

    FinancialReporting --> ReportAction{Report Action}
    ReportAction -->|Project P&L| ProjectPL[Generate Project P&L]
    ReportAction -->|Cash Flow| CashFlow[Generate Cash Flow Report]
    ReportAction -->|Financial Metrics| FinancialMetrics[Generate Financial Metrics]

    CreateBudget --> ConfigureBudget[Configure Budget Parameters]
    UpdateBudget --> ConfigureBudget
    ConfigureBudget --> SaveBudget[Save Budget]

    ReviewBudget --> VarianceAnalysis[Perform Variance Analysis]
    VarianceAnalysis --> BudgetAdjustment{Adjustment Needed?}
    BudgetAdjustment -->|Yes| UpdateBudget
    BudgetAdjustment -->|No| SaveBudgetReview[Save Budget Review]

    RecordCosts --> EnterCostDetails[Enter Cost Details]
    EnterCostDetails --> AttachDocuments[Attach Supporting Documents]
    AttachDocuments --> SaveCosts[Save Cost Records]

    CategorizeCosts --> SelectCategories[Select Cost Categories]
    SelectCategories --> AssignCosts[Assign Costs to Categories]
    AssignCosts --> SaveCategories[Save Categorization]

    AnalyzeCosts --> GenerateCostReport[Generate Cost Analysis Report]

    RecordRevenue --> EnterRevenueDetails[Enter Revenue Details]
    EnterRevenueDetails --> CategorizeRevenue[Categorize Revenue]
    CategorizeRevenue --> SaveRevenue[Save Revenue Records]

    CreditSales --> LinkToMarketplace[Link to Marketplace Sales]
    LinkToMarketplace --> ReconcileSales[Reconcile Sales Records]
    ReconcileSales --> SaveSalesData[Save Sales Data]

    OtherRevenue --> EnterRevenueSource[Enter Revenue Source]
    EnterRevenueSource --> SaveOtherRevenue[Save Other Revenue]

    CalculateROI --> GatherFinancialData[Gather Financial Data]
    GatherFinancialData --> PerformCalculation[Perform ROI Calculation]
    PerformCalculation --> SaveROIResults[Save ROI Results]

    ForecastROI --> SetAssumptions[Set Forecast Assumptions]
    SetAssumptions --> RunForecastModel[Run Forecast Model]
    RunForecastModel --> SaveForecast[Save ROI Forecast]

    CompareROI --> SelectProjects[Select Projects to Compare]
    SelectProjects --> GenerateComparison[Generate ROI Comparison]
    GenerateComparison --> SaveComparison[Save Comparison Results]

    ProjectPL --> ConfigurePL[Configure P&L Parameters]
    ConfigurePL --> GeneratePL[Generate P&L Report]
    GeneratePL --> SavePL[Save P&L Report]

    CashFlow --> ConfigureCashFlow[Configure Cash Flow Parameters]
    ConfigureCashFlow --> GenerateCashFlow[Generate Cash Flow Report]
    GenerateCashFlow --> SaveCashFlow[Save Cash Flow Report]

    FinancialMetrics --> SelectMetrics[Select Financial Metrics]
    SelectMetrics --> CalculateMetrics[Calculate Selected Metrics]
    CalculateMetrics --> SaveMetrics[Save Financial Metrics]

    SaveBudget --> NotifyStakeholders[Notify Stakeholders]
    SaveBudgetReview --> NotifyStakeholders
    SaveCosts --> UpdateFinancials[Update Financial Records]
    SaveCategories --> UpdateFinancials
    GenerateCostReport --> ShareReport[Share Report]
    SaveRevenue --> UpdateFinancials
    SaveSalesData --> UpdateFinancials
    SaveOtherRevenue --> UpdateFinancials
    SaveROIResults --> ShareResults[Share ROI Results]
    SaveForecast --> ShareResults
    SaveComparison --> ShareResults
    SavePL --> DistributeReport[Distribute Financial Report]
    SaveCashFlow --> DistributeReport
    SaveMetrics --> DistributeReport

    NotifyStakeholders --> Complete([Management Complete])
    UpdateFinancials --> Complete
    ShareReport --> Complete
    ShareResults --> Complete
    DistributeReport --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style FinancialAction fill:#FFC107,stroke:#FFA000,color:black
    style BudgetAction fill:#FFC107,stroke:#FFA000,color:black
    style CostAction fill:#FFC107,stroke:#FFA000,color:black
    style RevenueAction fill:#FFC107,stroke:#FFA000,color:black
    style ROIAction fill:#FFC107,stroke:#FFA000,color:black
    style ReportAction fill:#FFC107,stroke:#FFA000,color:black
    style BudgetAdjustment fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Finance Dashboard**: Access the finance section
2. **Select Project Finance**: Go to the project finance area
3. **View Project Portfolio**: See all projects in the organization
4. **Select Project**: Choose a specific project to manage
5. **View Project Financial Dashboard**: See financial overview for the project
6. **Financial Action**: Choose a financial management action:
   - Budget Management: Create and manage project budgets
   - Cost Tracking: Track and analyze project costs
   - Revenue Tracking: Track and analyze project revenue
   - ROI Analysis: Analyze return on investment
   - Financial Reporting: Generate financial reports
7. **Budget Management Flow**:
   - Create or update project budgets
   - Configure budget parameters
   - Review budget performance
   - Perform variance analysis
   - Make budget adjustments if needed
8. **Cost Tracking Flow**:
   - Record project costs
   - Attach supporting documents
   - Categorize costs
   - Analyze cost breakdown
   - Generate cost reports
9. **Revenue Tracking Flow**:
   - Record project revenue
   - Track carbon credit sales revenue
   - Track other revenue sources
   - Reconcile sales records
10. **ROI Analysis Flow**:
    - Calculate project ROI
    - Forecast future ROI
    - Compare ROI with other projects
    - Share ROI results
11. **Financial Reporting Flow**:
    - Generate project P&L reports
    - Generate cash flow reports
    - Calculate financial metrics
    - Distribute financial reports

## 2. Marketplace Financial Monitoring Journey

The process of monitoring financial activities and transactions on the marketplace.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Finance Dashboard]
    Navigate --> SelectMarketplace[Select Marketplace Finance]
    SelectMarketplace --> ViewDashboard[View Marketplace Financial Dashboard]
    ViewDashboard --> SelectMetric{Select Metric to Monitor}

    SelectMetric -->|Transaction Volume| TxVolume[View Transaction Volume]
    SelectMetric -->|Revenue| Revenue[View Revenue Metrics]
    SelectMetric -->|Fees| Fees[View Platform Fees]
    SelectMetric -->|Price Trends| PriceTrends[View Price Trends]
    SelectMetric -->|Wallet Activities| WalletActivities[View Wallet Activities]

    TxVolume --> FilterOptions{Filter Options}
    Revenue --> FilterOptions
    Fees --> FilterOptions
    PriceTrends --> FilterOptions
    WalletActivities --> FilterOptions

    FilterOptions -->|By Project| FilterByProject[Filter by Project]
    FilterOptions -->|By Credit Type| FilterByType[Filter by Credit Type]
    FilterOptions -->|By Date Range| FilterByDate[Filter by Date Range]
    FilterOptions -->|By Organization| FilterByOrg[Filter by Organization]
    FilterOptions -->|By Transaction Type| FilterByTx[Filter by Transaction Type]

    FilterByProject --> ApplyFilters[Apply Filters]
    FilterByType --> ApplyFilters
    FilterByDate --> ApplyFilters
    FilterByOrg --> ApplyFilters
    FilterByTx --> ApplyFilters

    ApplyFilters --> ViewDetailed[View Detailed Breakdown]
    ViewDetailed --> CompareOption{Compare Periods?}

    CompareOption -->|Yes| SelectPeriods[Select Periods to Compare]
    CompareOption -->|No| AnalyzeOption{Analyze Trends?}

    SelectPeriods --> ViewComparison[View Period Comparison]
    ViewComparison --> AnalyzeOption

    AnalyzeOption -->|Yes| RunAnalysis[Run Trend Analysis]
    AnalyzeOption -->|No| AlertOption{Set Alerts?}

    RunAnalysis --> ViewTrends[View Trend Analysis]
    ViewTrends --> AlertOption

    AlertOption -->|Yes| ConfigureAlerts[Configure Financial Alerts]
    AlertOption -->|No| ExportOption{Export Data?}

    ConfigureAlerts --> SetThresholds[Set Alert Thresholds]
    SetThresholds --> SetNotifications[Set Notification Preferences]
    SetNotifications --> SaveAlerts[Save Alert Configuration]
    SaveAlerts --> ExportOption

    ExportOption -->|Yes| ExportFormat[Select Export Format]
    ExportOption -->|No| Complete([Monitoring Complete])

    ExportFormat --> ExportData[Export Financial Data]
    ExportData --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style SelectMetric fill:#FFC107,stroke:#FFA000,color:black
    style FilterOptions fill:#FFC107,stroke:#FFA000,color:black
    style CompareOption fill:#FFC107,stroke:#FFA000,color:black
    style AnalyzeOption fill:#FFC107,stroke:#FFA000,color:black
    style AlertOption fill:#FFC107,stroke:#FFA000,color:black
    style ExportOption fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Finance Dashboard**: Access the finance section
2. **Select Marketplace Finance**: Go to the marketplace finance area
3. **View Marketplace Financial Dashboard**: See high-level financial metrics
4. **Select Metric to Monitor**: Choose a specific area to explore:
   - Transaction Volume: Amount and frequency of marketplace transactions
   - Revenue: Income from carbon credit sales and fees
   - Fees: Platform fees collected from transactions
   - Price Trends: How carbon credit prices have changed
   - Wallet Activities: Financial activities in blockchain wallets
5. **Filter Options**: Choose filtering criteria:
   - By Project: Filter by specific carbon projects
   - By Credit Type: Filter by carbon credit type
   - By Date Range: Filter by time period
   - By Organization: Filter by organization
   - By Transaction Type: Filter by transaction type
6. **Apply Filters**: Apply selected filters to the data
7. **View Detailed Breakdown**: Explore specific data points
8. **Compare Periods (Optional)**: Compare metrics across time periods
9. **Analyze Trends (Optional)**: Run trend analysis on the data
10. **Set Alerts (Optional)**: Configure financial alerts for specific thresholds
11. **Export Data (Optional)**: Download financial data for further analysis

## 3. Transaction Audit Journey

The process of auditing and reconciling financial transactions.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Transaction Audit]
    Navigate --> ViewTransactions[View Transaction List]
    ViewTransactions --> FilterTransactions[Filter Transactions]
    FilterTransactions --> SelectTransaction[Select Transaction to Audit]
    SelectTransaction --> ViewDetails[View Transaction Details]

    ViewDetails --> VerifyDetails{Verify Transaction Details}
    VerifyDetails -->|Correct| MarkVerified[Mark as Verified]
    VerifyDetails -->|Discrepancy| FlagIssue[Flag Transaction Issue]

    MarkVerified --> RecordAudit[Record Audit Action]
    FlagIssue --> IssueType{Issue Type}

    IssueType -->|Minor| AddNote[Add Audit Note]
    IssueType -->|Major| CreateCase[Create Investigation Case]
    IssueType -->|Reconciliation| ReconcileTransaction[Reconcile Transaction]

    AddNote --> RecordAudit
    CreateCase --> AssignCase[Assign Case to Team Member]
    ReconcileTransaction --> RecordReconciliation[Record Reconciliation]

    AssignCase --> RecordAudit
    RecordReconciliation --> RecordAudit

    RecordAudit --> NextTransaction{Audit Another?}
    NextTransaction -->|Yes| SelectTransaction
    NextTransaction -->|No| GenerateReport[Generate Audit Report]

    GenerateReport --> ReviewReport[Review Audit Report]
    ReviewReport --> FinalizeReport[Finalize Audit Report]
    FinalizeReport --> ShareReport[Share with Stakeholders]
    ShareReport --> Complete([Audit Complete])

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style VerifyDetails fill:#FFC107,stroke:#FFA000,color:black
    style IssueType fill:#FFC107,stroke:#FFA000,color:black
    style NextTransaction fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Transaction Audit**: Access the audit section
2. **View Transaction List**: See all financial transactions
3. **Filter Transactions**: Narrow down by date, type, amount, etc.
4. **Select Transaction**: Choose a specific transaction to audit
5. **View Transaction Details**: Examine all transaction information
6. **Verify Transaction Details**: Check for accuracy and compliance
   - If correct, mark as verified
   - If discrepancy found, flag the issue
7. **Handle Issues**: Based on issue type:
   - Minor: Add an audit note
   - Major: Create an investigation case
   - Reconciliation: Reconcile the transaction
8. **Record Audit Action**: Document the audit process
9. **Continue or Complete**: Audit another transaction or generate report
10. **Generate Audit Report**: Create a summary of the audit
11. **Review and Finalize**: Complete the audit process
12. **Share with Stakeholders**: Distribute the audit report

## 4. Financial Reporting Journey

The process of generating and analyzing financial reports.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Financial Reporting]
    Navigate --> ReportType{Select Report Type}

    ReportType -->|Transaction Summary| TxReport[Generate Transaction Report]
    ReportType -->|Revenue Report| RevReport[Generate Revenue Report]
    ReportType -->|Asset Valuation| ValReport[Generate Asset Valuation Report]
    ReportType -->|Tax Report| TaxReport[Generate Tax Report]
    ReportType -->|Custom Report| CustomReport[Configure Custom Report]

    TxReport --> ConfigureTx[Configure Report Parameters]
    RevReport --> ConfigureRev[Configure Report Parameters]
    ValReport --> ConfigureVal[Configure Report Parameters]
    TaxReport --> ConfigureTax[Configure Report Parameters]
    CustomReport --> ConfigureCustom[Configure Custom Parameters]

    ConfigureTx --> GenerateTx[Generate Transaction Report]
    ConfigureRev --> GenerateRev[Generate Revenue Report]
    ConfigureVal --> GenerateVal[Generate Asset Valuation Report]
    ConfigureTax --> GenerateTax[Generate Tax Report]
    ConfigureCustom --> GenerateCustom[Generate Custom Report]

    GenerateTx --> ReviewReport[Review Generated Report]
    GenerateRev --> ReviewReport
    GenerateVal --> ReviewReport
    GenerateTax --> ReviewReport
    GenerateCustom --> ReviewReport

    ReviewReport --> ReportAction{Report Action}

    ReportAction -->|Export| ExportFormat[Select Export Format]
    ReportAction -->|Share| ShareOptions[Configure Sharing Options]
    ReportAction -->|Schedule| ScheduleReport[Schedule Recurring Report]
    ReportAction -->|Edit| EditReport[Edit Report Parameters]

    ExportFormat --> ExportReport[Export Report]
    ShareOptions --> ShareReport[Share Report]
    ScheduleReport --> ConfigureSchedule[Configure Schedule]
    EditReport --> RegenerateReport[Regenerate Report]

    ExportReport --> SaveReport[Save Report to Financial Records]
    ShareReport --> NotifyRecipients[Notify Recipients]
    ConfigureSchedule --> ConfirmSchedule[Confirm Schedule]
    RegenerateReport --> ReviewReport

    SaveReport --> Complete([Reporting Complete])
    NotifyRecipients --> Complete
    ConfirmSchedule --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style ReportType fill:#FFC107,stroke:#FFA000,color:black
    style ReportAction fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Financial Reporting**: Access the reporting section
2. **Select Report Type**: Choose the type of report to generate:
   - Transaction Summary: Overview of financial transactions
   - Revenue Report: Analysis of income sources
   - Asset Valuation: Value of carbon assets held
   - Tax Report: Information for tax compliance
   - Custom Report: Configurable custom report
3. **Configure Report Parameters**: Set filters, date ranges, etc.
4. **Generate Report**: Create the report based on parameters
5. **Review Generated Report**: Examine the report contents
6. **Report Action**: Choose what to do with the report:
   - Export: Save in various formats (PDF, CSV, etc.)
   - Share: Send to stakeholders
   - Schedule: Set up recurring report generation
   - Edit: Modify report parameters
7. **Complete Action**: Finish the selected action

## 5. Tax Compliance Journey

The process of managing tax compliance for carbon credit trading.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Tax Compliance]
    Navigate --> ViewStatus[View Tax Compliance Status]
    ViewStatus --> TaxPeriod{Select Tax Period}

    TaxPeriod -->|Current Period| CurrentPeriod[View Current Period]
    TaxPeriod -->|Previous Period| PreviousPeriod[View Previous Period]
    TaxPeriod -->|Custom Range| CustomRange[Configure Custom Range]

    CurrentPeriod --> ViewTransactions[View Taxable Transactions]
    PreviousPeriod --> ViewTransactions
    CustomRange --> ViewTransactions

    ViewTransactions --> CalculateTax[Calculate Tax Liability]
    CalculateTax --> ReviewCalculation[Review Tax Calculation]

    ReviewCalculation --> AdjustmentNeeded{Adjustments Needed?}
    AdjustmentNeeded -->|Yes| MakeAdjustments[Make Tax Adjustments]
    AdjustmentNeeded -->|No| GenerateReport[Generate Tax Report]

    MakeAdjustments --> RecordAdjustments[Record Adjustment Reasons]
    RecordAdjustments --> GenerateReport

    GenerateReport --> ReportFormat{Report Format}
    ReportFormat -->|Summary| SummaryReport[Generate Summary Report]
    ReportFormat -->|Detailed| DetailedReport[Generate Detailed Report]
    ReportFormat -->|Jurisdiction-Specific| JurisdictionReport[Generate Jurisdiction Report]

    SummaryReport --> ReviewFinalReport[Review Final Tax Report]
    DetailedReport --> ReviewFinalReport
    JurisdictionReport --> ReviewFinalReport

    ReviewFinalReport --> ApproveReport[Approve Tax Report]
    ApproveReport --> ExportReport[Export for Filing]
    ExportReport --> RecordCompliance[Record Tax Compliance Action]
    RecordCompliance --> Complete([Tax Compliance Complete])

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style TaxPeriod fill:#FFC107,stroke:#FFA000,color:black
    style AdjustmentNeeded fill:#FFC107,stroke:#FFA000,color:black
    style ReportFormat fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Tax Compliance**: Access the tax section
2. **View Tax Compliance Status**: See overall tax compliance
3. **Select Tax Period**: Choose the time period for tax analysis
4. **View Taxable Transactions**: See transactions with tax implications
5. **Calculate Tax Liability**: Determine tax obligations
6. **Review Tax Calculation**: Verify the calculated amounts
7. **Make Adjustments (if needed)**: Correct any discrepancies
8. **Generate Tax Report**: Create a report in the required format
9. **Review Final Tax Report**: Verify all information
10. **Approve Tax Report**: Finalize the tax report
11. **Export for Filing**: Prepare for submission to tax authorities
12. **Record Compliance Action**: Document the tax compliance process

## 6. Project Portfolio Valuation Journey

The process of valuing the organization's project portfolio and carbon assets.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Portfolio Valuation]
    Navigate --> ViewPortfolio[View Project Portfolio]
    ViewPortfolio --> ValuationScope{Select Valuation Scope}

    ValuationScope -->|Project Valuation| ProjectValuation[Value Projects]
    ValuationScope -->|Credit Valuation| CreditValuation[Value Carbon Credits]
    ValuationScope -->|Portfolio Valuation| PortfolioValuation[Value Entire Portfolio]

    ProjectValuation --> SelectProjects[Select Projects to Value]
    CreditValuation --> SelectCredits[Select Credits to Value]
    PortfolioValuation --> ConfigurePortfolio[Configure Portfolio Parameters]

    SelectProjects --> ProjectValuationMethod{Project Valuation Method}
    ProjectValuationMethod -->|DCF Method| ProjectDCF[Use Discounted Cash Flow]
    ProjectValuationMethod -->|Comparable Method| ProjectComparable[Use Comparable Projects]
    ProjectValuationMethod -->|Cost Method| ProjectCost[Use Cost-Based Method]
    ProjectValuationMethod -->|Custom Method| ProjectCustom[Configure Custom Method]

    SelectCredits --> CreditValuationMethod{Credit Valuation Method}
    CreditValuationMethod -->|Market Value| CreditMarket[Use Market Value Method]
    CreditValuationMethod -->|Historical Cost| CreditHistorical[Use Historical Cost Method]
    CreditValuationMethod -->|Fair Value| CreditFair[Use Fair Value Method]
    CreditValuationMethod -->|Custom Method| CreditCustom[Configure Custom Method]

    ConfigurePortfolio --> PortfolioValuationMethod{Portfolio Valuation Method}
    PortfolioValuationMethod -->|Sum of Parts| PortfolioSum[Use Sum of Parts Method]
    PortfolioValuationMethod -->|Weighted Average| PortfolioWeighted[Use Weighted Average Method]
    PortfolioValuationMethod -->|Risk-Adjusted| PortfolioRisk[Use Risk-Adjusted Method]
    PortfolioValuationMethod -->|Custom Method| PortfolioCustom[Configure Custom Method]

    ProjectDCF --> ConfigureProjectParams[Configure Project Parameters]
    ProjectComparable --> ConfigureProjectParams
    ProjectCost --> ConfigureProjectParams
    ProjectCustom --> ConfigureProjectParams

    CreditMarket --> ConfigureCreditParams[Configure Credit Parameters]
    CreditHistorical --> ConfigureCreditParams
    CreditFair --> ConfigureCreditParams
    CreditCustom --> ConfigureCreditParams

    PortfolioSum --> ConfigurePortfolioParams[Configure Portfolio Parameters]
    PortfolioWeighted --> ConfigurePortfolioParams
    PortfolioRisk --> ConfigurePortfolioParams
    PortfolioCustom --> ConfigurePortfolioParams

    ConfigureProjectParams --> PerformProjectValuation[Perform Project Valuation]
    ConfigureCreditParams --> PerformCreditValuation[Perform Credit Valuation]
    ConfigurePortfolioParams --> PerformPortfolioValuation[Perform Portfolio Valuation]

    PerformProjectValuation --> ReviewProjectResults[Review Project Valuation Results]
    PerformCreditValuation --> ReviewCreditResults[Review Credit Valuation Results]
    PerformPortfolioValuation --> ReviewPortfolioResults[Review Portfolio Valuation Results]

    ReviewProjectResults --> ProjectAdjustment{Adjustments Needed?}
    ProjectAdjustment -->|Yes| AdjustProjectValues[Adjust Project Values]
    ProjectAdjustment -->|No| FinalizeProjectValuation[Finalize Project Valuation]

    ReviewCreditResults --> CreditAdjustment{Adjustments Needed?}
    CreditAdjustment -->|Yes| AdjustCreditValues[Adjust Credit Values]
    CreditAdjustment -->|No| FinalizeCreditValuation[Finalize Credit Valuation]

    ReviewPortfolioResults --> PortfolioAdjustment{Adjustments Needed?}
    PortfolioAdjustment -->|Yes| AdjustPortfolioValues[Adjust Portfolio Values]
    PortfolioAdjustment -->|No| FinalizePortfolioValuation[Finalize Portfolio Valuation]

    AdjustProjectValues --> RecordProjectAdjustments[Record Project Adjustment Reasons]
    RecordProjectAdjustments --> FinalizeProjectValuation

    AdjustCreditValues --> RecordCreditAdjustments[Record Credit Adjustment Reasons]
    RecordCreditAdjustments --> FinalizeCreditValuation

    AdjustPortfolioValues --> RecordPortfolioAdjustments[Record Portfolio Adjustment Reasons]
    RecordPortfolioAdjustments --> FinalizePortfolioValuation

    FinalizeProjectValuation --> GenerateValuationReport[Generate Valuation Report]
    FinalizeCreditValuation --> GenerateValuationReport
    FinalizePortfolioValuation --> GenerateValuationReport

    GenerateValuationReport --> ReviewReport[Review Valuation Report]
    ReviewReport --> ApproveReport[Approve Valuation Report]
    ApproveReport --> ShareReport[Share with Stakeholders]
    ShareReport --> RecordValuation[Record Valuation in Financial System]
    RecordValuation --> Complete([Valuation Complete])

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style ValuationScope fill:#FFC107,stroke:#FFA000,color:black
    style ProjectValuationMethod fill:#FFC107,stroke:#FFA000,color:black
    style CreditValuationMethod fill:#FFC107,stroke:#FFA000,color:black
    style PortfolioValuationMethod fill:#FFC107,stroke:#FFA000,color:black
    style ProjectAdjustment fill:#FFC107,stroke:#FFA000,color:black
    style CreditAdjustment fill:#FFC107,stroke:#FFA000,color:black
    style PortfolioAdjustment fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Portfolio Valuation**: Access the valuation section
2. **View Project Portfolio**: See all projects and carbon assets
3. **Select Valuation Scope**: Choose what to value:
   - Project Valuation: Value specific carbon projects
   - Credit Valuation: Value specific carbon credits
   - Portfolio Valuation: Value the entire portfolio
4. **Project Valuation Flow**:
   - Select projects to value
   - Choose valuation method (DCF, comparable, cost, custom)
   - Configure project parameters
   - Perform project valuation
   - Review and adjust results if needed
   - Finalize project valuation
5. **Credit Valuation Flow**:
   - Select credits to value
   - Choose valuation method (market, historical, fair, custom)
   - Configure credit parameters
   - Perform credit valuation
   - Review and adjust results if needed
   - Finalize credit valuation
6. **Portfolio Valuation Flow**:
   - Configure portfolio parameters
   - Choose valuation method (sum of parts, weighted average, risk-adjusted, custom)
   - Perform portfolio valuation
   - Review and adjust results if needed
   - Finalize portfolio valuation
7. **Generate Valuation Report**: Create a detailed report
8. **Review and Approve**: Verify and finalize the report
9. **Share with Stakeholders**: Distribute the valuation report
10. **Record in Financial System**: Update financial records

## Success Metrics

The Finance Manager journey is successful when:

1. Project finances are accurately tracked and managed
2. Marketplace financial activities are effectively monitored
3. Transactions are properly audited and reconciled
4. Financial reports provide clear insights for decision-making
5. Tax compliance requirements are fully satisfied
6. Project portfolio is accurately valued and recorded
7. Financial data supports strategic decision-making
8. Cross-project financial analysis provides portfolio insights

## Common Challenges and Solutions

| Challenge | Solution |
|-----------|----------|
| Project financial tracking complexity | Project-specific financial templates and dashboards |
| Marketplace transaction monitoring | Real-time financial monitoring tools with project context |
| Complex transaction reconciliation | Automated reconciliation tools and clear audit trails |
| Project valuation methodology selection | Project-specific valuation frameworks with documentation |
| Portfolio-level financial analysis | Portfolio analytics tools with cross-project capabilities |
| Tax jurisdiction complexity | Jurisdiction-specific tax rule engines and guidance |
| Financial reporting requirements | Customizable report templates with project context |
| Asset valuation volatility | Multiple valuation methods and historical tracking |
| Cross-project financial comparison | Standardized financial metrics across projects |
