# User Journey Documentation

This directory contains detailed user journey documentation for the Carbonix platform, illustrating the paths different user roles take when interacting with the platform.

## Overview

User journeys are visual representations of the steps users take to accomplish specific goals on the platform. These journeys help in understanding the user experience, identifying pain points, and improving the overall usability of the platform.

## User Roles

The Carbonix platform supports several user roles, each with specific permissions and journeys:

1. [Enterprise Administrator](./enterprise-admin.md) - Manages the organization, users, and overall settings
2. [Carbon Credit Manager](./carbon-credit-manager.md) - Handles carbon credit listing and management
3. [Wallet Manager](./wallet-manager.md) - Manages blockchain wallets and transactions
4. [Compliance Officer](./compliance-officer.md) - Ensures regulatory compliance
5. [Finance Manager](./finance-manager.md) - Handles financial aspects and reporting
6. [Regular User](./regular-user.md) - Basic platform user with limited permissions
7. [Platform Administrator](./platform-admin.md) - Manages the entire platform (Carbonix staff)

## Journey Types

Each user role has several key journeys:

1. **Onboarding Journey** - How users register and set up their organization
2. **Core Functionality Journey** - How users perform their primary tasks
3. **Administrative Journey** - How users manage settings and configurations
4. **Reporting Journey** - How users access and analyze data

## How to Use This Documentation

Each user journey document includes:

1. A description of the user role and their goals
2. Mermaid diagrams illustrating the journey steps
3. Detailed explanations of each step
4. Key touchpoints and potential pain points
5. Success metrics for the journey

## Contributing

To contribute to this documentation:

1. Use Mermaid diagrams for visualizing journeys
2. Follow the existing structure and format
3. Include both happy paths and error scenarios
4. Consider accessibility and user experience factors
