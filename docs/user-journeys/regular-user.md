# Regular User User Journey

This document outlines the key journeys for Regular Users on the Carbonix platform.

## Role Overview

The Regular User is a standard platform user with basic permissions, typically a team member who needs to access and use the platform for day-to-day project and carbon credit activities but doesn't require administrative capabilities. This role might belong to sustainability team members, traders, or other employees within the enterprise.

## Permissions

Regular Users have the following key permissions:
- View organization information
- View user information
- View projects and their details
- View carbon credits with project context
- View marketplace listings
- Participate in marketplace trading (with approval)
- View wallet information
- View team information
- Tokenize carbon credits (with approval)
- Retire carbon credits
- Transfer tokens
- View transaction history
- Access basic analytics

## 1. Platform Onboarding Journey

The process of joining the platform and getting started as a Regular User.

```mermaid
flowchart TD
    Start([Start]) --> ReceiveInvite[Receive Invitation Email]
    ReceiveInvite --> ClickLink[Click Invitation Link]
    ClickLink --> CreateAccount[Create User Account]
    CreateAccount --> SetPassword[Set Password]
    SetPassword --> CompleteProfile[Complete User Profile]
    CompleteProfile --> ViewTutorial[View Platform Tutorial]
    ViewTutorial --> ExploreInterface[Explore Platform Interface]
    ExploreInterface --> ViewDashboard[View User Dashboard]
    ViewDashboard --> NotificationPrefs[Set Notification Preferences]
    NotificationPrefs --> Complete([Onboarding Complete])

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
```

### Journey Steps:

1. **Receive Invitation Email**: Get invited by an organization administrator
2. **Click Invitation Link**: Access the registration page
3. **Create User Account**: Enter basic information
4. **Set Password**: Create a secure password
5. **Complete User Profile**: Add additional profile information
6. **View Platform Tutorial**: Learn about platform features
7. **Explore Platform Interface**: Navigate through the platform
8. **View User Dashboard**: See personalized dashboard
9. **Set Notification Preferences**: Configure communication settings

## 2. Project Exploration Journey

The process of exploring and understanding carbon projects on the platform.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Projects]
    Navigate --> ViewProjects[View Project List]
    ViewProjects --> FilterProjects[Filter Projects by Criteria]
    FilterProjects --> SortProjects[Sort Projects by Attribute]
    SortProjects --> SelectProject[Select Project]
    SelectProject --> ViewProjectDetails[View Project Details]

    ViewProjectDetails --> ExploreOption{Explore Options}

    ExploreOption -->|Project Overview| ProjectOverview[View Project Overview]
    ExploreOption -->|Project Documentation| ProjectDocs[View Project Documentation]
    ExploreOption -->|Project Verification| VerificationInfo[View Verification Details]
    ExploreOption -->|Project Credits| ProjectCredits[View Associated Carbon Credits]
    ExploreOption -->|Project Impact| ImpactInfo[View Environmental Impact]

    ProjectOverview --> ViewSummary[View Project Summary]
    ProjectDocs --> ViewDocuments[View Project Documents]
    VerificationInfo --> ViewVerifiers[View Verification Reports]
    ProjectCredits --> ViewCredits[View Credit List]
    ImpactInfo --> ViewImpactData[View Impact Metrics]

    ViewSummary --> SaveOption{Save for Later?}
    ViewDocuments --> SaveOption
    ViewVerifiers --> SaveOption
    ViewCredits --> CreditAction{Credit Action}
    ViewImpactData --> SaveOption

    CreditAction -->|View Credit Details| SelectCredit[Select Carbon Credit]
    CreditAction -->|Return to Project| SaveOption

    SelectCredit --> ViewCreditDetails[View Credit Details]
    ViewCreditDetails --> CreditOption{Credit Option}

    CreditOption -->|View Pricing| PricingInfo[View Pricing History]
    CreditOption -->|View Tokenization| TokenInfo[View Tokenization Status]
    CreditOption -->|View Marketplace| MarketplaceInfo[View Marketplace Listing]
    CreditOption -->|Return to Credits| ViewCredits

    PricingInfo --> ViewPriceChart[View Price Charts]
    TokenInfo --> ViewBlockchain[View Blockchain Details]
    MarketplaceInfo --> ViewMarketplace[View Marketplace Details]

    ViewPriceChart --> ReturnToCredit[Return to Credit]
    ViewBlockchain --> ReturnToCredit
    ViewMarketplace --> ReturnToCredit

    ReturnToCredit --> ViewCreditDetails

    SaveOption -->|Yes| SaveToFavorites[Save to Favorites]
    SaveOption -->|No| ExploreMore{Explore More?}

    SaveToFavorites --> ExploreMore

    ExploreMore -->|Yes| ExploreChoice{Explore Choice}
    ExploreMore -->|No| Complete([Exploration Complete])

    ExploreChoice -->|Same Project| ExploreOption
    ExploreChoice -->|Different Project| SelectProject

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style ExploreOption fill:#FFC107,stroke:#FFA000,color:black
    style CreditAction fill:#FFC107,stroke:#FFA000,color:black
    style CreditOption fill:#FFC107,stroke:#FFA000,color:black
    style SaveOption fill:#FFC107,stroke:#FFA000,color:black
    style ExploreMore fill:#FFC107,stroke:#FFA000,color:black
    style ExploreChoice fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Projects**: Access the projects section
2. **View Project List**: See available carbon projects
3. **Filter Projects**: Narrow down by type, location, status, etc.
4. **Sort Projects**: Arrange by size, impact, date, etc.
5. **Select Project**: Choose a specific project to explore
6. **View Project Details**: See project overview and information
7. **Explore Options**: Dive deeper into specific aspects:
   - Project Overview: General information about the project
   - Project Documentation: Detailed project documents
   - Project Verification: Information about verification status
   - Project Credits: Carbon credits generated by the project
   - Project Impact: Environmental impact metrics
8. **Credit Exploration Flow** (if selected):
   - View list of credits associated with the project
   - Select a specific credit to view details
   - Explore credit options (pricing, tokenization, marketplace)
   - Return to project view
9. **Save to Favorites (Optional)**: Bookmark for future reference
10. **Continue Exploration**: Explore more aspects of the same project or different projects

## 3. Carbon Credit Exploration Journey

The process of exploring and understanding carbon credits across projects.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Carbon Credits]
    Navigate --> ViewList[View Carbon Credit List]
    ViewList --> FilterOptions{Filter Options}

    FilterOptions -->|By Project| FilterByProject[Filter by Project]
    FilterOptions -->|By Vintage| FilterByVintage[Filter by Vintage]
    FilterOptions -->|By Standard| FilterByStandard[Filter by Standard]
    FilterOptions -->|By Status| FilterByStatus[Filter by Status]
    FilterOptions -->|By Price| FilterByPrice[Filter by Price Range]

    FilterByProject --> ApplyFilters[Apply Filters]
    FilterByVintage --> ApplyFilters
    FilterByStandard --> ApplyFilters
    FilterByStatus --> ApplyFilters
    FilterByPrice --> ApplyFilters

    ApplyFilters --> SortCredits[Sort Credits by Attribute]
    SortCredits --> SelectCredit[Select Carbon Credit]
    SelectCredit --> ViewDetails[View Detailed Information]

    ViewDetails --> ExploreOption{Explore Options}

    ExploreOption -->|Parent Project| ParentProject[View Parent Project]
    ExploreOption -->|Verification Details| VerificationInfo[View Verification Details]
    ExploreOption -->|Pricing History| PricingInfo[View Pricing History]
    ExploreOption -->|Tokenization Status| TokenInfo[View Tokenization Status]
    ExploreOption -->|Marketplace Listing| MarketplaceListing[View Marketplace Listing]

    ParentProject --> ViewProjectDetails[View Project Details]
    VerificationInfo --> ViewVerifiers[View Verification Reports]
    PricingInfo --> ViewPriceChart[View Price Charts]
    TokenInfo --> ViewBlockchain[View Blockchain Details]
    MarketplaceListing --> ViewListing[View Listing Details]

    ViewProjectDetails --> ReturnToCredit[Return to Credit]
    ViewVerifiers --> SaveOption{Save for Later?}
    ViewPriceChart --> SaveOption
    ViewBlockchain --> SaveOption
    ViewListing --> MarketplaceAction{Marketplace Action}

    MarketplaceAction -->|View Orders| ViewOrders[View Buy/Sell Orders]
    MarketplaceAction -->|Return to Credit| SaveOption

    ViewOrders --> ReturnToListing[Return to Listing]
    ReturnToListing --> ViewListing
    ReturnToCredit --> ViewDetails

    SaveOption -->|Yes| SaveToFavorites[Save to Favorites]
    SaveOption -->|No| ExploreMore{Explore More?}

    SaveToFavorites --> ExploreMore

    ExploreMore -->|Yes| ExploreChoice{Explore Choice}
    ExploreMore -->|No| Complete([Exploration Complete])

    ExploreChoice -->|Same Credit| ExploreOption
    ExploreChoice -->|Different Credit| SelectCredit
    ExploreChoice -->|Return to List| ViewList

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style FilterOptions fill:#FFC107,stroke:#FFA000,color:black
    style ExploreOption fill:#FFC107,stroke:#FFA000,color:black
    style MarketplaceAction fill:#FFC107,stroke:#FFA000,color:black
    style SaveOption fill:#FFC107,stroke:#FFA000,color:black
    style ExploreMore fill:#FFC107,stroke:#FFA000,color:black
    style ExploreChoice fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Carbon Credits**: Access the carbon credits section
2. **View Carbon Credit List**: See available carbon credits
3. **Filter Options**: Choose filtering criteria:
   - By Project: Filter by parent carbon project
   - By Vintage: Filter by credit vintage year
   - By Standard: Filter by verification standard
   - By Status: Filter by credit status
   - By Price: Filter by price range
4. **Apply Filters**: Apply selected filters
5. **Sort Credits**: Arrange by price, vintage, etc.
6. **Select Carbon Credit**: Choose a specific credit to explore
7. **View Detailed Information**: See all credit information
8. **Explore Options**: Dive deeper into specific aspects:
   - Parent Project: View the project that generated the credit
   - Verification Details: Information about verification
   - Pricing History: Historical price data
   - Tokenization Status: Blockchain tokenization details
   - Marketplace Listing: View marketplace listing details
9. **Marketplace Action Flow** (if selected):
   - View buy/sell orders for the credit
   - Return to listing details
10. **Save to Favorites (Optional)**: Bookmark for future reference
11. **Continue Exploration**: Explore more aspects of the same credit, different credits, or return to the list

## 4. Wallet Interaction Journey

The process of viewing and interacting with blockchain wallets.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Wallet Section]
    Navigate --> ViewWallets[View Available Wallets]
    ViewWallets --> SelectWallet[Select Wallet]
    SelectWallet --> ViewBalance[View Wallet Balance]

    ViewBalance --> WalletAction{Select Action}

    WalletAction -->|View Transactions| ViewTransactions[View Transaction History]
    WalletAction -->|View Tokens| ViewTokens[View Token Holdings]
    WalletAction -->|View NFTs| ViewNFTs[View NFT Holdings]
    WalletAction -->|Send Tokens| RequestSend[Request Token Transfer]

    ViewTransactions --> FilterTransactions[Filter Transactions]
    ViewTokens --> TokenDetails[View Token Details]
    ViewNFTs --> NFTDetails[View NFT Details]
    RequestSend --> FillTransferForm[Fill Transfer Request Form]

    FilterTransactions --> TransactionDetails[View Transaction Details]
    TokenDetails --> TokenAction{Token Action}
    NFTDetails --> NFTAction{NFT Action}
    FillTransferForm --> SubmitRequest[Submit Transfer Request]

    TokenAction -->|View on Blockchain| ViewTokenChain[View Token on Blockchain]
    NFTAction -->|View on Blockchain| ViewNFTChain[View NFT on Blockchain]
    SubmitRequest --> WaitApproval[Wait for Approval]

    TransactionDetails --> ViewOnChain[View on Blockchain Explorer]
    ViewTokenChain --> Complete([Wallet Interaction Complete])
    ViewNFTChain --> Complete

    WaitApproval --> ApprovalStatus{Approval Status}
    ApprovalStatus -->|Approved| TransferComplete[Transfer Complete]
    ApprovalStatus -->|Rejected| ReviewFeedback[Review Rejection Feedback]

    TransferComplete --> ViewUpdatedBalance[View Updated Balance]
    ReviewFeedback --> ReviseRequest{Revise Request?}

    ViewUpdatedBalance --> Complete
    ViewOnChain --> Complete

    ReviseRequest -->|Yes| FillTransferForm
    ReviseRequest -->|No| Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style WalletAction fill:#FFC107,stroke:#FFA000,color:black
    style TokenAction fill:#FFC107,stroke:#FFA000,color:black
    style NFTAction fill:#FFC107,stroke:#FFA000,color:black
    style ApprovalStatus fill:#FFC107,stroke:#FFA000,color:black
    style ReviseRequest fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Wallet Section**: Access the wallet area
2. **View Available Wallets**: See wallets accessible to the user
3. **Select Wallet**: Choose a specific wallet to interact with
4. **View Wallet Balance**: See current token balances
5. **Select Action**: Choose from various wallet options:
   - View Transactions: See transaction history
   - View Tokens: See token holdings
   - View NFTs: See NFT holdings
   - Send Tokens: Request to transfer tokens
6. **Specific Action Flows**:
   - Transaction History: Filter and view transaction details
   - Token Details: View token information and blockchain details
   - NFT Details: View NFT information and blockchain details
   - Send Tokens: Submit transfer request and wait for approval
7. **Complete Action**: Finish the selected wallet interaction

## 5. Marketplace Participation Journey

The process of participating in the carbon credit marketplace.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Marketplace]
    Navigate --> ViewMarketplace[View Marketplace Dashboard]
    ViewMarketplace --> BrowseListings[Browse Credit Listings]
    BrowseListings --> FilterOptions{Filter Options}

    FilterOptions -->|By Project| FilterByProject[Filter by Project]
    FilterOptions -->|By Credit Type| FilterByType[Filter by Credit Type]
    FilterOptions -->|By Price Range| FilterByPrice[Filter by Price Range]
    FilterOptions -->|By Vintage| FilterByVintage[Filter by Vintage]
    FilterOptions -->|By Volume| FilterByVolume[Filter by Volume]

    FilterByProject --> ApplyFilters[Apply Filters]
    FilterByType --> ApplyFilters
    FilterByPrice --> ApplyFilters
    FilterByVintage --> ApplyFilters
    FilterByVolume --> ApplyFilters

    ApplyFilters --> SortListings[Sort Listings]
    SortListings --> SelectListing[Select Listing]
    SelectListing --> ViewListingDetails[View Listing Details]

    ViewListingDetails --> ListingAction{Listing Action}

    ListingAction -->|View Project| ViewProject[View Parent Project]
    ListingAction -->|View Credit Details| ViewCredit[View Credit Details]
    ListingAction -->|View Seller| ViewSeller[View Seller Information]
    ListingAction -->|View Price History| ViewPriceHistory[View Price History]
    ListingAction -->|Participate in Trading| TradingAction[Trading Action]

    ViewProject --> ReturnToListing[Return to Listing]
    ViewCredit --> ReturnToListing
    ViewSeller --> ReturnToListing
    ViewPriceHistory --> ReturnToListing

    TradingAction --> TradeOption{Trade Option}

    TradeOption -->|Place Buy Order| PlaceBuyOrder[Place Buy Order]
    TradeOption -->|View Orders| ViewOrders[View Existing Orders]
    TradeOption -->|Save to Watchlist| AddToWatchlist[Add to Watchlist]

    PlaceBuyOrder --> ConfigureOrder[Configure Order Parameters]
    ConfigureOrder --> ReviewOrder[Review Order Details]
    ReviewOrder --> ConfirmOrder[Confirm Order]
    ConfirmOrder --> OrderPlaced[Order Placed Successfully]

    ViewOrders --> OrderAction{Order Action}
    OrderAction -->|View Order Details| ViewOrderDetails[View Order Details]
    OrderAction -->|Cancel Order| CancelOrder[Cancel Existing Order]
    OrderAction -->|Modify Order| ModifyOrder[Modify Order Parameters]

    ViewOrderDetails --> ReturnToOrders[Return to Orders]
    CancelOrder --> ConfirmCancellation[Confirm Cancellation]
    ModifyOrder --> ConfigureNewParams[Configure New Parameters]

    ConfirmCancellation --> OrderCancelled[Order Cancelled]
    ConfigureNewParams --> ConfirmModification[Confirm Modification]
    ConfirmModification --> OrderModified[Order Modified]

    AddToWatchlist --> ConfigureAlerts[Configure Price Alerts]
    ConfigureAlerts --> SaveWatchlist[Save to Watchlist]

    OrderPlaced --> ViewOrderStatus[View Order Status]
    OrderCancelled --> ReturnToOrders
    OrderModified --> ReturnToOrders
    SaveWatchlist --> ReturnToListing

    ReturnToListing --> ViewListingDetails
    ReturnToOrders --> ViewOrders
    ViewOrderStatus --> Complete([Participation Complete])

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style FilterOptions fill:#FFC107,stroke:#FFA000,color:black
    style ListingAction fill:#FFC107,stroke:#FFA000,color:black
    style TradeOption fill:#FFC107,stroke:#FFA000,color:black
    style OrderAction fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Marketplace**: Access the marketplace section
2. **View Marketplace Dashboard**: See marketplace overview
3. **Browse Credit Listings**: View available carbon credit listings
4. **Filter Options**: Choose filtering criteria:
   - By Project: Filter by parent carbon project
   - By Credit Type: Filter by credit type
   - By Price Range: Filter by price range
   - By Vintage: Filter by credit vintage year
   - By Volume: Filter by available volume
5. **Apply Filters**: Apply selected filters
6. **Sort Listings**: Arrange by price, date, etc.
7. **Select Listing**: Choose a specific listing to explore
8. **View Listing Details**: See detailed information about the listing
9. **Listing Action**: Choose an action:
   - View Project: See the parent project details
   - View Credit Details: See the carbon credit details
   - View Seller: See information about the seller
   - View Price History: See historical price data
   - Participate in Trading: Engage in marketplace trading
10. **Trading Action Flow**:
    - Place Buy Order: Create a new buy order
    - View Orders: See existing orders
    - Save to Watchlist: Add listing to watchlist
11. **Buy Order Flow**:
    - Configure order parameters (quantity, price, etc.)
    - Review order details
    - Confirm order
    - View order status
12. **Order Management Flow**:
    - View order details
    - Cancel existing order
    - Modify order parameters
13. **Watchlist Flow**:
    - Configure price alerts
    - Save to watchlist

## 6. Carbon Credit Retirement Journey

The process of retiring carbon credits for offsetting purposes.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Carbon Credits]
    Navigate --> ViewCredits[View Available Credits]
    ViewCredits --> SelectCredit[Select Credit to Retire]
    SelectCredit --> InitiateRetirement[Initiate Retirement Process]

    InitiateRetirement --> EnterDetails[Enter Retirement Details]
    EnterDetails --> SpecifyQuantity[Specify Quantity to Retire]
    SpecifyQuantity --> EnterReason[Enter Retirement Reason]
    EnterReason --> EnterBeneficiary[Enter Beneficiary Information]

    EnterBeneficiary --> ReviewDetails[Review Retirement Details]
    ReviewDetails --> ConfirmRetirement[Confirm Retirement]
    ConfirmRetirement --> ProcessingStatus{Processing Status}

    ProcessingStatus -->|Success| RetirementComplete[Retirement Complete]
    ProcessingStatus -->|Failed| ErrorHandling[View Error Details]

    ErrorHandling --> RetryOptions{Retry Options}
    RetryOptions -->|Adjust Parameters| EnterDetails
    RetryOptions -->|Cancel| Cancelled[Retirement Cancelled]

    RetirementComplete --> ViewCertificate[View Retirement Certificate]
    ViewCertificate --> ShareOption{Share Certificate?}

    ShareOption -->|Yes| ShareCertificate[Share Certificate]
    ShareOption -->|No| ViewImpact[View Environmental Impact]

    ShareCertificate --> ViewImpact
    Cancelled --> Complete([Process Complete])

    ViewImpact --> Complete

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style ProcessingStatus fill:#FFC107,stroke:#FFA000,color:black
    style RetryOptions fill:#FFC107,stroke:#FFA000,color:black
    style ShareOption fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Carbon Credits**: Access the carbon credits section
2. **View Available Credits**: See credits available for retirement
3. **Select Credit to Retire**: Choose a specific credit
4. **Initiate Retirement Process**: Start the retirement workflow
5. **Enter Retirement Details**: Provide necessary information:
   - Quantity to retire
   - Retirement reason
   - Beneficiary information
6. **Review Retirement Details**: Verify all information
7. **Confirm Retirement**: Approve the retirement transaction
8. **Processing Status**: Monitor the retirement process
   - If successful, proceed to certificate
   - If failed, review error details and retry
9. **View Retirement Certificate**: Access the official certificate
10. **Share Certificate (Optional)**: Share with stakeholders
11. **View Environmental Impact**: See the positive impact created

## 7. Analytics Exploration Journey

The process of accessing and analyzing data on the platform.

```mermaid
flowchart TD
    Start([Start]) --> Navigate[Navigate to Analytics Dashboard]
    Navigate --> ViewOverview[View Analytics Overview]
    ViewOverview --> SelectMetric{Select Metric to Analyze}

    SelectMetric -->|Project Performance| ProjectPerformance[View Project Performance]
    SelectMetric -->|Carbon Impact| CarbonImpact[View Carbon Impact Metrics]
    SelectMetric -->|Transaction History| TxHistory[View Transaction History]
    SelectMetric -->|Credit Activity| CreditActivity[View Credit Activity]
    SelectMetric -->|Marketplace Activity| MarketplaceActivity[View Marketplace Activity]
    SelectMetric -->|Personal Activity| PersonalActivity[View Personal Activity]

    ProjectPerformance --> FilterOptions{Filter Options}
    CarbonImpact --> FilterOptions
    TxHistory --> FilterOptions
    CreditActivity --> FilterOptions
    MarketplaceActivity --> FilterOptions
    PersonalActivity --> FilterOptions

    FilterOptions -->|By Project| FilterByProject[Filter by Project]
    FilterOptions -->|By Date Range| FilterByDate[Filter by Date Range]
    FilterOptions -->|By Credit Type| FilterByType[Filter by Credit Type]
    FilterOptions -->|By Activity Type| FilterByActivity[Filter by Activity Type]

    FilterByProject --> ApplyFilters[Apply Filters]
    FilterByDate --> ApplyFilters
    FilterByType --> ApplyFilters
    FilterByActivity --> ApplyFilters

    ApplyFilters --> ViewDetailed[View Detailed Analysis]
    ViewDetailed --> VisualizationOption{Visualization Option}

    VisualizationOption -->|Chart| ViewChart[View as Chart]
    VisualizationOption -->|Table| ViewTable[View as Table]
    VisualizationOption -->|Summary| ViewSummary[View as Summary]
    VisualizationOption -->|Dashboard| ViewDashboard[View as Dashboard]

    ViewChart --> CompareOption{Compare Data?}
    ViewTable --> CompareOption
    ViewSummary --> CompareOption
    ViewDashboard --> CompareOption

    CompareOption -->|Yes| SelectComparison[Select Comparison Parameters]
    CompareOption -->|No| ExportOption{Export Data?}

    SelectComparison --> ViewComparison[View Comparison]
    ViewComparison --> ExportOption

    ExportOption -->|Yes| ExportFormat[Select Export Format]
    ExportOption -->|No| ShareOption{Share Analysis?}

    ExportFormat --> ExportData[Export Analytics Data]
    ExportData --> ShareOption

    ShareOption -->|Yes| ConfigureSharing[Configure Sharing Options]
    ShareOption -->|No| AnalyzeMore{Analyze More?}

    ConfigureSharing --> ShareAnalysis[Share Analysis]
    ShareAnalysis --> AnalyzeMore

    AnalyzeMore -->|Yes| SelectMetric
    AnalyzeMore -->|No| Complete([Analytics Complete])

    style Start fill:#4CAF50,stroke:#388E3C,color:white
    style Complete fill:#4CAF50,stroke:#388E3C,color:white
    style SelectMetric fill:#FFC107,stroke:#FFA000,color:black
    style FilterOptions fill:#FFC107,stroke:#FFA000,color:black
    style VisualizationOption fill:#FFC107,stroke:#FFA000,color:black
    style CompareOption fill:#FFC107,stroke:#FFA000,color:black
    style ExportOption fill:#FFC107,stroke:#FFA000,color:black
    style ShareOption fill:#FFC107,stroke:#FFA000,color:black
    style AnalyzeMore fill:#FFC107,stroke:#FFA000,color:black
```

### Journey Steps:

1. **Navigate to Analytics Dashboard**: Access the analytics section
2. **View Analytics Overview**: See high-level metrics
3. **Select Metric to Analyze**: Choose a specific area to explore:
   - Project Performance: Metrics about carbon projects
   - Carbon Impact: Environmental impact metrics
   - Transaction History: Financial transaction data
   - Credit Activity: Carbon credit activity
   - Marketplace Activity: Trading and listing activity
   - Personal Activity: User's platform activity
4. **Filter Options**: Choose filtering criteria:
   - By Project: Filter by specific carbon projects
   - By Date Range: Filter by time period
   - By Credit Type: Filter by carbon credit type
   - By Activity Type: Filter by activity type
5. **Apply Filters**: Apply selected filters
6. **View Detailed Analysis**: Explore specific data points
7. **Visualization Option**: Choose how to view the data:
   - Chart: Visual representation
   - Table: Tabular data
   - Summary: Condensed overview
   - Dashboard: Interactive dashboard
8. **Compare Data (Optional)**: Compare different datasets
9. **Export Data (Optional)**: Download data for further analysis
10. **Share Analysis (Optional)**: Share insights with team members
11. **Continue Analysis**: Explore more metrics or complete

## Success Metrics

The Regular User journey is successful when:

1. User can easily navigate and use the platform
2. Projects and their details can be explored and understood
3. Carbon credits can be explored with project context
4. Marketplace participation is intuitive and efficient
5. Wallet interactions are completed successfully
6. Carbon credit retirements are processed correctly
7. Analytics provide useful project and credit insights

## Common Challenges and Solutions

| Challenge | Solution |
|-----------|----------|
| Platform navigation complexity | Interactive tutorials and tooltips |
| Project structure understanding | Project visualization tools and guides |
| Understanding carbon credit details | Simplified explanations with project context |
| Marketplace participation confusion | Guided marketplace tutorials and simulations |
| Wallet interaction complexity | Step-by-step guidance for blockchain operations |
| Retirement process complexity | Streamlined workflow with clear instructions |
| Project data interpretation difficulty | Pre-configured project analytics views |
| Cross-project comparison challenges | Standardized metrics across projects |
