# Prisma Migration Guide

This guide explains how to use the Prisma migration scripts to manage your database schema in different scenarios.

## Overview

The Carbonx application uses Prisma as its ORM (Object-Relational Mapping) tool to interact with the PostgreSQL database. Prisma provides a way to define your database schema in a declarative way using the Prisma schema language, and then generate the necessary SQL migrations to apply those changes to your database.

We have created several scripts to help manage Prisma migrations in different scenarios:

1. **Basic Migration**: Standard Prisma migration commands for development and deployment
2. **Fix Migration**: A script to fix migration issues by resetting the database and creating a clean migration
3. **Advanced Migration**: A comprehensive script with multiple options for different migration scenarios

## 1. Basic Migration Commands

### Development Migration

For local development, you can use the following command to create and apply a new migration:

```bash
pnpm prisma:migrate
# or
npx prisma migrate dev
```

This will:
1. Compare your Prisma schema with the current state of the database
2. Create a new migration if there are changes
3. Apply the migration to your database
4. Generate the Prisma client

### Deployment Migration

For production deployments, you should use the following command to apply existing migrations:

```bash
pnpm prisma:deploy
# or
npx prisma migrate deploy
```

This will apply any pending migrations to your database without creating new ones.

### Docker Migration

If you're using Docker for development, you can use the following command to run migrations against the dockerized PostgreSQL:

```bash
pnpm prisma:migrate:docker
```

## 2. Fix Migration Script

If you're experiencing issues with migrations, you can use the fix migration script to reset the database and create a clean migration:

```bash
pnpm prisma:fix
# or
./scripts/fix-migration.sh
```

This script will:
1. Reset the database (drop all tables)
2. Create a new migration
3. Apply the migration
4. Generate the Prisma client
5. Run the deployment seed

**Warning**: This will delete all data in your database. Only use this in development or when setting up a new environment.

## 3. Advanced Migration Script

For more advanced migration scenarios, you can use the advanced migration script:

```bash
pnpm prisma:migrate:advanced [OPTIONS]
# or
./scripts/prisma-migrate.sh [OPTIONS]
```

### Options

- `-h, --help`: Show help message
- `-d, --dev`: Run a development migration (prisma migrate dev)
- `-p, --deploy`: Run a deployment migration (prisma migrate deploy)
- `-r, --reset`: Reset the database and run a clean migration
- `-f, --fix`: Fix migration issues by resetting and creating a clean migration
- `-s, --schema-push`: Push the schema directly to the database (bypasses migrations)
- `-c, --create-only NAME`: Create a migration without applying it
- `-g, --generate`: Generate Prisma client
- `--seed`: Run the deployment seed after migration

### Examples

```bash
# Run a development migration
pnpm prisma:migrate:advanced -d

# Run a deployment migration
pnpm prisma:migrate:advanced -p

# Reset the database and run a clean migration
pnpm prisma:migrate:advanced -r
# or
pnpm prisma:reset

# Fix migration issues
pnpm prisma:migrate:advanced -f

# Push the schema directly to the database
pnpm prisma:migrate:advanced -s
# or
pnpm prisma:push

# Create a migration named 'initial_migration' without applying it
pnpm prisma:migrate:advanced -c initial_migration

# Run a development migration and seed the database
pnpm prisma:migrate:advanced -d --seed
```

## 4. Schema Push

In some cases, you may want to bypass migrations entirely and push your schema directly to the database. This is useful when you're in early development or when migrations are failing:

```bash
pnpm prisma:push
# or
pnpm prisma:migrate:advanced -s
```

**Warning**: This will reset your database and apply the schema directly. All data will be lost. Only use this in development.

## 5. Troubleshooting

### Common Issues

1. **Migration failed to apply cleanly to the shadow database**:
   - This usually happens when there's a conflict between your schema and the current state of the database
   - Try using the fix migration script: `pnpm prisma:fix`

2. **Constraint already exists**:
   - This happens when a constraint is being created that already exists in the database
   - Try using the schema push command: `pnpm prisma:push`

3. **Migration history conflict**:
   - This happens when the migration history in the database doesn't match the migration files
   - Try resetting the database: `pnpm prisma:reset`

### Logs

All migration scripts create detailed logs in the `logs` directory. If something goes wrong, check the log file for more information.

## 6. Best Practices

1. **Always backup your database before running migrations in production**
2. **Use `prisma migrate dev` for development and `prisma migrate deploy` for production**
3. **Keep your migrations small and focused on specific changes**
4. **Test migrations in a staging environment before applying them to production**
5. **Use the deployment seed script to create essential data after migrations**
6. **Monitor the logs for any issues during migration**

## 7. Additional Resources

- [Prisma Migration Documentation](https://www.prisma.io/docs/concepts/components/prisma-migrate)
- [Prisma Schema Documentation](https://www.prisma.io/docs/concepts/components/prisma-schema)
- [Prisma Client Documentation](https://www.prisma.io/docs/concepts/components/prisma-client)
