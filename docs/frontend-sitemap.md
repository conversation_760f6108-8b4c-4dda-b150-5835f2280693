# Frontend Sitemap for Carbonix Platform

This document outlines the complete sitemap for the Carbonix platform frontend, based on the user journeys documented in the user journey documentation. The sitemap is organized by main sections and includes all pages required to support the various user roles and their journeys.

## Sitemap Overview

```mermaid
flowchart TD
    Home[Home/Landing Page] --> Auth[Authentication]
    Home --> Marketing[Marketing Pages]

    Auth --> Login[Login]
    Auth --> Register[Registration]
    Auth --> PasswordReset[Password Reset]
    Auth --> EmailVerification[Email Verification]

    Login --> Dashboard[Dashboard]

    Dashboard --> Projects[Projects]
    Dashboard --> CarbonCredits[Carbon Credits]
    Dashboard --> Marketplace[Marketplace]
    Dashboard --> Wallet[Wallet]
    Dashboard --> Analytics[Analytics]
    Dashboard --> Compliance[Compliance]
    Dashboard --> Settings[Settings]
    Dashboard --> Admin[Admin Panel]
    Dashboard --> Teams[Teams]
    Dashboard --> Notifications[Notifications]

    subgraph Marketing Pages
        About[About]
        HowItWorks[How It Works]
        Contact[Contact]
        Pricing[Pricing]
        Blog[Blog]
        FAQ[FAQ]
    end

    style Home fill:#4CAF50,stroke:#388E3C,color:white
    style Dashboard fill:#2196F3,stroke:#1976D2,color:white
    style Auth fill:#FFC107,stroke:#FFA000,color:black
```

## Detailed Sitemap

### 1. Authentication & Onboarding

```mermaid
flowchart TD
    Auth[Authentication] --> Login[Login]
    Auth --> Register[Registration]
    Auth --> PasswordReset[Password Reset]
    Auth --> EmailVerification[Email Verification]

    Register --> OrgRegistration[Organization Registration]
    OrgRegistration --> OrgDetails[Organization Details]
    OrgDetails --> VerificationDocs[Verification Documents]
    VerificationDocs --> WalletSetup[Wallet Setup]
    WalletSetup --> TeamSetup[Team Setup]
    TeamSetup --> OnboardingComplete[Onboarding Complete]

    Login --> MFA[Multi-Factor Authentication]
    PasswordReset --> RequestReset[Request Reset]
    PasswordReset --> ConfirmReset[Confirm Reset]

    style Auth fill:#FFC107,stroke:#FFA000,color:black
```

#### Pages:
- **/login** - User login page
- **/register** - User registration page
- **/password-reset** - Password reset request page
- **/password-reset/confirm** - Password reset confirmation page
- **/email-verification** - Email verification page
- **/onboarding** - Main onboarding page
- **/onboarding/organization** - Organization registration
- **/onboarding/verification** - Document verification upload
- **/onboarding/wallet** - Wallet setup
- **/onboarding/team** - Team setup
- **/onboarding/complete** - Onboarding completion

### 2. Dashboard

```mermaid
flowchart TD
    Dashboard[Dashboard] --> UserDashboard[User Dashboard]
    Dashboard --> OrgDashboard[Organization Dashboard]
    Dashboard --> AdminDashboard[Admin Dashboard]

    UserDashboard --> UserOverview[User Overview]
    UserDashboard --> UserActivity[User Activity]
    UserDashboard --> UserNotifications[User Notifications]

    OrgDashboard --> OrgOverview[Organization Overview]
    OrgDashboard --> OrgMetrics[Organization Metrics]
    OrgDashboard --> OrgActivity[Organization Activity]

    AdminDashboard --> PlatformOverview[Platform Overview]
    AdminDashboard --> SystemHealth[System Health]
    AdminDashboard --> AdminActivity[Admin Activity]

    style Dashboard fill:#2196F3,stroke:#1976D2,color:white
```

#### Pages:
- **/dashboard** - Main dashboard (redirects based on role)
- **/dashboard/user** - User-specific dashboard
- **/dashboard/organization** - Organization dashboard
- **/dashboard/admin** - Admin dashboard
- **/dashboard/activity** - User activity feed
- **/dashboard/notifications** - Notifications center

### 3. Projects

```mermaid
flowchart TD
    Projects[Projects] --> ProjectsList[Projects List]
    Projects --> CreateProject[Create Project]
    Projects --> ProjectDetails[Project Details]
    Projects --> ProjectPortfolio[Project Portfolio]
    Projects --> BatchOperations[Batch Operations]

    CreateProject --> ProjectType[Select Project Type]
    CreateProject --> ProjectInfo[Project Information]
    CreateProject --> ProjectDocuments[Project Documents]
    CreateProject --> ProjectVerification[Project Verification]

    ProjectType --> RenewableEnergy[Renewable Energy]
    ProjectType --> Forestry[Forestry]
    ProjectType --> MethaneReduction[Methane Reduction]
    ProjectType --> EnergyEfficiency[Energy Efficiency]
    ProjectType --> OtherType[Other Type]

    ProjectDetails --> ProjectOverview[Project Overview]
    ProjectDetails --> ProjectCredits[Project Credits]
    ProjectDetails --> ProjectDocumentation[Project Documentation]
    ProjectDetails --> ProjectVerificationStatus[Verification Status]
    ProjectDetails --> ProjectImpact[Environmental Impact]
    ProjectDetails --> ProjectFinancials[Project Financials]
    ProjectDetails --> EditProject[Edit Project]

    ProjectCredits --> ViewCredits[View Credits]
    ProjectCredits --> CreateCredit[Create Credit]
    ProjectCredits --> ImportCredits[Import Credits]
    ProjectCredits --> ExportCredits[Export Credits]

    ProjectPortfolio --> PortfolioOverview[Portfolio Overview]
    ProjectPortfolio --> PortfolioAnalytics[Portfolio Analytics]
    ProjectPortfolio --> PortfolioComparison[Portfolio Comparison]
    ProjectPortfolio --> PortfolioReporting[Portfolio Reporting]

    style Projects fill:#4CAF50,stroke:#388E3C,color:white
```

#### Pages:
- **/projects** - Projects list
- **/projects/create** - Create new project
- **/projects/create/type** - Select project type
- **/projects/create/info** - Project information
- **/projects/create/documents** - Project documents
- **/projects/create/verification** - Project verification
- **/projects/batch** - Batch operations
- **/projects/portfolio** - Project portfolio
- **/projects/portfolio/overview** - Portfolio overview
- **/projects/portfolio/analytics** - Portfolio analytics
- **/projects/portfolio/comparison** - Portfolio comparison
- **/projects/portfolio/reporting** - Portfolio reporting
- **/projects/[id]** - Project details
- **/projects/[id]/overview** - Project overview
- **/projects/[id]/credits** - Project credits
- **/projects/[id]/credits/create** - Create credit for project
- **/projects/[id]/credits/import** - Import credits for project
- **/projects/[id]/credits/export** - Export project credits
- **/projects/[id]/documentation** - Project documentation
- **/projects/[id]/verification** - Verification status
- **/projects/[id]/impact** - Environmental impact
- **/projects/[id]/financials** - Project financials
- **/projects/[id]/edit** - Edit project

### 4. Carbon Credits

```mermaid
flowchart TD
    CarbonCredits[Carbon Credits] --> CreditsList[Credits List]
    CarbonCredits --> CreateCredit[Create Credit]
    CarbonCredits --> CreditDetails[Credit Details]
    CarbonCredits --> BatchOperations[Batch Operations]
    CarbonCredits --> CreditFilters[Credit Filters]

    CreditFilters --> FilterByProject[Filter by Project]
    CreditFilters --> FilterByVintage[Filter by Vintage]
    CreditFilters --> FilterByStandard[Filter by Standard]
    CreditFilters --> FilterByStatus[Filter by Status]

    CreateCredit --> SelectProject[Select Project]
    CreateCredit --> ManualEntry[Manual Entry]
    CreateCredit --> BulkImport[Bulk Import]
    CreateCredit --> APIImport[API Import]

    CreditDetails --> ParentProject[Parent Project]
    CreditDetails --> CreditOverview[Credit Overview]
    CreditDetails --> EditCredit[Edit Credit]
    CreditDetails --> VerifyCredit[Verify Credit]
    CreditDetails --> TokenizeCredit[Tokenize Credit]
    CreditDetails --> RetireCredit[Retire Credit]
    CreditDetails --> ListCredit[List on Marketplace]

    TokenizeCredit --> SelectNetwork[Select Network]
    TokenizeCredit --> TokenizationDetails[Tokenization Details]
    TokenizeCredit --> TokenizationStatus[Tokenization Status]

    RetireCredit --> RetirementDetails[Retirement Details]
    RetireCredit --> RetirementCertificate[Retirement Certificate]
    RetireCredit --> RetirementImpact[Retirement Impact]

    style CarbonCredits fill:#4CAF50,stroke:#388E3C,color:white
```

#### Pages:
- **/carbon-credits** - Carbon credits list
- **/carbon-credits/filter** - Filter credits
- **/carbon-credits/filter/project** - Filter by project
- **/carbon-credits/filter/vintage** - Filter by vintage
- **/carbon-credits/filter/standard** - Filter by standard
- **/carbon-credits/filter/status** - Filter by status
- **/carbon-credits/create** - Create new carbon credit
- **/carbon-credits/create/project** - Select project
- **/carbon-credits/create/manual** - Manual credit entry
- **/carbon-credits/create/import** - Bulk import
- **/carbon-credits/create/api** - API import
- **/carbon-credits/batch** - Batch operations
- **/carbon-credits/[id]** - Carbon credit details
- **/carbon-credits/[id]/project** - Parent project
- **/carbon-credits/[id]/overview** - Credit overview
- **/carbon-credits/[id]/edit** - Edit carbon credit
- **/carbon-credits/[id]/verify** - Verify carbon credit
- **/carbon-credits/[id]/tokenize** - Tokenize carbon credit
- **/carbon-credits/[id]/tokenize/status** - Tokenization status
- **/carbon-credits/[id]/retire** - Retire carbon credit
- **/carbon-credits/[id]/retire/certificate** - Retirement certificate
- **/carbon-credits/[id]/retire/impact** - Retirement impact
- **/carbon-credits/[id]/list** - List credit on marketplace

### 5. Marketplace

```mermaid
flowchart TD
    Marketplace[Marketplace] --> MarketplaceHome[Marketplace Home]
    Marketplace --> SearchListings[Search Listings]
    Marketplace --> ListingDetails[Listing Details]
    Marketplace --> CreateOrder[Create Order]
    Marketplace --> OrderBook[Order Book]
    Marketplace --> MyOrders[My Orders]
    Marketplace --> MarketplaceAnalytics[Marketplace Analytics]
    Marketplace --> Watchlist[Watchlist]

    SearchListings --> FilterListings[Filter Listings]
    FilterListings --> FilterByProject[Filter by Project]
    FilterListings --> FilterByType[Filter by Credit Type]
    FilterListings --> FilterByPrice[Filter by Price Range]
    FilterListings --> FilterByVintage[Filter by Vintage]
    FilterListings --> FilterByVolume[Filter by Volume]
    SearchListings --> SortListings[Sort Listings]

    ListingDetails --> ViewProject[View Project]
    ListingDetails --> ViewCreditDetails[View Credit Details]
    ListingDetails --> ViewSellerInfo[View Seller Information]
    ListingDetails --> ViewPriceHistory[View Price History]
    ListingDetails --> ViewOrderBook[View Order Book]
    ListingDetails --> PlaceBuyOrder[Place Buy Order]
    ListingDetails --> PlaceSellOrder[Place Sell Order]
    ListingDetails --> AddToWatchlist[Add to Watchlist]

    CreateOrder --> OrderType[Select Order Type]
    OrderType --> MarketOrder[Market Order]
    OrderType --> LimitOrder[Limit Order]
    OrderType --> StopOrder[Stop Order]
    CreateOrder --> OrderDetails[Order Details]
    OrderDetails --> PriceStrategy[Pricing Strategy]
    PriceStrategy --> FixedPrice[Fixed Price]
    PriceStrategy --> AuctionPrice[Auction]
    PriceStrategy --> DynamicPrice[Dynamic Pricing]
    PriceStrategy --> TieredPrice[Tiered Pricing]
    OrderDetails --> OrderQuantity[Order Quantity]
    OrderDetails --> OrderExpiration[Order Expiration]
    CreateOrder --> ConfirmOrder[Confirm Order]

    MyOrders --> ActiveOrders[Active Orders]
    MyOrders --> PendingOrders[Pending Orders]
    MyOrders --> CompletedOrders[Completed Orders]
    MyOrders --> OrderHistory[Order History]
    MyOrders --> CancelOrder[Cancel Order]
    MyOrders --> ModifyOrder[Modify Order]

    MarketplaceAnalytics --> PriceTrends[Price Trends]
    MarketplaceAnalytics --> VolumeAnalysis[Volume Analysis]
    MarketplaceAnalytics --> MarketDepth[Market Depth]
    MarketplaceAnalytics --> ProjectPerformance[Project Performance]

    Watchlist --> WatchlistItems[Watchlist Items]
    Watchlist --> PriceAlerts[Price Alerts]
    Watchlist --> ManageWatchlist[Manage Watchlist]

    style Marketplace fill:#9C27B0,stroke:#7B1FA2,color:white
```

#### Pages:
- **/marketplace** - Marketplace home
- **/marketplace/search** - Search listings
- **/marketplace/search/filter** - Filter listings
- **/marketplace/search/filter/project** - Filter by project
- **/marketplace/search/filter/type** - Filter by credit type
- **/marketplace/search/filter/price** - Filter by price range
- **/marketplace/search/filter/vintage** - Filter by vintage
- **/marketplace/search/filter/volume** - Filter by volume
- **/marketplace/listings/[id]** - Listing details
- **/marketplace/listings/[id]/project** - View project
- **/marketplace/listings/[id]/credit** - View credit details
- **/marketplace/listings/[id]/seller** - View seller information
- **/marketplace/listings/[id]/price-history** - View price history
- **/marketplace/order-book/[id]** - Order book for specific credit
- **/marketplace/create-order/[id]** - Create order
- **/marketplace/create-order/[id]/type** - Select order type
- **/marketplace/create-order/[id]/details** - Order details
- **/marketplace/create-order/[id]/pricing** - Pricing strategy
- **/marketplace/create-order/[id]/confirm** - Confirm order
- **/marketplace/my-orders** - User's orders
- **/marketplace/my-orders/active** - Active orders
- **/marketplace/my-orders/pending** - Pending orders
- **/marketplace/my-orders/completed** - Completed orders
- **/marketplace/my-orders/history** - Order history
- **/marketplace/my-orders/[id]/cancel** - Cancel order
- **/marketplace/my-orders/[id]/modify** - Modify order
- **/marketplace/analytics** - Marketplace analytics
- **/marketplace/analytics/prices** - Price trends
- **/marketplace/analytics/volume** - Volume analysis
- **/marketplace/analytics/depth** - Market depth
- **/marketplace/analytics/projects** - Project performance
- **/marketplace/watchlist** - Watchlist
- **/marketplace/watchlist/alerts** - Price alerts
- **/marketplace/watchlist/manage** - Manage watchlist

### 6. Wallet

```mermaid
flowchart TD
    Wallet[Wallet] --> WalletDashboard[Wallet Dashboard]
    Wallet --> CreateWallet[Create Wallet]
    Wallet --> WalletDetails[Wallet Details]
    Wallet --> SendTokens[Send Tokens]
    Wallet --> ReceiveTokens[Receive Tokens]
    Wallet --> TransactionHistory[Transaction History]
    Wallet --> MultiChainView[Multi-Chain View]
    Wallet --> WalletSecurity[Wallet Security]

    CreateWallet --> WalletType[Select Wallet Type]
    CreateWallet --> NetworkSelection[Select Network]
    CreateWallet --> SecuritySetup[Security Setup]
    CreateWallet --> BackupOptions[Backup & Recovery Options]

    WalletDetails --> TokenHoldings[Token Holdings]
    WalletDetails --> NFTHoldings[NFT Holdings]
    WalletDetails --> WalletActivity[Wallet Activity]

    SendTokens --> RecipientDetails[Recipient Details]
    SendTokens --> AmountDetails[Amount Details]
    SendTokens --> ReviewTransaction[Review Transaction]
    SendTokens --> ConfirmTransaction[Confirm Transaction]

    MultiChainView --> NetworkSelector[Network Selector]
    MultiChainView --> EthereumWallets[Ethereum Wallets]
    MultiChainView --> PolygonWallets[Polygon Wallets]
    MultiChainView --> ArbitrumWallets[Arbitrum Wallets]
    MultiChainView --> OptimismWallets[Optimism Wallets]
    MultiChainView --> BaseWallets[Base Wallets]
    MultiChainView --> CrossChainTransfer[Cross-Chain Transfer]

    CrossChainTransfer --> SelectSourceNetwork[Select Source Network]
    CrossChainTransfer --> SelectDestNetwork[Select Destination Network]
    CrossChainTransfer --> SelectAsset[Select Asset]
    CrossChainTransfer --> ReviewBridgeFees[Review Bridge Fees]
    CrossChainTransfer --> BridgeStatus[Bridge Status]

    WalletSecurity --> SecurityStatus[Security Status]
    WalletSecurity --> AccessControl[Access Control]
    WalletSecurity --> SecuritySettings[Security Settings]
    WalletSecurity --> WalletAudit[Wallet Audit Logs]
    WalletSecurity --> ComplianceCheck[Compliance Check]

    SecuritySettings --> RecoveryOptions[Recovery Options]
    SecuritySettings --> TwoFactorAuth[Two-Factor Authentication]
    SecuritySettings --> TransactionLimits[Transaction Limits]
    SecuritySettings --> ApprovalRules[Approval Rules]

    style Wallet fill:#FF9800,stroke:#F57C00,color:black
```

#### Pages:
- **/wallet** - Wallet dashboard
- **/wallet/create** - Create new wallet
- **/wallet/create/type** - Select wallet type
- **/wallet/create/network** - Select network
- **/wallet/create/security** - Security setup
- **/wallet/create/backup** - Backup & recovery options
- **/wallet/[id]** - Wallet details
- **/wallet/[id]/tokens** - Token holdings
- **/wallet/[id]/nfts** - NFT holdings
- **/wallet/[id]/activity** - Wallet activity
- **/wallet/[id]/send** - Send tokens
- **/wallet/[id]/receive** - Receive tokens
- **/wallet/[id]/transactions** - Transaction history
- **/wallet/multi-chain** - Multi-chain view
- **/wallet/multi-chain/ethereum** - Ethereum wallets
- **/wallet/multi-chain/polygon** - Polygon wallets
- **/wallet/multi-chain/arbitrum** - Arbitrum wallets
- **/wallet/multi-chain/optimism** - Optimism wallets
- **/wallet/multi-chain/base** - Base wallets
- **/wallet/cross-chain** - Cross-chain transfer
- **/wallet/cross-chain/source** - Select source network
- **/wallet/cross-chain/destination** - Select destination network
- **/wallet/cross-chain/asset** - Select asset to bridge
- **/wallet/cross-chain/fees** - Review bridge fees
- **/wallet/cross-chain/status** - Bridge status
- **/wallet/security** - Wallet security
- **/wallet/security/status** - Security status
- **/wallet/security/access** - Access control
- **/wallet/security/settings** - Security settings
- **/wallet/security/settings/recovery** - Recovery options
- **/wallet/security/settings/2fa** - Two-factor authentication
- **/wallet/security/settings/limits** - Transaction limits
- **/wallet/security/settings/approvals** - Approval rules
- **/wallet/security/audit** - Wallet audit logs
- **/wallet/security/compliance** - Compliance check

### 7. Analytics

```mermaid
flowchart TD
    Analytics[Analytics] --> AnalyticsDashboard[Analytics Dashboard]
    Analytics --> CarbonImpact[Carbon Impact]
    Analytics --> FinancialMetrics[Financial Metrics]
    Analytics --> TradingActivity[Trading Activity]
    Analytics --> UserActivity[User Activity]
    Analytics --> CustomReports[Custom Reports]
    Analytics --> TransactionAudit[Transaction Audit]
    Analytics --> AssetValuation[Asset Valuation]

    CarbonImpact --> EmissionsReduced[Emissions Reduced]
    CarbonImpact --> CreditsRetired[Credits Retired]
    CarbonImpact --> ImpactVisualization[Impact Visualization]

    FinancialMetrics --> TransactionVolume[Transaction Volume]
    FinancialMetrics --> Revenue[Revenue]
    FinancialMetrics --> Expenses[Expenses]
    FinancialMetrics --> WalletBalances[Wallet Balances]
    FinancialMetrics --> CarbonAssetValue[Carbon Asset Value]
    FinancialMetrics --> PeriodComparison[Period Comparison]
    FinancialMetrics --> TrendAnalysis[Trend Analysis]

    TradingActivity --> OrderAnalytics[Order Analytics]
    TradingActivity --> MarketTrends[Market Trends]
    TradingActivity --> PriceHistory[Price History]

    TransactionAudit --> AuditDashboard[Audit Dashboard]
    TransactionAudit --> TransactionList[Transaction List]
    TransactionAudit --> AuditDetails[Audit Details]
    TransactionAudit --> FlaggedTransactions[Flagged Transactions]
    TransactionAudit --> AuditReports[Audit Reports]

    AssetValuation --> ValuationDashboard[Valuation Dashboard]
    AssetValuation --> CarbonAssets[Carbon Assets]
    AssetValuation --> TokenAssets[Token Assets]
    AssetValuation --> ValuationHistory[Valuation History]
    AssetValuation --> ValuationReports[Valuation Reports]

    CustomReports --> ReportBuilder[Report Builder]
    CustomReports --> SavedReports[Saved Reports]
    CustomReports --> ScheduledReports[Scheduled Reports]

    style Analytics fill:#3F51B5,stroke:#303F9F,color:white
```

#### Pages:
- **/analytics** - Analytics dashboard
- **/analytics/carbon-impact** - Carbon impact metrics
- **/analytics/carbon-impact/emissions** - Emissions reduced
- **/analytics/carbon-impact/retired** - Credits retired
- **/analytics/carbon-impact/visualization** - Impact visualization
- **/analytics/financial** - Financial metrics
- **/analytics/financial/transactions** - Transaction volume
- **/analytics/financial/revenue** - Revenue analytics
- **/analytics/financial/expenses** - Expenses tracking
- **/analytics/financial/wallets** - Wallet balances
- **/analytics/financial/carbon-value** - Carbon asset value
- **/analytics/financial/comparison** - Period comparison
- **/analytics/financial/trends** - Trend analysis
- **/analytics/trading** - Trading activity
- **/analytics/trading/orders** - Order analytics
- **/analytics/trading/market** - Market trends
- **/analytics/trading/prices** - Price history
- **/analytics/transaction-audit** - Transaction audit
- **/analytics/transaction-audit/dashboard** - Audit dashboard
- **/analytics/transaction-audit/list** - Transaction list
- **/analytics/transaction-audit/details/[id]** - Audit details
- **/analytics/transaction-audit/flagged** - Flagged transactions
- **/analytics/transaction-audit/reports** - Audit reports
- **/analytics/asset-valuation** - Asset valuation
- **/analytics/asset-valuation/dashboard** - Valuation dashboard
- **/analytics/asset-valuation/carbon** - Carbon assets
- **/analytics/asset-valuation/tokens** - Token assets
- **/analytics/asset-valuation/history** - Valuation history
- **/analytics/asset-valuation/reports** - Valuation reports
- **/analytics/user-activity** - User activity
- **/analytics/reports** - Custom reports
- **/analytics/reports/builder** - Report builder
- **/analytics/reports/saved** - Saved reports
- **/analytics/reports/scheduled** - Scheduled reports

### 8. Compliance

```mermaid
flowchart TD
    Compliance[Compliance] --> ComplianceDashboard[Compliance Dashboard]
    Compliance --> KYCVerification[KYC Verification]
    Compliance --> AMLMonitoring[AML Monitoring]
    Compliance --> DocumentManagement[Document Management]
    Compliance --> AuditLogs[Audit Logs]
    Compliance --> TaxReporting[Tax Reporting]
    Compliance --> CarbonVerification[Carbon Credit Verification]
    Compliance --> ComplianceReporting[Compliance Reporting]

    KYCVerification --> SubmitKYC[Submit KYC]
    KYCVerification --> KYCStatus[KYC Status]
    KYCVerification --> UpdateKYC[Update KYC]
    KYCVerification --> PendingVerifications[Pending Verifications]
    KYCVerification --> VerificationHistory[Verification History]

    AMLMonitoring --> AMLAlerts[AML Alerts]
    AMLMonitoring --> AMLChecks[AML Checks]
    AMLMonitoring --> AMLReports[AML Reports]
    AMLMonitoring --> AMLRules[AML Rules]

    DocumentManagement --> UploadDocuments[Upload Documents]
    DocumentManagement --> ViewDocuments[View Documents]
    DocumentManagement --> VerifyDocuments[Verify Documents]
    DocumentManagement --> DocumentHistory[Document History]

    CarbonVerification --> PendingCredits[Pending Credits]
    CarbonVerification --> VerifyProject[Verify Project Documents]
    CarbonVerification --> VerifyMethodology[Verify Methodology]
    CarbonVerification --> VerifyReports[Verify Verification Reports]
    CarbonVerification --> CheckRegistry[Check Registry Information]
    CarbonVerification --> VerificationLog[Verification Log]

    ComplianceReporting --> KYCReport[KYC Summary Report]
    ComplianceReporting --> AMLReport[AML Activity Report]
    ComplianceReporting --> VerificationReport[Carbon Verification Report]
    ComplianceReporting --> AuditReport[Audit Log Report]
    ComplianceReporting --> CustomReport[Custom Compliance Report]

    AuditLogs --> FilterLogs[Filter Audit Logs]
    AuditLogs --> ExportLogs[Export Audit Logs]

    TaxReporting --> GenerateTaxReport[Generate Tax Report]
    TaxReporting --> ViewTaxHistory[View Tax History]
    TaxReporting --> TaxSettings[Tax Settings]

    style Compliance fill:#E91E63,stroke:#C2185B,color:white
```

#### Pages:
- **/compliance** - Compliance dashboard
- **/compliance/kyc** - KYC verification
- **/compliance/kyc/submit** - Submit KYC
- **/compliance/kyc/status** - KYC status
- **/compliance/kyc/update** - Update KYC
- **/compliance/kyc/pending** - Pending verifications
- **/compliance/kyc/history** - Verification history
- **/compliance/aml** - AML monitoring
- **/compliance/aml/alerts** - AML alerts
- **/compliance/aml/checks** - AML checks
- **/compliance/aml/reports** - AML reports
- **/compliance/aml/rules** - AML rules configuration
- **/compliance/documents** - Document management
- **/compliance/documents/upload** - Upload documents
- **/compliance/documents/view** - View documents
- **/compliance/documents/verify** - Verify documents
- **/compliance/documents/history** - Document history
- **/compliance/carbon-verification** - Carbon credit verification
- **/compliance/carbon-verification/pending** - Pending credits
- **/compliance/carbon-verification/project** - Verify project documents
- **/compliance/carbon-verification/methodology** - Verify methodology
- **/compliance/carbon-verification/reports** - Verify verification reports
- **/compliance/carbon-verification/registry** - Check registry information
- **/compliance/carbon-verification/log** - Verification log
- **/compliance/reporting** - Compliance reporting
- **/compliance/reporting/kyc** - KYC summary report
- **/compliance/reporting/aml** - AML activity report
- **/compliance/reporting/verification** - Carbon verification report
- **/compliance/reporting/audit** - Audit log report
- **/compliance/reporting/custom** - Custom compliance report
- **/compliance/audit** - Audit logs
- **/compliance/audit/filter** - Filter audit logs
- **/compliance/audit/export** - Export audit logs
- **/compliance/tax** - Tax reporting
- **/compliance/tax/generate** - Generate tax report
- **/compliance/tax/history** - View tax history
- **/compliance/tax/settings** - Tax settings

### 9. Teams

```mermaid
flowchart TD
    Teams[Teams] --> TeamsList[Teams List]
    Teams --> CreateTeam[Create Team]
    Teams --> TeamDetails[Team Details]
    Teams --> TeamMembers[Team Members]
    Teams --> TeamRoles[Team Roles]
    Teams --> ResourceAccess[Resource Access]

    CreateTeam --> TeamInfo[Team Information]
    CreateTeam --> TeamScope[Team Scope]
    CreateTeam --> InviteMembers[Invite Members]

    TeamDetails --> EditTeam[Edit Team]
    TeamDetails --> TeamActivity[Team Activity]

    TeamMembers --> AddMember[Add Member]
    TeamMembers --> RemoveMember[Remove Member]
    TeamMembers --> UpdateRole[Update Member Role]

    TeamRoles --> CreateRole[Create Role]
    TeamRoles --> EditRole[Edit Role]
    TeamRoles --> DeleteRole[Delete Role]

    ResourceAccess --> ManageAccess[Manage Access]
    ResourceAccess --> AccessHistory[Access History]

    style Teams fill:#009688,stroke:#00796B,color:white
```

#### Pages:
- **/teams** - Teams list
- **/teams/create** - Create team
- **/teams/create/info** - Team information
- **/teams/create/scope** - Team scope
- **/teams/create/invite** - Invite members
- **/teams/[id]** - Team details
- **/teams/[id]/edit** - Edit team
- **/teams/[id]/activity** - Team activity
- **/teams/[id]/members** - Team members
- **/teams/[id]/members/add** - Add member
- **/teams/[id]/roles** - Team roles
- **/teams/[id]/roles/create** - Create role
- **/teams/[id]/roles/[roleId]/edit** - Edit role
- **/teams/[id]/access** - Resource access
- **/teams/[id]/access/manage** - Manage access
- **/teams/[id]/access/history** - Access history

### 10. Settings

```mermaid
flowchart TD
    Settings[Settings] --> UserSettings[User Settings]
    Settings --> OrganizationSettings[Organization Settings]
    Settings --> SecuritySettings[Security Settings]
    Settings --> NotificationSettings[Notification Settings]
    Settings --> APISettings[API Settings]
    Settings --> BillingSettings[Billing Settings]

    UserSettings --> ProfileSettings[Profile Settings]
    UserSettings --> PreferenceSettings[Preference Settings]

    OrganizationSettings --> OrgProfile[Organization Profile]
    OrganizationSettings --> OrgMembers[Organization Members]
    OrganizationSettings --> OrgBranding[Organization Branding]

    SecuritySettings --> PasswordSettings[Password Settings]
    SecuritySettings --> MFASettings[MFA Settings]
    SecuritySettings --> SessionSettings[Session Settings]

    NotificationSettings --> EmailNotifications[Email Notifications]
    NotificationSettings --> PushNotifications[Push Notifications]
    NotificationSettings --> AlertSettings[Alert Settings]

    APISettings --> APIKeys[API Keys]
    APISettings --> Webhooks[Webhooks]
    APISettings --> APIUsage[API Usage]

    BillingSettings --> Subscription[Subscription]
    BillingSettings --> PaymentMethods[Payment Methods]
    BillingSettings --> InvoiceHistory[Invoice History]

    style Settings fill:#607D8B,stroke:#455A64,color:white
```

#### Pages:
- **/settings** - Settings dashboard
- **/settings/user** - User settings
- **/settings/user/profile** - Profile settings
- **/settings/user/preferences** - Preference settings
- **/settings/organization** - Organization settings
- **/settings/organization/profile** - Organization profile
- **/settings/organization/members** - Organization members
- **/settings/organization/branding** - Organization branding
- **/settings/security** - Security settings
- **/settings/security/password** - Password settings
- **/settings/security/mfa** - MFA settings
- **/settings/security/sessions** - Session settings
- **/settings/notifications** - Notification settings
- **/settings/notifications/email** - Email notifications
- **/settings/notifications/push** - Push notifications
- **/settings/notifications/alerts** - Alert settings
- **/settings/api** - API settings
- **/settings/api/keys** - API keys
- **/settings/api/webhooks** - Webhooks
- **/settings/api/usage** - API usage
- **/settings/billing** - Billing settings
- **/settings/billing/subscription** - Subscription
- **/settings/billing/payment** - Payment methods
- **/settings/billing/invoices** - Invoice history

### 11. Admin Panel

```mermaid
flowchart TD
    Admin[Admin Panel] --> UserManagement[User Management]
    Admin --> OrganizationManagement[Organization Management]
    Admin --> ProjectManagement[Project Management]
    Admin --> CreditVerification[Credit Verification]
    Admin --> PlatformConfiguration[Platform Configuration]
    Admin --> SystemMonitoring[System Monitoring]
    Admin --> AdminAnalytics[Admin Analytics]

    UserManagement --> UsersList[Users List]
    UserManagement --> UserDetails[User Details]
    UserManagement --> UserActions[User Actions]

    OrganizationManagement --> OrgsList[Organizations List]
    OrganizationManagement --> OrgDetails[Organization Details]
    OrganizationManagement --> OrgVerification[Organization Verification]

    ProjectManagement --> ProjectsList[Projects List]
    ProjectManagement --> ProjectDetails[Project Details]
    ProjectManagement --> ProjectVerification[Project Verification]
    ProjectManagement --> ProjectMonitoring[Project Monitoring]

    CreditVerification --> PendingVerifications[Pending Verifications]
    CreditVerification --> VerificationDetails[Verification Details]
    CreditVerification --> VerificationHistory[Verification History]

    PlatformConfiguration --> GeneralConfig[General Configuration]
    PlatformConfiguration --> FeeStructure[Fee Structure]
    PlatformConfiguration --> BlockchainConfig[Blockchain Configuration]

    SystemMonitoring --> SystemHealth[System Health]
    SystemMonitoring --> ErrorLogs[Error Logs]
    SystemMonitoring --> SecurityEvents[Security Events]

    AdminAnalytics --> PlatformMetrics[Platform Metrics]
    AdminAnalytics --> AdminReports[Admin Reports]

    style Admin fill:#F44336,stroke:#D32F2F,color:white
```

#### Pages:
- **/admin** - Admin panel dashboard
- **/admin/users** - User management
- **/admin/users/list** - Users list
- **/admin/users/[id]** - User details
- **/admin/users/[id]/actions** - User actions
- **/admin/organizations** - Organization management
- **/admin/organizations/list** - Organizations list
- **/admin/organizations/[id]** - Organization details
- **/admin/organizations/[id]/verify** - Organization verification
- **/admin/projects** - Project management
- **/admin/projects/list** - Projects list
- **/admin/projects/[id]** - Project details
- **/admin/projects/[id]/verify** - Project verification
- **/admin/projects/[id]/monitor** - Project monitoring
- **/admin/verification** - Credit verification
- **/admin/verification/pending** - Pending verifications
- **/admin/verification/[id]** - Verification details
- **/admin/verification/history** - Verification history
- **/admin/configuration** - Platform configuration
- **/admin/configuration/general** - General configuration
- **/admin/configuration/fees** - Fee structure
- **/admin/configuration/blockchain** - Blockchain configuration
- **/admin/monitoring** - System monitoring
- **/admin/monitoring/health** - System health
- **/admin/monitoring/errors** - Error logs
- **/admin/monitoring/security** - Security events
- **/admin/analytics** - Admin analytics
- **/admin/analytics/metrics** - Platform metrics
- **/admin/analytics/reports** - Admin reports

### 12. Notifications

```mermaid
flowchart TD
    Notifications[Notifications] --> NotificationCenter[Notification Center]
    Notifications --> NotificationDetails[Notification Details]
    Notifications --> NotificationSettings[Notification Settings]

    NotificationCenter --> AllNotifications[All Notifications]
    NotificationCenter --> UnreadNotifications[Unread Notifications]
    NotificationCenter --> FilterNotifications[Filter Notifications]

    NotificationDetails --> MarkRead[Mark as Read]
    NotificationDetails --> TakeAction[Take Action]
    NotificationDetails --> Dismiss[Dismiss]

    NotificationSettings --> EmailPreferences[Email Preferences]
    NotificationSettings --> PushPreferences[Push Preferences]
    NotificationSettings --> NotificationTypes[Notification Types]

    style Notifications fill:#795548,stroke:#5D4037,color:white
```

#### Pages:
- **/notifications** - Notification center
- **/notifications/all** - All notifications
- **/notifications/unread** - Unread notifications
- **/notifications/[id]** - Notification details
- **/notifications/settings** - Notification settings
- **/notifications/settings/email** - Email preferences
- **/notifications/settings/push** - Push preferences
- **/notifications/settings/types** - Notification types

## Role-Based Access

The sitemap supports different user roles with appropriate access restrictions:

### Enterprise Administrator
- Full access to organization settings
- Access to project portfolio management
- Access to team management
- Access to organization analytics
- Access to marketplace strategy
- Limited access to admin panel

### Carbon Credit Manager
- Full access to projects section
- Full access to carbon credits section
- Access to marketplace
- Limited access to analytics
- Limited access to wallet

### Wallet Manager
- Full access to wallet section
- Full access to project wallets
- Full access to tokenization center
- Full access to wallet security and compliance
- Access to multi-chain management
- Access to transaction history
- Limited access to analytics
- Limited access to carbon credits

### Compliance Officer
- Full access to compliance section
- Access to project verification
- Access to carbon credit verification
- Access to marketplace compliance monitoring
- Full access to compliance reporting
- Access to audit logs
- Limited access to analytics
- Limited access to carbon credits

### Finance Manager
- Access to project financial management
- Access to marketplace financial monitoring
- Full access to financial analytics
- Full access to transaction audit
- Full access to project portfolio valuation
- Access to transaction history
- Access to tax reporting
- Limited access to wallet

### Regular User
- Access to project exploration
- Access to carbon credit exploration
- Access to marketplace participation
- Limited access to wallet
- Limited access to analytics

### Platform Administrator
- Full access to admin panel
- Full access to all sections
- Full access to system configuration
- Full access to monitoring

## Mobile Responsiveness

All pages in the sitemap are designed to be fully responsive, with optimized layouts for:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## Accessibility Considerations

The sitemap includes considerations for accessibility:
- Clear navigation hierarchy
- Consistent page structure
- Logical tab order
- Screen reader compatibility
- Keyboard navigation support

## Future Expansion

The sitemap is designed to accommodate future expansion in these areas:
- Project-based carbon credit management
- Enhanced marketplace trading features
- Advanced project analytics capabilities
- Additional blockchain networks
- Expanded project verification workflows
- Advanced wallet security features
- Cross-chain bridging optimizations
- Project financial monitoring and auditing tools
- Project portfolio valuation methodologies
- Mobile app integration
