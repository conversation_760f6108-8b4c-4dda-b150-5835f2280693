# Carbonix Platform Documentation

Welcome to the Carbonix platform documentation. This documentation provides comprehensive information about the platform for both developers and end-users, covering all aspects of the B2B carbon credit trading ecosystem.

## Documentation Structure

The documentation is organized into several sections:

- **[Developer Documentation](./developers/README.md)**: Technical documentation for developers working on the platform
- **[User Documentation](./users/README.md)**: User guides and tutorials for end-users of the platform
- **[User Journeys](./user-journeys/)**: Detailed user journey documentation for different platform roles
- **[Module Documentation](../src/lib/README.md)**: Documentation for the core library modules
- **[Project Structure](../README.md#project-structure)**: Overview of the project structure and organization

## Developer Documentation

The developer documentation provides technical details about the platform architecture, components, and implementation:

- [Developer Guide](./developers/README.md): Overview of the platform architecture and components
- [Alchemy Integration](./developers/alchemy-integration.md): Detailed information about the integration of Alchemy's Smart Wallet and Portfolio Management features
- [Transaction History & Gas Estimation](./developers/transaction-history-gas-estimation.md): Implementation details for transaction history and gas estimation features
- [Account Abstraction](./developers/account-abstraction.md): Implementation details for Alchemy's Account Abstraction
- [RBAC System](./rbac-system.md): Documentation for the Role-Based Access Control system
- [Module Testing Plan](./module-testing-plan.md): Testing plan for the consolidated modules
- [Consolidation Summary](./consolidation-summary.md): Summary of the module consolidation process

## User Documentation

The user documentation provides guides and tutorials for end-users of the platform:

- [User Guide](./users/README.md): General guide for using the platform
- [Smart Wallet Guide](./users/smart-wallet-guide.md): Detailed guide for using Smart Wallets and Portfolio Management features
- [Blockchain Operations Guide](./users/blockchain-operations.md): Guide for performing blockchain operations
- [Project Management Guide](./users/project-management.md): Guide for managing carbon credit projects
- [Compliance Guide](./users/compliance-guide.md): Guide for compliance and regulatory requirements

## Getting Started

If you're new to the platform, we recommend starting with the following:

- **For Developers**:
  - Start with the [Developer Guide](./developers/README.md) to understand the platform architecture and components
  - Review the [Module Documentation](../src/lib/README.md) to understand the core library modules
  - Check the [Consolidation Summary](./consolidation-summary.md) to understand recent code organization changes

- **For End-Users**:
  - Start with the [User Guide](./users/README.md) to learn how to use the platform
  - Review the [User Journeys](./user-journeys/) to understand the different workflows
  - Check the [Blockchain Operations Guide](./users/blockchain-operations.md) for blockchain-specific operations

## Recent Updates

The documentation has been updated to reflect the following changes:

1. **Module Consolidation**: The codebase has been reorganized into domain-specific modules for better maintainability
2. **Project-Based Structure**: Updated user journeys to reflect the project-based carbon credit management
3. **Enhanced Compliance Features**: Added documentation for KYC/AML verification and regulatory reporting
4. **RBAC System**: Added documentation for the Role-Based Access Control system
5. **Multi-Chain Support**: Updated blockchain integration documentation to cover all supported networks

## Contributing to the Documentation

If you'd like to contribute to the documentation, please follow these guidelines:

1. Use Markdown for all documentation files
2. Use Mermaid diagrams for visualizing processes and relationships
3. Follow the existing structure and style
4. Ensure all links are working correctly
5. Include code examples where appropriate
6. Submit a pull request with your changes

## License

This documentation is licensed under the [MIT License](../LICENSE).
