# Validation Library

This document describes the validation library used in the Carbonix platform to ensure consistent validation rules and error handling across all forms.

## Overview

The validation library provides a comprehensive set of tools for form validation, including:

- **Validation Schemas**: Standardized Zod schemas for common form fields
- **Validation Utilities**: Helper functions for validation and error handling
- **Validation Hooks**: React hooks for form validation and API error handling
- **Validated Form Component**: A reusable form component with built-in validation

## Validation Schemas

The validation library provides a comprehensive set of validation schemas for common form fields. These schemas ensure consistent validation rules and error messages across all forms.

### Basic Schemas

```typescript
// Email validation
const email = emailSchema; // z.string().email("Please enter a valid email address")...

// Password validation
const password = passwordSchema; // z.string().min(8, "Password must be at least 8 characters")...

// Name validation
const name = nameSchema; // z.string().min(2, "Name must be at least 2 characters")...

// URL validation
const website = urlSchema; // z.string().url("Please enter a valid URL")...

// Phone number validation
const phone = phoneNumberSchema; // z.string().regex(/^\+?[0-9]{10,15}$/, "Please enter a valid phone number")...
```

### Composite Schemas

```typescript
// Registration form
const registrationForm = registrationSchema; // z.object({ name, email, password, confirmPassword })...

// Login form
const loginForm = loginSchema; // z.object({ email, password })...

// Organization form
const organizationForm = organizationSchema; // z.object({ name, industry, size, country, ... })...
```

## Validation Hooks

The validation library provides React hooks for form validation and API error handling.

### useValidatedForm

The `useValidatedForm` hook provides a wrapper around React Hook Form with built-in Zod validation and error handling.

```typescript
import { useValidatedForm, loginSchema } from "@/lib/validation";

function LoginForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    isSubmitting,
    formError,
    handleSubmitWithValidation,
  } = useValidatedForm(loginSchema);

  const onSubmit = async (data) => {
    // Handle form submission
  };

  return (
    <form onSubmit={handleSubmitWithValidation(onSubmit)}>
      {/* Form fields */}
    </form>
  );
}
```

### useApiError

The `useApiError` hook provides utilities for handling API errors.

```typescript
import { useApiError } from "@/lib/validation";

function DataFetcher() {
  const { error, isLoading, handleApiRequest } = useApiError();

  const fetchData = async () => {
    await handleApiRequest(
      async () => {
        // Make API request
        const response = await fetch("/api/data");
        return response.json();
      },
      {
        onSuccess: (data) => {
          // Handle success
        },
        onError: (error) => {
          // Handle error
        },
      }
    );
  };

  return (
    <div>
      {error && <div className="error">{error.message}</div>}
      <button onClick={fetchData} disabled={isLoading}>
        {isLoading ? "Loading..." : "Fetch Data"}
      </button>
    </div>
  );
}
```

## Validated Form Component

The `ValidatedForm` component provides a reusable form component with built-in validation.

```tsx
import { ValidatedForm } from "@/components/forms/validated-form";
import { loginSchema } from "@/lib/validation";

function LoginForm() {
  const handleLogin = async (data) => {
    // Handle login
  };

  return (
    <ValidatedForm
      schema={loginSchema}
      defaultValues={{ email: "", password: "" }}
      onSubmit={handleLogin}
    >
      {({ register, formState, isSubmitting }) => (
        <>
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              {...register("email")}
              className={formState.errors.email ? "error" : ""}
            />
            {formState.errors.email && (
              <div className="error">{formState.errors.email.message}</div>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              {...register("password")}
              className={formState.errors.password ? "error" : ""}
            />
            {formState.errors.password && (
              <div className="error">{formState.errors.password.message}</div>
            )}
          </div>

          <button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Logging in..." : "Login"}
          </button>
        </>
      )}
    </ValidatedForm>
  );
}
```

## Best Practices

### 1. Use Standardized Schemas

Always use the standardized validation schemas from the validation library instead of creating your own. This ensures consistent validation rules and error messages across all forms.

```typescript
// Good
import { emailSchema, passwordSchema } from "@/lib/validation";

const loginSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

// Bad
const loginSchema = z.object({
  email: z.string().email("Invalid email"),
  password: z.string().min(8, "Password too short"),
});
```

### 2. Use the ValidatedForm Component

Use the `ValidatedForm` component for all forms that require validation. This ensures consistent error handling and user experience.

```tsx
// Good
<ValidatedForm
  schema={loginSchema}
  defaultValues={{ email: "", password: "" }}
  onSubmit={handleLogin}
>
  {/* Form fields */}
</ValidatedForm>

// Bad
<form onSubmit={handleSubmit(onSubmit)}>
  {/* Form fields */}
</form>
```

### 3. Handle API Errors Consistently

Use the `useApiError` hook to handle API errors consistently.

```typescript
// Good
const { handleApiRequest } = useApiError();

const fetchData = async () => {
  await handleApiRequest(
    async () => {
      // Make API request
    }
  );
};

// Bad
const fetchData = async () => {
  try {
    // Make API request
  } catch (error) {
    // Handle error
  }
};
```

### 4. Display Validation Errors Consistently

Use the `FormMessage` component from the UI library to display validation errors consistently.

```tsx
// Good
<FormItem>
  <FormLabel>Email</FormLabel>
  <FormControl>
    <Input {...register("email")} />
  </FormControl>
  <FormMessage />
</FormItem>

// Bad
<div>
  <label>Email</label>
  <input {...register("email")} />
  {errors.email && <span>{errors.email.message}</span>}
</div>
```

## Examples

### Login Form

```tsx
import { ValidatedForm } from "@/components/forms/validated-form";
import { loginSchema } from "@/lib/validation";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

function LoginForm() {
  const handleLogin = async (data) => {
    // Handle login
  };

  return (
    <ValidatedForm
      schema={loginSchema}
      defaultValues={{ email: "", password: "" }}
      onSubmit={handleLogin}
    >
      {({ control, isSubmitting }) => (
        <>
          <FormField
            control={control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="Password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Logging in..." : "Login"}
          </Button>
        </>
      )}
    </ValidatedForm>
  );
}
```

### Registration Form

```tsx
import { ValidatedForm } from "@/components/forms/validated-form";
import { registrationSchema } from "@/lib/validation";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

function RegistrationForm() {
  const handleRegistration = async (data) => {
    // Handle registration
  };

  return (
    <ValidatedForm
      schema={registrationSchema}
      defaultValues={{ name: "", email: "", password: "", confirmPassword: "" }}
      onSubmit={handleRegistration}
    >
      {({ control, isSubmitting }) => (
        <>
          <FormField
            control={control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="Password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="Confirm Password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Registering..." : "Register"}
          </Button>
        </>
      )}
    </ValidatedForm>
  );
}
```

## Conclusion

The validation library provides a comprehensive set of tools for form validation, ensuring consistent validation rules and error handling across all forms. By using the standardized schemas, hooks, and components, you can create forms that provide a consistent user experience and are easy to maintain.
