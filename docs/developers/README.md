# Carbonix Platform - Developer Documentation

This documentation provides comprehensive technical details about the Carbonix platform architecture, components, and implementation. It serves as the primary reference for developers working on the platform.

## Table of Contents

- [System Architecture](#system-architecture)
- [Database Schema](#database-schema)
- [Authentication Flow](#authentication-flow)
- [Order Matching Process](#order-matching-process)
- [Multi-Chain Wallet Integration](#multi-chain-wallet-integration)
- [Gas Optimization Strategy](#gas-optimization-strategy)
- [Tenant Isolation System](#tenant-isolation-system)
- [API Endpoints](#api-endpoints)
- [Environment Variables](#environment-variables)
- [Component Structure](#component-structure)
- [Development Guidelines](#development-guidelines)
- [Module Structure](#module-structure)
- [Testing Strategy](#testing-strategy)
- [Deployment Process](#deployment-process)
- [Performance Optimization](#performance-optimization)
- [Security Considerations](#security-considerations)

## Recent Updates

The platform has undergone significant improvements:

1. **Module Consolidation**: Restructured the `lib` directory into domain-specific modules
2. **Project-Based Carbon Credits**: Enhanced the carbon credit management to support project-based structure
3. **Enhanced RBAC System**: Implemented granular permissions with team-level integration
4. **Multi-Chain Support**: Added support for Ethereum, Polygon, Arbitrum, Optimism, and Base networks
5. **Compliance Features**: Added KYC/AML verification and regulatory reporting
6. **Performance Optimizations**: Improved database queries and blockchain interactions
7. **UI/UX Enhancements**: Implemented micro-animations and improved user flows

## System Architecture

The Carbonix platform is built using the @.clinerules stack, which includes Next.js, TypeScript, Prisma, NextAuth.js, Tailwind CSS, and shadcn/ui.

```mermaid
flowchart TD
    Client[Client Browser] <--> NextJS[Next.js Application]
    NextJS <--> API[API Routes]
    API <--> Auth[Authentication]
    API <--> DB[(Database)]
    API <--> Blockchain[Blockchain Integration]
    Blockchain <--> Alchemy[Alchemy SDK]
    Alchemy <--> ETH[Ethereum]
    Alchemy <--> POLY[Polygon]
    Alchemy <--> ARB[Arbitrum]
    Alchemy <--> OPT[Optimism]
    Alchemy <--> BASE[Base]

    subgraph Backend
        API
        Auth
        DB
        Blockchain
    end

    subgraph Frontend
        NextJS
        Client
    end

    subgraph Blockchain Networks
        Alchemy
        ETH
        POLY
        ARB
        OPT
        BASE
    end
```

### Key Components

- **Frontend**: Next.js application with React components
- **Backend**: Next.js API routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Blockchain Integration**: Alchemy SDK and ethers.js

## Database Schema

The database schema is defined using Prisma and includes the following models:

```mermaid
erDiagram
    User ||--o{ Organization : "belongs to"
    User ||--o{ Wallet : "owns"
    User ||--o{ Notification : "receives"
    Organization ||--o{ CarbonCredit : "lists"
    Organization ||--o{ Wallet : "owns"
    Organization ||--o{ Subscription : "has"
    CarbonCredit ||--o{ Order : "involved in"
    Wallet ||--o{ Transaction : "has"
    Wallet ||--o{ Token : "contains"
    Wallet ||--o{ NFT : "contains"
    Order ||--o{ Transaction : "generates"

    User {
        string id PK
        string name
        string email
        string password
        string role
        boolean emailVerified
        datetime createdAt
        datetime updatedAt
    }

    Organization {
        string id PK
        string name
        string description
        string website
        string logo
        string status
        float transactionFeeRate
        float listingFee
        datetime createdAt
        datetime updatedAt
    }

    CarbonCredit {
        string id PK
        string name
        string description
        float quantity
        float price
        int vintage
        string standard
        string methodology
        string location
        string status
        string organizationId FK
        datetime createdAt
        datetime updatedAt
    }

    Wallet {
        string id PK
        string address
        string network
        int chainId
        boolean isTestnet
        string encryptedKey
        boolean isSmartWallet
        string ownerAddress
        string factoryAddress
        float balance
        datetime lastSyncedAt
        string userId FK
        string organizationId FK
        datetime createdAt
        datetime updatedAt
    }

    Transaction {
        string id PK
        float amount
        float fee
        float gasPrice
        int gasLimit
        int gasUsed
        string type
        string status
        string transactionHash
        int blockNumber
        string network
        int chainId
        string walletId FK
        string orderId FK
        datetime createdAt
        datetime updatedAt
    }

    Order {
        string id PK
        string type
        float quantity
        float price
        string status
        string userId FK
        string carbonCreditId FK
        datetime createdAt
        datetime updatedAt
    }

    Token {
        string id PK
        string contractAddress
        string name
        string symbol
        int decimals
        string balance
        string walletId FK
        datetime lastUpdated
    }

    NFT {
        string id PK
        string contractAddress
        string tokenId
        string name
        string description
        string tokenType
        json metadata
        string walletId FK
        datetime lastUpdated
    }

    Notification {
        string id PK
        string title
        string message
        string type
        boolean read
        string userId FK
        datetime createdAt
        datetime updatedAt
    }

    Subscription {
        string id PK
        string plan
        string status
        datetime startDate
        datetime endDate
        float amount
        string organizationId FK
        datetime createdAt
        datetime updatedAt
    }
```

## Authentication Flow

The authentication flow is handled by NextAuth.js and includes email verification and password reset functionality.

```mermaid
sequenceDiagram
    participant User
    participant Client
    participant NextAuth
    participant Database
    participant Email

    User->>Client: Enter credentials
    Client->>NextAuth: Submit login request
    NextAuth->>Database: Verify credentials

    alt Invalid credentials
        Database-->>NextAuth: Authentication failed
        NextAuth-->>Client: Return error
        Client-->>User: Show error message
    else Valid credentials
        Database-->>NextAuth: Authentication successful
        NextAuth-->>Client: Create session
        Client-->>User: Redirect to dashboard
    end

    User->>Client: Register new account
    Client->>NextAuth: Submit registration
    NextAuth->>Database: Create user (unverified)
    NextAuth->>Email: Send verification email
    Email-->>User: Receive verification email
    User->>Client: Click verification link
    Client->>NextAuth: Verify email
    NextAuth->>Database: Mark email as verified
    NextAuth-->>Client: Confirmation
    Client-->>User: Show success message
```

## Order Matching Process

The order matching process is implemented in the `/api/orders` endpoint and follows this flow:

```mermaid
flowchart TD
    Start[New Buy Order] --> CheckSellOrders[Check Available Sell Orders]

    CheckSellOrders --> MatchFound{Matching Sell Orders?}
    MatchFound -->|Yes| PriceCheck{Price Acceptable?}
    MatchFound -->|No| CreateBuyOrder[Create Buy Order]

    PriceCheck -->|Yes| QuantityCheck{Full Quantity?}
    PriceCheck -->|No| NextSellOrder[Check Next Sell Order]
    NextSellOrder --> MatchFound

    QuantityCheck -->|Yes| FullMatch[Match Full Order]
    QuantityCheck -->|No| PartialMatch[Match Partial Order]

    PartialMatch --> UpdateQuantity[Update Remaining Quantity]
    UpdateQuantity --> CheckSellOrders

    FullMatch --> CreateTransactions[Create Transactions]
    PartialMatch --> CreateTransactions

    CreateTransactions --> UpdateBalances[Update Wallet Balances]
    UpdateBalances --> UpdateCarbonCredits[Update Carbon Credit Quantities]
    UpdateCarbonCredits --> CreateNotifications[Create Notifications]
    CreateNotifications --> End[End Process]

    CreateBuyOrder --> End
```

## Multi-Chain Wallet Integration

The platform supports multiple blockchain networks through Alchemy's SDK:

```mermaid
flowchart TD
    Start[Create Wallet] --> NetworkSelection[Select Blockchain Network]

    NetworkSelection --> ETH[Ethereum]
    NetworkSelection --> POLY[Polygon]
    NetworkSelection --> ARB[Arbitrum]
    NetworkSelection --> OPT[Optimism]
    NetworkSelection --> BASE[Base]

    ETH --> WalletType{Wallet Type}
    POLY --> WalletType
    ARB --> WalletType
    OPT --> WalletType
    BASE --> WalletType

    WalletType -->|EOA| CreateEOA[Create EOA Wallet]
    WalletType -->|Smart Wallet| CreateSmartWallet[Create Smart Contract Wallet]

    CreateEOA --> GenerateKeys[Generate & Encrypt Keys]
    CreateSmartWallet --> DeployContract[Deploy Smart Wallet Contract]

    GenerateKeys --> StoreWallet[Store Wallet in Database]
    DeployContract --> StoreWallet

    StoreWallet --> End[Return Wallet to User]
```

### Key Files

- `src/lib/blockchain-config.ts`: Configuration for different blockchain networks
- `src/lib/blockchain-client.ts`: Client for interacting with blockchain networks
- `src/components/wallet/multi-chain-wallet.tsx`: UI component for multi-chain wallet management

## Gas Optimization Strategy

The platform includes advanced gas optimization strategies:

```mermaid
flowchart TD
    Start[Prepare Transaction] --> NetworkCheck{EIP-1559 Support?}

    NetworkCheck -->|Yes| EIP1559[Use EIP-1559 Fee Structure]
    NetworkCheck -->|No| LegacyGas[Use Legacy Gas Price]

    EIP1559 --> UrgencyCheck{Transaction Urgency?}
    LegacyGas --> UrgencyCheck

    UrgencyCheck -->|Low| SlowStrategy[Use Slow Fee Strategy]
    UrgencyCheck -->|Medium| AverageStrategy[Use Average Fee Strategy]
    UrgencyCheck -->|High| FastStrategy[Use Fast Fee Strategy]
    UrgencyCheck -->|Custom| CustomStrategy[Use Custom Fee Parameters]

    SlowStrategy --> CongestionCheck{Network Congested?}
    AverageStrategy --> CongestionCheck
    FastStrategy --> CongestionCheck

    CongestionCheck -->|Yes| AdjustFees[Adjust Fees Upward]
    CongestionCheck -->|No| PrepareParams[Prepare Transaction Parameters]

    CustomStrategy --> PrepareParams
    AdjustFees --> PrepareParams

    PrepareParams --> EstimateGas[Estimate Gas Limit]
    EstimateGas --> AddBuffer[Add Safety Buffer to Gas Limit]
    AddBuffer --> SendTransaction[Send Transaction]
    SendTransaction --> End[Return Transaction Hash]
```

### Key Files

- `src/lib/gas-optimizer.ts`: Gas optimization strategies
- `src/app/api/wallet/send/route.ts`: API endpoint for sending transactions with gas optimization

## API Endpoints

| Endpoint | Method | Description | Authentication |
|----------|--------|-------------|----------------|
| `/api/auth/[...nextauth]` | Various | NextAuth.js authentication endpoints | Varies |
| `/api/register` | POST | Register a new user | None |
| `/api/auth/verify-email` | POST | Verify a user's email | None |
| `/api/auth/reset-password` | POST/PUT | Request/perform password reset | None |
| `/api/organizations` | GET/POST | List/create organizations | User |
| `/api/organizations/[id]` | GET/PUT/DELETE | Get/update/delete an organization | User/Admin |
| `/api/carbon-credits` | GET/POST | List/create carbon credits | User |
| `/api/carbon-credits/[id]` | GET/PUT/DELETE | Get/update/delete a carbon credit | User/Admin |
| `/api/orders` | GET/POST | List/create orders | User |
| `/api/orders/[id]` | GET/PUT/DELETE | Get/update/delete an order | User |
| `/api/wallet` | GET | Get user's wallet | User |
| `/api/wallet?operation=deposit` | POST | Deposit funds | User |
| `/api/wallet?operation=withdraw` | POST | Withdraw funds | User |
| `/api/wallet/create` | POST | Create a new wallet | User |
| `/api/wallet/smart-wallet` | GET/POST | Get/create smart wallet | User |
| `/api/wallet/portfolio` | GET | Get wallet portfolio | User |
| `/api/wallet/send` | POST | Send transaction | User |
| `/api/notifications` | GET | Get user's notifications | User |
| `/api/notifications?id=[id]` | PATCH | Mark notification as read | User |
| `/api/admin/users` | GET | List all users | Admin |
| `/api/admin/organizations` | GET | List all organizations | Admin |
| `/api/admin/carbon-credits` | GET | List all carbon credits | Admin |
| `/api/admin/orders` | GET | List all orders | Admin |
| `/api/admin/analytics` | GET | Get platform analytics | Admin |

## Environment Variables

```
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/carbon_exchange"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Email
EMAIL_SERVER_HOST="smtp.example.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="password"
EMAIL_FROM="<EMAIL>"

# Blockchain
ALCHEMY_API_KEY="your-alchemy-api-key"
ALCHEMY_NETWORK="eth-sepolia"
WALLET_ENCRYPTION_KEY="your-wallet-encryption-key"
USE_TESTNET="true"

# Application
NODE_ENV="development"
APP_VERSION="1.0.0"
LOG_LEVEL="2"
```

## Component Structure

The application follows the Atomic Design methodology:

```mermaid
flowchart TD
    subgraph Atoms
        Button[Button]
        Input[Input]
        Label[Label]
        Select[Select]
        Checkbox[Checkbox]
        RadioGroup[RadioGroup]
        Switch[Switch]
        Card[Card]
    end

    subgraph Molecules
        FormField[FormField]
        SearchInput[SearchInput]
        Notification[Notification]
        Dropdown[Dropdown]
        Modal[Modal]
        Tabs[Tabs]
        Pagination[Pagination]
    end

    subgraph Organisms
        NavBar[Navigation Bar]
        Sidebar[Sidebar]
        WalletCard[Wallet Card]
        CarbonCreditCard[Carbon Credit Card]
        OrderForm[Order Form]
        TransactionList[Transaction List]
        PortfolioView[Portfolio View]
    end

    subgraph Templates
        DashboardLayout[Dashboard Layout]
        MarketplaceLayout[Marketplace Layout]
        AdminLayout[Admin Layout]
        AuthLayout[Authentication Layout]
    end

    subgraph Pages
        Dashboard[Dashboard Page]
        Marketplace[Marketplace Page]
        Wallet[Wallet Page]
        Settings[Settings Page]
        Admin[Admin Page]
        Login[Login Page]
        Register[Register Page]
    end

    Atoms --> Molecules
    Molecules --> Organisms
    Organisms --> Templates
    Templates --> Pages
```

## Development Guidelines

### Code Style

- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Use meaningful variable and function names
- Add JSDoc comments for functions and components

### Component Development

- Follow the Atomic Design methodology
- Use composition over inheritance
- Implement proper error handling and loading states
- Use React hooks for state management and side effects
- Ensure components are responsive and accessible
- Use shadcn/ui components as building blocks

### API Development

- Follow RESTful API design principles
- Implement proper error handling and validation
- Use Zod for request validation
- Document API endpoints with JSDoc comments
- Implement rate limiting for public endpoints
- Use tenant isolation for multi-tenant endpoints

## Module Structure

The codebase follows a domain-driven design approach with modules organized by functionality in the `src/lib` directory:

```mermaid
flowchart TD
    App[Application] --> Lib[Library Modules]

    Lib --> Analytics[Analytics]
    Lib --> Blockchain[Blockchain]
    Lib --> CarbonCredits[Carbon Credits]
    Lib --> Marketplace[Marketplace]
    Lib --> Orders[Orders]
    Lib --> Notifications[Notifications]
    Lib --> Audit[Audit]
    Lib --> Payments[Payments]
    Lib --> Compliance[Compliance]
    Lib --> RBAC[RBAC]
    Lib --> Storage[Storage]
    Lib --> Validation[Validation]

    Analytics --> PlatformAnalytics[Platform Analytics]
    Analytics --> OrganizationAnalytics[Organization Analytics]
    Analytics --> UserAnalytics[User Analytics]
    Analytics --> MarketAnalytics[Market Analytics]

    Blockchain --> Core[Core Client]
    Blockchain --> Config[Network Config]
    Blockchain --> Contracts[Smart Contracts]
    Blockchain --> Gas[Gas Estimation]

    CarbonCredits --> Service[Credit Service]
    CarbonCredits --> Verification[Verification]
    CarbonCredits --> Tokenization[Tokenization]

    Marketplace --> Listings[Listings]
    Marketplace --> Discovery[Discovery]
    Marketplace --> Search[Search]

    Orders --> Management[Order Management]
    Orders --> Matching[Matching Engine]
    Orders --> MarketData[Market Data]
```

Each module has a consistent structure:

- `index.ts`: Main entry point that exports the public API
- `types.ts`: TypeScript types and interfaces
- `constants.ts`: Constants and configuration
- `utils.ts`: Utility functions
- Domain-specific files (e.g., `service.ts`, `client.ts`, etc.)

## Testing Strategy

The platform uses Jest for unit and integration testing:

### Unit Tests

Unit tests focus on testing individual functions and components in isolation:

```typescript
// Example unit test for a utility function
describe('formatCarbonCreditPrice', () => {
  it('should format the price correctly', () => {
    expect(formatCarbonCreditPrice(10.5)).toBe('$10.50');
    expect(formatCarbonCreditPrice(0)).toBe('$0.00');
    expect(formatCarbonCreditPrice(1000)).toBe('$1,000.00');
  });
});
```

### Integration Tests

Integration tests focus on testing the interaction between different modules:

```typescript
// Example integration test for the order matching process
describe('Order Matching Process', () => {
  it('should match a buy order with a sell order', async () => {
    // Create a buy order
    const buyOrder = await createOrder({
      type: 'BUY',
      quantity: 10,
      price: 100,
      userId: 'user-1',
      carbonCreditId: 'credit-1',
    });

    // Create a sell order
    const sellOrder = await createOrder({
      type: 'SELL',
      quantity: 10,
      price: 100,
      userId: 'user-2',
      carbonCreditId: 'credit-1',
    });

    // Run the matching process
    await matchOrders();

    // Verify the orders are matched
    const updatedBuyOrder = await getOrder(buyOrder.id);
    const updatedSellOrder = await getOrder(sellOrder.id);

    expect(updatedBuyOrder.status).toBe('MATCHED');
    expect(updatedSellOrder.status).toBe('MATCHED');
  });
});
```

### End-to-End Tests

End-to-end tests focus on testing the entire application from the user's perspective:

```typescript
// Example end-to-end test for the carbon credit creation process
describe('Carbon Credit Creation', () => {
  it('should create a carbon credit', async () => {
    // Login as an organization admin
    await login('<EMAIL>', 'password');

    // Navigate to the carbon credit creation page
    await navigateTo('/carbon-credits/create');

    // Fill out the form
    await fillField('name', 'Test Carbon Credit');
    await fillField('description', 'Test Description');
    await fillField('quantity', '100');
    await fillField('price', '10');
    await fillField('vintage', '2023');
    await fillField('standard', 'VCS');
    await fillField('methodology', 'VM0015');
    await fillField('location', 'Brazil');

    // Submit the form
    await clickButton('Create');

    // Verify the carbon credit was created
    await expectToBeOnPage('/carbon-credits');
    await expectToSee('Test Carbon Credit');
  });
});
```

## Deployment Process

The platform uses Docker and Docker Compose for deployment:

### Development Deployment

```bash
# Build and start the development environment
pnpm docker:dev:build

# Stop the development environment
pnpm docker:dev:stop
```

### Production Deployment

```bash
# Build and start the production environment
pnpm docker:prod:build

# Stop the production environment
pnpm docker:prod:stop
```

### Continuous Integration/Continuous Deployment (CI/CD)

The platform uses GitHub Actions for CI/CD:

1. **Pull Request Workflow**:
   - Run linting and type checking
   - Run unit and integration tests
   - Build the application
   - Deploy to a preview environment

2. **Main Branch Workflow**:
   - Run linting and type checking
   - Run unit and integration tests
   - Build the application
   - Deploy to the staging environment
   - Run end-to-end tests
   - Deploy to the production environment

## Performance Optimization

The platform implements several performance optimizations:

### Database Optimization

- Use indexes for frequently queried fields
- Use pagination for large result sets
- Use database transactions for atomic operations
- Use connection pooling for efficient database connections

### Frontend Optimization

- Use React.memo for expensive components
- Use useMemo and useCallback for expensive calculations
- Implement code splitting with dynamic imports
- Use image optimization with Next.js Image component
- Implement skeleton loading for better perceived performance

### Blockchain Optimization

- Use batched transactions for multiple operations
- Implement gas estimation and optimization
- Use caching for blockchain data
- Implement retry mechanisms for failed transactions
- Use WebSockets for real-time updates

## Security Considerations

The platform implements several security measures:

### Authentication and Authorization

- Use NextAuth.js for secure authentication
- Implement role-based access control (RBAC)
- Use JWT with short expiration times
- Implement multi-factor authentication
- Use secure password hashing with bcrypt

### Data Protection

- Use HTTPS for all communications
- Encrypt sensitive data at rest
- Implement proper input validation
- Use parameterized queries to prevent SQL injection
- Implement rate limiting to prevent brute force attacks

### Blockchain Security

- Use secure key management
- Implement transaction signing
- Use smart contract auditing
- Implement gas limits to prevent DoS attacks
- Use secure random number generation
- Make components reusable and self-contained
- Ensure components are accessible

### API Development

- Use consistent response formats
- Implement proper error handling
- Validate all inputs using Zod schemas
- Add rate limiting for public endpoints

### Testing

- Write unit tests for utility functions
- Write integration tests for API endpoints
- Write component tests for UI components
- Use mock data for testing

### Security

- Validate all user inputs
- Implement proper authentication and authorization
- Use HTTPS for all communications
- Encrypt sensitive data
- Implement rate limiting and CSRF protection

## Tenant Isolation System

The platform implements a robust tenant isolation system to ensure proper multi-tenancy and prevent cross-tenant data leakage.

```mermaid
flowchart TD
    A[API Request] --> B{Tenant Middleware}
    B -->|Authorized| C[API Handler]
    B -->|Unauthorized| D[403 Forbidden]

    C --> E{Database Query}
    E --> F[withTenantIsolation]
    F --> G[Prisma Query]

    G --> H{Resource Access}
    H --> I[canAccessResource]
    I -->|Authorized| J[Return Data]
    I -->|Unauthorized| K[Access Denied]

    subgraph "Tenant Isolation System"
        B
        F
        I
    end
```

### Key Components

1. **Middleware for API Routes**: Ensures that API requests can only access data from the user's organization
2. **Database Query Utilities**: Modifies database queries to include organization filters
3. **Resource Access Checks**: Verifies that a user can access specific resources

### Key Files

- `src/lib/tenant-isolation.ts`: Core tenant isolation implementation
- `src/middleware/tenant-middleware.ts`: Backward-compatible middleware (deprecated)
- `src/lib/multi-tenant.ts`: Backward-compatible utilities (deprecated)

For detailed documentation on the tenant isolation system, see [Tenant Isolation System](./tenant-isolation.md).
