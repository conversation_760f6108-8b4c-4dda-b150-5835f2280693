# Tenant Isolation System

This document describes the tenant isolation system used in the Carbonix platform to ensure proper multi-tenancy.

## Overview

The tenant isolation system ensures that users can only access data from their own organization, preventing cross-tenant data leakage. It provides both middleware for API routes and utility functions for database queries.

## Key Components

The tenant isolation system consists of the following key components:

1. **Middleware for API Routes**: Ensures that API requests can only access data from the user's organization
2. **Database Query Utilities**: Modifies database queries to include organization filters
3. **Resource Access Checks**: Verifies that a user can access specific resources

## Using the Tenant Isolation System

### API Route Protection

To protect an API route with tenant isolation, use one of the middleware functions:

```typescript
import { withTenantIsolation, withOrganizationIsolation, withResourceIsolation } from '@/lib/tenant-isolation';

// General tenant isolation
export const GET = withTenantIsolation(async (req, { params }) => {
  // Handler code here
});

// Organization-specific isolation
export const GET = withOrganizationIsolation('orgId')(async (req, { params }) => {
  // Handler code here
});

// Resource-specific isolation
export const GET = withResourceIsolation('carbon_credit', 'id')(async (req, { params }) => {
  // Handler code here
});
```

You can also use the more flexible `createTenantMiddleware` function for custom isolation:

```typescript
import { createTenantMiddleware } from '@/lib/tenant-isolation';

const customIsolation = createTenantMiddleware({
  resourceType: 'carbon_credit',
  resourceIdParam: 'creditId'
});

export const GET = customIsolation(async (req, { params }) => {
  // Handler code here
});
```

### Database Query Isolation

To apply tenant isolation to database queries, use the `withTenantIsolation` function:

```typescript
import { getTenantContext, withTenantIsolation } from '@/lib/tenant-isolation';

async function getProjectsForUser(userId: string) {
  // Get tenant context
  const tenantContext = await getTenantContext(userId);
  
  // Create base query
  let query = {
    where: {
      // Your conditions here
    },
    include: {
      // Your includes here
    }
  };
  
  // Apply tenant isolation
  query = withTenantIsolation(query, tenantContext);
  
  // Execute query
  return db.project.findMany(query);
}
```

For orders, which don't have a direct organization relationship, use `withOrderTenantIsolation`:

```typescript
import { getTenantContext, withOrderTenantIsolation } from '@/lib/tenant-isolation';

async function getOrdersForUser(userId: string) {
  // Get tenant context
  const tenantContext = await getTenantContext(userId);
  
  // Create base query
  let query = {
    where: {
      // Your conditions here
    },
    include: {
      // Your includes here
    }
  };
  
  // Apply order tenant isolation
  query = withOrderTenantIsolation(query, tenantContext);
  
  // Execute query
  return db.order.findMany(query);
}
```

### Resource Access Checks

To check if a user can access a specific resource, use the `canAccessResource` function:

```typescript
import { canAccessResource } from '@/lib/tenant-isolation';

async function checkAccess(userId: string, resourceType: string, resourceId: string) {
  const hasAccess = await canAccessResource(userId, resourceType, resourceId);
  
  if (!hasAccess) {
    throw new Error('Access denied');
  }
  
  // Continue with access granted
}
```

## Supported Resource Types

The tenant isolation system supports the following resource types:

- `organization`: Organization resources
- `team`: Team resources
- `carbon_credit`: Carbon credit resources
- `wallet`: Wallet resources
- `user`: User resources
- `project`: Project resources
- `department`: Department resources
- `division`: Division resources
- `marketplace_listing`: Marketplace listing resources
- `marketplace_watchlist`: Marketplace watchlist resources

## Architecture

```mermaid
flowchart TD
    A[API Request] --> B{Tenant Middleware}
    B -->|Authorized| C[API Handler]
    B -->|Unauthorized| D[403 Forbidden]
    
    C --> E{Database Query}
    E --> F[withTenantIsolation]
    F --> G[Prisma Query]
    
    G --> H{Resource Access}
    H --> I[canAccessResource]
    I -->|Authorized| J[Return Data]
    I -->|Unauthorized| K[Access Denied]
    
    subgraph "Tenant Isolation System"
        B
        F
        I
    end
```

## Best Practices

1. **Always use tenant isolation middleware for API routes**: This ensures that all API routes are properly isolated.
2. **Apply tenant isolation to all database queries**: This prevents cross-tenant data leakage.
3. **Check resource access before performing operations**: This ensures that users can only access resources they are authorized to access.
4. **Use the appropriate isolation function**: Use `withTenantIsolation` for most resources, and `withOrderTenantIsolation` for orders.
5. **Handle access denied cases gracefully**: Return appropriate error messages when access is denied.

## Migration from Legacy System

If you're migrating from the legacy tenant isolation system, here's how to update your code:

### API Routes

```typescript
// Old
import { withTenantIsolation } from '@/middleware/tenant-middleware';

// New
import { withTenantIsolation } from '@/lib/tenant-isolation';
```

### Database Queries

```typescript
// Old
import { getTenantContext, withTenantIsolation } from '@/lib/multi-tenant';

// New
import { getTenantContext, withTenantIsolation } from '@/lib/tenant-isolation';
```

The new system is backward compatible, so existing code should continue to work, but it's recommended to update to the new imports for better maintainability.
