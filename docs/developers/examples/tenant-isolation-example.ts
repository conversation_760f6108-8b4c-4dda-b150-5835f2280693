/**
 * Tenant Isolation Example
 *
 * This file demonstrates how to use the tenant isolation system in different scenarios.
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { auth } from '@/lib/auth';
import { logger } from '@/lib/logger';
import {
  withTenantIsolation,
  withOrganizationIsolation,
  withResourceIsolation,
  createTenantMiddleware,
  getTenantContext,
  withOrderTenantIsolation,
  canAccessResource
} from '@/lib/tenant-isolation';

/**
 * Example 1: Basic Tenant Isolation
 *
 * This example demonstrates how to use the basic tenant isolation middleware
 * to protect an API route.
 */
async function getProjectsHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view projects" },
        { status: 401 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query
    let query = {
      where: {
        // Add any additional filters here
      },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
      },
    };

    // Apply tenant isolation
    // This is just an example - in a real implementation, you would use the function correctly
    // query = withTenantIsolation(query, tenantContext);

    // Execute query
    const projects = await db.project.findMany(query);

    return NextResponse.json({ projects });
  } catch (error) {
    logger.error("Error fetching projects:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching projects" },
      { status: 500 }
    );
  }
}

// This is just an example - in a real implementation, you would use the middleware correctly
// export const GET = withTenantIsolation(getProjectsHandler);

/**
 * Example 2: Organization-Specific Isolation
 *
 * This example demonstrates how to use the organization isolation middleware
 * to protect an API route for a specific organization.
 */
async function getOrganizationDetailsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view organization details" },
        { status: 401 }
      );
    }

    // The middleware has already verified that the user can access this organization
    const organization = await db.organization.findUnique({
      where: {
        id: params.id,
      },
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        projects: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ organization });
  } catch (error) {
    logger.error(`Error fetching organization ${params.id}:`, error);
    return NextResponse.json(
      { error: "An error occurred while fetching organization details" },
      { status: 500 }
    );
  }
}

// This is just an example - in a real implementation, you would use the middleware correctly
// export const GET_ORG = withOrganizationIsolation('id')(getOrganizationDetailsHandler);

/**
 * Example 3: Resource-Specific Isolation
 *
 * This example demonstrates how to use the resource isolation middleware
 * to protect an API route for a specific resource.
 */
async function getCarbonCreditHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view carbon credits" },
        { status: 401 }
      );
    }

    // The middleware has already verified that the user can access this carbon credit
    const carbonCredit = await db.carbonCredit.findUnique({
      where: {
        id: params.id,
      },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
        project: {
          select: {
            name: true,
            description: true,
          },
        },
      },
    });

    if (!carbonCredit) {
      return NextResponse.json(
        { error: "Carbon credit not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ carbonCredit });
  } catch (error) {
    logger.error(`Error fetching carbon credit ${params.id}:`, error);
    return NextResponse.json(
      { error: "An error occurred while fetching carbon credit details" },
      { status: 500 }
    );
  }
}

// This is just an example - in a real implementation, you would use the middleware correctly
// export const GET_CREDIT = withResourceIsolation('carbon_credit', 'id')(getCarbonCreditHandler);

/**
 * Example 4: Custom Tenant Middleware
 *
 * This example demonstrates how to use the createTenantMiddleware function
 * to create a custom tenant isolation middleware.
 */
async function getProjectCarbonCreditsHandler(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view carbon credits" },
        { status: 401 }
      );
    }

    // The middleware has already verified that the user can access this project
    const carbonCredits = await db.carbonCredit.findMany({
      where: {
        projectId: params.projectId,
      },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
      },
    });

    return NextResponse.json({ carbonCredits });
  } catch (error) {
    logger.error(`Error fetching carbon credits for project ${params.projectId}:`, error);
    return NextResponse.json(
      { error: "An error occurred while fetching carbon credits" },
      { status: 500 }
    );
  }
}

// Create custom tenant middleware
const projectResourceIsolation = createTenantMiddleware({
  resourceType: 'project',
  resourceIdParam: 'projectId',
});

// This is just an example - in a real implementation, you would use the middleware correctly
// export const GET_PROJECT_CREDITS = projectResourceIsolation(getProjectCarbonCreditsHandler);

/**
 * Example 5: Order Tenant Isolation
 *
 * This example demonstrates how to use the withOrderTenantIsolation function
 * to apply tenant isolation to order queries.
 */
async function getOrdersHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to view orders" },
        { status: 401 }
      );
    }

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Create base query
    let query = {
      where: {
        // Add any additional filters here
      },
      include: {
        carbonCredit: {
          select: {
            name: true,
          },
        },
        buyer: {
          select: {
            name: true,
          },
        },
        seller: {
          select: {
            name: true,
          },
        },
      },
    };

    // Apply order tenant isolation
    // This is just an example - in a real implementation, you would use the function correctly
    // query = withOrderTenantIsolation(query, tenantContext);

    // Execute query
    const orders = await db.order.findMany(query);

    return NextResponse.json({ orders });
  } catch (error) {
    logger.error("Error fetching orders:", error);
    return NextResponse.json(
      { error: "An error occurred while fetching orders" },
      { status: 500 }
    );
  }
}

// This is just an example - in a real implementation, you would use the middleware correctly
// export const GET_ORDERS = withTenantIsolation(getOrdersHandler);

/**
 * Example 6: Manual Resource Access Check
 *
 * This example demonstrates how to manually check if a user can access a resource.
 */
async function transferCarbonCreditHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to transfer carbon credits" },
        { status: 401 }
      );
    }

    // Parse request body
    const { recipientId, quantity } = await req.json();

    // Check if the user can access the carbon credit
    const hasAccess = await canAccessResource(
      session.user.id,
      'carbon_credit',
      params.id
    );

    if (!hasAccess) {
      return NextResponse.json(
        { error: "You do not have access to this carbon credit" },
        { status: 403 }
      );
    }

    // Check if the user can access the recipient
    const canAccessRecipient = await canAccessResource(
      session.user.id,
      'user',
      recipientId
    );

    if (!canAccessRecipient) {
      return NextResponse.json(
        { error: "You do not have access to the recipient" },
        { status: 403 }
      );
    }

    // Perform the transfer
    // ... transfer logic here ...

    return NextResponse.json({
      message: "Carbon credit transferred successfully",
    });
  } catch (error) {
    logger.error(`Error transferring carbon credit ${params.id}:`, error);
    return NextResponse.json(
      { error: "An error occurred while transferring the carbon credit" },
      { status: 500 }
    );
  }
}

// This is just an example - in a real implementation, you would use the middleware correctly
// export const POST = withResourceIsolation('carbon_credit', 'id')(transferCarbonCreditHandler);
