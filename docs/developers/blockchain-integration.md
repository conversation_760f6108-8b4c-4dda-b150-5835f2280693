# Blockchain Integration

This document provides detailed information about the blockchain integration in the Carbonix platform.

## Overview

Carbonix integrates with multiple blockchain networks to enable tokenization, transfer, and retirement of carbon credits. The platform uses Alchemy's SDK and Account Abstraction to provide a seamless experience for users while ensuring security and efficiency.

## Supported Networks

The platform supports the following blockchain networks:

| Network | Mainnet | Testnet |
|---------|---------|---------|
| Ethereum | ✅ | ✅ (Sepolia) |
| Polygon | ✅ | ✅ (Mumbai) |
| Arbitrum | ✅ | ✅ (Sepolia) |
| Optimism | ✅ | ✅ (Sepolia) |
| Base | ✅ | ✅ (Sepolia) |

## Carbon Credit Token Standard

Carbon credits are tokenized as ERC-1155 tokens, which provide the following benefits:

- **Multi-token Standard**: Support for both fungible and non-fungible tokens
- **Batch Operations**: Efficient gas usage through batch minting, transfers, and retirements
- **Metadata Support**: Rich metadata for carbon credit details
- **Retirement Functionality**: Built-in support for retiring carbon credits

## Smart Contract Architecture

```mermaid
classDiagram
    class CarbonCreditToken {
        +balanceOf(address account, uint256 id) uint256
        +balanceOfBatch(address[] accounts, uint256[] ids) uint256[]
        +setApprovalForAll(address operator, bool approved)
        +isApprovedForAll(address account, address operator) bool
        +safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes data)
        +safeBatchTransferFrom(address from, address to, uint256[] ids, uint256[] amounts, bytes data)
        +mint(address to, uint256 id, uint256 amount, bytes data)
        +mintBatch(address to, uint256[] ids, uint256[] amounts, bytes data)
        +retire(uint256 id, uint256 amount)
        +retireBatch(uint256[] ids, uint256[] amounts)
        +getCarbonCreditData(uint256 id) (string, string, string, string, uint256, uint256)
        +setURI(uint256 id, string newuri)
        +uri(uint256 id) string
    }
```

## Token Metadata

Each tokenized carbon credit includes the following metadata:

```json
{
  "name": "Carbon Credit Name",
  "description": "Detailed description of the carbon credit project",
  "image": "https://carbon-exchange.com/api/carbon-credits/{id}/image",
  "external_url": "https://carbon-exchange.com/dashboard/carbon-credits/{id}",
  "attributes": [
    {
      "trait_type": "Standard",
      "value": "Verra VCS"
    },
    {
      "trait_type": "Methodology",
      "value": "VM0015"
    },
    {
      "trait_type": "Vintage",
      "value": 2023
    },
    {
      "trait_type": "Quantity",
      "value": 1000,
      "display_type": "number"
    },
    {
      "trait_type": "Price",
      "value": 15.5,
      "display_type": "number"
    },
    {
      "trait_type": "Status",
      "value": "TOKENIZED"
    }
  ],
  "properties": {
    "issuer": {
      "name": "Organization Name",
      "logo": "https://carbon-exchange.com/logos/org.png",
      "website": "https://organization.com"
    },
    "project": {
      "id": "project-123",
      "name": "Rainforest Conservation Project",
      "location": "Amazon, Brazil",
      "description": "Detailed project description"
    },
    "verifications": [
      {
        "id": "verification-123",
        "type": "METHODOLOGY",
        "date": "2023-01-15T00:00:00Z"
      }
    ],
    "documents": [
      {
        "url": "https://carbon-exchange.com/documents/doc1.pdf",
        "type": "VERIFICATION_REPORT"
      }
    ],
    "tokenId": "*********",
    "tokenAddress": "0x*********0*********0*********0*********0",
    "tokenNetwork": "polygon",
    "tokenChainId": 137
  }
}
```

## Account Abstraction

The platform uses Alchemy's Account Abstraction SDK to provide:

- **Smart Contract Wallets**: Enhanced security and functionality compared to EOAs
- **Gas Optimization**: Efficient gas usage through batching and other optimizations
- **Multi-Chain Support**: Seamless experience across different blockchain networks
- **User Experience**: Simplified transaction flow for non-technical users

### Implementation

The platform implements account abstraction using the latest Alchemy AA SDK:

```typescript
import {
  createLightAccount,
  sendUserOperation,
  getSmartAccountAddress
} from '@/lib/blockchain/account-abstraction';

// Create a smart account
const client = await createLightAccount(privateKey, SupportedNetwork.ETHEREUM, true, true);

// Get the smart account address
const smartAccountAddress = await getSmartAccountAddress(client);

// Send a user operation
const result = await sendUserOperation(client, contractAddress, encodedData);
```

The implementation includes:

- **Standardized Interface**: A unified interface for working with smart accounts
- **Error Handling**: Comprehensive error handling with detailed error messages
- **Gas Sponsorship**: Support for gas sponsorship through Alchemy's Gas Manager
- **Batch Operations**: Support for batching multiple operations into a single transaction

## Blockchain Operations

### Tokenization

The tokenization process involves the following steps:

1. User requests tokenization of a verified carbon credit
2. Platform generates a unique token ID for the carbon credit
3. Platform creates metadata for the token
4. Platform mints the token on the selected blockchain network
5. Platform sets the token URI for metadata access
6. Platform updates the carbon credit status to "TOKENIZED"
7. Platform creates a tokenization record in the database

```mermaid
sequenceDiagram
    participant User
    participant Platform
    participant Blockchain

    User->>Platform: Request Tokenization
    Platform->>Platform: Validate Carbon Credit
    Platform->>Platform: Generate Token ID & Metadata
    Platform->>Blockchain: Mint Token
    Blockchain-->>Platform: Transaction Hash
    Platform->>Blockchain: Set Token URI
    Blockchain-->>Platform: Transaction Hash
    Platform->>Platform: Update Carbon Credit Status
    Platform->>Platform: Create Tokenization Record
    Platform-->>User: Tokenization Complete
```

### Transfer

The transfer process involves the following steps:

1. User requests transfer of tokenized carbon credits
2. Platform validates the request and available balance
3. Platform transfers the tokens on the blockchain
4. Platform updates the carbon credit available quantity
5. Platform creates a transfer record in the database

```mermaid
sequenceDiagram
    participant User
    participant Platform
    participant Blockchain

    User->>Platform: Request Transfer
    Platform->>Platform: Validate Request & Balance
    Platform->>Blockchain: Transfer Tokens
    Blockchain-->>Platform: Transaction Hash
    Platform->>Platform: Update Available Quantity
    Platform->>Platform: Create Transfer Record
    Platform-->>User: Transfer Complete
```

### Retirement

The retirement process involves the following steps:

1. User requests retirement of tokenized carbon credits
2. Platform validates the request and available balance
3. Platform retires the tokens on the blockchain
4. Platform updates the carbon credit available and retired quantities
5. Platform creates a retirement record in the database

```mermaid
sequenceDiagram
    participant User
    participant Platform
    participant Blockchain

    User->>Platform: Request Retirement
    Platform->>Platform: Validate Request & Balance
    Platform->>Blockchain: Retire Tokens
    Blockchain-->>Platform: Transaction Hash
    Platform->>Platform: Update Quantities
    Platform->>Platform: Create Retirement Record
    Platform-->>User: Retirement Complete
```

## Transaction Handling

The platform implements a robust transaction handling system with standardized error handling and retry logic.

### Transaction Service

The `TransactionService` provides a unified interface for sending and managing blockchain transactions:

```typescript
import { TransactionService, TransactionOptions } from '@/lib/blockchain/transaction-service';

// Create a transaction service for a specific network
const txService = new TransactionService(SupportedNetwork.ETHEREUM, true);

// Send a transaction with retry logic
const result = await txService.sendTransaction(wallet, {
  to: recipientAddress,
  value: "0.01", // ETH
}, {
  maxRetries: 3,
  waitForConfirmation: true,
});

// Get transaction status
const status = await txService.getTransactionStatus(txHash);
```

### Retry Logic

The platform implements automatic retry logic for blockchain transactions:

1. **Exponential Backoff**: Retries with increasing delays between attempts
2. **Error Classification**: Only retries for specific error types (network, gas price, nonce, etc.)
3. **Gas Price Adjustment**: Automatically increases gas price for retries
4. **Maximum Retries**: Configurable maximum number of retry attempts

### Transaction Status Tracking

```mermaid
stateDiagram-v2
    [*] --> PENDING
    PENDING --> MINING
    PENDING --> DROPPED
    MINING --> CONFIRMED
    MINING --> FAILED
    DROPPED --> [*]
    CONFIRMED --> [*]
    FAILED --> [*]
```

## Gas Estimation

The platform provides gas estimation for all blockchain operations to help users understand the cost of transactions before submitting them.

### Estimation Process

1. User initiates a blockchain operation (tokenize, transfer, retire)
2. Platform simulates the transaction on the blockchain
3. Platform calculates the gas required for the operation
4. Platform converts the gas cost to native currency (ETH, MATIC, etc.)
5. Platform converts the native currency cost to USD
6. Platform displays the estimated cost to the user

```mermaid
sequenceDiagram
    participant User
    participant Platform
    participant Blockchain

    User->>Platform: Initiate Operation
    Platform->>Blockchain: Simulate Transaction
    Blockchain-->>Platform: Gas Estimate
    Platform->>Platform: Calculate Native Currency Cost
    Platform->>Platform: Convert to USD
    Platform-->>User: Display Estimated Cost
    User->>Platform: Confirm Operation
    Platform->>Blockchain: Submit Transaction
```

## Transaction History

The platform maintains a comprehensive transaction history for all blockchain operations, including:

- Tokenization transactions
- Transfer transactions
- Retirement transactions

Each transaction record includes:

- Transaction type
- Amount
- Status
- Transaction hash
- Block explorer link
- Timestamp
- Network information
- Contract address
- Additional details specific to the transaction type

## Implementation Details

### Blockchain Service

The blockchain integration is organized into several key modules:

#### Transaction Service (`transaction-service.ts`)

The `TransactionService` provides a unified interface for sending and managing blockchain transactions:

- `sendTransaction`: Send a transaction with retry logic
- `waitForTransaction`: Wait for a transaction to be confirmed
- `getTransactionStatus`: Get the status of a transaction
- `isTransactionPending`: Check if a transaction is pending
- `prepareTransactionRequest`: Prepare a transaction request with gas estimates

#### Alchemy Configuration (`alchemy-config.ts`)

The `alchemy-config.ts` file provides utilities for configuring and creating Alchemy SDK instances:

- `getAlchemyNetwork`: Map a supported network to an Alchemy network
- `getViemChain`: Map a supported network to a viem chain
- `createAlchemyInstance`: Create an Alchemy SDK instance with enhanced configuration
- `createAlchemyTransport`: Create an Alchemy transport for Account Kit

#### Account Abstraction (`account-abstraction.ts`)

The `account-abstraction.ts` file provides a unified interface for working with Ethereum account abstraction:

- `createLightAccount`: Create a smart account client
- `createLightAccountFromEncryptedKey`: Create a smart account from an encrypted private key
- `sendUserOperation`: Send a user operation using a smart account client
- `sendBatchUserOperation`: Send a batch of user operations
- `estimateUserOperationGas`: Estimate gas for a user operation
- `getSmartAccountAddress`: Get the smart account address

#### Alchemy AA SDK Integration (`alchemy/index.ts`)

The `alchemy/index.ts` file provides a unified interface for working with the Alchemy AA SDK:

- `createAlchemySmartAccountClient`: Create a smart account client with the Alchemy provider
- `sendUserOperation`: Send a user operation with the smart account client
- `estimateUserOperationGas`: Estimate gas for a user operation
- `getSmartAccountAddress`: Get the smart account address

#### Blockchain Core (`blockchain.ts`)

The `blockchain.ts` file provides the following functions:

- `verifyTransaction`: Verify a transaction on the blockchain
- `getCarbonCreditContractAddress`: Get the contract address for a network
- `tokenizeCarbonCredit`: Tokenize a carbon credit on the blockchain
- `transferCarbonCredit`: Transfer tokenized carbon credits
- `retireCarbonCredit`: Retire tokenized carbon credits
- `getCarbonCreditBalance`: Get carbon credit token balance

### Gas Estimation Service

The gas estimation service provides functions for estimating gas costs for various operations:

- `estimateTokenizationGas`: Estimate gas for tokenizing a carbon credit
- `estimateTransferGas`: Estimate gas for transferring a carbon credit
- `estimateRetirementGas`: Estimate gas for retiring a carbon credit
- `estimateGasPrice`: Estimate the current gas price for a network
- `convertGasCostToNativeCurrency`: Convert gas cost to native currency
- `convertNativeCurrencyToUSD`: Convert native currency to USD

### API Endpoints

The platform provides the following blockchain-related API endpoints:

- `POST /api/carbon-credits/:id/tokenize`: Tokenize a carbon credit
- `POST /api/carbon-credits/:id/transfer`: Transfer a tokenized carbon credit
- `POST /api/carbon-credits/:id/retire`: Retire a tokenized carbon credit
- `GET /api/carbon-credits/:id/transactions`: Get transaction history
- `POST /api/gas-estimation/tokenize`: Estimate gas for tokenization
- `POST /api/gas-estimation/transfer`: Estimate gas for transfer
- `POST /api/gas-estimation/retire`: Estimate gas for retirement

## Security Considerations

The platform implements the following security measures for blockchain operations:

- **Encrypted Private Keys**: Private keys are encrypted before storage
- **Access Control**: Only authorized users can perform blockchain operations
- **Transaction Validation**: All transactions are validated before submission
- **Error Handling**: Robust error handling for blockchain operations
- **Audit Logging**: Comprehensive audit logging for all blockchain operations
- **Retry Limits**: Maximum retry limits to prevent infinite retry loops
- **Gas Price Caps**: Maximum gas price limits to prevent excessive gas costs
- **Transaction Timeouts**: Timeouts for transaction confirmation to prevent hanging operations
- **Error Classification**: Classification of errors to determine appropriate handling
- **Secure RPC Endpoints**: Use of secure RPC endpoints for blockchain communication

## Future Enhancements

Planned enhancements for the blockchain integration include:

- **Cross-Chain Bridging**: Enable carbon credits to be bridged between different blockchain networks
- **Layer 2 Support**: Add support for additional Layer 2 solutions
- **Smart Contract Upgrades**: Implement upgradeable smart contracts
- **Decentralized Exchange Integration**: Integrate with decentralized exchanges for trading
- **Carbon Credit NFTs**: Support for non-fungible carbon credits with unique attributes
- **Enhanced Transaction Monitoring**: Real-time monitoring and alerting for transaction status
- **Gas Optimization Strategies**: Advanced gas optimization strategies for complex operations
- **Multi-Signature Support**: Support for multi-signature wallets and governance
- **Transaction Simulation**: Pre-execution simulation of transactions to detect potential issues
- **Blockchain Analytics**: Advanced analytics for blockchain operations and gas usage

## Best Practices

When working with the blockchain integration, follow these best practices:

1. **Always Use the Transaction Service**: Use the `TransactionService` for all blockchain transactions to benefit from standardized error handling and retry logic.

2. **Handle Errors Gracefully**: Always handle errors from blockchain operations and provide user-friendly error messages.

3. **Wait for Confirmations**: Wait for at least one confirmation before considering a transaction complete.

4. **Use Account Abstraction**: Use account abstraction for a better user experience when appropriate.

5. **Monitor Gas Prices**: Monitor gas prices and adjust transaction parameters accordingly.

6. **Test on Testnets**: Always test on testnets before deploying to mainnet.

7. **Use Gas Manager**: Use Alchemy's Gas Manager for sponsored transactions when available.

8. **Implement Proper Error Handling**: Implement proper error handling for all blockchain operations, including retry logic for transient errors.

9. **Validate Inputs**: Validate all inputs before sending transactions to the blockchain.

10. **Monitor Transaction Status**: Monitor the status of transactions and provide feedback to users.
