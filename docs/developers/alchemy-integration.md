# Alchemy Smart Wallet and Portfolio Management Integration

This document provides detailed information about the integration of Alchemy's Smart Wallet and Portfolio Management features into the Carbonix platform.

## Table of Contents

- [Overview](#overview)
- [Smart Wallet Implementation](#smart-wallet-implementation)
- [Portfolio Management](#portfolio-management)
- [Multi-Chain Support](#multi-chain-support)
- [Gas Optimization](#gas-optimization)
- [Enterprise Onboarding](#enterprise-onboarding)
- [Implementation Details](#implementation-details)

## Overview

The Carbonix platform leverages Alchemy's SDK to provide advanced blockchain functionality, including smart contract wallets and comprehensive portfolio management. This integration enhances the platform's capabilities for enterprise users, making it easier to manage carbon credits across multiple blockchain networks.

```mermaid
flowchart TD
    Platform[Carbonix Platform] --> AlchemySDK[Alchemy SDK]
    AlchemySDK --> SmartWallet[Smart Wallet]
    AlchemySDK --> Portfolio[Portfolio Management]
    AlchemySDK --> MultiChain[Multi-Chain Support]
    AlchemySDK --> GasOptimization[Gas Optimization]
    
    SmartWallet --> AccountKit[Account Kit]
    SmartWallet --> KeyManagement[Key Management]
    SmartWallet --> SocialRecovery[Social Recovery]
    
    Portfolio --> TokenBalances[Token Balances]
    Portfolio --> NFTManagement[NFT Management]
    Portfolio --> TransactionHistory[Transaction History]
    
    MultiChain --> Ethereum[Ethereum]
    MultiChain --> Polygon[Polygon]
    MultiChain --> Arbitrum[Arbitrum]
    MultiChain --> Optimism[Optimism]
    MultiChain --> Base[Base]
    
    GasOptimization --> EIP1559[EIP-1559 Support]
    GasOptimization --> GasEstimation[Gas Estimation]
    GasOptimization --> CongestionDetection[Congestion Detection]
```

## Smart Wallet Implementation

### Account Kit Integration

The platform integrates with Alchemy's Account Kit to provide smart contract wallets with enhanced security and features.

```mermaid
sequenceDiagram
    participant User
    participant Platform
    participant AccountKit
    participant Blockchain
    
    User->>Platform: Request Smart Wallet
    Platform->>AccountKit: Create Smart Wallet
    AccountKit->>Blockchain: Deploy Smart Contract
    Blockchain-->>AccountKit: Return Contract Address
    AccountKit-->>Platform: Return Wallet Details
    Platform->>Platform: Store Wallet Information
    Platform-->>User: Return Wallet Address
```

### Key Files

- `src/lib/smart-wallet.ts`: Core implementation of smart wallet functionality
- `src/app/api/wallet/smart-wallet/route.ts`: API endpoint for smart wallet operations
- `src/components/wallet/smart-wallet.tsx`: UI component for smart wallet management

### Key Features

1. **Secure Key Management**
   - Private keys are encrypted before storage
   - Keys are only decrypted when needed for transactions
   - Encryption uses a secure key derived from environment variables

2. **Social Recovery**
   - Support for adding guardian addresses
   - Recovery process for lost keys
   - Time-locked recovery to prevent unauthorized access

3. **Batched Transactions**
   - Support for batching multiple operations into a single transaction
   - Reduces gas costs for complex operations
   - Improves user experience for multi-step processes

## Portfolio Management

### Token and NFT Tracking

The platform provides comprehensive portfolio management across multiple chains:

```mermaid
flowchart TD
    Portfolio[Portfolio Management] --> TokenTracking[Token Tracking]
    Portfolio --> NFTTracking[NFT Tracking]
    Portfolio --> TransactionHistory[Transaction History]
    
    TokenTracking --> FetchBalances[Fetch Token Balances]
    TokenTracking --> TokenMetadata[Get Token Metadata]
    TokenTracking --> DisplayBalances[Display Balances]
    
    NFTTracking --> FetchNFTs[Fetch NFTs]
    NFTTracking --> NFTMetadata[Get NFT Metadata]
    NFTTracking --> DisplayNFTs[Display NFTs]
    
    TransactionHistory --> FetchTransfers[Fetch Asset Transfers]
    TransactionHistory --> CategorizeTransactions[Categorize Transactions]
    TransactionHistory --> DisplayHistory[Display History]
```

### Key Files

- `src/lib/blockchain-client.ts`: Client for interacting with blockchain networks
- `src/app/api/wallet/portfolio/route.ts`: API endpoint for portfolio data
- `src/components/wallet/portfolio.tsx`: UI component for portfolio display

### Key Features

1. **Multi-Token Support**
   - Tracking of ERC-20 tokens across multiple networks
   - Token metadata retrieval (name, symbol, decimals)
   - Real-time balance updates

2. **NFT Management**
   - Support for ERC-721 and ERC-1155 tokens
   - NFT metadata and media display
   - NFT transfer capabilities

3. **Transaction History**
   - Comprehensive transaction history across all networks
   - Categorization of transactions (in/out, token type)
   - Links to block explorers for transaction details

## Multi-Chain Support

### Network Configuration

The platform supports multiple blockchain networks through a flexible configuration system:

```mermaid
flowchart TD
    Config[Network Configuration] --> Mainnet[Mainnet Networks]
    Config --> Testnet[Testnet Networks]
    
    Mainnet --> EthMainnet[Ethereum Mainnet]
    Mainnet --> PolyMainnet[Polygon Mainnet]
    Mainnet --> ArbMainnet[Arbitrum Mainnet]
    Mainnet --> OptMainnet[Optimism Mainnet]
    Mainnet --> BaseMainnet[Base Mainnet]
    
    Testnet --> EthTestnet[Ethereum Sepolia]
    Testnet --> PolyTestnet[Polygon Mumbai]
    Testnet --> ArbTestnet[Arbitrum Sepolia]
    Testnet --> OptTestnet[Optimism Sepolia]
    Testnet --> BaseTestnet[Base Sepolia]
    
    EthMainnet --> EthConfig[Chain ID: 1]
    PolyMainnet --> PolyConfig[Chain ID: 137]
    ArbMainnet --> ArbConfig[Chain ID: 42161]
    OptMainnet --> OptConfig[Chain ID: 10]
    BaseMainnet --> BaseConfig[Chain ID: 8453]
    
    EthTestnet --> EthTestConfig[Chain ID: 11155111]
    PolyTestnet --> PolyTestConfig[Chain ID: 80001]
    ArbTestnet --> ArbTestConfig[Chain ID: 421614]
    OptTestnet --> OptTestConfig[Chain ID: 11155420]
    BaseTestnet --> BaseTestConfig[Chain ID: 84532]
```

### Key Files

- `src/lib/blockchain-config.ts`: Configuration for different blockchain networks
- `src/lib/blockchain-client.ts`: Client for interacting with blockchain networks
- `src/components/wallet/multi-chain-wallet.tsx`: UI component for multi-chain wallet management

### Key Features

1. **Network Selection**
   - Support for mainnet and testnet environments
   - Easy switching between networks
   - Network-specific configuration (RPC URLs, block explorers)

2. **Chain ID Tracking**
   - Proper chain ID tracking for all networks
   - Prevention of cross-chain transaction errors
   - Support for EIP-155 replay protection

3. **Network-Specific Features**
   - Support for network-specific features and limitations
   - Adaptation to different gas models
   - Network-specific error handling

## Gas Optimization

### Gas Strategies

The platform includes advanced gas optimization strategies:

```mermaid
flowchart TD
    GasOptimizer[Gas Optimizer] --> GasStrategy[Gas Strategy]
    GasOptimizer --> NetworkMonitoring[Network Monitoring]
    GasOptimizer --> TransactionSimulation[Transaction Simulation]
    
    GasStrategy --> SlowStrategy[Slow Strategy]
    GasStrategy --> AverageStrategy[Average Strategy]
    GasStrategy --> FastStrategy[Fast Strategy]
    GasStrategy --> CustomStrategy[Custom Strategy]
    
    NetworkMonitoring --> BaseFeeTracking[Base Fee Tracking]
    NetworkMonitoring --> CongestionDetection[Congestion Detection]
    NetworkMonitoring --> GasHistoryAnalysis[Gas History Analysis]
    
    TransactionSimulation --> GasEstimation[Gas Estimation]
    TransactionSimulation --> FailureDetection[Failure Detection]
    TransactionSimulation --> OptimizationSuggestions[Optimization Suggestions]
```

### Key Files

- `src/lib/gas-optimizer.ts`: Gas optimization strategies
- `src/app/api/wallet/send/route.ts`: API endpoint for sending transactions with gas optimization

### Key Features

1. **EIP-1559 Support**
   - Support for EIP-1559 fee structure
   - Dynamic base fee and priority fee calculation
   - Adaptation to network conditions

2. **Gas Price Estimation**
   - Historical gas price analysis
   - Network congestion detection
   - Urgency-based fee adjustment

3. **Transaction Simulation**
   - Pre-transaction simulation to detect failures
   - Gas limit estimation with safety buffer
   - Optimization suggestions for complex transactions

## Enterprise Onboarding

### Streamlined Wallet Setup

The platform provides a streamlined onboarding process for enterprises:

```mermaid
flowchart TD
    Onboarding[Enterprise Onboarding] --> Registration[Organization Registration]
    Registration --> Verification[Organization Verification]
    Verification --> WalletSetup[Wallet Setup]
    
    WalletSetup --> NetworkSelection[Network Selection]
    WalletSetup --> WalletType[Wallet Type Selection]
    WalletSetup --> SecuritySetup[Security Setup]
    
    NetworkSelection --> TestnetOption[Testnet for Testing]
    NetworkSelection --> MainnetOption[Mainnet for Production]
    
    WalletType --> EOAOption[Regular Wallet]
    WalletType --> SmartWalletOption[Smart Wallet]
    
    SecuritySetup --> KeyManagement[Key Management]
    SecuritySetup --> RecoverySetup[Recovery Setup]
    SecuritySetup --> TeamAccess[Team Access Configuration]
```

### Key Files

- `src/app/onboarding/page.tsx`: Onboarding UI
- `src/app/api/organizations/route.ts`: API endpoint for organization management
- `src/app/api/wallet/create/route.ts`: API endpoint for wallet creation

### Key Features

1. **Guided Setup Process**
   - Step-by-step wallet creation process
   - Clear explanations of options and implications
   - Recommendations based on organization needs

2. **Team Management**
   - Role-based access control for wallet operations
   - Multi-signature approval for high-value transactions
   - Activity logging for audit purposes

3. **Testing Environment**
   - Testnet environment for practice and testing
   - Faucet integration for obtaining test tokens
   - Seamless transition to mainnet when ready

## Implementation Details

### Smart Wallet Creation

```typescript
/**
 * Create a new Smart Wallet using Alchemy's Account Kit
 * @param userId User ID for reference
 * @returns Smart wallet details
 */
export async function createSmartWallet(userId: string) {
  try {
    // In a production environment, we would use Alchemy's Account Kit
    // to create a smart contract wallet. For this example, we'll simulate it.
    
    // Generate a new EOA (Externally Owned Account) as the owner
    const ownerWallet = ethers.Wallet.createRandom();
    const ownerAddress = ownerWallet.address;
    
    // Encrypt the owner's private key
    const encryptedOwnerKey = await ownerWallet.encrypt(
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );
    
    // Generate a simulated smart wallet address
    // In production, this would be a deployed smart contract wallet
    const smartWalletAddress = ethers.getCreateAddress({
      from: ownerAddress,
      nonce: 0,
    });
    
    logger.info(`Created smart wallet for user ${userId}: ${smartWalletAddress}`);
    
    return {
      smartWalletAddress,
      ownerAddress,
      encryptedOwnerKey,
      // In production, we would return additional details like:
      // factoryAddress: "0x...", // Address of the smart wallet factory
      // implementation: "0x...", // Address of the implementation contract
    };
  } catch (error) {
    logger.error("Error creating smart wallet:", error);
    throw new Error("Failed to create smart wallet");
  }
}
```

### Portfolio Retrieval

```typescript
/**
 * Get portfolio balances for a wallet
 * @param address Wallet address
 * @returns Portfolio details including tokens and NFTs
 */
export async function getPortfolio(address: string) {
  try {
    // Get ETH balance
    const ethBalance = await alchemy.core.getBalance(address);
    const ethBalanceFormatted = ethers.formatEther(ethBalance);
    
    // Get ERC-20 token balances
    const tokenBalances = await alchemy.core.getTokenBalances(address);
    
    // Get token metadata for non-zero balances
    const tokenMetadata = await Promise.all(
      tokenBalances.tokenBalances
        .filter(token => token.tokenBalance !== "0")
        .map(async token => {
          const metadata = await alchemy.core.getTokenMetadata(token.contractAddress);
          return {
            ...metadata,
            contractAddress: token.contractAddress,
            balance: token.tokenBalance,
            formattedBalance: token.tokenBalance && metadata.decimals 
              ? ethers.formatUnits(token.tokenBalance, metadata.decimals)
              : "0",
          };
        })
    );
    
    // Get NFTs owned by the address
    const nftsResponse = await alchemy.nft.getNftsForOwner(address);
    const nfts = nftsResponse.ownedNfts.map(nft => ({
      contractAddress: nft.contract.address,
      tokenId: nft.tokenId,
      name: nft.title,
      description: nft.description,
      tokenType: nft.tokenType,
      media: nft.media,
    }));
    
    logger.info(`Retrieved portfolio for ${address}: ${tokenMetadata.length} tokens, ${nfts.length} NFTs`);
    
    return {
      address,
      nativeBalance: {
        eth: ethBalanceFormatted,
        wei: ethBalance.toString(),
      },
      tokens: tokenMetadata,
      nfts,
    };
  } catch (error) {
    logger.error(`Error getting portfolio for ${address}:`, error);
    throw new Error("Failed to get portfolio");
  }
}
```

### Gas Optimization

```typescript
/**
 * Get optimized gas parameters
 * @param options Gas options
 * @returns Optimized gas parameters
 */
async getOptimizedGasParams(options: GasOptions = {}) {
  try {
    // Get current gas prices
    const gasPrices = await this.client.getGasPrices();
    
    // If custom strategy with explicit parameters, use those
    if (options.strategy === GasStrategy.CUSTOM) {
      if (gasPrices.eip1559) {
        return {
          maxFeePerGas: options.maxFeePerGas,
          maxPriorityFeePerGas: options.maxPriorityFeePerGas,
          gasLimit: options.gasLimit,
          nonce: options.nonce,
        };
      } else {
        return {
          gasPrice: options.gasPrice,
          gasLimit: options.gasLimit,
          nonce: options.nonce,
        };
      }
    }
    
    // Otherwise, use the selected strategy
    const strategy = options.strategy || GasStrategy.AVERAGE;
    
    if (gasPrices.eip1559) {
      return {
        maxFeePerGas: gasPrices[strategy].maxFeePerGas,
        maxPriorityFeePerGas: gasPrices[strategy].maxPriorityFeePerGas,
        gasLimit: options.gasLimit,
        nonce: options.nonce,
      };
    } else {
      return {
        gasPrice: gasPrices[strategy].gasPrice,
        gasLimit: options.gasLimit,
        nonce: options.nonce,
      };
    }
  } catch (error) {
    logger.error("Error getting optimized gas parameters:", error);
    throw new Error("Failed to get optimized gas parameters");
  }
}
```

## Conclusion

The integration of Alchemy's Smart Wallet and Portfolio Management features significantly enhances the Carbonix platform's capabilities. It provides enterprises with a secure, efficient, and user-friendly way to manage carbon credits across multiple blockchain networks.

The implementation follows best practices for blockchain integration, with a focus on security, usability, and gas optimization. The multi-chain support ensures that enterprises can operate on their preferred networks, while the smart wallet features provide enhanced security and recovery options.

This integration is a key differentiator for the Carbonix platform, providing enterprise users with advanced blockchain capabilities in an accessible and intuitive interface.
