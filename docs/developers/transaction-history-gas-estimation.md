# Transaction History and Gas Estimation

This document provides detailed information about the transaction history and gas estimation features in the Carbonix platform.

## Transaction History

The transaction history feature provides a comprehensive view of all blockchain transactions related to carbon credits, including tokenization, transfers, and retirements.

### Overview

The transaction history component:
- Displays all blockchain transactions for a carbon credit
- Shows transaction details including type, amount, status, and date
- Provides links to block explorers for each transaction
- Includes a refresh button to fetch the latest transactions
- Implements proper loading states and error handling

### Implementation

#### API Endpoint

The platform provides a dedicated API endpoint for fetching transaction history:

```
GET /api/carbon-credits/:id/transactions
```

This endpoint:
- Requires authentication
- Validates the user's access to the carbon credit
- Retrieves all transactions related to the carbon credit
- Formats the transactions for display
- Returns the transactions sorted by date (newest first)

#### Database Schema

The transaction history is stored across multiple tables:

- `Tokenization`: Records of tokenized carbon credits
- `TokenTransfer`: Records of token transfers
- `Retirement`: Records of token retirements

Each record includes:
- Transaction type
- Amount
- Status
- Transaction hash
- Network information
- Contract address
- Timestamp
- Additional details specific to the transaction type

#### UI Component

The `TransactionHistory` component:
- Fetches transaction data from the API
- Displays transactions in a table format
- Provides filtering and sorting options
- Shows transaction details
- Includes links to block explorers
- Handles loading states and errors
- Provides a refresh button

```tsx
// Example usage of the TransactionHistory component
<TransactionHistory carbonCreditId={carbonCredit.id} />
```

### User Experience

The transaction history feature enhances user experience by:
- Providing transparency into all blockchain operations
- Enabling verification of transactions on the blockchain
- Offering a clean, organized view of all activities
- Making it easy to track the lifecycle of carbon credits

## Gas Estimation

The gas estimation feature provides users with cost estimates for blockchain operations before they submit transactions.

### Overview

The gas estimation system:
- Estimates gas costs for tokenization, transfer, and retirement operations
- Provides real-time gas estimates as users fill out forms
- Displays gas costs in both native currency (ETH, MATIC, etc.) and USD
- Breaks down gas costs by operation
- Updates estimates when form values change

### Implementation

#### Gas Estimation Service

The `gas-estimation.ts` file provides the core functionality:

```typescript
// Example of the gas estimation service
export class GasEstimationService {
  async estimateTokenizationGas(
    tokenId: number,
    amount: number,
    ownerAddress: string,
    metadata: object,
    network: SupportedNetwork,
    useTestnet: boolean
  ) {
    // Implementation details
  }
  
  async estimateTransferGas(
    fromAddress: string,
    toAddress: string,
    tokenId: number,
    amount: number,
    network: SupportedNetwork,
    useTestnet: boolean
  ) {
    // Implementation details
  }
  
  async estimateRetirementGas(
    ownerAddress: string,
    tokenId: number,
    amount: number,
    network: SupportedNetwork,
    useTestnet: boolean
  ) {
    // Implementation details
  }
}
```

#### API Endpoints

The platform provides dedicated API endpoints for gas estimation:

```
POST /api/gas-estimation/tokenize
POST /api/gas-estimation/transfer
POST /api/gas-estimation/retire
```

Each endpoint:
- Requires authentication
- Validates the request parameters
- Simulates the transaction on the blockchain
- Calculates the gas cost in native currency and USD
- Returns detailed gas estimation information

#### UI Components

The `GasEstimation` component:
- Fetches gas estimates from the API
- Displays gas costs in a user-friendly format
- Shows a breakdown of gas costs by operation
- Updates estimates when form values change
- Handles loading states and errors

```tsx
// Example usage of the GasEstimation component
<GasEstimation
  carbonCreditId={carbonCredit.id}
  type="tokenize"
  network={SupportedNetwork.POLYGON}
  useTestnet={true}
/>
```

The gas estimation is also integrated into the operation forms:
- `TokenizeButton` component
- `TransferButton` component
- `RetireButton` component

Each form displays gas estimates as users fill out the form, providing immediate feedback on the cost of the operation.

### Technical Details

#### Gas Calculation

Gas costs are calculated as:
```
Total Gas Cost = Gas Used × Gas Price
```

Where:
- **Gas Used**: The computational resources required for the operation
- **Gas Price**: The price per unit of gas, which varies based on network congestion

#### Price Conversion

The platform converts gas costs to USD using:
- Current gas prices from the blockchain
- Native currency prices from price feeds
- Conversion formulas for different networks

#### Optimization

The gas estimation system includes optimizations:
- Caching of gas estimates for similar operations
- Batch estimation for multiple operations
- Fallback mechanisms for network issues
- Throttling to prevent excessive API calls

### User Experience

The gas estimation feature enhances user experience by:
- Providing transparency about transaction costs
- Eliminating surprises when submitting transactions
- Helping users make informed decisions
- Reducing failed transactions due to insufficient gas

## Integration with Blockchain Operations

The transaction history and gas estimation features are tightly integrated with the blockchain operations:

### Tokenization Flow

1. User initiates tokenization
2. System provides gas estimation
3. User confirms tokenization
4. System submits transaction
5. Transaction is recorded in history
6. User can view transaction details

### Transfer Flow

1. User initiates transfer
2. System provides gas estimation
3. User confirms transfer
4. System submits transaction
5. Transaction is recorded in history
6. User can view transaction details

### Retirement Flow

1. User initiates retirement
2. System provides gas estimation
3. User confirms retirement
4. System submits transaction
5. Transaction is recorded in history
6. User can view transaction details

## Future Enhancements

Planned enhancements for these features include:

### Transaction History

- Advanced filtering and search capabilities
- Export functionality (CSV, PDF)
- Aggregated statistics and visualizations
- Integration with reporting tools
- Real-time updates via WebSockets

### Gas Estimation

- Gas price alerts for optimal transaction timing
- Historical gas price trends
- Gas optimization recommendations
- Fee delegation options
- EIP-1559 support for more predictable gas costs
