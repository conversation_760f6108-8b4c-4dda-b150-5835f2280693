# Account Abstraction Implementation

This document describes the implementation of Account Abstraction (AA) in the Carbon Exchange platform using Alchemy's Account Kit.

## Overview

Account Abstraction (ERC-4337) allows for smart contract wallets that provide enhanced security, better user experience, and programmable transaction logic. Our implementation uses Alchemy's Account Kit to create and manage Light Accounts, which are simple, gas-efficient smart contract wallets.

```mermaid
graph TD
    User[User] --> EOA[Externally Owned Account]
    EOA --> SmartAccount[Smart Contract Account]
    SmartAccount --> UserOp[User Operation]
    UserOp --> Bundler[Bundler]
    Bundler --> EntryPoint[EntryPoint Contract]
    EntryPoint --> Execution[Transaction Execution]
    
    Paymaster[Gas Paymaster] --> EntryPoint
    
    subgraph "Account Abstraction Flow"
        SmartAccount
        UserOp
        Bundler
        EntryPoint
        Execution
        Paymaster
    end
```

## Key Components

1. **Light Account**: A simple, gas-efficient smart contract wallet implementation
2. **Gas Manager**: Alchemy's service for sponsoring gas fees for user operations
3. **Bundler**: Alchemy's service for bundling and submitting user operations to the EntryPoint contract

## Environment Variables

The following environment variables are required for the Account Abstraction implementation:

- `ALCHEMY_API_KEY`: Your Alchemy API key
- `ALCHEMY_NETWORK`: The Alchemy network to use (e.g., eth-sepolia, eth-mainnet)
- `ALCHEMY_GAS_MANAGER_POLICY_ID`: Your Alchemy Gas Manager policy ID for sponsored transactions
- `WALLET_ENCRYPTION_KEY`: Key for encrypting wallet private keys

## Implementation Details

### Creating a Smart Account

```typescript
// Create a Light Account client
const lightAccountClient = await createLightAccount(
  privateKey,
  network,
  useTestnet,
  useGasManager
);

// Get the smart account address
const smartAccountAddress = await getSmartAccountAddress(lightAccountClient);
```

### Sending User Operations

```typescript
// Send a user operation
const result = await sendUserOperation(
  lightAccountClient,
  toAddress,
  data,
  value
);

// Send a batch of user operations
const batchResult = await sendBatchUserOperation(
  lightAccountClient,
  operations
);
```

### Gas Sponsorship

Gas sponsorship is implemented using Alchemy's Gas Manager. When enabled, the Gas Manager will pay for the gas fees of user operations, allowing users to interact with the blockchain without needing ETH for gas.

```typescript
// Add gas manager middleware
clientConfig.middleware = alchemyGasAndPaymasterAndDataMiddleware({
  policyId: GAS_MANAGER_POLICY_ID,
  transport: alchemy({ apiKey: ALCHEMY_API_KEY }),
  entryPoint: "******************************************", // Standard ERC-4337 EntryPoint
});
```

## Multi-Chain Support

The implementation supports multiple blockchain networks:

- Ethereum (Mainnet and Sepolia)
- Polygon (Mainnet and Mumbai)
- Optimism (Mainnet and Sepolia)
- Arbitrum (Mainnet and Sepolia)
- Base (Mainnet and Sepolia)

## Security Considerations

1. **Private Key Management**: Private keys are encrypted using the `WALLET_ENCRYPTION_KEY` before being stored in the database.
2. **Smart Account Recovery**: The implementation includes functionality for recovering smart accounts in case of lost private keys.
3. **Transaction Limits**: Smart accounts can have transaction limits and approval requirements for enhanced security.

## Gas Optimization

The implementation includes gas optimization strategies:

1. **Batch Transactions**: Multiple operations can be batched into a single user operation to save gas.
2. **Gas Estimation**: Gas is estimated before sending user operations to ensure sufficient gas is provided.
3. **Gas Sponsorship**: Gas fees can be sponsored using Alchemy's Gas Manager.

## Testing

To test the Account Abstraction implementation:

1. Set up the required environment variables
2. Create a smart account using the `createWallet` function with `isSmartWallet: true`
3. Send a test transaction using the `sendTransaction` function with `isSmartAccount: true`
4. Verify the transaction was successful using the `verifyTransaction` function

## Troubleshooting

Common issues and their solutions:

1. **Missing Environment Variables**: Ensure all required environment variables are set.
2. **Gas Manager Policy ID**: If gas sponsorship is not working, check that the `ALCHEMY_GAS_MANAGER_POLICY_ID` is set correctly.
3. **Network Configuration**: Ensure the correct network is configured for the desired environment (testnet or mainnet).
4. **Transaction Failures**: Check the transaction status and error messages in the logs for details on why a transaction failed.

## References

- [Alchemy Account Kit Documentation](https://docs.alchemy.com/reference/account-kit-overview)
- [ERC-4337 Specification](https://eips.ethereum.org/EIPS/eip-4337)
- [Alchemy Gas Manager Documentation](https://docs.alchemy.com/reference/gas-manager-overview)
