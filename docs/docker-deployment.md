# Docker Deployment Guide

This guide explains how to deploy the Carbonix application using Docker for different environments.

## Prerequisites

- Docker (version 20.10.0 or higher)
- Docker Compose (version 2.0.0 or higher)
- Git

## Quick Setup

For a quick setup of any environment, use the setup script:

```bash
pnpm setup:docker
```

This interactive script will guide you through setting up the development, pre-production, or production environment.

## Environment-Specific Deployment

### Development Environment

The development environment uses test blockchain networks and is configured for local development.

1. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your development configuration.

2. Build and start the development containers:
   ```bash
   pnpm docker:dev:build
   ```

3. Access the application at http://localhost:3000

4. Useful commands:
   ```bash
   # View logs
   pnpm docker:logs

   # Stop the development environment
   pnpm docker:down

   # Start only the database
   pnpm docker:db

   # Stop only the database
   pnpm docker:db:stop
   ```

### Pre-Production Environment

The pre-production environment uses test blockchain networks but is configured like production.

1. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your pre-production configuration.

2. Deploy to pre-production:
   ```bash
   pnpm deploy:preprod
   ```
   This script will guide you through the deployment process, including SSL certificate setup.

3. Alternatively, build and start the pre-production containers manually:
   ```bash
   pnpm docker:preprod:build
   ```

4. Access the application at the URL specified in your NEXTAUTH_URL environment variable.

### Production Environment

The production environment uses mainnet blockchain networks and is configured for production use.

1. Set up environment variables:
   ```bash
   cp .env.production.example .env
   ```
   Edit the `.env` file with your production configuration.

2. Build and start the production containers:
   ```bash
   pnpm docker:prod:build
   ```

3. Initialize SSL certificates (first time only):
   ```bash
   pnpm ssl:init yourdomain.com <EMAIL>
   ```

4. Access the application at the URL specified in your NEXTAUTH_URL environment variable.

## Docker Compose Files

The project includes several Docker Compose files for different environments:

- `docker-compose.yml`: Base configuration for development
- `docker-compose.preprod.yml`: Configuration for pre-production
- `docker-compose.prod.yml`: Configuration for production
- `docker-compose.override.yml`: Additional configuration that can be used with any environment

## Environment Variables

Each environment requires specific environment variables. The most important ones are:

### Database Configuration
- `DATABASE_URL`: PostgreSQL connection string
- `DATABASE_HOST`: PostgreSQL host (usually `db` in Docker)
- `DATABASE_PORT`: PostgreSQL port (usually `5432` inside Docker, mapped to `5433` on host)
- `DATABASE_USER`: PostgreSQL user (usually `postgres`)
- `DATABASE_PASSWORD`: PostgreSQL password

### NextAuth Configuration
- `NEXTAUTH_URL`: URL of the application (e.g., `http://localhost:3000` for development)
- `NEXTAUTH_SECRET`: Secret for NextAuth session encryption

### Blockchain Configuration
- `ALCHEMY_API_KEY`: Alchemy API key for blockchain integration
- `ALCHEMY_NETWORK`: Alchemy network (e.g., `eth-sepolia` for development, `eth-mainnet` for production)
- `ETHEREUM_NETWORK`: Ethereum network (e.g., `sepolia` for development, `mainnet` for production)
- `POLYGON_NETWORK`: Polygon network (e.g., `mumbai` for development, `polygon` for production)
- `OPTIMISM_NETWORK`: Optimism network (e.g., `optimism-sepolia` for development, `optimism` for production)
- `ARBITRUM_NETWORK`: Arbitrum network (e.g., `arbitrum-sepolia` for development, `arbitrum` for production)
- `BASE_NETWORK`: Base network (e.g., `base-sepolia` for development, `base` for production)

## Troubleshooting

### Container Fails to Start

1. Check the logs:
   ```bash
   pnpm docker:logs
   ```

2. Verify environment variables:
   ```bash
   docker compose config
   ```

3. Check if the database is running:
   ```bash
   docker compose ps db
   ```

### Database Connection Issues

1. Ensure the database container is running:
   ```bash
   docker compose ps db
   ```

2. Check database logs:
   ```bash
   docker compose logs db
   ```

3. Verify database connection settings in your `.env` file.

### SSL Certificate Issues

1. Make sure your domain points to the server's IP address.
2. Check if port 80 and 443 are accessible from the internet.
3. Run the SSL initialization script again:
   ```bash
   pnpm ssl:init yourdomain.com <EMAIL>
   ```
