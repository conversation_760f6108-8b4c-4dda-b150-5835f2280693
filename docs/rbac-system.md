# Enhanced Role-Based Access Control (RBAC) System

This document describes the enhanced RBAC system implemented for the Carbon Exchange platform.

> **Note:** This RBAC system is fully multi-tenant, ensuring strict isolation between different organizations (tenants) in the platform.

## Overview

The RBAC system provides a comprehensive approach to managing permissions and access control with the following features:

- **Granular Permissions**: Atomic permissions for fine-grained access control
- **Dynamic Role-Permission Mapping**: Flexible mapping of roles to permissions
- **Custom Roles**: Support for organization-specific custom roles
- **Resource-Level Permissions**: Permissions tied to specific resources
- **Team-Level RBAC Integration**: Team-specific permissions and access controls
- **Role Hierarchy**: Inheritance of permissions through role hierarchies
- **Permission Auditing**: Comprehensive logging of permission changes and usage
- **Dynamic Permission Adjustments**: Context-based permission evaluation
- **Enterprise-Specific Role Management**: Support for complex organizational structures

## Architecture

```mermaid
graph TD
    A[User] --> B[Roles]
    A --> C[Direct Permissions]
    A --> D[Temporary Permissions]
    A --> E[Team Memberships]
    B --> F[Role Permissions]
    E --> G[Team Permissions]
    E --> H[Resource Scopes]

    I[Permission Check] --> J[Check Temporary Permissions]
    J --> K[Check Direct Permissions]
    K --> L[Check Role Permissions]
    L --> M[Check Team Permissions]
    M --> N[Check Resource Permissions]
```

## Database Schema

The RBAC system uses the following database models:

- **Permission**: Defines atomic permissions
- **CustomRole**: Defines roles with associated permissions
- **RolePermission**: Maps roles to permissions
- **UserCustomRole**: Assigns roles to users
- **PermissionGrant**: Grants direct permissions to users
- **ResourcePermission**: Defines resource-level permissions
- **TemporaryPermission**: Grants temporary permissions to users
- **PermissionRequest**: Handles permission request workflows
- **PermissionUsageLog**: Logs permission usage
- **Team**: Defines teams with associated permissions
- **TeamRole**: Defines team-specific roles
- **TeamMember**: Assigns users to teams with roles
- **ResourceScope**: Defines team-specific resource access
- **Department**: Defines organizational departments
- **Division**: Defines organizational divisions
- **OrganizationRbacSettings**: Configures RBAC settings for organizations

## Permission Categories

Permissions are organized into the following categories:

- **Organization**: Permissions related to organization management
- **User Management**: Permissions related to user management
- **Carbon Credit**: Permissions related to carbon credit management
- **Wallet**: Permissions related to wallet management
- **Admin**: Permissions related to platform administration
- **Team**: Permissions related to team management
- **Compliance**: Permissions related to compliance management
- **Billing**: Permissions related to billing management
- **Marketplace**: Permissions related to marketplace management
- **Reporting**: Permissions related to reporting
- **Settings**: Permissions related to settings management

## System Roles

The system defines the following built-in roles:

- **ADMIN**: Platform administrator with full access
- **ORGANIZATION_ADMIN**: Organization administrator with full access to organization features
- **USER**: Regular user with basic access to organization features
- **DEPARTMENT_ADMIN**: Department administrator with administrative access to a department
- **DIVISION_ADMIN**: Division administrator with administrative access to a division

## Predefined Custom Roles

The system provides the following predefined custom roles:

- **CARBON_CREDIT_MANAGER**: Manages carbon credits for the organization
- **WALLET_MANAGER**: Manages wallets for the organization
- **TEAM_MANAGER**: Manages teams for the organization
- **COMPLIANCE_OFFICER**: Manages compliance for the organization
- **FINANCE_MANAGER**: Manages financial aspects for the organization
- **READONLY_USER**: Read-only access to organization features

## Usage

### Server-Side Permission Checking

```typescript
import { hasPermission, requirePermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

// Check if a user has a permission
const context: PermissionContext = {
  userId: 'user-id',
  organizationId: 'org-id',
  resourceType: 'carbon_credit',
  resourceId: 'credit-id',
};

const granted = await hasPermission('update:carbon_credit', context);

// Require a permission (throws an error if not granted)
await requirePermission('update:carbon_credit', context);
```

### Middleware for API Routes

```typescript
import { requirePermission } from '@/middleware/rbac-middleware';

// Protect an API route with a permission check
export const GET = requirePermission('read:carbon_credit', {
  resourceType: 'carbon_credit',
  resourceIdParam: 'id',
  logUsage: true,
})(async (req) => {
  // Handler code here
});
```

### Client-Side Permission Checking

```tsx
import { usePermission } from '@/hooks/use-permissions';
import { PermissionGate } from '@/components/ui/permission-gate';

// Use the permission hook
function MyComponent() {
  const { hasPermission, isLoading } = usePermission('update:carbon_credit', {
    resourceType: 'carbon_credit',
    resourceId: 'credit-id',
  });

  return (
    <div>
      {hasPermission ? <EditButton /> : null}
    </div>
  );
}

// Use the PermissionGate component
function MyOtherComponent() {
  return (
    <PermissionGate
      permission="update:carbon_credit"
      resourceType="carbon_credit"
      resourceId="credit-id"
      fallback={<ReadOnlyView />}
    >
      <EditableView />
    </PermissionGate>
  );
}
```

## Initialization

To initialize the RBAC system, run the following command:

```bash
pnpm rbac:init
```

This will create all permissions and system roles in the database.

## Enterprise Features

### Departments and Divisions

The RBAC system supports enterprise organizational structures with departments and divisions:

```typescript
import { createDepartment, createDivision } from '@/lib/rbac/enterprise-rbac';

// Create a department
const departmentId = await createDepartment(
  'org-id',
  'Finance Department',
  'Handles financial operations',
  'FIN'
);

// Create a division within a department
const divisionId = await createDivision(
  'org-id',
  'Accounting Division',
  'Handles accounting operations',
  'ACC',
  departmentId
);
```

### RBAC Settings

Organizations can configure their RBAC settings:

```typescript
import { updateRbacSettings } from '@/lib/rbac/enterprise-rbac';

// Update RBAC settings
await updateRbacSettings('org-id', {
  enableCustomRoles: true,
  enableResourcePermissions: true,
  enableRoleHierarchy: true,
  enableTemporaryAccess: true,
  enablePermissionRequests: true,
});
```

## Multi-Tenant Architecture

The RBAC system is designed with multi-tenancy as a core principle, ensuring strict isolation between different organizations (tenants):

```mermaid
graph TD
    A[User] --> B[Organization/Tenant]
    B --> C[Resources]
    B --> D[Teams]
    B --> E[Custom Roles]
    B --> F[Departments]
    B --> G[Divisions]

    H[Permission Check] --> I[Tenant Isolation Check]
    I --> J[Permission Evaluation]
```

### Tenant Isolation Features

- **Organization Boundaries**: All resources are scoped to an organization
- **Cross-Organization Protection**: Prevents users from accessing data from other organizations
- **Resource Ownership Verification**: Ensures resources belong to the user's organization
- **Tenant-Aware Middleware**: API routes enforce tenant isolation
- **Audit Logging**: Logs cross-tenant access attempts

### Using Tenant Isolation

```typescript
// In API routes
import { withTenantIsolation, withOrganizationIsolation, withResourceIsolation } from '@/middleware/tenant-middleware';

// General tenant isolation
export const GET = withTenantIsolation(async (req, { params }) => {
  // Handler code here
});

// Organization-specific isolation
export const GET = withOrganizationIsolation('orgId')(async (req, { params }) => {
  // Handler code here
});

// Resource-specific isolation
export const GET = withResourceIsolation('carbon_credit', 'creditId')(async (req, { params }) => {
  // Handler code here
});

// In database queries
import { withTenantIsolation, getTenantContext } from '@/lib/multi-tenant';

// Get tenant context
const context = await getTenantContext(userId);

// Apply tenant isolation to a query
const query = withTenantIsolation({
  where: { status: 'ACTIVE' },
}, context);

// Execute the query
const results = await db.carbonCredit.findMany(query);
```

## Best Practices

1. **Use Atomic Permissions**: Define permissions at a granular level for maximum flexibility.
2. **Leverage Role Hierarchy**: Use role inheritance to avoid permission duplication.
3. **Prefer Role-Based Access**: Assign roles to users rather than direct permissions when possible.
4. **Audit Permission Changes**: Always log permission changes for accountability.
5. **Use Resource-Level Permissions**: Tie permissions to specific resources for fine-grained control.
6. **Implement Least Privilege**: Grant only the permissions necessary for a user's role.
7. **Regular Permission Reviews**: Periodically review and clean up permissions.
8. **Document Custom Roles**: Maintain documentation for custom roles and their permissions.
9. **Test Permission Changes**: Thoroughly test permission changes before deploying to production.
10. **Use Permission Gates in UI**: Consistently use permission gates in the UI to enforce access control.
11. **Enforce Tenant Isolation**: Always use tenant isolation middleware for API routes.
12. **Verify Resource Ownership**: Always verify that resources belong to the user's organization.
