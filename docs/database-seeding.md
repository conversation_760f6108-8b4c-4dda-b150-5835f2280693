# Database Seeding Guide

This guide explains the different seed scripts available in the Carbonx application and how to use them.

## Overview

The application includes several seed scripts for different purposes:

1. **Deployment Seed**: Creates the minimum required data for the application to function properly
2. **Comprehensive Seed**: Populates all tables with sufficient data for development and testing
3. **Basic Seed**: Creates basic carbon credits for an existing organization

## 1. Deployment Seed

The `scripts/deployment-seed.ts` script creates the minimum required data for the application to function properly. This script is automatically run during deployment to pre-production and production environments.

### What it creates:

- Initializes the RBAC system (permissions and roles)
- Creates the platform organization and admin user
- Creates a sample organization with an admin user
- Creates sample projects
- Creates sample carbon credits

### How to run:

```bash
# Run the deployment seed manually
pnpm seed:deployment
```

### Default credentials:

- **Admin User**:
  - Email: <EMAIL>
  - Password: Admin123!

- **Organization Admin**:
  - Email: <EMAIL>
  - Password: Password123!

## 2. Comprehensive Seed

The `scripts/seed-comprehensive.ts` script populates all tables with sufficient data for development and testing. This script is useful for development and testing purposes.

### What it creates:

- Multiple organizations (5-10) with different statuses and verification levels
- Multiple users per organization with different roles
- Multiple projects (10-15) with different types and statuses
- Multiple carbon credits (20-30) with different statuses, standards, and vintages
- Multiple wallets for users and organizations
- Multiple orders and transactions with different statuses
- Multiple tokenizations with different statuses
- Compliance documents and verification records
- Historical data for charts and analytics
- Teams and team members
- Permissions and roles
- Notifications and audit logs

### How to run:

```bash
# Run the comprehensive seed
pnpm prisma:seed:comprehensive
```

## 3. Basic Seed

The `prisma/seed.ts` script is a simpler seed that creates basic carbon credits for an existing organization. This script is automatically run when using `prisma db seed`.

### What it creates:

- Sample carbon credits for an existing organization

### How to run:

```bash
# Run the basic seed
pnpm prisma:seed
```

## Seed Modules

The comprehensive seed script is modular and uses several seed modules located in the `scripts/seed` directory:

- `seed-organizations-users.ts`: Creates organizations and users
- `seed-projects.ts`: Creates projects
- `seed-carbon-credits.ts`: Creates carbon credits
- `seed-wallets-transactions.ts`: Creates wallets and transactions
- `seed-marketplace.ts`: Creates marketplace listings and orders
- `seed-compliance.ts`: Creates compliance documents and verification records
- `seed-notifications-audit.ts`: Creates notifications and audit logs

These modules can be used individually if needed.

## Best Practices

1. **Development Environment**: Use the comprehensive seed script to populate the database with sufficient data for development and testing.

2. **Pre-Production Environment**: Use the deployment seed script to create the minimum required data for the application to function properly.

3. **Production Environment**: Use the deployment seed script to create the minimum required data, then add real data through the application.

4. **Custom Seeding**: If you need to create specific data for testing, you can create a custom seed script using the existing seed modules.

## Troubleshooting

Common issues:

1. **Database connection issues**: Make sure the database is running and accessible.

2. **Duplicate data**: The seed scripts check for existing data before creating new data. If you want to run a seed script again, you may need to clear the database first.

3. **Missing dependencies**: Make sure all required dependencies are installed by running `pnpm install`.

4. **Prisma client not generated**: Make sure the Prisma client is generated by running `pnpm prisma:generate`.

For more detailed information, refer to the [Deployment Guide](deployment-guide.md).
