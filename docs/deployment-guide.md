# Carbonx Deployment Guide

This guide explains how to deploy the Carbonx application in different environments and how to seed the database with initial data.

## Deployment

The application can be deployed to three different environments:

1. **Development**: Local development environment with Docker and test blockchain networks
2. **Pre-Production**: Test environment with test blockchain networks but production-like configuration
3. **Production**: Live environment with real blockchain networks

### Docker Environment Setup Script (Recommended)

The recommended way to deploy the application is using the Docker environment setup script, which provides an interactive setup process:

```bash
# Make the script executable
chmod +x ./scripts/setup-docker-env.sh

# Run the setup script
./scripts/setup-docker-env.sh
```

This script will:
1. Check if Docker and Docker Compose are installed
2. Ask which environment you want to set up (Development, Pre-Production, or Production)
3. Set up environment variables based on your choice
4. Build and start the Docker containers
5. Initialize the database with migrations and seed data
6. Provide login credentials and useful commands

The setup script performs the following tasks for each environment:

#### Development Environment
1. Sets up environment variables for development (test blockchain networks)
2. Builds the app-dev and db-init Docker containers
3. Starts the app-dev container and PostgreSQL database
4. Runs database migrations and seeds essential data
5. Provides admin credentials for logging in

#### Pre-Production Environment
1. Sets up environment variables for pre-production (test blockchain networks with production configuration)
2. Builds all Docker containers including the database, app, and supporting services
3. Starts the pre-production environment
4. Initializes the database with migrations and seed data
5. Provides admin credentials for logging in

#### Production Environment
1. Sets up environment variables for production (mainnet blockchain networks)
2. Builds all Docker containers
3. Starts the production environment
4. Initializes the database with migrations and seed data
5. Provides admin credentials for logging in

### Alternative: Unified Deployment Script

For more advanced deployment options, you can use the enhanced deployment script:

```bash
# Deploy to any environment (interactive)
pnpm deploy

# Deploy to development environment
./scripts/carbonx-deploy.sh 1
# or
pnpm deploy:dev

# Deploy to pre-production environment
./scripts/carbonx-deploy.sh 2
# or
pnpm deploy:preprod

# Deploy to production environment
./scripts/carbonx-deploy.sh 3
# or
pnpm deploy:prod
```

If you run the script without arguments, it will prompt you to choose an environment.

### Docker Configuration

The application uses Docker and Docker Compose for deployment:

- `docker-compose.yml`: Base configuration for development
- `docker-compose.preprod.yml`: Configuration for pre-production
- `docker-compose.prod.yml`: Configuration for production
- `docker-compose.override.yml`: Additional configuration that can be used with any environment

### Environment Variables

Each environment requires specific environment variables. Both deployment scripts will check for a `.env` file and create one from the example if it doesn't exist.

For production, it's recommended to create a `.env` file based on `.env.production.example`.

## Database Seeding

When you run the Docker environment setup script, it automatically initializes the database with essential data. The application includes several seed scripts for different purposes:

### 1. Deployment Seed

The `scripts/deployment-seed.ts` script creates the minimum required data for the application to function properly:

- Initializes the RBAC system (permissions and roles)
- Creates the platform organization and admin user
- Creates a sample organization with an admin user
- Creates sample projects
- Creates sample carbon credits

This script is automatically run during deployment when using the Docker environment setup script.

```bash
# Run the deployment seed manually in Docker
sudo docker compose exec app-dev pnpm seed:deployment
```

### 2. Comprehensive Seed

The `scripts/seed-comprehensive.ts` script populates all tables with sufficient data for development and testing:

- Multiple organizations with different statuses and verification levels
- Multiple users per organization with different roles
- Multiple projects with different types and statuses
- Multiple carbon credits with different statuses, standards, and vintages
- Multiple wallets for users and organizations
- Multiple orders and transactions with different statuses
- Compliance documents and verification records
- Notifications and audit logs

This script is useful for development and testing purposes.

```bash
# Run the comprehensive seed in Docker
sudo docker compose exec app-dev pnpm prisma:seed:comprehensive
```

### 3. Basic Seed

The `prisma/seed.ts` script is a simpler seed that creates basic carbon credits for an existing organization. This script is automatically run when using `prisma db seed`.

```bash
# Run the basic seed in Docker
sudo docker compose exec app-dev pnpm prisma:seed
```

### Default Credentials

After running the deployment seed or using the Docker environment setup script, you can log in with these credentials:

- **Admin User**:
  - Email: <EMAIL>
  - Password: Admin123!

- **Organization Admin**:
  - Email: <EMAIL>
  - Password: Password123!

## Useful Commands

### Docker Commands

```bash
# Run the Docker environment setup script (recommended)
./scripts/setup-docker-env.sh

# Check running containers
sudo docker compose ps

# Start the development environment
sudo docker compose up app-dev

# Build and start the development environment
sudo docker compose build app-dev && sudo docker compose up app-dev

# Start only the database
sudo docker compose up -d db

# Stop the database
sudo docker compose down db

# View logs for all services
sudo docker compose logs -f

# View logs for a specific service
sudo docker compose logs -f app-dev

# Restart all services
sudo docker compose restart

# Stop all services
sudo docker compose down

# Stop and remove containers, networks, and volumes
sudo docker compose down -v

# Build and start the pre-production environment
sudo docker compose -f docker-compose.preprod.yml -f docker-compose.override.yml build && sudo docker compose -f docker-compose.preprod.yml -f docker-compose.override.yml up -d

# Build and start the production environment
sudo docker compose -f docker-compose.prod.yml -f docker-compose.override.yml build && sudo docker compose -f docker-compose.prod.yml -f docker-compose.override.yml up -d
```

### Database Commands

```bash
# Connect to the database from inside Docker
sudo docker compose exec db psql -U postgres -d carbon_exchange

# Generate Prisma client in Docker
sudo docker compose exec app-dev pnpm prisma:generate

# Run database migrations in Docker
sudo docker compose exec app-dev pnpm prisma:migrate

# Open Prisma Studio in Docker
sudo docker compose exec app-dev pnpm prisma studio

# Run the comprehensive seed in Docker
sudo docker compose exec app-dev pnpm prisma:seed:comprehensive

# Export database dump
sudo docker compose exec db pg_dump -U postgres -d carbon_exchange > backup.sql

# Import database dump
cat backup.sql | sudo docker compose exec -T db psql -U postgres -d carbon_exchange
```

### Database Connection Information

When running in Docker, the database is accessible:

- **From inside Docker containers**:
  - Host: `db`
  - Port: `5432`
  - Username: `postgres`
  - Password: `root`
  - Database: `carbon_exchange`
  - Connection URL: `**********************************/carbon_exchange`

- **From your host machine**:
  - Host: `localhost`
  - Port: `5433` (mapped from container port 5432)
  - Username: `postgres`
  - Password: `root`
  - Database: `carbon_exchange`
  - Connection URL: `postgresql://postgres:root@localhost:5433/carbon_exchange`

## Troubleshooting

Common issues:

1. **Docker not running**: Make sure Docker is installed and running
   ```bash
   sudo systemctl start docker
   ```

2. **Permission issues**: If you get permission errors, try using `sudo` with Docker commands or add your user to the docker group:
   ```bash
   sudo usermod -aG docker $USER && newgrp docker
   ```

3. **Database connection issues**: Check if the database container is running and accessible
   ```bash
   sudo docker compose ps db
   sudo docker compose logs db
   ```

4. **Environment variables**: Make sure all required environment variables are set in the `.env` file

5. **Port conflicts**: Check if the required ports (3000, 5433) are available
   ```bash
   sudo lsof -i :3000
   sudo lsof -i :5433
   ```

6. **Container not starting**: Check the logs for errors
   ```bash
   sudo docker compose logs app-dev
   ```

For more detailed information, refer to the [Docker Deployment Guide](docker-deployment.md) and [Docker Cleanup Commands](docker-cleanup-commands.md).
