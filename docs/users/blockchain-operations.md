# Blockchain Operations Guide

This guide provides detailed instructions for performing blockchain operations on the Carbonix platform, including tokenization, transfers, and retirement of carbon credits.

## Understanding Blockchain Integration

Carbonix uses blockchain technology to provide transparency, immutability, and traceability for carbon credits. By tokenizing carbon credits, you can:

- Prove ownership with cryptographic certainty
- Transfer credits easily and securely
- Retire credits with permanent on-chain records
- Track the entire lifecycle of your carbon credits

## Supported Blockchain Networks

The platform supports multiple blockchain networks:

| Network | Description | Best For |
|---------|-------------|----------|
| Ethereum | The original smart contract platform | High-value transactions, maximum security |
| Polygon | Ethereum scaling solution with low fees | Frequent transactions, cost-efficiency |
| Arbitrum | Ethereum Layer 2 with fast transactions | Balance of security and efficiency |
| Optimism | Ethereum Layer 2 with optimistic rollups | Fast transactions with Ethereum security |
| Base | Coinbase's Ethereum Layer 2 | User-friendly experience |

Each network has both mainnet (production) and testnet (testing) environments.

## Tokenizing Carbon Credits

Tokenization converts your verified carbon credits into blockchain tokens that can be transferred or retired.

### Prerequisites

Before tokenizing carbon credits:

1. Your organization must be verified
2. Your carbon credits must be verified
3. Your organization must have a blockchain wallet

### Tokenization Process

1. **Navigate to Carbon Credits**: Go to "Dashboard" > "Carbon Credits"
2. **Select a Carbon Credit**: Choose a verified carbon credit from your list
3. **Initiate Tokenization**: Click the "Tokenize" button
4. **Configure Tokenization**:
   - Select the blockchain network (e.g., Polygon)
   - Choose between mainnet or testnet
   - Review the carbon credit details
5. **Review Gas Estimation**:
   - The platform will estimate the gas (transaction fee) required
   - Gas is shown in both native currency (ETH, MATIC, etc.) and USD
   - Gas prices fluctuate based on network congestion
6. **Confirm Tokenization**: Click "Tokenize" to proceed
7. **Wait for Confirmation**:
   - The transaction will be submitted to the blockchain
   - This process may take a few minutes depending on network conditions
8. **View Results**:
   - Once complete, you'll see the transaction details
   - The carbon credit status will change to "TOKENIZED"
   - You'll see the token ID, contract address, and network information

### Tokenization Metadata

When tokenizing carbon credits, the following metadata is stored on-chain:

- Project ID
- Vintage year
- Standard (e.g., Verra VCS)
- Methodology
- Quantity
- Issuer information

Additional metadata is accessible via the token URI, which points to the Carbonix API.

## Transferring Tokenized Carbon Credits

Once tokenized, carbon credits can be transferred to other wallets or organizations.

### Transfer Process

1. **Navigate to Carbon Credits**: Go to "Dashboard" > "Carbon Credits"
2. **Select a Carbon Credit**: Choose a tokenized carbon credit
3. **Initiate Transfer**: Click the "Transfer" button
4. **Configure Transfer**:
   - Enter the recipient's wallet address
   - Specify the quantity to transfer
   - Add any notes (optional)
5. **Review Gas Estimation**:
   - The platform will estimate the gas required
   - Review the cost in both native currency and USD
6. **Confirm Transfer**: Click "Transfer" to proceed
7. **Wait for Confirmation**:
   - The transaction will be submitted to the blockchain
   - This process may take a few minutes
8. **View Results**:
   - Once complete, you'll see the transaction details
   - The available quantity will be updated
   - The transaction will appear in your transaction history

### Transfer Considerations

- You can only transfer up to the available quantity of your tokenized carbon credits
- Transfers are irreversible once confirmed on the blockchain
- The recipient must have a compatible wallet on the same blockchain network
- Consider gas prices when making transfers, as they vary by network and time

## Retiring Carbon Credits

Retirement permanently removes carbon credits from circulation, typically to offset emissions.

### Retirement Process

1. **Navigate to Carbon Credits**: Go to "Dashboard" > "Carbon Credits"
2. **Select a Carbon Credit**: Choose a tokenized carbon credit
3. **Initiate Retirement**: Click the "Retire" button
4. **Configure Retirement**:
   - Specify the quantity to retire
   - Provide a reason for retirement (e.g., "Annual corporate emissions offset")
   - Optionally specify a beneficiary (e.g., your company name or client)
5. **Review Gas Estimation**:
   - The platform will estimate the gas required
   - Review the cost in both native currency and USD
6. **Confirm Retirement**: Click "Retire" to proceed
7. **Wait for Confirmation**:
   - The transaction will be submitted to the blockchain
   - This process may take a few minutes
8. **View Results**:
   - Once complete, you'll see the transaction details
   - The available quantity will decrease and retired quantity will increase
   - The retirement will appear in your transaction history
   - A retirement certificate will be generated (if enabled)

### Retirement Certificates

After retiring carbon credits, you can generate retirement certificates that include:

- Retirement details (quantity, date, reason)
- Carbon credit information
- Blockchain transaction proof
- Your organization information
- Beneficiary information (if specified)

These certificates can be used to prove your carbon offset claims to stakeholders.

## Viewing Transaction History

The platform maintains a comprehensive history of all blockchain transactions.

### Accessing Transaction History

1. **Navigate to Carbon Credits**: Go to "Dashboard" > "Carbon Credits"
2. **Select a Carbon Credit**: Choose any carbon credit
3. **View Transactions**: Click on the "Transactions" tab
4. **Filter Transactions**:
   - Filter by type (tokenization, transfer, retirement)
   - Filter by date range
   - Filter by status (completed, pending, failed)
5. **View Details**: Click on any transaction to see details:
   - Transaction type and amount
   - Status and timestamp
   - Transaction hash and block explorer link
   - Gas used and transaction cost
   - Additional details specific to the transaction type

### Block Explorer Links

Each transaction includes a link to the appropriate block explorer, where you can view:

- Transaction status and confirmations
- Contract interactions
- Gas usage details
- Block information
- Other on-chain details

## Gas Estimation

The platform provides gas estimation for all blockchain operations to help you understand costs before submitting transactions.

### Understanding Gas Costs

Gas costs consist of:

- **Gas Used**: The computational resources required for the operation
- **Gas Price**: The price per unit of gas, which varies based on network congestion
- **Total Gas Cost**: Gas Used × Gas Price, paid in the network's native currency (ETH, MATIC, etc.)

### Gas Estimation Features

The platform provides:

- Real-time gas estimates based on current network conditions
- Conversion to USD for easier understanding
- Breakdown of gas costs by operation
- Historical gas price trends
- Recommendations for optimal transaction timing

### Gas Optimization Tips

To minimize gas costs:

- Use Polygon or other Layer 2 networks for lower fees
- Perform operations during periods of low network congestion
- Batch operations when possible
- Consider using the platform's gas price alerts

## Troubleshooting

### Common Issues and Solutions

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| Transaction Pending | Network congestion | Wait for confirmation or speed up with higher gas |
| Transaction Failed | Insufficient gas, wallet issues | Check error details and retry with higher gas |
| Cannot Tokenize | Carbon credit not verified | Complete verification process first |
| Cannot Transfer | Insufficient balance | Check available quantity and try a smaller amount |
| High Gas Costs | Network congestion | Try during off-peak hours or use a different network |

### Getting Help

If you encounter issues with blockchain operations:

1. Check the error message for specific details
2. Consult our Blockchain FAQ
3. Contact support with the transaction hash and details
4. For urgent issues, use the live chat support
