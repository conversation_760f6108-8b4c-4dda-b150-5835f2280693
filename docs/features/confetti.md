# Confetti Animation Feature

This document describes the confetti animation feature implemented in the Carbonix platform.

## Overview

The confetti animation is used to celebrate important milestones and achievements within the application, such as completing the organization onboarding process. It provides a delightful user experience and positive reinforcement for completing important tasks.

## Implementation

The confetti animation is implemented using the `canvas-confetti` library, which creates a canvas-based confetti animation that is lightweight and performant.

### Components

1. **Confetti Component** (`src/components/ui/confetti.tsx`)
   - A reusable React component that wraps the canvas-confetti library
   - Handles dynamic import to avoid SSR issues
   - Configurable with props for customization

2. **useConfetti Hook** (`src/hooks/use-confetti.ts`)
   - A custom React hook that provides state management for confetti animations
   - Exposes methods to start and stop confetti animations

## Usage

### Basic Usage

```tsx
import { useConfetti } from "@/hooks/use-confetti";
import { Confetti } from "@/components/ui/confetti";

function MyComponent() {
  const { isActive, startConfetti, stopConfetti } = useConfetti();

  return (
    <div>
      {isActive && <Confetti />}
      <button onClick={startConfetti}>Celebrate!</button>
    </div>
  );
}
```

### Customization Options

The `Confetti` component accepts the following props:

- `duration` (number): Duration of the animation in milliseconds (default: 3000)
- `particleCount` (number): Number of confetti particles (default: 100)
- `spread` (number): Spread of the confetti particles (default: 70)
- `origin` (object): Origin point of the confetti (default: { x: 0.5, y: 0.5 })
- `colors` (string[]): Array of colors for the confetti particles
- `onComplete` (function): Callback function to execute when animation completes

Example with custom options:

```tsx
<Confetti
  particleCount={200}
  spread={90}
  origin={{ x: 0.5, y: 0.3 }}
  colors={['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0']}
  duration={5000}
  onComplete={() => console.log('Confetti animation completed')}
/>
```

## Current Implementations

The confetti animation is currently implemented in:

1. **Organization Onboarding Completion** (`src/app/(auth)/onboarding/complete/complete-content.tsx`)
   - Triggers when the organization onboarding process is completed
   - Enhances the celebration of this important milestone

## Adding to New Features

To add confetti animations to new features:

1. Import the `useConfetti` hook and `Confetti` component
2. Use the `startConfetti` function at the appropriate moment
3. Render the `Confetti` component conditionally based on the `isActive` state

## Dependencies

- `canvas-confetti`: The underlying library for creating confetti animations
- `@types/canvas-confetti`: TypeScript type definitions for canvas-confetti

## Accessibility Considerations

The confetti animation respects the user's reduced motion preferences by using the `disableForReducedMotion: true` option, which will disable the animation for users who have indicated a preference for reduced motion in their operating system settings.
