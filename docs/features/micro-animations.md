# Micro-Animations and Micro-Interactions

This document describes the micro-animations and micro-interactions implemented in the Carbonix platform.

## Overview

Micro-animations and micro-interactions are subtle animations and interactive elements that enhance the user experience by providing visual feedback, guiding attention, and making the interface feel more responsive and engaging.

## Implementation

The animation system is built using Framer Motion, a powerful animation library for React. It provides a set of reusable animation variants and components that can be used throughout the application.

### Animation Utilities

1. **Animation Constants** (`src/lib/animations.ts`)
   - Defines standard durations, easings, and animation variants
   - Provides helper functions for creating staggered animations and combining variants

2. **Animation Hooks** (`src/hooks/use-animations.ts`)
   - `useAnimations`: Manages animations with respect to reduced motion preferences
   - `useAnimationVariant`: Gets animation variants for a specific component

### Animated Components

The animation system includes enhanced versions of UI components with built-in animations:

1. **Animated Button** (`src/components/ui/animated-button.tsx`)
   - Adds hover and tap animations to buttons
   - Supports customizable animation variants

2. **Animated Card** (`src/components/ui/animated-card.tsx`)
   - Adds hover lift effect to cards
   - Supports interactive and non-interactive modes

3. **Animated Form** (`src/components/ui/animated-form.tsx`)
   - Adds animations to form inputs and controls
   - Supports staggered animations for form fields

4. **Animated List** (`src/components/ui/animated-list.tsx`)
   - Adds staggered animations to list items
   - Supports grid layouts with animations

5. **Animated Icon** (`src/components/ui/animated-icon.tsx`)
   - Adds animations to icons
   - Includes specialized components like AnimatedNotificationBell

6. **Animated Tooltip** (`src/components/ui/animated-tooltip.tsx`)
   - Adds entrance and exit animations to tooltips and popovers
   - Supports customizable animation variants

7. **Animated Badge** (`src/components/ui/animated-badge.tsx`)
   - Adds animations to badges and status indicators
   - Supports pulse effects for drawing attention

8. **Animated Tabs** (`src/components/ui/animated-tabs.tsx`)
   - Adds smooth transitions between tab content
   - Includes animated tab indicators

9. **Animated Loading** (`src/components/ui/animated-loading.tsx`)
   - Enhanced loading indicators with animations
   - Includes skeletons, spinners, and progress bars

10. **Animated Feedback** (`src/components/ui/animated-feedback.tsx`)
    - Animated success and error indicators
    - Enhanced toast notifications with animations

11. **Page Transitions** (`src/components/ui/page-transition.tsx`)
    - Smooth transitions between pages
    - Staggered animations for page content

## Usage

### Basic Usage

```tsx
import { AnimatedButton } from "@/components/ui/animated";

function MyComponent() {
  return (
    <AnimatedButton>
      Click Me
    </AnimatedButton>
  );
}
```

### Customizing Animations

```tsx
import { AnimatedCard } from "@/components/ui/animated";

function MyComponent() {
  return (
    <AnimatedCard animationVariant="hoverScale">
      <h2>Card Title</h2>
      <p>Card content</p>
    </AnimatedCard>
  );
}
```

### Page Transitions

```tsx
import { PageTransition } from "@/components/ui/animated";

function MyPage() {
  return (
    <PageTransition animationVariant="fadeIn">
      <h1>Page Title</h1>
      <p>Page content</p>
    </PageTransition>
  );
}
```

### Animated Lists

```tsx
import { AnimatedList } from "@/components/ui/animated";

function MyList({ items }) {
  return (
    <AnimatedList
      items={items}
      renderItem={(item) => <div>{item.name}</div>}
      keyExtractor={(item) => item.id}
    />
  );
}
```

## Accessibility Considerations

The animation system respects the user's reduced motion preferences by:

1. Using the `useReducedMotion` hook from Framer Motion to detect reduced motion preferences
2. Providing simplified animations for users who prefer reduced motion
3. Ensuring all animations can be disabled if needed

## Animation Principles

The animation system follows these principles:

1. **Subtle and Purposeful**: Animations should enhance the user experience, not distract from it
2. **Responsive and Fast**: Animations should be quick and responsive to user actions
3. **Consistent**: Animations should be consistent across the application
4. **Accessible**: Animations should respect user preferences and accessibility needs
5. **Performance-Focused**: Animations should be optimized for performance

## Animation Types

### Micro-Animations

- **Hover Effects**: Subtle scaling or elevation changes on hover
- **Click/Tap Feedback**: Visual feedback when buttons or interactive elements are clicked
- **Focus States**: Enhanced focus indicators for accessibility
- **Loading States**: Animated loading indicators and skeletons
- **Transitions**: Smooth transitions between states and pages

### Micro-Interactions

- **Form Feedback**: Visual feedback for form validation
- **Success/Error States**: Animated indicators for success and error states
- **Notifications**: Animated toast notifications and alerts
- **Status Indicators**: Animated badges and status indicators
- **Interactive Elements**: Enhanced interactive elements with animations

## Implementation Details

```mermaid
graph TD
    A[Animation System] --> B[Animation Utilities]
    A --> C[Animated Components]
    A --> D[Page Transitions]
    
    B --> B1[Animation Constants]
    B --> B2[Animation Hooks]
    
    C --> C1[Interactive Elements]
    C --> C2[Feedback Components]
    C --> C3[Loading Components]
    
    C1 --> C1A[Animated Button]
    C1 --> C1B[Animated Card]
    C1 --> C1C[Animated Form]
    
    C2 --> C2A[Animated Feedback]
    C2 --> C2B[Animated Toast]
    
    C3 --> C3A[Animated Skeleton]
    C3 --> C3B[Animated Spinner]
    
    D --> D1[Page Transition]
    D --> D2[Staggered List]
```
