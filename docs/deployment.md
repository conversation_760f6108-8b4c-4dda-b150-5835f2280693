# Carbonx Deployment Guide

This guide explains how to deploy the Carbonx application to different environments using Docker.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- Git repository cloned

## Deployment Options

The application can be deployed to three different environments:

1. **Development**: Local development environment with test blockchain networks
2. **Pre-Production**: Test environment with test blockchain networks but production-like configuration
3. **Production**: Live environment with real blockchain networks

## Deployment Methods

There are two main ways to deploy the application:

### Option 1: Using the Docker Environment Setup Script (Recommended)

The simplest way to deploy is using the Docker environment setup script, which provides an interactive setup process:

```bash
# Make the script executable
chmod +x ./scripts/setup-docker-env.sh

# Run the setup script
./scripts/setup-docker-env.sh
```

This script will:
1. Check if Docker and Docker Compose are installed
2. Ask which environment you want to set up (Development, Pre-Production, or Production)
3. Set up environment variables based on your choice
4. Build and start the Docker containers
5. Initialize the database with migrations and seed data
6. Provide login credentials and useful commands

### Option 2: Using the Unified Deployment Script

For more advanced deployment options, you can use the enhanced deployment script:

```bash
# Deploy to any environment (interactive)
pnpm deploy

# Deploy directly to development
pnpm deploy:dev

# Deploy directly to pre-production
pnpm deploy:preprod

# Deploy directly to production
pnpm deploy:prod
```

Or run the script directly:

```bash
# Deploy to any environment (interactive)
./scripts/carbonx-deploy.sh

# Deploy directly to development
./scripts/carbonx-deploy.sh 1

# Deploy directly to pre-production
./scripts/carbonx-deploy.sh 2

# Deploy directly to production
./scripts/carbonx-deploy.sh 3
```

## What the Docker Environment Setup Script Does

The `setup-docker-env.sh` script performs the following tasks:

### For Development Environment

1. Sets up environment variables for development (test blockchain networks)
2. Builds the app-dev and db-init Docker containers
3. Starts the app-dev container and PostgreSQL database
4. Runs database migrations and seeds essential data
5. Provides admin credentials for logging in

### For Pre-Production Environment

1. Sets up environment variables for pre-production (test blockchain networks with production configuration)
2. Builds all Docker containers including the database, app, and supporting services
3. Starts the pre-production environment
4. Initializes the database with migrations and seed data
5. Provides admin credentials for logging in

### For Production Environment

1. Sets up environment variables for production (mainnet blockchain networks)
2. Builds all Docker containers
3. Starts the production environment
4. Initializes the database with migrations and seed data
5. Provides admin credentials for logging in

## Environment Variables

Each environment requires specific environment variables. The setup script will check for a `.env` file and create one from the example if it doesn't exist.

For production, it's recommended to create a `.env` file based on `.env.production.example`.

## Database Seeding

When you run the Docker environment setup script, it automatically initializes the database with essential data. If you want to populate the database with more comprehensive test data, you can run:

```bash
# Run the comprehensive seed script in the Docker container
sudo docker compose exec app-dev pnpm prisma:seed:comprehensive
```

This will create multiple organizations, users, projects, carbon credits, and other data for testing purposes.

## Logs and Troubleshooting

You can view logs for all services using:

```bash
# View logs for all services
sudo docker compose logs -f

# View logs for a specific service (e.g., app-dev)
sudo docker compose logs -f app-dev

# View logs for the database
sudo docker compose logs -f db
```

Common troubleshooting commands:

```bash
# Check running containers
sudo docker compose ps

# Restart all services
sudo docker compose restart

# Stop all services
sudo docker compose down

# Stop and remove containers, networks, and volumes
sudo docker compose down -v
```

## Database Connection

When running in Docker, the database is accessible:

- **From inside Docker**: Using hostname `db`, port `5432`, username `postgres`, password `root`
- **From host machine**: Using hostname `localhost`, port `5433`, username `postgres`, password `root`

Connection string from host: `postgresql://postgres:root@localhost:5433/carbon_exchange`

## Visual Feedback

Both the Docker environment setup script and the enhanced deployment script provide rich visual feedback with colors to make it easy to follow the deployment process.

## Error Handling

If any step fails, the scripts will:

1. Display an error message in red
2. Exit with a non-zero status code (for critical errors)

This makes it easy to identify and fix issues during deployment.
