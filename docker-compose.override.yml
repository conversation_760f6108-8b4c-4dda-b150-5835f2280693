# This file allows overriding docker-compose.prod.yml settings
# without modifying the original file

services:
  app:
    # Add container resource limits
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  db:
    # Add container resource limits
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    volumes:
      # Add volume for database backups
      - postgres_backups:/var/lib/postgresql/backups

  # Add a backup service for regular database backups
  backup:
    image: postgres:16
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - postgres_backups:/backups
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-root}
    command: >
      bash -c 'mkdir -p /backups &&
      pg_dump -h db -U postgres -d carbon_exchange -f /backups/backup_$$(date +"%Y%m%d_%H%M%S").sql &&
      find /backups -type f -mtime +7 -delete'
    depends_on:
      - db
    restart: "no"
    networks:
      - app_network

  # Add monitoring service
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
    ports:
      - "9090:9090"
    restart: always
    networks:
      - app_network

  grafana:
    image: grafana/grafana:latest
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    depends_on:
      - prometheus
    ports:
      - "3001:3000"
    restart: always
    networks:
      - app_network

volumes:
  prometheus_data:
  grafana_data:
  postgres_backups:
