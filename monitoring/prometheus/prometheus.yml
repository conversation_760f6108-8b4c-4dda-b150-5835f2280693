global:
    scrape_interval: 15s
    evaluation_interval: 15s

alerting:
    alertmanagers:
        - static_configs:
              - targets:
                # - alertmanager:9093

rule_files:
    # - "first_rules.yml"
    # - "second_rules.yml"

scrape_configs:
    - job_name: "prometheus"
      static_configs:
          - targets: ["localhost:9090"]

    - job_name: "node"
      static_configs:
          - targets: ["node-exporter:9100"]

    - job_name: "app"
      metrics_path: "/metrics"
      static_configs:
          - targets: ["app:3000"]
