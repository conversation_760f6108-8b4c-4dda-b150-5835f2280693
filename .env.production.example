# Database
DATABASE_URL="******************************************************/carbon_exchange"
DATABASE_HOST="db"
DATABASE_PORT="5432"
DATABASE_USER="postgres"
DATABASE_PASSWORD="your-production-password"

# NextAuth
NEXTAUTH_URL="https://your-production-domain.com"
NEXTAUTH_SECRET="your-production-secret-key"

# Email (SMTP)
SMTP_HOST="smtp.your-email-provider.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-smtp-password"
SMTP_FROM="<EMAIL>"

# Blockchain
ALCHEMY_API_KEY="your-production-alchemy-api-key"
ALCHEMY_NETWORK="eth-mainnet"
ALCHEMY_GAS_MANAGER_POLICY_ID="your-production-gas-manager-policy-id"
WALLET_ENCRYPTION_KEY="your-production-wallet-encryption-key"

# Blockchain networks (all production networks)
ETHEREUM_NETWORK="mainnet"
POLYGON_NETWORK="polygon"
OPTIMISM_NETWORK="optimism"
ARBITRUM_NETWORK="arbitrum"
BASE_NETWORK="base"

# Node environment
NODE_ENV="production"

# Storage
STORAGE_PROVIDER="local"
STORAGE_BASE_URL="https://your-production-domain.com"
STORAGE_ROOT_DIR="./public/uploads"
STORAGE_USE_RELATIVE_URLS="true"

# For S3 storage (when STORAGE_PROVIDER is set to "s3")
# S3_REGION="us-east-1"
# S3_BUCKET="your-production-s3-bucket"
# S3_ACCESS_KEY_ID="your-production-s3-access-key-id"
# S3_SECRET_ACCESS_KEY="your-production-s3-secret-access-key"

# KYC/AML Providers - Use a real provider in production
KYC_PROVIDER_TYPE="ONFIDO" # Options: MOCK, ONFIDO, SUMSUB, JUMIO, TRULIOO, CUSTOM
ONFIDO_API_KEY="your-production-onfido-api-key"
ONFIDO_REGION="EU" # or "US", "CA", etc.
# SUMSUB_APP_TOKEN="your-production-sumsub-app-token"
# SUMSUB_SECRET_KEY="your-production-sumsub-secret-key"
# JUMIO_API_TOKEN="your-production-jumio-api-token"
# JUMIO_API_SECRET="your-production-jumio-api-secret"
# TRULIOO_API_KEY="your-production-trulioo-api-key"
# TRULIOO_USERNAME="your-production-trulioo-username"
# TRULIOO_PASSWORD="your-production-trulioo-password"
