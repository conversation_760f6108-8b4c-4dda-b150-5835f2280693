{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/ensure-uploads-dir.js && next dev --turbopack", "dev:no-watch": "node scripts/ensure-uploads-dir.js && NODE_OPTIONS=--max-old-space-size=4096 next dev", "build": "node scripts/ensure-uploads-dir.js && node scripts/node-protocol-transpiler.js && next build --turbopack", "start": "next start --turbopack", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:docker": "./scripts/run-migrations.sh", "prisma:studio": "prisma studio", "prisma:studio:docker": "./scripts/run-prisma-studio.sh", "prisma:deploy": "prisma migrate deploy", "prisma:seed": "ts-node prisma/seed.ts", "prisma:seed:comprehensive": "tsx scripts/seed-comprehensive.ts", "prisma:fix": "./scripts/fix-migration.sh", "prisma:migrate:advanced": "./scripts/prisma-migrate.sh", "prisma:reset": "./scripts/prisma-migrate.sh --reset", "prisma:push": "./scripts/prisma-migrate.sh --schema-push", "docker:dev": "docker compose up app-dev", "docker:dev:build": "docker compose build app-dev && docker compose up app-dev", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:db": "./scripts/start-db.sh", "docker:db:stop": "docker compose down db", "docker:prod": "docker compose -f docker-compose.prod.yml up -d", "docker:prod:build": "docker compose -f docker-compose.prod.yml build && docker compose -f docker-compose.prod.yml up -d", "docker:preprod": "docker compose -f docker-compose.preprod.yml up -d", "docker:preprod:build": "docker compose -f docker-compose.preprod.yml build && docker compose -f docker-compose.preprod.yml up -d", "docker:logs": "docker compose logs -f", "dev:with-db": "pnpm docker:db && pnpm dev", "setup:db": "node scripts/setup-db.js", "setup:docker": "./scripts/setup-docker-env.sh", "deploy": "./scripts/carbonx-deploy.sh", "deploy:dev": "./scripts/carbonx-deploy.sh 1", "deploy:preprod": "./scripts/carbonx-deploy.sh 2", "deploy:prod": "./scripts/carbonx-deploy.sh 3", "seed:deployment": "ts-node scripts/deployment-seed.ts", "ensure-uploads-dir": "node scripts/ensure-uploads-dir.js", "transpile-node-imports": "node scripts/node-protocol-transpiler.js", "ssl:init": "./scripts/init-letsencrypt.sh", "rbac:init": "ts-node scripts/initialize-rbac.ts"}, "dependencies": {"@aa-sdk/core": "^4.31.2", "@account-kit/core": "^4.31.2", "@account-kit/infra": "^4.31.2", "@account-kit/smart-contracts": "^4.31.2", "@alchemy/aa-alchemy": "^3.19.0", "@alchemy/aa-core": "^3.19.0", "@auth/prisma-adapter": "^2.9.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.75.2", "@tanstack/react-table": "^8.21.3", "alchemy-sdk": "^3.5.8", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "ethers": "^6.13.7", "framer-motion": "^12.9.7", "input-otp": "^1.4.2", "lucide-react": "^0.507.0", "marked": "^15.0.11", "next": "15.3.1", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "nodemailer": "^7.0.2", "otplib": "^12.0.1", "qrcode": "^1.5.4", "react": "^19.1.0", "react-day-picker": "9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.56.2", "react-resizable-panels": "^3.0.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.9", "vaul": "^1.1.2", "viem": "^2.29.0", "websocket": "^1.0.35", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9", "eslint-config-next": "15.3.1", "ignore-loader": "^0.1.2", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "null-loader": "^4.0.1", "prisma": "^6.7.0", "tailwindcss": "^4", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5"}, "packageManager": "pnpm@10.10.0"}